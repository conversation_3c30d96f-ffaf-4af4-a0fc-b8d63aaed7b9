<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="39881 -32722 7209 4747">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape205">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,27 38,5 50,5 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="25" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3316700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e7830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33dce30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33141a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_314ccd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3337fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_259caf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_334d3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d54e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_337c890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31c6ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_296bdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311ee40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33fcad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32ef520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_26ee8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33b9d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32f0050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_334c950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a109f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30269f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_28235d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33202c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3375fe0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3370680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33b71f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_333c6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33b88e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33fadf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33762c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="4757" width="7219" x="39876" y="-32727"/>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="0" cx="45790" cy="-29984" fill="none" fillStyle="0" r="17.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45792" cy="-29962" fill="none" fillStyle="0" r="17.5" stroke="rgb(255,255,255)" stroke-width="1"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43544" x2="43544" y1="-32219" y2="-32129"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43532" x2="43556" y1="-32186" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43532" x2="43556" y1="-32053" y2="-32030"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43544" x2="43542" y1="-32081" y2="-31691"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43438" x2="43438" y1="-32217" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43426" x2="43449" y1="-32185" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43426" x2="43449" y1="-32052" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43438" x2="43438" y1="-32080" y2="-31679"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43649" x2="43649" y1="-32218" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43638" x2="43661" y1="-32186" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43638" x2="43661" y1="-32052" y2="-32029"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43649" x2="43649" y1="-32080" y2="-31395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43756" x2="43756" y1="-32213" y2="-32123"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43744" x2="43768" y1="-32181" y2="-32158"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43744" x2="43768" y1="-32048" y2="-32024"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43756" x2="43756" y1="-32076" y2="-31682"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43333" x2="43333" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43321" x2="43345" y1="-32184" y2="-32160"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43321" x2="43345" y1="-32050" y2="-32027"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43333" x2="43333" y1="-32078" y2="-31677"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43223" x2="43223" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43211" x2="43235" y1="-32184" y2="-32160"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43211" x2="43235" y1="-32050" y2="-32027"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43223" x2="43223" y1="-32078" y2="-31676"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42991" x2="42993" y1="-30152" y2="-30282"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43649" x2="43649" y1="-31325" y2="-31134"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43649" x2="43650" y1="-31064" y2="-30921"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43634" x2="43669" y1="-31434" y2="-31399"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43633" x2="43668" y1="-31322" y2="-31287"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43633" x2="43667" y1="-31172" y2="-31138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43634" x2="43669" y1="-31060" y2="-31026"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43634" x2="43669" y1="-30959" y2="-30924"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43634" x2="43669" y1="-30847" y2="-30812"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43325" x2="43461" y1="-30670" y2="-30671"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43155" x2="43258" y1="-30670" y2="-30670"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42991" x2="42989" y1="-30401" y2="-30544"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42972" x2="43007" y1="-30542" y2="-30508"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42991" x2="42779" y1="-30444" y2="-30444"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43159" x2="43159" y1="-29810" y2="-29719"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43293" x2="43293" y1="-29808" y2="-29718"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43416" x2="43416" y1="-29803" y2="-29712"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43159" x2="43159" y1="-29672" y2="-29416"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43293" x2="43293" y1="-29670" y2="-29046"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43416" x2="43416" y1="-29665" y2="-29049"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43147" x2="43171" y1="-29778" y2="-29754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43147" x2="43171" y1="-29644" y2="-29620"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43281" x2="43304" y1="-29776" y2="-29752"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43281" x2="43304" y1="-29643" y2="-29619"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43404" x2="43427" y1="-29771" y2="-29747"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43404" x2="43427" y1="-29637" y2="-29614"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43158" x2="43158" y1="-29057" y2="-28942"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43137" x2="43095" y1="-29262" y2="-29262"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43031" x2="42777" y1="-29262" y2="-29262"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42992" x2="43025" y1="-29278" y2="-29246"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42777" x2="42776" y1="-29262" y2="-29420"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42777" x2="42779" y1="-29857" y2="-30444"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="43143" x2="43177" y1="-29161" y2="-29128"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="43143" x2="43177" y1="-29053" y2="-29019"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43597" x2="43597" y1="-29805" y2="-29714"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43728" x2="43728" y1="-29806" y2="-29711"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44246" x2="44246" y1="-29806" y2="-29715"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43862" x2="43862" y1="-29806" y2="-29680"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44073" x2="44073" y1="-29807" y2="-29680"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44005" x2="44073" y1="-29680" y2="-29680"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43728" x2="43729" y1="-29663" y2="-29130"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43597" x2="43599" y1="-29667" y2="-29051"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43586" x2="43609" y1="-29773" y2="-29749"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43586" x2="43609" y1="-29639" y2="-29615"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43587" x2="43611" y1="-29590" y2="-29566"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43716" x2="43740" y1="-29769" y2="-29745"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43716" x2="43740" y1="-29636" y2="-29612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43718" x2="43742" y1="-29587" y2="-29563"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43862" x2="43931" y1="-29680" y2="-29680"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43844" x2="43881" y1="-29762" y2="-29725"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44234" x2="44258" y1="-29774" y2="-29750"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44234" x2="44258" y1="-29640" y2="-29617"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44246" x2="44246" y1="-29668" y2="-29552"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44235" x2="44259" y1="-29591" y2="-29567"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44054" x2="44091" y1="-29762" y2="-29725"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44153" x2="44224" y1="-29344" y2="-29344"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43710" x2="43610" y1="-28693" y2="-28693"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43540" x2="43179" y1="-28693" y2="-28693"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43158" x2="43158" y1="-28841" y2="-28715"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43901" x2="43938" y1="-28854" y2="-28817"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43884" x2="44028" y1="-28830" y2="-28830"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44114" x2="44151" y1="-28849" y2="-28812"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44103" x2="44153" y1="-28830" y2="-28830"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44153" x2="44153" y1="-28830" y2="-29040"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="44135" x2="44172" y1="-29033" y2="-28996"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44153" x2="44153" y1="-29114" y2="-29344"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43729" x2="43729" y1="-28812" y2="-28712"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44246" x2="44246" y1="-29322" y2="-29202"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43875" x2="43877" y1="-32043" y2="-31523"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43875" y1="-32075" y2="-32055"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43863" x2="43887" y1="-32000" y2="-31976"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43893" y1="-32043" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43893" y1="-32055" y2="-32073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43858" y1="-32043" y2="-32060"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43858" y1="-32055" y2="-32072"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43875" y1="-32213" y2="-32154"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43875" y1="-32141" y2="-32122"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43893" y1="-32154" y2="-32136"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43893" y1="-32141" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43858" y1="-32154" y2="-32137"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43875" x2="43858" y1="-32141" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44109" y1="-32044" y2="-31646"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43996" y1="-32043" y2="-31645"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44109" y1="-32076" y2="-32056"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43997" y1="-32075" y2="-32055"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43986" x2="44009" y1="-32000" y2="-31976"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44098" x2="44122" y1="-32000" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="44014" y1="-32043" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="44014" y1="-32055" y2="-32073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43980" y1="-32043" y2="-32060"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43980" y1="-32055" y2="-32072"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44127" y1="-32044" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44127" y1="-32056" y2="-32074"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44092" y1="-32044" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44092" y1="-32056" y2="-32073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44499" x2="44499" y1="-32077" y2="-31501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44631" y1="-32074" y2="-31620"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44367" y1="-32077" y2="-31630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44352" x2="44375" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44488" x2="44511" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44620" x2="44644" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44363" y1="-32077" y2="-32057"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44381" y1="-32045" y2="-32062"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44381" y1="-32057" y2="-32075"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44346" y1="-32045" y2="-32062"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44346" y1="-32057" y2="-32074"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44499" y1="-32077" y2="-32056"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44516" y1="-32044" y2="-32062"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44516" y1="-32056" y2="-32074"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44482" y1="-32044" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44482" y1="-32056" y2="-32073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44631" y1="-32076" y2="-32056"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44649" y1="-32043" y2="-32061"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44649" y1="-32056" y2="-32074"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44614" y1="-32043" y2="-32060"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44614" y1="-32056" y2="-32073"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44282" x2="44258" y1="-32180" y2="-32156"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44149" x2="44187" y1="-32132" y2="-32132"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44109" y1="-32214" y2="-32155"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44109" y1="-32142" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43997" y1="-32213" y2="-32153"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43997" y1="-32141" y2="-32122"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44149" x2="44149" y1="-32213" y2="-32132"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="44014" y1="-32153" y2="-32136"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="44014" y1="-32141" y2="-32123"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43980" y1="-32153" y2="-32136"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43997" x2="43980" y1="-32141" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44127" y1="-32155" y2="-32137"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44127" y1="-32142" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44092" y1="-32155" y2="-32138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44109" x2="44092" y1="-32142" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44234" x2="44270" y1="-32132" y2="-32131"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44270" x2="44270" y1="-32204" y2="-32131"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44363" y1="-32215" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44499" y1="-32214" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44363" y1="-32143" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44381" y1="-32156" y2="-32138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44381" y1="-32143" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44346" y1="-32156" y2="-32139"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44363" x2="44346" y1="-32143" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44499" y1="-32143" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44516" y1="-32155" y2="-32137"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44516" y1="-32143" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44482" y1="-32155" y2="-32138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44499" x2="44482" y1="-32143" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44631" y1="-32212" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44631" y1="-32142" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44649" y1="-32155" y2="-32137"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44649" y1="-32142" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44614" y1="-32155" y2="-32138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44631" x2="44614" y1="-32142" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43877" x2="43878" y1="-31451" y2="-31046"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44478" x2="44479" y1="-31046" y2="-30874"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44477" x2="44476" y1="-30324" y2="-29930"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44459" x2="44498" y1="-29849" y2="-29811"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44476" x2="44478" y1="-29853" y2="-29590"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44478" x2="44478" y1="-29521" y2="-29371"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44246" x2="44246" y1="-28564" y2="-28426"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44269" x2="44358" y1="-28587" y2="-28587"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-28587" y2="-28673"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44340" x2="44377" y1="-28667" y2="-28630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44339" x2="44376" y1="-28790" y2="-28753"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44478" x2="44380" y1="-29371" y2="-29371"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-29393" y2="-29872"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45174" x2="45174" y1="-29893" y2="-29643"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45032" x2="45032" y1="-29891" y2="-29325"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44912" x2="44912" y1="-29889" y2="-29183"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44787" x2="44787" y1="-29892" y2="-29454"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45162" x2="45186" y1="-29866" y2="-29842"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45164" x2="45188" y1="-29817" y2="-29793"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45020" x2="45044" y1="-29863" y2="-29839"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45022" x2="45046" y1="-29814" y2="-29790"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44900" x2="44924" y1="-29861" y2="-29838"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44902" x2="44925" y1="-29812" y2="-29788"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44775" x2="44799" y1="-29865" y2="-29841"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44777" x2="44800" y1="-29815" y2="-29792"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45276" x2="45276" y1="-29892" y2="-29600"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45392" x2="45392" y1="-29891" y2="-29357"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45507" x2="45507" y1="-29887" y2="-29388"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45265" x2="45288" y1="-29865" y2="-29841"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45266" x2="45290" y1="-29816" y2="-29792"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45380" x2="45404" y1="-29863" y2="-29839"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45381" x2="45405" y1="-29814" y2="-29790"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45496" x2="45519" y1="-29860" y2="-29836"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45497" x2="45521" y1="-29810" y2="-29787"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45174" x2="45174" y1="-30031" y2="-29941"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45162" x2="45186" y1="-29999" y2="-29975"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45032" x2="45032" y1="-30028" y2="-29938"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45020" x2="45044" y1="-29996" y2="-29973"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44912" x2="44912" y1="-30027" y2="-29937"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44900" x2="44924" y1="-29995" y2="-29971"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44787" x2="44787" y1="-30030" y2="-29940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44775" x2="44799" y1="-29998" y2="-29974"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45276" x2="45276" y1="-30030" y2="-29940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45265" x2="45288" y1="-29999" y2="-29975"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45392" x2="45392" y1="-30028" y2="-29938"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45380" x2="45404" y1="-29996" y2="-29973"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45507" x2="45507" y1="-30025" y2="-29935"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45496" x2="45519" y1="-29993" y2="-29969"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44781" x2="44782" y1="-31297" y2="-30920"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44762" x2="44801" y1="-31250" y2="-31211"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44762" x2="44801" y1="-30970" y2="-30932"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44782" x2="44781" y1="-30843" y2="-30441"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44787" x2="44788" y1="-29387" y2="-29150"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44770" x2="44804" y1="-29499" y2="-29466"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44781" x2="44782" y1="-30364" y2="-30091"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="44762" x2="44800" y1="-30501" y2="-30462"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45954" x2="45978" y1="-32185" y2="-32161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45954" x2="45978" y1="-32051" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45956" x2="45980" y1="-32002" y2="-31978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45848" x2="45906" y1="-32135" y2="-32135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45906" x2="45906" y1="-32216" y2="-32135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45918" x2="45894" y1="-32187" y2="-32164"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46325" x2="46323" y1="-32071" y2="-31461"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46205" x2="46204" y1="-32070" y2="-31239"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46441" x2="46441" y1="-32070" y2="-31732"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46559" x2="46560" y1="-32059" y2="-30710"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-32207" y2="-32117"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46193" x2="46217" y1="-32176" y2="-32152"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46193" x2="46217" y1="-32042" y2="-32018"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46194" x2="46218" y1="-31993" y2="-31969"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46325" x2="46325" y1="-32208" y2="-32118"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46313" x2="46336" y1="-32176" y2="-32153"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46313" x2="46336" y1="-32043" y2="-32019"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46314" x2="46338" y1="-31994" y2="-31970"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46441" x2="46441" y1="-32207" y2="-32117"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46429" x2="46452" y1="-32176" y2="-32152"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46429" x2="46452" y1="-32042" y2="-32018"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46430" x2="46454" y1="-31993" y2="-31969"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46559" x2="46559" y1="-32197" y2="-32107"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46547" x2="46571" y1="-32165" y2="-32141"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46547" x2="46571" y1="-32031" y2="-32008"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46549" x2="46573" y1="-31982" y2="-31959"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45339" x2="45363" y1="-32182" y2="-32159"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45339" x2="45363" y1="-32049" y2="-32025"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45341" x2="45365" y1="-32000" y2="-31976"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45585" x2="45585" y1="-32077" y2="-31518"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45748" x2="45801" y1="-32135" y2="-32135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45707" x2="45725" y1="-31919" y2="-31919"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45768" x2="45778" y1="-31931" y2="-31931"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45732" x2="45768" y1="-31916" y2="-31931"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45740" x2="45763" y1="-31912" y2="-31922"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45735" x2="45740" y1="-31924" y2="-31912"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45758" x2="45735" y1="-31934" y2="-31924"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45763" x2="45758" y1="-31922" y2="-31934"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45466" x2="45466" y1="-32215" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45454" x2="45478" y1="-32183" y2="-32159"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45454" x2="45478" y1="-32050" y2="-32026"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45455" x2="45479" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45585" x2="45585" y1="-32214" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45573" x2="45597" y1="-32182" y2="-32159"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45573" x2="45597" y1="-32049" y2="-32025"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45575" x2="45598" y1="-32000" y2="-31976"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45706" x2="45706" y1="-32211" y2="-32121"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45694" x2="45718" y1="-32179" y2="-32156"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45694" x2="45718" y1="-32046" y2="-32022"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45696" x2="45719" y1="-31997" y2="-31973"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45748" x2="45748" y1="-32208" y2="-32135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45760" x2="45737" y1="-32180" y2="-32156"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45352" x2="45352" y1="-32076" y2="-31592"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45065" x2="45065" y1="-32080" y2="-31642"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45184" x2="45184" y1="-32077" y2="-30556"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44922" x2="44922" y1="-32079" y2="-31582"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44796" x2="44796" y1="-32079" y2="-31629"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44795" x2="44795" y1="-32217" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44783" x2="44807" y1="-32185" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44783" x2="44807" y1="-32052" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44784" x2="44808" y1="-32003" y2="-31979"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44921" x2="44921" y1="-32218" y2="-32128"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44909" x2="44933" y1="-32186" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44909" x2="44933" y1="-32053" y2="-32029"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44911" x2="44935" y1="-32004" y2="-31980"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45065" x2="45065" y1="-32217" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45053" x2="45077" y1="-32185" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45053" x2="45077" y1="-32052" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45055" x2="45078" y1="-32003" y2="-31979"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45351" x2="45351" y1="-32214" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45184" x2="45184" y1="-32214" y2="-32124"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45172" x2="45196" y1="-32182" y2="-32159"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45172" x2="45196" y1="-32049" y2="-32025"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45173" x2="45197" y1="-32000" y2="-31976"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45565" x2="45602" y1="-31574" y2="-31537"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45585" x2="45585" y1="-31444" y2="-31053"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45569" x2="45605" y1="-30975" y2="-30938"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45585" x2="45586" y1="-30979" y2="-30682"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45586" x2="45203" y1="-30538" y2="-30537"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45273" x2="45334" y1="-30568" y2="-30507"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45184" x2="45184" y1="-30517" y2="-30243"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45586" x2="45586" y1="-30649" y2="-30538"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45602" x2="45805" y1="-30662" y2="-30662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45851" x2="45960" y1="-30662" y2="-30662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45828" x2="45828" y1="-30685" y2="-30804"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45828" x2="45994" y1="-30804" y2="-30804"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46305" x2="46341" y1="-31521" y2="-31485"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46323" x2="46324" y1="-31391" y2="-31152"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46036" x2="46325" y1="-30804" y2="-30803"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45750" x2="45750" y1="-29930" y2="-29841"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46542" x2="46581" y1="-30629" y2="-30591"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46540" x2="46579" y1="-30754" y2="-30716"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46561" x2="46562" y1="-30156" y2="-29412"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45733" x2="45767" y1="-29885" y2="-29852"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45750" x2="45750" y1="-29548" y2="-29346"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45750" x2="45750" y1="-29775" y2="-29614"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45734" x2="45767" y1="-29545" y2="-29512"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45750" x2="45750" y1="-29279" y2="-29166"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45767" x2="45888" y1="-29388" y2="-29388"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45932" x2="46547" y1="-29388" y2="-29388"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42016" x2="42016" y1="-29990" y2="-30168"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42525" y1="-29893" y2="-30170"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41112" x2="41111" y1="-31427" y2="-32079"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41116" x2="41116" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41104" x2="41128" y1="-32185" y2="-32161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41104" x2="41128" y1="-32051" y2="-32027"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41105" x2="41129" y1="-32002" y2="-31978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40995" x2="40995" y1="-32216" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40983" x2="41007" y1="-32184" y2="-32160"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40983" x2="41007" y1="-32050" y2="-32026"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40985" x2="41008" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41242" x2="41242" y1="-32078" y2="-31426"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41361" x2="41361" y1="-32075" y2="-31554"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41568" x2="41569" y1="-32079" y2="-31586"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41885" x2="41885" y1="-32080" y2="-31409"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41400" x2="41423" y1="-32126" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41242" x2="41242" y1="-32216" y2="-32125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41230" x2="41254" y1="-32184" y2="-32160"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41230" x2="41254" y1="-32050" y2="-32026"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41231" x2="41255" y1="-32001" y2="-31977"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41361" x2="41361" y1="-32212" y2="-32122"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41349" x2="41373" y1="-32180" y2="-32157"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41349" x2="41373" y1="-32047" y2="-32023"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41351" x2="41375" y1="-31998" y2="-31974"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41400" x2="41400" y1="-32207" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41412" x2="41388" y1="-32189" y2="-32165"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41470" x2="41505" y1="-32126" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41505" x2="41505" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41517" x2="41493" y1="-32188" y2="-32164"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41568" x2="41568" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41556" x2="41580" y1="-32184" y2="-32161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41556" x2="41580" y1="-32051" y2="-32027"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41558" x2="41581" y1="-32002" y2="-31978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41885" x2="41885" y1="-32218" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41873" x2="41897" y1="-32186" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41873" x2="41897" y1="-32052" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41874" x2="41898" y1="-32003" y2="-31979"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42018" x2="42018" y1="-32218" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42006" x2="42030" y1="-32186" y2="-32162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42006" x2="42030" y1="-32052" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42008" x2="42031" y1="-32003" y2="-31979"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41785" x2="41785" y1="-32216" y2="-32126"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41776" x2="41799" y1="-32185" y2="-32161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41774" x2="41797" y1="-32051" y2="-32027"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41775" x2="41799" y1="-32002" y2="-31978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41736" x2="41736" y1="-28910" y2="-29364"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41875" x2="41875" y1="-28905" y2="-29563"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42016" x2="42016" y1="-28908" y2="-29068"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-28899" y2="-29110"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41736" x2="41736" y1="-28773" y2="-28863"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41748" x2="41724" y1="-28826" y2="-28850"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41748" x2="41724" y1="-28938" y2="-28962"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41875" x2="41875" y1="-28768" y2="-28858"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41887" x2="41863" y1="-28821" y2="-28845"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41887" x2="41863" y1="-28933" y2="-28957"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42016" x2="42016" y1="-28770" y2="-28860"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42027" x2="42004" y1="-28818" y2="-28842"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42027" x2="42004" y1="-28936" y2="-28959"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42524" x2="42524" y1="-28761" y2="-28852"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42536" x2="42512" y1="-28811" y2="-28835"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42536" x2="42512" y1="-28927" y2="-28950"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41736" x2="41736" y1="-29000" y2="-29000"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41746" x2="41722" y1="-28987" y2="-29011"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42525" x2="42525" y1="-28991" y2="-28991"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42016" x2="42016" y1="-28999" y2="-28999"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41873" x2="41873" y1="-28994" y2="-28994"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41885" x2="41862" y1="-28982" y2="-29006"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42026" x2="42002" y1="-28985" y2="-29009"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42534" x2="42511" y1="-28976" y2="-29000"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42525" y1="-31689" y2="-32422"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42506" x2="42542" y1="-31735" y2="-31700"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-31572" y2="-31618"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42506" x2="42542" y1="-31368" y2="-31333"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-31198" y2="-31380"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42506" x2="42542" y1="-31272" y2="-31237"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-30856" y2="-30966"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42505" x2="42543" y1="-30767" y2="-30728"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42505" x2="42543" y1="-30905" y2="-30866"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42016" x2="42016" y1="-31381" y2="-32080"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41996" x2="42035" y1="-31433" y2="-31393"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42015" x2="42015" y1="-30861" y2="-30965"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41997" x2="42034" y1="-30775" y2="-30738"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41997" x2="42034" y1="-30902" y2="-30864"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41993" x2="42036" y1="-30509" y2="-30466"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42140" x2="42379" y1="-30188" y2="-30188"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42074" x2="42034" y1="-30187" y2="-30187"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42142" x2="42175" y1="-30205" y2="-30171"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42036" x2="42069" y1="-30204" y2="-30171"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42341" x2="42374" y1="-30204" y2="-30171"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42447" x2="42480" y1="-30205" y2="-30171"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42445" x2="42507" y1="-30188" y2="-30188"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42499" x2="42549" y1="-29918" y2="-29868"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-29529" y2="-29770"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42501" x2="42547" y1="-29424" y2="-29378"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42016" x2="42016" y1="-29574" y2="-29656"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41996" x2="42035" y1="-29644" y2="-29605"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41991" x2="42040" y1="-29505" y2="-29455"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42016" x2="42016" y1="-29287" y2="-29656"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41998" x2="42033" y1="-29207" y2="-29172"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42524" x2="42524" y1="-31198" y2="-31241"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42015" x2="42016" y1="-31137" y2="-31302"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42523" x2="42524" y1="-30655" y2="-30779"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42015" x2="42015" y1="-30652" y2="-30787"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42524" x2="42524" y1="-29770" y2="-29893"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42016" x2="42017" y1="-29735" y2="-29786"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41886" x2="41886" y1="-31364" y2="-31217"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41816" x2="41839" y1="-31250" y2="-31273"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41816" x2="41840" y1="-31174" y2="-31197"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41568" x2="41568" y1="-31436" y2="-31406"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40680" x2="40657" y1="-28940" y2="-28964"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40545" x2="40542" y1="-28918" y2="-29760"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40274" x2="40275" y1="-28913" y2="-29405"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40669" x2="40668" y1="-28912" y2="-29751"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40416" x2="40415" y1="-28916" y2="-29737"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40274" x2="40274" y1="-28775" y2="-28865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40286" x2="40262" y1="-28941" y2="-28964"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40416" x2="40416" y1="-28778" y2="-28869"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40427" x2="40404" y1="-28831" y2="-28855"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40427" x2="40404" y1="-28944" y2="-28968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40545" x2="40545" y1="-28780" y2="-28870"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40557" x2="40533" y1="-28831" y2="-28854"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40557" x2="40533" y1="-28945" y2="-28969"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40669" x2="40669" y1="-28775" y2="-28865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40801" x2="40801" y1="-28910" y2="-29754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41130" x2="41130" y1="-28900" y2="-29292"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41340" x2="41342" y1="-28915" y2="-29427"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40801" x2="40801" y1="-28772" y2="-28862"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40813" x2="40790" y1="-28822" y2="-28846"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40813" x2="40790" y1="-28938" y2="-28961"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41130" x2="41130" y1="-28762" y2="-28852"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41142" x2="41118" y1="-28816" y2="-28839"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41142" x2="41118" y1="-28928" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41340" x2="41340" y1="-28777" y2="-28867"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40813" x2="40790" y1="-28760" y2="-28784"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41352" x2="41328" y1="-28943" y2="-28966"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41500" x2="41497" y1="-28780" y2="-28889"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41510" x2="41486" y1="-28841" y2="-28864"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41509" x2="41485" y1="-28965" y2="-28988"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41497" x2="41497" y1="-28937" y2="-29235"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41352" x2="41328" y1="-28982" y2="-29006"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41352" x2="41328" y1="-28825" y2="-28849"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41510" x2="41486" y1="-28999" y2="-29023"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40680" x2="40657" y1="-28824" y2="-28848"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40286" x2="40262" y1="-28824" y2="-28848"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40287" x2="40263" y1="-28976" y2="-29000"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40427" x2="40403" y1="-28983" y2="-29007"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40556" x2="40533" y1="-28975" y2="-28999"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40680" x2="40657" y1="-28974" y2="-28998"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40813" x2="40790" y1="-28972" y2="-28996"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41142" x2="41118" y1="-28966" y2="-28990"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40704" x2="40704" y1="-32076" y2="-31942"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40704" x2="40704" y1="-32195" y2="-32139"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40704" x2="40704" y1="-32153" y2="-32131"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40704" x2="40704" y1="-32087" y2="-32043"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40166" x2="40165" y1="-32075" y2="-31940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40824" x2="40824" y1="-32076" y2="-31942"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40824" x2="40826" y1="-31942" y2="-30700"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40165" x2="40165" y1="-31940" y2="-30978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40824" x2="40824" y1="-32153" y2="-32131"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40824" x2="40824" y1="-32153" y2="-32131"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40824" x2="40824" y1="-32087" y2="-32043"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40824" x2="40824" y1="-32155" y2="-32203"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40392" x2="40346" y1="-32142" y2="-32142"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40346" x2="40346" y1="-32142" y2="-32202"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40166" x2="40166" y1="-32154" y2="-32132"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40166" x2="40166" y1="-32088" y2="-32044"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40166" x2="40166" y1="-32154" y2="-32201"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40480" x2="40423" y1="-32142" y2="-32142"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40480" x2="40480" y1="-32142" y2="-32202"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40292" x2="40291" y1="-31443" y2="-31429"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40292" x2="40292" y1="-32154" y2="-32130"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40292" x2="40291" y1="-32086" y2="-31406"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40292" x2="40292" y1="-32154" y2="-32202"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40482" x2="40482" y1="-32160" y2="-32138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40578" x2="40578" y1="-32136" y2="-32208"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40561" x2="40594" y1="-32184" y2="-32151"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40690" x2="40723" y1="-32181" y2="-32148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40808" x2="40841" y1="-32178" y2="-32145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40335" x2="40359" y1="-32183" y2="-32159"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40467" x2="40491" y1="-32180" y2="-32156"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40155" x2="40188" y1="-32181" y2="-32148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40277" x2="40310" y1="-32176" y2="-32143"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40559" x2="40592" y1="-32053" y2="-32020"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40688" x2="40721" y1="-32050" y2="-32017"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40806" x2="40839" y1="-32046" y2="-32013"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40153" x2="40186" y1="-32082" y2="-32050"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40275" x2="40308" y1="-32077" y2="-32044"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40559" x2="40592" y1="-32006" y2="-31973"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40688" x2="40721" y1="-32003" y2="-31970"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40806" x2="40839" y1="-31999" y2="-31966"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40153" x2="40185" y1="-32035" y2="-32002"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40275" x2="40308" y1="-32030" y2="-31997"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42017" x2="42016" y1="-29820" y2="-29990"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42000" x2="41940" y1="-29803" y2="-29803"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41942" x2="41975" y1="-29819" y2="-29787"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41839" x2="41871" y1="-29819" y2="-29786"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41876" x2="41516" y1="-29803" y2="-29801"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41402" x2="41439" y1="-29819" y2="-29783"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41531" x2="41568" y1="-29820" y2="-29783"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41444" x2="41366" y1="-29801" y2="-29801"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41485" x2="41509" y1="-29228" y2="-29204"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41497" x2="41496" y1="-29282" y2="-29329"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41496" x2="41497" y1="-29364" y2="-29577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41479" x2="41375" y1="-29346" y2="-29347"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40910" x2="40911" y1="-29896" y2="-29344"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,255)" stroke-width="1" x1="40256" x2="40294" y1="-29792" y2="-29754"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40275" x2="40275" y1="-29880" y2="-30331"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40256" x2="40294" y1="-30457" y2="-30419"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40275" x2="40275" y1="-30407" y2="-30774"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40274" x2="40274" y1="-29749" y2="-29803"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40237" x2="40165" y1="-29711" y2="-29710"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41568" x2="41568" y1="-31363" y2="-31021"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41827" x2="41862" y1="-31384" y2="-31384"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41827" x2="41827" y1="-31384" y2="-31248"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41827" x2="41826" y1="-31201" y2="-30748"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41627" x2="41627" y1="-31201" y2="-31085"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41590" x2="41627" y1="-31384" y2="-31384"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41627" x2="41627" y1="-31384" y2="-31193"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41627" x2="41627" y1="-30710" y2="-30538"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40995" x2="40994" y1="-32078" y2="-31421"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40826" x2="40826" y1="-30624" y2="-30584"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40826" x2="40826" y1="-30624" y2="-29896"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40826" x2="40910" y1="-29896" y2="-29896"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41315" x2="41160" y1="-29346" y2="-29345"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41100" x2="40911" y1="-29344" y2="-29344"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41133" x2="41132" y1="-29808" y2="-29738"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40974" x2="41011" y1="-30699" y2="-30662"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40994" x2="40992" y1="-31344" y2="-30046"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40955" x2="40920" y1="-31383" y2="-31383"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40920" x2="40918" y1="-31383" y2="-31083"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40992" x2="40992" y1="-30046" y2="-29714"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41131" x2="41131" y1="-29691" y2="-29575"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41107" x2="41068" y1="-29714" y2="-29714"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41133" x2="41133" y1="-29808" y2="-30098"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40165" x2="40165" y1="-30123" y2="-29710"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46560" x2="46561" y1="-30633" y2="-29932"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40148" x2="40186" y1="-30880" y2="-30842"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40165" x2="40165" y1="-30901" y2="-30086"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40807" x2="40845" y1="-30748" y2="-30710"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40807" x2="40845" y1="-30615" y2="-30577"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41114" x2="41152" y1="-30088" y2="-30050"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41133" x2="41133" y1="-30174" y2="-30435"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41606" x2="41644" y1="-31151" y2="-31113"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41626" x2="41626" y1="-30751" y2="-31009"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42015" x2="42015" y1="-30442" y2="-30610"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42444" x2="42502" y1="-30633" y2="-30632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="42368" x2="42401" y1="-30649" y2="-30616"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42250" x2="42404" y1="-30633" y2="-30633"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42150" x2="42183" y1="-30649" y2="-30616"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42183" x2="42116" y1="-30633" y2="-30632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42252" x2="42285" y1="-30649" y2="-30616"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42129" x2="42162" y1="-31001" y2="-30968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42062" x2="42035" y1="-30983" y2="-30983"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42029" x2="42062" y1="-30999" y2="-30966"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42128" x2="42220" y1="-30984" y2="-30984"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42475" x2="42508" y1="-31001" y2="-30968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42371" x2="42404" y1="-31000" y2="-30967"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42445" x2="42504" y1="-30984" y2="-30984"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43047" x2="43012" y1="-30670" y2="-30670"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43650" x2="43650" y1="-30638" y2="-30311"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43729" x2="43729" y1="-28674" y2="-28432"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44477" x2="44477" y1="-30599" y2="-30401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44478" x2="44340" y1="-31046" y2="-31047"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42015" x2="42015" y1="-31002" y2="-31137"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-31005" y2="-31128"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42016" x2="42015" y1="-30205" y2="-30356"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42523" y1="-30206" y2="-30611"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41342" x2="41342" y1="-29883" y2="-29924"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40275" x2="40274" y1="-29481" y2="-29683"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43568" x2="43627" y1="-30668" y2="-30668"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43650" x2="43650" y1="-30851" y2="-30694"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42968" x2="42859" y1="-30670" y2="-30670"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43159" x2="43158" y1="-29240" y2="-29125"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41647" x2="41812" y1="-30731" y2="-30731"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41886" x2="41886" y1="-30637" y2="-30567"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41861" x2="41766" y1="-30661" y2="-30661"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41766" x2="41766" y1="-30661" y2="-30492"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41747" x2="41785" y1="-30407" y2="-30369"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41766" x2="41764" y1="-30416" y2="-29990"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44788" x2="44788" y1="-29150" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41765" x2="41986" y1="-30054" y2="-30055"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42807" x2="44287" y1="-30056" y2="-30054"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42046" x2="42495" y1="-30055" y2="-30055"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45750" x2="45750" y1="-29179" y2="-29133"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45734" x2="45767" y1="-29172" y2="-29138"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45751" x2="45470" y1="-28951" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45351" x2="45008" y1="-28951" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45472" x2="45506" y1="-28968" y2="-28934"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46325" x2="46324" y1="-30899" y2="-30803"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41341" x2="41342" y1="-30196" y2="-30270"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41365" x2="41443" y1="-30172" y2="-30172"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41568" x2="41568" y1="-31523" y2="-31436"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41531" x2="41442" y1="-31564" y2="-31565"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41442" x2="41442" y1="-31565" y2="-30853"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41422" x2="41460" y1="-30913" y2="-30875"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41422" x2="41465" y1="-30274" y2="-30231"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41422" x2="41464" y1="-30481" y2="-30438"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41443" x2="41443" y1="-30308" y2="-30172"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41886" x2="41885" y1="-31152" y2="-30876"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41869" x2="41902" y1="-31261" y2="-31228"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41885" x2="41885" y1="-30810" y2="-30685"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41869" x2="41902" y1="-30919" y2="-30886"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40256" x2="40294" y1="-29541" y2="-29503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43672" x2="43701" y1="-30666" y2="-30666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43809" x2="44156" y1="-30666" y2="-30666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43991" x2="43991" y1="-30666" y2="-30532"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43990" x2="43384" y1="-30196" y2="-30196"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43159" x2="43159" y1="-29372" y2="-29283"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43137" x2="43107" y1="-29394" y2="-29394"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42930" x2="42930" y1="-29394" y2="-30016"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43092" x2="42997" y1="-30197" y2="-30078"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42969" x2="42930" y1="-30047" y2="-30016"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41869" x2="41902" y1="-31054" y2="-31021"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43991" x2="43990" y1="-30462" y2="-30196"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43973" x2="44008" y1="-30569" y2="-30535"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43973" x2="44008" y1="-30457" y2="-30422"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43040" x2="42930" y1="-29394" y2="-29394"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43003" x2="43036" y1="-29407" y2="-29374"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(38,0,0)" stroke-width="1" x1="43108" x2="43142" y1="-29408" y2="-29375"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43318" x2="43092" y1="-30196" y2="-30197"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43280" x2="43313" y1="-30213" y2="-30179"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43386" x2="43419" y1="-30213" y2="-30180"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44912" x2="44912" y1="-29162" y2="-29162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="44974" x2="45007" y1="-29178" y2="-29145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45087" x2="45253" y1="-29162" y2="-29161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45101" x2="45134" y1="-29178" y2="-29145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41323" x2="41361" y1="-29552" y2="-29514"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41323" x2="41361" y1="-29418" y2="-29380"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41342" x2="41342" y1="-29503" y2="-29649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41324" x2="41363" y1="-29639" y2="-29601"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41342" x2="41342" y1="-29725" y2="-29883"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41322" x2="41360" y1="-30023" y2="-29985"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42052" x2="42085" y1="-30648" y2="-30615"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44499" x2="44781" y1="-31501" y2="-31501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44781" x2="44781" y1="-31373" y2="-31501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-31451" y2="-31501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42848" x2="42839" y1="-31704" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42834" x2="42844" y1="-31703" y2="-31692"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42885" x2="42886" y1="-31797" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42969" x2="42969" y1="-31797" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43052" x2="43052" y1="-31797" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42961" x2="42976" y1="-31756" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42969" x2="42969" y1="-31728" y2="-31588"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42886" x2="42886" y1="-31728" y2="-31588"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42886" x2="42872" y1="-31728" y2="-31752"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42878" x2="42893" y1="-31756" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42844" x2="42886" y1="-31716" y2="-31716"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42844" x2="42844" y1="-31716" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42844" x2="42844" y1="-31692" y2="-31677"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42969" x2="42955" y1="-31728" y2="-31752"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42927" x2="42969" y1="-31716" y2="-31716"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42932" x2="42922" y1="-31704" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42927" x2="42927" y1="-31716" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42917" x2="42927" y1="-31703" y2="-31692"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42927" x2="42927" y1="-31692" y2="-31677"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43010" x2="43052" y1="-31716" y2="-31716"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43015" x2="43005" y1="-31704" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43010" x2="43010" y1="-31716" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43001" x2="43010" y1="-31703" y2="-31692"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43010" x2="43010" y1="-31692" y2="-31677"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43052" x2="43052" y1="-31728" y2="-31588"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43052" x2="43038" y1="-31728" y2="-31752"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43044" x2="43059" y1="-31756" y2="-31756"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43099" x2="43142" y1="-31401" y2="-31401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43090" x2="43099" y1="-31387" y2="-31377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43104" x2="43094" y1="-31389" y2="-31389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43099" x2="43099" y1="-31377" y2="-31361"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43099" x2="43099" y1="-31401" y2="-31389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43228" x2="43228" y1="-31479" y2="-31442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43142" x2="43142" y1="-31479" y2="-31442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43142" x2="43142" y1="-31413" y2="-31262"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43228" x2="43228" y1="-31413" y2="-31269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43218" x2="43237" y1="-31269" y2="-31269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43228" x2="43237" y1="-31253" y2="-31269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43220" x2="43235" y1="-31442" y2="-31442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43185" x2="43185" y1="-31377" y2="-31361"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43175" x2="43185" y1="-31387" y2="-31377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43185" x2="43185" y1="-31401" y2="-31389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43190" x2="43180" y1="-31389" y2="-31389"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43185" x2="43228" y1="-31401" y2="-31401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43228" x2="43213" y1="-31413" y2="-31438"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43135" x2="43150" y1="-31442" y2="-31442"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43142" x2="43128" y1="-31413" y2="-31438"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42885" x2="42886" y1="-31568" y2="-31588"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42969" x2="42968" y1="-31588" y2="-31500"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42968" x2="42524" y1="-31500" y2="-31501"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43052" x2="43055" y1="-31588" y2="-31262"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43228" x2="43232" y1="-31260" y2="-30888"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42116" x2="42036" y1="-30632" y2="-30631"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42802" x2="42547" y1="-30555" y2="-30552"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42494" x2="42142" y1="-30538" y2="-30537"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42203" x2="42236" y1="-31001" y2="-30968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42269" x2="42302" y1="-31001" y2="-30968"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42286" x2="42408" y1="-30984" y2="-30984"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41076" x2="41094" y1="-29704" y2="-29726"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40996" x2="41014" y1="-29703" y2="-29725"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41023" x2="40992" y1="-29714" y2="-29714"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43233" x2="42799" y1="-30888" y2="-30887"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42799" x2="42802" y1="-30887" y2="-30555"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46300" y1="-28564" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46232" x2="46131" y1="-28564" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46237" x2="46295" y1="-28546" y2="-28580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46300" x2="46300" y1="-28582" y2="-28546"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45907" x2="45370" y1="-28564" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45975" x2="45975" y1="-28582" y2="-28546"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45907" x2="45966" y1="-28564" y2="-28598"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46232" x2="46232" y1="-28582" y2="-28546"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46241" x2="46175" y1="-28488" y2="-28488"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46269" x2="46241" y1="-28488" y2="-28488"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46269" x2="46269" y1="-28488" y2="-28538"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46291" x2="46248" y1="-28538" y2="-28538"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46058" x2="45975" y1="-28564" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45174" x2="45174" y1="-29306" y2="-29169"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45172" x2="45172" y1="-29147" y2="-29077"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45154" x2="45135" y1="-29662" y2="-29683"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45296" x2="45172" y1="-28564" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45174" x2="45174" y1="-29446" y2="-29340"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45406" x2="45443" y1="-28583" y2="-28545"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45224" x2="45261" y1="-28583" y2="-28546"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45157" x2="45190" y1="-29267" y2="-29234"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41131" x2="41131" y1="-29575" y2="-29475"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41113" x2="41151" y1="-29523" y2="-29485"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42142" x2="42142" y1="-30626" y2="-30537"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40577" x2="40577" y1="-32088" y2="-31954"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41772" x2="41796" y1="-31813" y2="-31790"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41785" x2="41785" y1="-32079" y2="-31906"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41598" x2="41786" y1="-31615" y2="-31615"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41592" x2="41592" y1="-31632" y2="-31632"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41391" x2="41538" y1="-31615" y2="-31614"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41271" x2="41331" y1="-31613" y2="-31614"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41142" x2="41211" y1="-31613" y2="-31612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41019" x2="41081" y1="-31613" y2="-31612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41678" x2="41717" y1="-31636" y2="-31596"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40853" x2="40958" y1="-31613" y2="-31612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40257" x2="40295" y1="-30623" y2="-30585"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40666" x2="40666" y1="-31612" y2="-31612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40793" x2="40630" y1="-31612" y2="-31612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40607" x2="40275" y1="-30773" y2="-30774"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40435" x2="40474" y1="-30795" y2="-30757"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40607" x2="40607" y1="-31593" y2="-31550"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="40607" x2="40607" y1="-30773" y2="-31507"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41443" x2="41443" y1="-30520" y2="-30394"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41785" x2="41785" y1="-30369" y2="-30369"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41531" x2="41570" y1="-30557" y2="-30519"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44503" x2="44782" y1="-30051" y2="-30091"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44287" x2="44443" y1="-30054" y2="-30051"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-28747" y2="-28850"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43716" x2="43740" y1="-29636" y2="-29612"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43729" x2="43729" y1="-29083" y2="-28848"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43714" x2="43752" y1="-29061" y2="-29024"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43862" x2="43897" y1="-31560" y2="-31525"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43216" x2="43252" y1="-30688" y2="-30652"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43497" x2="43532" y1="-28710" y2="-28675"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43810" x2="43748" y1="-28830" y2="-28831"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44226" x2="44265" y1="-29462" y2="-29423"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44246" x2="44246" y1="-29474" y2="-29365"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44225" x2="44262" y1="-29245" y2="-29208"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44226" x2="44263" y1="-29122" y2="-29085"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44225" x2="44262" y1="-28945" y2="-28908"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44226" x2="44263" y1="-28822" y2="-28785"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44912" x2="44912" y1="-29162" y2="-29162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44912" x2="44912" y1="-29162" y2="-29162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44934" x2="45058" y1="-29162" y2="-29162"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44912" x2="44912" y1="-29140" y2="-29022"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45153" x2="45190" y1="-29001" y2="-28964"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45275" x2="45275" y1="-29139" y2="-28983"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45172" x2="45172" y1="-29003" y2="-29003"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45172" x2="45172" y1="-29003" y2="-28964"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45259" x2="45292" y1="-29443" y2="-29410"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45275" x2="45275" y1="-29337" y2="-29182"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45157" x2="45191" y1="-29433" y2="-29399"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45157" x2="45191" y1="-29563" y2="-29529"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45276" x2="45276" y1="-29523" y2="-29523"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45259" x2="45292" y1="-29517" y2="-29484"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45173" x2="45173" y1="-29576" y2="-29514"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45342" x2="45359" y1="-29326" y2="-29326"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45276" x2="45276" y1="-29532" y2="-29404"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44246" x2="44246" y1="-29128" y2="-28903"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44246" x2="44246" y1="-28829" y2="-28610"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-29171" y2="-29349"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44358" x2="44358" y1="-28907" y2="-28907"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-28924" y2="-29097"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44358" x2="44358" y1="-28875" y2="-28875"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44358" x2="44358" y1="-28875" y2="-28875"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44358" x2="44358" y1="-28875" y2="-28875"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-29171" y2="-29093"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44358" x2="44358" y1="-28924" y2="-28838"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46306" x2="46341" y1="-31368" y2="-31333"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="41774" x2="41798" y1="-31629" y2="-31605"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41775" x2="41799" y1="-31924" y2="-31901"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41785" x2="41785" y1="-31858" y2="-31848"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41788" x2="41788" y1="-31709" y2="-31704"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41787" x2="41787" y1="-31657" y2="-31615"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41786" x2="41787" y1="-31615" y2="-31615"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41442" x2="41442" y1="-30777" y2="-30558"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43022" x2="43022" y1="-28695" y2="-28695"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43022" x2="43022" y1="-28695" y2="-28694"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43136" x2="43022" y1="-28693" y2="-28693"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41463" x2="41627" y1="-30541" y2="-30538"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46074" x2="46074" y1="-32079" y2="-31831"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46062" x2="46085" y1="-32185" y2="-32161"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46062" x2="46085" y1="-32051" y2="-32028"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46063" x2="46087" y1="-32002" y2="-31978"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45831" x2="45728" y1="-31686" y2="-31686"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45939" x2="46052" y1="-31686" y2="-31686"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46074" x2="45613" y1="-31304" y2="-31306"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45966" x2="45966" y1="-32209" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45966" x2="45966" y1="-32079" y2="-31826"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46074" x2="46074" y1="-32209" y2="-32127"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46074" x2="46073" y1="-31495" y2="-31304"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45449" x2="45484" y1="-31546" y2="-31510"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45449" x2="45484" y1="-31402" y2="-31367"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45466" x2="45466" y1="-32077" y2="-31723"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46063" x2="46087" y1="-31751" y2="-31727"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46060" x2="46084" y1="-31462" y2="-31439"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45553" x2="45467" y1="-31305" y2="-31305"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45706" x2="45706" y1="-32074" y2="-31892"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45706" x2="45706" y1="-31599" y2="-31664"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46074" x2="46074" y1="-31785" y2="-31707"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46074" x2="46074" y1="-31664" y2="-31542"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45467" x2="45467" y1="-31305" y2="-31424"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45467" x2="45468" y1="-31305" y2="-31262"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42555" x2="42746" y1="-30055" y2="-30056"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43099" x2="43131" y1="-29277" y2="-29245"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45448" x2="45484" y1="-31774" y2="-31738"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45449" x2="45484" y1="-31630" y2="-31595"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,127,63)" stroke-width="1" x1="45466" x2="45466" y1="-31652" y2="-31495"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45172" x2="45172" y1="-28905" y2="-28564"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43108" x2="43141" y1="-29410" y2="-29377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41112" x2="41150" y1="-29613" y2="-29575"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41130" x2="41130" y1="-29399" y2="-29292"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46253" x2="46152" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46258" x2="46316" y1="-28848" y2="-28881"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46321" x2="46321" y1="-28883" y2="-28847"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45996" x2="45996" y1="-28883" y2="-28847"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45928" x2="45987" y1="-28865" y2="-28899"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46253" x2="46253" y1="-28883" y2="-28847"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46261" x2="46196" y1="-28789" y2="-28789"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46290" x2="46261" y1="-28789" y2="-28789"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46290" x2="46290" y1="-28789" y2="-28840"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46312" x2="46269" y1="-28840" y2="-28840"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46079" x2="45996" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46321" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45305" x2="45342" y1="-28887" y2="-28850"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45698" x2="45735" y1="-28885" y2="-28848"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45425" x2="45425" y1="-28865" y2="-28747"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45615" x2="45615" y1="-28866" y2="-28748"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45734" x2="45767" y1="-29267" y2="-29234"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45404" x2="45351" y1="-28951" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41296" x2="41296" y1="-32219" y2="-32283"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41307" x2="41284" y1="-32245" y2="-32269"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41296" x2="41295" y1="-32330" y2="-32419"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41307" x2="41284" y1="-32343" y2="-32367"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41306" x2="41282" y1="-32377" y2="-32401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41295" x2="42524" y1="-32419" y2="-32419"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41666" x2="41666" y1="-29833" y2="-30090"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41301" x2="41398" y1="-30381" y2="-30381"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41398" x2="41398" y1="-30381" y2="-30202"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41398" x2="41398" y1="-30141" y2="-30090"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41398" x2="41666" y1="-30090" y2="-30090"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41666" x2="41666" y1="-29617" y2="-29682"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41644" x2="41689" y1="-29512" y2="-29466"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41667" x2="41667" y1="-29377" y2="-29526"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41722" x2="41667" y1="-29377" y2="-29377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41736" x2="41736" y1="-29392" y2="-29497"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41301" x2="41301" y1="-31127" y2="-30381"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41281" x2="41319" y1="-30492" y2="-30455"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41361" x2="41361" y1="-31107" y2="-30909"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41342" x2="41379" y1="-31726" y2="-31688"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41361" x2="41361" y1="-31483" y2="-31429"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41344" x2="41379" y1="-31471" y2="-31436"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41344" x2="41379" y1="-31236" y2="-31201"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41517" x2="41546" y1="-30985" y2="-31014"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41453" x2="41483" y1="-31000" y2="-31000"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41511" x2="41546" y1="-30999" y2="-30999"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41568" x2="41568" y1="-30977" y2="-30885"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41342" x2="41342" y1="-29972" y2="-30148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41264" x2="41293" y1="-29934" y2="-29963"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41143" x2="41209" y1="-29949" y2="-29949"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41237" x2="41317" y1="-29949" y2="-29949"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42501" x2="42546" y1="-29088" y2="-29042"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42524" y1="-29201" y2="-29438"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41999" x2="42034" y1="-29050" y2="-29016"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42016" x2="42016" y1="-29137" y2="-29218"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41666" x2="41666" y1="-29709" y2="-29773"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41711" x2="41756" y1="-29475" y2="-29429"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41736" x2="41736" y1="-29588" y2="-29610"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41361" x2="41361" y1="-31275" y2="-31148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45015" x2="45048" y1="-28968" y2="-28934"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44942" x2="44889" y1="-28951" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44836" x2="44836" y1="-28933" y2="-28830"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44836" x2="44836" y1="-28764" y2="-28700"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44852" x2="44819" y1="-28762" y2="-28728"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44942" x2="44788" y1="-28951" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45694" x2="45718" y1="-31794" y2="-31770"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45706" x2="45706" y1="-31818" y2="-31707"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(76,0,0)" stroke-width="1" x1="47088" x2="47088" y1="-28851" y2="-28851"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45985" y1="-30482" y2="-30409"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45985" y1="-30401" y2="-30397"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45973" y1="-30409" y2="-30397"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45998" y1="-30409" y2="-30397"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45973" y1="-30401" y2="-30388"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45998" y1="-30401" y2="-30388"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45985" y1="-30371" y2="-30401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45973" y1="-30305" y2="-30318"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45998" y1="-30305" y2="-30318"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45985" x2="45985" y1="-30305" y2="-30335"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45986" x2="45973" y1="-30297" y2="-30309"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45986" x2="45998" y1="-30297" y2="-30309"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45986" x2="45986" y1="-30297" y2="-30059"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45942" x2="45929" y1="-30145" y2="-30145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45940" x2="45931" y1="-30141" y2="-30141"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45938" x2="45934" y1="-30136" y2="-30136"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45936" x2="45936" y1="-30189" y2="-30248"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45936" x2="45936" y1="-30163" y2="-30145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46035" x2="45936" y1="-30248" y2="-30248"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46035" x2="46035" y1="-30172" y2="-30148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46035" x2="46035" y1="-30207" y2="-30248"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-30480" y2="-30408"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-30399" y2="-30395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46192" y1="-30408" y2="-30395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46217" y1="-30408" y2="-30395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46192" y1="-30399" y2="-30387"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46217" y1="-30399" y2="-30387"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-30369" y2="-30399"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46192" y1="-30304" y2="-30316"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46217" y1="-30304" y2="-30316"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-30304" y2="-30334"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46192" y1="-30295" y2="-30308"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46217" y1="-30295" y2="-30308"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46205" x2="46205" y1="-30295" y2="-29925"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46162" x2="46149" y1="-30143" y2="-30143"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46160" x2="46151" y1="-30139" y2="-30139"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46157" x2="46153" y1="-30135" y2="-30135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46155" x2="46155" y1="-30188" y2="-30246"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46155" x2="46155" y1="-30161" y2="-30143"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46255" x2="46155" y1="-30246" y2="-30246"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46255" x2="46255" y1="-30170" y2="-30146"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46255" x2="46255" y1="-30206" y2="-30246"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45750" x2="45750" y1="-30479" y2="-30437"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45750" x2="45750" y1="-30208" y2="-29930"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46406" y1="-30487" y2="-30414"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46406" y1="-30405" y2="-30402"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46394" y1="-30414" y2="-30402"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46419" y1="-30414" y2="-30402"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46394" y1="-30405" y2="-30393"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46419" y1="-30405" y2="-30393"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46406" y1="-30376" y2="-30405"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46394" y1="-30310" y2="-30322"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46419" y1="-30310" y2="-30322"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46406" y1="-30310" y2="-30340"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46394" y1="-30301" y2="-30314"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46419" y1="-30301" y2="-30314"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46406" x2="46406" y1="-30301" y2="-29932"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46363" x2="46350" y1="-30150" y2="-30150"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46361" x2="46352" y1="-30145" y2="-30145"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46359" x2="46354" y1="-30141" y2="-30141"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46357" x2="46357" y1="-30194" y2="-30253"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46357" x2="46357" y1="-30168" y2="-30150"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46456" x2="46357" y1="-30253" y2="-30253"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46456" x2="46456" y1="-30176" y2="-30152"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46456" x2="46456" y1="-30212" y2="-30253"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45986" x2="45986" y1="-30032" y2="-29940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46012" x2="45986" y1="-30016" y2="-30016"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46065" x2="46039" y1="-30016" y2="-30016"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46065" x2="46065" y1="-30009" y2="-30022"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46070" x2="46070" y1="-30011" y2="-30020"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46074" x2="46074" y1="-30013" y2="-30018"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46039" x2="46016" y1="-30016" y2="-30029"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46012" x2="46012" y1="-30009" y2="-30022"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44234" x2="44267" y1="-31064" y2="-31031"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44459" x2="44497" y1="-30451" y2="-30413"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="44458" x2="44493" y1="-29629" y2="-29594"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="44459" x2="44494" y1="-29517" y2="-29482"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44458" x2="44496" y1="-30924" y2="-30886"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="44459" x2="44497" y1="-30665" y2="-30627"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45751" x2="45751" y1="-29066" y2="-28951"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46205" x2="46205" y1="-31211" y2="-31187"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44922" x2="44922" y1="-31548" y2="-31451"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45737" x2="45772" y1="-31244" y2="-31209"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,255)" stroke-width="1" x1="45820" x2="45841" y1="-29722" y2="-29702"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42776" x2="42777" y1="-29417" y2="-29738"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42990" x2="42990" y1="-30622" y2="-30648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41786" x2="41785" y1="-31801" y2="-31770"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41786" x2="41785" y1="-31722" y2="-31709"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="41774" x2="41798" y1="-31788" y2="-31764"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="40147" x2="40185" y1="-31046" y2="-31008"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="46193" x2="46217" y1="-31564" y2="-31540"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="46324" x2="46325" y1="-31110" y2="-30943"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45790" x2="45751" y1="-30070" y2="-30070"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45790" x2="45790" y1="-30057" y2="-30069"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45790" x2="45790" y1="-30002" y2="-30014"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45750" x2="45750" y1="-30326" y2="-30349"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45750" x2="45750" y1="-30376" y2="-30402"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45750" x2="45750" y1="-30243" y2="-30290"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45651" x2="45651" y1="-30478" y2="-30436"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45651" x2="45651" y1="-30325" y2="-30348"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45651" x2="45651" y1="-30375" y2="-30401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45651" x2="45651" y1="-30242" y2="-30289"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45651" x2="45651" y1="-30159" y2="-30206"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45842" x2="45842" y1="-30478" y2="-30436"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45842" x2="45842" y1="-30325" y2="-30348"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45842" x2="45842" y1="-30375" y2="-30401"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45842" x2="45842" y1="-30242" y2="-30289"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45842" x2="45842" y1="-30158" y2="-30205"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="42524" x2="42885" y1="-31572" y2="-31568"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="43055" x2="43142" y1="-31262" y2="-31262"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="42220" x2="42286" y1="-30984" y2="-30984"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45398" x2="45398" y1="-28808" y2="-28808"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45928" x2="45844" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45243" x2="45172" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="45769" x2="45294" y1="-28865" y2="-28865"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="41340" x2="41301" y1="-31127" y2="-31127"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41433" x2="41374" y1="-31000" y2="-31000"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41353" x2="41312" y1="-31001" y2="-31001"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41291" x2="41232" y1="-31002" y2="-31002"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="41122" x2="41002" y1="-29950" y2="-29951"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="40982" x2="40836" y1="-29951" y2="-29953"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="40815" x2="40753" y1="-29953" y2="-29953"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="43878" x2="44272" y1="-31046" y2="-31046"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44477" x2="44476" y1="-30599" y2="-30797"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.8" x1="46188" x2="46170" y1="-31226" y2="-31226"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45554" x2="45207" y1="-31227" y2="-31226"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="45148" x2="44989" y1="-31226" y2="-31226"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44972" x2="44973" y1="-31563" y2="-31422"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44973" x2="44973" y1="-31210" y2="-31142"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44939" x2="44972" y1="-31563" y2="-31563"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.8" x1="45786" x2="45612" y1="-31226" y2="-31226"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="44974" x2="44974" y1="-31243" y2="-31318"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45950" x2="45950" y1="-29928" y2="-29728"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45767" x2="45781" y1="-29711" y2="-29711"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45902" x2="45931" y1="-29711" y2="-29711"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45950" x2="45950" y1="-29711" y2="-29711"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45950" x2="45950" y1="-29694" y2="-29514"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="3" x1="45815" x2="45860" y1="-29711" y2="-29711"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.8" x1="46069" x2="45819" y1="-31226" y2="-31227"/>
   <line DF8003:Layer="主干线" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1.8" x1="46137" x2="46114" y1="-31227" y2="-31227"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="0" fill="none" points="43556,-32081 43532,-32081 43532,-32129 43556,-32129 43556,-32081 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43449,-32080 43426,-32080 43426,-32127 43449,-32127 43449,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43661,-32080 43638,-32080 43638,-32127 43661,-32127 43661,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43768,-32076 43744,-32076 43744,-32123 43768,-32123 43768,-32076 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43345,-32078 43321,-32078 43321,-32126 43345,-32126 43345,-32078 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43235,-32078 43211,-32078 43211,-32126 43235,-32126 43235,-32078 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43667,-31325 43632,-31325 43632,-31395 43667,-31395 43667,-31325 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43667,-31064 43632,-31064 43632,-31134 43667,-31134 43667,-31064 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43667,-30851 43632,-30851 43632,-30921 43667,-30921 43667,-30851 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43006,-30551 42972,-30551 42972,-30621 43006,-30621 43006,-30551 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43171,-29672 43147,-29672 43147,-29719 43171,-29719 43171,-29672 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43304,-29670 43281,-29670 43281,-29718 43304,-29718 43304,-29670 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43427,-29665 43404,-29665 43404,-29712 43427,-29712 43427,-29665 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43031,-29278 43031,-29246 43095,-29246 43095,-29278 43031,-29278 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43175,-29057 43141,-29057 43141,-29125 43175,-29125 43175,-29057 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43609,-29667 43586,-29667 43586,-29714 43609,-29714 43609,-29667 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43740,-29663 43716,-29663 43716,-29711 43740,-29711 43740,-29663 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43931,-29698 43931,-29661 44005,-29661 44005,-29698 43931,-29698 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44258,-29668 44234,-29668 44234,-29715 44258,-29715 44258,-29668 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43540,-28710 43540,-28676 43610,-28676 43610,-28710 43540,-28710 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43810,-28849 43810,-28812 43884,-28812 43884,-28849 43810,-28849 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44028,-28849 44028,-28812 44103,-28812 44103,-28849 44028,-28849 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44171,-29040 44134,-29040 44134,-29114 44171,-29114 44171,-29040 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43887,-32075 43863,-32075 43863,-32122 43887,-32122 43887,-32075 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44121,-32076 44098,-32076 44098,-32124 44121,-32124 44121,-32076 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44008,-32075 43985,-32075 43985,-32122 44008,-32122 44008,-32075 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44375,-32077 44351,-32077 44351,-32125 44375,-32125 44375,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44510,-32077 44487,-32077 44487,-32124 44510,-32124 44510,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44643,-32076 44619,-32076 44619,-32124 44643,-32124 44643,-32076 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44187,-32143 44187,-32120 44234,-32120 44234,-32143 44187,-32143 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44496,-29853 44457,-29853 44457,-29930 44496,-29930 44496,-29853 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44376,-28673 44339,-28673 44339,-28747 44376,-28747 44376,-28673 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45288,-29893 45265,-29893 45265,-29940 45288,-29940 45288,-29893 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45186,-29893 45162,-29893 45162,-29941 45186,-29941 45186,-29893 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45404,-29891 45380,-29891 45380,-29938 45404,-29938 45404,-29891 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45519,-29887 45496,-29887 45496,-29935 45519,-29935 45519,-29887 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45044,-29891 45020,-29891 45020,-29938 45044,-29938 45044,-29891 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44924,-29889 44900,-29889 44900,-29937 44924,-29937 44924,-29889 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44799,-29892 44775,-29892 44775,-29940 44799,-29940 44799,-29892 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44801,-31297 44762,-31297 44762,-31373 44801,-31373 44801,-31297 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44801,-30843 44762,-30843 44762,-30920 44801,-30920 44801,-30843 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44804,-29387 44770,-29387 44770,-29454 44804,-29454 44804,-29387 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44800,-30364 44762,-30364 44762,-30441 44800,-30441 44800,-30364 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45978,-32079 45954,-32079 45954,-32127 45978,-32127 45978,-32079 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46217,-32070 46193,-32070 46193,-32117 46217,-32117 46217,-32070 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46336,-32071 46313,-32071 46313,-32118 46336,-32118 46336,-32071 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46452,-32070 46429,-32070 46429,-32117 46452,-32117 46452,-32070 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46571,-32059 46547,-32059 46547,-32107 46571,-32107 46571,-32059 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45363,-32077 45339,-32077 45339,-32124 45363,-32124 45363,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45478,-32077 45454,-32077 45454,-32125 45478,-32125 45478,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45597,-32077 45573,-32077 45573,-32124 45597,-32124 45597,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45718,-32074 45694,-32074 45694,-32121 45718,-32121 45718,-32074 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45801,-32147 45801,-32123 45848,-32123 45848,-32147 45801,-32147 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44807,-32080 44783,-32080 44783,-32127 44807,-32127 44807,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44933,-32080 44909,-32080 44909,-32128 44933,-32128 44933,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45077,-32080 45053,-32080 45053,-32127 45077,-32127 45077,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45196,-32077 45172,-32077 45172,-32124 45196,-32124 45196,-32077 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45604,-31444 45567,-31444 45567,-31518 45604,-31518 45604,-31444 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45604,-30979 45567,-30979 45567,-31053 45604,-31053 45604,-30979 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46340,-31391 46305,-31391 46305,-31461 46340,-31461 46340,-31391 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46579,-30633 46540,-30633 46540,-30710 46579,-30710 46579,-30633 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45767,-29775 45734,-29775 45734,-29841 45767,-29841 45767,-29775 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45767,-29548 45734,-29548 45734,-29614 45767,-29614 45767,-29548 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41128,-32079 41104,-32079 41104,-32126 41128,-32126 41128,-32079 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41254,-32078 41230,-32078 41230,-32125 41254,-32125 41254,-32078 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41373,-32075 41349,-32075 41349,-32122 41373,-32122 41373,-32075 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41423,-32138 41423,-32114 41470,-32114 41470,-32138 41423,-32138 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41580,-32079 41556,-32079 41556,-32126 41580,-32126 41580,-32079 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41897,-32080 41873,-32080 41873,-32127 41897,-32127 41897,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42030,-32080 42006,-32080 42006,-32127 42030,-32127 42030,-32080 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41797,-32079 41774,-32079 41774,-32126 41797,-32126 41797,-32079 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41724,-28910 41748,-28910 41748,-28863 41724,-28863 41724,-28910 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41863,-28905 41887,-28905 41887,-28858 41863,-28858 41863,-28905 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42004,-28908 42027,-28908 42027,-28860 42004,-28860 42004,-28908 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42512,-28899 42536,-28899 42536,-28852 42512,-28852 42512,-28899 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42542,-31618 42506,-31618 42506,-31689 42542,-31689 42542,-31618 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42542,-31380 42506,-31380 42506,-31451 42542,-31451 42542,-31380 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42542,-31128 42506,-31128 42506,-31198 42542,-31198 42542,-31128 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42543,-30779 42505,-30779 42505,-30856 42543,-30856 42543,-30779 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42035,-31302 41996,-31302 41996,-31381 42035,-31381 42035,-31302 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42034,-30787 41997,-30787 41997,-30861 42034,-30861 42034,-30787 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42037,-30356 41994,-30356 41994,-30442 42037,-30442 42037,-30356 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42074,-30205 42074,-30171 42140,-30171 42140,-30205 42074,-30205 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42379,-30205 42379,-30171 42445,-30171 42445,-30205 42379,-30205 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42547,-29438 42501,-29438 42501,-29529 42547,-29529 42547,-29438 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42035,-29656 41996,-29656 41996,-29735 42035,-29735 42035,-29656 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42033,-29218 41998,-29218 41998,-29287 42033,-29287 42033,-29218 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41815,-31201 41839,-31201 41839,-31248 41815,-31248 41815,-31201 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40657,-28912 40680,-28912 40680,-28865 40657,-28865 40657,-28912 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40262,-28913 40286,-28913 40286,-28865 40262,-28865 40262,-28913 " stroke="rgb(38,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="40404,-28916 40427,-28916 40427,-28869 40404,-28869 40404,-28916 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40533,-28918 40557,-28918 40557,-28870 40533,-28870 40533,-28918 " stroke="rgb(38,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="40790,-28910 40813,-28910 40813,-28862 40790,-28862 40790,-28910 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41118,-28900 41142,-28900 41142,-28852 41118,-28852 41118,-28900 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41328,-28915 41352,-28915 41352,-28867 41328,-28867 41328,-28915 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41485,-28937 41509,-28937 41509,-28889 41485,-28889 41485,-28937 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40423,-32134 40423,-32150 40392,-32150 40392,-32135 40423,-32134 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40281,-32086 40302,-32086 40303,-32129 40281,-32130 40281,-32086 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41876,-29819 41876,-29787 41940,-29787 41940,-29819 41876,-29819 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41444,-29819 41444,-29783 41516,-29783 41516,-29819 41444,-29819 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41509,-29235 41485,-29235 41485,-29282 41509,-29282 41509,-29235 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40293,-29803 40255,-29803 40255,-29879 40293,-29879 40293,-29803 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40294,-30331 40256,-30331 40256,-30407 40294,-30407 40294,-30331 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40184,-30901 40146,-30901 40146,-30978 40184,-30978 40184,-30901 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40845,-30624 40807,-30624 40807,-30700 40845,-30700 40845,-30624 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41152,-30098 41114,-30098 41114,-30174 41152,-30174 41152,-30098 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41646,-31009 41607,-31009 41607,-31085 41646,-31085 41646,-31009 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42404,-30649 42404,-30616 42470,-30616 42470,-30649 42404,-30649 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42183,-30649 42183,-30616 42250,-30616 42250,-30649 42183,-30649 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42062,-31001 42062,-30968 42128,-30968 42128,-31001 42062,-31001 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42408,-31001 42408,-30968 42474,-30968 42474,-31001 42408,-31001 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41785,-30416 41747,-30416 41747,-30492 41785,-30492 41785,-30416 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45768,-29067 45735,-29067 45735,-29133 45768,-29133 45768,-29067 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45404,-28935 45404,-28968 45470,-28968 45470,-28935 45404,-28935 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41461,-30777 41422,-30777 41422,-30853 41461,-30853 41461,-30777 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41465,-30308 41422,-30308 41422,-30394 41465,-30394 41465,-30308 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41902,-31152 41869,-31152 41869,-31217 41902,-31217 41902,-31152 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41902,-30810 41869,-30810 41869,-30876 41902,-30876 41902,-30810 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="40294,-29405 40256,-29405 40256,-29481 40294,-29481 40294,-29405 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44006,-30462 43972,-30462 43972,-30532 44006,-30532 44006,-30462 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43040,-29408 43040,-29375 43107,-29375 43107,-29408 43040,-29408 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43318,-30213 43318,-30180 43384,-30180 43384,-30213 43318,-30213 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45021,-29145 45021,-29178 45087,-29178 45087,-29145 45021,-29145 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41006,-32078 40982,-32078 40982,-32125 41006,-32125 41006,-32078 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41361,-29427 41323,-29427 41323,-29503 41361,-29503 41361,-29427 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41363,-29649 41324,-29649 41324,-29725 41363,-29725 41363,-29649 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42869,-31799 43061,-31799 42869,-31799 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42869,-31799 43061,-31799 43061,-31795 42869,-31795 42869,-31799 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43129,-31481 43255,-31481 43255,-31478 43129,-31478 43129,-31481 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43129,-31481 43255,-31481 43129,-31481 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41068,-29725 41068,-29703 41023,-29703 41023,-29725 41068,-29725 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46058,-28545 46058,-28581 46131,-28581 46131,-28545 46058,-28545 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45296,-28583 45296,-28546 45370,-28546 45370,-28583 45296,-28583 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45191,-29279 45158,-29279 45158,-29346 45191,-29346 45191,-29279 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41151,-29399 41113,-29399 41113,-29475 41151,-29475 41151,-29399 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43259,-30686 43259,-30653 43325,-30653 43325,-30686 43259,-30686 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41798,-31801 41775,-31801 41775,-31848 41798,-31848 41798,-31801 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43740,-29082 43716,-29082 43716,-29130 43740,-29130 43740,-29082 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="43895,-31451 43861,-31451 43861,-31521 43895,-31521 43895,-31451 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44265,-29474 44226,-29474 44226,-29552 44265,-29552 44265,-29474 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44262,-29128 44225,-29128 44225,-29202 44262,-29202 44262,-29128 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44262,-28829 44225,-28829 44225,-28903 44262,-28903 44262,-28829 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45190,-29003 45153,-29003 45153,-29077 45190,-29077 45190,-29003 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45294,-29337 45261,-29337 45261,-29404 45294,-29404 45294,-29337 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45190,-29446 45157,-29446 45157,-29514 45190,-29514 45190,-29446 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45190,-29576 45156,-29576 45156,-29643 45190,-29643 45190,-29576 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45293,-29532 45259,-29532 45259,-29600 45293,-29600 45293,-29532 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41799,-31657 41775,-31657 41775,-31704 41799,-31704 41799,-31657 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41798,-31858 41774,-31858 41774,-31906 41798,-31906 41798,-31858 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46085,-32079 46062,-32079 46062,-32127 46085,-32127 46085,-32079 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45484,-31424 45448,-31424 45448,-31495 45484,-31495 45484,-31424 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46085,-31785 46062,-31785 46062,-31833 46085,-31833 46085,-31785 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46085,-31495 46062,-31495 46062,-31542 46085,-31542 46085,-31495 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45484,-31652 45448,-31652 45448,-31723 45484,-31723 45484,-31652 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46079,-28847 46079,-28883 46152,-28883 46152,-28847 46079,-28847 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45769,-28882 45769,-28845 45844,-28845 45844,-28882 45769,-28882 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45227,-28849 45227,-28882 45294,-28882 45294,-28849 45227,-28849 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45769,-29279 45735,-29279 45735,-29346 45769,-29346 45769,-29279 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41284,-32330 41307,-32330 41307,-32283 41284,-32283 41284,-32330 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41689,-29526 41644,-29526 41644,-29617 41689,-29617 41689,-29526 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41379,-31483 41344,-31483 41344,-31554 41379,-31554 41379,-31483 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41511,-31007 41511,-30993 41482,-30993 41482,-31007 41511,-31007 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="41237,-29956 41237,-29942 41209,-29942 41209,-29956 41237,-29956 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="0" fill="none" points="42549,-29110 42504,-29110 42504,-29201 42549,-29201 42549,-29110 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="42033,-29068 41999,-29068 41999,-29137 42033,-29137 42033,-29068 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41758,-29497 41713,-29497 41713,-29588 41758,-29588 41758,-29497 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44942,-28935 44942,-28968 45008,-28968 45008,-28935 44942,-28935 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44819,-28830 44852,-28830 44852,-28764 44819,-28764 44819,-28830 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45726,-31818 45689,-31818 45689,-31892 45726,-31892 45726,-31818 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45976,-30112 45986,-30128 45995,-30112 45976,-30112 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45976,-30172 45986,-30156 45995,-30172 45976,-30172 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46196,-30023 46205,-30039 46214,-30023 46196,-30023 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46196,-30083 46205,-30067 46214,-30083 46196,-30083 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46397,-30030 46406,-30045 46416,-30030 46397,-30030 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46397,-30089 46406,-30073 46416,-30089 46397,-30089 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44272,-31065 44272,-31032 44338,-31032 44338,-31065 44272,-31065 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44497,-30324 44459,-30324 44459,-30401 44497,-30401 44497,-30324 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44491,-29522 44458,-29522 44458,-29590 44491,-29590 44491,-29522 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="44496,-30797 44458,-30797 44458,-30874 44496,-30874 44496,-30797 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45860,-29721 45860,-29700 45902,-29700 45902,-29721 45860,-29721 " stroke="rgb(0,0,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="41796,-31722 41773,-31722 41773,-31769 41796,-31769 41796,-31722 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="45406,-31135 45406,-31102 45472,-31102 45472,-31135 45406,-31135 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="0" fill="none" points="46216,-31573 46193,-31573 46193,-31620 46216,-31620 46216,-31573 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="主干线" fill="none" points="46188,-31239 46222,-31239 46222,-31211 46188,-31211 46188,-31239 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="主干线" fill="none" points="41532,-31585 41602,-31585 41602,-31522 41532,-31522 41532,-31585 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="43228,-31253 43237,-31269 43218,-31269 43228,-31253 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42573,-31572 42556,-31581 42556,-31562 42573,-31572 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42762,-31569 42779,-31560 42779,-31579 42762,-31569 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42558,-31501 42572,-31493 42572,-31508 42558,-31501 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42902,-31501 42918,-31491 42918,-31511 42902,-31501 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="43076,-31262 43093,-31253 43093,-31272 43076,-31262 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="43052,-31565 43061,-31582 43042,-31582 43052,-31565 " stroke="rgb(0,255,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42198,-30538 42181,-30548 42181,-30528 42198,-30538 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42232,-30984 42228,-30987 42228,-30982 42232,-30984 " stroke="rgb(255,0,0)"/>
   <polyline DF8003:Layer="图层3" fill="none" points="42276,-30984 42280,-30982 42280,-30987 42276,-30984 " stroke="rgb(255,0,0)"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45741.000000 -30285.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45741.000000 -30396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45741.000000 -30201.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45642.000000 -30284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45642.000000 -30395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45642.000000 -30200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45833.000000 -30284.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45833.000000 -30395.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45833.000000 -30200.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_2c7dd60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 46319.000000 -31103.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2dd7040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 46320.000000 -30893.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2decd40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 46063.000000 -31222.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2fd3c30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45987.000000 -30800.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_2d697d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45882.000000 -29383.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="g_29bf9c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45785.000000 -30008.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43506.000000 -32192.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43457.000000 -32119.000000) translate(0,25)">094</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43400.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43354.000000 -32118.000000) translate(0,25)">093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43612.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43563.000000 -32118.000000) translate(0,25)">095</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43718.000000 -32187.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43670.000000 -32114.000000) translate(0,25)">096</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43587.000000 -31982.000000) translate(0,24)">万德线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43712.000000 -31974.000000) translate(0,24)">新民线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43499.000000 -31988.000000) translate(0,24)">以吐庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43393.000000 -31991.000000) translate(0,24)">水库专线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43295.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43246.000000 -32116.000000) translate(0,25)">092</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43290.000000 -31983.000000) translate(0,24)">板桥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43185.000000 -32051.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43185.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43126.000000 -32120.000000) translate(0,25)">091</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43178.000000 -31986.000000) translate(0,24)">己衣集镇线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43292.000000 -32055.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43399.000000 -32053.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43495.000000 -32052.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43606.000000 -32049.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43716.000000 -32042.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42799.000000 -29876.000000) translate(0,15)">\pxsm1,ql;10kV以赤叨支线30号杆A0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43671.000000 -31111.000000) translate(0,20)">54</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43672.000000 -30897.000000) translate(0,20)">97</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43267.000000 -30721.000000) translate(0,21)">06</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="58" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43284.000000 -32393.000000) translate(0,47)">35kV己衣变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43121.000000 -29783.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43073.000000 -29710.000000) translate(0,25)">081</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="47" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43169.000000 -29895.000000) translate(0,38)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43125.000000 -29576.000000) translate(0,24)">河</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43125.000000 -29576.000000) translate(0,53)">东</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43125.000000 -29576.000000) translate(0,82)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43110.000000 -29642.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43258.000000 -29572.000000) translate(0,24)">环</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43258.000000 -29572.000000) translate(0,53)">州</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43258.000000 -29572.000000) translate(0,82)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43384.000000 -29573.000000) translate(0,24)">东</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43384.000000 -29573.000000) translate(0,53)">坡</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43384.000000 -29573.000000) translate(0,82)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43255.000000 -29782.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43206.000000 -29709.000000) translate(0,25)">083</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43378.000000 -29776.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43329.000000 -29703.000000) translate(0,25)">084</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43249.000000 -29639.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43375.000000 -29631.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42865.000000 -30723.000000) translate(0,22)">团碑线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42794.000000 -29259.000000) translate(0,15)">10kV河东线T水^M^J田村支线01号杆^M^JA01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43190.000000 -28942.000000) translate(0,15)">10kV河东线06号杆G0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43642.000000 -29702.000000) translate(0,25)">063</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43559.000000 -29640.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43560.000000 -29778.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43511.000000 -29705.000000) translate(0,25)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43560.000000 -29591.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43690.000000 -29636.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43691.000000 -29775.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43691.000000 -29587.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43918.000000 -29656.000000) translate(0,39)">012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43874.000000 -29754.000000) translate(0,39)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44207.000000 -29641.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44208.000000 -29779.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44159.000000 -29706.000000) translate(0,25)">083</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44209.000000 -29592.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44020.000000 -29753.000000) translate(0,39)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43731.000000 -29968.000000) translate(0,39)">110kV田心变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43518.000000 -29889.000000) translate(0,39)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44037.000000 -29892.000000) translate(0,39)">10kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44063.000000 -29329.000000) translate(0,19)">发块支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43562.000000 -29531.000000) translate(0,24)">普</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43562.000000 -29531.000000) translate(0,53)">龙</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43562.000000 -29531.000000) translate(0,82)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44278.000000 -29577.000000) translate(0,24)">发</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44278.000000 -29577.000000) translate(0,53)">窝</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44278.000000 -29577.000000) translate(0,82)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43740.000000 -29510.000000) translate(0,24)">田</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43740.000000 -29510.000000) translate(0,53)">心</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43740.000000 -29510.000000) translate(0,82)">集</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43740.000000 -29510.000000) translate(0,111)">镇</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43740.000000 -29510.000000) translate(0,140)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43004.000000 -28669.000000) translate(0,15)">勐果河支线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44179.000000 -29084.000000) translate(0,22)">31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43714.000000 -28705.000000) translate(0,18)">33</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44229.000000 -29357.000000) translate(0,20)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44448.000000 -31935.000000) translate(0,24)">长冲线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44418.000000 -32118.000000) translate(0,25)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43959.000000 -32000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="46" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44355.000000 -32291.000000) translate(0,37)">10kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44272.000000 -32117.000000) translate(0,25)">063</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44310.000000 -31930.000000) translate(0,24)">哪吐线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43837.000000 -32000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44543.000000 -32117.000000) translate(0,25)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43790.000000 -32115.000000) translate(0,25)">052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43911.000000 -32116.000000) translate(0,25)">053</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44023.000000 -32116.000000) translate(0,25)">054</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44172.000000 -32115.000000) translate(0,25)">012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43833.000000 -31911.000000) translate(0,24)">古普线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44325.000000 -32001.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44581.000000 -31936.000000) translate(0,24)">机关线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44072.000000 -32001.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43952.000000 -31912.000000) translate(0,24)">安德线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44064.000000 -31915.000000) translate(0,24)">花桥矿山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44461.000000 -32001.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44593.000000 -32001.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="51" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44036.000000 -32382.000000) translate(0,41)">35kV插甸变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44236.000000 -32185.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="46" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43802.000000 -32300.000000) translate(0,37)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44228.000000 -28602.000000) translate(0,23)">83</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44341.000000 -29385.000000) translate(0,21)">18</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44749.000000 -29865.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44749.000000 -30004.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44707.000000 -29931.000000) translate(0,25)">042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44750.000000 -29816.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45195.000000 -29931.000000) translate(0,25)">046</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45093.000000 -29932.000000) translate(0,25)">045</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45311.000000 -29929.000000) translate(0,25)">047</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45426.000000 -29926.000000) translate(0,25)">048</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44950.000000 -29929.000000) translate(0,25)">044</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44832.000000 -29927.000000) translate(0,25)">043</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45130.000000 -29789.000000) translate(0,24)">禄金线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44988.000000 -29748.000000) translate(0,24)">武化专线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44867.000000 -29762.000000) translate(0,24)">九近线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44744.000000 -29763.000000) translate(0,24)">滑坡线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45136.000000 -29866.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45137.000000 -29817.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44994.000000 -29863.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44995.000000 -29814.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44874.000000 -29862.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44875.000000 -29813.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45347.000000 -29750.000000) translate(0,24)">机关线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45459.000000 -29752.000000) translate(0,24)">恒通冶炼厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45227.000000 -29755.000000) translate(0,24)">姚铭线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45238.000000 -29866.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45239.000000 -29817.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45353.000000 -29863.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45355.000000 -29814.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45469.000000 -29860.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45470.000000 -29811.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45137.000000 -30005.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44994.000000 -30002.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44874.000000 -30000.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45273.000000 -30104.000000) translate(0,36)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45239.000000 -30004.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45354.000000 -30002.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45470.000000 -29999.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44808.000000 -29432.000000) translate(0,20)">G01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45583.000000 -32360.000000) translate(0,36)">35kV白路变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45744.000000 -31998.000000) translate(0,24)">站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45920.000000 -31926.000000) translate(0,24)">三合线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45928.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45929.000000 -32190.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45880.000000 -32117.000000) translate(0,25)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45929.000000 -32003.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45864.000000 -32281.000000) translate(0,32)">10kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45792.000000 -32118.000000) translate(0,25)">016</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45872.000000 -32181.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46391.000000 -31932.000000) translate(0,24)">花桥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46272.000000 -31933.000000) translate(0,24)">马鞍线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46154.000000 -31935.000000) translate(0,24)">机关线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46238.000000 -32109.000000) translate(0,25)">063</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="37" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46376.000000 -32279.000000) translate(0,30)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46166.000000 -32043.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46167.000000 -32181.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46113.000000 -32112.000000) translate(0,25)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46168.000000 -31993.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46286.000000 -32043.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46287.000000 -32182.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46287.000000 -31994.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46402.000000 -32043.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46403.000000 -32181.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46354.000000 -32108.000000) translate(0,25)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46404.000000 -31993.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46521.000000 -32032.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46522.000000 -32170.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46473.000000 -32097.000000) translate(0,25)">065</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46522.000000 -31983.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46509.000000 -31922.000000) translate(0,24)">勒外线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45379.000000 -32116.000000) translate(0,25)">053</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45422.000000 -31926.000000) translate(0,24)">岔河线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45641.000000 -31949.000000) translate(0,24)">白路集镇线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45756.000000 -32175.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45539.000000 -31923.000000) translate(0,24)">石腊它线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45365.000000 -32278.000000) translate(0,32)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45427.000000 -32050.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45428.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45429.000000 -32001.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45547.000000 -32049.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45547.000000 -32188.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45498.000000 -32115.000000) translate(0,25)">054</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45548.000000 -32000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45668.000000 -32046.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45668.000000 -32185.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45619.000000 -32112.000000) translate(0,25)">055</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45669.000000 -31997.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45302.000000 -31922.000000) translate(0,24)">关坡线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44725.000000 -31953.000000) translate(0,24)">大沙线（无线路）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45016.000000 -31934.000000) translate(0,24)">二级站线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44877.000000 -31936.000000) translate(0,24)">乐茂河线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44835.000000 -32119.000000) translate(0,25)">012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="47" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44940.000000 -32290.000000) translate(0,38)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44756.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44757.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44708.000000 -32118.000000) translate(0,25)">011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44758.000000 -32003.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44883.000000 -32053.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44883.000000 -32192.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44884.000000 -32004.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45027.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45027.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44979.000000 -32117.000000) translate(0,25)">013</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45028.000000 -32003.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45142.000000 -31925.000000) translate(0,24)">小电站 线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45313.000000 -32049.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45314.000000 -32188.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45265.000000 -32115.000000) translate(0,25)">052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45314.000000 -32000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45146.000000 -32049.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45146.000000 -32188.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45099.000000 -32117.000000) translate(0,25)">014</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45147.000000 -32000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45169.000000 -30549.000000) translate(0,19)">05</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45126.000000 -30475.000000) translate(0,24)">小电站 线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45248.000000 -30493.000000) translate(0,16)">10kV石腊它线^M^J79号杆G003隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45573.000000 -30676.000000) translate(0,16)">55</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45810.000000 -30677.000000) translate(0,22)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45886.000000 -30656.000000) translate(0,16)">石腊它机关支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45898.000000 -30787.000000) translate(0,16)">10kV石腊它机关支线T石腊它村</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45898.000000 -30787.000000) translate(0,35)">支线01号杆G0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="33" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45708.000000 -30554.000000) translate(0,27)">35kV猫街变移动式变电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="36" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46254.000000 -30556.000000) translate(0,29)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45771.000000 -29819.000000) translate(0,20)">04</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45737.000000 -29399.000000) translate(0,16)">42+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45771.000000 -29586.000000) translate(0,20)">30</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45895.000000 -29441.000000) translate(0,22)">02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46550.000000 -29407.000000) translate(0,16)">06</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46121.000000 -29420.000000) translate(0,19)">上狮子口支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46516.000000 -29626.000000) translate(0,24)">勒外线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40922.000000 -32302.000000) translate(0,36)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41155.000000 -32119.000000) translate(0,25)">433</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41030.000000 -32064.000000) translate(0,24)">大矣波矿山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40957.000000 -32051.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40957.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(38,0,0)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40908.000000 -32116.000000) translate(0,25)">431</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40958.000000 -32002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41077.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41078.000000 -32190.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41029.000000 -32117.000000) translate(0,25)">432</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41079.000000 -32002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41147.000000 -32052.000000) translate(0,24)">红土田冶炼厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41518.000000 -31919.000000) translate(0,24)">西北片区线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41310.000000 -31958.000000) translate(0,24)">赵家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41826.000000 -31972.000000) translate(0,24)">吆鹰线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41950.000000 -31913.000000) translate(0,24)">城区II^M^J回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42466.000000 -32161.000000) translate(0,24)">城区I^M^J回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41692.000000 -32067.000000) translate(0,24)">狮高线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41323.000000 -32047.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41324.000000 -32186.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41275.000000 -32113.000000) translate(0,25)">434</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41324.000000 -31998.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41203.000000 -32051.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41204.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41205.000000 -32002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41408.000000 -32174.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41580.000000 -32123.000000) translate(0,25)">441</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41409.000000 -32107.000000) translate(0,25)">412</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41471.000000 -32182.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41530.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41530.000000 -32190.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41531.000000 -32002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41846.000000 -32053.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41847.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41798.000000 -32118.000000) translate(0,25)">442</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41848.000000 -32004.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41699.000000 -32117.000000) translate(0,25)">445</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41975.000000 -32053.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41975.000000 -32191.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41926.000000 -32118.000000) translate(0,25)">444</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41981.000000 -32004.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41897.000000 -32305.000000) translate(0,36)">10kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41747.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41748.000000 -32190.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41748.000000 -32002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41652.000000 -28901.000000) translate(0,25)">455</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41692.000000 -28855.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41697.000000 -28955.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="65" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41696.000000 -28711.000000) translate(0,52)">110kV武定变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41932.000000 -28900.000000) translate(0,25)">452</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41973.000000 -28851.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41977.000000 -28955.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41790.000000 -28895.000000) translate(0,25)">453</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41829.000000 -28850.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41834.000000 -28950.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42442.000000 -28890.000000) translate(0,25)">451</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42487.000000 -28840.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42478.000000 -28946.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41692.000000 -29004.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41680.000000 -29246.000000) translate(0,24)">化工厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42127.000000 -30156.000000) translate(0,24)">武康南路联络线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41971.000000 -29004.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42480.000000 -29000.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41829.000000 -28999.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41815.000000 -29256.000000) translate(0,24)">亚麻厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42205.000000 -30579.000000) translate(0,24)">中新街支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42137.000000 -30958.000000) translate(0,24)">复兴街联络线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42262.000000 -29898.000000) translate(0,15)">09号塔G003隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42037.000000 -29483.000000) translate(0,15)">10kV城区Ⅱ回线22号塔G003隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42508.000000 -30998.000000) translate(0,20)">35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42002.000000 -30995.000000) translate(0,18)">59</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42507.000000 -30647.000000) translate(0,21)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41999.000000 -30645.000000) translate(0,20)">45</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42511.000000 -30200.000000) translate(0,18)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42001.000000 -30198.000000) translate(0,18)">40</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41867.000000 -31401.000000) translate(0,22)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41551.000000 -31398.000000) translate(0,21)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41601.000000 -31329.000000) translate(0,16)">静^M^J城^M^J路^M^J支^M^J线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41813.000000 -30742.000000) translate(0,16)">07</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41833.000000 -31081.000000) translate(0,19)">牡^M^J丹^M^J路^M^J联^M^J络^M^J线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41671.000000 -30726.000000) translate(0,14)">牡丹路支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40190.000000 -28900.000000) translate(0,25)">035</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40239.000000 -28853.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40233.000000 -28960.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40227.000000 -29009.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40461.000000 -28904.000000) translate(0,25)">037</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40330.000000 -28902.000000) translate(0,25)">033</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40379.000000 -28857.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40372.000000 -28962.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40367.000000 -29011.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40583.000000 -28896.000000) translate(0,25)">034</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40626.000000 -28956.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40621.000000 -29005.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40506.000000 -28853.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40504.000000 -28964.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40499.000000 -29014.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41415.000000 -28921.000000) translate(0,25)">032</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41047.000000 -28888.000000) translate(0,25)">036</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41085.000000 -28847.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41090.000000 -28948.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41085.000000 -28997.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40716.000000 -28893.000000) translate(0,25)">038</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40760.000000 -28847.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40758.000000 -28953.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40753.000000 -29002.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41256.000000 -28903.000000) translate(0,25)">031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41308.000000 -28858.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41302.000000 -28964.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41303.000000 -29013.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41469.000000 -28870.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41464.000000 -28981.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41459.000000 -29031.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41444.000000 -29261.000000) translate(0,24)">钛白粉线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41079.000000 -29266.000000) translate(0,24)">二龙山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41296.000000 -29308.000000) translate(0,24)">城区Ⅲ^J回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40754.000000 -29285.000000) translate(0,24)">水泥厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40617.000000 -29233.000000) translate(0,24)">盛源线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40494.000000 -29232.000000) translate(0,24)">新立线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40362.000000 -29230.000000) translate(0,24)">永丰线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40223.000000 -29338.000000) translate(0,24)">铺西矿山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40629.000000 -28849.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40102.986095 -31909.406694) translate(0,28)">五</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40102.986095 -31862.406694) translate(0,28)">凤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40101.985182 -32280.301980) translate(0,32)">10kVⅡ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40738.986400 -32124.441599) translate(0,26)">095</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40612.986400 -32122.441599) translate(0,26)">094</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40767.986552 -31907.459052) translate(0,25)">钛</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40767.986552 -31864.459052) translate(0,25)">白</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40382.000000 -32129.000000) translate(0,20)">012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40077.986400 -32127.441599) translate(0,26)">082</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40235.986552 -31872.459052) translate(0,25)">倘</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40238.986552 -31826.459052) translate(0,25)">寻</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40237.986552 -31783.459052) translate(0,25)">高</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40236.986552 -31918.459052) translate(0,25)">武</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40198.000000 -32128.000000) translate(0,28)">086</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40102.986095 -31816.406694) translate(0,28)">山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40103.986095 -31764.406694) translate(0,28)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40238.986552 -31691.459052) translate(0,25)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40237.986552 -31739.459052) translate(0,25)">速</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40522.986095 -31821.406694) translate(0,28)">间</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40522.986095 -31775.406694) translate(0,28)">隔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40522.986095 -31912.406694) translate(0,28)">备</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40522.986095 -31867.406694) translate(0,28)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40767.986552 -31822.459052) translate(0,25)">粉</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40766.986552 -31769.459052) translate(0,25)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40766.986552 -31721.459052) translate(0,25)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40486.986095 -32131.406694) translate(0,28)">093</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40540.000000 -32194.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40669.000000 -32193.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40784.000000 -32189.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40322.000000 -32193.000000) translate(0,18)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40454.000000 -32190.000000) translate(0,18)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40133.000000 -32195.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40255.000000 -32189.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40537.000000 -32062.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40667.000000 -32061.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40782.000000 -32058.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40128.000000 -32084.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40251.000000 -32081.000000) translate(0,25)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40537.000000 -32015.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40666.000000 -32014.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40782.000000 -32011.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40129.000000 -32047.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40253.000000 -32044.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="41" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40521.985029 -32281.284527) translate(0,33)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42004.000000 -29814.000000) translate(0,16)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41547.000000 -29769.000000) translate(0,24)">狮山北路联络线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41324.000000 -29816.000000) translate(0,24)">43</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40888.000000 -32062.000000) translate(0,24)">二龙山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41483.000000 -29357.000000) translate(0,16)">21</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40249.000000 -29737.000000) translate(0,32)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41948.000000 -29304.000000) translate(0,24)">城区II^M^J回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42434.000000 -29321.000000) translate(0,24)">城区I^M^J回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="79" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44769.000000 -28387.000000) translate(0,64)">武定配网10kV网络图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44644.000000 -28212.000000) translate(0,48)">制图</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44644.000000 -28071.000000) translate(0,48)">审核</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45239.000000 -28071.000000) translate(0,48)">批准</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45235.000000 -28214.000000) translate(0,48)">现场</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45913.000000 -28210.000000) translate(0,48)">绘制日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45913.000000 -28071.000000) translate(0,48)">生效日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="41" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46059.000000 -28352.000000) translate(0,33)">DWJX-WDPW-2019-001</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46196.000000 -28221.000000) translate(0,32)">2019年12月26日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46196.000000 -28084.000000) translate(0,32)">2019年12月26日</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41611.000000 -30743.000000) translate(0,20)">06</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40961.000000 -31393.000000) translate(0,16)">11T10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40872.000000 -31338.000000) translate(0,28)">二龙山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41002.000000 -31326.000000) translate(0,21)">西和支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="28" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41113.000000 -29729.000000) translate(0,23)">19</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="68" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40420.000000 -28701.000000) translate(0,55)">35kV白邑变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="58" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41427.000000 -32320.000000) translate(0,47)">35kV近城变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="58" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40202.000000 -32380.000000) translate(0,47)">110kV西和变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41511.000000 -31304.000000) translate(0,24)">西北片区线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43367.000000 -32306.000000) translate(0,39)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44826.000000 -32380.000000) translate(0,36)">大响水电站</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="37" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46179.000000 -32338.000000) translate(0,30)">35kV高桥变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="62" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41177.000000 -28709.000000) translate(0,50)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="64" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42303.000000 -28718.000000) translate(0,52)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43133.000000 -29974.000000) translate(0,40)">35kV东坡变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="45" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44901.000000 -30134.000000) translate(0,36)">35kV九厂变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41003.000000 -30281.000000) translate(0,17)">西和支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41150.000000 -29926.000000) translate(0,24)">二龙山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40316.000000 -30234.000000) translate(0,24)">铺西矿山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43496.000000 -30759.000000) translate(0,21)">01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42973.000000 -30683.000000) translate(0,21)">23</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43142.000000 -29276.000000) translate(0,16)">89+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43141.000000 -28707.000000) translate(0,21)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44421.000000 -29375.000000) translate(0,22)">螃蟹箐支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44371.000000 -31031.000000) translate(0,24)">古普线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44423.000000 -29791.000000) translate(0,24)">古普线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44801.000000 -30771.000000) translate(0,24)">长冲线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45594.000000 -30648.000000) translate(0,16)">石腊它机关支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41865.000000 -30671.000000) translate(0,16)">120</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41710.000000 -30548.000000) translate(0,22)">老^M^J插^M^J甸^M^J支^M^J线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41900.000000 -30720.000000) translate(0,24)">吆鹰线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45355.000000 -28962.000000) translate(0,16)">40</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45576.000000 -28963.000000) translate(0,16)">93</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45471.000000 -29138.000000) translate(0,19)">10kV迤纳厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45471.000000 -29138.000000) translate(0,42)">69号杆G04断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45793.000000 -29358.000000) translate(0,19)">10kV迤纳厂线T上狮子口支</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45793.000000 -29358.000000) translate(0,42)">线02号杆A0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45559.000000 -29603.000000) translate(0,19)">10kV迤纳厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45559.000000 -29603.000000) translate(0,42)">30号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46311.000000 -30702.000000) translate(0,19)">10kV勒外线27号^M^J杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46216.000000 -31461.000000) translate(0,13)">10kV马鞍</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46216.000000 -31461.000000) translate(0,29)">线10号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46216.000000 -31461.000000) translate(0,45)">G01断路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46216.000000 -31461.000000) translate(0,61)">器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46130.000000 -31161.000000) translate(0,19)">10kV马鞍线28号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46130.000000 -31161.000000) translate(0,42)">杆G0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46120.000000 -30959.000000) translate(0,19)">10kV马鞍线67号杆</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46120.000000 -30959.000000) translate(0,42)">G0R2跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45618.000000 -31516.000000) translate(0,19)">10kV石腊它线26</text>
   <text DF8003:Layer="0" fill="rgb(255,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45618.000000 -31516.000000) translate(0,42)">号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44550.000000 -31381.000000) translate(0,19)">10kV长冲线08^M^J号塔G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44803.000000 -30915.000000) translate(0,19)">10kV长冲线29^M^J号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44804.000000 -30440.000000) translate(0,19)">10kV长冲线51^M^J号杆G03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41792.000000 -30490.000000) translate(0,19)">01号杆A01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43906.000000 -31523.000000) translate(0,19)">10kV古普线06</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43906.000000 -31523.000000) translate(0,42)">号塔G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44181.000000 -30869.000000) translate(0,19)">10kV古普线75号杆^JG03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44203.000000 -30404.000000) translate(0,19)">10kV古普线121号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44203.000000 -30404.000000) translate(0,42)">杆G05断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43409.000000 -31136.000000) translate(0,19)">10kV万德线54^M^J号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43409.000000 -31397.000000) translate(0,19)">10kV万德线02^M^J号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43409.000000 -30929.000000) translate(0,19)">10kV万德线97^M^J号杆G03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43662.000000 -30459.000000) translate(0,24)">万德线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43434.000000 -30638.000000) translate(0,19)">10kV团碑线01^M^J号杆A0R1跌^M^J落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43200.000000 -30647.000000) translate(0,19)">10kV团碑线06^M^J号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42997.000000 -30804.000000) translate(0,19)">10kV团碑线10号杆^M^JA0R2跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43015.000000 -30528.000000) translate(0,15)">\pxsm0.75;10kV团碑线T以潘德支线06+1号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42896.000000 -30370.000000) translate(0,17)">以^M^J潘^M^J德^M^J支^M^J线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42963.000000 -29120.000000) translate(0,15)">10kV河东线35^M^J号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43501.000000 -28666.000000) translate(0,15)">10kV田心集镇线T勐果河支线01号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43802.000000 -28799.000000) translate(0,15)">10kV田心集镇线T麻栗树支线10号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43984.000000 -28787.000000) translate(0,15)">10kV田心集镇线T发块支线01号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43938.000000 -29132.000000) translate(0,15)">10kV田心集镇</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43938.000000 -29132.000000) translate(0,33)">线T发块支线31</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43938.000000 -29132.000000) translate(0,51)">号杆A02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44504.000000 -29926.000000) translate(0,19)">10kV古普线^J139号杆G06^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44508.000000 -29606.000000) translate(0,16)">10kV古普线167号杆G07断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42547.000000 -31690.000000) translate(0,19)">10kV城区I回线53号塔G07断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42553.000000 -31451.000000) translate(0,19)">10kV城区I回线52号塔G06断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42554.000000 -31198.000000) translate(0,19)">10kV城区I回线36号塔G05断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42550.000000 -30854.000000) translate(0,19)">\pxsm0.75;10kV城区I回线28号塔G04断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42248.000000 -29534.000000) translate(0,19)">10kV城区I回线06号塔G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42043.000000 -31379.000000) translate(0,19)">10kV城区Ⅱ回线61塔G07断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42042.000000 -30862.000000) translate(0,19)">10kV城区Ⅱ回线49号塔G06断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42038.000000 -30441.000000) translate(0,19)">10kV城区Ⅱ回线44塔G05断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42043.000000 -29732.000000) translate(0,19)">10kV城区Ⅱ回线29号塔G04断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42040.000000 -29287.000000) translate(0,19)">10kV城区Ⅱ回线07号塔G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41322.000000 -30187.000000) translate(0,24)">64</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41537.000000 -31578.000000) translate(0,21)">\T0.75;05+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41463.000000 -30865.000000) translate(0,16)">10kV西北片</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41463.000000 -30865.000000) translate(0,35)">区线T静山</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41463.000000 -30865.000000) translate(0,54)">路支线02号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41463.000000 -30865.000000) translate(0,73)">塔A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41475.000000 -30430.000000) translate(0,19)">10kV西北片区^M^J线T静山路支^M^J线12号塔A02^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41648.000000 -31091.000000) translate(0,19)">10kV西北片^M^J区线T静城路^M^J支线02号塔^M^JA01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41164.000000 -30194.000000) translate(0,19)">10kV二龙^M^J山线41号^M^J杆G02断^M^J路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41010.000000 -30723.000000) translate(0,19)">10kV二龙山线^M^JT西和支线11号^M^J杆A001隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40604.000000 -30695.000000) translate(0,19)">10kV钛白粉厂^M^J线08号杆G01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40296.000000 -29880.000000) translate(0,19)">10kV铺西矿山线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40296.000000 -29880.000000) translate(0,42)">13号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40296.000000 -30404.000000) translate(0,19)">10kV铺西矿山线^M^J33号杆G03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40194.000000 -30982.000000) translate(0,19)">10kV五凤山线^M^J08号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45825.000000 -29309.000000) translate(0,16)">（正常在拉开位置）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45308.000000 -30418.000000) translate(0,16)">（正常在拉开位置）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46119.000000 -30905.000000) translate(0,16)">（正常在拉开位置）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44497.000000 -29499.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42970.000000 -29055.000000) translate(0,14)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42532.000000 -30768.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41461.000000 -30263.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41667.000000 -31278.000000) translate(0,15)">\pxsm0.75;10kV牡丹路支线08号塔A01断路器(正常处冷备用)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41904.000000 -31223.000000) translate(0,16)">10kV吆鹰</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41904.000000 -31223.000000) translate(0,35)">线27号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41904.000000 -31223.000000) translate(0,54)">G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41903.000000 -30891.000000) translate(0,13)">10kV吆鹰^M^J线85号杆^M^JG03断路^M^J器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40061.000000 -29482.000000) translate(0,19)">10kV铺西矿山^M^J线06号杆G01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40646.986095 -31905.406694) translate(0,28)">备</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40646.986095 -31859.406694) translate(0,28)">用</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40645.986095 -31814.406694) translate(0,28)">间</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(0.999848 0.017452 -0.017452 0.999848 40645.986095 -31767.406694) translate(0,28)">隔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43736.000000 -30755.000000) translate(0,21)">01</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43675.000000 -30633.000000) translate(0,19)">10kV万德线T万^M^J德集镇线01号杆^M^JA0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43142.000000 -29408.000000) translate(0,21)">97</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45625.000000 -31055.000000) translate(0,19)">10kV石腊它线37^M^J号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42361.000000 -30752.000000) translate(0,16)">11号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42361.000000 -30752.000000) translate(0,35)">A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42154.000000 -30715.000000) translate(0,16)">01号塔^M^JA02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42342.000000 -31121.000000) translate(0,15)">04号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42342.000000 -31121.000000) translate(0,33)">A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42342.000000 -31121.000000) translate(0,51)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42048.000000 -31052.000000) translate(0,12)">01号塔^M^JA02断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42055.000000 -30316.000000) translate(0,16)">16号塔^M^JA02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42383.000000 -30276.000000) translate(0,16)">01号塔^M^JA01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41893.000000 -31074.000000) translate(0,13)">10kV吆鹰线^M^J73号杆G002^M^J隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41846.000000 -29924.000000) translate(0,16)">11号塔^M^JA02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41411.000000 -29888.000000) translate(0,16)">01号塔^M^JA01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44013.000000 -30530.000000) translate(0,19)">10kV东万线107^M^J号塔A03断路器^M^J</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42825.000000 -29364.000000) translate(0,19)">10kV东万线01^M^J号塔A01断路器^M^J</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43254.000000 -30293.000000) translate(0,19)">10kV东万线44^M^J号塔A02断路器^M^J</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44923.000000 -29134.000000) translate(0,19)">10kV九近线^M^J07号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43676.000000 -30508.000000) translate(0,16)">（正常在拉开位置）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41368.000000 -29522.000000) translate(0,16)">10kV城区^M^JⅢ回线15^M^J号塔G01断^M^J路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41368.000000 -29735.000000) translate(0,16)">10kV城区Ⅲ^M^J回线34号塔^M^JG02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41370.000000 -30029.000000) translate(0,16)">10kV城区Ⅲ回线52号^M^J塔G003隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42021.000000 -30612.000000) translate(0,15)">0号塔A003^M^J隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42537.000000 -31552.000000) translate(0,13)">(ZRC-YJV22-8.7/15KV-3*300)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42585.000000 -31490.000000) translate(0,11)">(ZRC-YJV22-8.7/15KV-3*240)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="7" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42137.000000 -30636.000000) translate(0,6)">01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43079.000000 -31576.000000) translate(0,20)">10kV狮山大道联^M^J络线1号环网柜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42875.000000 -31899.000000) translate(0,20)">10kV城区Ⅰ回^M^J线1号环网柜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43243.000000 -31435.000000) translate(0,14)">（正常在断开位置）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42162.000000 -31037.000000) translate(0,8)">08号塔A004^M^J  隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42251.000000 -31038.000000) translate(0,8)">07号塔A003^M^J  隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41128.000000 -29688.000000) translate(0,12)">10kV二龙山线T西和支线39号塔A02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46040.000000 -28654.000000) translate(0,39)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45874.000000 -28662.000000) translate(0,39)">0646</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45165.000000 -29201.000000) translate(0,16)">86</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45206.000000 -29322.000000) translate(0,12)">倪家坡支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45136.000000 -28799.000000) translate(0,16)">倪家坡支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45159.000000 -28575.000000) translate(0,16)">26</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45248.000000 -28535.000000) translate(0,15)">牡丹变10kV禄金线18号杆G04断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45088.000000 -29407.000000) translate(0,12)">10kV禄金线^M^J84号杆G03^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41156.000000 -29485.000000) translate(0,16)">10kV二龙山^M^J线04号杆G01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(38,0,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45652.000000 -31432.000000) translate(0,16)">（不能操作）</text>
   <text DF8003:Layer="0" fill="rgb(38,0,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45668.000000 -30972.000000) translate(0,16)">（不能操作）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40045.000000 -29359.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41659.000000 -31841.000000) translate(0,15)">44号杆G02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42299.000000 -30684.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42039.000000 -30357.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42032.000000 -30244.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41801.000000 -29856.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43025.000000 -30477.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41592.000000 -31596.000000) translate(0,12)">10kV狮高线137号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="14" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41592.000000 -31596.000000) translate(0,26)">    G005隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40302.000000 -30614.000000) translate(0,19)">10kV铺西矿山线^M^J53号杆G004隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40279.000000 -30704.000000) translate(0,19)">10kV庄房村分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40587.000000 -31627.000000) translate(0,16)">138</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40286.000000 -30861.000000) translate(0,19)">10kV庄房村分支线^M^J18号杆A001隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40620.000000 -31651.000000) translate(0,8)">10kV狮高线138号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40590.000000 -31542.000000) translate(0,21)">31</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40629.000000 -31533.000000) translate(0,8)">10kV庄房村分支线^M^J     31号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41427.000000 -30553.000000) translate(0,20)">09</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41451.000000 -30628.000000) translate(0,16)">10KV静城路支线09号塔^M^JA002隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41454.000000 -30513.000000) translate(0,16)">（正常在拉开位置）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41147.000000 -28870.000000) translate(0,14)">（正常^M^J处热备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41500.000000 -28961.000000) translate(0,14)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44813.000000 -30353.000000) translate(0,16)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43937.000000 -29032.000000) translate(0,14)">（正常处冷备用/</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43937.000000 -29032.000000) translate(0,31)">相序不正确，不具备</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43937.000000 -29032.000000) translate(0,48)">转供电条件）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44913.000000 -29057.000000) translate(0,14)">（正常处冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45078.000000 -29256.000000) translate(0,14)">（正常处^M^J冷备用）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43754.000000 -29176.000000) translate(0,15)">10kV田心集镇线05号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41520.000000 -29326.000000) translate(0,19)">10kV钛白粉线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41520.000000 -29326.000000) translate(0,42)">20号杆G02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41520.000000 -29326.000000) translate(0,65)">断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43713.000000 -28844.000000) translate(0,18)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44085.000000 -29538.000000) translate(0,15)">10kV发窝线01号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44085.000000 -29538.000000) translate(0,33)">杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44267.000000 -29176.000000) translate(0,19)">G02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44149.000000 -29024.000000) translate(0,19)">10kV发窝线75^M^J号杆G03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45258.000000 -29174.000000) translate(0,21)">22</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45184.000000 -29123.000000) translate(0,19)">10kV^M^J倪家坡支线^M^J05号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44905.000000 -29174.000000) translate(0,21)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45296.000000 -29598.000000) translate(0,12)">10kV姚铭线^M^J3号杆G01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45048.000000 -29498.000000) translate(0,12)">10kV禄金线^M^J46号杆G02^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45042.000000 -29635.000000) translate(0,12)">10kV禄金线^M^J21号杆G01^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45304.000000 -29405.000000) translate(0,12)">10kV姚铭线^M^J16号杆G02^M^J断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44399.000000 -28736.000000) translate(0,15)">10kV发窝线螃蟹箐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44399.000000 -28736.000000) translate(0,33)">支线01号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44911.000000 -28209.000000) translate(0,48)">李兴灵</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41587.000000 -31730.000000) translate(0,15)">\pxsm0.75;\T0.75;97号杆G04断路器(正常处冷备用)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41665.000000 -31896.000000) translate(0,15)">20号塔G01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40629.000000 -31341.000000) translate(0,21)">10kV庄房村分支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40217.000000 -31033.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40342.000000 -30444.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40303.000000 -29770.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40159.000000 -29548.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41806.000000 -31644.000000) translate(0,19)">G042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41748.000000 -31923.000000) translate(0,19)">\W0.5;\T0.75;G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41703.000000 -31785.000000) translate(0,15)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40698.000000 -30751.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40711.000000 -30581.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41501.000000 -29198.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41162.000000 -29519.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41066.000000 -29760.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40962.000000 -29757.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41045.000000 -30070.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41266.000000 -29406.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41350.000000 -29554.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41364.000000 -29625.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41372.000000 -29794.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41559.000000 -29837.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41781.000000 -29800.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41929.000000 -29785.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41460.000000 -30224.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41467.000000 -30479.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41447.000000 -30920.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41655.000000 -31133.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41776.000000 -31164.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41797.000000 -31317.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41904.000000 -31260.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41898.000000 -30923.000000) translate(0,19)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41769.000000 -30390.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44837.000000 -30482.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44825.000000 -30969.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44670.000000 -31257.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42039.000000 -29195.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42055.000000 -29620.000000) translate(0,19)">G041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42033.000000 -30151.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42163.000000 -30219.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42446.000000 -30161.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42308.000000 -30263.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42049.000000 -30489.000000) translate(0,19)">G052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42151.000000 -30619.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42250.000000 -30613.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42359.000000 -30600.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42043.000000 -30761.000000) translate(0,19)">G061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42029.000000 -30895.000000) translate(0,19)">G062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42021.000000 -30955.000000) translate(0,16)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42134.000000 -30980.000000) translate(0,16)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42349.000000 -31021.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42466.000000 -31024.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42051.000000 -31432.000000) translate(0,19)">G072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42552.000000 -29409.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42563.000000 -30725.000000) translate(0,19)">G041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42550.000000 -30902.000000) translate(0,19)">G042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42554.000000 -31259.000000) translate(0,19)">G052</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42573.000000 -31364.000000) translate(0,19)">G061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42540.000000 -31731.000000) translate(0,19)">G072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43185.000000 -29164.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43179.000000 -29040.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43088.000000 -29237.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43102.000000 -29368.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42975.000000 -29450.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42872.000000 -30538.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43194.000000 -30717.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43673.000000 -30833.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43677.000000 -30948.000000) translate(0,19)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43679.000000 -31041.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43675.000000 -31172.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43666.000000 -31306.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43668.000000 -31463.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43244.000000 -30171.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43391.000000 -30168.000000) translate(0,19)">A022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44026.000000 -30440.000000) translate(0,19)">A031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44015.000000 -30567.000000) translate(0,19)">A032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43462.000000 -28759.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43751.000000 -29054.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43879.000000 -28893.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44081.000000 -28893.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44270.000000 -29228.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44257.000000 -29111.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44260.000000 -29444.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44258.000000 -28934.000000) translate(0,19)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44254.000000 -28810.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44397.000000 -28656.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44394.000000 -28778.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44398.000000 -29833.000000) translate(0,19)">G062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43906.000000 -31563.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44804.000000 -29495.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45475.000000 -28996.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45647.000000 -29180.000000) translate(0,19)">G041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45786.000000 -29539.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44935.000000 -29222.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45096.000000 -29201.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45087.000000 -29291.000000) translate(0,19)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45179.000000 -29442.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45179.000000 -29558.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45294.000000 -29508.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45299.000000 -29436.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45221.000000 -28621.000000) translate(0,19)">G042</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45413.000000 -28632.000000) translate(0,19)">G041</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46468.000000 -30765.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46483.000000 -30598.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46257.000000 -31519.000000) translate(0,13)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46259.000000 -31360.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45484.000000 -30956.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45611.000000 -31574.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45191.000000 -28983.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44078.000000 -28965.000000) translate(0,19)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46035.000000 -32052.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45987.000000 -32117.000000) translate(0,25)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46037.000000 -32003.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46030.000000 -32190.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45736.000000 -31663.000000) translate(0,19)">10kV白路集镇线T张家支线05号杆A0R1跌落熔断器(正常在拉开位置)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46011.000000 -31926.000000) translate(0,24)">洒胶泥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46091.000000 -31571.000000) translate(0,11)">\pxsm0.75;10kV洒胶泥线55号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46093.000000 -31470.000000) translate(0,14)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46092.000000 -31863.000000) translate(0,11)">\pxsm0.75;10kV洒胶泥线03号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46089.000000 -31745.000000) translate(0,11)">G012</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45255.000000 -31515.000000) translate(0,19)">10kV岔河线91</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45255.000000 -31515.000000) translate(0,42)">号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45255.000000 -31515.000000) translate(0,65)">(正常在拉开位置)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45393.000000 -31390.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45386.000000 -31550.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45689.000000 -31700.000000) translate(0,21)">07</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46057.000000 -31700.000000) translate(0,21)">08</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45746.000000 -31347.000000) translate(0,19)">10kV洒胶泥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45396.000000 -31334.000000) translate(0,19)">\pxsm0.75;10kV洒胶泥线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45470.000000 -31300.000000) translate(0,13)">78</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45470.000000 -31329.000000) translate(0,13)">109</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="58" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41463.000000 -28173.000000) translate(0,47)">注：图中洋红色线圈内为本次修改部分。</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="58" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40168.000000 -28439.000000) translate(0,47)">注：图中蓝色部分断路器及隔离开关表示常断点，该图仅表示10kV线路联络关系，具体线路间隔情况已现场实际为主，虚线部分为待实施的联络线。</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42987.000000 -29232.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45258.000000 -31750.000000) translate(0,19)">10kV岔河线10号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45474.000000 -31778.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45472.000000 -31621.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41162.000000 -29609.000000) translate(0,19)">G001</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41012.000000 -29613.000000) translate(0,19)">12号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46060.000000 -28956.000000) translate(0,39)">066</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="48" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45895.000000 -28964.000000) translate(0,39)">0666</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45488.000000 -28934.000000) translate(0,19)">10kV滑坡线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45488.000000 -28934.000000) translate(0,42)">40号杆G03</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45041.000000 -28983.000000) translate(0,19)">滑坡线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45139.000000 -28879.000000) translate(0,19)">25</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45294.000000 -28916.000000) translate(0,19)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45220.000000 -28913.000000) translate(0,19)">A01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45195.000000 -28823.000000) translate(0,19)">工业园区支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45792.000000 -28917.000000) translate(0,19)">G01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45689.000000 -28910.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45923.000000 -28828.000000) translate(0,19)">工业园区Ⅰ回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45740.000000 -28852.000000) translate(0,19)">01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45295.000000 -28855.000000) translate(0,19)">02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45412.000000 -28908.000000) translate(0,19)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45452.000000 -28858.000000) translate(0,19)">兴裕食品厂支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45602.000000 -28910.000000) translate(0,19)">03</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45562.000000 -29351.000000) translate(0,19)">10kV迤纳厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45562.000000 -29351.000000) translate(0,42)">44号杆G03断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45648.000000 -29264.000000) translate(0,19)">G032</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41315.000000 -32321.000000) translate(0,25)">435</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41256.000000 -32284.000000) translate(0,25)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41255.000000 -32371.000000) translate(0,25)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="31" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41308.000000 -32401.000000) translate(0,25)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="27" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42198.000000 -32404.000000) translate(0,22)">10kV城区Ⅰ回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43908.000000 -31443.000000) translate(0,19)">（已短接）</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41253.000000 -31131.000000) translate(0,20)">化工厂线01号塔T赵家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41611.000000 -29483.000000) translate(0,15)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41522.000000 -29669.000000) translate(0,16)">10kV化工厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41522.000000 -29669.000000) translate(0,36)">线01号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41522.000000 -29669.000000) translate(0,56)">T赵家庄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41522.000000 -29669.000000) translate(0,76)">线01号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41522.000000 -29669.000000) translate(0,96)">A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41732.000000 -29388.000000) translate(0,16)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41753.000000 -29393.000000) translate(0,19)">1号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41313.000000 -30900.000000) translate(0,14)">赵家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41148.000000 -30581.000000) translate(0,19)">10kV化工厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41148.000000 -30581.000000) translate(0,42)">线01号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41148.000000 -30581.000000) translate(0,65)">T赵家庄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41148.000000 -30581.000000) translate(0,88)">线06号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41148.000000 -30581.000000) translate(0,111)">A002隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="13" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41343.000000 -31134.000000) translate(0,11)">38+1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41383.000000 -31848.000000) translate(0,19)">10kV赵</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41383.000000 -31848.000000) translate(0,42)">家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41383.000000 -31848.000000) translate(0,65)">26号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41383.000000 -31848.000000) translate(0,88)">G001隔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41383.000000 -31848.000000) translate(0,111)">离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41246.000000 -31596.000000) translate(0,19)">10kV赵</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41246.000000 -31596.000000) translate(0,42)">家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41246.000000 -31596.000000) translate(0,65)">32号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41246.000000 -31596.000000) translate(0,88)">G02断</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41246.000000 -31596.000000) translate(0,111)">路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41368.000000 -31470.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41150.000000 -31329.000000) translate(0,19)">10kV赵家庄线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41150.000000 -31329.000000) translate(0,42)">38+1号塔G003</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41150.000000 -31329.000000) translate(0,65)"> 隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41480.000000 -30985.000000) translate(0,9)">A02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41552.000000 -31012.000000) translate(0,21)">15</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41512.000000 -30983.000000) translate(0,9)">A021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="29" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41323.000000 -29964.000000) translate(0,24)">51</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41209.000000 -29933.000000) translate(0,9)">A01</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="11" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41255.000000 -29932.000000) translate(0,9)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42556.000000 -29177.000000) translate(0,19)">10kV城区I回线01号塔G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42560.000000 -29056.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42047.000000 -29032.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42050.000000 -29138.000000) translate(0,19)">10kV城区Ⅱ回线01号塔G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41664.000000 -29705.000000) translate(0,16)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41570.000000 -29713.000000) translate(0,19)">1号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41760.000000 -29592.000000) translate(0,15)">10kV化工厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41760.000000 -29592.000000) translate(0,33)">线5号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41760.000000 -29592.000000) translate(0,51)">G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41741.000000 -29472.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41375.000000 -31427.000000) translate(0,12)">32号塔</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41351.000000 -31423.000000) translate(0,12)">32</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41375.000000 -31301.000000) translate(0,12)">36号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41351.000000 -31297.000000) translate(0,12)">36</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44893.000000 -28962.000000) translate(0,16)">24</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44941.000000 -29009.000000) translate(0,19)">G02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45003.000000 -29013.000000) translate(0,19)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44774.000000 -29554.000000) translate(0,16)">05</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44825.000000 -28962.000000) translate(0,16)">20</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44825.000000 -28882.000000) translate(0,16)">03</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44819.000000 -28656.000000) translate(0,16)">漫坡支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44765.000000 -28805.000000) translate(0,19)">A01</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44756.000000 -28743.000000) translate(0,19)">A012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44921.000000 -28920.000000) translate(0,16)">10kV滑坡线24号杆G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44861.000000 -28811.000000) translate(0,8)">10kV滑坡线漫坡支线03号杆A01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45731.000000 -31909.000000) translate(0,19)">10kV白路集镇线01号杆G01断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45730.000000 -31779.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45595.000000 -29837.000000) translate(0,19)">10kV迤纳厂线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45595.000000 -29837.000000) translate(0,42)">04号杆G01断</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45595.000000 -29837.000000) translate(0,65)">路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45655.000000 -29893.000000) translate(0,19)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46010.000000 -30366.000000) translate(0,17)">061</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46041.000000 -30201.000000) translate(0,17)">06167</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46226.000000 -30364.000000) translate(0,17)">062</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46268.000000 -30199.000000) translate(0,17)">06267</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46429.000000 -30370.000000) translate(0,17)">064</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46465.000000 -30205.000000) translate(0,17)">06467</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45766.000000 -30371.000000) translate(0,17)">073</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45996.000000 -30069.000000) translate(0,14)">0786</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45993.000000 -30000.000000) translate(0,19)">07867</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45811.000000 -30181.000000) translate(0,18)">咪</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45811.000000 -30181.000000) translate(0,40)">三</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45811.000000 -30181.000000) translate(0,62)">咱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45811.000000 -30181.000000) translate(0,84)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45718.000000 -30141.000000) translate(0,18)">迤</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45718.000000 -30141.000000) translate(0,40)">纳</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45718.000000 -30141.000000) translate(0,62)">厂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45718.000000 -30141.000000) translate(0,84)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45929.000000 -30460.000000) translate(0,18)">转供Ⅰ回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46148.000000 -30458.000000) translate(0,18)">转供Ⅱ回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46351.000000 -30464.000000) translate(0,18)">转供Ⅳ回线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45933.000000 -30118.000000) translate(0,18)">恒雄专线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46081.000000 -30116.000000) translate(0,18)">万翔专线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46146.000000 -30128.000000) translate(0,18)">龙庆关线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46226.000000 -30127.000000) translate(0,18)">收费站专线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46440.000000 -30121.000000) translate(0,18)">三家村线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46351.000000 -30120.000000) translate(0,18)">猫街镇线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45217.000000 -30311.000000) translate(0,19)">10kV古普线167号杆G0R5跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44201.000000 -31156.000000) translate(0,19)">10kV古普线35</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44201.000000 -31156.000000) translate(0,42)">号塔G02断路器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44201.000000 -31020.000000) translate(0,19)">G021</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44505.000000 -30442.000000) translate(0,19)">G051</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44388.000000 -29621.000000) translate(0,19)">G071</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44390.000000 -29505.000000) translate(0,19)">G072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44497.000000 -30917.000000) translate(0,19)">G031</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44179.000000 -30686.000000) translate(0,19)">10kV古普线86号杆^JG004隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44963.000000 -31235.000000) translate(0,13)">14</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45735.000000 -31199.000000) translate(0,13)">10kV中所支线27号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45735.000000 -31199.000000) translate(0,29)">杆A001隔离开关</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46192.000000 -31231.000000) translate(0,13)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45794.000000 -31234.000000) translate(0,13)">27</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45860.000000 -31253.000000) translate(0,13)">10kV中所支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45282.000000 -31206.000000) translate(0,13)">10kV嘎哪支线T中所联络线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44994.000000 -31417.000000) translate(0,13)">10kV乐茂河T嘎哪</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44994.000000 -31417.000000) translate(0,29)">支线05号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44994.000000 -31417.000000) translate(0,45)">G0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44910.000000 -31113.000000) translate(0,13)">10kV乐茂河T</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44910.000000 -31113.000000) translate(0,29)">嘎哪支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46328.000000 -31740.000000) translate(0,13)">10kV花桥</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46328.000000 -31740.000000) translate(0,29)">线14号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46328.000000 -31740.000000) translate(0,45)">G01断路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46328.000000 -31740.000000) translate(0,61)">器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46366.000000 -31777.000000) translate(0,13)">G011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44918.000000 -31575.000000) translate(0,13)">0</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45739.000000 -29719.000000) translate(0,13)">13</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45787.000000 -29719.000000) translate(0,13)">02</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45939.000000 -29718.000000) translate(0,13)">62</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45774.000000 -29696.000000) translate(0,13)">10kV迤纳厂联络线</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45774.000000 -29696.000000) translate(0,29)">02号杆A01断</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45774.000000 -29696.000000) translate(0,45)">路器</text>
   <text DF8003:Layer="0" fill="rgb(0,0,255)" font-family="SimSun" font-size="9" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45816.000000 -29739.000000) translate(0,8)">A011</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43041.000000 -30383.000000) translate(0,19)">10kV团碑线T以潘德支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43041.000000 -30383.000000) translate(0,42)">14号杆B0R1跌落熔断器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41657.000000 -31766.000000) translate(0,15)">70号杆G03</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41816.000000 -31822.000000) translate(0,15)">G022</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40191.000000 -30852.000000) translate(0,19)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46057.000000 -31279.000000) translate(0,19)">01号杆A0R1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46137.000000 -31727.000000) translate(0,13)">10kV机关线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46137.000000 -31727.000000) translate(0,29)">线10号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46137.000000 -31727.000000) translate(0,45)">G01断路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46137.000000 -31727.000000) translate(0,61)">器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="16" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46223.000000 -31571.000000) translate(0,13)">G012</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="23" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46133.000000 -31208.000000) translate(0,19)">01号</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44906.000000 -28075.000000) translate(0,48)">樊婕</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="59" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45523.000000 -28077.000000) translate(0,48)">方俊钧</text>
   <text DF8003:Layer="0" fill="rgb(0,0,0)" font-family="SimSun" font-size="28" graphid="g_2529760" transform="matrix(1.311077 -0.000000 -0.000000 1.186047 39978.984161 -32690.994413) translate(0,23)">武定电网10kV网络图</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46434.000000 -28772.000000) translate(0,26)">110</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46434.000000 -28772.000000) translate(0,58)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46434.000000 -28772.000000) translate(0,90)">牡</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46434.000000 -28772.000000) translate(0,122)">丹</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252b780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46434.000000 -28772.000000) translate(0,154)">变</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252cd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46366.000000 -28983.000000) translate(0,26)">10kVⅠ母</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="32" graphid="g_252d600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45679.000000 -28560.000000) translate(0,26)">禄金线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_30006d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45761.000000 -29942.000000) translate(0,18)">2号站用变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_20e5a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45763.000000 -30316.000000) translate(0,17)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_301ff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45764.000000 -30235.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2958dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45765.000000 -30428.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_28f28e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45667.000000 -30370.000000) translate(0,17)">072</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3051690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45664.000000 -30315.000000) translate(0,17)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30cd1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45665.000000 -30234.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2588930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45666.000000 -30427.000000) translate(0,17)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32c4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45607.000000 -30212.000000) translate(0,18)">猫</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32c4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45607.000000 -30212.000000) translate(0,40)">街</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32c4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45607.000000 -30212.000000) translate(0,62)">镇</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_32c4270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45607.000000 -30212.000000) translate(0,84)">线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c8eb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45858.000000 -30370.000000) translate(0,17)">073</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2840b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45855.000000 -30315.000000) translate(0,17)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_295bd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45856.000000 -30234.000000) translate(0,17)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a37030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 45857.000000 -30427.000000) translate(0,17)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42831.000000 -31765.000000) translate(0,15)">010</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 42816.000000 -31716.000000) translate(0,15)">0107</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42990.000000 -31765.000000) translate(0,15)">01A</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 42899.000000 -31716.000000) translate(0,15)">01B7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 42914.000000 -31765.000000) translate(0,15)">01B</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 42982.000000 -31716.000000) translate(0,15)">01A7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43086.000000 -31451.000000) translate(0,16)">010</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 43070.000000 -31400.000000) translate(0,16)">0107</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 43156.000000 -31400.000000) translate(0,16)">01A7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 43172.000000 -31451.000000) translate(0,16)">01A</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="44" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46207.000000 -28648.000000) translate(0,36)">0641</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="44" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 46228.000000 -28950.000000) translate(0,36)">0661</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41166.000000 -30995.000000) translate(0,8)">\pxt5;（另一电源为10kV城区Ⅲ线）</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41366.000000 -30997.000000) translate(0,8)">至10kV调度大楼箱变（双电源客户）</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41282.000000 -31024.000000) translate(0,8)">10kV调度大楼箱变支线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40841.000000 -29946.000000) translate(0,8)">10kV调度大楼箱变支线</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 41035.000000 -29946.000000) translate(0,8)">至10kV调度大楼箱变（双电源客户）</text>
   <text DF8003:Layer="图层2" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40688.000000 -29935.000000) translate(0,8)">\pxt5;（另一电源为10kV西北片区线）</text>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41315,-29346 41315,-29342 41316,-29338 41317,-29335 41319,-29331 41321,-29328 41323,-29325 41326,-29323 41329,-29321 41333,-29319 41336,-29317 41340,-29317 41344,-29316 41347,-29316 41351,-29317 41355,-29318 41358,-29319 41362,-29321 41365,-29323 41367,-29326 41370,-29329 41372,-29332 41373,-29336 41374,-29339 41375,-29343 41375,-29347 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41100,-29344 41100,-29340 41101,-29337 41103,-29333 41104,-29330 41106,-29327 41109,-29324 41111,-29321 41114,-29319 41118,-29318 41121,-29316 41125,-29315 41129,-29315 41132,-29315 41136,-29316 41140,-29317 41143,-29318 41146,-29320 41149,-29322 41152,-29325 41154,-29328 41156,-29331 41158,-29334 41159,-29338 41160,-29341 41160,-29345 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="42746,-30056 42746,-30052 42747,-30048 42748,-30045 42750,-30041 42752,-30038 42754,-30035 42757,-30033 42760,-30030 42763,-30029 42767,-30027 42771,-30026 42775,-30026 42778,-30026 42782,-30026 42786,-30027 42790,-30029 42793,-30030 42796,-30033 42799,-30035 42801,-30038 42803,-30041 42805,-30045 42806,-30048 42807,-30052 42807,-30056 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41986,-30055 41986,-30051 41987,-30048 41988,-30044 41990,-30041 41992,-30037 41994,-30034 41997,-30032 42000,-30030 42003,-30028 42007,-30027 42010,-30026 42014,-30025 42018,-30025 42022,-30026 42025,-30027 42029,-30028 42032,-30030 42035,-30032 42038,-30034 42040,-30037 42042,-30041 42044,-30044 42045,-30048 42046,-30051 42046,-30055 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="44443,-30051 44443,-30047 44444,-30044 44445,-30040 44447,-30037 44449,-30033 44451,-30030 44454,-30028 44457,-30026 44460,-30024 44464,-30023 44467,-30022 44471,-30021 44475,-30021 44479,-30022 44482,-30023 44486,-30024 44489,-30026 44492,-30028 44495,-30030 44497,-30033 44499,-30037 44501,-30040 44502,-30044 44503,-30047 44503,-30051 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="42969,-30047 42971,-30045 42973,-30043 42975,-30042 42978,-30041 42980,-30040 42983,-30039 42985,-30039 42988,-30039 42991,-30040 42993,-30041 42996,-30042 42998,-30043 43000,-30045 43002,-30047 43003,-30049 43005,-30052 43006,-30054 43006,-30057 43007,-30060 43006,-30062 43006,-30065 43005,-30068 43004,-30070 43003,-30072 43001,-30075 42999,-30076 42997,-30078 " stroke="rgb(255,255,255)" stroke-width="0.17"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="42491,-30539 42491,-30535 42492,-30531 42493,-30528 42495,-30524 42497,-30521 42499,-30518 42502,-30515 42505,-30513 42508,-30511 42512,-30510 42516,-30509 42520,-30509 42524,-30509 42527,-30510 42531,-30511 42534,-30512 42538,-30514 42541,-30517 42543,-30519 42546,-30523 42548,-30526 42549,-30530 42550,-30533 42550,-30537 42550,-30541 42550,-30545 42549,-30549 42547,-30552 " stroke="rgb(38,0,0)" stroke-width="0.205"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="45172,-29147 45173,-29147 45175,-29147 45176,-29148 45177,-29148 45179,-29149 45180,-29150 45181,-29151 45181,-29152 45182,-29153 45183,-29154 45183,-29156 45183,-29157 45183,-29158 45183,-29160 45183,-29161 45182,-29163 45182,-29164 45181,-29165 45180,-29166 45179,-29167 45178,-29168 45177,-29168 45175,-29169 45174,-29169 " stroke="rgb(0,255,0)" stroke-width="0.075"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41538,-31614 41538,-31610 41539,-31606 41540,-31603 41542,-31599 41544,-31596 41546,-31593 41549,-31591 41552,-31589 41556,-31587 41559,-31585 41563,-31585 41567,-31584 41570,-31584 41574,-31585 41578,-31586 41581,-31587 41585,-31589 41588,-31591 41590,-31594 41593,-31597 41595,-31600 41596,-31604 41597,-31607 41598,-31611 41598,-31615 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41331,-31614 41331,-31610 41332,-31606 41333,-31603 41335,-31599 41337,-31596 41339,-31593 41342,-31591 41345,-31589 41349,-31587 41352,-31585 41356,-31585 41360,-31584 41363,-31584 41367,-31585 41371,-31586 41374,-31587 41378,-31589 41381,-31591 41383,-31594 41386,-31597 41388,-31600 41389,-31604 41390,-31607 41391,-31611 41391,-31615 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41211,-31612 41211,-31608 41212,-31605 41214,-31601 41215,-31598 41217,-31595 41220,-31592 41222,-31589 41225,-31587 41229,-31586 41232,-31584 41236,-31583 41240,-31583 41243,-31583 41247,-31584 41251,-31585 41254,-31586 41257,-31588 41260,-31590 41263,-31593 41265,-31596 41267,-31599 41269,-31602 41270,-31606 41271,-31609 41271,-31613 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41081,-31612 41081,-31608 41082,-31605 41083,-31601 41085,-31598 41087,-31595 41090,-31592 41093,-31589 41096,-31587 41099,-31585 41103,-31584 41106,-31583 41110,-31583 41114,-31583 41118,-31583 41121,-31584 41125,-31586 41128,-31588 41131,-31590 41134,-31592 41136,-31595 41138,-31598 41140,-31602 41141,-31606 41142,-31609 41142,-31613 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="40958,-31612 40958,-31608 40959,-31605 40960,-31601 40962,-31598 40964,-31595 40967,-31592 40970,-31589 40973,-31587 40976,-31585 40980,-31584 40983,-31583 40987,-31583 40991,-31583 40995,-31583 40998,-31584 41002,-31586 41005,-31588 41008,-31590 41011,-31592 41013,-31595 41015,-31598 41017,-31602 41018,-31606 41019,-31609 41019,-31613 " stroke="rgb(255,255,255)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="40793,-31612 40793,-31608 40794,-31605 40796,-31601 40797,-31598 40799,-31595 40802,-31592 40804,-31589 40807,-31587 40811,-31586 40814,-31584 40818,-31583 40822,-31583 40825,-31583 40829,-31584 40833,-31585 40836,-31586 40839,-31588 40842,-31590 40845,-31593 40847,-31596 40849,-31599 40851,-31602 40852,-31606 40853,-31609 40853,-31613 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="45553,-31305 45553,-31301 45554,-31297 45555,-31294 45557,-31290 45559,-31287 45561,-31284 45564,-31282 45567,-31280 45571,-31278 45574,-31276 45578,-31276 45582,-31275 45585,-31275 45589,-31276 45593,-31277 45596,-31278 45600,-31280 45603,-31282 45605,-31285 45608,-31288 45610,-31291 45611,-31295 45612,-31298 45613,-31302 45613,-31306 " stroke="rgb(0,255,0)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="42495,-30055 42495,-30051 42496,-30048 42497,-30044 42499,-30041 42501,-30037 42503,-30034 42506,-30032 42509,-30030 42512,-30028 42516,-30027 42519,-30026 42523,-30025 42527,-30025 42531,-30026 42534,-30027 42538,-30028 42541,-30030 42544,-30032 42547,-30034 42549,-30037 42551,-30041 42553,-30044 42554,-30048 42555,-30051 42555,-30055 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="45172,-28904 45176,-28904 45179,-28905 45183,-28906 45186,-28908 45189,-28910 45192,-28912 45194,-28915 45196,-28918 45198,-28921 45200,-28925 45201,-28928 45201,-28932 45201,-28936 45201,-28940 45200,-28943 45198,-28947 45196,-28950 45194,-28953 45192,-28956 45189,-28958 45186,-28960 45183,-28962 45179,-28963 45176,-28964 45172,-28964 " stroke="rgb(255,255,255)" stroke-width="0.1925"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41666,-29773 41670,-29773 41673,-29774 41677,-29775 41680,-29777 41684,-29779 41687,-29781 41689,-29784 41691,-29787 41693,-29790 41695,-29794 41695,-29797 41696,-29801 41696,-29805 41695,-29809 41695,-29812 41693,-29816 41691,-29819 41689,-29822 41687,-29825 41684,-29827 41680,-29829 41677,-29831 41673,-29832 41670,-29833 41666,-29833 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41398,-30141 41402,-30141 41405,-30142 41409,-30143 41412,-30145 41415,-30147 41418,-30150 41421,-30152 41423,-30156 41425,-30159 41426,-30162 41427,-30166 41427,-30170 41427,-30173 41427,-30177 41426,-30181 41425,-30184 41423,-30187 41421,-30191 41418,-30193 41415,-30196 41412,-30198 41409,-30200 41405,-30201 41402,-30202 41398,-30202 " stroke="rgb(255,255,255)" stroke-width="0.195"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41453,-30999 41453,-31000 41453,-31002 41453,-31003 41452,-31004 41451,-31005 41451,-31006 41450,-31007 41449,-31008 41447,-31009 41446,-31009 41445,-31010 41444,-31010 41442,-31010 41441,-31010 41440,-31010 41438,-31009 41437,-31009 41436,-31008 41435,-31007 41434,-31006 41434,-31005 41433,-31004 41432,-31003 41432,-31001 41432,-31000 " stroke="rgb(255,0,0)" stroke-width="0.0725"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41374,-31000 41374,-31001 41374,-31003 41374,-31004 41374,-31005 41373,-31007 41372,-31008 41371,-31009 41370,-31010 41369,-31011 41368,-31011 41367,-31012 41365,-31012 41364,-31012 41363,-31012 41361,-31012 41360,-31012 41359,-31011 41358,-31011 41356,-31010 41356,-31009 41355,-31008 41354,-31006 41353,-31005 41353,-31004 41353,-31002 41353,-31001 " stroke="rgb(255,0,0)" stroke-width="0.0725"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41312,-31001 41312,-31002 41312,-31004 41311,-31005 41311,-31006 41310,-31007 41309,-31008 41308,-31009 41307,-31010 41306,-31010 41305,-31011 41303,-31011 41302,-31011 41301,-31011 41300,-31011 41298,-31011 41297,-31010 41296,-31010 41295,-31009 41294,-31008 41293,-31007 41292,-31006 41292,-31005 41291,-31004 41291,-31002 41291,-31001 " stroke="rgb(255,0,0)" stroke-width="0.07"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41143,-29949 41143,-29950 41143,-29952 41143,-29953 41142,-29954 41141,-29955 41141,-29956 41140,-29957 41139,-29958 41137,-29959 41136,-29959 41135,-29960 41134,-29960 41132,-29960 41131,-29960 41130,-29960 41128,-29959 41127,-29959 41126,-29958 41125,-29957 41124,-29956 41124,-29955 41123,-29954 41122,-29953 41122,-29951 41122,-29950 " stroke="rgb(255,0,0)" stroke-width="0.0725"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="40836,-29953 40836,-29954 40835,-29955 40835,-29957 40834,-29958 40833,-29959 40832,-29960 40831,-29960 40830,-29961 40829,-29961 40828,-29962 40827,-29962 40826,-29962 40824,-29962 40823,-29962 40822,-29961 40821,-29961 40820,-29960 40819,-29960 40818,-29959 40817,-29958 40816,-29957 40816,-29955 40815,-29954 40815,-29953 " stroke="rgb(255,0,0)" stroke-width="0.0675"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="41002,-29951 41002,-29952 41002,-29953 41001,-29955 41001,-29956 41000,-29957 40999,-29958 40998,-29959 40997,-29959 40996,-29960 40995,-29961 40994,-29961 40993,-29961 40991,-29961 40990,-29961 40989,-29961 40988,-29960 40987,-29959 40986,-29959 40985,-29958 40984,-29957 40983,-29956 40983,-29955 40982,-29953 40982,-29952 40982,-29951 " stroke="rgb(255,0,0)" stroke-width="0.065"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="45612,-31226 45612,-31230 45612,-31233 45611,-31237 45610,-31241 45608,-31244 45606,-31247 45604,-31250 45601,-31252 45598,-31254 45594,-31256 45591,-31257 45587,-31258 45584,-31258 45580,-31258 45576,-31257 45573,-31256 45569,-31255 45566,-31253 45563,-31251 45561,-31248 45559,-31245 45557,-31241 45555,-31238 45554,-31234 45554,-31231 45554,-31227 " stroke="rgb(0,255,0)" stroke-width="0.1975"/>
   <polyline DF8003:Layer="0" arcFlag="1" fill="none" points="45207,-31226 45207,-31230 45207,-31234 45206,-31237 45204,-31241 45203,-31244 45201,-31247 45198,-31250 45195,-31252 45192,-31254 45188,-31256 45185,-31257 45181,-31258 45178,-31258 45174,-31258 45170,-31257 45167,-31256 45163,-31254 45160,-31252 45157,-31250 45154,-31247 45152,-31244 45151,-31241 45149,-31237 45148,-31234 45148,-31230 45148,-31226 " stroke="rgb(0,255,0)" stroke-width="0.195"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45741.000000 -30340.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45642.000000 -30339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45833.000000 -30339.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="51" qtmmishow="hidden" width="400" x="39942" y="-32702"/>
    </a>
   <metadata/><rect fill="white" height="51" opacity="0" stroke="white" transform="" width="400" x="39942" y="-32702"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="82" qtmmishow="hidden" width="101" x="39882" y="-32722"/>
    </a>
   <metadata/><rect fill="white" height="82" opacity="0" stroke="white" transform="" width="101" x="39882" y="-32722"/></g>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="43729" cy="-28693" fill="none" r="19" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44245" cy="-29343" fill="none" r="21.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44245" cy="-28586" fill="none" r="23.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44358" cy="-29371" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45796" cy="-31930" fill="none" r="18.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45821" cy="-31930" fill="none" r="18.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45183" cy="-30536" fill="none" r="19.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45585" cy="-30665" fill="none" r="16.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45828" cy="-30662" fill="none" r="23" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45750" cy="-29388" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="46562" cy="-29395" fill="none" r="16.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42524" cy="-30985" fill="none" r="20" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42016" cy="-30983" fill="none" r="18.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42524" cy="-30633" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42015" cy="-30631" fill="none" r="21" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42525" cy="-30188" fill="none" r="18" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42015" cy="-30186" fill="none" r="18.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41884" cy="-31386" fill="none" r="22.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41568" cy="-31384" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41826" cy="-30731" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42017" cy="-29803" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41342" cy="-29801" fill="none" r="24" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41496" cy="-29346" fill="none" r="17.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="40274" cy="-29715" fill="none" r="33.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41626" cy="-30730" fill="none" r="20.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="40993" cy="-31382" fill="none" r="38.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41130" cy="-29714" fill="none" r="23.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42990" cy="-30669" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43159" cy="-29261" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43158" cy="-28693" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41885" cy="-30661" fill="none" r="24" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45368" cy="-28951" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45589" cy="-28953" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41341" cy="-30172" fill="none" r="24" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43159" cy="-29394" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42969" cy="-31750" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42886" cy="-31750" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43052" cy="-31750" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42885" cy="-31796" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42968" cy="-31796" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43048" cy="-31797" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43228" cy="-31436" fill="none" r="6" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43142" cy="-31435" fill="none" r="6.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43051" cy="-31796" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43228" cy="-31479" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43143" cy="-31479" fill="none" r="2" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="42141" cy="-30631" fill="none" r="6.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="46266" cy="-28563" fill="none" r="14" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45178" cy="-29188" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45172" cy="-28564" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="40607" cy="-31617" fill="none" r="24" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="40606" cy="-31528" fill="none" r="21.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41443" cy="-30540" fill="none" r="20.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="43729" cy="-28831" fill="none" r="19" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44912" cy="-29161" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45275" cy="-29160" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45706" cy="-31685" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="46074" cy="-31685" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="46287" cy="-28864" fill="none" r="14.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41736" cy="-29378" fill="none" r="14" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41360" cy="-31127" fill="none" r="20.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41568" cy="-30999" fill="none" r="22" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41341" cy="-29948" fill="none" r="24" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41667" cy="-29695" fill="none" r="14" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41360" cy="-31415" fill="none" r="13.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="41361" cy="-31289" fill="none" r="14" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44906" cy="-28951" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44787" cy="-29544" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44838" cy="-28951" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44838" cy="-28871" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44973" cy="-31226" fill="none" r="16.5" stroke="rgb(0,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="44922" cy="-31565" fill="none" r="16.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45802" cy="-31226" fill="none" r="16.5" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45749" cy="-29711" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45948" cy="-29711" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="45798" cy="-29711" fill="none" r="17" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="0" cx="46153" cy="-31226" fill="none" r="16.5" stroke="rgb(255,255,255)" stroke-width="1"/>
  </g><g id="Load_Layer">
   <g DF8003:Layer="0" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45642.000000 -30133.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43804,-32218 43137,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43555,-32219 43533,-32219 43555,-32219 43533,-32219 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43449,-32217 43426,-32217 43449,-32217 43426,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43661,-32218 43638,-32218 43661,-32218 43638,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43767,-32213 43745,-32213 43767,-32213 43745,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43344,-32216 43322,-32216 43344,-32216 43322,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43234,-32216 43212,-32216 43234,-32216 43212,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43479,-29807 43088,-29807 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44300,-29806 43990,-29806 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43926,-29806 43528,-29806 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44177,-32215 43838,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="43884,-32213 43866,-32213 43884,-32213 43866,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44692,-32214 44217,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44279,-32213 44261,-32213 44279,-32213 44261,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44158,-32213 44140,-32213 44158,-32213 44140,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44118,-32214 44101,-32214 44118,-32214 44101,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44005,-32213 43988,-32213 44005,-32213 43988,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44372,-32215 44354,-32215 44372,-32215 44354,-32215 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44507,-32214 44490,-32214 44507,-32214 44490,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44640,-32211 44622,-32211 44640,-32211 44622,-32211 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45563,-30028 44723,-30028 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45183,-30030 45166,-30030 45183,-30030 45166,-30030 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45041,-30028 45024,-30028 45041,-30028 45024,-30028 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44921,-30027 44903,-30027 44921,-30027 44903,-30027 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44796,-30030 44779,-30030 44796,-30030 44779,-30030 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45285,-30030 45268,-30030 45285,-30030 45268,-30030 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45400,-30028 45383,-30028 45400,-30028 45383,-30028 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45516,-30025 45499,-30025 45516,-30025 45499,-30025 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46598,-32208 46123,-32208 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45914,-32216 45898,-32216 45914,-32216 45898,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45974,-32217 45959,-32217 45974,-32217 45959,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46094,-32216 45869,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46213,-32207 46197,-32207 46213,-32207 46197,-32207 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46332,-32208 46317,-32208 46332,-32208 46317,-32208 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46448,-32207 46433,-32207 46448,-32207 46433,-32207 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46567,-32204 46552,-32204 46567,-32204 46552,-32204 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45785,-32215 45269,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45359,-32214 45344,-32214 45359,-32214 45344,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45474,-32215 45458,-32215 45474,-32215 45458,-32215 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45593,-32214 45577,-32214 45593,-32214 45577,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45756,-32216 45741,-32216 45756,-32216 45741,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45714,-32214 45698,-32214 45714,-32214 45698,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45241,-32218 44726,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44802,-32217 44787,-32217 44802,-32217 44787,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44929,-32218 44914,-32218 44929,-32218 44914,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45073,-32217 45057,-32217 45073,-32217 45057,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45192,-32214 45176,-32214 45192,-32214 45176,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41437,-32216 40943,-32215 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41124,-32216 41107,-32216 41124,-32216 41107,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41004,-32216 40986,-32216 41004,-32216 40986,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="42194,-32220 41477,-32217 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41250,-32215 41233,-32215 41250,-32215 41233,-32215 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41406,-32216 41388,-32216 41406,-32216 41388,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41370,-32212 41353,-32212 41370,-32212 41353,-32212 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41513,-32216 41496,-32216 41513,-32216 41496,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41577,-32216 41559,-32216 41577,-32216 41559,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41894,-32218 41876,-32218 41894,-32218 41876,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="42027,-32218 42010,-32218 42027,-32218 42010,-32218 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41793,-32216 41775,-32216 41793,-32216 41775,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="42709,-28772 41646,-28774 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40097,-32211 40384,-32211 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40438,-32213 40859,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40713,-32212 40695,-32212 40713,-32212 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40471,-32213 40489,-32213 40471,-32213 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40833,-32212 40815,-32212 40833,-32212 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40175,-32211 40157,-32211 40175,-32211 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40337,-32211 40355,-32211 40337,-32211 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40301,-32211 40283,-32211 40301,-32211 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40586,-32214 40568,-32214 40586,-32214 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44605,-27975 44605,-28414 46621,-28414 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44605,-28115 46621,-28115 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44605,-28255 46621,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="44845,-27975 44845,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45200,-27975 45200,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45439,-27975 45439,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45895,-27975 45895,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46176,-27975 46176,-28255 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41604,-28771 40081,-28771 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41510,-28771 41488,-28771 41510,-28771 41488,-28771 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41349,-28771 41327,-28771 41349,-28771 41327,-28771 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41143,-28772 41120,-28772 41143,-28772 41120,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40812,-28775 40789,-28775 40812,-28775 40789,-28775 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40678,-28772 40656,-28772 40678,-28772 40656,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40556,-28774 40534,-28774 40556,-28774 40534,-28774 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40427,-28769 40404,-28769 40427,-28769 40404,-28769 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="40280,-28772 40258,-28772 40280,-28772 40258,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41745,-28773 41723,-28773 41745,-28773 41723,-28773 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41887,-28772 41865,-28772 41887,-28772 41865,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="42029,-28772 42006,-28772 42029,-28772 42006,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="42535,-28772 42512,-28772 42535,-28772 42512,-28772 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46406,-28444 46406,-28949 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46026,-28251 46026,-28414 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46081,-32216 46066,-32216 46081,-32216 46066,-32216 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45425,-28856 45425,-28872 45425,-28856 45425,-28872 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="45616,-28860 45616,-28876 45616,-28860 45616,-28876 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="41287,-32219 41304,-32219 41287,-32219 41304,-32219 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46437,-30479 45591,-30479 " stroke="rgb(255,255,255)" stroke-width="1"/>
   <polyline DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="46413,-30479 46399,-30479 46413,-30479 46399,-30479 " stroke="rgb(255,255,255)" stroke-width="1"/>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="51" qtmmishow="hidden" width="400" x="39942" y="-32702"/></g>
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="82" qtmmishow="hidden" width="101" x="39882" y="-32722"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="支线:0.000000 0.000000" layer11="支线开关:0.000000 0.000000" layer12="支线油开关:0.000000 0.000000" layer13="公变:0.000000 0.000000" layer14="自变:0.000000 0.000000" layer15="图框（细实线）:0.000000 0.000000" layer16="标注、文字:0.000000 0.000000" layer17="图框（粗实线）:0.000000 0.000000" layer18="实线:0.000000 0.000000" layer19="粗线:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="10KV线路:0.000000 0.000000" layer21="虚线:0.000000 0.000000" layer22="Defpoints:0.000000 0.000000" layer23="----电气层----:0.000000 0.000000" layer24="图层3:0.000000 0.000000" layer25="SX:0.000000 0.000000" layer26="图层2:0.000000 0.000000" layer27="$AUDIT-BAD-LAYER:0.000000 0.000000" layer28="0:0.000000 0.000000" layer29="主干线:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="主干线开关:0.000000 0.000000" layer31="主干线油开关:0.000000 0.000000" layer32="次干线:0.000000 0.000000" layer33="次干线开关:0.000000 0.000000" layer34="次干线油开关:0.000000 0.000000" layer35="支线:0.000000 0.000000" layer36="支线开关:0.000000 0.000000" layer37="支线油开关:0.000000 0.000000" layer38="公变:0.000000 0.000000" layer39="自变:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer40="图框（细实线）:0.000000 0.000000" layer41="标注、文字:0.000000 0.000000" layer42="图框（粗实线）:0.000000 0.000000" layer43="实线:0.000000 0.000000" layer44="粗线:0.000000 0.000000" layer45="10KV线路:0.000000 0.000000" layer46="虚线:0.000000 0.000000" layer47="Defpoints:0.000000 0.000000" layer48="----电气层----:0.000000 0.000000" layer49="图层3:0.000000 0.000000" layer5="主干线开关:0.000000 0.000000" layer50="SX:0.000000 0.000000" layer51="图层2:0.000000 0.000000" layer52="$AUDIT-BAD-LAYER:0.000000 0.000000" layer53="PUBLIC:0.000000 0.000000" layer6="主干线油开关:0.000000 0.000000" layer7="次干线:0.000000 0.000000" layer8="次干线开关:0.000000 0.000000" layer9="次干线油开关:0.000000 0.000000" layerN="54" moveAndZoomFlag="1"/>
</svg>