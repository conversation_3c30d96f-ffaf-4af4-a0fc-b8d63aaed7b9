<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-220" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-211 -1171 2053 1199">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2639fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_263a840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_263b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_263be90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_263d0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_263dd10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_263e4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_263ee30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_263f7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2640120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2640d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_26415d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2641ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2642b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2643340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2643cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2645370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_270d150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_270d8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_270e2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_270f4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_270fe30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2710920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_24af4c0" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2713130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2713ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2723870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    
   </symbol>
   <symbol id="Tag:shape33">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_27163e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2712a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape36">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
   </symbol>
   <symbol id="Tag:shape37">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1209" width="2063" x="-216" y="-1176"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.000000 -915.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25933" ObjectName="SW-YA_DXC.YA_DXC_311BK"/>
     <cge:Meas_Ref ObjectId="148332"/>
    <cge:TPSR_Ref TObjectID="25933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -915.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25943" ObjectName="SW-YA_DXC.YA_DXC_313BK"/>
     <cge:Meas_Ref ObjectId="148380"/>
    <cge:TPSR_Ref TObjectID="25943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148356">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -914.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25938" ObjectName="SW-YA_DXC.YA_DXC_312BK"/>
     <cge:Meas_Ref ObjectId="148356"/>
    <cge:TPSR_Ref TObjectID="25938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1619.000000 -815.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -697.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144604">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 -933.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25452" ObjectName="SW-YA_LC.YA_LC_332BK"/>
     <cge:Meas_Ref ObjectId="144604"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -614.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25799" ObjectName="SW-YA_XJC.YA_XJC_382BK"/>
     <cge:Meas_Ref ObjectId="147631"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147652">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -492.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25803" ObjectName="SW-YA_XJC.YA_XJC_383BK"/>
     <cge:Meas_Ref ObjectId="147652"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 298.000000 -369.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25795" ObjectName="SW-YA_XJC.YA_XJC_381BK"/>
     <cge:Meas_Ref ObjectId="147610"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129577">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -150.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23738" ObjectName="SW-NH_HS.NH_HS_373BK"/>
     <cge:Meas_Ref ObjectId="129577"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4742">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -575.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="831" ObjectName="SW-CX_YA.CX_YA_351BK"/>
     <cge:Meas_Ref ObjectId="4742"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4786">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.000000 -575.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="841" ObjectName="SW-CX_YA.CX_YA_353BK"/>
     <cge:Meas_Ref ObjectId="4786"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-55192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 804.000000 -436.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9750" ObjectName="SW-CX_YA.CX_YA_312BK"/>
     <cge:Meas_Ref ObjectId="55192"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4764">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -400.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="836" ObjectName="SW-CX_YA.CX_YA_352BK"/>
     <cge:Meas_Ref ObjectId="4764"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4807">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -401.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="845" ObjectName="SW-CX_YA.CX_YA_356BK"/>
     <cge:Meas_Ref ObjectId="4807"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4828">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -401.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="849" ObjectName="SW-CX_YA.CX_YA_354BK"/>
     <cge:Meas_Ref ObjectId="4828"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -159.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25546" ObjectName="SW-YA_TP.YA_TP_311BK"/>
     <cge:Meas_Ref ObjectId="145519"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145562">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 -159.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25551" ObjectName="SW-YA_TP.YA_TP_312BK"/>
     <cge:Meas_Ref ObjectId="145562"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148012">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 -158.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25883" ObjectName="SW-YA_MX.YA_MX_341BK"/>
     <cge:Meas_Ref ObjectId="148012"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144547">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 407.000000 -1108.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25446" ObjectName="SW-YA_LC.YA_LC_331BK"/>
     <cge:Meas_Ref ObjectId="144547"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_LC.YA_LC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-1134 227,-921 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25443" ObjectName="BS-YA_LC.YA_LC_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="227,-1134 227,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,-693 228,-320 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25791" ObjectName="BS-YA_XJC.YA_XJC_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="228,-693 228,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_DXC.YA_DXC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="956,-1026 1311,-1026 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25930" ObjectName="BS-YA_DXC.YA_DXC_3M"/>
    <cge:TPSR_Ref TObjectID="25930"/></metadata>
   <polyline fill="none" opacity="0" points="956,-1026 1311,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1725,-912 1725,-749 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1725,-912 1725,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1216,-633 1349,-633 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1216,-633 1349,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_HS.NH_HS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-95 576,-95 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23829" ObjectName="BS-NH_HS.NH_HS_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="416,-95 576,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_QC.YA_QC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="564,-783 692,-783 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25496" ObjectName="BS-YA_QC.YA_QC_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="564,-783 692,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YA.CX_YA_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="600,-511 760,-511 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="799" ObjectName="BS-CX_YA.CX_YA_3IM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="600,-511 760,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YA.CX_YA_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="889,-511 1235,-511 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10189" ObjectName="BS-CX_YA.CX_YA_3IIM"/>
    </metadata>
   <polyline fill="none" opacity="0" points="889,-511 1235,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_TP.YA_TP_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="857,-95 1128,-95 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25544" ObjectName="BS-YA_TP.YA_TP_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="857,-95 1128,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_MX.YA_MX_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-95 1669,-95 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25880" ObjectName="BS-YA_MX.YA_MX_3M"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1474,-95 1669,-95 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -128.000000 -1096.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-0" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 75.500000 -1066.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="DYN-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_24b3160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -49.000000 -1143.500000) translate(0,16)">35kV网络图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3b3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -1002.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c3dba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -179.000000 -564.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c28600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -1138.000000) translate(0,12)">连厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c29720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -722.000000) translate(0,12)">连</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c29720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -722.000000) translate(0,27)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c29720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1827.000000 -722.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218cc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1232.000000 -621.000000) translate(0,12)">仓街变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,12)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,27)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,42)">坝</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,57)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,72)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,87)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,102)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_211d5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1745.000000 -890.000000) translate(0,117)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22490e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -969.000000) translate(0,12)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22490e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -969.000000) translate(0,27)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22490e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -969.000000) translate(0,42)">连</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22490e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 940.000000 -969.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223e710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -798.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223ebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -810.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223ee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1679.000000 -810.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_223f040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1571.000000 -810.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2188ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -769.000000) translate(0,12)">前</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2188ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -769.000000) translate(0,27)">场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2188ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -769.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -669.000000) translate(0,12)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -669.000000) translate(0,27)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 741.000000 -669.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b452c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -958.000000) translate(0,12)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b452c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -958.000000) translate(0,27)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b452c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1086.000000 -958.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -966.000000) translate(0,12)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -966.000000) translate(0,27)">仓</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -966.000000) translate(0,42)">南</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -966.000000) translate(0,57)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -780.000000) translate(0,12)">3216</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1243.000000 -725.000000) translate(0,12)">321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -670.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b40c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 791.000000 -545.000000) translate(0,12)">110kV姚安变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -445.000000) translate(0,12)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -445.000000) translate(0,27)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -445.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -445.000000) translate(0,12)">太</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -445.000000) translate(0,27)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -445.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -445.000000) translate(0,12)">连</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -445.000000) translate(0,27)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b4ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1170.000000 -445.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b5c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 735.000000 -322.000000) translate(0,12)">姚西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bc200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -216.000000) translate(0,12)">太</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bc200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -216.000000) translate(0,27)">平</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33bc200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1049.000000 -216.000000) translate(0,42)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c88c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -282.000000) translate(0,12)">黄西太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -215.000000) translate(0,12)">弥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -215.000000) translate(0,27)">兴</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -215.000000) translate(0,42)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -215.000000) translate(0,57)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c97540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -215.000000) translate(0,72)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0b570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 292.000000 -970.000000) translate(0,12)">西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0bba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 417.000000 -1142.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0bde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 335.000000 -1141.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0c020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -1142.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0c260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 262.000000 -1050.000000) translate(0,12)">33110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 373.000000 -1051.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 448.000000 -1051.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0c920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 250.000000 -929.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0cb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -929.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0cda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 353.000000 -929.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0cfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 428.000000 -913.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0d220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 256.000000 -403.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0d460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 428.000000 -349.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0d6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 356.000000 -403.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -403.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0db20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 257.000000 -648.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0dd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 428.000000 -596.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0dfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -648.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0e1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 310.000000 -648.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0e420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -526.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0e660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -472.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -526.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0eae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -526.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0fa70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 569.000000 -779.000000) translate(0,12)">32117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d0ff20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -730.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -243.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d103a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -257.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d105e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -133.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1027.000000 -142.000000) translate(0,12)">31117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -142.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -133.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d10ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -194.000000) translate(0,12)">31260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -256.000000) translate(0,12)">31267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -243.000000) translate(0,12)">3126</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d115a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 918.000000 -188.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d117e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 -188.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 988.000000 -945.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 983.000000 -889.000000) translate(0,12)">3112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d11ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 983.000000 -998.000000) translate(0,12)">3111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d120e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -867.000000) translate(0,12)">31127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d12320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -944.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d12560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -889.000000) translate(0,12)">3132</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d127a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -998.000000) translate(0,12)">3131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d129e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1075.000000 -867.000000) translate(0,12)">31327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d12c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1294.000000 -943.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d12e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -997.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d130a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -888.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d132e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1235.000000 -867.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d13520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -187.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d13760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -132.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d139a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1584.000000 -241.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d13be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1521.000000 -268.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d13e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 596.000000 -605.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -657.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d142a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -549.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d144e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -708.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -605.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 679.000000 -657.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 660.000000 -708.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d14de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -549.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -470.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 870.000000 -470.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d154a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 865.000000 -417.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d156e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -470.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 745.000000 -417.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 602.000000 -501.000000) translate(0,12)">姚安变35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -534.000000) translate(0,12)">姚安变35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d15ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 969.000000 -429.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -374.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 908.000000 -357.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d166a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -484.000000) translate(0,12)">3542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d168e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -357.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1207.000000 -375.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1209.000000 -430.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d16fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -483.000000) translate(0,12)">3522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d171e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1090.000000 -430.000000) translate(0,12)">356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -375.000000) translate(0,12)">3566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -484.000000) translate(0,12)">3562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d178a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 -357.000000) translate(0,12)">35667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,12)">连</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,27)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,42)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,57)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,72)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,87)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 196.000000 -1086.000000) translate(0,102)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,12)">西</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,27)">教</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,42)">场</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,57)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,72)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,87)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,102)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d17d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 -563.000000) translate(0,117)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d181d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 945.000000 -85.000000) translate(0,12)">太平变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -85.000000) translate(0,12)">弥兴变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 956.000000 -1046.000000) translate(0,12)">大新仓变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d188c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 -804.000000) translate(0,12)">前场变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 506.000000 -179.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.000000 -225.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d18f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.000000 -133.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d191b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -276.000000) translate(0,12)">37367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d193f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 445.000000 -85.000000) translate(0,12)">黄山变35kV母线</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-144549">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -1113.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25447" ObjectName="SW-YA_LC.YA_LC_3311SW"/>
     <cge:Meas_Ref ObjectId="144549"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144550">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 273.000000 -1050.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25448" ObjectName="SW-YA_LC.YA_LC_33110SW"/>
     <cge:Meas_Ref ObjectId="144550"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.000000 -968.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25934" ObjectName="SW-YA_DXC.YA_DXC_3111SW"/>
     <cge:Meas_Ref ObjectId="148334"/>
    <cge:TPSR_Ref TObjectID="25934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148336">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 967.000000 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25936" ObjectName="SW-YA_DXC.YA_DXC_3112SW"/>
     <cge:Meas_Ref ObjectId="148336"/>
    <cge:TPSR_Ref TObjectID="25936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 916.000000 -834.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25935" ObjectName="SW-YA_DXC.YA_DXC_31127SW"/>
     <cge:Meas_Ref ObjectId="148335"/>
    <cge:TPSR_Ref TObjectID="25935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -968.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25944" ObjectName="SW-YA_DXC.YA_DXC_3131SW"/>
     <cge:Meas_Ref ObjectId="148382"/>
    <cge:TPSR_Ref TObjectID="25944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148384">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1115.000000 -859.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25946" ObjectName="SW-YA_DXC.YA_DXC_3132SW"/>
     <cge:Meas_Ref ObjectId="148384"/>
    <cge:TPSR_Ref TObjectID="25946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 -834.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25945" ObjectName="SW-YA_DXC.YA_DXC_31327SW"/>
     <cge:Meas_Ref ObjectId="148383"/>
    <cge:TPSR_Ref TObjectID="25945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148358">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -967.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25939" ObjectName="SW-YA_DXC.YA_DXC_3121SW"/>
     <cge:Meas_Ref ObjectId="148358"/>
    <cge:TPSR_Ref TObjectID="25939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148360">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -858.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25941" ObjectName="SW-YA_DXC.YA_DXC_3122SW"/>
     <cge:Meas_Ref ObjectId="148360"/>
    <cge:TPSR_Ref TObjectID="25941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148359">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1224.000000 -834.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25940" ObjectName="SW-YA_DXC.YA_DXC_31227SW"/>
     <cge:Meas_Ref ObjectId="148359"/>
    <cge:TPSR_Ref TObjectID="25940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.000000 -820.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1566.000000 -820.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1538.000000 -764.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -641.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 -938.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25455" ObjectName="SW-YA_LC.YA_LC_3321SW"/>
     <cge:Meas_Ref ObjectId="144608"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144609">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -938.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25456" ObjectName="SW-YA_LC.YA_LC_3326SW"/>
     <cge:Meas_Ref ObjectId="144609"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.000000 -878.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25454" ObjectName="SW-YA_LC.YA_LC_33267SW"/>
     <cge:Meas_Ref ObjectId="144607"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 247.000000 -619.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25800" ObjectName="SW-YA_XJC.YA_XJC_3821SW"/>
     <cge:Meas_Ref ObjectId="147633"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 350.000000 -619.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25802" ObjectName="SW-YA_XJC.YA_XJC_3826SW"/>
     <cge:Meas_Ref ObjectId="147635"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 412.000000 -559.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25801" ObjectName="SW-YA_XJC.YA_XJC_38267SW"/>
     <cge:Meas_Ref ObjectId="147634"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -497.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25804" ObjectName="SW-YA_XJC.YA_XJC_3831SW"/>
     <cge:Meas_Ref ObjectId="147654"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.000000 -497.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25806" ObjectName="SW-YA_XJC.YA_XJC_3836SW"/>
     <cge:Meas_Ref ObjectId="147656"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -437.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25805" ObjectName="SW-YA_XJC.YA_XJC_38367SW"/>
     <cge:Meas_Ref ObjectId="147655"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 245.000000 -374.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25796" ObjectName="SW-YA_XJC.YA_XJC_3811SW"/>
     <cge:Meas_Ref ObjectId="147612"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 348.000000 -374.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25798" ObjectName="SW-YA_XJC.YA_XJC_3816SW"/>
     <cge:Meas_Ref ObjectId="147614"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.000000 -314.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25797" ObjectName="SW-YA_XJC.YA_XJC_38167SW"/>
     <cge:Meas_Ref ObjectId="147613"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23740" ObjectName="SW-NH_HS.NH_HS_3731SW"/>
     <cge:Meas_Ref ObjectId="129579"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -195.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23739" ObjectName="SW-NH_HS.NH_HS_3736SW"/>
     <cge:Meas_Ref ObjectId="129578"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-129580">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 435.000000 -243.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23741" ObjectName="SW-NH_HS.NH_HS_37367SW"/>
     <cge:Meas_Ref ObjectId="129580"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -628.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="828" ObjectName="SW-CX_YA.CX_YA_3516SW"/>
     <cge:Meas_Ref ObjectId="4736"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -519.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="827" ObjectName="SW-CX_YA.CX_YA_3511SW"/>
     <cge:Meas_Ref ObjectId="4735"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4737">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 -676.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="829" ObjectName="SW-CX_YA.CX_YA_35167SW"/>
     <cge:Meas_Ref ObjectId="4737"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145180">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.000000 -749.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25500" ObjectName="SW-YA_QC.YA_QC_32117SW"/>
     <cge:Meas_Ref ObjectId="145180"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145179">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -698.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25499" ObjectName="SW-YA_QC.YA_QC_3211SW"/>
     <cge:Meas_Ref ObjectId="145179"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4779">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.000000 -519.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="837" ObjectName="SW-CX_YA.CX_YA_3531SW"/>
     <cge:Meas_Ref ObjectId="4779"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-143271">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.000000 -627.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="838" ObjectName="SW-CX_YA.CX_YA_3536SW"/>
     <cge:Meas_Ref ObjectId="143271"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4781">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 -676.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="839" ObjectName="SW-CX_YA.CX_YA_35367SW"/>
     <cge:Meas_Ref ObjectId="4781"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4545">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -441.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9979" ObjectName="SW-CX_YA.CX_YA_3121SW"/>
     <cge:Meas_Ref ObjectId="4545"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4546">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 -441.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9980" ObjectName="SW-CX_YA.CX_YA_3122SW"/>
     <cge:Meas_Ref ObjectId="4546"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4548">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -382.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9982" ObjectName="SW-CX_YA.CX_YA_31227SW"/>
     <cge:Meas_Ref ObjectId="4548"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4547">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 789.000000 -382.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9981" ObjectName="SW-CX_YA.CX_YA_31217SW"/>
     <cge:Meas_Ref ObjectId="4547"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4757">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="833" ObjectName="SW-CX_YA.CX_YA_3526SW"/>
     <cge:Meas_Ref ObjectId="4757"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-143256">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -453.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="832" ObjectName="SW-CX_YA.CX_YA_3522SW"/>
     <cge:Meas_Ref ObjectId="143256"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4759">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 898.000000 -324.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="834" ObjectName="SW-CX_YA.CX_YA_35267SW"/>
     <cge:Meas_Ref ObjectId="4759"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4801">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="843" ObjectName="SW-CX_YA.CX_YA_3566SW"/>
     <cge:Meas_Ref ObjectId="4801"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-143286">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -454.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="842" ObjectName="SW-CX_YA.CX_YA_3562SW"/>
     <cge:Meas_Ref ObjectId="143286"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-143287">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -325.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="844" ObjectName="SW-CX_YA.CX_YA_35667SW"/>
     <cge:Meas_Ref ObjectId="143287"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-143303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -454.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="846" ObjectName="SW-CX_YA.CX_YA_3542SW"/>
     <cge:Meas_Ref ObjectId="143303"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4824">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1138.000000 -325.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="848" ObjectName="SW-CX_YA.CX_YA_35467SW"/>
     <cge:Meas_Ref ObjectId="4824"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-4822">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="847" ObjectName="SW-CX_YA.CX_YA_3546SW"/>
     <cge:Meas_Ref ObjectId="4822"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145521">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25547" ObjectName="SW-YA_TP.YA_TP_3111SW"/>
     <cge:Meas_Ref ObjectId="145521"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145522">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25548" ObjectName="SW-YA_TP.YA_TP_3116SW"/>
     <cge:Meas_Ref ObjectId="145522"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -262.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25550" ObjectName="SW-YA_TP.YA_TP_31167SW"/>
     <cge:Meas_Ref ObjectId="145524"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -147.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25549" ObjectName="SW-YA_TP.YA_TP_31117SW"/>
     <cge:Meas_Ref ObjectId="145523"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145567">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -262.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25555" ObjectName="SW-YA_TP.YA_TP_31267SW"/>
     <cge:Meas_Ref ObjectId="145567"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145565">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25553" ObjectName="SW-YA_TP.YA_TP_3126SW"/>
     <cge:Meas_Ref ObjectId="145565"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -147.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25556" ObjectName="SW-YA_TP.YA_TP_31217SW"/>
     <cge:Meas_Ref ObjectId="145568"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145564">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25552" ObjectName="SW-YA_TP.YA_TP_3121SW"/>
     <cge:Meas_Ref ObjectId="145564"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145566">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -199.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25554" ObjectName="SW-YA_TP.YA_TP_31260SW"/>
     <cge:Meas_Ref ObjectId="145566"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148015">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 -211.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25885" ObjectName="SW-YA_MX.YA_MX_3416SW"/>
     <cge:Meas_Ref ObjectId="148015"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1568.000000 -102.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25884" ObjectName="SW-YA_MX.YA_MX_3411SW"/>
     <cge:Meas_Ref ObjectId="148014"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148016">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -270.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25886" ObjectName="SW-YA_MX.YA_MX_34167SW"/>
     <cge:Meas_Ref ObjectId="148016"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144551">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 485.000000 -1113.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25449" ObjectName="SW-YA_LC.YA_LC_3316SW"/>
     <cge:Meas_Ref ObjectId="144551"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144552">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 385.000000 -1050.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25450" ObjectName="SW-YA_LC.YA_LC_33160SW"/>
     <cge:Meas_Ref ObjectId="144552"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-144553">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -1050.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25451" ObjectName="SW-YA_LC.YA_LC_33167SW"/>
     <cge:Meas_Ref ObjectId="144553"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1285" cy="-1026" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="227" cy="-1118" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1725" cy="-825" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1285" cy="-633" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25443" cx="227" cy="-943" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="228" cy="-624" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="228" cy="-502" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="976" cy="-1026" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23829" cx="497" cy="-95" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25496" cx="632" cy="-783" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="799" cx="632" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="799" cx="725" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25930" cx="1124" cy="-1026" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="799" cx="747" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10189" cx="908" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10189" cx="960" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10189" cx="1081" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="1081" cy="-95" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25544" cx="909" cy="-95" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10189" cx="1200" cy="-511" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25880" cx="1577" cy="-95" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="228" cy="-379" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="MotifButton_Layer">
   <g href="ya_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-76" y="-1154"/></g>
   <g href="ya_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-125" y="-1171"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="-209" x2="-209" y1="11" y2="28"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-76" y="-1154"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-76" y="-1154"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-125" y="-1171"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-125" y="-1171"/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2043330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-1026 1285,-1008 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25939@1" Pin0InfoVect0LinkObjId="SW-148358_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b42e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-1026 1285,-1008 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2043590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-972 1285,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25939@0" ObjectIDZND0="25938@1" Pin0InfoVect0LinkObjId="SW-148356_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148358_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-972 1285,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20437f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-922 1285,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25938@0" ObjectIDZND0="25941@1" Pin0InfoVect0LinkObjId="SW-148360_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148356_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-922 1285,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_206aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="282,-1101 282,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="25448@0" ObjectIDZND0="25443@0" ObjectIDZND1="25447@x" Pin0InfoVect0LinkObjId="g_2c9c700_0" Pin0InfoVect1LinkObjId="SW-144549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="282,-1101 282,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_206b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1725,-825 1710,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1725,-825 1710,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_206b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,-825 1655,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,-825 1655,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_206b6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1628,-825 1607,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1628,-825 1607,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218bdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-646 1285,-633 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-646 1285,-633 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-755 1285,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-755 1285,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_218c270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-705 1285,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-705 1285,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-825 1285,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="25940@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-148359_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-825 1285,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_218e000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-815 1547,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25940@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-148359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-815 1547,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_211d110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1571,-825 1547,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="25940@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-148359_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1571,-825 1547,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_211d370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-825 1285,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="25940@x" ObjectIDZND2="25941@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-148359_0" Pin0InfoVect2LinkObjId="SW-148360_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-825 1285,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="227,-943 250,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25443@0" ObjectIDZND0="25455@0" Pin0InfoVect0LinkObjId="SW-144608_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_206aa50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="227,-943 250,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-943 307,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25455@1" ObjectIDZND0="25452@1" Pin0InfoVect0LinkObjId="SW-144604_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144608_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-943 307,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20f8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,-943 353,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25452@0" ObjectIDZND0="25456@0" Pin0InfoVect0LinkObjId="SW-144609_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144604_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,-943 353,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ade00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,-929 419,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25454@0" ObjectIDZND0="25456@x" ObjectIDZND1="25801@x" ObjectIDZND2="25802@x" Pin0InfoVect0LinkObjId="SW-144609_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="SW-147635_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="419,-929 419,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20ae8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,-943 419,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25456@1" ObjectIDZND0="25454@x" ObjectIDZND1="25801@x" ObjectIDZND2="25802@x" Pin0InfoVect0LinkObjId="SW-144607_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="SW-147635_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="389,-943 419,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fc9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="288,-624 309,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25800@1" ObjectIDZND0="25799@1" Pin0InfoVect0LinkObjId="SW-147631_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147633_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="288,-624 309,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20fcc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-624 355,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@0" ObjectIDZND0="25802@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-624 355,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-502 308,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25804@1" ObjectIDZND0="25803@1" Pin0InfoVect0LinkObjId="SW-147652_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="287,-502 308,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_204cc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="335,-502 354,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@0" ObjectIDZND0="25806@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="335,-502 354,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25219c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="286,-379 307,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25796@1" ObjectIDZND0="25795@1" Pin0InfoVect0LinkObjId="SW-147610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="286,-379 307,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2521c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="334,-379 353,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25795@0" ObjectIDZND0="25798@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="334,-379 353,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2525170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,-365 419,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25797@0" ObjectIDZND0="25798@x" ObjectIDZND1="23741@x" ObjectIDZND2="23739@x" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="SW-129580_0" Pin0InfoVect2LinkObjId="SW-129578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="419,-365 419,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25253d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="389,-379 419,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25798@1" ObjectIDZND0="25797@x" ObjectIDZND1="23741@x" ObjectIDZND2="23739@x" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="SW-129580_0" Pin0InfoVect2LinkObjId="SW-129578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147614_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="389,-379 419,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2245760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="252,-624 228,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25800@0" ObjectIDZND0="25791@0" Pin0InfoVect0LinkObjId="g_22459c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147633_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="252,-624 228,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22459c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="251,-502 228,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25804@0" ObjectIDZND0="25791@0" Pin0InfoVect0LinkObjId="g_2245760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="251,-502 228,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22466f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="249,-379 228,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="25791@0" Pin0InfoVect0LinkObjId="g_2245760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="249,-379 228,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2246950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,-610 421,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25801@0" ObjectIDZND0="25802@1" ObjectIDZND1="25454@x" ObjectIDZND2="25456@x" Pin0InfoVect0LinkObjId="SW-147635_1" Pin0InfoVect1LinkObjId="SW-144607_0" Pin0InfoVect2LinkObjId="SW-144609_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="421,-610 421,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2247440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="391,-624 421,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25802@1" ObjectIDZND0="25801@x" ObjectIDZND1="25454@x" ObjectIDZND2="25456@x" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="SW-144607_0" Pin0InfoVect2LinkObjId="SW-144609_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="391,-624 421,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2248500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,-624 543,-624 543,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25801@x" ObjectIDND1="25802@1" ObjectIDZND0="25454@x" ObjectIDZND1="25456@x" ObjectIDZND2="25935@x" Pin0InfoVect0LinkObjId="SW-144607_0" Pin0InfoVect1LinkObjId="SW-144609_0" Pin0InfoVect2LinkObjId="SW-148335_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="SW-147635_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="421,-624 543,-624 543,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2248760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="543,-816 543,-943 419,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25801@x" ObjectIDND1="25802@x" ObjectIDND2="25935@x" ObjectIDZND0="25454@x" ObjectIDZND1="25456@x" Pin0InfoVect0LinkObjId="SW-144607_0" Pin0InfoVect1LinkObjId="SW-144609_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="SW-147635_0" Pin1InfoVect2LinkObjId="SW-148335_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="543,-816 543,-943 419,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22489c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-1026 976,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25930@0" ObjectIDZND0="25934@1" Pin0InfoVect0LinkObjId="SW-148334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b42e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-1026 976,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2248c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-973 976,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25934@0" ObjectIDZND0="25933@1" Pin0InfoVect0LinkObjId="SW-148332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-973 976,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2248e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-923 976,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25933@0" ObjectIDZND0="25936@1" Pin0InfoVect0LinkObjId="SW-148336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="976,-923 976,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2249720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-841 976,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25935@0" ObjectIDZND0="25936@x" ObjectIDZND1="25801@x" ObjectIDZND2="25802@x" Pin0InfoVect0LinkObjId="SW-148336_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="SW-147635_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="967,-841 976,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-864 976,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25936@0" ObjectIDZND0="25935@x" ObjectIDZND1="25801@x" ObjectIDZND2="25802@x" Pin0InfoVect0LinkObjId="SW-148335_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="SW-147635_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="976,-864 976,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224a280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="976,-841 976,-816 543,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25935@x" ObjectIDND1="25936@x" ObjectIDZND0="25801@x" ObjectIDZND1="25802@x" ObjectIDZND2="25454@x" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="SW-147635_0" Pin0InfoVect2LinkObjId="SW-144607_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148335_0" Pin1InfoVect1LinkObjId="SW-148336_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="976,-841 976,-816 543,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_224a4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1275,-841 1285,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25940@0" ObjectIDZND0="25941@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-148360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148359_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1275,-841 1285,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223e250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-863 1285,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25941@0" ObjectIDZND0="25940@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-148359_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-863 1285,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_223e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1285,-841 1285,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25940@x" ObjectIDND1="25941@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148359_0" Pin1InfoVect1LinkObjId="SW-148360_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1285,-841 1285,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24935b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-95 497,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23829@0" ObjectIDZND0="23740@0" Pin0InfoVect0LinkObjId="SW-129579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-95 497,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-144 497,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23740@1" ObjectIDZND0="23738@0" Pin0InfoVect0LinkObjId="SW-129577_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-144 497,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2493a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-185 497,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23738@1" ObjectIDZND0="23739@0" Pin0InfoVect0LinkObjId="SW-129578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129577_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-185 497,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fe6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-633 632,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="828@0" ObjectIDZND0="831@1" Pin0InfoVect0LinkObjId="SW-4742_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-633 632,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21fe930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-583 632,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="831@0" ObjectIDZND0="827@1" Pin0InfoVect0LinkObjId="SW-4735_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4742_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-583 632,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21feb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="603,-756 632,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="25500@0" ObjectIDZND0="25496@0" ObjectIDZND1="25499@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-145179_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="603,-756 632,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ff680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-783 632,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25496@0" ObjectIDZND0="25500@x" ObjectIDZND1="25499@x" Pin0InfoVect0LinkObjId="SW-145180_0" Pin0InfoVect1LinkObjId="SW-145179_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21feb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,-783 632,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ff8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-756 632,-739 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="25500@x" ObjectIDND1="25496@0" ObjectIDZND0="25499@1" Pin0InfoVect0LinkObjId="SW-145179_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145180_0" Pin1InfoVect1LinkObjId="g_21feb90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-756 632,-739 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_21ffb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="602,-683 632,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="829@0" ObjectIDZND0="25499@x" ObjectIDZND1="828@x" Pin0InfoVect0LinkObjId="SW-145179_0" Pin0InfoVect1LinkObjId="SW-4736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="602,-683 632,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2200630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-703 632,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25499@0" ObjectIDZND0="829@x" ObjectIDZND1="828@x" Pin0InfoVect0LinkObjId="SW-4737_0" Pin0InfoVect1LinkObjId="SW-4736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,-703 632,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2200890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-683 632,-669 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="829@x" ObjectIDND1="25499@x" ObjectIDZND0="828@1" Pin0InfoVect0LinkObjId="SW-4736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-4737_0" Pin1InfoVect1LinkObjId="SW-145179_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-683 632,-669 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b40e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-524 632,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="827@0" ObjectIDZND0="799@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-524 632,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-900 1124,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25946@1" ObjectIDZND0="25943@0" Pin0InfoVect0LinkObjId="SW-148380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148384_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-900 1124,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-511 725,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="799@0" ObjectIDZND0="837@0" Pin0InfoVect0LinkObjId="SW-4779_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b40e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-511 725,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b426f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-560 725,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="837@1" ObjectIDZND0="841@0" Pin0InfoVect0LinkObjId="SW-4786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4779_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-560 725,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-610 725,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="841@1" ObjectIDZND0="838@0" Pin0InfoVect0LinkObjId="SW-143271_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4786_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="725,-610 725,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-950 1124,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25943@1" ObjectIDZND0="25944@0" Pin0InfoVect0LinkObjId="SW-148382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-950 1124,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-1009 1124,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25944@1" ObjectIDZND0="25930@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148382_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-1009 1124,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b43070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-841 1124,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25945@0" ObjectIDZND0="25946@x" ObjectIDZND1="839@x" ObjectIDZND2="838@x" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="SW-4781_0" Pin0InfoVect2LinkObjId="SW-143271_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-841 1124,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b43b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1124,-841 1124,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25945@x" ObjectIDND1="839@x" ObjectIDND2="838@x" ObjectIDZND0="25946@0" Pin0InfoVect0LinkObjId="SW-148384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148383_0" Pin1InfoVect1LinkObjId="SW-4781_0" Pin1InfoVect2LinkObjId="SW-143271_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1124,-841 1124,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b43dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-683 725,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="839@0" ObjectIDZND0="838@x" ObjectIDZND1="25945@x" ObjectIDZND2="25946@x" Pin0InfoVect0LinkObjId="SW-143271_0" Pin0InfoVect1LinkObjId="SW-148383_0" Pin0InfoVect2LinkObjId="SW-148384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="702,-683 725,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b448b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-668 725,-683 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="838@1" ObjectIDZND0="839@x" ObjectIDZND1="25945@x" ObjectIDZND2="25946@x" Pin0InfoVect0LinkObjId="SW-4781_0" Pin0InfoVect1LinkObjId="SW-148383_0" Pin0InfoVect2LinkObjId="SW-148384_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-143271_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="725,-668 725,-683 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b44b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-683 725,-715 1124,-715 1124,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="839@x" ObjectIDND1="838@x" ObjectIDZND0="25945@x" ObjectIDZND1="25946@x" Pin0InfoVect0LinkObjId="SW-148383_0" Pin0InfoVect1LinkObjId="SW-148384_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-4781_0" Pin1InfoVect1LinkObjId="SW-143271_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="725,-683 725,-715 1124,-715 1124,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="747,-511 747,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="799@0" ObjectIDZND0="9979@0" Pin0InfoVect0LinkObjId="SW-4545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b40e60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="747,-511 747,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-433 798,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9981@0" ObjectIDZND0="9979@x" ObjectIDZND1="9750@x" Pin0InfoVect0LinkObjId="SW-4545_0" Pin0InfoVect1LinkObjId="SW-55192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="798,-433 798,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf2440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-446 798,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9979@1" ObjectIDZND0="9981@x" ObjectIDZND1="9750@x" Pin0InfoVect0LinkObjId="SW-4547_0" Pin0InfoVect1LinkObjId="SW-55192_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4545_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="783,-446 798,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf2680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="798,-446 813,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9981@x" ObjectIDND1="9979@x" ObjectIDZND0="9750@1" Pin0InfoVect0LinkObjId="SW-55192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-4547_0" Pin1InfoVect1LinkObjId="SW-4545_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="798,-446 813,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf28e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-433 856,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9982@0" ObjectIDZND0="9750@x" ObjectIDZND1="9980@x" Pin0InfoVect0LinkObjId="SW-55192_0" Pin0InfoVect1LinkObjId="SW-4546_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4548_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="856,-433 856,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf33b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-446 856,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9750@0" ObjectIDZND0="9982@x" ObjectIDZND1="9980@x" Pin0InfoVect0LinkObjId="SW-4548_0" Pin0InfoVect1LinkObjId="SW-4546_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-55192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="840,-446 856,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf3610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-446 872,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9982@x" ObjectIDND1="9750@x" ObjectIDZND0="9980@0" Pin0InfoVect0LinkObjId="SW-4546_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-4548_0" Pin1InfoVect1LinkObjId="SW-55192_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="856,-446 872,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cf3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-446 908,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9980@1" ObjectIDZND0="10189@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4546_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="908,-446 908,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-408 960,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="836@0" ObjectIDZND0="833@1" Pin0InfoVect0LinkObjId="SW-4757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-408 960,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-511 960,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10189@0" ObjectIDZND0="832@1" Pin0InfoVect0LinkObjId="SW-143256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf3870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-511 960,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b5a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-458 960,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="832@0" ObjectIDZND0="836@1" Pin0InfoVect0LinkObjId="SW-4764_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-143256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="960,-458 960,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b6060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-331 960,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="834@0" ObjectIDZND0="833@x" ObjectIDZND1="25805@x" ObjectIDZND2="25806@x" Pin0InfoVect0LinkObjId="SW-4757_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="SW-147656_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="949,-331 960,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-349 960,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="833@0" ObjectIDZND0="834@x" ObjectIDZND1="25805@x" ObjectIDZND2="25806@x" Pin0InfoVect0LinkObjId="SW-4759_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="SW-147656_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="960,-349 960,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-250 497,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="23741@0" ObjectIDZND0="23739@x" ObjectIDZND1="25798@x" ObjectIDZND2="25797@x" Pin0InfoVect0LinkObjId="SW-129578_0" Pin0InfoVect1LinkObjId="SW-147614_0" Pin0InfoVect2LinkObjId="SW-147613_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-129580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="486,-250 497,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b7610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-250 497,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="23741@x" ObjectIDND1="25798@x" ObjectIDND2="25797@x" ObjectIDZND0="23739@1" Pin0InfoVect0LinkObjId="SW-129578_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-129580_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="SW-147613_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-250 497,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-409 1081,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="845@0" ObjectIDZND0="843@1" Pin0InfoVect0LinkObjId="SW-4801_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4807_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-409 1081,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-511 1081,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10189@0" ObjectIDZND0="842@1" Pin0InfoVect0LinkObjId="SW-143286_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf3870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-511 1081,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c2f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-459 1081,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="842@0" ObjectIDZND0="845@1" Pin0InfoVect0LinkObjId="SW-4807_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-143286_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-459 1081,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c3180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-108 1081,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25547@0" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_2c82960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145521_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-108 1081,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c33e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-218 1081,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25548@0" ObjectIDZND0="25546@1" Pin0InfoVect0LinkObjId="SW-145519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145522_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-218 1081,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c3640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1070,-332 1081,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="844@0" ObjectIDZND0="843@x" ObjectIDZND1="25550@x" ObjectIDZND2="25548@x" Pin0InfoVect0LinkObjId="SW-4801_0" Pin0InfoVect1LinkObjId="SW-145524_0" Pin0InfoVect2LinkObjId="SW-145522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-143287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1070,-332 1081,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c4130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-350 1081,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="843@0" ObjectIDZND0="844@x" ObjectIDZND1="25550@x" ObjectIDZND2="25548@x" Pin0InfoVect0LinkObjId="SW-143287_0" Pin0InfoVect1LinkObjId="SW-145524_0" Pin0InfoVect2LinkObjId="SW-145522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4801_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-350 1081,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c74d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1070,-154 1081,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25549@0" ObjectIDZND0="25547@x" ObjectIDZND1="25546@x" Pin0InfoVect0LinkObjId="SW-145521_0" Pin0InfoVect1LinkObjId="SW-145519_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145523_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1070,-154 1081,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-167 1081,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25546@0" ObjectIDZND0="25549@x" ObjectIDZND1="25547@x" Pin0InfoVect0LinkObjId="SW-145523_0" Pin0InfoVect1LinkObjId="SW-145521_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-167 1081,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c8220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-154 1081,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25549@x" ObjectIDND1="25546@x" ObjectIDZND0="25547@1" Pin0InfoVect0LinkObjId="SW-145521_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145523_0" Pin1InfoVect1LinkObjId="SW-145519_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-154 1081,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c80690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1070,-269 1081,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25550@0" ObjectIDZND0="844@x" ObjectIDZND1="843@x" ObjectIDZND2="25548@x" Pin0InfoVect0LinkObjId="SW-143287_0" Pin0InfoVect1LinkObjId="SW-4801_0" Pin0InfoVect2LinkObjId="SW-145522_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1070,-269 1081,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c81180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-332 1081,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="844@x" ObjectIDND1="843@x" ObjectIDZND0="25550@x" ObjectIDZND1="25548@x" Pin0InfoVect0LinkObjId="SW-145524_0" Pin0InfoVect1LinkObjId="SW-145522_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-143287_0" Pin1InfoVect1LinkObjId="SW-4801_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-332 1081,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c813e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1081,-269 1081,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25550@x" ObjectIDND1="844@x" ObjectIDND2="843@x" ObjectIDZND0="25548@1" Pin0InfoVect0LinkObjId="SW-145522_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145524_0" Pin1InfoVect1LinkObjId="SW-143287_0" Pin1InfoVect2LinkObjId="SW-4801_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1081,-269 1081,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c81ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="419,-379 497,-379 497,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25798@x" ObjectIDND1="25797@x" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" ObjectIDZND2="25555@x" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="SW-145567_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147614_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="419,-379 497,-379 497,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c82130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-287 497,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25798@x" ObjectIDND1="25797@x" ObjectIDND2="25555@x" ObjectIDZND0="23741@x" ObjectIDZND1="23739@x" Pin0InfoVect0LinkObjId="SW-129580_0" Pin0InfoVect1LinkObjId="SW-129578_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147614_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-145567_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-287 497,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c82960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-108 909,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25552@0" ObjectIDZND0="25544@0" Pin0InfoVect0LinkObjId="g_33c3180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-108 909,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c82bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-269 909,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25555@0" ObjectIDZND0="25798@x" ObjectIDZND1="25797@x" ObjectIDZND2="23741@x" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="SW-147613_0" Pin0InfoVect2LinkObjId="SW-129580_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145567_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="898,-269 909,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c836b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-287 909,-287 909,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25798@x" ObjectIDND1="25797@x" ObjectIDND2="23741@x" ObjectIDZND0="25555@x" ObjectIDZND1="25553@x" Pin0InfoVect0LinkObjId="SW-145567_0" Pin0InfoVect1LinkObjId="SW-145565_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147614_0" Pin1InfoVect1LinkObjId="SW-147613_0" Pin1InfoVect2LinkObjId="SW-129580_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-287 909,-287 909,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c83910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-269 909,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25555@x" ObjectIDND1="25798@x" ObjectIDND2="25797@x" ObjectIDZND0="25553@1" Pin0InfoVect0LinkObjId="SW-145565_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145567_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="SW-147613_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-269 909,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c83b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-154 909,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25556@0" ObjectIDZND0="25551@x" ObjectIDZND1="25552@x" Pin0InfoVect0LinkObjId="SW-145562_0" Pin0InfoVect1LinkObjId="SW-145564_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="898,-154 909,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c84660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-167 909,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25551@0" ObjectIDZND0="25556@x" ObjectIDZND1="25552@x" Pin0InfoVect0LinkObjId="SW-145568_0" Pin0InfoVect1LinkObjId="SW-145564_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="909,-167 909,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c848c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-154 909,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25556@x" ObjectIDND1="25551@x" ObjectIDZND0="25552@1" Pin0InfoVect0LinkObjId="SW-145564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145568_0" Pin1InfoVect1LinkObjId="SW-145562_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-154 909,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c87cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="898,-206 909,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25554@0" ObjectIDZND0="25553@x" ObjectIDZND1="25551@x" Pin0InfoVect0LinkObjId="SW-145565_0" Pin0InfoVect1LinkObjId="SW-145562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145566_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="898,-206 909,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c887a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-218 909,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25553@0" ObjectIDZND0="25554@x" ObjectIDZND1="25551@x" Pin0InfoVect0LinkObjId="SW-145566_0" Pin0InfoVect1LinkObjId="SW-145562_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145565_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="909,-218 909,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c88a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-206 909,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25554@x" ObjectIDND1="25553@x" ObjectIDZND0="25551@1" Pin0InfoVect0LinkObjId="SW-145562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145566_0" Pin1InfoVect1LinkObjId="SW-145565_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-206 909,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c93940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-511 1200,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10189@0" ObjectIDZND0="846@1" Pin0InfoVect0LinkObjId="SW-143303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cf3870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-511 1200,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c93ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-459 1200,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="846@0" ObjectIDZND0="849@1" Pin0InfoVect0LinkObjId="SW-4828_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-143303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-459 1200,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c93e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-409 1200,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="849@0" ObjectIDZND0="847@1" Pin0InfoVect0LinkObjId="SW-4822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4828_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-409 1200,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c94060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1189,-332 1200,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="848@0" ObjectIDZND0="847@x" ObjectIDZND1="25886@x" ObjectIDZND2="25885@x" Pin0InfoVect0LinkObjId="SW-4822_0" Pin0InfoVect1LinkObjId="SW-148016_0" Pin0InfoVect2LinkObjId="SW-148015_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-4824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1189,-332 1200,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c94b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-332 1200,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="848@x" ObjectIDND1="25886@x" ObjectIDND2="25885@x" ObjectIDZND0="847@0" Pin0InfoVect0LinkObjId="SW-4822_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-4824_0" Pin1InfoVect1LinkObjId="SW-148016_0" Pin1InfoVect2LinkObjId="SW-148015_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-332 1200,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c95c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-301 1200,-301 1200,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25886@x" ObjectIDND1="25885@x" ObjectIDND2="25449@x" ObjectIDZND0="848@x" ObjectIDZND1="847@x" Pin0InfoVect0LinkObjId="SW-4824_0" Pin0InfoVect1LinkObjId="SW-4822_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148016_0" Pin1InfoVect1LinkObjId="SW-148015_0" Pin1InfoVect2LinkObjId="SW-144551_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-301 1200,-301 1200,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c95e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-193 1577,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25883@1" ObjectIDZND0="25885@0" Pin0InfoVect0LinkObjId="SW-148015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-193 1577,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c960d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-95 1577,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25880@0" ObjectIDZND0="25884@0" Pin0InfoVect0LinkObjId="SW-148014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-95 1577,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c96330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-143 1577,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25884@1" ObjectIDZND0="25883@0" Pin0InfoVect0LinkObjId="SW-148012_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-143 1577,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c96590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1564,-277 1577,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25886@0" ObjectIDZND0="25885@x" ObjectIDZND1="848@x" ObjectIDZND2="847@x" Pin0InfoVect0LinkObjId="SW-148015_0" Pin0InfoVect1LinkObjId="SW-4824_0" Pin0InfoVect2LinkObjId="SW-4822_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148016_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1564,-277 1577,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c97080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-252 1577,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25885@1" ObjectIDZND0="25886@x" ObjectIDZND1="848@x" ObjectIDZND2="847@x" Pin0InfoVect0LinkObjId="SW-148016_0" Pin0InfoVect1LinkObjId="SW-4824_0" Pin0InfoVect2LinkObjId="SW-4822_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-252 1577,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c972e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1577,-277 1577,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25886@x" ObjectIDND1="25885@x" ObjectIDZND0="848@x" ObjectIDZND1="847@x" ObjectIDZND2="25449@x" Pin0InfoVect0LinkObjId="SW-4824_0" Pin0InfoVect1LinkObjId="SW-4822_0" Pin0InfoVect2LinkObjId="SW-144551_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-148016_0" Pin1InfoVect1LinkObjId="SW-148015_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1577,-277 1577,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9bc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="526,-1118 1811,-1118 1811,-301 1577,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25449@1" ObjectIDZND0="848@x" ObjectIDZND1="847@x" ObjectIDZND2="25886@x" Pin0InfoVect0LinkObjId="SW-4824_0" Pin0InfoVect1LinkObjId="SW-4822_0" Pin0InfoVect2LinkObjId="SW-148016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144551_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="526,-1118 1811,-1118 1811,-301 1577,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9c700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="282,-1118 227,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25448@x" ObjectIDND1="25447@x" ObjectIDZND0="25443@0" Pin0InfoVect0LinkObjId="g_206aa50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144550_0" Pin1InfoVect1LinkObjId="SW-144549_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="282,-1118 227,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c9c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="331,-1118 282,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25447@0" ObjectIDZND0="25448@x" ObjectIDZND1="25443@0" Pin0InfoVect0LinkObjId="SW-144550_0" Pin0InfoVect1LinkObjId="g_206aa50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144549_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="331,-1118 282,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca3060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-1101 468,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25451@0" ObjectIDZND0="25446@x" ObjectIDZND1="25449@x" Pin0InfoVect0LinkObjId="SW-144547_0" Pin0InfoVect1LinkObjId="SW-144551_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144553_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="468,-1101 468,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca32c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="394,-1101 394,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25450@0" ObjectIDZND0="25446@x" ObjectIDZND1="25447@x" Pin0InfoVect0LinkObjId="SW-144547_0" Pin0InfoVect1LinkObjId="SW-144549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144552_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="394,-1101 394,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca3db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="368,-1118 394,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25447@1" ObjectIDZND0="25450@x" ObjectIDZND1="25446@x" Pin0InfoVect0LinkObjId="SW-144552_0" Pin0InfoVect1LinkObjId="SW-144547_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="368,-1118 394,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca4010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="394,-1118 416,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25450@x" ObjectIDND1="25447@x" ObjectIDZND0="25446@1" Pin0InfoVect0LinkObjId="SW-144547_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144552_0" Pin1InfoVect1LinkObjId="SW-144549_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="394,-1118 416,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ca4b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="443,-1118 468,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25446@0" ObjectIDZND0="25451@x" ObjectIDZND1="25449@x" Pin0InfoVect0LinkObjId="SW-144553_0" Pin0InfoVect1LinkObjId="SW-144551_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-144547_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="443,-1118 468,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0b310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="468,-1118 490,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25451@x" ObjectIDND1="25446@x" ObjectIDZND0="25449@0" Pin0InfoVect0LinkObjId="SW-144551_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-144553_0" Pin1InfoVect1LinkObjId="SW-144547_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="468,-1118 490,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0ed20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="420,-488 420,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="25805@0" ObjectIDZND0="834@x" ObjectIDZND1="833@x" ObjectIDZND2="25806@x" Pin0InfoVect0LinkObjId="SW-4759_0" Pin0InfoVect1LinkObjId="SW-4757_0" Pin0InfoVect2LinkObjId="SW-147656_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="420,-488 420,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0f690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="960,-331 960,-303 543,-303 543,-502 420,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="834@x" ObjectIDND1="833@x" ObjectIDZND0="25805@x" ObjectIDZND1="25806@x" Pin0InfoVect0LinkObjId="SW-147655_0" Pin0InfoVect1LinkObjId="SW-147656_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-4759_0" Pin1InfoVect1LinkObjId="SW-4757_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="960,-331 960,-303 543,-303 543,-502 420,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d0f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="420,-502 390,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="25805@x" ObjectIDND1="834@x" ObjectIDND2="833@x" ObjectIDZND0="25806@1" Pin0InfoVect0LinkObjId="SW-147656_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="SW-4759_0" Pin1InfoVect2LinkObjId="SW-4757_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="420,-502 390,-502 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>