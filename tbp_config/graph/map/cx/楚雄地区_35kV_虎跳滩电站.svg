<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-286" aopId="1835050" id="thSvg" product="E8000V2" version="1.0" viewBox="-498 -1229 2154 1280">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape100">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="12" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="12" y2="16"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="9" y1="35" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="9" x2="9" y1="40" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="15" x2="9" y1="35" y2="41"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape16">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,53 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="55" y2="59"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape13_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="12" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="3" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="46" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="55" y2="46"/>
   </symbol>
   <symbol id="switch2:shape13-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="-5" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="4" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="62" y2="-5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="7" x2="-2" y1="63" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="16" x2="7" y1="54" y2="63"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer:shape0_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="32" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="32"/>
   </symbol>
   <symbol id="transformer:shape0_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape0-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="46" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="26" cy="69" fillStyle="0" rx="25.5" ry="26" stroke-width="0.540816"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="27" x2="27" y1="63" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="27" x2="35" y1="72" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="18" x2="27" y1="80" y2="72"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="26" cy="35" fillStyle="0" r="25.5" stroke-width="0.540816"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="27" x2="27" y1="23" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="27" x2="35" y1="31" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.540816" x1="18" x2="27" y1="39" y2="31"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape145">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="5" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="13" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="21" x2="21" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="21" x2="24" y1="28" y2="25"/>
    <ellipse cx="8" cy="20" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="20" cy="27" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="13" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="18" x2="21" y1="10" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape90">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(43,43,43)" height="1290" width="2164" x="-503" y="-1234"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1272,-750 1288,-750 1280,-741 1272,-750 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1288,-722 1272,-722 1280,-731 1288,-722 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1271,-571 1287,-571 1279,-562 1271,-571 " stroke="rgb(0,0,0)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1287,-543 1271,-543 1279,-552 1287,-543 " stroke="rgb(0,0,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-237843">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.751370 -784.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39687" ObjectName="SW-CX_HTT.CX_HTT_331BK"/>
     <cge:Meas_Ref ObjectId="237843"/>
    <cge:TPSR_Ref TObjectID="39687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237854">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 -969.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39694" ObjectName="SW-CX_HTT.CX_HTT_088BK"/>
     <cge:Meas_Ref ObjectId="237854"/>
    <cge:TPSR_Ref TObjectID="39694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237850">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -501.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39691" ObjectName="SW-CX_HTT.CX_HTT_601BK"/>
     <cge:Meas_Ref ObjectId="237850"/>
    <cge:TPSR_Ref TObjectID="39691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237935">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -350.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39715" ObjectName="SW-CX_HTT.CX_HTT_621BK"/>
     <cge:Meas_Ref ObjectId="237935"/>
    <cge:TPSR_Ref TObjectID="39715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237950">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -359.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39724" ObjectName="SW-CX_HTT.CX_HTT_622BK"/>
     <cge:Meas_Ref ObjectId="237950"/>
    <cge:TPSR_Ref TObjectID="39724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237940">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.000000 -352.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39718" ObjectName="SW-CX_HTT.CX_HTT_623BK"/>
     <cge:Meas_Ref ObjectId="237940"/>
    <cge:TPSR_Ref TObjectID="39718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237945">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -356.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39721" ObjectName="SW-CX_HTT.CX_HTT_624BK"/>
     <cge:Meas_Ref ObjectId="237945"/>
    <cge:TPSR_Ref TObjectID="39721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 -822.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39688" ObjectName="SW-CX_HTT.CX_HTT_001BK"/>
     <cge:Meas_Ref ObjectId="237846"/>
    <cge:TPSR_Ref TObjectID="39688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237859">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -829.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39699" ObjectName="SW-CX_HTT.CX_HTT_002BK"/>
     <cge:Meas_Ref ObjectId="237859"/>
    <cge:TPSR_Ref TObjectID="39699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237861">
    <use class="BV-400KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -419.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39702" ObjectName="SW-CX_HTT.CX_HTT_401BK"/>
     <cge:Meas_Ref ObjectId="237861"/>
    <cge:TPSR_Ref TObjectID="39702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237864">
    <use class="BV-400KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 -261.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39703" ObjectName="SW-CX_HTT.CX_HTT_431BK"/>
     <cge:Meas_Ref ObjectId="237864"/>
    <cge:TPSR_Ref TObjectID="39703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237867">
    <use class="BV-400KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -260.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39704" ObjectName="SW-CX_HTT.CX_HTT_432BK"/>
     <cge:Meas_Ref ObjectId="237867"/>
    <cge:TPSR_Ref TObjectID="39704"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTT.CX_HTT_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="59807"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.751370 -669.000000)" xlink:href="#transformer:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="59809"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.751370 -669.000000)" xlink:href="#transformer:shape0_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="59811"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 194.751370 -669.000000)" xlink:href="#transformer:shape0-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39708" ObjectName="TF-CX_HTT.CX_HTT_1T"/>
    <cge:TPSR_Ref TObjectID="39708"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTT.CX_HTT_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-80,-458 883,-458 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39710" ObjectName="BS-CX_HTT.CX_HTT_6IM"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   <polyline fill="none" opacity="0" points="-80,-458 883,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTT.CX_HTT_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="924,-915 1492,-915 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39680" ObjectName="BS-CX_HTT.CX_HTT_9IM"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   <polyline fill="none" opacity="0" points="924,-915 1492,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HTT.CX_HTT_4IM">
    <g class="BV-400KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-393 1596,-393 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39729" ObjectName="BS-CX_HTT.CX_HTT_4IM"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   <polyline fill="none" opacity="0" points="1042,-393 1596,-393 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTT.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -47.122677 2.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43442" ObjectName="SM-CX_HTT.P1"/>
    <cge:TPSR_Ref TObjectID="43442"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTT.P4">
    <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.616406 -89.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43445" ObjectName="SM-CX_HTT.P4"/>
    <cge:TPSR_Ref TObjectID="43445"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTT.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.877323 2.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43443" ObjectName="SM-CX_HTT.P2"/>
    <cge:TPSR_Ref TObjectID="43443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTT.P3">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.877323 1.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43444" ObjectName="SM-CX_HTT.P3"/>
    <cge:TPSR_Ref TObjectID="43444"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HTT.P5">
    <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.616406 -86.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43446" ObjectName="SM-CX_HTT.P5"/>
    <cge:TPSR_Ref TObjectID="43446"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_2b19550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f9690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-791 221,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer" ObjectIDND0="39687@0" ObjectIDZND0="39708@1" Pin0InfoVect0LinkObjId="g_21dc320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237843_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-791 221,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2099590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-417 -23,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39716@0" ObjectIDZND0="39710@0" Pin0InfoVect0LinkObjId="g_22765b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237936_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-417 -23,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2147920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="978,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="978,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24437b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="985,-1134 1012,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="39732@0" ObjectIDZND0="g_229d150@0" ObjectIDZND1="g_2b16910@0" ObjectIDZND2="g_26bb5f0@0" Pin0InfoVect0LinkObjId="g_229d150_0" Pin0InfoVect1LinkObjId="g_2b16910_0" Pin0InfoVect2LinkObjId="g_26bb5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237993_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="985,-1134 1012,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_223dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-47 -23,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43442@0" ObjectIDZND0="g_2325c20@0" Pin0InfoVect0LinkObjId="g_2325c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTT.P1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-47 -23,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21adc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-169 -23,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2325c20@1" ObjectIDZND0="0@x" ObjectIDZND1="g_210ed10@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_210ed10_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2325c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-169 -23,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_168f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-273 -23,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2325c20@0" ObjectIDND1="0@x" ObjectIDND2="g_210ed10@0" ObjectIDZND0="39717@0" Pin0InfoVect0LinkObjId="SW-237936_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2325c20_0" Pin1InfoVect1LinkObjId="g_2096cf0_0" Pin1InfoVect2LinkObjId="g_210ed10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-273 -23,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd3660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="54,-359 54,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2117580@0" ObjectIDZND0="g_20ea4c0@1" Pin0InfoVect0LinkObjId="g_20ea4c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2117580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="54,-359 54,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_211e7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-67,-241 -67,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2325c20@0" ObjectIDZND1="g_20ea4c0@0" ObjectIDZND2="39717@x" Pin0InfoVect0LinkObjId="g_2325c20_0" Pin0InfoVect1LinkObjId="g_20ea4c0_0" Pin0InfoVect2LinkObjId="SW-237936_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-67,-241 -67,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2106130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-126,-240 -126,-273 -67,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2325c20@0" ObjectIDZND2="g_20ea4c0@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2325c20_0" Pin0InfoVect2LinkObjId="g_20ea4c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-126,-240 -126,-273 -67,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f58ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-67,-273 -23,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_210ed10@0" ObjectIDND2="0@x" ObjectIDZND0="g_2325c20@0" ObjectIDZND1="g_20ea4c0@0" ObjectIDZND2="39717@x" Pin0InfoVect0LinkObjId="g_2325c20_0" Pin0InfoVect1LinkObjId="g_20ea4c0_0" Pin0InfoVect2LinkObjId="SW-237936_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="g_210ed10_0" Pin1InfoVect2LinkObjId="g_2096cf0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-67,-273 -23,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_256f180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-67,-294 -67,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_210ed10@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2325c20@0" ObjectIDZND2="g_20ea4c0@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2325c20_0" Pin0InfoVect2LinkObjId="g_20ea4c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_210ed10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-67,-294 -67,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a1c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="54,-300 54,-273 -23,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_20ea4c0@0" ObjectIDZND0="g_2325c20@0" ObjectIDZND1="0@x" ObjectIDZND2="g_210ed10@0" Pin0InfoVect0LinkObjId="g_2325c20_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_210ed10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20ea4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="54,-300 54,-273 -23,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_23e9a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-67,-172 -67,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_18eb650@0" Pin0InfoVect0LinkObjId="g_18eb650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-67,-172 -67,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20a1870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-126,-171 -126,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_18e9f60@0" Pin0InfoVect0LinkObjId="g_18e9f60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-126,-171 -126,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bdce10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-550 480,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39727@0" ObjectIDZND0="g_246e810@0" ObjectIDZND1="g_2703c50@0" Pin0InfoVect0LinkObjId="g_246e810_0" Pin0InfoVect1LinkObjId="g_2703c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="480,-550 480,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23f6d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,-584 549,-563 480,-563 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_246e810@1" ObjectIDZND0="39727@x" ObjectIDZND1="g_2703c50@0" Pin0InfoVect0LinkObjId="SW-237952_0" Pin0InfoVect1LinkObjId="g_2703c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_246e810_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="549,-584 549,-563 480,-563 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15460a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-563 410,-563 410,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39727@x" ObjectIDND1="g_246e810@0" ObjectIDZND0="g_2703c50@0" Pin0InfoVect0LinkObjId="g_2703c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237952_0" Pin1InfoVect1LinkObjId="g_246e810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-563 410,-563 410,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_251a400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-938 222,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="39682@1" ObjectIDZND0="g_2160230@0" ObjectIDZND1="39685@x" ObjectIDZND2="39684@x" Pin0InfoVect0LinkObjId="g_2160230_0" Pin0InfoVect1LinkObjId="SW-237839_0" Pin0InfoVect2LinkObjId="SW-237838_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237836_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="222,-938 222,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2519820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-1026 222,-1102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="39682@x" ObjectIDND1="39685@x" ObjectIDND2="39684@x" ObjectIDZND0="g_2160230@0" Pin0InfoVect0LinkObjId="g_2160230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-237836_0" Pin1InfoVect1LinkObjId="SW-237839_0" Pin1InfoVect2LinkObjId="SW-237838_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="222,-1026 222,-1102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23b8f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-1010 467,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39684@x" ObjectIDND1="g_2160230@0" ObjectIDND2="39682@x" ObjectIDZND0="39685@0" Pin0InfoVect0LinkObjId="SW-237839_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-237838_0" Pin1InfoVect1LinkObjId="g_2160230_0" Pin1InfoVect2LinkObjId="SW-237836_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-1010 467,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_217ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-1026 429,-1026 429,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2160230@0" ObjectIDND1="39682@x" ObjectIDZND0="39685@x" ObjectIDZND1="39684@x" Pin0InfoVect0LinkObjId="SW-237839_0" Pin0InfoVect1LinkObjId="SW-237838_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2160230_0" Pin1InfoVect1LinkObjId="SW-237836_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="222,-1026 429,-1026 429,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167cba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-1010 429,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="39685@x" ObjectIDND1="g_2160230@0" ObjectIDND2="39682@x" ObjectIDZND0="39684@0" Pin0InfoVect0LinkObjId="SW-237838_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-237839_0" Pin1InfoVect1LinkObjId="g_2160230_0" Pin1InfoVect2LinkObjId="SW-237836_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-1010 429,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2321df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-931 469,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="39684@x" ObjectIDND1="g_2480bb0@0" ObjectIDND2="g_23e47f0@0" ObjectIDZND0="39686@0" Pin0InfoVect0LinkObjId="SW-237840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-237838_0" Pin1InfoVect1LinkObjId="g_2480bb0_0" Pin1InfoVect2LinkObjId="g_23e47f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-931 469,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f607d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-958 429,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="39684@1" ObjectIDZND0="39686@x" ObjectIDZND1="g_2480bb0@0" ObjectIDZND2="g_23e47f0@0" Pin0InfoVect0LinkObjId="SW-237840_0" Pin0InfoVect1LinkObjId="g_2480bb0_0" Pin0InfoVect2LinkObjId="g_23e47f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237838_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="429,-958 429,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_20cfe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="459,-808 459,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2096cf0@0" ObjectIDZND0="g_23e47f0@1" Pin0InfoVect0LinkObjId="g_23e47f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="459,-808 459,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2518ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1057,-777 1012,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_217ba80@0" ObjectIDZND0="39730@x" ObjectIDZND1="39690@x" ObjectIDZND2="39708@x" Pin0InfoVect0LinkObjId="SW-237991_0" Pin0InfoVect1LinkObjId="SW-237847_0" Pin0InfoVect2LinkObjId="g_24f9690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_217ba80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1057,-777 1012,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c53000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="975,-777 1012,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="39730@0" ObjectIDZND0="39690@x" ObjectIDZND1="g_217ba80@0" ObjectIDZND2="39708@x" Pin0InfoVect0LinkObjId="SW-237847_0" Pin0InfoVect1LinkObjId="g_217ba80_0" Pin0InfoVect2LinkObjId="g_24f9690_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237991_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="975,-777 1012,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2164980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-946 1012,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39695@0" ObjectIDZND0="39680@0" Pin0InfoVect0LinkObjId="g_1ac6d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-946 1012,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f4390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-1181 1012,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_229d150@0" ObjectIDZND0="39732@x" ObjectIDZND1="g_2b16910@0" ObjectIDZND2="g_26bb5f0@0" Pin0InfoVect0LinkObjId="SW-237993_0" Pin0InfoVect1LinkObjId="g_2b16910_0" Pin0InfoVect2LinkObjId="g_26bb5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_229d150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-1181 1012,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26b2190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-1099 1109,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b16910@1" ObjectIDZND0="g_215a110@0" Pin0InfoVect0LinkObjId="g_215a110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b16910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-1099 1109,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20f3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-1099 1012,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2b16910@0" ObjectIDZND0="39732@x" ObjectIDZND1="g_229d150@0" ObjectIDZND2="g_26bb5f0@0" Pin0InfoVect0LinkObjId="SW-237993_0" Pin0InfoVect1LinkObjId="g_229d150_0" Pin0InfoVect2LinkObjId="g_26bb5f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b16910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-1099 1012,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_281b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-1038 1012,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="39696@0" ObjectIDZND0="g_2b16910@0" ObjectIDZND1="39732@x" ObjectIDZND2="g_229d150@0" Pin0InfoVect0LinkObjId="g_2b16910_0" Pin0InfoVect1LinkObjId="SW-237993_0" Pin0InfoVect2LinkObjId="g_229d150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237855_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-1038 1012,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1c999a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-1099 1012,-1134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b16910@0" ObjectIDND1="g_26bb5f0@0" ObjectIDND2="39696@x" ObjectIDZND0="39732@x" ObjectIDZND1="g_229d150@0" Pin0InfoVect0LinkObjId="SW-237993_0" Pin0InfoVect1LinkObjId="g_229d150_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b16910_0" Pin1InfoVect1LinkObjId="g_26bb5f0_0" Pin1InfoVect2LinkObjId="SW-237855_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-1099 1012,-1134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1bf3650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="983,-1099 1012,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_26bb5f0@0" ObjectIDZND0="g_2b16910@0" ObjectIDZND1="39732@x" ObjectIDZND2="g_229d150@0" Pin0InfoVect0LinkObjId="g_2b16910_0" Pin0InfoVect1LinkObjId="SW-237993_0" Pin0InfoVect2LinkObjId="g_229d150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26bb5f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="983,-1099 1012,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ac6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-945 1278,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39697@1" ObjectIDZND0="39680@0" Pin0InfoVect0LinkObjId="g_2164980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237856_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-945 1278,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1fe1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1347,-1117 1347,-1084 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_215b840@0" ObjectIDZND0="g_2518740@0" Pin0InfoVect0LinkObjId="g_2518740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_215b840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-1117 1347,-1084 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25762b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-1014 1278,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="39697@0" ObjectIDZND0="g_2518740@0" ObjectIDZND1="g_20d0f40@0" Pin0InfoVect0LinkObjId="g_2518740_0" Pin0InfoVect1LinkObjId="g_20d0f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-1014 1278,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_166e900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1347,-1054 1347,-1033 1278,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2518740@1" ObjectIDZND0="39697@x" ObjectIDZND1="g_20d0f40@0" Pin0InfoVect0LinkObjId="SW-237856_0" Pin0InfoVect1LinkObjId="g_20d0f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2518740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1347,-1054 1347,-1033 1278,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f90b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-1033 1208,-1033 1208,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="39697@x" ObjectIDND1="g_2518740@0" ObjectIDZND0="g_20d0f40@0" Pin0InfoVect0LinkObjId="g_20d0f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237856_0" Pin1InfoVect1LinkObjId="g_2518740_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-1033 1208,-1033 1208,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2830230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1335,-779 1280,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_24cddc0@0" ObjectIDZND0="39701@x" ObjectIDZND1="39707@x" Pin0InfoVect0LinkObjId="SW-237860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24cddc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1335,-779 1280,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e0c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-779 1280,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_24cddc0@0" ObjectIDND1="39707@x" ObjectIDZND0="39701@0" Pin0InfoVect0LinkObjId="SW-237860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24cddc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-779 1280,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2980e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-47 480,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43443@0" ObjectIDZND0="g_20e78a0@0" Pin0InfoVect0LinkObjId="g_20e78a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTT.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-47 480,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2482980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-169 480,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_20e78a0@1" ObjectIDZND0="g_23f7110@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_23f7110_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_20e78a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="480,-169 480,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23c7c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="557,-359 557,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2711390@0" ObjectIDZND0="g_23f7110@1" Pin0InfoVect0LinkObjId="g_23f7110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2711390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="557,-359 557,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_251bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="436,-241 436,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2099230@0" ObjectIDZND2="g_20e78a0@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2099230_0" Pin0InfoVect2LinkObjId="g_20e78a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="436,-241 436,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1ff9650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="377,-240 377,-273 436,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2099230@0" ObjectIDZND2="g_20e78a0@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2099230_0" Pin0InfoVect2LinkObjId="g_20e78a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="377,-240 377,-273 436,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2098fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="436,-273 480,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2099230@0" ObjectIDZND0="g_20e78a0@0" ObjectIDZND1="g_23f7110@0" ObjectIDZND2="39720@x" Pin0InfoVect0LinkObjId="g_20e78a0_0" Pin0InfoVect1LinkObjId="g_23f7110_0" Pin0InfoVect2LinkObjId="SW-237941_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="g_2096cf0_0" Pin1InfoVect2LinkObjId="g_2099230_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="436,-273 480,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2519ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="436,-294 436,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2099230@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_20e78a0@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_20e78a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2099230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="436,-294 436,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2519d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="557,-300 557,-273 480,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23f7110@0" ObjectIDZND0="g_20e78a0@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_20e78a0_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23f7110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="557,-300 557,-273 480,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2518080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="436,-172 436,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_24538a0@0" Pin0InfoVect0LinkObjId="g_24538a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="436,-172 436,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20e8580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="377,-171 377,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_2452320@0" Pin0InfoVect0LinkObjId="g_2452320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="377,-171 377,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24ff090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-48 816,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43444@0" ObjectIDZND0="g_117a270@0" Pin0InfoVect0LinkObjId="g_117a270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTT.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-48 816,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24ff2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-170 816,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_117a270@1" ObjectIDZND0="g_198d160@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_198d160_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_117a270_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="816,-170 816,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21642b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="893,-360 893,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_23e4430@0" ObjectIDZND0="g_198d160@1" Pin0InfoVect0LinkObjId="g_198d160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e4430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="893,-360 893,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2164510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-242 772,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2240fe0@0" ObjectIDZND2="g_117a270@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2240fe0_0" Pin0InfoVect2LinkObjId="g_117a270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="772,-242 772,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21c6b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-241 713,-274 772,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2240fe0@0" ObjectIDZND2="g_117a270@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2240fe0_0" Pin0InfoVect2LinkObjId="g_117a270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="713,-241 713,-274 772,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21c6df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-274 816,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2240fe0@0" ObjectIDZND0="g_117a270@0" ObjectIDZND1="g_198d160@0" ObjectIDZND2="39723@x" Pin0InfoVect0LinkObjId="g_117a270_0" Pin0InfoVect1LinkObjId="g_198d160_0" Pin0InfoVect2LinkObjId="SW-237946_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2096cf0_0" Pin1InfoVect1LinkObjId="g_2096cf0_0" Pin1InfoVect2LinkObjId="g_2240fe0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="772,-274 816,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2831410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-295 772,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2240fe0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_117a270@0" Pin0InfoVect0LinkObjId="g_2096cf0_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_117a270_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2240fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="772,-295 772,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2831670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="893,-301 893,-274 816,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_198d160@0" ObjectIDZND0="g_117a270@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_117a270_0" Pin0InfoVect1LinkObjId="g_2096cf0_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_198d160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="893,-301 893,-274 816,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_290cf60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="772,-173 772,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_2158a70@0" Pin0InfoVect0LinkObjId="g_2158a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="772,-173 772,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2830870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-172 713,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_2454f90@0" Pin0InfoVect0LinkObjId="g_2454f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2096cf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-172 713,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_244e590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-186 1587,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_244e7f0@1" ObjectIDZND0="g_24ad900@0" Pin0InfoVect0LinkObjId="g_24ad900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_244e7f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-186 1587,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_29781d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-217 1587,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_244e7f0@0" ObjectIDZND0="g_280eef0@0" ObjectIDZND1="g_18e9280@0" ObjectIDZND2="39704@x" Pin0InfoVect0LinkObjId="g_280eef0_0" Pin0InfoVect1LinkObjId="g_18e9280_0" Pin0InfoVect2LinkObjId="SW-237867_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_244e7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-217 1587,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2978400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-239 1587,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_244e7f0@0" ObjectIDND1="g_18e9280@0" ObjectIDND2="39704@x" ObjectIDZND0="g_280eef0@0" Pin0InfoVect0LinkObjId="g_280eef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_244e7f0_0" Pin1InfoVect1LinkObjId="g_18e9280_0" Pin1InfoVect2LinkObjId="SW-237867_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-239 1587,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_1bea070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-219 1366,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_217e2a0@0" ObjectIDZND0="g_23e5910@0" ObjectIDZND1="g_24aeff0@0" ObjectIDZND2="39703@x" Pin0InfoVect0LinkObjId="g_23e5910_0" Pin0InfoVect1LinkObjId="g_24aeff0_0" Pin0InfoVect2LinkObjId="SW-237864_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_217e2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-219 1366,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_1bea260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-241 1366,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_217e2a0@0" ObjectIDND1="g_24aeff0@0" ObjectIDND2="39703@x" ObjectIDZND0="g_23e5910@0" Pin0InfoVect0LinkObjId="g_23e5910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_217e2a0_0" Pin1InfoVect1LinkObjId="g_24aeff0_0" Pin1InfoVect2LinkObjId="SW-237864_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-241 1366,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_1bea450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-188 1366,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_217e2a0@1" ObjectIDZND0="g_24ac210@0" Pin0InfoVect0LinkObjId="g_24ac210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_217e2a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-188 1366,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_29281d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="189,-294 221,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_23ace40@0" ObjectIDZND0="g_2927cb0@0" ObjectIDZND1="39726@x" Pin0InfoVect0LinkObjId="g_2927cb0_0" Pin0InfoVect1LinkObjId="SW-237951_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ace40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="189,-294 221,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23ac9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-265 221,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2927cb0@1" ObjectIDZND0="g_23ace40@0" ObjectIDZND1="39726@x" Pin0InfoVect0LinkObjId="g_23ace40_0" Pin0InfoVect1LinkObjId="SW-237951_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2927cb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="221,-265 221,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23acbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-294 221,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_23ace40@0" ObjectIDND1="g_2927cb0@0" ObjectIDZND0="39726@0" Pin0InfoVect0LinkObjId="SW-237951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23ace40_0" Pin1InfoVect1LinkObjId="g_2927cb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-294 221,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22765b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-476 221,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39692@0" ObjectIDZND0="39710@0" Pin0InfoVect0LinkObjId="g_2099590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-476 221,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1970300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-458 480,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39710@0" ObjectIDZND0="39727@1" Pin0InfoVect0LinkObjId="SW-237952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2099590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-458 480,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2486460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-231 221,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2927cb0@0" ObjectIDZND0="g_1bf04f0@0" Pin0InfoVect0LinkObjId="g_1bf04f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2927cb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-231 221,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1be3b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-509 221,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39691@0" ObjectIDZND0="39692@1" Pin0InfoVect0LinkObjId="SW-237851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-509 221,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1be3d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-552 221,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39693@1" ObjectIDZND0="39691@1" Pin0InfoVect0LinkObjId="SW-237850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237851_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-552 221,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-358 -23,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39715@0" ObjectIDZND0="39717@1" Pin0InfoVect0LinkObjId="SW-237936_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237935_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-358 -23,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2909900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-23,-398 -23,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39716@1" ObjectIDZND0="39715@1" Pin0InfoVect0LinkObjId="SW-237935_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237936_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-23,-398 -23,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_27130f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-458 221,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39710@0" ObjectIDZND0="39725@0" Pin0InfoVect0LinkObjId="SW-237951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2099590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-458 221,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20cf770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-367 221,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39724@0" ObjectIDZND0="39726@1" Pin0InfoVect0LinkObjId="SW-237951_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-367 221,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20cf9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-407 221,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39725@1" ObjectIDZND0="39724@1" Pin0InfoVect0LinkObjId="SW-237950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237951_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-407 221,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2977320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="481,-360 481,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39718@0" ObjectIDZND0="39720@1" Pin0InfoVect0LinkObjId="SW-237941_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="481,-360 481,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2977580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-458 480,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39710@0" ObjectIDZND0="39719@0" Pin0InfoVect0LinkObjId="SW-237941_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2099590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-458 480,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_29777e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-404 480,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39719@1" ObjectIDZND0="39718@1" Pin0InfoVect0LinkObjId="SW-237940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237941_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="480,-404 480,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bfa450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="480,-325 480,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="39720@0" ObjectIDZND0="g_20e78a0@0" ObjectIDZND1="g_23f7110@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_20e78a0_0" Pin0InfoVect1LinkObjId="g_23f7110_0" Pin0InfoVect2LinkObjId="g_2096cf0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237941_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="480,-325 480,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2119760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-413 816,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39722@1" ObjectIDZND0="39721@1" Pin0InfoVect0LinkObjId="SW-237945_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237946_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-413 816,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21199c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-274 816,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_117a270@0" ObjectIDND1="g_198d160@0" ObjectIDND2="0@x" ObjectIDZND0="39723@0" Pin0InfoVect0LinkObjId="SW-237946_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_117a270_0" Pin1InfoVect1LinkObjId="g_198d160_0" Pin1InfoVect2LinkObjId="g_2096cf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-274 816,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2119c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-361 816,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39721@0" ObjectIDZND0="39723@1" Pin0InfoVect0LinkObjId="SW-237946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237945_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-361 816,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21dfd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-830 1012,-813 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39688@0" ObjectIDZND0="39690@1" Pin0InfoVect0LinkObjId="SW-237847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-830 1012,-813 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21f7d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-876 1012,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39689@1" ObjectIDZND0="39688@1" Pin0InfoVect0LinkObjId="SW-237846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237847_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-876 1012,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25147c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-915 1012,-891 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39680@0" ObjectIDZND0="39689@0" Pin0InfoVect0LinkObjId="SW-237847_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2164980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-915 1012,-891 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292f910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-837 1280,-814 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39699@0" ObjectIDZND0="39701@1" Pin0InfoVect0LinkObjId="SW-237860_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237859_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-837 1280,-814 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21cca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-915 1280,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="39680@0" ObjectIDZND0="39700@0" Pin0InfoVect0LinkObjId="SW-237860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2164980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-915 1280,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-887 1280,-864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39700@1" ObjectIDZND0="39699@1" Pin0InfoVect0LinkObjId="SW-237859_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-887 1280,-864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251f150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-1020 1012,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="39696@1" ObjectIDZND0="39694@1" Pin0InfoVect0LinkObjId="SW-237854_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237855_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-1020 1012,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251f3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-977 1012,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="39694@0" ObjectIDZND0="39695@1" Pin0InfoVect0LinkObjId="SW-237855_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237854_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-977 1012,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_251f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-393 1278,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="39729@0" ObjectIDZND0="39703@0" Pin0InfoVect0LinkObjId="SW-237864_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28219c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-393 1278,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_251f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-269 1278,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="39703@1" ObjectIDZND0="g_217e2a0@0" ObjectIDZND1="g_23e5910@0" ObjectIDZND2="g_24aeff0@0" Pin0InfoVect0LinkObjId="g_217e2a0_0" Pin0InfoVect1LinkObjId="g_23e5910_0" Pin0InfoVect2LinkObjId="g_24aeff0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237864_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-269 1278,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2520340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1366,-241 1278,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_217e2a0@0" ObjectIDND1="g_23e5910@0" ObjectIDZND0="g_24aeff0@0" ObjectIDZND1="39703@x" Pin0InfoVect0LinkObjId="g_24aeff0_0" Pin0InfoVect1LinkObjId="SW-237864_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_217e2a0_0" Pin1InfoVect1LinkObjId="g_23e5910_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1366,-241 1278,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25205a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-434 816,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="39722@0" ObjectIDZND0="39710@0" Pin0InfoVect0LinkObjId="g_2099590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-434 816,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251e7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="275,-716 1012,-717 1012,-777 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="39708@2" ObjectIDZND0="39730@x" ObjectIDZND1="39690@x" ObjectIDZND2="g_217ba80@0" Pin0InfoVect0LinkObjId="SW-237991_0" Pin0InfoVect1LinkObjId="SW-237847_0" Pin0InfoVect2LinkObjId="g_217ba80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f9690_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="275,-716 1012,-717 1012,-777 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-777 1012,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="39730@x" ObjectIDND1="g_217ba80@0" ObjectIDND2="39708@x" ObjectIDZND0="39690@0" Pin0InfoVect0LinkObjId="SW-237847_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-237991_0" Pin1InfoVect1LinkObjId="g_217ba80_0" Pin1InfoVect2LinkObjId="g_24f9690_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-777 1012,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_28219c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-359 1488,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="39704@0" ObjectIDZND0="39729@0" Pin0InfoVect0LinkObjId="g_21c2a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-359 1488,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24e2390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-874 409,-874 409,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_23e47f0@0" ObjectIDND1="39686@x" ObjectIDND2="39684@x" ObjectIDZND0="g_2480bb0@0" Pin0InfoVect0LinkObjId="g_2480bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23e47f0_0" Pin1InfoVect1LinkObjId="SW-237840_0" Pin1InfoVect2LinkObjId="SW-237838_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="429,-874 409,-874 409,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a9d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="459,-860 459,-874 429,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23e47f0@0" ObjectIDZND0="g_2480bb0@0" ObjectIDZND1="39686@x" ObjectIDZND2="39684@x" Pin0InfoVect0LinkObjId="g_2480bb0_0" Pin0InfoVect1LinkObjId="SW-237840_0" Pin0InfoVect2LinkObjId="SW-237838_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23e47f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="459,-860 459,-874 429,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="429,-874 429,-931 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2480bb0@0" ObjectIDND1="g_23e47f0@0" ObjectIDZND0="39686@x" ObjectIDZND1="39684@x" Pin0InfoVect0LinkObjId="SW-237840_0" Pin0InfoVect1LinkObjId="SW-237838_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2480bb0_0" Pin1InfoVect1LinkObjId="g_23e47f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="429,-874 429,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25210a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-886 222,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="39682@x" ObjectIDND1="39683@x" ObjectIDZND0="39687@1" Pin0InfoVect0LinkObjId="SW-237843_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237836_0" Pin1InfoVect1LinkObjId="SW-237837_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="222,-886 222,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23a2900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-596 242,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="39693@x" ObjectIDND1="39708@x" ObjectIDZND0="g_21dc530@0" Pin0InfoVect0LinkObjId="g_21dc530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237851_0" Pin1InfoVect1LinkObjId="g_24f9690_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-596 242,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21dc130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-571 221,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="39693@0" ObjectIDZND0="g_21dc530@0" ObjectIDZND1="39708@x" Pin0InfoVect0LinkObjId="g_21dc530_0" Pin0InfoVect1LinkObjId="g_24f9690_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237851_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="221,-571 221,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21dc320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="221,-596 220,-674 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer" ObjectIDND0="39693@x" ObjectIDND1="g_21dc530@0" ObjectIDZND0="39708@0" Pin0InfoVect0LinkObjId="g_24f9690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237851_0" Pin1InfoVect1LinkObjId="g_21dc530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="221,-596 220,-674 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21df4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,-615 549,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_246e810@0" ObjectIDZND0="g_21ddd70@0" Pin0InfoVect0LinkObjId="g_21ddd70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_246e810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="549,-615 549,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_18e8dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-241 1278,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_217e2a0@0" ObjectIDND1="g_23e5910@0" ObjectIDND2="39703@x" ObjectIDZND0="g_24aeff0@1" Pin0InfoVect0LinkObjId="g_24aeff0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_217e2a0_0" Pin1InfoVect1LinkObjId="g_23e5910_0" Pin1InfoVect2LinkObjId="SW-237864_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-241 1278,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e9020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-174 1278,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="g_24aeff0@0" ObjectIDZND0="43445@0" Pin0InfoVect0LinkObjId="SM-CX_HTT.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24aeff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-174 1278,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_18e9d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-135 1488,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="43446@0" ObjectIDZND0="g_18e9280@0" Pin0InfoVect0LinkObjId="g_18e9280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HTT.P5_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-135 1488,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_18ed240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1246,-744 1246,-766 1280,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="39731@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237992_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1246,-744 1246,-766 1280,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_21c1360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-682 1280,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="39707@0" ObjectIDZND0="g_24cddc0@0" ObjectIDZND1="39701@x" Pin0InfoVect0LinkObjId="g_24cddc0_0" Pin0InfoVect1LinkObjId="SW-237860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-682 1280,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_21c1550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-535 1488,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_24aab80@0" ObjectIDZND0="g_2688940@0" Pin0InfoVect0LinkObjId="g_2688940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24aab80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-535 1488,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_21c27a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1279,-600 1279,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="39707@1" ObjectIDZND0="39702@0" Pin0InfoVect0LinkObjId="SW-237861_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-600 1279,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-400KV" id="g_21c2a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1280,-426 1280,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="39702@1" ObjectIDZND0="39729@0" Pin0InfoVect0LinkObjId="g_28219c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237861_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1280,-426 1280,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_21c3230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1419,-457 1488,-457 1488,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="g_231fea0@0" ObjectIDND1="39729@0" ObjectIDZND0="g_2688940@1" Pin0InfoVect0LinkObjId="g_2688940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_231fea0_0" Pin1InfoVect1LinkObjId="g_28219c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1419,-457 1488,-457 1488,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_21c3d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1349,-479 1349,-457 1419,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="g_231fea0@0" ObjectIDZND0="g_2688940@0" ObjectIDZND1="39729@0" Pin0InfoVect0LinkObjId="g_2688940_0" Pin0InfoVect1LinkObjId="g_28219c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_231fea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1349,-479 1349,-457 1419,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21c3f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1419,-457 1419,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="busSection" ObjectIDND0="g_2688940@0" ObjectIDND1="g_231fea0@0" ObjectIDZND0="39729@0" Pin0InfoVect0LinkObjId="g_28219c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2688940_0" Pin1InfoVect1LinkObjId="g_231fea0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1419,-457 1419,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_21c4db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-239 1488,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_244e7f0@0" ObjectIDND1="g_280eef0@0" ObjectIDND2="39704@x" ObjectIDZND0="g_18e9280@1" Pin0InfoVect0LinkObjId="g_18e9280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_244e7f0_0" Pin1InfoVect1LinkObjId="g_280eef0_0" Pin1InfoVect2LinkObjId="SW-237867_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-239 1488,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_21c58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1587,-239 1488,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_244e7f0@0" ObjectIDND1="g_280eef0@0" ObjectIDZND0="g_18e9280@0" ObjectIDZND1="39704@x" Pin0InfoVect0LinkObjId="g_18e9280_0" Pin0InfoVect1LinkObjId="SW-237867_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_244e7f0_0" Pin1InfoVect1LinkObjId="g_280eef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1587,-239 1488,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_21c5b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1488,-239 1488,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_18e9280@0" ObjectIDND1="g_244e7f0@0" ObjectIDND2="g_280eef0@0" ObjectIDZND0="39704@1" Pin0InfoVect0LinkObjId="SW-237867_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_18e9280_0" Pin1InfoVect1LinkObjId="g_244e7f0_0" Pin1InfoVect2LinkObjId="g_280eef0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1488,-239 1488,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2929470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-902 222,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="39682@0" ObjectIDZND0="39687@x" ObjectIDZND1="39683@x" Pin0InfoVect0LinkObjId="SW-237843_0" Pin0InfoVect1LinkObjId="SW-237837_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="222,-902 222,-883 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29296d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="222,-883 160,-883 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="39687@x" ObjectIDND1="39682@x" ObjectIDZND0="39683@0" Pin0InfoVect0LinkObjId="SW-237837_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237843_0" Pin1InfoVect1LinkObjId="SW-237836_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="222,-883 160,-883 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="480" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="221" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="-23" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="221" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="480" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39710" cx="816" cy="-458" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39729" cx="1488" cy="-393" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39680" cx="1012" cy="-915" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39680" cx="1278" cy="-915" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39680" cx="1012" cy="-915" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39680" cx="1280" cy="-915" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39729" cx="1280" cy="-393" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39729" cx="1278" cy="-393" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39729" cx="1419" cy="-393" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-237627" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -193.000000 -1067.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39648" ObjectName="DYN-CX_HTT"/>
     <cge:Meas_Ref ObjectId="237627"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_fd2740" transform="matrix(1.000000 0.000000 -0.000000 1.000000 174.000000 -1154.000000) translate(0,18)">35kV黄虎线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_282fc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -476.000000 -559.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_23750a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -484.000000 -1038.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1fe1ac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -1147.500000) translate(0,16)">虎跳滩电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2441a10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 -162.000000 -475.000000) translate(0,15)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -659.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -659.000000) translate(0,27)">S11-800/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -659.000000) translate(0,42)">10±5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -659.000000) translate(0,57)">Y,yn0</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2413830" transform="matrix(1.000000 0.000000 -0.000000 1.000000 970.000000 -1229.000000) translate(0,18)">10kV土林线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24fab90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1044.000000 -425.000000) translate(0,15)">0.4kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b1b080" transform="matrix(1.000000 0.000000 -0.000000 1.000000 7.000000 -51.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_24906c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 507.000000 -50.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2490bb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 844.000000 -49.000000) translate(0,15)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2830b10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1236.000000 -77.000000) translate(0,15)">4号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2830d30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1450.000000 -77.000000) translate(0,15)">5号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2830f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.751370 -426.000000) translate(0,12)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2920260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 521.751370 -430.000000) translate(0,12)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2920500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.751370 -426.000000) translate(0,12)">3号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2276810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -151.000000 -89.000000) translate(0,12)">励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be47f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -85.000000 -92.000000) translate(0,12)">保护TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be4a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -85.000000) translate(0,12)">励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be4c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.000000 -93.000000) translate(0,12)">励磁TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1be4eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 420.000000 -86.000000) translate(0,12)">保护TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_196fe80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 756.000000 -95.000000) translate(0,12)">保护TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19700c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 176.000000 -102.000000) translate(0,15)">6kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19cff80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 572.000000 -674.000000) translate(0,15)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17342d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -720.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17342d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -720.000000) translate(0,27)">SFS11-6300/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17342d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -720.000000) translate(0,42)">38±2x2.5%/10.5±5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17342d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -24.000000 -720.000000) translate(0,57)">Y,y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_17345b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 429.000000 -781.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1734940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 22.000000 -21.000000) translate(0,12)">1.6MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2385ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 525.000000 -18.000000) translate(0,12)">1.6MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23860b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -19.000000) translate(0,12)">1.6MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23862f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -40.000000) translate(0,12)">0.32MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2386530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 -46.000000) translate(0,12)">0.32MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2270950" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1329.000000 -112.000000) translate(0,15)">4号发电机TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2270b80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1552.000000 -108.000000) translate(0,15)">5号发电机TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2270dc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1506.000000 -542.000000) translate(0,15)">0.4kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2271010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1375.000000 -1136.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bf02c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1107.000000 -1079.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24866c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 231.000000 -813.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246bbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 229.000000 -927.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246bdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 436.000000 -983.000000) translate(0,12)">3319</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -1036.000000) translate(0,12)">33197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 472.000000 -957.000000) translate(0,12)">33190</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_fe64d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 125.000000 -733.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fb7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1021.000000 -998.000000) translate(0,12)">088</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fbe20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -987.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1021.000000 -851.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -857.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 925.000000 -938.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 -657.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28fc960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -530.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -523.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 230.000000 -388.000000) translate(0,12)">622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -15.000000 -379.000000) translate(0,12)">621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -381.000000) translate(0,12)">623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251d9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 -385.000000) translate(0,12)">624</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251dc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 916.000000 -766.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ebc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1186.000000 -740.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_280cb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 949.000000 -1160.000000) translate(0,12)">08867</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2821c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1292.000000 -482.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2822250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -321.000000) translate(0,12)">431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2822490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1500.000000 -320.000000) translate(0,12)">432</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_24e0bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -446.000000 -855.000000) translate(0,16)">公共信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 117.000000 -909.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a2410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 229.000000 -927.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18ecd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -364.000000 -184.000000) translate(0,12)">13312607670</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -364.000000 -208.000000) translate(0,12)">8352050</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-497" y="-1060"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="-497" y="-1180"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="-74" y="-220"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="-133" y="-220"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="429" y="-220"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="370" y="-220"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="765" y="-221"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(60,120,255)" stroke-width="0.416667" width="14" x="706" y="-221"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2096cf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.000000 -786.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21ddd70">
    <use class="BV-6KV" transform="matrix(1.574775 -0.000000 0.000000 -1.583333 518.000000 -635.000000)" xlink:href="#voltageTransformer:shape145"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2452320">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 368.000000 -95.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24538a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 427.000000 -97.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2454f90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 -96.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2158a70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 763.000000 -98.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_215a110">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1104.000000 -1120.000000)" xlink:href="#voltageTransformer:shape145"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_215b840">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1326.000000 -1112.000000)" xlink:href="#voltageTransformer:shape145"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24aab80">
    <use class="BV-38KV" transform="matrix(1.235294 -0.000000 0.000000 -1.333333 1462.000000 -529.000000)" xlink:href="#voltageTransformer:shape145"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ac210">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -122.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ad900">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -118.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18e9f60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -135.000000 -95.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18eb650">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -76.000000 -97.000000)" xlink:href="#voltageTransformer:shape90"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2160230">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 231.751370 -1121.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_229d150">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1021.751370 -1200.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2117580">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 42.000000 -356.000000)" xlink:href="#lightningRod:shape100"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20ea4c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 44.000000 -295.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2325c20">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -130.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2703c50">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 403.000000 -576.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210ed10">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -74.000000 -290.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_246e810">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 540.000000 -579.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2480bb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 -804.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e47f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 450.000000 -824.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b16910">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1088.500000 -1090.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26bb5f0">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 929.500000 -1106.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_217ba80">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1111.500000 -770.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20d0f40">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1215.000000 -1108.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24cddc0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1389.500000 -772.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e5910">
    <use class="BV-38KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1373.000000 -320.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2518740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1338.000000 -1049.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_231fea0">
    <use class="BV-38KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1356.000000 -533.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2688940">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 -476.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2711390">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 -356.000000)" xlink:href="#lightningRod:shape100"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f7110">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 547.000000 -295.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20e78a0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 -130.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2099230">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -290.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e4430">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 -357.000000)" xlink:href="#lightningRod:shape100"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_198d160">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 -296.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117a270">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 -131.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2240fe0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -291.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_217e2a0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1357.000000 -183.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_280eef0">
    <use class="BV-38KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1594.000000 -318.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_244e7f0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1578.000000 -180.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2927cb0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -226.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ace40">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 135.500000 -301.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1bf04f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 206.000000 -111.000000)" xlink:href="#lightningRod:shape16"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21dc530">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 -588.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24aeff0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 -169.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_18e9280">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1478.000000 -169.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237735" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -827.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237735" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39687"/>
     <cge:Term_Ref ObjectID="59762"/>
    <cge:TPSR_Ref TObjectID="39687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -827.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39687"/>
     <cge:Term_Ref ObjectID="59762"/>
    <cge:TPSR_Ref TObjectID="39687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237740" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -827.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39687"/>
     <cge:Term_Ref ObjectID="59762"/>
    <cge:TPSR_Ref TObjectID="39687"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -554.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39691"/>
     <cge:Term_Ref ObjectID="59770"/>
    <cge:TPSR_Ref TObjectID="39691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -554.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39691"/>
     <cge:Term_Ref ObjectID="59770"/>
    <cge:TPSR_Ref TObjectID="39691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -554.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39691"/>
     <cge:Term_Ref ObjectID="59770"/>
    <cge:TPSR_Ref TObjectID="39691"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -868.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39688"/>
     <cge:Term_Ref ObjectID="59764"/>
    <cge:TPSR_Ref TObjectID="39688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -868.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39688"/>
     <cge:Term_Ref ObjectID="59764"/>
    <cge:TPSR_Ref TObjectID="39688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237754" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 922.000000 -868.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39688"/>
     <cge:Term_Ref ObjectID="59764"/>
    <cge:TPSR_Ref TObjectID="39688"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -867.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39699"/>
     <cge:Term_Ref ObjectID="59786"/>
    <cge:TPSR_Ref TObjectID="39699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -867.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39699"/>
     <cge:Term_Ref ObjectID="59786"/>
    <cge:TPSR_Ref TObjectID="39699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237783" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1395.000000 -867.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237783" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39699"/>
     <cge:Term_Ref ObjectID="59786"/>
    <cge:TPSR_Ref TObjectID="39699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 -1016.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39694"/>
     <cge:Term_Ref ObjectID="59776"/>
    <cge:TPSR_Ref TObjectID="39694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237764" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 -1016.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39694"/>
     <cge:Term_Ref ObjectID="59776"/>
    <cge:TPSR_Ref TObjectID="39694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 -1016.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39694"/>
     <cge:Term_Ref ObjectID="59776"/>
    <cge:TPSR_Ref TObjectID="39694"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237967" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 -488.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39702"/>
     <cge:Term_Ref ObjectID="59792"/>
    <cge:TPSR_Ref TObjectID="39702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 -488.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39702"/>
     <cge:Term_Ref ObjectID="59792"/>
    <cge:TPSR_Ref TObjectID="39702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 -488.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39702"/>
     <cge:Term_Ref ObjectID="59792"/>
    <cge:TPSR_Ref TObjectID="39702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39715"/>
     <cge:Term_Ref ObjectID="59816"/>
    <cge:TPSR_Ref TObjectID="39715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39715"/>
     <cge:Term_Ref ObjectID="59816"/>
    <cge:TPSR_Ref TObjectID="39715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -56.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39715"/>
     <cge:Term_Ref ObjectID="59816"/>
    <cge:TPSR_Ref TObjectID="39715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39718"/>
     <cge:Term_Ref ObjectID="59822"/>
    <cge:TPSR_Ref TObjectID="39718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39718"/>
     <cge:Term_Ref ObjectID="59822"/>
    <cge:TPSR_Ref TObjectID="39718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39718"/>
     <cge:Term_Ref ObjectID="59822"/>
    <cge:TPSR_Ref TObjectID="39718"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39721"/>
     <cge:Term_Ref ObjectID="59828"/>
    <cge:TPSR_Ref TObjectID="39721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39721"/>
     <cge:Term_Ref ObjectID="59828"/>
    <cge:TPSR_Ref TObjectID="39721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 794.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39721"/>
     <cge:Term_Ref ObjectID="59828"/>
    <cge:TPSR_Ref TObjectID="39721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237794" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39703"/>
     <cge:Term_Ref ObjectID="59794"/>
    <cge:TPSR_Ref TObjectID="39703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237795" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39703"/>
     <cge:Term_Ref ObjectID="59794"/>
    <cge:TPSR_Ref TObjectID="39703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237799" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39703"/>
     <cge:Term_Ref ObjectID="59794"/>
    <cge:TPSR_Ref TObjectID="39703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-237812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39704"/>
     <cge:Term_Ref ObjectID="59796"/>
    <cge:TPSR_Ref TObjectID="39704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-237813" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237813" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39704"/>
     <cge:Term_Ref ObjectID="59796"/>
    <cge:TPSR_Ref TObjectID="39704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-237817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39704"/>
     <cge:Term_Ref ObjectID="59796"/>
    <cge:TPSR_Ref TObjectID="39704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-237929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -543.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39710"/>
     <cge:Term_Ref ObjectID="59815"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-237930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -543.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39710"/>
     <cge:Term_Ref ObjectID="59815"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-237931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -543.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39710"/>
     <cge:Term_Ref ObjectID="59815"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-237932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -543.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39710"/>
     <cge:Term_Ref ObjectID="59815"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-237983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -3.000000 -543.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39710"/>
     <cge:Term_Ref ObjectID="59815"/>
    <cge:TPSR_Ref TObjectID="39710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-237984" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -486.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237984" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39729"/>
     <cge:Term_Ref ObjectID="59813"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-237985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -486.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39729"/>
     <cge:Term_Ref ObjectID="59813"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-237986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -486.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39729"/>
     <cge:Term_Ref ObjectID="59813"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-237987" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -486.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39729"/>
     <cge:Term_Ref ObjectID="59813"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-237990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1625.000000 -486.500000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39729"/>
     <cge:Term_Ref ObjectID="59813"/>
    <cge:TPSR_Ref TObjectID="39729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-237828" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -1009.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237828" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39680"/>
     <cge:Term_Ref ObjectID="59749"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-237829" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -1009.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237829" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39680"/>
     <cge:Term_Ref ObjectID="59749"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-237830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -1009.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39680"/>
     <cge:Term_Ref ObjectID="59749"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-237831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -1009.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39680"/>
     <cge:Term_Ref ObjectID="59749"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-237834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -1009.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39680"/>
     <cge:Term_Ref ObjectID="59749"/>
    <cge:TPSR_Ref TObjectID="39680"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="-369" y="-1158"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-418" y="-1175"/></g>
   <g href="35kV虎跳滩电站1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="125" y="-733"/></g>
   <g href="35kV虎跳滩电站2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1326" y="-657"/></g>
   <g href="35kV虎跳滩电站10kV土林线088间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1021" y="-998"/></g>
   <g href="35kV虎跳滩电站6kV1号机组621间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="-15" y="-379"/></g>
   <g href="35kV虎跳滩电站6kV站用变622间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="230" y="-388"/></g>
   <g href="35kV虎跳滩电站6kV2号机组623间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="489" y="-381"/></g>
   <g href="35kV虎跳滩电站6kV3号机组624间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="825" y="-385"/></g>
   <g href="35kV虎跳滩电站0.4kV4号机组431间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1290" y="-321"/></g>
   <g href="35kV虎跳滩电站0.4kV5号机组432间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1500" y="-320"/></g>
   <g href="35kV虎跳滩电站GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="75" x="-445" y="-861"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c3c20" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 -60.000000 528.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20f4b40" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 -60.000000 513.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193a5c0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 -68.000000 498.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_229dad0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 -60.000000 543.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_ecf8b0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 -52.000000 483.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1bce740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 76.000000 827.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21446f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 65.000000 812.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ba980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 90.000000 797.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218c3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 867.000000 868.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2388990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 853.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118eb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 881.000000 838.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_244e260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 488.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2703860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 473.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d5e870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1144.000000 458.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24f5be0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1423.000000 994.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c0c500" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1423.000000 979.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_213b5a0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1415.000000 964.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2909400" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1423.000000 1009.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_290d520" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1431.000000 949.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.000000 -0.689655 -0.941176 -0.000000 1668.294118 -348.068966)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="533" x2="519" y1="777" y2="762"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="519" x2="548" y1="762" y2="762"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="533" x2="548" y1="777" y2="762"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="533" x2="519" y1="777" y2="762"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(-0.000000 0.700000 0.705882 0.000000 -227.823529 -1088.950000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="533" x2="519" y1="777" y2="762"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="519" x2="548" y1="762" y2="762"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="533" x2="548" y1="777" y2="762"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="533" x2="519" y1="777" y2="762"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 554.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 268.000000 539.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25220a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 524.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25228a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 868.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 853.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.000000 838.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 1016.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2810360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1062.000000 1001.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2810570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1087.000000 986.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2810dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -145.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2811010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -156.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2811250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -131.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ad450" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1569.000000 471.500000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ad660" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1569.000000 456.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ad8a0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1561.000000 441.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23adae0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1569.000000 486.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23add20" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 1577.000000 426.033333) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.733333 -0.000000 0.000000 -0.888889 119.400000 -115.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="129" x2="144" y1="592" y2="592"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="144" x2="137" y1="592" y2="585"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="129" x2="137" y1="592" y2="585"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="129" x2="144" y1="592" y2="592"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(-0.733333 -0.000000 -0.000000 0.888889 321.600000 -1140.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="129" x2="144" y1="592" y2="592"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="144" x2="137" y1="592" y2="585"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(139,139,0)" stroke-width="1" x1="129" x2="137" y1="592" y2="585"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="129" x2="144" y1="592" y2="592"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2929a50" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 277.000000 850.033333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2929f40" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 267.000000 832.033333) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292a180" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 277.000000 884.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292a3c0" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 274.000000 815.033333) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_292a600" transform="matrix(1.000000 -0.000000 0.000000 -0.966667 276.000000 867.500000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HTT.CX_HTT_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="59804"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 -591.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-400KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 -591.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39707" ObjectName="TF-CX_HTT.CX_HTT_2T"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="-369" y="-1158"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="-369" y="-1158"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-418" y="-1175"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-418" y="-1175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="125" y="-733"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="125" y="-733"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1326" y="-657"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1326" y="-657"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1021" y="-998"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1021" y="-998"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="-15" y="-379"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="-15" y="-379"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="230" y="-388"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="230" y="-388"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="489" y="-381"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="489" y="-381"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="825" y="-385"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="825" y="-385"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1290" y="-321"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1290" y="-321"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1500" y="-320"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1500" y="-320"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="75" x="-445" y="-861"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="75" x="-445" y="-861"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-237993">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 934.000000 -1127.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39732" ObjectName="SW-CX_HTT.CX_HTT_08867SW"/>
     <cge:Meas_Ref ObjectId="237993"/>
    <cge:TPSR_Ref TObjectID="39732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237952">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -487.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39727" ObjectName="SW-CX_HTT.CX_HTT_6901XC"/>
     <cge:Meas_Ref ObjectId="237952"/>
    <cge:TPSR_Ref TObjectID="39727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -74.000000 -178.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -133.000000 -177.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237839">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -1003.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39685" ObjectName="SW-CX_HTT.CX_HTT_33197SW"/>
     <cge:Meas_Ref ObjectId="237839"/>
    <cge:TPSR_Ref TObjectID="39685"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237840">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 464.000000 -924.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39686" ObjectName="SW-CX_HTT.CX_HTT_33190SW"/>
     <cge:Meas_Ref ObjectId="237840"/>
    <cge:TPSR_Ref TObjectID="39686"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237838">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39684" ObjectName="SW-CX_HTT.CX_HTT_3319SW"/>
     <cge:Meas_Ref ObjectId="237838"/>
    <cge:TPSR_Ref TObjectID="39684"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237991">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 966.000000 -726.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39730" ObjectName="SW-CX_HTT.CX_HTT_00117SW"/>
     <cge:Meas_Ref ObjectId="237991"/>
    <cge:TPSR_Ref TObjectID="39730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1271.000000 -951.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39697" ObjectName="SW-CX_HTT.CX_HTT_0901XC"/>
     <cge:Meas_Ref ObjectId="237856"/>
    <cge:TPSR_Ref TObjectID="39697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237992">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -693.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39731" ObjectName="SW-CX_HTT.CX_HTT_00217SW"/>
     <cge:Meas_Ref ObjectId="237992"/>
    <cge:TPSR_Ref TObjectID="39731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -178.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 -177.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 765.000000 -179.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 706.000000 -178.000000)" xlink:href="#switch2:shape13_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237855">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 -1013.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39696" ObjectName="SW-CX_HTT.CX_HTT_088XC1"/>
     <cge:Meas_Ref ObjectId="237855"/>
    <cge:TPSR_Ref TObjectID="39696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237855">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 -938.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39695" ObjectName="SW-CX_HTT.CX_HTT_088XC"/>
     <cge:Meas_Ref ObjectId="237855"/>
    <cge:TPSR_Ref TObjectID="39695"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237851">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -547.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39693" ObjectName="SW-CX_HTT.CX_HTT_601XC1"/>
     <cge:Meas_Ref ObjectId="237851"/>
    <cge:TPSR_Ref TObjectID="39693"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237851">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -469.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39692" ObjectName="SW-CX_HTT.CX_HTT_601XC"/>
     <cge:Meas_Ref ObjectId="237851"/>
    <cge:TPSR_Ref TObjectID="39692"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237936">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -391.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39716" ObjectName="SW-CX_HTT.CX_HTT_621XC"/>
     <cge:Meas_Ref ObjectId="237936"/>
    <cge:TPSR_Ref TObjectID="39716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237936">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -33.000000 -321.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39717" ObjectName="SW-CX_HTT.CX_HTT_621XC1"/>
     <cge:Meas_Ref ObjectId="237936"/>
    <cge:TPSR_Ref TObjectID="39717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237951">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -330.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39726" ObjectName="SW-CX_HTT.CX_HTT_622XC1"/>
     <cge:Meas_Ref ObjectId="237951"/>
    <cge:TPSR_Ref TObjectID="39726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237951">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 211.000000 -400.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39725" ObjectName="SW-CX_HTT.CX_HTT_622XC"/>
     <cge:Meas_Ref ObjectId="237951"/>
    <cge:TPSR_Ref TObjectID="39725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237941">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 470.000000 -397.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39719" ObjectName="SW-CX_HTT.CX_HTT_623XC"/>
     <cge:Meas_Ref ObjectId="237941"/>
    <cge:TPSR_Ref TObjectID="39719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237941">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 471.000000 -318.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39720" ObjectName="SW-CX_HTT.CX_HTT_623XC1"/>
     <cge:Meas_Ref ObjectId="237941"/>
    <cge:TPSR_Ref TObjectID="39720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237946">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 -410.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39722" ObjectName="SW-CX_HTT.CX_HTT_624XC"/>
     <cge:Meas_Ref ObjectId="237946"/>
    <cge:TPSR_Ref TObjectID="39722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237946">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 806.000000 -311.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39723" ObjectName="SW-CX_HTT.CX_HTT_624XC1"/>
     <cge:Meas_Ref ObjectId="237946"/>
    <cge:TPSR_Ref TObjectID="39723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237847">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 -789.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39690" ObjectName="SW-CX_HTT.CX_HTT_001XC1"/>
     <cge:Meas_Ref ObjectId="237847"/>
    <cge:TPSR_Ref TObjectID="39690"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237847">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1002.000000 -869.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39689" ObjectName="SW-CX_HTT.CX_HTT_001XC"/>
     <cge:Meas_Ref ObjectId="237847"/>
    <cge:TPSR_Ref TObjectID="39689"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -790.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39701" ObjectName="SW-CX_HTT.CX_HTT_002XC1"/>
     <cge:Meas_Ref ObjectId="237860"/>
    <cge:TPSR_Ref TObjectID="39701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237860">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1270.000000 -880.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39700" ObjectName="SW-CX_HTT.CX_HTT_002XC"/>
     <cge:Meas_Ref ObjectId="237860"/>
    <cge:TPSR_Ref TObjectID="39700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237837">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 109.000000 -876.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39683" ObjectName="SW-CX_HTT.CX_HTT_33160SW"/>
     <cge:Meas_Ref ObjectId="237837"/>
    <cge:TPSR_Ref TObjectID="39683"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237836">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 213.000000 -897.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39682" ObjectName="SW-CX_HTT.CX_HTT_3316SW"/>
     <cge:Meas_Ref ObjectId="237836"/>
    <cge:TPSR_Ref TObjectID="39682"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -381.000000 -1099.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237720" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -366.000000 -992.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237720" ObjectName="CX_HTT:CX_HTT_GG_P_0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237721" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -366.000000 -951.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237721" ObjectName="CX_HTT:CX_HTT_GG_Q_1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237743" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -883.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237743" ObjectName="CX_HTT:CX_HTT_331BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237745" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -849.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237745" ObjectName="CX_HTT:CX_HTT_331BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237746" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -830.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237746" ObjectName="CX_HTT:CX_HTT_331BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237744" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -867.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237744" ObjectName="CX_HTT:CX_HTT_331BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237738" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 330.000000 -813.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237738" ObjectName="CX_HTT:CX_HTT_331BK_U0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HTT"/>
</svg>