<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-87" aopId="3932930" id="thSvg" product="E8000V2" version="1.0" viewBox="3071 -1350 2080 1545">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape51">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="81" y1="118" y2="118"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="80" x2="87" y1="52" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="74" x2="92" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="83" x2="83" y1="73" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="81" x2="85" y1="48" y2="48"/>
    <circle cx="83" cy="85" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="101" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="105" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="109" y2="105"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="83" y1="81" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="83" x2="87" y1="85" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="79" x2="83" y1="89" y2="85"/>
    <ellipse cx="83" cy="103" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="20" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="2" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="44" x2="44" y1="20" y2="118"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="77" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="83" x2="117" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="117" x2="117" y1="85" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="117" y1="2" y2="2"/>
   </symbol>
   <symbol id="currentTransformer:shape0">
    <circle cx="18" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="10" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape21_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="28" x2="36" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="25" x2="25" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="25" x2="9" y1="23" y2="23"/>
   </symbol>
   <symbol id="switch2:shape21-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153509" x1="26" x2="26" y1="26" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="6" y1="27" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="10" x2="10" y1="30" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429363" x1="26" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="3" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="35" x2="37" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="36" x2="36" y1="32" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="36" x2="36" y1="34" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape89">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="27" x2="28" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="22" x2="21" y1="25" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="21" x2="28" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="8" y2="11"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="23" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="7" y2="11"/>
    <circle cx="23" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="8" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="7" y2="11"/>
   </symbol>
   <symbol id="voltageTransformer:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="69" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="29" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="83" y2="103"/>
    <circle cx="28" cy="83" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="54" cy="84" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="26" x2="29" y1="86" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="29" x2="32" y1="83" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="29" x2="29" y1="83" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="44" y1="90" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="90" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="38" x2="41" y1="93" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="41" x2="41" y1="76" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="38" x2="41" y1="79" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="41" x2="44" y1="76" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="93" y2="103"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="57" y1="86" y2="103"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="74" y1="103" y2="103"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="79" x2="79" y1="113" y2="110"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="69" y1="125" y2="122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="69" x2="79" y1="122" y2="113"/>
    <rect height="13" stroke-width="1" width="5" x="72" y="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="74" x2="74" y1="76" y2="111"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="76" x2="72" y1="136" y2="136"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="75" x2="73" y1="138" y2="138"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="71" y1="134" y2="134"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="74" x2="74" y1="134" y2="124"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="74" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="23" y2="5"/>
    <circle cx="41" cy="89" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="57" y1="88" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="57" y1="80" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="53" y1="80" y2="88"/>
    <ellipse cx="41" cy="77" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="71" y2="71"/>
    <rect height="27" stroke-width="0.416667" width="14" x="0" y="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="23" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="41" y1="23" y2="23"/>
    <rect height="27" stroke-width="0.416667" width="14" x="34" y="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3391640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33927b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33931a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3393e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3395070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3395d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33968b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33971d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cbc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cbc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3399fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3399fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_339c740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339e020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_339ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_339f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a0080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a16b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a2830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a2ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a40d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a4a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a5f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a73b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a7ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a8f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33b8320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ab1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33abe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33ad3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1555" width="2090" x="3066" y="-1355"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5048" x2="5105" y1="-370" y2="-370"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="5105" x2="5105" y1="-370" y2="-323"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4013" x2="4068" y1="-361" y2="-361"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="4068" x2="4068" y1="-361" y2="-330"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5142" x2="5151" y1="-513" y2="-513"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-192994">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.713355 -836.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29201" ObjectName="SW-LF_YJ.LF_YJ_302BK"/>
     <cge:Meas_Ref ObjectId="192994"/>
    <cge:TPSR_Ref TObjectID="29201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.713355 -554.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29182" ObjectName="SW-LF_YJ.LF_YJ_002BK"/>
     <cge:Meas_Ref ObjectId="192759"/>
    <cge:TPSR_Ref TObjectID="29182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.333333 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29202" ObjectName="SW-LF_YJ.LF_YJ_051BK"/>
     <cge:Meas_Ref ObjectId="193003"/>
    <cge:TPSR_Ref TObjectID="29202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192982">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.895765 -828.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19730" ObjectName="SW-LF_YJ.LF_YJ_301BK"/>
     <cge:Meas_Ref ObjectId="192982"/>
    <cge:TPSR_Ref TObjectID="19730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.895765 -524.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29177" ObjectName="SW-LF_YJ.LF_YJ_001BK"/>
     <cge:Meas_Ref ObjectId="192751"/>
    <cge:TPSR_Ref TObjectID="29177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-60183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.995657 -1093.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28984" ObjectName="SW-LF_YJ.LF_YJ_381BK"/>
     <cge:Meas_Ref ObjectId="60183"/>
    <cge:TPSR_Ref TObjectID="28984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193012">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.333333 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29203" ObjectName="SW-LF_YJ.LF_YJ_052BK"/>
     <cge:Meas_Ref ObjectId="193012"/>
    <cge:TPSR_Ref TObjectID="29203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193021">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.333333 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29204" ObjectName="SW-LF_YJ.LF_YJ_053BK"/>
     <cge:Meas_Ref ObjectId="193021"/>
    <cge:TPSR_Ref TObjectID="29204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193030">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.333333 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29205" ObjectName="SW-LF_YJ.LF_YJ_054BK"/>
     <cge:Meas_Ref ObjectId="193030"/>
    <cge:TPSR_Ref TObjectID="29205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.333333 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29206" ObjectName="SW-LF_YJ.LF_YJ_055BK"/>
     <cge:Meas_Ref ObjectId="193039"/>
    <cge:TPSR_Ref TObjectID="29206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193048">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.333333 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29207" ObjectName="SW-LF_YJ.LF_YJ_061BK"/>
     <cge:Meas_Ref ObjectId="193048"/>
    <cge:TPSR_Ref TObjectID="29207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193219">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.333333 -268.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29208" ObjectName="SW-LF_YJ.LF_YJ_063BK"/>
     <cge:Meas_Ref ObjectId="193219"/>
    <cge:TPSR_Ref TObjectID="29208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.333333 -266.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29210" ObjectName="SW-LF_YJ.LF_YJ_062BK"/>
     <cge:Meas_Ref ObjectId="193076"/>
    <cge:TPSR_Ref TObjectID="29210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192959">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.944444 3725.333333 -274.555556)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29209" ObjectName="SW-LF_YJ.LF_YJ_064BK"/>
     <cge:Meas_Ref ObjectId="192959"/>
    <cge:TPSR_Ref TObjectID="29209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192955">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.333333 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29214" ObjectName="SW-LF_YJ.LF_YJ_065BK"/>
     <cge:Meas_Ref ObjectId="192955"/>
    <cge:TPSR_Ref TObjectID="29214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.333333 -271.333333)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29187" ObjectName="SW-LF_YJ.LF_YJ_066BK"/>
     <cge:Meas_Ref ObjectId="192785"/>
    <cge:TPSR_Ref TObjectID="29187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193094">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.333333 -265.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29211" ObjectName="SW-LF_YJ.LF_YJ_012BK"/>
     <cge:Meas_Ref ObjectId="193094"/>
    <cge:TPSR_Ref TObjectID="29211"/></metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_387f940">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.995657 -1231.000000)" xlink:href="#currentTransformer:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJ.LF_YJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3631,-959 4813,-959 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29486" ObjectName="BS-LF_YJ.LF_YJ_3IM"/>
    <cge:TPSR_Ref TObjectID="29486"/></metadata>
   <polyline fill="none" opacity="0" points="3631,-959 4813,-959 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJ.LF_YJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4359,-409 5108,-409 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17456" ObjectName="BS-LF_YJ.LF_YJ_9IM"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   <polyline fill="none" opacity="0" points="4359,-409 5108,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJ.LF_YJ_9PM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3399,-19 5116,-19 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32061" ObjectName="BS-LF_YJ.LF_YJ_9PM"/>
    <cge:TPSR_Ref TObjectID="32061"/></metadata>
   <polyline fill="none" opacity="0" points="3399,-19 5116,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_YJ.LF_YJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3410,-409 4305,-409 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17484" ObjectName="BS-LF_YJ.LF_YJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   <polyline fill="none" opacity="0" points="3410,-409 4305,-409 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-LF_YJ.LF_YJ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.000000 139.000000)" xlink:href="#capacitor:shape51"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40098" ObjectName="CB-LF_YJ.LF_YJ_Cb1"/>
    <cge:TPSR_Ref TObjectID="40098"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -546.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3842.000000 -546.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -748.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -748.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJ.LF_YJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16278"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -724.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -724.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11574" ObjectName="TF-LF_YJ.LF_YJ_2T"/>
    <cge:TPSR_Ref TObjectID="11574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_YJ.LF_YJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="16286"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -720.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.000000 -720.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="11572" ObjectName="TF-LF_YJ.LF_YJ_1T"/>
    <cge:TPSR_Ref TObjectID="11572"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_38595c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.807818 -767.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_385a370">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.807818 -771.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_385ea50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.333333 69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3884100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.995657 -1277.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3896f90">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4061.000000 -720.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_389b600">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4684.000000 -695.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38abbe0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5027.666667 -214.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38b82b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4919.333333 69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38c2d30">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4886.666667 -214.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38d0f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4773.333333 68.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38dbcb0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4740.666667 -215.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38e97b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.333333 69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f49e0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4604.666667 -214.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39134c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4496.333333 68.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_391d800">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4463.666667 -215.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_392ba20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4177.333333 72.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3936300">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4140.666667 -211.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_394fe10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3851.666667 -211.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39645d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3986.666667 -209.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3971a60">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4071.666667 -97.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3975410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.944444 3746.333333 45.555556)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_397c170">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.944444 3708.666667 -220.722222)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_398b210">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.333333 69.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3992b10">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3572.666667 -214.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39b2880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3460.000000 60.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39cea70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -499.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a33cd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 -879.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3104.000000 -1108.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80994" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3196.000000 -957.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80994" ObjectName="LF_YJ:LF_YJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="1" id="ME-80995" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3196.000000 -917.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80995" ObjectName="LF_YJ:LF_YJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217863" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -1037.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217863" ObjectName="LF_YJ:LF_YJ_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217863" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3195.000000 -996.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217863" ObjectName="LF_YJ:LF_YJ_sumP1"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -868.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19730"/>
     <cge:Term_Ref ObjectID="25239"/>
    <cge:TPSR_Ref TObjectID="19730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192807" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -868.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19730"/>
     <cge:Term_Ref ObjectID="25239"/>
    <cge:TPSR_Ref TObjectID="19730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192803" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4678.000000 -868.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19730"/>
     <cge:Term_Ref ObjectID="25239"/>
    <cge:TPSR_Ref TObjectID="19730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-93091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1139.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28984"/>
     <cge:Term_Ref ObjectID="16241"/>
    <cge:TPSR_Ref TObjectID="28984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-93092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1139.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28984"/>
     <cge:Term_Ref ObjectID="16241"/>
    <cge:TPSR_Ref TObjectID="28984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-93088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -1139.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28984"/>
     <cge:Term_Ref ObjectID="16241"/>
    <cge:TPSR_Ref TObjectID="28984"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -881.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29201"/>
     <cge:Term_Ref ObjectID="25241"/>
    <cge:TPSR_Ref TObjectID="29201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192825" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -881.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29201"/>
     <cge:Term_Ref ObjectID="25241"/>
    <cge:TPSR_Ref TObjectID="29201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -881.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29201"/>
     <cge:Term_Ref ObjectID="25241"/>
    <cge:TPSR_Ref TObjectID="29201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -580.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29177"/>
     <cge:Term_Ref ObjectID="16219"/>
    <cge:TPSR_Ref TObjectID="29177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192816" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -580.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192816" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29177"/>
     <cge:Term_Ref ObjectID="16219"/>
    <cge:TPSR_Ref TObjectID="29177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4845.000000 -580.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29177"/>
     <cge:Term_Ref ObjectID="16219"/>
    <cge:TPSR_Ref TObjectID="29177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -592.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29182"/>
     <cge:Term_Ref ObjectID="16229"/>
    <cge:TPSR_Ref TObjectID="29182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -592.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29182"/>
     <cge:Term_Ref ObjectID="16229"/>
    <cge:TPSR_Ref TObjectID="29182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -592.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29182"/>
     <cge:Term_Ref ObjectID="16229"/>
    <cge:TPSR_Ref TObjectID="29182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29202"/>
     <cge:Term_Ref ObjectID="25243"/>
    <cge:TPSR_Ref TObjectID="29202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29202"/>
     <cge:Term_Ref ObjectID="25243"/>
    <cge:TPSR_Ref TObjectID="29202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29202"/>
     <cge:Term_Ref ObjectID="25243"/>
    <cge:TPSR_Ref TObjectID="29202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29203"/>
     <cge:Term_Ref ObjectID="25245"/>
    <cge:TPSR_Ref TObjectID="29203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29203"/>
     <cge:Term_Ref ObjectID="25245"/>
    <cge:TPSR_Ref TObjectID="29203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4908.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29203"/>
     <cge:Term_Ref ObjectID="25245"/>
    <cge:TPSR_Ref TObjectID="29203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192860" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192860" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29204"/>
     <cge:Term_Ref ObjectID="27512"/>
    <cge:TPSR_Ref TObjectID="29204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29204"/>
     <cge:Term_Ref ObjectID="27512"/>
    <cge:TPSR_Ref TObjectID="29204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192857" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29204"/>
     <cge:Term_Ref ObjectID="27512"/>
    <cge:TPSR_Ref TObjectID="29204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29205"/>
     <cge:Term_Ref ObjectID="27514"/>
    <cge:TPSR_Ref TObjectID="29205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29205"/>
     <cge:Term_Ref ObjectID="27514"/>
    <cge:TPSR_Ref TObjectID="29205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29205"/>
     <cge:Term_Ref ObjectID="27514"/>
    <cge:TPSR_Ref TObjectID="29205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29206"/>
     <cge:Term_Ref ObjectID="27516"/>
    <cge:TPSR_Ref TObjectID="29206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29206"/>
     <cge:Term_Ref ObjectID="27516"/>
    <cge:TPSR_Ref TObjectID="29206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4487.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29206"/>
     <cge:Term_Ref ObjectID="27516"/>
    <cge:TPSR_Ref TObjectID="29206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192931" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29210"/>
     <cge:Term_Ref ObjectID="27528"/>
    <cge:TPSR_Ref TObjectID="29210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29210"/>
     <cge:Term_Ref ObjectID="27528"/>
    <cge:TPSR_Ref TObjectID="29210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3993.000000 -10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29210"/>
     <cge:Term_Ref ObjectID="27528"/>
    <cge:TPSR_Ref TObjectID="29210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29209"/>
     <cge:Term_Ref ObjectID="27526"/>
    <cge:TPSR_Ref TObjectID="29209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29209"/>
     <cge:Term_Ref ObjectID="27526"/>
    <cge:TPSR_Ref TObjectID="29209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3717.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29209"/>
     <cge:Term_Ref ObjectID="27526"/>
    <cge:TPSR_Ref TObjectID="29209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29214"/>
     <cge:Term_Ref ObjectID="41321"/>
    <cge:TPSR_Ref TObjectID="29214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29214"/>
     <cge:Term_Ref ObjectID="41321"/>
    <cge:TPSR_Ref TObjectID="29214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29214"/>
     <cge:Term_Ref ObjectID="41321"/>
    <cge:TPSR_Ref TObjectID="29214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3449.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29187"/>
     <cge:Term_Ref ObjectID="16247"/>
    <cge:TPSR_Ref TObjectID="29187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3449.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29187"/>
     <cge:Term_Ref ObjectID="16247"/>
    <cge:TPSR_Ref TObjectID="29187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3449.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29187"/>
     <cge:Term_Ref ObjectID="16247"/>
    <cge:TPSR_Ref TObjectID="29187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29207"/>
     <cge:Term_Ref ObjectID="27518"/>
    <cge:TPSR_Ref TObjectID="29207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29207"/>
     <cge:Term_Ref ObjectID="27518"/>
    <cge:TPSR_Ref TObjectID="29207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4178.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29207"/>
     <cge:Term_Ref ObjectID="27518"/>
    <cge:TPSR_Ref TObjectID="29207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 150.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29208"/>
     <cge:Term_Ref ObjectID="27524"/>
    <cge:TPSR_Ref TObjectID="29208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 150.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29208"/>
     <cge:Term_Ref ObjectID="27524"/>
    <cge:TPSR_Ref TObjectID="29208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3894.000000 150.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29208"/>
     <cge:Term_Ref ObjectID="27524"/>
    <cge:TPSR_Ref TObjectID="29208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -185.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29211"/>
     <cge:Term_Ref ObjectID="27532"/>
    <cge:TPSR_Ref TObjectID="29211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -185.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29211"/>
     <cge:Term_Ref ObjectID="27532"/>
    <cge:TPSR_Ref TObjectID="29211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -185.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29211"/>
     <cge:Term_Ref ObjectID="27532"/>
    <cge:TPSR_Ref TObjectID="29211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-193161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -499.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17456"/>
     <cge:Term_Ref ObjectID="42011"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-193162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -499.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17456"/>
     <cge:Term_Ref ObjectID="42011"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-193163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -499.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17456"/>
     <cge:Term_Ref ObjectID="42011"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-193164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -499.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17456"/>
     <cge:Term_Ref ObjectID="42011"/>
    <cge:TPSR_Ref TObjectID="17456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-93193" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.969697 -0.000000 -0.000000 1.666667 3857.000000 -1084.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93193" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29486"/>
     <cge:Term_Ref ObjectID="42013"/>
    <cge:TPSR_Ref TObjectID="29486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-93194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.969697 -0.000000 -0.000000 1.666667 3857.000000 -1084.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29486"/>
     <cge:Term_Ref ObjectID="42013"/>
    <cge:TPSR_Ref TObjectID="29486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-93195" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.969697 -0.000000 -0.000000 1.666667 3857.000000 -1084.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93195" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29486"/>
     <cge:Term_Ref ObjectID="42013"/>
    <cge:TPSR_Ref TObjectID="29486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-93196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.969697 -0.000000 -0.000000 1.666667 3857.000000 -1084.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="93196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29486"/>
     <cge:Term_Ref ObjectID="42013"/>
    <cge:TPSR_Ref TObjectID="29486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-229385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -778.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11574"/>
     <cge:Term_Ref ObjectID="16279"/>
    <cge:TPSR_Ref TObjectID="11574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-79526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -778.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11574"/>
     <cge:Term_Ref ObjectID="16279"/>
    <cge:TPSR_Ref TObjectID="11574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-229384" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -778.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11572"/>
     <cge:Term_Ref ObjectID="16284"/>
    <cge:TPSR_Ref TObjectID="11572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-79525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4626.000000 -778.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="11572"/>
     <cge:Term_Ref ObjectID="16284"/>
    <cge:TPSR_Ref TObjectID="11572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-193168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3417.000000 -548.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-193169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3417.000000 -548.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-193170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3417.000000 -548.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-215346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -499.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-215347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -499.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-215348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -499.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17484"/>
     <cge:Term_Ref ObjectID="42012"/>
    <cge:TPSR_Ref TObjectID="17484"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3137" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3137" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3088" y="-1211"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3088" y="-1211"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3475" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3475" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3606" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3606" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="3744" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="3744" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3888" y="-299"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3888" y="-299"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4022" y="-297"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4022" y="-297"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4175" y="-295"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4175" y="-295"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4291" y="-294"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4291" y="-294"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4275" y="-1122"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4275" y="-1122"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4494" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4494" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="31" x="4635" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="31" x="4635" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4771" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4771" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4917" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4917" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="5056" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="5056" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="88" x="3089" y="-828"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="88" x="3089" y="-828"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3299" y="-1173"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3299" y="-1173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3299" y="-1208"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3299" y="-1208"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="70" x="4762" y="-781"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="70" x="4762" y="-781"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="20" qtmmishow="hidden" width="69" x="4144" y="-781"/>
    </a>
   <metadata/><rect fill="white" height="20" opacity="0" stroke="white" transform="" width="69" x="4144" y="-781"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3298" y="-1031"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3298" y="-1031"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3137" y="-1194"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3088" y="-1211"/></g>
   <g href="35kV羊街变10kV云铜铁峰线066间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3475" y="-302"/></g>
   <g href="35kV羊街变10kV华家线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3606" y="-302"/></g>
   <g href="35kV羊街变10kV可里线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="3744" y="-302"/></g>
   <g href="35kV羊街变10kV电容器063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3888" y="-299"/></g>
   <g href="35kV羊街变10kV旁路062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4022" y="-297"/></g>
   <g href="35kV羊街变10kV铁路线061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4175" y="-295"/></g>
   <g href="35kV羊街变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4291" y="-294"/></g>
   <g href="35kV羊街变35kV羊街线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4275" y="-1122"/></g>
   <g href="35kV羊街变10kV勤丰线055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4494" y="-303"/></g>
   <g href="35kV羊街变10kV六七六线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="31" x="4635" y="-302"/></g>
   <g href="35kV羊街变10kV云铜线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4771" y="-303"/></g>
   <g href="35kV羊街变10kV三二九线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4917" y="-302"/></g>
   <g href="35kV羊街变10kV四八七六线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="5056" y="-300"/></g>
   <g href="35kV羊街变公共间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="88" x="3089" y="-828"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3299" y="-1173"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3299" y="-1208"/></g>
   <g href="35kV羊街变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="70" x="4762" y="-781"/></g>
   <g href="35kV羊街变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="20" qtmmishow="hidden" width="69" x="4144" y="-781"/></g>
   <g href="AVC羊街站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3298" y="-1031"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3299" y="-1030"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3a29340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 -727.000000)" xlink:href="#voltageTransformer:shape89"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a2abc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 -520.000000)" xlink:href="#voltageTransformer:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a2f160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4471.000000 -556.000000)" xlink:href="#voltageTransformer:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 103.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34015" ObjectName="EC-LF_YJ.LD_YJ_051Ld"/>
    <cge:TPSR_Ref TObjectID="34015"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.000000 103.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18107" ObjectName="EC-LF_YJ.LD_YJ_052Ld"/>
    <cge:TPSR_Ref TObjectID="18107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 102.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18106" ObjectName="EC-LF_YJ.LD_YJ_053Ld"/>
    <cge:TPSR_Ref TObjectID="18106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 103.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18105" ObjectName="EC-LF_YJ.LD_YJ_054Ld"/>
    <cge:TPSR_Ref TObjectID="18105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_055Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 102.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18104" ObjectName="EC-LF_YJ.LD_YJ_055Ld"/>
    <cge:TPSR_Ref TObjectID="18104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_061Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 106.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18103" ObjectName="EC-LF_YJ.LD_YJ_061Ld"/>
    <cge:TPSR_Ref TObjectID="18103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 103.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18101" ObjectName="EC-LF_YJ.LD_YJ_065Ld"/>
    <cge:TPSR_Ref TObjectID="18101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_066Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 103.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18100" ObjectName="EC-LF_YJ.LD_YJ_066Ld"/>
    <cge:TPSR_Ref TObjectID="18100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_YJ.LD_YJ_064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 95.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18102" ObjectName="EC-LF_YJ.LD_YJ_064Ld"/>
    <cge:TPSR_Ref TObjectID="18102"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_3870db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-1009 4224,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28987@0" ObjectIDZND0="g_3868800@0" Pin0InfoVect0LinkObjId="g_3868800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-1009 4224,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3870fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-959 3721,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="29486@0" ObjectIDZND0="g_3a33cd0@1" Pin0InfoVect0LinkObjId="g_3a33cd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-959 3721,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3871190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3720,-888 3720,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3a33cd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_387f940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a33cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3720,-888 3720,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3871380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-776 3909,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_385a370@0" ObjectIDZND0="g_3a29340@0" Pin0InfoVect0LinkObjId="g_3a29340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_385a370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-776 3909,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38715b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-8 5068,-8 5068,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="34015@x" ObjectIDND1="29222@x" ObjectIDND2="29221@x" ObjectIDZND0="g_385ea50@0" Pin0InfoVect0LinkObjId="g_385ea50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_051Ld_0" Pin1InfoVect1LinkObjId="SW-193009_0" Pin1InfoVect2LinkObjId="SW-193008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-8 5068,-8 5068,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38717e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-8 5048,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_385ea50@0" ObjectIDND1="29222@x" ObjectIDND2="29221@x" ObjectIDZND0="34015@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_051Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_385ea50_0" Pin1InfoVect1LinkObjId="SW-193009_0" Pin1InfoVect2LinkObjId="SW-193008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-8 5048,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3871a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-959 4725,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29486@0" ObjectIDZND0="29216@1" Pin0InfoVect0LinkObjId="SW-229377_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-959 4725,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3871c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-959 4105,-938 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29486@0" ObjectIDZND0="29301@1" Pin0InfoVect0LinkObjId="SW-229379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-959 4105,-938 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3877ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-1083 4225,-1083 4225,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11595@x" ObjectIDND1="28984@x" ObjectIDZND0="28987@1" Pin0InfoVect0LinkObjId="SW-192970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-191786_0" Pin1InfoVect1LinkObjId="SW-60183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-1083 4225,-1083 4225,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3878570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1083 4266,-1064 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28987@x" ObjectIDND1="28984@x" ObjectIDZND0="11595@1" Pin0InfoVect0LinkObjId="SW-191786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192970_0" Pin1InfoVect1LinkObjId="SW-60183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1083 4266,-1064 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387a700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1101 4266,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="28984@0" ObjectIDZND0="28987@x" ObjectIDZND1="11595@x" Pin0InfoVect0LinkObjId="SW-192970_0" Pin0InfoVect1LinkObjId="SW-191786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-60183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1101 4266,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1149 4363,-1149 4363,-1163 4364,-1162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="28984@x" ObjectIDND1="28986@x" ObjectIDZND0="28989@0" Pin0InfoVect0LinkObjId="SW-192971_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-60183_0" Pin1InfoVect1LinkObjId="SW-192969_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1149 4363,-1149 4363,-1163 4364,-1162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387b4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1174 4266,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28986@0" ObjectIDZND0="28984@x" ObjectIDZND1="28989@x" Pin0InfoVect0LinkObjId="SW-60183_0" Pin0InfoVect1LinkObjId="SW-192971_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1174 4266,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387b700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1149 4266,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28989@x" ObjectIDND1="28986@x" ObjectIDZND0="28984@1" Pin0InfoVect0LinkObjId="SW-60183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192971_0" Pin1InfoVect1LinkObjId="SW-192969_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1149 4266,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1226 4209,-1226 4208,-1205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3884100@0" ObjectIDND2="18071@1" ObjectIDZND0="28988@0" Pin0InfoVect0LinkObjId="SW-192972_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="g_3884100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1226 4209,-1226 4208,-1205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_387f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1226 4266,-1210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3884100@0" ObjectIDND2="18071@1" ObjectIDZND0="28986@1" Pin0InfoVect0LinkObjId="SW-192969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="g_3884100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1226 4266,-1210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3883110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4368,-1238 4404,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="currentTransformer" ObjectIDND0="0@1" ObjectIDZND0="g_387f940@0" Pin0InfoVect0LinkObjId="g_387f940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387f940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4368,-1238 4404,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3883370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1238 4313,-1238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="28988@x" ObjectIDND1="28986@x" ObjectIDND2="g_3884100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_387f940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-192972_0" Pin1InfoVect1LinkObjId="SW-192969_0" Pin1InfoVect2LinkObjId="g_3884100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1238 4313,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3883ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1238 4266,-1226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_3884100@0" ObjectIDND2="18071@1" ObjectIDZND0="28988@x" ObjectIDZND1="28986@x" Pin0InfoVect0LinkObjId="SW-192972_0" Pin0InfoVect1LinkObjId="SW-192969_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="g_3884100_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1238 4266,-1226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3884e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1285 4317,-1285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="28988@x" ObjectIDND2="28986@x" ObjectIDZND0="g_3884100@0" Pin0InfoVect0LinkObjId="g_3884100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="SW-192972_0" Pin1InfoVect2LinkObjId="SW-192969_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1285 4317,-1285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a1e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-435 4105,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29183@0" ObjectIDZND0="17484@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-435 4105,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4801,-495 4827,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29180@1" ObjectIDZND0="g_38a7300@0" Pin0InfoVect0LinkObjId="g_38a7300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4801,-495 4827,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a7d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-512 4827,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a7300@0" ObjectIDZND0="29180@x" Pin0InfoVect0LinkObjId="SW-192754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-512 4827,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38a7ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-434 4724,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29178@0" ObjectIDZND0="17456@0" Pin0InfoVect0LinkObjId="g_3a206f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-434 4724,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29c89b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5086,-39 5086,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29222@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_38bf8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5086,-39 5086,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38beb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-89 4945,-89 4945,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_38b82b0@0" ObjectIDND1="18107@x" ObjectIDND2="29227@x" ObjectIDZND0="29228@1" Pin0InfoVect0LinkObjId="SW-193018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38b82b0_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_052Ld_0" Pin1InfoVect2LinkObjId="SW-193017_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-89 4945,-89 4945,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bed00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-8 4927,-8 4927,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="29227@x" ObjectIDND1="29228@x" ObjectIDND2="18107@x" ObjectIDZND0="g_38b82b0@0" Pin0InfoVect0LinkObjId="g_38b82b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193017_0" Pin1InfoVect1LinkObjId="SW-193018_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_052Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-8 4927,-8 4927,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38beef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-89 4907,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29227@x" ObjectIDND1="29228@x" ObjectIDZND0="g_38b82b0@0" ObjectIDZND1="18107@x" Pin0InfoVect0LinkObjId="g_38b82b0_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193017_0" Pin1InfoVect1LinkObjId="SW-193018_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-89 4907,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bf0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-8 4907,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_38b82b0@0" ObjectIDND1="29227@x" ObjectIDND2="29228@x" ObjectIDZND0="18107@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38b82b0_0" Pin1InfoVect1LinkObjId="SW-193017_0" Pin1InfoVect2LinkObjId="SW-193018_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-8 4907,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38bf8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4945,-39 4945,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29228@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4945,-39 4945,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c62d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-409 4907,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29223@1" Pin0InfoVect0LinkObjId="SW-193013_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-409 4907,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38c6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4964,-324 4964,-366 4910,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_38c6790@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38c6790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4964,-324 4964,-366 4910,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c7220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4927,-335 4907,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29225@1" ObjectIDZND0="29203@x" ObjectIDZND1="29223@x" Pin0InfoVect0LinkObjId="SW-193012_0" Pin0InfoVect1LinkObjId="SW-193013_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4927,-335 4907,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-355 4907,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29223@0" ObjectIDZND0="29203@x" ObjectIDZND1="29225@x" Pin0InfoVect0LinkObjId="SW-193012_0" Pin0InfoVect1LinkObjId="SW-193015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193013_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-355 4907,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38c76e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-335 4907,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29225@x" ObjectIDND1="29223@x" ObjectIDZND0="29203@1" Pin0InfoVect0LinkObjId="SW-193012_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193015_0" Pin1InfoVect1LinkObjId="SW-193013_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-335 4907,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38ca140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-122 4907,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29227@0" ObjectIDZND0="g_38b82b0@0" ObjectIDZND1="18107@x" ObjectIDZND2="29228@x" Pin0InfoVect0LinkObjId="g_38b82b0_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_052Ld_0" Pin0InfoVect2LinkObjId="SW-193018_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193017_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-122 4907,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38cec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-279 4907,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29203@0" ObjectIDZND0="29224@1" Pin0InfoVect0LinkObjId="SW-193014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193012_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-279 4907,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d7be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-90 4799,-90 4799,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_38d0f40@0" ObjectIDND1="18106@x" ObjectIDND2="29233@x" ObjectIDZND0="29234@1" Pin0InfoVect0LinkObjId="SW-193027_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38d0f40_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_053Ld_0" Pin1InfoVect2LinkObjId="SW-193026_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-90 4799,-90 4799,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d7dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-9 4781,-9 4781,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="29233@x" ObjectIDND1="29234@x" ObjectIDND2="18106@x" ObjectIDZND0="g_38d0f40@0" Pin0InfoVect0LinkObjId="g_38d0f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193026_0" Pin1InfoVect1LinkObjId="SW-193027_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_053Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-9 4781,-9 4781,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-90 4761,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29233@x" ObjectIDND1="29234@x" ObjectIDZND0="g_38d0f40@0" ObjectIDZND1="18106@x" Pin0InfoVect0LinkObjId="g_38d0f40_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193026_0" Pin1InfoVect1LinkObjId="SW-193027_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-90 4761,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d81b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-9 4761,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_38d0f40@0" ObjectIDND1="29233@x" ObjectIDND2="29234@x" ObjectIDZND0="18106@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38d0f40_0" Pin1InfoVect1LinkObjId="SW-193026_0" Pin1InfoVect2LinkObjId="SW-193027_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-9 4761,75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38d8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-40 4799,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29234@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193027_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-40 4799,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38df260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4818,-325 4818,-367 4764,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_38df4c0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38df4c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4818,-325 4818,-367 4764,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38dff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4781,-335 4761,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29231@1" ObjectIDZND0="29229@x" ObjectIDZND1="29204@x" Pin0InfoVect0LinkObjId="SW-193022_0" Pin0InfoVect1LinkObjId="SW-193021_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193024_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4781,-335 4761,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e29b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-123 4761,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29233@0" ObjectIDZND0="g_38d0f40@0" ObjectIDZND1="18106@x" ObjectIDZND2="29234@x" Pin0InfoVect0LinkObjId="g_38d0f40_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_053Ld_0" Pin0InfoVect2LinkObjId="SW-193027_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193026_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-123 4761,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38e7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-280 4761,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29204@0" ObjectIDZND0="29230@1" Pin0InfoVect0LinkObjId="SW-193023_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-280 4761,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-89 4663,-89 4663,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_38e97b0@0" ObjectIDND1="18105@x" ObjectIDND2="29239@x" ObjectIDZND0="29240@1" Pin0InfoVect0LinkObjId="SW-193036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38e97b0_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_054Ld_0" Pin1InfoVect2LinkObjId="SW-193035_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-89 4663,-89 4663,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f0930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-8 4645,-8 4645,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="29239@x" ObjectIDND1="29240@x" ObjectIDND2="18105@x" ObjectIDZND0="g_38e97b0@0" Pin0InfoVect0LinkObjId="g_38e97b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193035_0" Pin1InfoVect1LinkObjId="SW-193036_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_054Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-8 4645,-8 4645,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f0b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-89 4625,-8 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29239@x" ObjectIDND1="29240@x" ObjectIDZND0="g_38e97b0@0" ObjectIDZND1="18105@x" Pin0InfoVect0LinkObjId="g_38e97b0_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193035_0" Pin1InfoVect1LinkObjId="SW-193036_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-89 4625,-8 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f0d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-8 4625,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_38e97b0@0" ObjectIDND1="29239@x" ObjectIDND2="29240@x" ObjectIDZND0="18105@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38e97b0_0" Pin1InfoVect1LinkObjId="SW-193035_0" Pin1InfoVect2LinkObjId="SW-193036_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-8 4625,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f1560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-39 4663,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29240@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-39 4663,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f7f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-408 4625,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29235@1" Pin0InfoVect0LinkObjId="SW-193031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-408 4625,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_38f81f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4682,-324 4682,-366 4628,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_38f8450@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f8450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4682,-324 4682,-366 4628,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f8ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4645,-335 4625,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29237@1" ObjectIDZND0="29235@x" ObjectIDZND1="29205@x" Pin0InfoVect0LinkObjId="SW-193031_0" Pin0InfoVect1LinkObjId="SW-193030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193033_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4645,-335 4625,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f9140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-347 4625,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29235@0" ObjectIDZND0="29205@x" ObjectIDZND1="29237@x" Pin0InfoVect0LinkObjId="SW-193030_0" Pin0InfoVect1LinkObjId="SW-193033_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193031_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-347 4625,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38f93a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-335 4625,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29235@x" ObjectIDND1="29237@x" ObjectIDZND0="29205@1" Pin0InfoVect0LinkObjId="SW-193030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193031_0" Pin1InfoVect1LinkObjId="SW-193033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-335 4625,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_38fbe00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-122 4625,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29239@0" ObjectIDZND0="g_38e97b0@0" ObjectIDZND1="18105@x" ObjectIDZND2="29240@x" Pin0InfoVect0LinkObjId="g_38e97b0_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_054Ld_0" Pin0InfoVect2LinkObjId="SW-193036_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-122 4625,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39112a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-279 4625,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29205@0" ObjectIDZND0="29236@1" Pin0InfoVect0LinkObjId="SW-193032_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-279 4625,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3919d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-90 4522,-90 4522,-76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29245@x" ObjectIDND1="g_39134c0@0" ObjectIDND2="18104@x" ObjectIDZND0="29246@1" Pin0InfoVect0LinkObjId="SW-193045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193044_0" Pin1InfoVect1LinkObjId="g_39134c0_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_055Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-90 4522,-90 4522,-76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_391a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-40 4522,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29246@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-40 4522,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3920db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-408 4484,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29241@1" Pin0InfoVect0LinkObjId="SW-193040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-408 4484,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3921010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4541,-325 4541,-367 4487,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3921270@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3921270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4541,-325 4541,-367 4487,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3921d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4505,-336 4484,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29243@1" ObjectIDZND0="29241@x" ObjectIDZND1="29206@x" Pin0InfoVect0LinkObjId="SW-193040_0" Pin0InfoVect1LinkObjId="SW-193039_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193042_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4505,-336 4484,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3921f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-348 4484,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29241@0" ObjectIDZND0="29206@x" ObjectIDZND1="29243@x" Pin0InfoVect0LinkObjId="SW-193039_0" Pin0InfoVect1LinkObjId="SW-193042_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-348 4484,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39221c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-336 4484,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29241@x" ObjectIDND1="29243@x" ObjectIDZND0="29206@1" Pin0InfoVect0LinkObjId="SW-193039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193040_0" Pin1InfoVect1LinkObjId="SW-193042_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-336 4484,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3924c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-123 4484,-90 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29245@0" ObjectIDZND0="29246@x" ObjectIDZND1="g_39134c0@0" ObjectIDZND2="18104@x" Pin0InfoVect0LinkObjId="SW-193045_0" Pin0InfoVect1LinkObjId="g_39134c0_0" Pin0InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_055Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-123 4484,-90 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39296f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-280 4484,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29206@0" ObjectIDZND0="29242@1" Pin0InfoVect0LinkObjId="SW-193041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-280 4484,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3932600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-5 4185,-5 4185,13 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18103@x" ObjectIDND1="29250@x" ObjectIDND2="29251@x" ObjectIDZND0="g_392ba20@0" Pin0InfoVect0LinkObjId="g_392ba20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_061Ld_0" Pin1InfoVect1LinkObjId="SW-193052_0" Pin1InfoVect2LinkObjId="SW-193053_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-5 4185,-5 4185,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39327f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-5 4165,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_392ba20@0" ObjectIDND1="29250@x" ObjectIDND2="29251@x" ObjectIDZND0="18103@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_061Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_392ba20_0" Pin1InfoVect1LinkObjId="SW-193052_0" Pin1InfoVect2LinkObjId="SW-193053_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-5 4165,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3932f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-39 4204,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29251@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193053_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-39 4204,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39398b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-409 4165,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29311@1" Pin0InfoVect0LinkObjId="SW-193216_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-409 4165,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3939b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4222,-321 4222,-363 4168,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3939d70@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3939d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4222,-321 4222,-363 4168,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_393a800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4184,-332 4165,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29248@1" ObjectIDZND0="29311@x" ObjectIDZND1="29207@x" Pin0InfoVect0LinkObjId="SW-193216_0" Pin0InfoVect1LinkObjId="SW-193048_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4184,-332 4165,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_393aa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-344 4165,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29311@0" ObjectIDZND0="29207@x" ObjectIDZND1="29248@x" Pin0InfoVect0LinkObjId="SW-193048_0" Pin0InfoVect1LinkObjId="SW-193050_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-344 4165,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_393acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-332 4165,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29311@x" ObjectIDND1="29248@x" ObjectIDZND0="29207@1" Pin0InfoVect0LinkObjId="SW-193048_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193216_0" Pin1InfoVect1LinkObjId="SW-193050_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-332 4165,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3941f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-276 4165,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29207@0" ObjectIDZND0="29247@1" Pin0InfoVect0LinkObjId="SW-193049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-276 4165,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39499d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-409 4761,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29229@1" Pin0InfoVect0LinkObjId="SW-193022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-409 4761,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3953380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-409 3882,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29213@1" Pin0InfoVect0LinkObjId="SW-193220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-409 3882,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39535e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3935,-321 3935,-363 3881,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3953840@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3953840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3935,-321 3935,-363 3881,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39542d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-332 3882,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29272@1" ObjectIDZND0="29213@x" ObjectIDZND1="29208@x" Pin0InfoVect0LinkObjId="SW-193220_0" Pin0InfoVect1LinkObjId="SW-193219_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193089_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3898,-332 3882,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3954530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-344 3882,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29213@0" ObjectIDZND0="29208@x" ObjectIDZND1="29272@x" Pin0InfoVect0LinkObjId="SW-193219_0" Pin0InfoVect1LinkObjId="SW-193089_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-344 3882,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3954790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-332 3882,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29213@x" ObjectIDND1="29272@x" ObjectIDZND0="29208@1" Pin0InfoVect0LinkObjId="SW-193219_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193220_0" Pin1InfoVect1LinkObjId="SW-193089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-332 3882,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_395ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-276 3882,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29208@0" ObjectIDZND0="29271@1" Pin0InfoVect0LinkObjId="SW-193088_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193219_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-276 3882,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3967b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-409 4012,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29266@1" Pin0InfoVect0LinkObjId="SW-193077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-409 4012,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3968830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-330 4012,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29268@1" ObjectIDZND0="29266@x" ObjectIDZND1="29210@x" Pin0InfoVect0LinkObjId="SW-193077_0" Pin0InfoVect1LinkObjId="SW-193076_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-330 4012,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3968a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-342 4012,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29266@0" ObjectIDZND0="29210@x" ObjectIDZND1="29268@x" Pin0InfoVect0LinkObjId="SW-193076_0" Pin0InfoVect1LinkObjId="SW-193079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-342 4012,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3968cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-330 4012,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29266@x" ObjectIDND1="29268@x" ObjectIDZND0="29210@1" Pin0InfoVect0LinkObjId="SW-193076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193077_0" Pin1InfoVect1LinkObjId="SW-193079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-330 4012,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_396ffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-274 4012,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29210@0" ObjectIDZND0="29267@1" Pin0InfoVect0LinkObjId="SW-193078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-274 4012,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3970ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-91 4064,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="29270@x" ObjectIDND1="32061@0" ObjectIDZND0="g_3971a60@0" Pin0InfoVect0LinkObjId="g_3971a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193081_0" Pin1InfoVect1LinkObjId="g_29c89b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-91 4064,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39715a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-117 4012,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="29270@0" ObjectIDZND0="g_3971a60@0" ObjectIDZND1="32061@0" Pin0InfoVect0LinkObjId="g_3971a60_0" Pin0InfoVect1LinkObjId="g_29c89b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-117 4012,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3971800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-91 4012,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_3971a60@0" ObjectIDND1="29270@x" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3971a60_0" Pin1InfoVect1LinkObjId="SW-193081_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-91 4012,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3972810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-90 4484,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29245@x" ObjectIDND1="29246@x" ObjectIDZND0="g_39134c0@0" ObjectIDZND1="18104@x" Pin0InfoVect0LinkObjId="g_39134c0_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_055Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193044_0" Pin1InfoVect1LinkObjId="SW-193045_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-90 4484,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_397f6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-409 3734,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29260@1" Pin0InfoVect0LinkObjId="SW-193068_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-409 3734,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_397f940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3791,-325 3791,-364 3737,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_397fba0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_397fba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3791,-325 3791,-364 3737,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3980630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-335 3734,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29262@1" ObjectIDZND0="29260@x" ObjectIDZND1="29209@x" Pin0InfoVect0LinkObjId="SW-193068_0" Pin0InfoVect1LinkObjId="SW-192959_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-335 3734,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3980890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-346 3734,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29260@0" ObjectIDZND0="29209@x" ObjectIDZND1="29262@x" Pin0InfoVect0LinkObjId="SW-192959_0" Pin0InfoVect1LinkObjId="SW-193070_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-346 3734,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3980af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-335 3734,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29260@x" ObjectIDND1="29262@x" ObjectIDZND0="29209@1" Pin0InfoVect0LinkObjId="SW-192959_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193068_0" Pin1InfoVect1LinkObjId="SW-193070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-335 3734,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3987dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-282 3734,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29209@0" ObjectIDZND0="29261@1" Pin0InfoVect0LinkObjId="SW-193069_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192959_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-282 3734,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_398f030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-8 3616,-8 3616,10 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="18101@x" ObjectIDND1="29259@x" ObjectIDND2="29258@x" ObjectIDZND0="g_398b210@0" Pin0InfoVect0LinkObjId="g_398b210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_065Ld_0" Pin1InfoVect1LinkObjId="SW-193065_0" Pin1InfoVect2LinkObjId="SW-193064_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-8 3616,-8 3616,10 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_398f220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-8 3596,76 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_398b210@0" ObjectIDND1="29259@x" ObjectIDND2="29258@x" ObjectIDZND0="18101@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_398b210_0" Pin1InfoVect1LinkObjId="SW-193065_0" Pin1InfoVect2LinkObjId="SW-193064_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-8 3596,76 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39960c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-409 3596,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29254@1" Pin0InfoVect0LinkObjId="SW-193060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-409 3596,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3996320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3653,-324 3653,-366 3599,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_3996580@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3996580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3653,-324 3653,-366 3599,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3997010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3616,-335 3596,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29256@1" ObjectIDZND0="29254@x" ObjectIDZND1="29214@x" Pin0InfoVect0LinkObjId="SW-193060_0" Pin0InfoVect1LinkObjId="SW-192955_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3616,-335 3596,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3997270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-347 3596,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29254@0" ObjectIDZND0="29214@x" ObjectIDZND1="29256@x" Pin0InfoVect0LinkObjId="SW-192955_0" Pin0InfoVect1LinkObjId="SW-193062_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-347 3596,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39974d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-335 3596,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29254@x" ObjectIDND1="29256@x" ObjectIDZND0="29214@1" Pin0InfoVect0LinkObjId="SW-192955_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193060_0" Pin1InfoVect1LinkObjId="SW-193062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-335 3596,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_399e7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-279 3596,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29214@0" ObjectIDZND0="29255@1" Pin0InfoVect0LinkObjId="SW-193061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192955_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-279 3596,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a7790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-409 3465,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29188@1" Pin0InfoVect0LinkObjId="SW-192786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-409 3465,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39a79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3522,-324 3522,-366 3468,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_39a7c50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39a7c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3522,-324 3522,-366 3468,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a86e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3485,-335 3465,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29252@1" ObjectIDZND0="29188@x" ObjectIDZND1="29187@x" Pin0InfoVect0LinkObjId="SW-192786_0" Pin0InfoVect1LinkObjId="SW-192785_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193056_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3485,-335 3465,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-347 3465,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29188@0" ObjectIDZND0="29187@x" ObjectIDZND1="29252@x" Pin0InfoVect0LinkObjId="SW-192785_0" Pin0InfoVect1LinkObjId="SW-193056_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-347 3465,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39a8ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-335 3465,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29188@x" ObjectIDND1="29252@x" ObjectIDZND0="29187@1" Pin0InfoVect0LinkObjId="SW-192785_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192786_0" Pin1InfoVect1LinkObjId="SW-193056_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-335 3465,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ab600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-279 3465,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29187@0" ObjectIDZND0="29189@1" Pin0InfoVect0LinkObjId="SW-192787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192785_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-279 3465,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ac2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3488,-113 3465,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="29253@1" ObjectIDZND0="29189@x" ObjectIDZND1="g_39b2880@0" Pin0InfoVect0LinkObjId="SW-192787_0" Pin0InfoVect1LinkObjId="g_39b2880_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193057_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3488,-113 3465,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39aed50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3525,-101 3525,-143 3469,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_39ab860@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39ab860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3525,-101 3525,-143 3469,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39aefb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,-113 3465,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29253@x" ObjectIDND1="g_39b2880@0" ObjectIDZND0="29189@0" Pin0InfoVect0LinkObjId="SW-192787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193057_0" Pin1InfoVect1LinkObjId="g_39b2880_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,-113 3465,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b32a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,76 3465,55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="18100@0" ObjectIDZND0="g_39b2880@0" Pin0InfoVect0LinkObjId="g_39b2880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_066Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3465,76 3465,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b3500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3465,3 3465,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_39b2880@1" ObjectIDZND0="29189@x" ObjectIDZND1="29253@x" Pin0InfoVect0LinkObjId="SW-192787_0" Pin0InfoVect1LinkObjId="SW-193057_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39b2880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3465,3 3465,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b4ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4511,-408 4511,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29297@0" Pin0InfoVect0LinkObjId="SW-93614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4511,-408 4511,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b9d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-542 4550,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="29297@x" ObjectIDND1="g_3a2f160@0" ObjectIDZND0="29302@0" Pin0InfoVect0LinkObjId="SW-193141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93614_0" Pin1InfoVect1LinkObjId="g_3a2f160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-542 4550,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39b9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-562 4512,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a2f160@0" ObjectIDZND0="29302@x" ObjectIDZND1="29297@x" Pin0InfoVect0LinkObjId="SW-193141_0" Pin0InfoVect1LinkObjId="SW-93614_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a2f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-562 4512,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ba1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-542 4512,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="29302@x" ObjectIDND1="g_3a2f160@0" ObjectIDZND0="29297@1" Pin0InfoVect0LinkObjId="SW-93614_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193141_0" Pin1InfoVect1LinkObjId="g_3a2f160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-542 4512,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ba430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-542 4615,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29302@1" ObjectIDZND0="g_39ba690@0" Pin0InfoVect0LinkObjId="g_39ba690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193141_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-542 4615,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39bb120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4615,-559 4615,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_39ba690@0" ObjectIDZND0="29302@x" Pin0InfoVect0LinkObjId="SW-193141_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39ba690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4615,-559 4615,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39bd4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-409 3646,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29303@0" Pin0InfoVect0LinkObjId="SW-193142_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-409 3646,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c2460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-508 3692,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_3a2abc0@0" ObjectIDND1="29303@x" ObjectIDZND0="29304@0" Pin0InfoVect0LinkObjId="SW-193143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3a2abc0_0" Pin1InfoVect1LinkObjId="SW-193142_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-508 3692,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c26c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-525 3646,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_3a2abc0@0" ObjectIDZND0="29304@x" ObjectIDZND1="29303@x" Pin0InfoVect0LinkObjId="SW-193143_0" Pin0InfoVect1LinkObjId="SW-193142_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3a2abc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-525 3646,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c2920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3646,-508 3646,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="29304@x" ObjectIDND1="g_3a2abc0@0" ObjectIDZND0="29303@1" Pin0InfoVect0LinkObjId="SW-193142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193143_0" Pin1InfoVect1LinkObjId="g_3a2abc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3646,-508 3646,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c2b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3728,-508 3748,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29304@1" ObjectIDZND0="g_39c2de0@0" Pin0InfoVect0LinkObjId="g_39c2de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193143_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3728,-508 3748,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c3870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-527 3748,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_39c2de0@0" ObjectIDZND0="29304@x" Pin0InfoVect0LinkObjId="SW-193143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39c2de0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-527 3748,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39c5270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-409 3857,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29299@0" Pin0InfoVect0LinkObjId="SW-192963_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-409 3857,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ca2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-494 3897,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29299@x" ObjectIDND1="g_39cea70@0" ObjectIDZND0="29300@0" Pin0InfoVect0LinkObjId="SW-93629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192963_0" Pin1InfoVect1LinkObjId="g_39cea70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-494 3897,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ca540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-494 3857,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="29300@x" ObjectIDND1="g_39cea70@0" ObjectIDZND0="29299@1" Pin0InfoVect0LinkObjId="SW-192963_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-93629_0" Pin1InfoVect1LinkObjId="g_39cea70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-494 3857,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39ca7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3933,-494 3959,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29300@1" ObjectIDZND0="g_39caa00@0" Pin0InfoVect0LinkObjId="g_39caa00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-93629_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3933,-494 3959,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39cb490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3959,-513 3959,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_39caa00@0" ObjectIDZND0="29300@x" Pin0InfoVect0LinkObjId="SW-93629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39caa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3959,-513 3959,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-103 4204,-103 4204,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_392ba20@0" ObjectIDND1="18103@x" ObjectIDND2="29250@x" ObjectIDZND0="29251@1" Pin0InfoVect0LinkObjId="SW-193053_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_392ba20_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_061Ld_0" Pin1InfoVect2LinkObjId="SW-193052_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-103 4204,-103 4204,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d1f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-5 4165,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_392ba20@0" ObjectIDND1="18103@x" ObjectIDZND0="29250@x" ObjectIDZND1="29251@x" Pin0InfoVect0LinkObjId="SW-193052_0" Pin0InfoVect1LinkObjId="SW-193053_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_392ba20_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_061Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-5 4165,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d21c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-103 4165,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_392ba20@0" ObjectIDND1="18103@x" ObjectIDND2="29251@x" ObjectIDZND0="29250@0" Pin0InfoVect0LinkObjId="SW-193052_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_392ba20_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_061Ld_0" Pin1InfoVect2LinkObjId="SW-193053_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-103 4165,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3774,-38 3774,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29265@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3774,-38 3774,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d7480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3635,-38 3635,-19 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29259@0" ObjectIDZND0="32061@0" Pin0InfoVect0LinkObjId="g_29c89b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193065_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3635,-38 3635,-19 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d76e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-102 3635,-102 3635,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_398b210@0" ObjectIDND1="18101@x" ObjectIDND2="29258@x" ObjectIDZND0="29259@1" Pin0InfoVect0LinkObjId="SW-193065_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_398b210_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_065Ld_0" Pin1InfoVect2LinkObjId="SW-193064_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-102 3635,-102 3635,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d87a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-8 3596,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_398b210@0" ObjectIDND1="18101@x" ObjectIDZND0="29259@x" ObjectIDZND1="29258@x" Pin0InfoVect0LinkObjId="SW-193065_0" Pin0InfoVect1LinkObjId="SW-193064_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_398b210_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_065Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-8 3596,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d8a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-102 3596,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29259@x" ObjectIDND1="g_398b210@0" ObjectIDND2="18101@x" ObjectIDZND0="29258@0" Pin0InfoVect0LinkObjId="SW-193064_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193065_0" Pin1InfoVect1LinkObjId="g_398b210_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_065Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-102 3596,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d8c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3653,-468 3748,-468 3748,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="29304@x" ObjectIDZND1="g_39c2de0@0" Pin0InfoVect0LinkObjId="SW-193143_0" Pin0InfoVect1LinkObjId="g_39c2de0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3653,-468 3748,-468 3748,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3864,-454 3959,-454 3959,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="29300@x" ObjectIDZND1="g_39caa00@0" Pin0InfoVect0LinkObjId="SW-93629_0" Pin0InfoVect1LinkObjId="g_39caa00_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3864,-454 3959,-454 3959,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d9120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4732,-453 4827,-453 4827,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="29180@x" ObjectIDZND1="g_38a7300@0" Pin0InfoVect0LinkObjId="SW-192754_0" Pin0InfoVect1LinkObjId="g_38a7300_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4732,-453 4827,-453 4827,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39d9380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4520,-500 4615,-500 4615,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="earth" ObjectIDZND0="29302@x" ObjectIDZND1="g_39ba690@0" Pin0InfoVect0LinkObjId="SW-193141_0" Pin0InfoVect1LinkObjId="g_39ba690_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4520,-500 4615,-500 4615,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dbde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-409 4277,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17484@0" ObjectIDZND0="29275@1" Pin0InfoVect0LinkObjId="SW-193096_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a1e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-409 4277,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39dc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-318 4334,-360 4280,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_39dc2a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39dc2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-318 4334,-360 4280,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dcd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4297,-329 4277,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29313@1" ObjectIDZND0="29275@x" ObjectIDZND1="29211@x" Pin0InfoVect0LinkObjId="SW-193096_0" Pin0InfoVect1LinkObjId="SW-193094_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193218_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4297,-329 4277,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dcf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-341 4277,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29275@0" ObjectIDZND0="29211@x" ObjectIDZND1="29313@x" Pin0InfoVect0LinkObjId="SW-193094_0" Pin0InfoVect1LinkObjId="SW-193218_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193096_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-341 4277,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39dd1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-329 4277,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29275@x" ObjectIDND1="29313@x" ObjectIDZND0="29211@1" Pin0InfoVect0LinkObjId="SW-193094_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193096_0" Pin1InfoVect1LinkObjId="SW-193218_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-329 4277,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-273 4277,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29211@0" ObjectIDZND0="29276@1" Pin0InfoVect0LinkObjId="SW-193097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193094_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-273 4277,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39e9880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4388,-409 4388,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17456@0" ObjectIDZND0="29274@1" Pin0InfoVect0LinkObjId="SW-193095_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a7ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4388,-409 4388,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_39e9ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-318 4445,-360 4391,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" ObjectIDND0="g_39e9d40@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39e9d40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-318 4445,-360 4391,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f3ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4109,-454 4204,-454 4204,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" EndDevType1="switch" ObjectIDZND0="g_38a13b0@0" ObjectIDZND1="29185@x" Pin0InfoVect0LinkObjId="g_38a13b0_0" Pin0InfoVect1LinkObjId="SW-192762_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4109,-454 4204,-454 4204,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f4610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-513 4204,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a13b0@0" ObjectIDZND0="29185@x" Pin0InfoVect0LinkObjId="SW-192762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a13b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-513 4204,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39f4800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4204,-496 4178,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_38a13b0@0" ObjectIDZND0="29185@1" Pin0InfoVect0LinkObjId="SW-192762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38a13b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4204,-496 4178,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39fc4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4768,-636 4724,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="29181@0" ObjectIDZND0="g_389b600@0" ObjectIDZND1="29179@x" ObjectIDZND2="11572@x" Pin0InfoVect0LinkObjId="g_389b600_0" Pin0InfoVect1LinkObjId="SW-192753_0" Pin0InfoVect2LinkObjId="g_3a48af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4768,-636 4724,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39fd020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4676,-636 4724,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer2" ObjectIDND0="g_389b600@0" ObjectIDZND0="29181@x" ObjectIDZND1="29179@x" ObjectIDZND2="11572@x" Pin0InfoVect0LinkObjId="SW-192755_0" Pin0InfoVect1LinkObjId="SW-192753_0" Pin0InfoVect2LinkObjId="g_3a48af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_389b600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4676,-636 4724,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_39fd240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-619 4724,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer2" ObjectIDND0="29179@0" ObjectIDZND0="29181@x" ObjectIDZND1="g_389b600@0" ObjectIDZND2="11572@x" Pin0InfoVect0LinkObjId="SW-192755_0" Pin0InfoVect1LinkObjId="g_389b600_0" Pin0InfoVect2LinkObjId="g_3a48af0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-619 4724,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a00740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-348 4761,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29229@0" ObjectIDZND0="29231@x" ObjectIDZND1="29204@x" Pin0InfoVect0LinkObjId="SW-193024_0" Pin0InfoVect1LinkObjId="SW-193021_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-348 4761,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a00930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-335 4761,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29231@x" ObjectIDND1="29229@x" ObjectIDZND0="29204@1" Pin0InfoVect0LinkObjId="SW-193021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193024_0" Pin1InfoVect1LinkObjId="SW-193022_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-335 4761,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a03720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3565,-208 3596,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3992b10@0" ObjectIDZND0="29255@x" ObjectIDZND1="29258@x" ObjectIDZND2="29257@x" Pin0InfoVect0LinkObjId="SW-193061_0" Pin0InfoVect1LinkObjId="SW-193064_0" Pin0InfoVect2LinkObjId="SW-193063_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3992b10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3565,-208 3596,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a04280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-227 3596,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29255@0" ObjectIDZND0="29258@x" ObjectIDZND1="g_3992b10@0" ObjectIDZND2="29257@x" Pin0InfoVect0LinkObjId="SW-193064_0" Pin0InfoVect1LinkObjId="g_3992b10_0" Pin0InfoVect2LinkObjId="SW-193063_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-227 3596,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a04470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3596,-208 3596,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29255@x" ObjectIDND1="g_3992b10@0" ObjectIDND2="29257@x" ObjectIDZND0="29258@1" Pin0InfoVect0LinkObjId="SW-193064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193061_0" Pin1InfoVect1LinkObjId="g_3992b10_0" Pin1InfoVect2LinkObjId="SW-193063_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3596,-208 3596,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a04680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3629,-208 3596,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29257@0" ObjectIDZND0="29255@x" ObjectIDZND1="29258@x" ObjectIDZND2="g_3992b10@0" Pin0InfoVect0LinkObjId="SW-193061_0" Pin0InfoVect1LinkObjId="SW-193064_0" Pin0InfoVect2LinkObjId="g_3992b10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3629,-208 3596,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a048e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-215 3734,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_397c170@0" ObjectIDZND0="29261@x" ObjectIDZND1="29264@x" ObjectIDZND2="29263@x" Pin0InfoVect0LinkObjId="SW-193069_0" Pin0InfoVect1LinkObjId="SW-193072_0" Pin0InfoVect2LinkObjId="SW-193071_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_397c170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-215 3734,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a055d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-233 3734,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29261@0" ObjectIDZND0="29264@x" ObjectIDZND1="g_397c170@0" ObjectIDZND2="29263@x" Pin0InfoVect0LinkObjId="SW-193072_0" Pin0InfoVect1LinkObjId="g_397c170_0" Pin0InfoVect2LinkObjId="SW-193071_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193069_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-233 3734,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a05810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-215 3734,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29261@x" ObjectIDND1="g_397c170@0" ObjectIDND2="29263@x" ObjectIDZND0="29264@1" Pin0InfoVect0LinkObjId="SW-193072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193069_0" Pin1InfoVect1LinkObjId="g_397c170_0" Pin1InfoVect2LinkObjId="SW-193071_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-215 3734,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a05a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3767,-215 3733,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29263@0" ObjectIDZND0="29261@x" ObjectIDZND1="29264@x" ObjectIDZND2="g_397c170@0" Pin0InfoVect0LinkObjId="SW-193069_0" Pin0InfoVect1LinkObjId="SW-193072_0" Pin0InfoVect2LinkObjId="g_397c170_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193071_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3767,-215 3733,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a076a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-205 3882,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_394fe10@0" ObjectIDZND0="29271@x" ObjectIDZND1="29273@x" ObjectIDZND2="40098@x" Pin0InfoVect0LinkObjId="SW-193088_0" Pin0InfoVect1LinkObjId="SW-193090_0" Pin0InfoVect2LinkObjId="CB-LF_YJ.LF_YJ_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_394fe10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-205 3882,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a08200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-224 3882,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="29271@0" ObjectIDZND0="g_394fe10@0" ObjectIDZND1="29273@x" ObjectIDZND2="40098@x" Pin0InfoVect0LinkObjId="g_394fe10_0" Pin0InfoVect1LinkObjId="SW-193090_0" Pin0InfoVect2LinkObjId="CB-LF_YJ.LF_YJ_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-224 3882,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a083f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3915,-205 3882,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="29273@0" ObjectIDZND0="29271@x" ObjectIDZND1="g_394fe10@0" ObjectIDZND2="40098@x" Pin0InfoVect0LinkObjId="SW-193088_0" Pin0InfoVect1LinkObjId="g_394fe10_0" Pin0InfoVect2LinkObjId="CB-LF_YJ.LF_YJ_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3915,-205 3882,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a09170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-203 4012,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_39645d0@0" ObjectIDZND0="29267@x" ObjectIDZND1="29270@x" ObjectIDZND2="29269@x" Pin0InfoVect0LinkObjId="SW-193078_0" Pin0InfoVect1LinkObjId="SW-193081_0" Pin0InfoVect2LinkObjId="SW-193080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39645d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-203 4012,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a09cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-222 4012,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29267@0" ObjectIDZND0="29270@x" ObjectIDZND1="g_39645d0@0" ObjectIDZND2="29269@x" Pin0InfoVect0LinkObjId="SW-193081_0" Pin0InfoVect1LinkObjId="g_39645d0_0" Pin0InfoVect2LinkObjId="SW-193080_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-222 4012,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a09ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4012,-203 4012,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29267@x" ObjectIDND1="g_39645d0@0" ObjectIDND2="29269@x" ObjectIDZND0="29270@1" Pin0InfoVect0LinkObjId="SW-193081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193078_0" Pin1InfoVect1LinkObjId="g_39645d0_0" Pin1InfoVect2LinkObjId="SW-193080_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4012,-203 4012,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0a0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-203 4012,-203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29269@0" ObjectIDZND0="29267@x" ObjectIDZND1="29270@x" ObjectIDZND2="g_39645d0@0" Pin0InfoVect0LinkObjId="SW-193078_0" Pin0InfoVect1LinkObjId="SW-193081_0" Pin0InfoVect2LinkObjId="g_39645d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4051,-203 4012,-203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0aea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-205 4165,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3936300@0" ObjectIDZND0="29247@x" ObjectIDZND1="29250@x" ObjectIDZND2="29249@x" Pin0InfoVect0LinkObjId="SW-193049_0" Pin0InfoVect1LinkObjId="SW-193052_0" Pin0InfoVect2LinkObjId="SW-193051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3936300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-205 4165,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0ba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-224 4165,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29247@0" ObjectIDZND0="g_3936300@0" ObjectIDZND1="29250@x" ObjectIDZND2="29249@x" Pin0InfoVect0LinkObjId="g_3936300_0" Pin0InfoVect1LinkObjId="SW-193052_0" Pin0InfoVect2LinkObjId="SW-193051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-224 4165,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0bbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4165,-205 4165,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3936300@0" ObjectIDND1="29247@x" ObjectIDND2="29249@x" ObjectIDZND0="29250@1" Pin0InfoVect0LinkObjId="SW-193052_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3936300_0" Pin1InfoVect1LinkObjId="SW-193049_0" Pin1InfoVect2LinkObjId="SW-193051_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4165,-205 4165,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-205 4165,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29249@0" ObjectIDZND0="g_3936300@0" ObjectIDZND1="29247@x" ObjectIDZND2="29250@x" Pin0InfoVect0LinkObjId="g_3936300_0" Pin0InfoVect1LinkObjId="SW-193049_0" Pin0InfoVect2LinkObjId="SW-193052_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193051_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-205 4165,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4408,-329 4389,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29312@1" ObjectIDZND0="29274@x" ObjectIDZND1="29276@x" Pin0InfoVect0LinkObjId="SW-193095_0" Pin0InfoVect1LinkObjId="SW-193097_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193217_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4408,-329 4389,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0d540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4389,-342 4389,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29274@0" ObjectIDZND0="29312@x" ObjectIDZND1="29276@x" Pin0InfoVect0LinkObjId="SW-193217_0" Pin0InfoVect1LinkObjId="SW-193097_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193095_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4389,-342 4389,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0d730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4389,-329 4389,-193 4277,-193 4277,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29312@x" ObjectIDND1="29274@x" ObjectIDZND0="29276@0" Pin0InfoVect0LinkObjId="SW-193097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193217_0" Pin1InfoVect1LinkObjId="SW-193095_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4389,-329 4389,-193 4277,-193 4277,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4456,-209 4484,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_391d800@0" ObjectIDZND0="29242@x" ObjectIDZND1="29245@x" ObjectIDZND2="29244@x" Pin0InfoVect0LinkObjId="SW-193041_0" Pin0InfoVect1LinkObjId="SW-193044_0" Pin0InfoVect2LinkObjId="SW-193043_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_391d800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4456,-209 4484,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0f230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-228 4484,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29242@0" ObjectIDZND0="29245@x" ObjectIDZND1="g_391d800@0" ObjectIDZND2="29244@x" Pin0InfoVect0LinkObjId="SW-193044_0" Pin0InfoVect1LinkObjId="g_391d800_0" Pin0InfoVect2LinkObjId="SW-193043_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-228 4484,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0f420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-209 4484,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29242@x" ObjectIDND1="g_391d800@0" ObjectIDND2="29244@x" ObjectIDZND0="29245@1" Pin0InfoVect0LinkObjId="SW-193044_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193041_0" Pin1InfoVect1LinkObjId="g_391d800_0" Pin1InfoVect2LinkObjId="SW-193043_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-209 4484,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0f630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4512,-209 4483,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29244@0" ObjectIDZND0="29242@x" ObjectIDZND1="29245@x" ObjectIDZND2="g_391d800@0" Pin0InfoVect0LinkObjId="SW-193041_0" Pin0InfoVect1LinkObjId="SW-193044_0" Pin0InfoVect2LinkObjId="g_391d800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193043_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4512,-209 4483,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a0f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4597,-208 4625,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38f49e0@0" ObjectIDZND0="29236@x" ObjectIDZND1="29239@x" ObjectIDZND2="29238@x" Pin0InfoVect0LinkObjId="SW-193032_0" Pin0InfoVect1LinkObjId="SW-193035_0" Pin0InfoVect2LinkObjId="SW-193034_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38f49e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4597,-208 4625,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a10580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-227 4625,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29236@0" ObjectIDZND0="29239@x" ObjectIDZND1="g_38f49e0@0" ObjectIDZND2="29238@x" Pin0InfoVect0LinkObjId="SW-193035_0" Pin0InfoVect1LinkObjId="g_38f49e0_0" Pin0InfoVect2LinkObjId="SW-193034_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193032_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-227 4625,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a107c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-208 4625,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29236@x" ObjectIDND1="g_38f49e0@0" ObjectIDND2="29238@x" ObjectIDZND0="29239@1" Pin0InfoVect0LinkObjId="SW-193035_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193032_0" Pin1InfoVect1LinkObjId="g_38f49e0_0" Pin1InfoVect2LinkObjId="SW-193034_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-208 4625,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a10a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4653,-208 4625,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29238@0" ObjectIDZND0="29236@x" ObjectIDZND1="29239@x" ObjectIDZND2="g_38f49e0@0" Pin0InfoVect0LinkObjId="SW-193032_0" Pin0InfoVect1LinkObjId="SW-193035_0" Pin0InfoVect2LinkObjId="g_38f49e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4653,-208 4625,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a10c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4733,-209 4761,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38dbcb0@0" ObjectIDZND0="29230@x" ObjectIDZND1="29233@x" ObjectIDZND2="29232@x" Pin0InfoVect0LinkObjId="SW-193023_0" Pin0InfoVect1LinkObjId="SW-193026_0" Pin0InfoVect2LinkObjId="SW-193025_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38dbcb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4733,-209 4761,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a11940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-228 4761,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29230@0" ObjectIDZND0="29233@x" ObjectIDZND1="g_38dbcb0@0" ObjectIDZND2="29232@x" Pin0InfoVect0LinkObjId="SW-193026_0" Pin0InfoVect1LinkObjId="g_38dbcb0_0" Pin0InfoVect2LinkObjId="SW-193025_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193023_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-228 4761,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a11ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-209 4761,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29230@x" ObjectIDND1="g_38dbcb0@0" ObjectIDND2="29232@x" ObjectIDZND0="29233@1" Pin0InfoVect0LinkObjId="SW-193026_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193023_0" Pin1InfoVect1LinkObjId="g_38dbcb0_0" Pin1InfoVect2LinkObjId="SW-193025_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-209 4761,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a11e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-209 4761,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29232@0" ObjectIDZND0="29230@x" ObjectIDZND1="29233@x" ObjectIDZND2="g_38dbcb0@0" Pin0InfoVect0LinkObjId="SW-193023_0" Pin0InfoVect1LinkObjId="SW-193026_0" Pin0InfoVect2LinkObjId="g_38dbcb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-209 4761,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a12060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4879,-208 4907,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38c2d30@0" ObjectIDZND0="29224@x" ObjectIDZND1="29227@x" ObjectIDZND2="29226@x" Pin0InfoVect0LinkObjId="SW-193014_0" Pin0InfoVect1LinkObjId="SW-193017_0" Pin0InfoVect2LinkObjId="SW-193016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38c2d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4879,-208 4907,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a12d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-227 4907,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="29224@0" ObjectIDZND0="29227@x" ObjectIDZND1="g_38c2d30@0" ObjectIDZND2="29226@x" Pin0InfoVect0LinkObjId="SW-193017_0" Pin0InfoVect1LinkObjId="g_38c2d30_0" Pin0InfoVect2LinkObjId="SW-193016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-227 4907,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a12f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4907,-208 4907,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="29224@x" ObjectIDND1="g_38c2d30@0" ObjectIDND2="29226@x" ObjectIDZND0="29227@1" Pin0InfoVect0LinkObjId="SW-193017_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193014_0" Pin1InfoVect1LinkObjId="g_38c2d30_0" Pin1InfoVect2LinkObjId="SW-193016_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4907,-208 4907,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a131e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4935,-208 4907,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="29226@0" ObjectIDZND0="29224@x" ObjectIDZND1="29227@x" ObjectIDZND2="g_38c2d30@0" Pin0InfoVect0LinkObjId="SW-193014_0" Pin0InfoVect1LinkObjId="SW-193017_0" Pin0InfoVect2LinkObjId="g_38c2d30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193016_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4935,-208 4907,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a17f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-134 3734,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="29264@0" ObjectIDZND0="29265@x" ObjectIDZND1="g_3975410@0" ObjectIDZND2="18102@x" Pin0InfoVect0LinkObjId="SW-193073_0" Pin0InfoVect1LinkObjId="g_3975410_0" Pin0InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-134 3734,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a18150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-103 3774,-103 3774,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="29264@x" ObjectIDND1="g_3975410@0" ObjectIDND2="18102@x" ObjectIDZND0="29265@1" Pin0InfoVect0LinkObjId="SW-193073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193072_0" Pin1InfoVect1LinkObjId="g_3975410_0" Pin1InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_064Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-103 3774,-103 3774,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a18390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3754,-9 3754,-30 3734,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_3975410@0" ObjectIDZND0="29264@x" ObjectIDZND1="29265@x" ObjectIDZND2="18102@x" Pin0InfoVect0LinkObjId="SW-193072_0" Pin0InfoVect1LinkObjId="SW-193073_0" Pin0InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_064Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3975410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3754,-9 3754,-30 3734,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a18e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-104 3734,-30 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="29264@x" ObjectIDND1="29265@x" ObjectIDZND0="g_3975410@0" ObjectIDZND1="18102@x" Pin0InfoVect0LinkObjId="g_3975410_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_064Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193072_0" Pin1InfoVect1LinkObjId="SW-193073_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-104 3734,-30 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a190c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-30 3734,68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_3975410@0" ObjectIDND1="29264@x" ObjectIDND2="29265@x" ObjectIDZND0="18102@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3975410_0" Pin1InfoVect1LinkObjId="SW-193072_0" Pin1InfoVect2LinkObjId="SW-193073_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3734,-30 3734,68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a19b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,9 4504,-9 4484,-9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="g_39134c0@0" ObjectIDZND0="29245@x" ObjectIDZND1="29246@x" ObjectIDZND2="18104@x" Pin0InfoVect0LinkObjId="SW-193044_0" Pin0InfoVect1LinkObjId="SW-193045_0" Pin0InfoVect2LinkObjId="EC-LF_YJ.LD_YJ_055Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39134c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4504,9 4504,-9 4484,-9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a19dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4484,-9 4484,75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="29245@x" ObjectIDND1="29246@x" ObjectIDND2="g_39134c0@0" ObjectIDZND0="18104@0" Pin0InfoVect0LinkObjId="EC-LF_YJ.LD_YJ_055Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193044_0" Pin1InfoVect1LinkObjId="SW-193045_0" Pin1InfoVect2LinkObjId="g_39134c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4484,-9 4484,75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a1fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-8 5048,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_385ea50@0" ObjectIDND1="34015@x" ObjectIDZND0="29222@x" ObjectIDZND1="29221@x" Pin0InfoVect0LinkObjId="SW-193009_0" Pin0InfoVect1LinkObjId="SW-193008_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_385ea50_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_051Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-8 5048,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a200c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-89 5086,-89 5086,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_385ea50@0" ObjectIDND1="34015@x" ObjectIDND2="29221@x" ObjectIDZND0="29222@1" Pin0InfoVect0LinkObjId="SW-193009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_385ea50_0" Pin1InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_051Ld_0" Pin1InfoVect2LinkObjId="SW-193008_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-89 5086,-89 5086,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a202b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-122 5048,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="29221@0" ObjectIDZND0="g_385ea50@0" ObjectIDZND1="34015@x" ObjectIDZND2="29222@x" Pin0InfoVect0LinkObjId="g_385ea50_0" Pin0InfoVect1LinkObjId="EC-LF_YJ.LD_YJ_051Ld_0" Pin0InfoVect2LinkObjId="SW-193009_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-122 5048,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a204c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-263 5048,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29218@1" ObjectIDZND0="29202@0" Pin0InfoVect0LinkObjId="SW-193003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-263 5048,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a206f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-383 5048,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="11599@1" ObjectIDZND0="17456@0" Pin0InfoVect0LinkObjId="g_38a7ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-383 5048,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a20f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5020,-208 5048,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38abbe0@0" ObjectIDZND0="29218@x" ObjectIDZND1="29221@x" ObjectIDZND2="29220@x" Pin0InfoVect0LinkObjId="SW-193005_0" Pin0InfoVect1LinkObjId="SW-193008_0" Pin0InfoVect2LinkObjId="SW-193007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38abbe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5020,-208 5048,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a21bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-227 5048,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29218@0" ObjectIDZND0="g_38abbe0@0" ObjectIDZND1="29221@x" ObjectIDZND2="29220@x" Pin0InfoVect0LinkObjId="g_38abbe0_0" Pin0InfoVect1LinkObjId="SW-193008_0" Pin0InfoVect2LinkObjId="SW-193007_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193005_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-227 5048,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a21e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-208 5048,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_38abbe0@0" ObjectIDND1="29218@x" ObjectIDND2="29220@x" ObjectIDZND0="29221@1" Pin0InfoVect0LinkObjId="SW-193008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38abbe0_0" Pin1InfoVect1LinkObjId="SW-193005_0" Pin1InfoVect2LinkObjId="SW-193007_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-208 5048,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a22090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5076,-208 5048,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="29220@0" ObjectIDZND0="g_38abbe0@0" ObjectIDZND1="29218@x" ObjectIDZND2="29221@x" Pin0InfoVect0LinkObjId="g_38abbe0_0" Pin0InfoVect1LinkObjId="SW-193005_0" Pin0InfoVect2LinkObjId="SW-193008_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193007_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5076,-208 5048,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a228a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-335 5048,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29219@1" ObjectIDZND0="29202@x" ObjectIDZND1="11599@x" Pin0InfoVect0LinkObjId="SW-193003_0" Pin0InfoVect1LinkObjId="SW-193004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193006_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-335 5048,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a23350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-306 5048,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29202@1" ObjectIDZND0="29219@x" ObjectIDZND1="11599@x" Pin0InfoVect0LinkObjId="SW-193006_0" Pin0InfoVect1LinkObjId="SW-193004_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-306 5048,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a235b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-335 5048,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29219@x" ObjectIDND1="29202@x" ObjectIDZND0="11599@0" Pin0InfoVect0LinkObjId="SW-193004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193006_0" Pin1InfoVect1LinkObjId="SW-193003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-335 5048,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a243f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4068,-330 4068,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29268@0" ObjectIDZND0="g_3967da0@0" Pin0InfoVect0LinkObjId="g_3967da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4068,-330 4068,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a35cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1238 4266,-1285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="28988@x" ObjectIDND2="28986@x" ObjectIDZND0="g_3884100@0" ObjectIDZND1="18071@1" Pin0InfoVect0LinkObjId="g_3884100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="SW-192972_0" Pin1InfoVect2LinkObjId="SW-192969_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1238 4266,-1285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a35f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-1309 4266,-1285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="18071@1" ObjectIDZND0="g_3884100@0" ObjectIDZND1="0@x" ObjectIDZND2="28988@x" Pin0InfoVect0LinkObjId="g_3884100_0" Pin0InfoVect1LinkObjId="g_387f940_0" Pin0InfoVect2LinkObjId="SW-192972_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-1309 4266,-1285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a36fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-471 4105,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29183@1" ObjectIDZND0="29185@x" ObjectIDZND1="29182@x" Pin0InfoVect0LinkObjId="SW-192762_0" Pin0InfoVect1LinkObjId="SW-192759_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192760_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-471 4105,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a37230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-496 4142,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29183@x" ObjectIDND1="29182@x" ObjectIDZND0="29185@0" Pin0InfoVect0LinkObjId="SW-192762_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192760_0" Pin1InfoVect1LinkObjId="SW-192759_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-496 4142,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a37490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-614 4105,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29184@1" ObjectIDZND0="29182@1" Pin0InfoVect0LinkObjId="SW-192759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192761_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-614 4105,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a376f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-562 4105,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29182@0" ObjectIDZND0="29183@x" ObjectIDZND1="29185@x" Pin0InfoVect0LinkObjId="SW-192760_0" Pin0InfoVect1LinkObjId="SW-192762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-562 4105,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a37f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-551 3857,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_39cea70@0" Pin0InfoVect0LinkObjId="g_39cea70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_387f940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-551 3857,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a38180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-504 3857,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_39cea70@1" ObjectIDZND0="29300@x" ObjectIDZND1="29299@x" Pin0InfoVect0LinkObjId="SW-93629_0" Pin0InfoVect1LinkObjId="SW-192963_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_39cea70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-504 3857,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a3af60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4762,-495 4724,-495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29180@0" ObjectIDZND0="29178@x" ObjectIDZND1="29177@x" Pin0InfoVect0LinkObjId="SW-192752_0" Pin0InfoVect1LinkObjId="SW-192751_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4762,-495 4724,-495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a3b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-495 4724,-470 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29180@x" ObjectIDND1="29177@x" ObjectIDZND0="29178@1" Pin0InfoVect0LinkObjId="SW-192752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192754_0" Pin1InfoVect1LinkObjId="SW-192751_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-495 4724,-470 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a3b400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-495 4724,-532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29180@x" ObjectIDND1="29178@x" ObjectIDZND0="29177@0" Pin0InfoVect0LinkObjId="SW-192751_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192754_0" Pin1InfoVect1LinkObjId="SW-192752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-495 4724,-532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a3b660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-559 4724,-583 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29177@1" ObjectIDZND0="29179@1" Pin0InfoVect0LinkObjId="SW-192753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192751_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-559 4724,-583 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a3b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-661 4053,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="29186@0" ObjectIDZND0="g_3896f90@0" Pin0InfoVect0LinkObjId="g_3896f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-661 4053,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a3ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-825 3909,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_38595c0@0" ObjectIDZND0="g_385a370@0" ObjectIDZND1="11598@x" ObjectIDZND2="38177@x" Pin0InfoVect0LinkObjId="g_385a370_0" Pin0InfoVect1LinkObjId="SW-229381_0" Pin0InfoVect2LinkObjId="SW-229382_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_38595c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-825 3909,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a3d8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-825 3909,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_38595c0@0" ObjectIDND1="11598@x" ObjectIDND2="38177@x" ObjectIDZND0="g_385a370@1" Pin0InfoVect0LinkObjId="g_385a370_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_38595c0_0" Pin1InfoVect1LinkObjId="SW-229381_0" Pin1InfoVect2LinkObjId="SW-229382_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-825 3909,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a40150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-959 3909,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29486@0" ObjectIDZND0="11598@x" ObjectIDZND1="38178@x" Pin0InfoVect0LinkObjId="SW-229381_0" Pin0InfoVect1LinkObjId="SW-229383_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-959 3909,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a403b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-943 3909,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29486@0" ObjectIDND1="38178@x" ObjectIDZND0="11598@1" Pin0InfoVect0LinkObjId="SW-229381_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-229383_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-943 3909,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a40ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-888 3909,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="11598@0" ObjectIDZND0="g_38595c0@0" ObjectIDZND1="g_385a370@0" ObjectIDZND2="38177@x" Pin0InfoVect0LinkObjId="g_38595c0_0" Pin0InfoVect1LinkObjId="g_385a370_0" Pin0InfoVect2LinkObjId="SW-229382_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229381_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-888 3909,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a41100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-861 3909,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11598@x" ObjectIDND1="38177@x" ObjectIDZND0="g_38595c0@0" ObjectIDZND1="g_385a370@0" Pin0InfoVect0LinkObjId="g_38595c0_0" Pin0InfoVect1LinkObjId="g_385a370_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-229381_0" Pin1InfoVect1LinkObjId="SW-229382_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-861 3909,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a44db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-943 3893,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29486@0" ObjectIDND1="11598@x" ObjectIDZND0="38178@1" Pin0InfoVect0LinkObjId="SW-229383_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-229381_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-943 3893,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a45010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-943 3844,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38178@0" ObjectIDZND0="g_3a41df0@0" Pin0InfoVect0LinkObjId="g_3a41df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229383_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-943 3844,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a477a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-861 3892,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="11598@x" ObjectIDND1="g_38595c0@0" ObjectIDND2="g_385a370@0" ObjectIDZND0="38177@1" Pin0InfoVect0LinkObjId="SW-229382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-229381_0" Pin1InfoVect1LinkObjId="g_38595c0_0" Pin1InfoVect2LinkObjId="g_385a370_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-861 3892,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a47a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3856,-861 3843,-861 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38177@0" ObjectIDZND0="g_3a41360@0" Pin0InfoVect0LinkObjId="g_3a41360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229382_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3856,-861 3843,-861 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a48710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4266,-959 4266,-1028 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29486@0" ObjectIDZND0="11595@0" Pin0InfoVect0LinkObjId="SW-191786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4266,-959 4266,-1028 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a48900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-650 4105,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="29184@0" ObjectIDZND0="11574@1" Pin0InfoVect0LinkObjId="g_3a52020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-650 4105,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3a48af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-636 4724,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="transformer2" ObjectIDND0="29181@x" ObjectIDND1="g_389b600@0" ObjectIDND2="29179@x" ObjectIDZND0="11572@1" Pin0InfoVect0LinkObjId="g_3a52210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-192755_0" Pin1InfoVect1LinkObjId="g_389b600_0" Pin1InfoVect2LinkObjId="SW-192753_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-636 4724,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a4b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-887 4118,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29201@x" ObjectIDND1="29301@x" ObjectIDZND0="38180@0" Pin0InfoVect0LinkObjId="SW-229380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192994_0" Pin1InfoVect1LinkObjId="SW-229379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-887 4118,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a4bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4154,-887 4173,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38180@1" ObjectIDZND0="g_3a4e770@0" Pin0InfoVect0LinkObjId="g_3a4e770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4154,-887 4173,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a4e2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-885 4736,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29216@x" ObjectIDND1="19730@x" ObjectIDZND0="38179@0" Pin0InfoVect0LinkObjId="SW-229378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-229377_0" Pin1InfoVect1LinkObjId="SW-192982_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-885 4736,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a4e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-885 4785,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38179@1" ObjectIDZND0="g_3a4f200@0" Pin0InfoVect0LinkObjId="g_3a4f200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-885 4785,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a50920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-904 4105,-887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29301@0" ObjectIDZND0="38180@x" ObjectIDZND1="29201@x" Pin0InfoVect0LinkObjId="SW-229380_0" Pin0InfoVect1LinkObjId="SW-192994_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-904 4105,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a50b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-887 4105,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="38180@x" ObjectIDND1="29301@x" ObjectIDZND0="29201@1" Pin0InfoVect0LinkObjId="SW-192994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-229380_0" Pin1InfoVect1LinkObjId="SW-229379_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-887 4105,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a51670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-907 4725,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="29216@0" ObjectIDZND0="38179@x" ObjectIDZND1="19730@x" Pin0InfoVect0LinkObjId="SW-229378_0" Pin0InfoVect1LinkObjId="SW-192982_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-229377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-907 4725,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a518d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-885 4725,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="38179@x" ObjectIDND1="29216@x" ObjectIDZND0="19730@1" Pin0InfoVect0LinkObjId="SW-192982_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-229378_0" Pin1InfoVect1LinkObjId="SW-229377_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-885 4725,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a52020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-844 4105,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="29201@0" ObjectIDZND0="11574@0" Pin0InfoVect0LinkObjId="g_3a48900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-844 4105,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a52210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4725,-836 4725,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="19730@0" ObjectIDZND0="11572@0" Pin0InfoVect0LinkObjId="g_3a48af0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192982_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4725,-836 4725,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_35add60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-205 3882,23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="29271@x" ObjectIDND1="g_394fe10@0" ObjectIDND2="29273@x" ObjectIDZND0="40098@0" Pin0InfoVect0LinkObjId="CB-LF_YJ.LF_YJ_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-193088_0" Pin1InfoVect1LinkObjId="g_394fe10_0" Pin1InfoVect2LinkObjId="SW-193090_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3882,-205 3882,23 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52536" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3308.000000 -1082.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9246" ObjectName="DYN-LF_YJ"/>
     <cge:Meas_Ref ObjectId="52536"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a00c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 469.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a02300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4983.500000 484.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a028a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.000000 499.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a02de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4976.000000 454.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3a1d7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 1077.000000) translate(0,18)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3a1e860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3750.000000 1007.000000) translate(0,18)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3a1ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 1030.000000) translate(0,18)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3a1f4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.500000 1053.000000) translate(0,18)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a24a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 1109.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a257c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4144.500000 1124.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a26350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 1139.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a26ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 839.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a26f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.500000 854.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a271c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 869.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a274f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 550.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a27750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4772.500000 565.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a27990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4784.000000 580.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a27cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 564.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a27f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3986.500000 579.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a28160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 594.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a28490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 852.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a286f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.500000 867.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a28930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 882.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a28c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3410.000000 -178.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a28ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3384.500000 -163.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a29100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3396.000000 -148.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a67b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3545.000000 -178.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a6af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3519.500000 -163.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a6d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3531.000000 -148.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 -180.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a72c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.500000 -165.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3665.000000 -150.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -179.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3828.500000 -164.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a7cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3840.000000 -149.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 -178.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.500000 -163.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a84a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4124.000000 -148.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a87d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4449.000000 -178.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4423.500000 -163.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -148.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a8fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4589.000000 -179.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4563.500000 -164.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -149.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -179.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a99d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.500000 -164.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -149.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a9f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.000000 -179.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aa1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.500000 -164.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aa3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4856.000000 -149.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aa710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5010.000000 -179.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aa970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4984.500000 -164.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35aabb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -149.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b1a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3364.000000 521.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b1ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3364.000000 535.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b2230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3356.000000 504.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b2470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3364.000000 550.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b26b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3357.000000 486.000000) translate(0,12)">Ubc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35b28f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3357.000000 466.000000) translate(0,12)">Uca(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_QF" endPointId="0" endStationName="LF_YJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_YangJie" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4266,-1307 4266,-1328 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18071" ObjectName="AC-35kV.LN_YangJie"/>
    <cge:TPSR_Ref TObjectID="18071_SS-87"/></metadata>
   <polyline fill="none" opacity="0" points="4266,-1307 4266,-1328 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="29486" cx="3909" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4625" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4484" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4945" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4799" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4663" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4522" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4204" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="4012" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4511" cy="-408" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="3635" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4388" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="5086" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4907" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="4165" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3882" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="4012" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3734" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3596" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3465" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="4277" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="5048" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32061" cx="3774" cy="-19" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4761" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29486" cx="3721" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3646" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="4105" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17484" cx="3857" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17456" cx="4724" cy="-409" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29486" cx="4725" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29486" cx="4266" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29486" cx="4105" cy="-959" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-229379">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4095.713355 -897.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29301" ObjectName="SW-LF_YJ.LF_YJ_3021SW"/>
     <cge:Meas_Ref ObjectId="229379"/>
    <cge:TPSR_Ref TObjectID="29301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-191786">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.995657 -1023.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11595" ObjectName="SW-LF_YJ.LF_YJ_3811SW"/>
     <cge:Meas_Ref ObjectId="191786"/>
    <cge:TPSR_Ref TObjectID="11595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229381">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3899.807818 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11598" ObjectName="SW-LF_YJ.LF_YJ_3901SW"/>
     <cge:Meas_Ref ObjectId="229381"/>
    <cge:TPSR_Ref TObjectID="11598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192970">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.995657 -1002.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28987" ObjectName="SW-LF_YJ.LF_YJ_38117SW"/>
     <cge:Meas_Ref ObjectId="192970"/>
    <cge:TPSR_Ref TObjectID="28987"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193009">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29222" ObjectName="SW-LF_YJ.LF_YJ_0515SW"/>
     <cge:Meas_Ref ObjectId="193009"/>
    <cge:TPSR_Ref TObjectID="29222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193008">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29221" ObjectName="SW-LF_YJ.LF_YJ_0516SW"/>
     <cge:Meas_Ref ObjectId="193008"/>
    <cge:TPSR_Ref TObjectID="29221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229377">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.895765 -902.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29216" ObjectName="SW-LF_YJ.LF_YJ_3011SW"/>
     <cge:Meas_Ref ObjectId="229377"/>
    <cge:TPSR_Ref TObjectID="29216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192971">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.995657 -1158.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28989" ObjectName="SW-LF_YJ.LF_YJ_38160SW"/>
     <cge:Meas_Ref ObjectId="192971"/>
    <cge:TPSR_Ref TObjectID="28989"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4307.995657 -1233.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192972">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.995657 -1154.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28988" ObjectName="SW-LF_YJ.LF_YJ_38167SW"/>
     <cge:Meas_Ref ObjectId="192972"/>
    <cge:TPSR_Ref TObjectID="28988"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.995657 -1169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28986" ObjectName="SW-LF_YJ.LF_YJ_3816SW"/>
     <cge:Meas_Ref ObjectId="192969"/>
    <cge:TPSR_Ref TObjectID="28986"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4687.895765 -578.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29179" ObjectName="SW-LF_YJ.LF_YJ_0016SW"/>
     <cge:Meas_Ref ObjectId="192753"/>
    <cge:TPSR_Ref TObjectID="29179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.713355 -609.000000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29184" ObjectName="SW-LF_YJ.LF_YJ_0026SW"/>
     <cge:Meas_Ref ObjectId="192761"/>
    <cge:TPSR_Ref TObjectID="29184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4154.000000 -712.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29186" ObjectName="SW-LF_YJ.LF_YJ_00267SW"/>
     <cge:Meas_Ref ObjectId="192763"/>
    <cge:TPSR_Ref TObjectID="29186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4777.000000 -687.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29181" ObjectName="SW-LF_YJ.LF_YJ_00167SW"/>
     <cge:Meas_Ref ObjectId="192755"/>
    <cge:TPSR_Ref TObjectID="29181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29183" ObjectName="SW-LF_YJ.LF_YJ_0022SW"/>
     <cge:Meas_Ref ObjectId="192760"/>
    <cge:TPSR_Ref TObjectID="29183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4137.000000 -505.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29185" ObjectName="SW-LF_YJ.LF_YJ_00227SW"/>
     <cge:Meas_Ref ObjectId="192762"/>
    <cge:TPSR_Ref TObjectID="29185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 -429.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29178" ObjectName="SW-LF_YJ.LF_YJ_0011SW"/>
     <cge:Meas_Ref ObjectId="192752"/>
    <cge:TPSR_Ref TObjectID="29178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4760.000000 -504.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29180" ObjectName="SW-LF_YJ.LF_YJ_00117SW"/>
     <cge:Meas_Ref ObjectId="192754"/>
    <cge:TPSR_Ref TObjectID="29180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193007">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5067.000000 -157.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29220" ObjectName="SW-LF_YJ.LF_YJ_05127SW"/>
     <cge:Meas_Ref ObjectId="193007"/>
    <cge:TPSR_Ref TObjectID="29220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193004">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5039.000000 -342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11599" ObjectName="SW-LF_YJ.LF_YJ_0511SW"/>
     <cge:Meas_Ref ObjectId="193004"/>
    <cge:TPSR_Ref TObjectID="11599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193006">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5109.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29219" ObjectName="SW-LF_YJ.LF_YJ_05117SW"/>
     <cge:Meas_Ref ObjectId="193006"/>
    <cge:TPSR_Ref TObjectID="29219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193005">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 5084.500000 -268.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29218" ObjectName="SW-LF_YJ.LF_YJ_0512SW"/>
     <cge:Meas_Ref ObjectId="193005"/>
    <cge:TPSR_Ref TObjectID="29218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29228" ObjectName="SW-LF_YJ.LF_YJ_0525SW"/>
     <cge:Meas_Ref ObjectId="193018"/>
    <cge:TPSR_Ref TObjectID="29228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29227" ObjectName="SW-LF_YJ.LF_YJ_0526SW"/>
     <cge:Meas_Ref ObjectId="193017"/>
    <cge:TPSR_Ref TObjectID="29227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4926.000000 -157.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29226" ObjectName="SW-LF_YJ.LF_YJ_05227SW"/>
     <cge:Meas_Ref ObjectId="193016"/>
    <cge:TPSR_Ref TObjectID="29226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193013">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4898.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29223" ObjectName="SW-LF_YJ.LF_YJ_0521SW"/>
     <cge:Meas_Ref ObjectId="193013"/>
    <cge:TPSR_Ref TObjectID="29223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4967.628000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29225" ObjectName="SW-LF_YJ.LF_YJ_05217SW"/>
     <cge:Meas_Ref ObjectId="193015"/>
    <cge:TPSR_Ref TObjectID="29225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4943.500000 -268.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29224" ObjectName="SW-LF_YJ.LF_YJ_0522SW"/>
     <cge:Meas_Ref ObjectId="193014"/>
    <cge:TPSR_Ref TObjectID="29224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193027">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4790.000000 -35.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29234" ObjectName="SW-LF_YJ.LF_YJ_0535SW"/>
     <cge:Meas_Ref ObjectId="193027"/>
    <cge:TPSR_Ref TObjectID="29234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193026">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29233" ObjectName="SW-LF_YJ.LF_YJ_0536SW"/>
     <cge:Meas_Ref ObjectId="193026"/>
    <cge:TPSR_Ref TObjectID="29233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193025">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4780.000000 -158.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29232" ObjectName="SW-LF_YJ.LF_YJ_05327SW"/>
     <cge:Meas_Ref ObjectId="193025"/>
    <cge:TPSR_Ref TObjectID="29232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193022">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29229" ObjectName="SW-LF_YJ.LF_YJ_0531SW"/>
     <cge:Meas_Ref ObjectId="193022"/>
    <cge:TPSR_Ref TObjectID="29229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193024">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4822.500000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29231" ObjectName="SW-LF_YJ.LF_YJ_05317SW"/>
     <cge:Meas_Ref ObjectId="193024"/>
    <cge:TPSR_Ref TObjectID="29231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193023">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4797.500000 -269.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29230" ObjectName="SW-LF_YJ.LF_YJ_0532SW"/>
     <cge:Meas_Ref ObjectId="193023"/>
    <cge:TPSR_Ref TObjectID="29230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29240" ObjectName="SW-LF_YJ.LF_YJ_0545SW"/>
     <cge:Meas_Ref ObjectId="193036"/>
    <cge:TPSR_Ref TObjectID="29240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29239" ObjectName="SW-LF_YJ.LF_YJ_0546SW"/>
     <cge:Meas_Ref ObjectId="193035"/>
    <cge:TPSR_Ref TObjectID="29239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193034">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4644.000000 -157.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29238" ObjectName="SW-LF_YJ.LF_YJ_05427SW"/>
     <cge:Meas_Ref ObjectId="193034"/>
    <cge:TPSR_Ref TObjectID="29238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193031">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29235" ObjectName="SW-LF_YJ.LF_YJ_0541SW"/>
     <cge:Meas_Ref ObjectId="193031"/>
    <cge:TPSR_Ref TObjectID="29235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4686.112000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29237" ObjectName="SW-LF_YJ.LF_YJ_05417SW"/>
     <cge:Meas_Ref ObjectId="193033"/>
    <cge:TPSR_Ref TObjectID="29237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4661.500000 -268.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29236" ObjectName="SW-LF_YJ.LF_YJ_0542SW"/>
     <cge:Meas_Ref ObjectId="193032"/>
    <cge:TPSR_Ref TObjectID="29236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193045">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -35.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29246" ObjectName="SW-LF_YJ.LF_YJ_0555SW"/>
     <cge:Meas_Ref ObjectId="193045"/>
    <cge:TPSR_Ref TObjectID="29246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193044">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29245" ObjectName="SW-LF_YJ.LF_YJ_0556SW"/>
     <cge:Meas_Ref ObjectId="193044"/>
    <cge:TPSR_Ref TObjectID="29245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193043">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4503.000000 -158.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29244" ObjectName="SW-LF_YJ.LF_YJ_05527SW"/>
     <cge:Meas_Ref ObjectId="193043"/>
    <cge:TPSR_Ref TObjectID="29244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193040">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4475.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29241" ObjectName="SW-LF_YJ.LF_YJ_0551SW"/>
     <cge:Meas_Ref ObjectId="193040"/>
    <cge:TPSR_Ref TObjectID="29241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193042">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4546.156000 -327.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29243" ObjectName="SW-LF_YJ.LF_YJ_05517SW"/>
     <cge:Meas_Ref ObjectId="193042"/>
    <cge:TPSR_Ref TObjectID="29243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4520.500000 -269.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29242" ObjectName="SW-LF_YJ.LF_YJ_0552SW"/>
     <cge:Meas_Ref ObjectId="193041"/>
    <cge:TPSR_Ref TObjectID="29242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193053">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -34.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29251" ObjectName="SW-LF_YJ.LF_YJ_0615SW"/>
     <cge:Meas_Ref ObjectId="193053"/>
    <cge:TPSR_Ref TObjectID="29251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193052">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -114.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29250" ObjectName="SW-LF_YJ.LF_YJ_0616SW"/>
     <cge:Meas_Ref ObjectId="193052"/>
    <cge:TPSR_Ref TObjectID="29250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193051">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4190.000000 -154.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29249" ObjectName="SW-LF_YJ.LF_YJ_06137SW"/>
     <cge:Meas_Ref ObjectId="193051"/>
    <cge:TPSR_Ref TObjectID="29249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193216">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29311" ObjectName="SW-LF_YJ.LF_YJ_0612SW"/>
     <cge:Meas_Ref ObjectId="193216"/>
    <cge:TPSR_Ref TObjectID="29311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193050">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4226.000000 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29248" ObjectName="SW-LF_YJ.LF_YJ_06127SW"/>
     <cge:Meas_Ref ObjectId="193050"/>
    <cge:TPSR_Ref TObjectID="29248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193049">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4201.500000 -265.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29247" ObjectName="SW-LF_YJ.LF_YJ_0613SW"/>
     <cge:Meas_Ref ObjectId="193049"/>
    <cge:TPSR_Ref TObjectID="29247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3906.000000 -154.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29273" ObjectName="SW-LF_YJ.LF_YJ_06367SW"/>
     <cge:Meas_Ref ObjectId="193090"/>
    <cge:TPSR_Ref TObjectID="29273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3873.000000 -339.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29213" ObjectName="SW-LF_YJ.LF_YJ_0632SW"/>
     <cge:Meas_Ref ObjectId="193220"/>
    <cge:TPSR_Ref TObjectID="29213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3939.000000 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29272" ObjectName="SW-LF_YJ.LF_YJ_06327SW"/>
     <cge:Meas_Ref ObjectId="193089"/>
    <cge:TPSR_Ref TObjectID="29272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3918.500000 -265.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29271" ObjectName="SW-LF_YJ.LF_YJ_0636SW"/>
     <cge:Meas_Ref ObjectId="193088"/>
    <cge:TPSR_Ref TObjectID="29271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29270" ObjectName="SW-LF_YJ.LF_YJ_0625SW"/>
     <cge:Meas_Ref ObjectId="193081"/>
    <cge:TPSR_Ref TObjectID="29270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4042.000000 -152.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29269" ObjectName="SW-LF_YJ.LF_YJ_06237SW"/>
     <cge:Meas_Ref ObjectId="193080"/>
    <cge:TPSR_Ref TObjectID="29269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4003.000000 -337.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29266" ObjectName="SW-LF_YJ.LF_YJ_0622SW"/>
     <cge:Meas_Ref ObjectId="193077"/>
    <cge:TPSR_Ref TObjectID="29266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193079">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4073.000000 -321.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29268" ObjectName="SW-LF_YJ.LF_YJ_06227SW"/>
     <cge:Meas_Ref ObjectId="193079"/>
    <cge:TPSR_Ref TObjectID="29268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4048.500000 -263.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29267" ObjectName="SW-LF_YJ.LF_YJ_0623SW"/>
     <cge:Meas_Ref ObjectId="193078"/>
    <cge:TPSR_Ref TObjectID="29267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193072">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.944444 3725.000000 -129.111111)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29264" ObjectName="SW-LF_YJ.LF_YJ_0646SW"/>
     <cge:Meas_Ref ObjectId="193072"/>
    <cge:TPSR_Ref TObjectID="29264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193071">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -0.944444 3758.000000 -166.888889)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29263" ObjectName="SW-LF_YJ.LF_YJ_06437SW"/>
     <cge:Meas_Ref ObjectId="193071"/>
    <cge:TPSR_Ref TObjectID="29263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193068">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.944444 3725.000000 -341.611111)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29260" ObjectName="SW-LF_YJ.LF_YJ_0642SW"/>
     <cge:Meas_Ref ObjectId="193068"/>
    <cge:TPSR_Ref TObjectID="29260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193070">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -0.944444 -1.000000 -0.000000 3795.000000 -326.500000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29262" ObjectName="SW-LF_YJ.LF_YJ_06427SW"/>
     <cge:Meas_Ref ObjectId="193070"/>
    <cge:TPSR_Ref TObjectID="29262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193069">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 0.944444 3770.500000 -272.194444)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29261" ObjectName="SW-LF_YJ.LF_YJ_0643SW"/>
     <cge:Meas_Ref ObjectId="193069"/>
    <cge:TPSR_Ref TObjectID="29261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193064">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29258" ObjectName="SW-LF_YJ.LF_YJ_0656SW"/>
     <cge:Meas_Ref ObjectId="193064"/>
    <cge:TPSR_Ref TObjectID="29258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3620.000000 -157.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29257" ObjectName="SW-LF_YJ.LF_YJ_06537SW"/>
     <cge:Meas_Ref ObjectId="193063"/>
    <cge:TPSR_Ref TObjectID="29257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3587.000000 -342.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29254" ObjectName="SW-LF_YJ.LF_YJ_0652SW"/>
     <cge:Meas_Ref ObjectId="193060"/>
    <cge:TPSR_Ref TObjectID="29254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3657.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29256" ObjectName="SW-LF_YJ.LF_YJ_06527SW"/>
     <cge:Meas_Ref ObjectId="193062"/>
    <cge:TPSR_Ref TObjectID="29256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3632.500000 -268.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29255" ObjectName="SW-LF_YJ.LF_YJ_0653SW"/>
     <cge:Meas_Ref ObjectId="193061"/>
    <cge:TPSR_Ref TObjectID="29255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 -118.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29189" ObjectName="SW-LF_YJ.LF_YJ_0666SW"/>
     <cge:Meas_Ref ObjectId="192787"/>
    <cge:TPSR_Ref TObjectID="29189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3456.000000 -342.333333)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29188" ObjectName="SW-LF_YJ.LF_YJ_0662SW"/>
     <cge:Meas_Ref ObjectId="192786"/>
    <cge:TPSR_Ref TObjectID="29188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193056">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3526.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29252" ObjectName="SW-LF_YJ.LF_YJ_06627SW"/>
     <cge:Meas_Ref ObjectId="193056"/>
    <cge:TPSR_Ref TObjectID="29252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3529.000000 -104.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29253" ObjectName="SW-LF_YJ.LF_YJ_06667SW"/>
     <cge:Meas_Ref ObjectId="193057"/>
    <cge:TPSR_Ref TObjectID="29253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93614">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -476.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29297" ObjectName="SW-LF_YJ.LF_YJ_0901SW"/>
     <cge:Meas_Ref ObjectId="93614"/>
    <cge:TPSR_Ref TObjectID="29297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193141">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4548.000000 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29302" ObjectName="SW-LF_YJ.LF_YJ_09017SW"/>
     <cge:Meas_Ref ObjectId="193141"/>
    <cge:TPSR_Ref TObjectID="29302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3637.000000 -442.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29303" ObjectName="SW-LF_YJ.LF_YJ_0902SW"/>
     <cge:Meas_Ref ObjectId="193142"/>
    <cge:TPSR_Ref TObjectID="29303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3687.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29304" ObjectName="SW-LF_YJ.LF_YJ_09027SW"/>
     <cge:Meas_Ref ObjectId="193143"/>
    <cge:TPSR_Ref TObjectID="29304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192963">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29299" ObjectName="SW-LF_YJ.LF_YJ_0672SW"/>
     <cge:Meas_Ref ObjectId="192963"/>
    <cge:TPSR_Ref TObjectID="29299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-93629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3892.000000 -503.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29300" ObjectName="SW-LF_YJ.LF_YJ_06727SW"/>
     <cge:Meas_Ref ObjectId="93629"/>
    <cge:TPSR_Ref TObjectID="29300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193073">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3765.000000 -33.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29265" ObjectName="SW-LF_YJ.LF_YJ_0645SW"/>
     <cge:Meas_Ref ObjectId="193073"/>
    <cge:TPSR_Ref TObjectID="29265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193065">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 -33.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29259" ObjectName="SW-LF_YJ.LF_YJ_0655SW"/>
     <cge:Meas_Ref ObjectId="193065"/>
    <cge:TPSR_Ref TObjectID="29259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193096">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29275" ObjectName="SW-LF_YJ.LF_YJ_0122SW"/>
     <cge:Meas_Ref ObjectId="193096"/>
    <cge:TPSR_Ref TObjectID="29275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4338.000000 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29313" ObjectName="SW-LF_YJ.LF_YJ_01227SW"/>
     <cge:Meas_Ref ObjectId="193218"/>
    <cge:TPSR_Ref TObjectID="29313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4313.500000 -260.500000)" xlink:href="#switch2:shape21_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29276" ObjectName="SW-LF_YJ.LF_YJ_0123SW"/>
     <cge:Meas_Ref ObjectId="193097"/>
    <cge:TPSR_Ref TObjectID="29276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193095">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4379.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29274" ObjectName="SW-LF_YJ.LF_YJ_0121SW"/>
     <cge:Meas_Ref ObjectId="193095"/>
    <cge:TPSR_Ref TObjectID="29274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193217">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4449.000000 -320.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29312" ObjectName="SW-LF_YJ.LF_YJ_01217SW"/>
     <cge:Meas_Ref ObjectId="193217"/>
    <cge:TPSR_Ref TObjectID="29312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229383">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3852.000000 -938.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38178" ObjectName="SW-LF_YJ.LF_YJ_39010SW"/>
     <cge:Meas_Ref ObjectId="229383"/>
    <cge:TPSR_Ref TObjectID="38178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229382">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3851.000000 -856.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38177" ObjectName="SW-LF_YJ.LF_YJ_39017SW"/>
     <cge:Meas_Ref ObjectId="229382"/>
    <cge:TPSR_Ref TObjectID="38177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229380">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -882.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38180" ObjectName="SW-LF_YJ.LF_YJ_30217SW"/>
     <cge:Meas_Ref ObjectId="229380"/>
    <cge:TPSR_Ref TObjectID="38180"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-229378">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 -880.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38179" ObjectName="SW-LF_YJ.LF_YJ_30117SW"/>
     <cge:Meas_Ref ObjectId="229378"/>
    <cge:TPSR_Ref TObjectID="38179"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3841910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3084.000000 -1040.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3845fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -711.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_3848e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3178.000000 -1183.500000) translate(0,16)">羊街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_384b2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4219.000000 -1350.000000) translate(0,18)">35kV羊街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3855bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3612.000000 -746.000000) translate(0,18)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385abf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3803.000000 -726.000000) translate(0,18)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.333333 19.000000) translate(0,18)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.333333 19.000000) translate(0,40)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385c470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.333333 19.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.333333 20.000000) translate(0,18)">勤</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.333333 20.000000) translate(0,40)">丰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_385f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4525.333333 20.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_298b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.333333 16.000000) translate(0,18)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_298b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.333333 16.000000) translate(0,40)">七</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_298b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.333333 16.000000) translate(0,62)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_298b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.333333 16.000000) translate(0,84)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3861170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.333333 17.000000) translate(0,18)">云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3861170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.333333 17.000000) translate(0,40)">铜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3861170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4799.333333 17.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.333333 17.000000) translate(0,18)">四</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.333333 17.000000) translate(0,40)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.333333 17.000000) translate(0,62)">七</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.333333 17.000000) translate(0,84)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38619f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5088.333333 17.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_386e290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5056.000000 -300.000000) translate(0,15)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5058.000000 -144.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_386fb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.713355 -928.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38700a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.713355 -584.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3870410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.995657 -1052.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3870b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5090.000000 -64.000000) translate(0,12)">0515</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_38756f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3222.000000 -361.000000) translate(0,17)">4840569</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_38756f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3222.000000 -361.000000) translate(0,38)">18887829206</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38a8710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.333333 16.000000) translate(0,18)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38a8710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.333333 16.000000) translate(0,40)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38a8710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.333333 16.000000) translate(0,62)">九</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_38a8710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.333333 16.000000) translate(0,84)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38be060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4917.000000 -302.000000) translate(0,15)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38be690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4913.000000 -144.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38be8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4948.000000 -64.000000) translate(0,12)">0525</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38d6cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -303.000000) translate(0,15)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d7450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4767.000000 -145.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38d79a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -65.500000) translate(0,12)">0535</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_38ef560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -302.000000) translate(0,15)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38efd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4631.000000 -144.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38f04d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -64.000000) translate(0,12)">0545</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3919270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4494.000000 -303.000000) translate(0,15)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39198a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -140.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3919ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4527.000000 -64.000000) translate(0,12)">0555</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39317d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4175.000000 -295.000000) translate(0,15)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3932150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4171.000000 -140.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39323c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4208.000000 -63.000000) translate(0,12)">0615</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_394c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3888.000000 -299.000000) translate(0,15)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3960e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4022.000000 -297.000000) translate(0,15)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3961450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4020.000000 -135.000000) translate(0,12)">0625</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3972a70" transform="matrix(1.000000 -0.000000 -0.000000 0.944444 3767.333333 16.666667) translate(0,18)">可</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3972a70" transform="matrix(1.000000 -0.000000 -0.000000 0.944444 3767.333333 16.666667) translate(0,40)">里</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3972a70" transform="matrix(1.000000 -0.000000 -0.000000 0.944444 3767.333333 16.666667) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39789c0" transform="matrix(1.000000 -0.000000 -0.000000 0.944444 3744.000000 -301.833333) translate(0,15)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3978ff0" transform="matrix(1.000000 -0.000000 -0.000000 0.944444 3739.000000 -154.611111) translate(0,12)">0646</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39888b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.333333 16.000000) translate(0,18)">华</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39888b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.333333 16.000000) translate(0,40)">家</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39888b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.333333 16.000000) translate(0,62)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_398e7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3606.000000 -302.000000) translate(0,15)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_398edf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -142.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39a43f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3475.000000 -302.000000) translate(0,15)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39a4a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3420.000000 -140.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b3760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.333333 33.000000) translate(0,18)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b3760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.333333 33.000000) translate(0,40)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b3760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.333333 33.000000) translate(0,62)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b3760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3922.333333 33.000000) translate(0,84)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b43c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.333333 -78.000000) translate(0,18)">旁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b43c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4021.333333 -78.000000) translate(0,40)">路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.333333 16.000000) translate(0,18)">云</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.333333 16.000000) translate(0,40)">铜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.333333 16.000000) translate(0,62)">铁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.333333 16.000000) translate(0,84)">峰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.333333 16.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39bb380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4549.000000 -567.000000) translate(0,15)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39bbae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4458.000000 -509.000000) translate(0,15)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39c3ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3685.000000 -535.000000) translate(0,15)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39c4100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3594.000000 -477.000000) translate(0,15)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39cb6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -521.000000) translate(0,15)">06727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39cbd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -463.000000) translate(0,15)">0672</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39cf2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4429.000000 -717.000000) translate(0,18)">10kV I 段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39d07e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3482.000000 -654.000000) translate(0,18)">10kV II 段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39d0a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -659.000000) translate(0,18)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_39f8e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4291.000000 -294.000000) translate(0,15)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fb050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4275.000000 -1122.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fb680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -1199.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fb8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -1189.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fbc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4372.000000 -1193.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fc140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4168.000000 -1032.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fd4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4114.000000 -860.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fd9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4111.000000 -638.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fdbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4156.000000 -691.000000) translate(0,12)">00267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fde20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4062.000000 -455.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39fe060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -522.000000) translate(0,12)">00227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39fe2a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3410.000000 -434.000000) translate(0,18)">10kVⅡ母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39fe7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4995.000000 -434.000000) translate(0,18)">10kVⅠ母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_39fedb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.000000 -987.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -855.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -932.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4738.000000 -553.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4731.000000 -608.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ff900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4779.000000 -666.000000) translate(0,12)">00167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ffb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -459.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ffd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -519.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a03060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3474.000000 -105.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a032a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3425.000000 -371.000000) translate(0,12)">0662</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a034e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3474.000000 -327.000000) translate(0,12)">06627</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a05cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3639.000000 -63.000000) translate(0,12)">0655</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a06260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3556.000000 -248.000000) translate(0,12)">0653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a064a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -191.000000) translate(0,12)">06537</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a066e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3555.000000 -372.000000) translate(0,12)">0652</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a06920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3604.000000 -328.000000) translate(0,12)">06527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a06b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3781.000000 -63.000000) translate(0,12)">0645</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a06da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3692.000000 -252.000000) translate(0,12)">0643</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a06fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3770.000000 -201.000000) translate(0,12)">06437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a07220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3695.000000 -370.000000) translate(0,12)">0642</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a07460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3741.000000 -327.000000) translate(0,12)">06427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.000000 -247.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3917.000000 -189.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3842.000000 -364.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a08f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3890.000000 -324.000000) translate(0,12)">06327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0a330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -246.000000) translate(0,12)">0623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0a7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4053.000000 -187.000000) translate(0,12)">06237</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3972.000000 -364.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0ac60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -320.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0c060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -248.000000) translate(0,12)">0613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0c510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -189.000000) translate(0,12)">06137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0c750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4124.000000 -369.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0c990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4173.000000 -322.000000) translate(0,12)">06127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0d920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4237.000000 -241.000000) translate(0,12)">0123</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0ddd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4239.000000 -362.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0e010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -320.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0e250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4347.000000 -366.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a0e490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -320.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a13440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4443.000000 -252.000000) translate(0,12)">0552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a13a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4516.000000 -193.000000) translate(0,12)">05527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a13cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4445.000000 -373.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a13ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -329.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a14130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4584.000000 -251.000000) translate(0,12)">0542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a14370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -192.000000) translate(0,12)">05427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a145b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -372.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a147f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4630.000000 -327.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a14a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -252.000000) translate(0,12)">0532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a14c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4798.000000 -193.000000) translate(0,12)">05327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a14eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4721.000000 -371.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a150f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4768.000000 -326.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a15330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -250.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a15570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4943.000000 -192.000000) translate(0,12)">05227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a157b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -374.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a159f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4913.000000 -328.000000) translate(0,12)">05217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a15c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5009.000000 -251.000000) translate(0,12)">0512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a15e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5085.000000 -192.000000) translate(0,12)">05127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a160b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5007.000000 -372.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a162f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5059.000000 -324.000000) translate(0,12)">05117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3a16530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3355.000000 -40.000000) translate(0,15)">10kV旁路母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3a1a9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3094.000000 -824.000000) translate(0,18)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3a1b940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3310.000000 -1165.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3a1cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3310.000000 -1200.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3a383e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4140.000000 -759.000000) translate(0,16)">SZ11-10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a3daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3679.713355 -915.000000) translate(0,12)">3802</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a47c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3916.000000 -913.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a48290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3853.000000 -935.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a484d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3851.000000 -855.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3a48ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4763.000000 -760.000000) translate(0,16)">SZ11-10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a51b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -884.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3a52400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -880.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a2c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -778.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a3aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -778.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a4440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -779.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a4da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3965.000000 -765.000000) translate(0,12)">油温：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a5d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -779.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_35a5f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -765.000000) translate(0,12)">油温：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35aaed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3071.000000 -313.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35aaed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3071.000000 -313.000000) translate(0,38)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_35ad690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3222.000000 -302.500000) translate(0,17)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_35b1080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3315.500000 -1019.000000) translate(0,16)">AVC</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_3868800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.995657 -980.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a13b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4198.000000 -508.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38a7300" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -507.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38c6790" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4970.000000 -329.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38df4c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4824.000000 -330.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_38f8450" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4688.000000 -329.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3921270" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4547.000000 -330.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3939d70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4228.000000 -326.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3953840" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3941.000000 -326.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3967da0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4074.000000 -324.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_397fba0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 0.944444 3797.000000 -330.333333)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3996580" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3659.000000 -329.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39a7c50" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3528.000000 -329.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39ab860" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3531.000000 -106.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39ba690" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4609.000000 -554.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39c2de0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3742.000000 -522.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39caa00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -508.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39dc2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4340.000000 -323.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_39e9d40" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4451.000000 -323.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a23b30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5099.000000 -305.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a41360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3825.000000 -855.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a41df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -937.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a4e770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4169.000000 -881.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3a4f200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -879.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_YJ"/>
</svg>