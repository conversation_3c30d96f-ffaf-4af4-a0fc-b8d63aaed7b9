<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-55" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="4 -2004 1117 1261">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="0.06"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape115">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="9" x2="13" y1="31" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0178798" x1="13" x2="13" y1="35" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0319731" x1="18" x2="13" y1="31" y2="35"/>
    <ellipse cx="13" cy="34" rx="12.5" ry="11.5" stroke-width="0.118558"/>
    <circle cx="13" cy="17" r="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="18" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="9" x2="13" y1="14" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="17" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape36">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="8" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.4375" x1="10" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.4375" x1="14" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.4375" x1="7" x2="7" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="7" y1="59" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="9" x2="7" y1="37" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="5" y1="34" y2="37"/>
    <rect height="24" stroke-width="0.75" width="8" x="3" y="27"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dbb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dbc790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbd180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbde20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbf050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbfcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc0820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dc1340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17381e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17381e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1dc73f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dc9be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dca9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dcb2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcc990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcd580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcde20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dce5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcf6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd0040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd0b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd14f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd2950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd3430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd4460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dd5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de3860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd6990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd7f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd94a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1271" width="1127" x="-1" y="-2009"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="5" y="-2003"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -1213.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8525" ObjectName="SW-CX_LHSL.CX_LHSL_011XC"/>
     <cge:Meas_Ref ObjectId="47288"/>
    <cge:TPSR_Ref TObjectID="8525"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47288">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -1136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8759" ObjectName="SW-CX_LHSL.CX_LHSL_011XC1"/>
     <cge:Meas_Ref ObjectId="47288"/>
    <cge:TPSR_Ref TObjectID="8759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 702.000000 -1064.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 618.000000 -1064.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -1213.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8530" ObjectName="SW-CX_LHSL.CX_LHSL_021XC"/>
     <cge:Meas_Ref ObjectId="47291"/>
    <cge:TPSR_Ref TObjectID="8530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47291">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -1136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8531" ObjectName="SW-CX_LHSL.CX_LHSL_021XC1"/>
     <cge:Meas_Ref ObjectId="47291"/>
    <cge:TPSR_Ref TObjectID="8531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.000000 -1064.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -1064.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -1211.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8528" ObjectName="SW-CX_LHSL.CX_LHSL_012XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.000000 -1135.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8529" ObjectName="SW-CX_LHSL.CX_LHSL_012XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8529"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 -1374.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8526" ObjectName="SW-CX_LHSL.CX_LHSL_001XC"/>
     <cge:Meas_Ref ObjectId="195747"/>
    <cge:TPSR_Ref TObjectID="8526"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 624.000000 -1287.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8527" ObjectName="SW-CX_LHSL.CX_LHSL_001XC1"/>
     <cge:Meas_Ref ObjectId="195747"/>
    <cge:TPSR_Ref TObjectID="8527"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -1373.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8534" ObjectName="SW-CX_LHSL.CX_LHSL_022XC"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -1288.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8535" ObjectName="SW-CX_LHSL.CX_LHSL_022XC1"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="8535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47296">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 -1500.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8538" ObjectName="SW-CX_LHSL.CX_LHSL_1511SW"/>
     <cge:Meas_Ref ObjectId="47296"/>
    <cge:TPSR_Ref TObjectID="8538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47298">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -1584.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8540" ObjectName="SW-CX_LHSL.CX_LHSL_15117SW"/>
     <cge:Meas_Ref ObjectId="47298"/>
    <cge:TPSR_Ref TObjectID="8540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47299">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 580.000000 -1661.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8541" ObjectName="SW-CX_LHSL.CX_LHSL_15167SW"/>
     <cge:Meas_Ref ObjectId="47299"/>
    <cge:TPSR_Ref TObjectID="8541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 455.000000 -1288.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47294">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 558.000000 -1409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8536" ObjectName="SW-CX_LHSL.CX_LHSL_1010SW"/>
     <cge:Meas_Ref ObjectId="47294"/>
    <cge:TPSR_Ref TObjectID="8536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47302">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 550.000000 -1764.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8544" ObjectName="SW-CX_LHSL.CX_LHSL_15197SW"/>
     <cge:Meas_Ref ObjectId="47302"/>
    <cge:TPSR_Ref TObjectID="8544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47300">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 721.000000 -1808.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8542" ObjectName="SW-CX_LHSL.CX_LHSL_15190SW"/>
     <cge:Meas_Ref ObjectId="47300"/>
    <cge:TPSR_Ref TObjectID="8542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47297">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 -1686.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8539" ObjectName="SW-CX_LHSL.CX_LHSL_1516SW"/>
     <cge:Meas_Ref ObjectId="47297"/>
    <cge:TPSR_Ref TObjectID="8539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47301">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 578.000000 -1821.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8543" ObjectName="SW-CX_LHSL.CX_LHSL_1519SW"/>
     <cge:Meas_Ref ObjectId="47301"/>
    <cge:TPSR_Ref TObjectID="8543"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LHSL" endPointId="0" endStationName="CX_LHSE" flowDrawDirect="1" flowShape="0" id="AC-110kV.linger_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="635,-1884 635,-1926 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11876" ObjectName="AC-110kV.linger_line"/>
    <cge:TPSR_Ref TObjectID="11876_SS-55"/></metadata>
   <polyline fill="none" opacity="0" points="635,-1884 635,-1926 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LHSL.CX_LHSL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 610.000000 -1422.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 610.000000 -1422.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8545" ObjectName="TF-CX_LHSL.CX_LHSL_1T"/>
    <cge:TPSR_Ref TObjectID="8545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -1421.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -1421.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_LHSL.CX_LHS_GN1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 649.000000 -1024.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15888" ObjectName="SM-CX_LHSL.CX_LHS_GN1"/>
    <cge:TPSR_Ref TObjectID="15888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LHSL.CX_LHS_GN2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 -1024.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15889" ObjectName="SM-CX_LHSL.CX_LHS_GN2"/>
    <cge:TPSR_Ref TObjectID="15889"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_16fcaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1260 673,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8523@0" ObjectIDZND0="8525@0" Pin0InfoVect0LinkObjId="SW-47288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17000c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1260 673,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1dc0330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1071 712,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1d5bdd0@0" ObjectIDZND1="g_1d5cb10@0" Pin0InfoVect0LinkObjId="g_1d5bdd0_0" Pin0InfoVect1LinkObjId="g_1d5cb10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1071 712,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc3b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1260 912,-1237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8523@0" ObjectIDZND0="8530@0" Pin0InfoVect0LinkObjId="SW-47291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17000c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1260 912,-1237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17316e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1220 912,-1203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8530@1" ObjectIDZND0="8756@1" Pin0InfoVect0LinkObjId="SW-47290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1220 912,-1203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16fae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1176 912,-1160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8756@0" ObjectIDZND0="8531@0" Pin0InfoVect0LinkObjId="SW-47291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1176 912,-1160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1734be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1024 912,-1000 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1dca170@0" Pin0InfoVect0LinkObjId="g_1dca170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1024 912,-1000 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1731440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-1071 951,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1d5e0d0@0" ObjectIDZND1="g_1d5e950@0" Pin0InfoVect0LinkObjId="g_1d5e0d0_0" Pin0InfoVect1LinkObjId="g_1d5e950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="951,-1071 951,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1472320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1260 508,-1235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8523@0" ObjectIDZND0="8528@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17000c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1260 508,-1235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1472580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1159 508,-1175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8529@0" ObjectIDZND0="8755@0" Pin0InfoVect0LinkObjId="SW-47289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1159 508,-1175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14727e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1202 508,-1218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8755@1" ObjectIDZND0="8528@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47289_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1202 508,-1218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dc7d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1069 508,-1044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1472a40@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1472a40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1069 508,-1044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_170cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1260 634,-1293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8523@0" ObjectIDZND0="8527@1" Pin0InfoVect0LinkObjId="SW-195747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17000c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1260 634,-1293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_170ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1311 634,-1331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8527@0" ObjectIDZND0="8757@0" Pin0InfoVect0LinkObjId="SW-195746_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1311 634,-1331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1707ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1358 634,-1381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8757@1" ObjectIDZND0="8526@1" Pin0InfoVect0LinkObjId="SW-195747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195746_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1358 634,-1381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_17000c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1294 866,-1260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8535@1" ObjectIDZND0="8523@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1294 866,-1260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1700320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1331 866,-1312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8758@0" ObjectIDZND0="8535@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47293_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1331 866,-1312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1700580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1380 866,-1358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8534@1" ObjectIDZND0="8758@1" Pin0InfoVect0LinkObjId="SW-47293_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1380 866,-1358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_173a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-1539 867,-1506 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-1539 867,-1506 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_170f3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1032,-1509 1032,-1488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_170e1c0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_170e1c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1032,-1509 1032,-1488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_170f640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1032,-1576 1032,-1552 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="8532@0" ObjectIDZND0="g_170e1c0@0" Pin0InfoVect0LinkObjId="g_170e1c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1032,-1576 1032,-1552 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1701570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1507 634,-1522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="8545@1" ObjectIDZND0="8538@0" Pin0InfoVect0LinkObjId="SW-47296_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d85f00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1507 634,-1522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_17017d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1585 634,-1610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="48331@0" ObjectIDZND0="8537@x" ObjectIDZND1="8540@x" Pin0InfoVect0LinkObjId="SW-47295_0" Pin0InfoVect1LinkObjId="SW-47298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1da6cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1585 634,-1610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1703a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1610 634,-1632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="breaker" ObjectIDND0="8540@x" ObjectIDND1="48331@0" ObjectIDZND0="8537@0" Pin0InfoVect0LinkObjId="SW-47295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47298_0" Pin1InfoVect1LinkObjId="g_1da6cf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1610 634,-1632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1501450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1659 634,-1687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8537@1" ObjectIDZND0="8541@x" ObjectIDZND1="8539@x" Pin0InfoVect0LinkObjId="SW-47299_0" Pin0InfoVect1LinkObjId="SW-47297_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1659 634,-1687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1504130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1610 610,-1610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="8537@x" ObjectIDND1="48331@0" ObjectIDZND0="8540@1" Pin0InfoVect0LinkObjId="SW-47298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47295_0" Pin1InfoVect1LinkObjId="g_1da6cf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1610 610,-1610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1504390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="574,-1610 551,-1610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8540@0" ObjectIDZND0="g_1728cc0@0" Pin0InfoVect0LinkObjId="g_1728cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="574,-1610 551,-1610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1727d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1687 607,-1687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8537@x" ObjectIDND1="8539@x" ObjectIDZND0="8541@1" Pin0InfoVect0LinkObjId="SW-47299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47295_0" Pin1InfoVect1LinkObjId="SW-47297_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1687 607,-1687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1727fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-1687 550,-1687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8541@0" ObjectIDZND0="g_1728230@0" Pin0InfoVect0LinkObjId="g_1728230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-1687 550,-1687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1db7f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="635,-1487 573,-1487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8545@x" ObjectIDZND0="g_1d82540@0" ObjectIDZND1="8536@x" Pin0InfoVect0LinkObjId="g_1d82540_0" Pin0InfoVect1LinkObjId="SW-47294_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d85f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="635,-1487 573,-1487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1db81c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="573,-1487 542,-1487 542,-1463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8545@x" ObjectIDND1="8536@x" ObjectIDZND0="g_1d82540@0" Pin0InfoVect0LinkObjId="g_1d82540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d85f00_0" Pin1InfoVect1LinkObjId="SW-47294_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="573,-1487 542,-1487 542,-1463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13d7ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="573,-1487 573,-1467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="g_1d82540@0" ObjectIDND1="8545@x" ObjectIDZND0="8536@1" Pin0InfoVect0LinkObjId="SW-47294_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1d82540_0" Pin1InfoVect1LinkObjId="g_1d85f00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="573,-1487 573,-1467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_13d7f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="573,-1431 573,-1413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8536@0" ObjectIDZND0="g_13d81a0@0" Pin0InfoVect0LinkObjId="g_13d81a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47294_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="573,-1431 573,-1413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d54e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-1799 695,-1783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8542@0" ObjectIDZND0="g_1d55090@0" Pin0InfoVect0LinkObjId="g_1d55090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="695,-1799 695,-1783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d55ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1745 524,-1755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_13dbf80@0" ObjectIDZND0="8544@0" Pin0InfoVect0LinkObjId="SW-47302_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13dbf80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1745 524,-1755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d59790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="465,-1312 465,-1322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1d59180@0" Pin0InfoVect0LinkObjId="g_1d59180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="465,-1312 465,-1322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d599f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="465,-1353 465,-1364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d59180@1" ObjectIDZND0="g_1db7680@0" Pin0InfoVect0LinkObjId="g_1db7680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d59180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="465,-1353 465,-1364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5abd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-1008 628,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_172f420@0" ObjectIDZND0="g_1d5a350@0" Pin0InfoVect0LinkObjId="g_1d5a350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_172f420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-1008 628,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5ae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="628,-1057 628,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d5a350@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5a350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="628,-1057 628,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5b910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1024 673,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1d5b090@1" Pin0InfoVect0LinkObjId="g_1d5b090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1024 673,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d5bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-982 673,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_1d5b090@0" ObjectIDZND0="g_172cce0@0" Pin0InfoVect0LinkObjId="g_172cce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5b090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-982 673,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5c650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1000 712,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_172ba10@0" ObjectIDZND0="g_1d5bdd0@0" Pin0InfoVect0LinkObjId="g_1d5bdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_172ba10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1000 712,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1045 712,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d5bdd0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1d5cb10@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1d5cb10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5bdd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1045 712,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-1007 742,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1dc0f70@0" ObjectIDZND0="g_1d5cb10@0" Pin0InfoVect0LinkObjId="g_1d5cb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dc0f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="742,-1007 742,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5d5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="742,-1051 742,-1058 712,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d5cb10@1" ObjectIDZND0="g_1d5bdd0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1d5bdd0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5cb10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="742,-1051 742,-1058 712,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5f1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-1023 867,-1030 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1734e40@0" ObjectIDZND0="g_1d5d850@0" Pin0InfoVect0LinkObjId="g_1d5d850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1734e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-1023 867,-1030 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5f430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-1061 867,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1d5d850@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5d850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-1061 867,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5f690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-994 951,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1dc4b90@0" ObjectIDZND0="g_1d5e0d0@0" Pin0InfoVect0LinkObjId="g_1d5e0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dc4b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="951,-994 951,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-1043 951,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d5e0d0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1d5e950@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1d5e950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5e0d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="951,-1043 951,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1001 981,-1020 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_13f7dd0@0" ObjectIDZND0="g_1d5e950@0" Pin0InfoVect0LinkObjId="g_1d5e950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f7dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1001 981,-1020 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d5fdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1051 981,-1058 951,-1058 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1d5e950@1" ObjectIDZND0="g_1d5e0d0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1d5e0d0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d5e950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1051 981,-1058 951,-1058 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d640e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1687 634,-1708 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8541@x" ObjectIDND1="8537@x" ObjectIDZND0="8539@0" Pin0InfoVect0LinkObjId="SW-47297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47299_0" Pin1InfoVect1LinkObjId="SW-47295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1687 634,-1708 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d698d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1160 673,-1174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8759@0" ObjectIDZND0="8524@0" Pin0InfoVect0LinkObjId="SW-47287_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1160 673,-1174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d69ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1201 673,-1220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8524@1" ObjectIDZND0="8525@1" Pin0InfoVect0LinkObjId="SW-47288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47287_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1201 673,-1220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d802d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="484,-1126 508,-1126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1d7f250@0" ObjectIDZND0="8529@x" ObjectIDZND1="g_1472a40@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1472a40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7f250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="484,-1126 508,-1126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d80530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1141 508,-1124 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="8529@1" ObjectIDZND0="g_1d7f250@0" ObjectIDZND1="g_1472a40@0" Pin0InfoVect0LinkObjId="g_1d7f250_0" Pin0InfoVect1LinkObjId="g_1472a40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1141 508,-1124 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d80790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-1124 508,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8529@x" ObjectIDND1="g_1d7f250@0" ObjectIDZND0="g_1472a40@0" Pin0InfoVect0LinkObjId="g_1472a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1d7f250_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="508,-1124 508,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d809f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="691,-1125 673,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="hydroGenerator" ObjectIDND0="g_1d7d510@0" ObjectIDZND0="8759@x" ObjectIDZND1="15888@x" Pin0InfoVect0LinkObjId="SW-47288_0" Pin0InfoVect1LinkObjId="SM-CX_LHSL.CX_LHS_GN1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7d510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="691,-1125 673,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d80c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1142 673,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" ObjectIDND0="8759@1" ObjectIDZND0="g_1d7d510@0" ObjectIDZND1="15888@x" Pin0InfoVect0LinkObjId="g_1d7d510_0" Pin0InfoVect1LinkObjId="SM-CX_LHSL.CX_LHS_GN1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1142 673,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d80eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="929,-1125 912,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="hydroGenerator" ObjectIDND0="g_1d7e2f0@0" ObjectIDZND0="8531@x" ObjectIDZND1="15889@x" Pin0InfoVect0LinkObjId="SW-47291_0" Pin0InfoVect1LinkObjId="SM-CX_LHSL.CX_LHS_GN2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d7e2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="929,-1125 912,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d81110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1142 912,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="hydroGenerator" ObjectIDND0="8531@1" ObjectIDZND0="g_1d7e2f0@0" ObjectIDZND1="15889@x" Pin0InfoVect0LinkObjId="g_1d7e2f0_0" Pin0InfoVect1LinkObjId="SM-CX_LHSL.CX_LHS_GN2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1142 912,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="485,-1282 465,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_1d84240@0" ObjectIDZND0="0@x" ObjectIDZND1="8523@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_17000c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d84240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="485,-1282 465,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="465,-1295 465,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="0@1" ObjectIDZND0="g_1d84240@0" ObjectIDZND1="8523@0" Pin0InfoVect0LinkObjId="g_1d84240_0" Pin0InfoVect1LinkObjId="g_17000c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="465,-1295 465,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d857e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="465,-1260 465,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8523@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1d84240@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1d84240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_17000c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="465,-1260 465,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="652,-1409 634,-1409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_1d83160@0" ObjectIDZND0="8526@x" ObjectIDZND1="8545@x" Pin0InfoVect0LinkObjId="SW-195747_0" Pin0InfoVect1LinkObjId="g_1d85f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d83160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="652,-1409 634,-1409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1398 634,-1409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="8526@0" ObjectIDZND0="g_1d83160@0" ObjectIDZND1="8545@x" Pin0InfoVect0LinkObjId="g_1d83160_0" Pin0InfoVect1LinkObjId="g_1d85f00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-195747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1398 634,-1409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d85f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1409 634,-1427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="8526@x" ObjectIDND1="g_1d83160@0" ObjectIDZND0="8545@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-195747_0" Pin1InfoVect1LinkObjId="g_1d83160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1409 634,-1427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d869d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-1408 866,-1408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_1d86bc0@0" ObjectIDZND0="0@x" ObjectIDZND1="8534@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d86bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="885,-1408 866,-1408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d87f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1426 866,-1408 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1d86bc0@0" ObjectIDZND1="8534@x" Pin0InfoVect0LinkObjId="g_1d86bc0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1426 866,-1408 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d88140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1408 866,-1397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1d86bc0@0" ObjectIDZND0="8534@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1d86bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1408 866,-1397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d89730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="963,-1733 1032,-1733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="8532@x" Pin0InfoVect0LinkObjId="SW-47292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="963,-1733 1032,-1733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d89920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="936,-1733 868,-1733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="936,-1733 868,-1733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-1733 868,-1925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="868,-1733 868,-1925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-1566 867,-1733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="867,-1566 867,-1733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8bf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="833,-1639 833,-1664 897,-1664 897,-1621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d8abf0@0" ObjectIDZND0="g_1d8b6b0@0" Pin0InfoVect0LinkObjId="g_1d8b6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d8abf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="833,-1639 833,-1664 897,-1664 897,-1621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-1088 712,-1110 628,-1110 628,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-1088 712,-1110 628,-1110 628,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d8d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="951,-1088 951,-1110 867,-1110 867,-1088 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="951,-1088 951,-1110 867,-1110 867,-1088 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,-1125 673,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="8759@x" ObjectIDND1="g_1d7d510@0" ObjectIDZND0="15888@0" Pin0InfoVect0LinkObjId="SM-CX_LHSL.CX_LHS_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47288_0" Pin1InfoVect1LinkObjId="g_1d7d510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,-1125 673,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="912,-1125 912,-1073 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="8531@x" ObjectIDND1="g_1d7e2f0@0" ObjectIDZND0="15889@0" Pin0InfoVect0LinkObjId="SM-CX_LHSL.CX_LHS_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47291_0" Pin1InfoVect1LinkObjId="g_1d7e2f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="912,-1125 912,-1073 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-1922 1032,-1922 1032,-1732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="8532@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-47292_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-1922 1032,-1922 1032,-1732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d8dfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1032,-1732 1032,-1603 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="8532@1" Pin0InfoVect0LinkObjId="SW-47292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1032,-1732 1032,-1603 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d97660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="552,-1812 552,-1798 553,-1798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8543@0" ObjectIDZND0="8544@x" ObjectIDZND1="g_13d8bf0@0" Pin0InfoVect0LinkObjId="SW-47302_0" Pin0InfoVect1LinkObjId="g_13d8bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="552,-1812 552,-1798 553,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d98150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1744 634,-1798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="8539@1" ObjectIDZND0="8542@x" ObjectIDZND1="8543@x" ObjectIDZND2="11876@1" Pin0InfoVect0LinkObjId="SW-47300_0" Pin0InfoVect1LinkObjId="SW-47301_0" Pin0InfoVect2LinkObjId="g_1d98ea0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1744 634,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d983b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="552,-1848 552,-1864 634,-1864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8543@1" ObjectIDZND0="8542@x" ObjectIDZND1="8539@x" ObjectIDZND2="g_1d88330@0" Pin0InfoVect0LinkObjId="SW-47300_0" Pin0InfoVect1LinkObjId="SW-47297_0" Pin0InfoVect2LinkObjId="g_1d88330_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="552,-1848 552,-1864 634,-1864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d98ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1864 634,-1887 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="8542@x" ObjectIDND1="8539@x" ObjectIDND2="g_1d88330@0" ObjectIDZND0="11876@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47300_0" Pin1InfoVect1LinkObjId="SW-47297_0" Pin1InfoVect2LinkObjId="g_1d88330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1864 634,-1887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d99100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="695,-1835 695,-1844 634,-1844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="8542@1" ObjectIDZND0="8539@x" ObjectIDZND1="g_1d88330@0" ObjectIDZND2="8543@x" Pin0InfoVect0LinkObjId="SW-47297_0" Pin0InfoVect1LinkObjId="g_1d88330_0" Pin0InfoVect2LinkObjId="SW-47301_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="695,-1835 695,-1844 634,-1844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d99bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1798 634,-1844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="8539@x" ObjectIDND1="g_1d88330@0" ObjectIDZND0="8542@x" ObjectIDZND1="8543@x" ObjectIDZND2="11876@1" Pin0InfoVect0LinkObjId="SW-47300_0" Pin0InfoVect1LinkObjId="SW-47301_0" Pin0InfoVect2LinkObjId="g_1d98ea0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47297_0" Pin1InfoVect1LinkObjId="g_1d88330_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1798 634,-1844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1d99e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1844 634,-1864 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="8542@x" ObjectIDND1="8539@x" ObjectIDND2="g_1d88330@0" ObjectIDZND0="8543@x" ObjectIDZND1="11876@1" Pin0InfoVect0LinkObjId="SW-47301_0" Pin0InfoVect1LinkObjId="g_1d98ea0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47300_0" Pin1InfoVect1LinkObjId="SW-47297_0" Pin1InfoVect2LinkObjId="g_1d88330_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1844 634,-1864 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da25d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1798 572,-1798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="8539@x" ObjectIDND1="8542@x" ObjectIDND2="8543@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47297_0" Pin1InfoVect1LinkObjId="SW-47300_0" Pin1InfoVect2LinkObjId="SW-47301_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1798 572,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1798 584,-1798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8539@x" ObjectIDND1="8542@x" ObjectIDND2="8543@x" ObjectIDZND0="g_1d88330@0" Pin0InfoVect0LinkObjId="g_1d88330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47297_0" Pin1InfoVect1LinkObjId="SW-47300_0" Pin1InfoVect2LinkObjId="SW-47301_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1798 584,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="584,-1798 584,-1779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8539@x" ObjectIDND1="8542@x" ObjectIDND2="8543@x" ObjectIDZND0="g_1d88330@0" Pin0InfoVect0LinkObjId="g_1d88330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47297_0" Pin1InfoVect1LinkObjId="SW-47300_0" Pin1InfoVect2LinkObjId="SW-47301_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="584,-1798 584,-1779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da46b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,-1791 524,-1798 552,-1798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8544@1" ObjectIDZND0="8543@x" ObjectIDZND1="g_13d8bf0@0" Pin0InfoVect0LinkObjId="SW-47301_0" Pin0InfoVect1LinkObjId="g_13d8bf0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47302_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="524,-1791 524,-1798 552,-1798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da48a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="552,-1798 552,-1744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8543@x" ObjectIDND1="8544@x" ObjectIDZND0="g_13d8bf0@0" Pin0InfoVect0LinkObjId="g_13d8bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47301_0" Pin1InfoVect1LinkObjId="SW-47302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="552,-1798 552,-1744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1da6cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="634,-1561 634,-1585 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="8538@1" ObjectIDZND0="48331@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47296_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="634,-1561 634,-1585 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="673" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="912" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="508" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="634" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="866" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8523" cx="465" cy="-1260" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48331" cx="634" cy="-1585" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48331" cx="634" cy="-1585" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37324" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 324.000000 -1890.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5906" ObjectName="DYN-CX_LHSL"/>
     <cge:Meas_Ref ObjectId="37324"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dd1840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 441.000000 -1012.000000) translate(0,15)">至厂用I段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dca460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.375000 -948.000000) translate(0,15)">1号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,15)">老</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,33)">虎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,51)">山</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,69)">零</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,87)">二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -1985.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d55d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -926.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d56580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 692.875000 -969.000000) translate(0,12)">1YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.875000 -983.000000) translate(0,12)">2YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 608.875000 -970.000000) translate(0,12)">1YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d57a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 942.875000 -967.000000) translate(0,12)">2YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d57ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -1100.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d57ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.375000 -1447.000000) translate(0,15)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d586a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.375000 -1548.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d586a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.375000 -1548.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d586a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.375000 -1548.000000) translate(0,51)">厂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d586a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.375000 -1548.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d586a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.375000 -1548.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d58c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.875000 -1712.000000) translate(0,12)">5YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="36" graphid="g_1d60010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 537.875000 -779.000000) translate(0,29)">老虎山零级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d61580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -1653.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d64340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -1733.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d64870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 -1547.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d64cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 -1677.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 573.000000 -1600.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 461.000000 -1777.000000) translate(0,12)">15197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -1839.000000) translate(0,12)">1519</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 649.000000 -1822.000000) translate(0,12)">15190</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d65f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -1454.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d663b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -1352.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d665f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 -1352.000000) translate(0,12)">022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 517.000000 -1196.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 682.000000 -1197.000000) translate(0,12)">011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -1197.000000) translate(0,12)">021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d66ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 997.000000 -1596.000000) translate(0,12)">025</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d67130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -1521.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d67670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 -1250.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d69cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -1382.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d6c130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 18.000000 -1861.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1d70840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 167.000000 -1970.500000) translate(0,16)">老虎山零级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d720a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 -1559.000000) translate(0,12)">023</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d72540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -1757.000000) translate(0,12)">024</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d72780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 635.000000 -1096.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d729c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -1096.000000) translate(0,12)">0912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d72c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -1096.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d72e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 957.000000 -1096.000000) translate(0,12)">0922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d73080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 425.000000 -1314.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -1558.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -1558.000000) translate(0,27)">SF9-20000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -1558.000000) translate(0,42)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d778a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 468.000000 -1558.000000) translate(0,57)">121±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,12)">1号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,27)">SF8000-8/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,42)">Pe=8MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,57)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,72)">Ie=549A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7aaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -931.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,12)">2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,27)">SF8000-8/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,42)">Pe=8MW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,57)">Ue=10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,72)">Ie=549A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 832.000000 -931.000000) translate(0,87)">CoS∮=0.8</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d7d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 419.875000 -1384.000000) translate(0,12)">3YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d81370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -1149.000000) translate(0,12)">5TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d81e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 700.000000 -1149.000000) translate(0,12)">1TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d82300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 934.000000 -1149.000000) translate(0,12)">2TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d86160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 656.000000 -1401.000000) translate(0,12)">3TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d86790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 500.000000 -1302.000000) translate(0,12)">4TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d87920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 -1400.000000) translate(0,12)">6TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d89130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -1774.000000) translate(0,12)">3PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d89b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 826.375000 -1946.000000) translate(0,15)">10kV近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d8c1a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.875000 -1576.000000) translate(0,12)">1PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d8c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 905.875000 -1629.000000) translate(0,12)">3YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.000000 -926.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 944.875000 -948.000000) translate(0,15)">2号励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8e1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1029.375000 -1941.000000) translate(0,15)">10kV坝区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d9a0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 391.000000 -1407.000000) translate(0,12)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_1da4a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 146.000000 -1024.000000) translate(0,16)">7813080</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47287">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -1166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8524" ObjectName="SW-CX_LHSL.CX_LHSL_011BK"/>
     <cge:Meas_Ref ObjectId="47287"/>
    <cge:TPSR_Ref TObjectID="8524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47290">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.000000 -1168.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8756" ObjectName="SW-CX_LHSL.CX_LHSL_021BK"/>
     <cge:Meas_Ref ObjectId="47290"/>
    <cge:TPSR_Ref TObjectID="8756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47289">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.000000 -1167.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8755" ObjectName="SW-CX_LHSL.CX_LHSL_012BK"/>
     <cge:Meas_Ref ObjectId="47289"/>
    <cge:TPSR_Ref TObjectID="8755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-195746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -1323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8757" ObjectName="SW-CX_LHSL.CX_LHSL_001BK"/>
     <cge:Meas_Ref ObjectId="195746"/>
    <cge:TPSR_Ref TObjectID="8757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47293">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -1323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8758" ObjectName="SW-CX_LHSL.CX_LHSL_022BK"/>
     <cge:Meas_Ref ObjectId="47293"/>
    <cge:TPSR_Ref TObjectID="8758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -1531.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 927.000000 -1723.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47295">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -1624.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8537" ObjectName="SW-CX_LHSL.CX_LHSL_151BK"/>
     <cge:Meas_Ref ObjectId="47295"/>
    <cge:TPSR_Ref TObjectID="8537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47292">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 -1568.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8532" ObjectName="SW-CX_LHSL.CX_LHSL_025BK"/>
     <cge:Meas_Ref ObjectId="47292"/>
    <cge:TPSR_Ref TObjectID="8532"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1023.000000 -1461.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.000000 -1017.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_172f420">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 616.000000 -972.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dc0f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 730.000000 -971.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_172ba10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 698.000000 -976.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1734e40">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 855.000000 -987.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f7dd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 969.000000 -965.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dc4b90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 -970.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1472a40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 495.000000 -1066.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_170e1c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 -1506.000000)" xlink:href="#lightningRod:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db7680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 451.000000 -1388.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d8bf0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.000000 -1720.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d59180">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -1317.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5a350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 619.000000 -1021.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5b090">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 664.000000 -977.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5bdd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 -1009.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5cb10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.000000 -1015.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5d850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -1025.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5e0d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 942.000000 -1007.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d5e950">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 -1015.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7d510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 686.000000 -1119.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7e2f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 924.000000 -1119.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7f250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 439.000000 -1119.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d82540">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 -1405.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d83160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 -1403.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d84240">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 480.000000 -1276.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d86bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 880.000000 -1402.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d88330">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 577.000000 -1721.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8abf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 826.000000 -1581.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d8b6b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.000000 -1597.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47275" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1673.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47275" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8537"/>
     <cge:Term_Ref ObjectID="12054"/>
    <cge:TPSR_Ref TObjectID="8537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47276" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1673.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47276" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8537"/>
     <cge:Term_Ref ObjectID="12054"/>
    <cge:TPSR_Ref TObjectID="8537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47277" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1673.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8537"/>
     <cge:Term_Ref ObjectID="12054"/>
    <cge:TPSR_Ref TObjectID="8537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="U" PreSymbol="0" appendix="" decimal="2" id="ME-47279" prefix="U " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -1673.000000) translate(0,57)">U  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8537"/>
     <cge:Term_Ref ObjectID="12054"/>
    <cge:TPSR_Ref TObjectID="8537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47264" prefix="P " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -899.000000) translate(0,12)">P  47264.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47264" ObjectName="CX_LHSL.CX_LHSL_011BK:F"/>
     <cge:PSR_Ref ObjectID="8524"/>
     <cge:Term_Ref ObjectID="12028"/>
    <cge:TPSR_Ref TObjectID="8524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47265" prefix="Q " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -899.000000) translate(0,27)">Q  47265.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47265" ObjectName="CX_LHSL.CX_LHSL_011BK:F"/>
     <cge:PSR_Ref ObjectID="8524"/>
     <cge:Term_Ref ObjectID="12028"/>
    <cge:TPSR_Ref TObjectID="8524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47263" prefix="Ia  " rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 675.000000 -899.000000) translate(0,42)">Ia   47263.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47263" ObjectName="CX_LHSL.CX_LHSL_011BK:F"/>
     <cge:PSR_Ref ObjectID="8524"/>
     <cge:Term_Ref ObjectID="12028"/>
    <cge:TPSR_Ref TObjectID="8524"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47269" prefix="P " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -892.000000) translate(0,12)">P  47269.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47269" ObjectName="CX_LHSL.CX_LHSL_021BK:F"/>
     <cge:PSR_Ref ObjectID="8756"/>
     <cge:Term_Ref ObjectID="12371"/>
    <cge:TPSR_Ref TObjectID="8756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47270" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -892.000000) translate(0,27)">Q  47270.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47270" ObjectName="CX_LHSL.CX_LHSL_021BK:F"/>
     <cge:PSR_Ref ObjectID="8756"/>
     <cge:Term_Ref ObjectID="12371"/>
    <cge:TPSR_Ref TObjectID="8756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47268" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -892.000000) translate(0,42)">Ia   47268.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47268" ObjectName="CX_LHSL.CX_LHSL_021BK:F"/>
     <cge:PSR_Ref ObjectID="8756"/>
     <cge:Term_Ref ObjectID="12371"/>
    <cge:TPSR_Ref TObjectID="8756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47273" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 316.000000 -1296.000000) translate(0,12)">Uab  47273.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47273" ObjectName="CX_LHSL.CX_LHSL_9IM:F"/>
     <cge:PSR_Ref ObjectID="8523"/>
     <cge:Term_Ref ObjectID="12027"/>
    <cge:TPSR_Ref TObjectID="8523"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47274" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 316.000000 -1296.000000) translate(0,27)">Hz  47274.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47274" ObjectName="CX_LHSL.CX_LHSL_9IM:F"/>
     <cge:PSR_Ref ObjectID="8523"/>
     <cge:Term_Ref ObjectID="12027"/>
    <cge:TPSR_Ref TObjectID="8523"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="171" x="133" y="-1981"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="84" y="-1998"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(170,85,127)" stroke-width="1" x1="553" x2="572" y1="-1798" y2="-1798"/>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_172cce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -945.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dca170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 902.000000 -974.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1728230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 524.000000 -1679.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1728cc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -1602.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13d81a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -1387.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13dbf80" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 532.000000 -1719.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d55090" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 703.000000 -1757.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="171" x="133" y="-1981"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="171" x="133" y="-1981"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="84" y="-1998"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="84" y="-1998"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LHSL.CX_LHSL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,-1260 1057,-1260 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8523" ObjectName="BS-CX_LHSL.CX_LHSL_9IM"/>
    <cge:TPSR_Ref TObjectID="8523"/></metadata>
   <polyline fill="none" opacity="0" points="379,-1260 1057,-1260 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LHSL.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="627,-1585 639,-1585 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48331" ObjectName="BS-CX_LHSL.XM"/>
    <cge:TPSR_Ref TObjectID="48331"/></metadata>
   <polyline fill="none" opacity="0" points="627,-1585 639,-1585 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 121.000000 -1922.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116468" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 138.538462 -1819.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116468" ObjectName="CX_LHSL:CX_LHSL_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-116469" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 136.538462 -1777.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="116469" ObjectName="CX_LHSL:CX_LHSL_Q"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LHSL"/>
</svg>