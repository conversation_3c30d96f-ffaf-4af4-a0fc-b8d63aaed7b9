<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-346" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1902 1199">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="25" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="51" x2="51" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="55" x2="55" y1="34" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="58" x2="58" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="35" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="35" x2="35" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="42" x2="42" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="16" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="4"/>
    <circle cx="41" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="17" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="26" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="20" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="26" y1="9" y2="5"/>
    <circle cx="35" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="18" y2="15"/>
    <circle cx="29" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="18" y2="15"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape186">
    <circle cx="15" cy="80" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="41" x2="39" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="43" x2="37" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="34" x2="46" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="59" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="19" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="57" y2="53"/>
    <circle cx="15" cy="58" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="43" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,16 21,28 9,28 15,16 15,16 15,16 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,59 40,59 40,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="53" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="10" y1="87" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="18" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="14" y1="81" y2="87"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_36b8000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape98">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="22" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="24" y2="24"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="35" y1="12" y2="12"/>
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bacca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bad7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2badf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2baec80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2baffc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bb0b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb1610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bb1eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bb2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2bb2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb3580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb3580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb43d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb43d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2bb50b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bb6c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bb74a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bb83b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bb8b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bba2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bbad00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bbb330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bbb8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bbca00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bbd380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bbde70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bbe830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bbefc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bc08e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2bc1a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bc2700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2bd0b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2bc8eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bca0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2bc4900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1209" width="1912" x="3112" y="-1205"/>
  </g><g id="Line_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4183,-743 4183,-664 4166,-664 " stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3127" y="-605"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-319493">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49611" ObjectName="SW-CX_KJZGF.CX_KJZGF_1016SW"/>
     <cge:Meas_Ref ObjectId="319493"/>
    <cge:TPSR_Ref TObjectID="49611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319494">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -1005.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49612" ObjectName="SW-CX_KJZGF.CX_KJZGF_10167SW"/>
     <cge:Meas_Ref ObjectId="319494"/>
    <cge:TPSR_Ref TObjectID="49612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319496">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -876.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49614" ObjectName="SW-CX_KJZGF.CX_KJZGF_10110SW"/>
     <cge:Meas_Ref ObjectId="319496"/>
    <cge:TPSR_Ref TObjectID="49614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319495">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4205.000000 -939.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49613" ObjectName="SW-CX_KJZGF.CX_KJZGF_10160SW"/>
     <cge:Meas_Ref ObjectId="319495"/>
    <cge:TPSR_Ref TObjectID="49613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319497">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4204.000000 -796.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49615" ObjectName="SW-CX_KJZGF.CX_KJZGF_10117SW"/>
     <cge:Meas_Ref ObjectId="319497"/>
    <cge:TPSR_Ref TObjectID="49615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319501">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -594.018519)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49619" ObjectName="SW-CX_KJZGF.CX_KJZGF_301XC1"/>
     <cge:Meas_Ref ObjectId="319501"/>
    <cge:TPSR_Ref TObjectID="49619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319501">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4254.000000 -513.018519)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49618" ObjectName="SW-CX_KJZGF.CX_KJZGF_301XC"/>
     <cge:Meas_Ref ObjectId="319501"/>
    <cge:TPSR_Ref TObjectID="49618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319499">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -679.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49616" ObjectName="SW-CX_KJZGF.CX_KJZGF_1010SW"/>
     <cge:Meas_Ref ObjectId="319499"/>
    <cge:TPSR_Ref TObjectID="49616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319492">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -820.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49610" ObjectName="SW-CX_KJZGF.CX_KJZGF_1011SW"/>
     <cge:Meas_Ref ObjectId="319492"/>
    <cge:TPSR_Ref TObjectID="49610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3700.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49641" ObjectName="SW-CX_KJZGF.CX_KJZGF_361XC"/>
     <cge:Meas_Ref ObjectId="319524"/>
    <cge:TPSR_Ref TObjectID="49641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319524">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3700.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49642" ObjectName="SW-CX_KJZGF.CX_KJZGF_361XC1"/>
     <cge:Meas_Ref ObjectId="319524"/>
    <cge:TPSR_Ref TObjectID="49642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -155.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319502">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -527.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49620" ObjectName="SW-CX_KJZGF.CX_KJZGF_3901XC"/>
     <cge:Meas_Ref ObjectId="319502"/>
    <cge:TPSR_Ref TObjectID="49620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319504">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49623" ObjectName="SW-CX_KJZGF.CX_KJZGF_362XC"/>
     <cge:Meas_Ref ObjectId="319504"/>
    <cge:TPSR_Ref TObjectID="49623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319504">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3941.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49624" ObjectName="SW-CX_KJZGF.CX_KJZGF_362XC1"/>
     <cge:Meas_Ref ObjectId="319504"/>
    <cge:TPSR_Ref TObjectID="49624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49633" ObjectName="SW-CX_KJZGF.CX_KJZGF_363XC"/>
     <cge:Meas_Ref ObjectId="319515"/>
    <cge:TPSR_Ref TObjectID="49633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319515">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4181.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49634" ObjectName="SW-CX_KJZGF.CX_KJZGF_363XC1"/>
     <cge:Meas_Ref ObjectId="319515"/>
    <cge:TPSR_Ref TObjectID="49634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -419.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49637" ObjectName="SW-CX_KJZGF.CX_KJZGF_364XC"/>
     <cge:Meas_Ref ObjectId="319519"/>
    <cge:TPSR_Ref TObjectID="49637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319519">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -332.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49638" ObjectName="SW-CX_KJZGF.CX_KJZGF_364XC1"/>
     <cge:Meas_Ref ObjectId="319519"/>
    <cge:TPSR_Ref TObjectID="49638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49627" ObjectName="SW-CX_KJZGF.CX_KJZGF_365XC"/>
     <cge:Meas_Ref ObjectId="319509"/>
    <cge:TPSR_Ref TObjectID="49627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319509">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49628" ObjectName="SW-CX_KJZGF.CX_KJZGF_365XC1"/>
     <cge:Meas_Ref ObjectId="319509"/>
    <cge:TPSR_Ref TObjectID="49628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -418.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49645" ObjectName="SW-CX_KJZGF.CX_KJZGF_366XC"/>
     <cge:Meas_Ref ObjectId="319529"/>
    <cge:TPSR_Ref TObjectID="49645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319529">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4872.000000 -331.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49646" ObjectName="SW-CX_KJZGF.CX_KJZGF_366XC1"/>
     <cge:Meas_Ref ObjectId="319529"/>
    <cge:TPSR_Ref TObjectID="49646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319511">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -188.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49630" ObjectName="SW-CX_KJZGF.CX_KJZGF_3656SW"/>
     <cge:Meas_Ref ObjectId="319511"/>
    <cge:TPSR_Ref TObjectID="49630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319510">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 -160.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49629" ObjectName="SW-CX_KJZGF.CX_KJZGF_36567SW"/>
     <cge:Meas_Ref ObjectId="319510"/>
    <cge:TPSR_Ref TObjectID="49629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319525">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3669.000000 -255.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49643" ObjectName="SW-CX_KJZGF.CX_KJZGF_36167SW"/>
     <cge:Meas_Ref ObjectId="319525"/>
    <cge:TPSR_Ref TObjectID="49643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319505">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3910.000000 -255.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49625" ObjectName="SW-CX_KJZGF.CX_KJZGF_36267SW"/>
     <cge:Meas_Ref ObjectId="319505"/>
    <cge:TPSR_Ref TObjectID="49625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -255.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49635" ObjectName="SW-CX_KJZGF.CX_KJZGF_36367SW"/>
     <cge:Meas_Ref ObjectId="319516"/>
    <cge:TPSR_Ref TObjectID="49635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -256.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49639" ObjectName="SW-CX_KJZGF.CX_KJZGF_36467SW"/>
     <cge:Meas_Ref ObjectId="319520"/>
    <cge:TPSR_Ref TObjectID="49639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4619.000000 -255.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49631" ObjectName="SW-CX_KJZGF.CX_KJZGF_36560SW"/>
     <cge:Meas_Ref ObjectId="319512"/>
    <cge:TPSR_Ref TObjectID="49631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319530">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 -255.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49647" ObjectName="SW-CX_KJZGF.CX_KJZGF_36667SW"/>
     <cge:Meas_Ref ObjectId="319530"/>
    <cge:TPSR_Ref TObjectID="49647"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2580c30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -717.000000)" xlink:href="#voltageTransformer:shape98"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -115.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -112.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -113.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2643a90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -1001.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2644200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 -935.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26449b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -872.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2645160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -792.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2645910" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 -624.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fcd2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3662.000000 -111.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_25ce4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-1010 4199,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49612@0" ObjectIDZND0="g_2643a90@0" Pin0InfoVect0LinkObjId="g_2643a90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319494_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-1010 4199,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25ce6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-881 4199,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49614@0" ObjectIDZND0="g_26449b0@0" Pin0InfoVect0LinkObjId="g_26449b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-881 4199,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25ce890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4208,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="49616@x" ObjectIDZND0="g_2699ff0@0" Pin0InfoVect0LinkObjId="g_2699ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-319499_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4208,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cea80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4263,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2699ff0@0" ObjectIDND1="49616@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2699ff0_0" Pin1InfoVect1LinkObjId="SW-319499_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4263,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cec70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4210,-944 4200,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49613@0" ObjectIDZND0="g_2644200@0" Pin0InfoVect0LinkObjId="g_2644200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4210,-944 4200,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cee60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-944 4246,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="49609@x" ObjectIDND1="49611@x" ObjectIDZND0="49613@1" Pin0InfoVect0LinkObjId="SW-319495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319491_0" Pin1InfoVect1LinkObjId="SW-319493_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-944 4246,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cf050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-927 4265,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49609@1" ObjectIDZND0="49613@x" ObjectIDZND1="49611@x" Pin0InfoVect0LinkObjId="SW-319495_0" Pin0InfoVect1LinkObjId="SW-319493_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319491_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-927 4265,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cf240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-944 4265,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49613@x" ObjectIDND1="49609@x" ObjectIDZND0="49611@1" Pin0InfoVect0LinkObjId="SW-319493_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319495_0" Pin1InfoVect1LinkObjId="SW-319491_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-944 4265,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cf430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-1010 4265,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="currentTransformer" EndDevType2="lightningRod" ObjectIDND0="49612@1" ObjectIDZND0="49611@x" ObjectIDZND1="g_2672f00@0" ObjectIDZND2="g_2675bb0@0" Pin0InfoVect0LinkObjId="SW-319493_0" Pin0InfoVect1LinkObjId="g_2672f00_0" Pin0InfoVect2LinkObjId="g_2675bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-1010 4265,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cf620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-1010 4265,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="currentTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49612@x" ObjectIDND1="g_2672f00@0" ObjectIDND2="g_2675bb0@0" ObjectIDZND0="49611@0" Pin0InfoVect0LinkObjId="SW-319493_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-319494_0" Pin1InfoVect1LinkObjId="g_2672f00_0" Pin1InfoVect2LinkObjId="g_2675bb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-1010 4265,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cf810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-801 4199,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="49615@0" ObjectIDZND0="g_2645160@0" Pin0InfoVect0LinkObjId="g_2645160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-801 4199,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cc5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4166,-650 4166,-664 4151,-664 4151,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2645910@0" ObjectIDZND0="49616@1" Pin0InfoVect0LinkObjId="SW-319499_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2645910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4166,-650 4166,-664 4151,-664 4151,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25cc7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-743 4151,-743 4151,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2699ff0@0" ObjectIDZND0="49616@0" Pin0InfoVect0LinkObjId="SW-319499_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2699ff0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-743 4151,-743 4151,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cccf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-601 4264,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49619@1" ObjectIDZND0="49617@1" Pin0InfoVect0LinkObjId="SW-319500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319501_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-601 4264,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ccee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-557 4264,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="49617@0" ObjectIDZND0="49618@0" Pin0InfoVect0LinkObjId="SW-319501_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-557 4264,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25cd0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-519 4264,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49618@1" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_2667550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319501_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-519 4264,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26722d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-881 4265,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49614@1" ObjectIDZND0="49610@x" ObjectIDZND1="49609@x" Pin0InfoVect0LinkObjId="SW-319492_0" Pin0InfoVect1LinkObjId="SW-319491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319496_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-881 4265,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26724c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-861 4265,-881 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49610@0" ObjectIDZND0="49614@x" ObjectIDZND1="49609@x" Pin0InfoVect0LinkObjId="SW-319496_0" Pin0InfoVect1LinkObjId="SW-319491_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-861 4265,-881 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26726b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-881 4265,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49614@x" ObjectIDND1="49610@x" ObjectIDZND0="49609@0" Pin0InfoVect0LinkObjId="SW-319491_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319496_0" Pin1InfoVect1LinkObjId="SW-319492_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-881 4265,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_26728a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-801 4265,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="49615@1" ObjectIDZND0="0@x" ObjectIDZND1="49610@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-319492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319497_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4245,-801 4265,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2672ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-761 4265,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="49615@x" ObjectIDZND1="49610@x" Pin0InfoVect0LinkObjId="SW-319497_0" Pin0InfoVect1LinkObjId="SW-319492_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-761 4265,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2672ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-801 4265,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="49615@x" ObjectIDND1="0@x" ObjectIDZND0="49610@1" Pin0InfoVect0LinkObjId="SW-319492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319497_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-801 4265,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25db0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-1089 4265,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2672f00@0" ObjectIDZND0="49612@x" ObjectIDZND1="49611@x" ObjectIDZND2="g_2675bb0@0" Pin0InfoVect0LinkObjId="SW-319494_0" Pin0InfoVect1LinkObjId="SW-319493_0" Pin0InfoVect2LinkObjId="g_2675bb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2672f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-1089 4265,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25db2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-1089 4265,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2672f00@0" ObjectIDND1="g_2675bb0@0" ObjectIDND2="0@x" ObjectIDZND0="49612@x" ObjectIDZND1="49611@x" Pin0InfoVect0LinkObjId="SW-319494_0" Pin0InfoVect1LinkObjId="SW-319493_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2672f00_0" Pin1InfoVect1LinkObjId="g_2675bb0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-1089 4265,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25db4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4304,-1100 4304,-1113 4265,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2675bb0@0" ObjectIDZND0="g_2672f00@0" ObjectIDZND1="49612@x" ObjectIDZND2="49611@x" Pin0InfoVect0LinkObjId="g_2672f00_0" Pin0InfoVect1LinkObjId="SW-319494_0" Pin0InfoVect2LinkObjId="SW-319493_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2675bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4304,-1100 4304,-1113 4265,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25db710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4265,-1113 4265,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="currentTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2675bb0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2672f00@0" ObjectIDZND1="49612@x" ObjectIDZND2="49611@x" Pin0InfoVect0LinkObjId="g_2672f00_0" Pin0InfoVect1LinkObjId="SW-319494_0" Pin0InfoVect2LinkObjId="SW-319493_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2675bb0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4265,-1113 4265,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25dd650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-1090 4536,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-1090 4536,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_25dd870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4536,-1117 4536,-1141 4265,-1141 4265,-1113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="currentTransformer" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_2675bb0@0" ObjectIDZND1="g_2672f00@0" ObjectIDZND2="49612@x" Pin0InfoVect0LinkObjId="g_2675bb0_0" Pin0InfoVect1LinkObjId="g_2672f00_0" Pin0InfoVect2LinkObjId="SW-319494_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4536,-1117 4536,-1141 4265,-1141 4265,-1113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25de270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-635 4313,-648 4264,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="g_2664ba0@0" ObjectIDZND0="0@x" ObjectIDZND1="49619@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-319501_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2664ba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4313,-635 4313,-648 4264,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25de460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-681 4264,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2664ba0@0" ObjectIDZND1="49619@x" Pin0InfoVect0LinkObjId="g_2664ba0_0" Pin0InfoVect1LinkObjId="SW-319501_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-681 4264,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25de650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-648 4264,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2664ba0@0" ObjectIDZND0="49619@0" Pin0InfoVect0LinkObjId="SW-319501_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2664ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-648 4264,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ff620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-425 3710,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49641@1" ObjectIDZND0="49640@1" Pin0InfoVect0LinkObjId="SW-319523_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-425 3710,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ff840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-355 3710,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49642@1" ObjectIDZND0="49640@0" Pin0InfoVect0LinkObjId="SW-319523_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-355 3710,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ffa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3678,-306 3678,-321 3710,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49643@0" ObjectIDZND0="g_2610690@0" ObjectIDZND1="49642@x" ObjectIDZND2="g_2667090@0" Pin0InfoVect0LinkObjId="g_2610690_0" Pin0InfoVect1LinkObjId="SW-319524_0" Pin0InfoVect2LinkObjId="g_2667090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3678,-306 3678,-321 3710,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2600680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-306 3710,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2610690@1" ObjectIDZND0="49643@x" ObjectIDZND1="49642@x" ObjectIDZND2="g_2667090@0" Pin0InfoVect0LinkObjId="SW-319525_0" Pin0InfoVect1LinkObjId="SW-319524_0" Pin0InfoVect2LinkObjId="g_2667090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2610690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-306 3710,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26008a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-321 3710,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="49643@x" ObjectIDND1="g_2610690@0" ObjectIDND2="g_2667090@0" ObjectIDZND0="49642@0" Pin0InfoVect0LinkObjId="SW-319524_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-319525_0" Pin1InfoVect1LinkObjId="g_2610690_0" Pin1InfoVect2LinkObjId="g_2667090_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-321 3710,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2600ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-321 3746,-321 3746,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49643@x" ObjectIDND1="g_2610690@0" ObjectIDND2="49642@x" ObjectIDZND0="g_2667090@0" Pin0InfoVect0LinkObjId="g_2667090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-319525_0" Pin1InfoVect1LinkObjId="g_2610690_0" Pin1InfoVect2LinkObjId="SW-319524_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-321 3746,-321 3746,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26019e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-228 3710,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2600ce0@0" ObjectIDZND0="g_2610690@0" Pin0InfoVect0LinkObjId="g_2610690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2600ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-228 3710,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f95b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-150 3668,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25f7fd0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25f7fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-150 3668,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f97d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-196 3668,-212 3710,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-196 3668,-212 3710,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2581af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-686 3740,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2580410@1" ObjectIDZND0="g_2580c30@0" Pin0InfoVect0LinkObjId="g_2580c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2580410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-686 3740,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2581d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-639 3706,-626 3740,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_257f820@0" ObjectIDZND0="g_2580410@0" ObjectIDZND1="49620@x" Pin0InfoVect0LinkObjId="g_2580410_0" Pin0InfoVect1LinkObjId="SW-319502_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_257f820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-639 3706,-626 3740,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2582820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-642 3740,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2580410@0" ObjectIDZND0="g_257f820@0" ObjectIDZND1="49620@x" Pin0InfoVect0LinkObjId="g_257f820_0" Pin0InfoVect1LinkObjId="SW-319502_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2580410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-642 3740,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2582a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-626 3740,-601 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_257f820@0" ObjectIDND1="g_2580410@0" ObjectIDZND0="49620@1" Pin0InfoVect0LinkObjId="SW-319502_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_257f820_0" Pin1InfoVect1LinkObjId="g_2580410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-626 3740,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a5300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-425 3951,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49623@1" ObjectIDZND0="49622@1" Pin0InfoVect0LinkObjId="SW-319503_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319504_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-425 3951,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a5560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-355 3951,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49624@1" ObjectIDZND0="49622@0" Pin0InfoVect0LinkObjId="SW-319503_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319504_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-355 3951,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a57c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3919,-306 3919,-321 3951,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49625@0" ObjectIDZND0="g_2583b90@0" ObjectIDZND1="g_25832d0@0" ObjectIDZND2="49624@x" Pin0InfoVect0LinkObjId="g_2583b90_0" Pin0InfoVect1LinkObjId="g_25832d0_0" Pin0InfoVect2LinkObjId="SW-319504_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319505_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3919,-306 3919,-321 3951,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a5a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-306 3951,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2583b90@1" ObjectIDZND0="g_25832d0@0" ObjectIDZND1="49624@x" ObjectIDZND2="49625@x" Pin0InfoVect0LinkObjId="g_25832d0_0" Pin0InfoVect1LinkObjId="SW-319504_0" Pin0InfoVect2LinkObjId="SW-319505_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2583b90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-306 3951,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a5c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-321 3951,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2583b90@0" ObjectIDND1="g_25832d0@0" ObjectIDND2="49625@x" ObjectIDZND0="49624@0" Pin0InfoVect0LinkObjId="SW-319504_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2583b90_0" Pin1InfoVect1LinkObjId="g_25832d0_0" Pin1InfoVect2LinkObjId="SW-319505_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-321 3951,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a5ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-321 3987,-321 3987,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2583b90@0" ObjectIDND1="49624@x" ObjectIDND2="49625@x" ObjectIDZND0="g_25832d0@0" Pin0InfoVect0LinkObjId="g_25832d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2583b90_0" Pin1InfoVect1LinkObjId="SW-319504_0" Pin1InfoVect2LinkObjId="SW-319505_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-321 3987,-321 3987,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262b7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-425 4191,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49633@1" ObjectIDZND0="49632@1" Pin0InfoVect0LinkObjId="SW-319514_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-425 4191,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262ba50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-355 4191,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49634@1" ObjectIDZND0="49632@0" Pin0InfoVect0LinkObjId="SW-319514_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319515_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-355 4191,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262bcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4159,-306 4159,-321 4191,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49635@0" ObjectIDZND0="g_25a79a0@0" ObjectIDZND1="g_25a6bf0@0" ObjectIDZND2="49634@x" Pin0InfoVect0LinkObjId="g_25a79a0_0" Pin0InfoVect1LinkObjId="g_25a6bf0_0" Pin0InfoVect2LinkObjId="SW-319515_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319516_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4159,-306 4159,-321 4191,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262bf10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-306 4191,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_25a79a0@1" ObjectIDZND0="49635@x" ObjectIDZND1="g_25a6bf0@0" ObjectIDZND2="49634@x" Pin0InfoVect0LinkObjId="SW-319516_0" Pin0InfoVect1LinkObjId="g_25a6bf0_0" Pin0InfoVect2LinkObjId="SW-319515_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a79a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-306 4191,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-321 4191,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_25a79a0@0" ObjectIDND1="49635@x" ObjectIDND2="g_25a6bf0@0" ObjectIDZND0="49634@0" Pin0InfoVect0LinkObjId="SW-319515_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25a79a0_0" Pin1InfoVect1LinkObjId="SW-319516_0" Pin1InfoVect2LinkObjId="g_25a6bf0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-321 4191,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_262c3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-321 4227,-321 4227,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25a79a0@0" ObjectIDND1="49635@x" ObjectIDND2="49634@x" ObjectIDZND0="g_25a6bf0@0" Pin0InfoVect0LinkObjId="g_25a6bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25a79a0_0" Pin1InfoVect1LinkObjId="SW-319516_0" Pin1InfoVect2LinkObjId="SW-319515_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-321 4227,-321 4227,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-426 4430,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49637@1" ObjectIDZND0="49636@1" Pin0InfoVect0LinkObjId="SW-319518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319519_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-426 4430,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e0fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-356 4430,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49638@1" ObjectIDZND0="49636@0" Pin0InfoVect0LinkObjId="SW-319518_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319519_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-356 4430,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e1220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4398,-307 4398,-322 4430,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49639@0" ObjectIDZND0="g_262de90@0" ObjectIDZND1="g_262d0e0@0" ObjectIDZND2="49638@x" Pin0InfoVect0LinkObjId="g_262de90_0" Pin0InfoVect1LinkObjId="g_262d0e0_0" Pin0InfoVect2LinkObjId="SW-319519_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4398,-307 4398,-322 4430,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e1480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-307 4430,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_262de90@1" ObjectIDZND0="49639@x" ObjectIDZND1="g_262d0e0@0" ObjectIDZND2="49638@x" Pin0InfoVect0LinkObjId="SW-319520_0" Pin0InfoVect1LinkObjId="g_262d0e0_0" Pin0InfoVect2LinkObjId="SW-319519_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262de90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-307 4430,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e16e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-322 4430,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_262de90@0" ObjectIDND1="49639@x" ObjectIDND2="g_262d0e0@0" ObjectIDZND0="49638@0" Pin0InfoVect0LinkObjId="SW-319519_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_262de90_0" Pin1InfoVect1LinkObjId="SW-319520_0" Pin1InfoVect2LinkObjId="g_262d0e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-322 4430,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25e1940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-322 4466,-322 4466,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_262de90@0" ObjectIDND1="49639@x" ObjectIDND2="49638@x" ObjectIDZND0="g_262d0e0@0" Pin0InfoVect0LinkObjId="g_262d0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_262de90_0" Pin1InfoVect1LinkObjId="SW-319520_0" Pin1InfoVect2LinkObjId="SW-319519_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-322 4466,-322 4466,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b85e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-425 4660,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49627@1" ObjectIDZND0="49626@1" Pin0InfoVect0LinkObjId="SW-319508_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-425 4660,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b8840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-355 4660,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49628@1" ObjectIDZND0="49626@0" Pin0InfoVect0LinkObjId="SW-319508_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319509_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-355 4660,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4628,-306 4628,-321 4660,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49631@0" ObjectIDZND0="g_25e3400@0" ObjectIDZND1="g_25e2650@0" ObjectIDZND2="49628@x" Pin0InfoVect0LinkObjId="g_25e3400_0" Pin0InfoVect1LinkObjId="g_25e2650_0" Pin0InfoVect2LinkObjId="SW-319509_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319512_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4628,-306 4628,-321 4660,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-306 4660,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_25e3400@1" ObjectIDZND0="g_25e2650@0" ObjectIDZND1="49628@x" ObjectIDZND2="49631@x" Pin0InfoVect0LinkObjId="g_25e2650_0" Pin0InfoVect1LinkObjId="SW-319509_0" Pin0InfoVect2LinkObjId="SW-319512_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25e3400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-306 4660,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b8f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-321 4660,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25e3400@0" ObjectIDND1="g_25e2650@0" ObjectIDND2="49631@x" ObjectIDZND0="49628@0" Pin0InfoVect0LinkObjId="SW-319509_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e3400_0" Pin1InfoVect1LinkObjId="g_25e2650_0" Pin1InfoVect2LinkObjId="SW-319512_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-321 4660,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25b91c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-321 4696,-321 4696,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25e3400@0" ObjectIDND1="49628@x" ObjectIDND2="49631@x" ObjectIDZND0="g_25e2650@0" Pin0InfoVect0LinkObjId="g_25e2650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25e3400_0" Pin1InfoVect1LinkObjId="SW-319509_0" Pin1InfoVect2LinkObjId="SW-319512_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-321 4696,-321 4696,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25297c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-426 4882,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49645@1" ObjectIDZND0="49644@1" Pin0InfoVect0LinkObjId="SW-319528_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-426 4882,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2529a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-355 4882,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="49646@1" ObjectIDZND0="49644@0" Pin0InfoVect0LinkObjId="SW-319528_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319529_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-355 4882,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2529c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4850,-306 4850,-321 4882,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="49647@0" ObjectIDZND0="g_25aa5c0@0" ObjectIDZND1="g_25a98b0@0" ObjectIDZND2="49646@x" Pin0InfoVect0LinkObjId="g_25aa5c0_0" Pin0InfoVect1LinkObjId="g_25a98b0_0" Pin0InfoVect2LinkObjId="SW-319529_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4850,-306 4850,-321 4882,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2529ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-306 4882,-321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_25aa5c0@1" ObjectIDZND0="49647@x" ObjectIDZND1="g_25a98b0@0" ObjectIDZND2="49646@x" Pin0InfoVect0LinkObjId="SW-319530_0" Pin0InfoVect1LinkObjId="g_25a98b0_0" Pin0InfoVect2LinkObjId="SW-319529_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25aa5c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-306 4882,-321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_252a140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-321 4882,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_25aa5c0@0" ObjectIDND1="49647@x" ObjectIDND2="g_25a98b0@0" ObjectIDZND0="49646@0" Pin0InfoVect0LinkObjId="SW-319529_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25aa5c0_0" Pin1InfoVect1LinkObjId="SW-319530_0" Pin1InfoVect2LinkObjId="g_25a98b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-321 4882,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_252a3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-321 4918,-321 4918,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25aa5c0@0" ObjectIDND1="49647@x" ObjectIDND2="49646@x" ObjectIDZND0="g_25a98b0@0" Pin0InfoVect0LinkObjId="g_25a98b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25aa5c0_0" Pin1InfoVect1LinkObjId="SW-319530_0" Pin1InfoVect2LinkObjId="SW-319529_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-321 4918,-321 4918,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_252d430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-253 3951,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2583b90@0" ObjectIDZND0="g_252b680@1" Pin0InfoVect0LinkObjId="g_252b680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2583b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-253 3951,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_252e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-253 4191,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_25a79a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a79a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-253 4191,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_252fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-254 4430,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_262de90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262de90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-254 4430,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2588c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4677,-167 4660,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="49629@0" ObjectIDZND0="49630@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-319511_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4677,-167 4660,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25895e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-193 4660,-167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="49630@0" ObjectIDZND0="49629@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-319510_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319511_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-193 4660,-167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2589810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-167 4660,-139 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="49629@x" ObjectIDND1="49630@x" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-319510_0" Pin1InfoVect1LinkObjId="SW-319511_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-167 4660,-139 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2589a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4661,-211 4693,-211 4693,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4661,-211 4693,-211 4693,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258a8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-253 4882,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_25aa5c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25aa5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-253 4882,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2667550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3740,-532 3740,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49620@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319502_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3740,-532 3740,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3710,-442 3710,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49641@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3710,-442 3710,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-442 3951,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49623@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319504_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-442 3951,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-442 4191,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49633@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319515_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-442 4191,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258f6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4430,-443 4430,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49637@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4430,-443 4430,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_258fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-442 4660,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49627@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319509_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-442 4660,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25906b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-442 4882,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49645@0" ObjectIDZND0="49607@0" Pin0InfoVect0LinkObjId="g_25cd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319529_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-442 4882,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2590ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-229 4660,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="49630@1" ObjectIDZND0="g_25e3400@0" Pin0InfoVect0LinkObjId="g_25e3400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-319511_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-229 4660,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32217b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-106 3668,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2fcd2a0@0" ObjectIDZND0="g_25f7fd0@1" Pin0InfoVect0LinkObjId="g_25f7fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fcd2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-106 3668,-116 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4536" cy="-1054" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="4264" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="3740" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="3710" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="3951" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="4191" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="4430" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="4660" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49607" cx="4882" cy="-482" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-319393" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3460.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49566" ObjectName="DYN-CX_KJZGF"/>
     <cge:Meas_Ref ObjectId="319393"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26afd20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_22b2d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_26c6160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3277.000000 -1166.500000) translate(0,16)">孔家庄光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4186.000000 -778.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -776.000000) translate(0,15)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -776.000000) translate(0,33)">SZ18-52000/110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -776.000000) translate(0,51)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -776.000000) translate(0,69)">115±8х1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2642760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4319.000000 -776.000000) translate(0,87)">Uk=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_25cd2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3472.000000 -1037.000000) translate(0,16)">AGC/AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25ddac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -1037.000000) translate(0,15)">220kV紫溪变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25de050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4382.000000 -1130.000000) translate(0,15)">紫孔线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25f99f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3578.000000 -77.000000) translate(0,15)">接地变及小电阻成套接地装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2582ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3664.000000 -781.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_252d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3892.000000 -77.000000) translate(0,15)">1号站用变400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_252f400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4154.000000 -79.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2530960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -79.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258ab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -92.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258ab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -92.000000) translate(0,33)">      ±11MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_258ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4850.000000 -94.000000) translate(0,15)">#1-1储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2591100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -921.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2591730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4205.000000 -915.000000) translate(0,12)">10110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2591970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -850.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2591bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -832.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2591e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -712.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2592270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -978.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25924b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4272.000000 -983.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2592850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4218.000000 -1036.000000) translate(0,12)">10167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2592a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -576.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2592cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3752.000000 -574.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25931f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3509.000000 -502.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2593610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3719.000000 -398.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2593aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -398.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2593d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -398.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2594170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4439.000000 -399.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25943b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4669.000000 -398.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25945f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4610.000000 -224.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2594830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4697.000000 -152.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2594a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4891.000000 -398.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2597b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -290.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e22f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -296.000000) translate(0,12)">36667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -295.000000) translate(0,12)">36560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -293.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4101.000000 -293.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3868.000000 -292.000000) translate(0,12)">36267</text>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4183,-708 4178,-718 4188,-718 4183,-708 " stroke="rgb(170,85,127)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4183,-696 4178,-686 4188,-686 4183,-696 " stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2672f00">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4242.000000 -1051.000000)" xlink:href="#currentTransformer:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -676.018519)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -676.018519)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3255.538462 -1016.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3253.538462 -971.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="182" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="182" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3445,-1051 3442,-1054 3442,-1000 3445,-1003 3445,-1051" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3445,-1051 3442,-1054 3591,-1054 3588,-1051 3445,-1051" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(112,119,119)" points="3445,-1003 3442,-1000 3591,-1000 3588,-1003 3445,-1003" stroke="rgb(112,119,119)"/>
     <polygon fill="rgb(112,119,119)" points="3588,-1051 3591,-1054 3591,-1000 3588,-1003 3588,-1051" stroke="rgb(112,119,119)"/>
     <rect fill="rgb(224,238,238)" height="48" stroke="rgb(224,238,238)" width="143" x="3445" y="-1051"/>
     <rect fill="none" height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3445" y="-1051"/>
    </a>
   <metadata/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2642ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 890.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2642c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 875.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2642e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 860.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2642f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 586.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2643130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 571.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26432e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4352.000000 556.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2643580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 52.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2643730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3580.000000 37.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26438e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 22.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d1490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.000000 560.666667) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 608.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 576.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3511.000000 592.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3517.000000 531.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ca6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3503.000000 545.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cdc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 955.666667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cde70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 971.333333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cdfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 912.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ce150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4333.000000 941.666667) translate(0,12)">U  (V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25ce2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 927.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25cda90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4327.000000 986.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_KJZGF.CX_KJZGF_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3509,-482 5019,-482 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49607" ObjectName="BS-CX_KJZGF.CX_KJZGF_3M"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   <polyline fill="none" opacity="0" points="3509,-482 5019,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4498,-1054 4578,-1054 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4498,-1054 4578,-1054 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -50.000000) translate(0,12)">319461.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319461" ObjectName="CX_KJZGF.CX_KJZGF_361BK:F"/>
     <cge:PSR_Ref ObjectID="49640"/>
     <cge:Term_Ref ObjectID="49356"/>
    <cge:TPSR_Ref TObjectID="49640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319462" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -50.000000) translate(0,27)">319462.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319462" ObjectName="CX_KJZGF.CX_KJZGF_361BK:F"/>
     <cge:PSR_Ref ObjectID="49640"/>
     <cge:Term_Ref ObjectID="49356"/>
    <cge:TPSR_Ref TObjectID="49640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -50.000000) translate(0,42)">319463.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319463" ObjectName="CX_KJZGF.CX_KJZGF_361BK:F"/>
     <cge:PSR_Ref ObjectID="49640"/>
     <cge:Term_Ref ObjectID="49356"/>
    <cge:TPSR_Ref TObjectID="49640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -46.000000) translate(0,12)">319437.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319437" ObjectName="CX_KJZGF.CX_KJZGF_362BK:F"/>
     <cge:PSR_Ref ObjectID="49622"/>
     <cge:Term_Ref ObjectID="49147"/>
    <cge:TPSR_Ref TObjectID="49622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -46.000000) translate(0,27)">319438.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319438" ObjectName="CX_KJZGF.CX_KJZGF_362BK:F"/>
     <cge:PSR_Ref ObjectID="49622"/>
     <cge:Term_Ref ObjectID="49147"/>
    <cge:TPSR_Ref TObjectID="49622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3941.000000 -46.000000) translate(0,42)">319439.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319439" ObjectName="CX_KJZGF.CX_KJZGF_362BK:F"/>
     <cge:PSR_Ref ObjectID="49622"/>
     <cge:Term_Ref ObjectID="49147"/>
    <cge:TPSR_Ref TObjectID="49622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -48.000000) translate(0,12)">319449.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319449" ObjectName="CX_KJZGF.CX_KJZGF_363BK:F"/>
     <cge:PSR_Ref ObjectID="49632"/>
     <cge:Term_Ref ObjectID="49195"/>
    <cge:TPSR_Ref TObjectID="49632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -48.000000) translate(0,27)">319450.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319450" ObjectName="CX_KJZGF.CX_KJZGF_363BK:F"/>
     <cge:PSR_Ref ObjectID="49632"/>
     <cge:Term_Ref ObjectID="49195"/>
    <cge:TPSR_Ref TObjectID="49632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -48.000000) translate(0,42)">319451.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319451" ObjectName="CX_KJZGF.CX_KJZGF_363BK:F"/>
     <cge:PSR_Ref ObjectID="49632"/>
     <cge:Term_Ref ObjectID="49195"/>
    <cge:TPSR_Ref TObjectID="49632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319455" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -52.000000) translate(0,12)">319455.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319455" ObjectName="CX_KJZGF.CX_KJZGF_364BK:F"/>
     <cge:PSR_Ref ObjectID="49636"/>
     <cge:Term_Ref ObjectID="49252"/>
    <cge:TPSR_Ref TObjectID="49636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -52.000000) translate(0,27)">319456.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319456" ObjectName="CX_KJZGF.CX_KJZGF_364BK:F"/>
     <cge:PSR_Ref ObjectID="49636"/>
     <cge:Term_Ref ObjectID="49252"/>
    <cge:TPSR_Ref TObjectID="49636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -52.000000) translate(0,42)">319457.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319457" ObjectName="CX_KJZGF.CX_KJZGF_364BK:F"/>
     <cge:PSR_Ref ObjectID="49636"/>
     <cge:Term_Ref ObjectID="49252"/>
    <cge:TPSR_Ref TObjectID="49636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4652.000000 -53.000000) translate(0,12)">319443.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319443" ObjectName="CX_KJZGF.CX_KJZGF_365BK:F"/>
     <cge:PSR_Ref ObjectID="49626"/>
     <cge:Term_Ref ObjectID="49155"/>
    <cge:TPSR_Ref TObjectID="49626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319444" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4652.000000 -53.000000) translate(0,27)">319444.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319444" ObjectName="CX_KJZGF.CX_KJZGF_365BK:F"/>
     <cge:PSR_Ref ObjectID="49626"/>
     <cge:Term_Ref ObjectID="49155"/>
    <cge:TPSR_Ref TObjectID="49626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4652.000000 -53.000000) translate(0,42)">319445.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319445" ObjectName="CX_KJZGF.CX_KJZGF_365BK:F"/>
     <cge:PSR_Ref ObjectID="49626"/>
     <cge:Term_Ref ObjectID="49155"/>
    <cge:TPSR_Ref TObjectID="49626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -54.000000) translate(0,12)">319467.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319467" ObjectName="CX_KJZGF.CX_KJZGF_366BK:F"/>
     <cge:PSR_Ref ObjectID="49644"/>
     <cge:Term_Ref ObjectID="49364"/>
    <cge:TPSR_Ref TObjectID="49644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319468" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -54.000000) translate(0,27)">319468.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319468" ObjectName="CX_KJZGF.CX_KJZGF_366BK:F"/>
     <cge:PSR_Ref ObjectID="49644"/>
     <cge:Term_Ref ObjectID="49364"/>
    <cge:TPSR_Ref TObjectID="49644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4878.000000 -54.000000) translate(0,42)">319469.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319469" ObjectName="CX_KJZGF.CX_KJZGF_366BK:F"/>
     <cge:PSR_Ref ObjectID="49644"/>
     <cge:Term_Ref ObjectID="49364"/>
    <cge:TPSR_Ref TObjectID="49644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -587.000000) translate(0,12)">319422.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319422" ObjectName="CX_KJZGF.CX_KJZGF_301BK:F"/>
     <cge:PSR_Ref ObjectID="49617"/>
     <cge:Term_Ref ObjectID="49137"/>
    <cge:TPSR_Ref TObjectID="49617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319423" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -587.000000) translate(0,27)">319423.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319423" ObjectName="CX_KJZGF.CX_KJZGF_301BK:F"/>
     <cge:PSR_Ref ObjectID="49617"/>
     <cge:Term_Ref ObjectID="49137"/>
    <cge:TPSR_Ref TObjectID="49617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319419" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -587.000000) translate(0,42)">319419.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319419" ObjectName="CX_KJZGF.CX_KJZGF_301BK:F"/>
     <cge:PSR_Ref ObjectID="49617"/>
     <cge:Term_Ref ObjectID="49137"/>
    <cge:TPSR_Ref TObjectID="49617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-319414" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -890.000000) translate(0,12)">319414.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319414" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-319415" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -890.000000) translate(0,27)">319415.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319415" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-319411" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -890.000000) translate(0,42)">319411.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319411" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-319405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,12)">319405.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319405" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-319406" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,27)">319406.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319406" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-319407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,42)">319407.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319407" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-319418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,57)">319418.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319418" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-319408" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,72)">319408.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319408" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-319417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -984.000000) translate(0,87)">319417.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319417" ObjectName="CX_KJZGF.CX_KJZGF_101BK:F"/>
     <cge:PSR_Ref ObjectID="49609"/>
     <cge:Term_Ref ObjectID="49121"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-319429" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,12)">319429.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319429" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-319430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,27)">319430.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319430" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-319431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,42)">319431.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319431" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-319436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,57)">319436.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319436" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-319432" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,72)">319432.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319432" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-319435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3570.000000 -606.000000) translate(0,87)">319435.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="319435" ObjectName="CX_KJZGF.CX_KJZGF_3M:F"/>
     <cge:PSR_Ref ObjectID="49607"/>
     <cge:Term_Ref ObjectID="49118"/>
    <cge:TPSR_Ref TObjectID="49607"/></metadata>
   </g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-319491">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4256.000000 -892.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49609" ObjectName="SW-CX_KJZGF.CX_KJZGF_101BK"/>
     <cge:Meas_Ref ObjectId="319491"/>
    <cge:TPSR_Ref TObjectID="49609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319500">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4255.000000 -549.018519)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49617" ObjectName="SW-CX_KJZGF.CX_KJZGF_301BK"/>
     <cge:Meas_Ref ObjectId="319500"/>
    <cge:TPSR_Ref TObjectID="49617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -1082.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319523">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49640" ObjectName="SW-CX_KJZGF.CX_KJZGF_361BK"/>
     <cge:Meas_Ref ObjectId="319523"/>
    <cge:TPSR_Ref TObjectID="49640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319503">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49622" ObjectName="SW-CX_KJZGF.CX_KJZGF_362BK"/>
     <cge:Meas_Ref ObjectId="319503"/>
    <cge:TPSR_Ref TObjectID="49622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319514">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49632" ObjectName="SW-CX_KJZGF.CX_KJZGF_363BK"/>
     <cge:Meas_Ref ObjectId="319514"/>
    <cge:TPSR_Ref TObjectID="49632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319518">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 -370.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49636" ObjectName="SW-CX_KJZGF.CX_KJZGF_364BK"/>
     <cge:Meas_Ref ObjectId="319518"/>
    <cge:TPSR_Ref TObjectID="49636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319508">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4651.000000 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49626" ObjectName="SW-CX_KJZGF.CX_KJZGF_365BK"/>
     <cge:Meas_Ref ObjectId="319508"/>
    <cge:TPSR_Ref TObjectID="49626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-319528">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -369.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49644" ObjectName="SW-CX_KJZGF.CX_KJZGF_366BK"/>
     <cge:Meas_Ref ObjectId="319528"/>
    <cge:TPSR_Ref TObjectID="49644"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="182" x="3246" y="-1177"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
   <g href="AVC孔家庄.svg" style="fill-opacity:0"><rect height="48" qtmmishow="hidden" stroke="rgb(255,0,0)" width="143" x="3445" y="-1051"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2699ff0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -671.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2664ba0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4306.000000 -576.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2667090">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2610690">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2675bb0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -1041.018519)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2600ce0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3693.000000 -195.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f7fd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 -111.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257f820">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3699.000000 -698.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2580410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -637.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25832d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2583b90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a6bf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a79a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262d0e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -250.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262de90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4425.000000 -249.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e2650">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e3400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a98b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4911.000000 -249.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aa5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -248.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_252b680">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3937.000000 -105.000000)" xlink:href="#lightningRod:shape186"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36b7aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -109.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_KJZGF"/>
</svg>