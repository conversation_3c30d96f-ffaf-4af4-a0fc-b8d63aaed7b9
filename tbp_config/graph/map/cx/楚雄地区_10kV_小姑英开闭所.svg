<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-58" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3119 -1198 2133 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="24" y2="24"/>
    <ellipse cx="14" cy="9" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="8" cy="17" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="24" y1="21" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="19" y1="21" y2="16"/>
    <ellipse cx="19" cy="17" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ae8590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ae8db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ae9520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ae9c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aea800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aeb3c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aebc10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aec3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aecb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aed570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aedeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1aee560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aeffc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af0c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af1450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af1e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af3370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af3e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af4610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af5000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af61e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af6b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1af7650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1afc850" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1afd4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1af9350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1afa8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1afb630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b093b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1aff380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="2143" x="3114" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3121" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40904">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3810.013739 -658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6724" ObjectName="SW-CX_XGY.CX_XGY_0901SW"/>
     <cge:Meas_Ref ObjectId="40904"/>
    <cge:TPSR_Ref TObjectID="6724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40910">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6730" ObjectName="SW-CX_XGY.CX_XGY_0751SW"/>
     <cge:Meas_Ref ObjectId="40910"/>
    <cge:TPSR_Ref TObjectID="6730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6731" ObjectName="SW-CX_XGY.CX_XGY_0756SW"/>
     <cge:Meas_Ref ObjectId="40911"/>
    <cge:TPSR_Ref TObjectID="6731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3664.500000 -553.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6732" ObjectName="SW-CX_XGY.CX_XGY_07517SW"/>
     <cge:Meas_Ref ObjectId="87237"/>
    <cge:TPSR_Ref TObjectID="6732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40918">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.013739 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6738" ObjectName="SW-CX_XGY.CX_XGY_0711SW"/>
     <cge:Meas_Ref ObjectId="40918"/>
    <cge:TPSR_Ref TObjectID="6738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4206.000000 -711.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11092" ObjectName="SW-CX_XGY.CX_XGY_07117SW"/>
     <cge:Meas_Ref ObjectId="87241"/>
    <cge:TPSR_Ref TObjectID="11092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4207.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11093" ObjectName="SW-CX_XGY.CX_XGY_07167SW"/>
     <cge:Meas_Ref ObjectId="58679"/>
    <cge:TPSR_Ref TObjectID="11093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -583.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6726" ObjectName="SW-CX_XGY.CX_XGY_0741SW"/>
     <cge:Meas_Ref ObjectId="40906"/>
    <cge:TPSR_Ref TObjectID="6726"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40907">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3826.000000 -465.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6727" ObjectName="SW-CX_XGY.CX_XGY_0746SW"/>
     <cge:Meas_Ref ObjectId="40907"/>
    <cge:TPSR_Ref TObjectID="6727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3872.500000 -560.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6728" ObjectName="SW-CX_XGY.CX_XGY_07417SW"/>
     <cge:Meas_Ref ObjectId="87236"/>
    <cge:TPSR_Ref TObjectID="6728"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40902">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6722" ObjectName="SW-CX_XGY.CX_XGY_0731SW"/>
     <cge:Meas_Ref ObjectId="40902"/>
    <cge:TPSR_Ref TObjectID="6722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40903">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4050.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6723" ObjectName="SW-CX_XGY.CX_XGY_0736SW"/>
     <cge:Meas_Ref ObjectId="40903"/>
    <cge:TPSR_Ref TObjectID="6723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87235">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4096.500000 -553.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11096" ObjectName="SW-CX_XGY.CX_XGY_07317SW"/>
     <cge:Meas_Ref ObjectId="87235"/>
    <cge:TPSR_Ref TObjectID="11096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40940">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5058.013739 -661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6756" ObjectName="SW-CX_XGY.CX_XGY_0902SW"/>
     <cge:Meas_Ref ObjectId="40940"/>
    <cge:TPSR_Ref TObjectID="6756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4258.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6734" ObjectName="SW-CX_XGY.CX_XGY_0721SW"/>
     <cge:Meas_Ref ObjectId="40914"/>
    <cge:TPSR_Ref TObjectID="6734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4258.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6735" ObjectName="SW-CX_XGY.CX_XGY_0726SW"/>
     <cge:Meas_Ref ObjectId="40915"/>
    <cge:TPSR_Ref TObjectID="6735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87234">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4304.500000 -553.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6736" ObjectName="SW-CX_XGY.CX_XGY_07217SW"/>
     <cge:Meas_Ref ObjectId="87234"/>
    <cge:TPSR_Ref TObjectID="6736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40928">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.013739 -658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6744" ObjectName="SW-CX_XGY.CX_XGY_0811SW"/>
     <cge:Meas_Ref ObjectId="40928"/>
    <cge:TPSR_Ref TObjectID="6744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4735.000000 -709.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11094" ObjectName="SW-CX_XGY.CX_XGY_08117SW"/>
     <cge:Meas_Ref ObjectId="87242"/>
    <cge:TPSR_Ref TObjectID="11094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4736.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11095" ObjectName="SW-CX_XGY.CX_XGY_08167SW"/>
     <cge:Meas_Ref ObjectId="58678"/>
    <cge:TPSR_Ref TObjectID="11095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40930">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6746" ObjectName="SW-CX_XGY.CX_XGY_0821SW"/>
     <cge:Meas_Ref ObjectId="40930"/>
    <cge:TPSR_Ref TObjectID="6746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40931">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6747" ObjectName="SW-CX_XGY.CX_XGY_0826SW"/>
     <cge:Meas_Ref ObjectId="40931"/>
    <cge:TPSR_Ref TObjectID="6747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87238">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4704.500000 -552.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6748" ObjectName="SW-CX_XGY.CX_XGY_08217SW"/>
     <cge:Meas_Ref ObjectId="87238"/>
    <cge:TPSR_Ref TObjectID="6748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40938">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -575.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6754" ObjectName="SW-CX_XGY.CX_XGY_0831SW"/>
     <cge:Meas_Ref ObjectId="40938"/>
    <cge:TPSR_Ref TObjectID="6754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40939">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6755" ObjectName="SW-CX_XGY.CX_XGY_0836SW"/>
     <cge:Meas_Ref ObjectId="40939"/>
    <cge:TPSR_Ref TObjectID="6755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87239">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4929.500000 -552.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11099" ObjectName="SW-CX_XGY.CX_XGY_08317SW"/>
     <cge:Meas_Ref ObjectId="87239"/>
    <cge:TPSR_Ref TObjectID="11099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40934">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 -573.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6750" ObjectName="SW-CX_XGY.CX_XGY_0841SW"/>
     <cge:Meas_Ref ObjectId="40934"/>
    <cge:TPSR_Ref TObjectID="6750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40935">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5105.000000 -455.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6751" ObjectName="SW-CX_XGY.CX_XGY_0846SW"/>
     <cge:Meas_Ref ObjectId="40935"/>
    <cge:TPSR_Ref TObjectID="6751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-87240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5152.500000 -550.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6752" ObjectName="SW-CX_XGY.CX_XGY_08417SW"/>
     <cge:Meas_Ref ObjectId="87240"/>
    <cge:TPSR_Ref TObjectID="6752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40920">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6740" ObjectName="SW-CX_XGY.CX_XGY_0121SW"/>
     <cge:Meas_Ref ObjectId="40920"/>
    <cge:TPSR_Ref TObjectID="6740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40921">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -577.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6741" ObjectName="SW-CX_XGY.CX_XGY_0122SW"/>
     <cge:Meas_Ref ObjectId="40921"/>
    <cge:TPSR_Ref TObjectID="6741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40922">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4404.000000 -499.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6742" ObjectName="SW-CX_XGY.CX_XGY_01217SW"/>
     <cge:Meas_Ref ObjectId="40922"/>
    <cge:TPSR_Ref TObjectID="6742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4546.000000 -497.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11098" ObjectName="SW-CX_XGY.CX_XGY_01227SW"/>
     <cge:Meas_Ref ObjectId="58683"/>
    <cge:TPSR_Ref TObjectID="11098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3761.500000 -714.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11090" ObjectName="SW-CX_XGY.CX_XGY_09017SW"/>
     <cge:Meas_Ref ObjectId="58681"/>
    <cge:TPSR_Ref TObjectID="11090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5018.500000 -717.500000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11091" ObjectName="SW-CX_XGY.CX_XGY_09027SW"/>
     <cge:Meas_Ref ObjectId="58680"/>
    <cge:TPSR_Ref TObjectID="11091"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -838.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4163.000000 -838.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -836.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4692.000000 -836.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_164f1f0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3864.513739 -797.500000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_158abf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -847.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1667250">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5112.513739 -801.500000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_167d410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.000000 -851.000000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_157fb80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3783.000000 -798.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1580530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5031.000000 -798.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3230.500000 -1117.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40849" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6737"/>
     <cge:Term_Ref ObjectID="9788"/>
    <cge:TPSR_Ref TObjectID="6737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40850" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40850" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6737"/>
     <cge:Term_Ref ObjectID="9788"/>
    <cge:TPSR_Ref TObjectID="6737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40845" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6737"/>
     <cge:Term_Ref ObjectID="9788"/>
    <cge:TPSR_Ref TObjectID="6737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40865" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40865" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6743"/>
     <cge:Term_Ref ObjectID="9800"/>
    <cge:TPSR_Ref TObjectID="6743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40866" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6743"/>
     <cge:Term_Ref ObjectID="9800"/>
    <cge:TPSR_Ref TObjectID="6743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40861" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6743"/>
     <cge:Term_Ref ObjectID="9800"/>
    <cge:TPSR_Ref TObjectID="6743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40806" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -754.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40806" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6719"/>
     <cge:Term_Ref ObjectID="9754"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40807" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -754.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40807" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6719"/>
     <cge:Term_Ref ObjectID="9754"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -754.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6719"/>
     <cge:Term_Ref ObjectID="9754"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-40809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -754.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6719"/>
     <cge:Term_Ref ObjectID="9754"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-40818" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3593.000000 -754.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40818" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6719"/>
     <cge:Term_Ref ObjectID="9754"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40825" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3836.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40825" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6725"/>
     <cge:Term_Ref ObjectID="9764"/>
    <cge:TPSR_Ref TObjectID="6725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40826" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3836.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6725"/>
     <cge:Term_Ref ObjectID="9764"/>
    <cge:TPSR_Ref TObjectID="6725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40821" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3836.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6725"/>
     <cge:Term_Ref ObjectID="9764"/>
    <cge:TPSR_Ref TObjectID="6725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40833" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6729"/>
     <cge:Term_Ref ObjectID="9772"/>
    <cge:TPSR_Ref TObjectID="6729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40834" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6729"/>
     <cge:Term_Ref ObjectID="9772"/>
    <cge:TPSR_Ref TObjectID="6729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40829" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3630.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40829" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6729"/>
     <cge:Term_Ref ObjectID="9772"/>
    <cge:TPSR_Ref TObjectID="6729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40814" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6721"/>
     <cge:Term_Ref ObjectID="9756"/>
    <cge:TPSR_Ref TObjectID="6721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40815" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6721"/>
     <cge:Term_Ref ObjectID="9756"/>
    <cge:TPSR_Ref TObjectID="6721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40810" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6721"/>
     <cge:Term_Ref ObjectID="9756"/>
    <cge:TPSR_Ref TObjectID="6721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40841" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40841" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6733"/>
     <cge:Term_Ref ObjectID="9780"/>
    <cge:TPSR_Ref TObjectID="6733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40842" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6733"/>
     <cge:Term_Ref ObjectID="9780"/>
    <cge:TPSR_Ref TObjectID="6733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40837" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4271.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40837" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6733"/>
     <cge:Term_Ref ObjectID="9780"/>
    <cge:TPSR_Ref TObjectID="6733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40857" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40857" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6739"/>
     <cge:Term_Ref ObjectID="9792"/>
    <cge:TPSR_Ref TObjectID="6739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40858" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6739"/>
     <cge:Term_Ref ObjectID="9792"/>
    <cge:TPSR_Ref TObjectID="6739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40853" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4491.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6739"/>
     <cge:Term_Ref ObjectID="9792"/>
    <cge:TPSR_Ref TObjectID="6739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6745"/>
     <cge:Term_Ref ObjectID="9804"/>
    <cge:TPSR_Ref TObjectID="6745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40874" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6745"/>
     <cge:Term_Ref ObjectID="9804"/>
    <cge:TPSR_Ref TObjectID="6745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40869" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6745"/>
     <cge:Term_Ref ObjectID="9804"/>
    <cge:TPSR_Ref TObjectID="6745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40889" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6753"/>
     <cge:Term_Ref ObjectID="9820"/>
    <cge:TPSR_Ref TObjectID="6753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40890" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6753"/>
     <cge:Term_Ref ObjectID="9820"/>
    <cge:TPSR_Ref TObjectID="6753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40885" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6753"/>
     <cge:Term_Ref ObjectID="9820"/>
    <cge:TPSR_Ref TObjectID="6753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-40881" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -342.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6749"/>
     <cge:Term_Ref ObjectID="9812"/>
    <cge:TPSR_Ref TObjectID="6749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-40882" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -342.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6749"/>
     <cge:Term_Ref ObjectID="9812"/>
    <cge:TPSR_Ref TObjectID="6749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-40877" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5120.000000 -342.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6749"/>
     <cge:Term_Ref ObjectID="9812"/>
    <cge:TPSR_Ref TObjectID="6749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-40893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -737.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6720"/>
     <cge:Term_Ref ObjectID="9755"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-40894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -737.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6720"/>
     <cge:Term_Ref ObjectID="9755"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-40895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -737.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6720"/>
     <cge:Term_Ref ObjectID="9755"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-40896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -737.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6720"/>
     <cge:Term_Ref ObjectID="9755"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-40897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5221.000000 -737.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="40897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6720"/>
     <cge:Term_Ref ObjectID="9755"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3242" y="-1176"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3242" y="-1176"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3194" y="-1193"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3194" y="-1193"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3642" y="-558"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3642" y="-558"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5129" y="-554"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5129" y="-554"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3850" y="-564"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3850" y="-564"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4074" y="-558"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4074" y="-558"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4282" y="-558"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4282" y="-558"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4475" y="-598"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4475" y="-598"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4681" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4681" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4906" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4906" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4714" y="-778"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4714" y="-778"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4185" y="-780"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4185" y="-780"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3242" y="-1176"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3194" y="-1193"/></g>
   <g href="10kV小姑英开闭所10kV环城东路线075断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3642" y="-558"/></g>
   <g href="10kV小姑英开闭所10kV东兴路线084断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5129" y="-554"/></g>
   <g href="10kV小姑英开闭所10kV城东线074断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3850" y="-564"/></g>
   <g href="10kV小姑英开闭所10kV东郊线073断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4074" y="-558"/></g>
   <g href="10kV小姑英开闭所10kV南东线072断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4282" y="-558"/></g>
   <g href="10kV小姑英开闭所10kV母联012断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4475" y="-598"/></g>
   <g href="10kV小姑英开闭所10kV蔡家冲线082断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4681" y="-556"/></g>
   <g href="10kV小姑英开闭所10kV楚城Ⅲ回线083断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4906" y="-556"/></g>
   <g href="10kV小姑英开闭所10kV2号变081断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4714" y="-778"/></g>
   <g href="10kV小姑英开闭所10kV1号变071断路器间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4185" y="-780"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3624.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6729" ObjectName="SW-CX_XGY.CX_XGY_075BK"/>
     <cge:Meas_Ref ObjectId="40909"/>
    <cge:TPSR_Ref TObjectID="6729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -751.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6737" ObjectName="SW-CX_XGY.CX_XGY_071BK"/>
     <cge:Meas_Ref ObjectId="40917"/>
    <cge:TPSR_Ref TObjectID="6737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40905">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6725" ObjectName="SW-CX_XGY.CX_XGY_074BK"/>
     <cge:Meas_Ref ObjectId="40905"/>
    <cge:TPSR_Ref TObjectID="6725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40901">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6721" ObjectName="SW-CX_XGY.CX_XGY_073BK"/>
     <cge:Meas_Ref ObjectId="40901"/>
    <cge:TPSR_Ref TObjectID="6721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40913">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6733" ObjectName="SW-CX_XGY.CX_XGY_072BK"/>
     <cge:Meas_Ref ObjectId="40913"/>
    <cge:TPSR_Ref TObjectID="6733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40927">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4696.000000 -749.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6743" ObjectName="SW-CX_XGY.CX_XGY_081BK"/>
     <cge:Meas_Ref ObjectId="40927"/>
    <cge:TPSR_Ref TObjectID="6743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40929">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4663.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6745" ObjectName="SW-CX_XGY.CX_XGY_082BK"/>
     <cge:Meas_Ref ObjectId="40929"/>
    <cge:TPSR_Ref TObjectID="6745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40937">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6753" ObjectName="SW-CX_XGY.CX_XGY_083BK"/>
     <cge:Meas_Ref ObjectId="40937"/>
    <cge:TPSR_Ref TObjectID="6753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40933">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5111.000000 -525.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6749" ObjectName="SW-CX_XGY.CX_XGY_084BK"/>
     <cge:Meas_Ref ObjectId="40933"/>
    <cge:TPSR_Ref TObjectID="6749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4509.000000 -565.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6739" ObjectName="SW-CX_XGY.CX_XGY_012BK"/>
     <cge:Meas_Ref ObjectId="40919"/>
    <cge:TPSR_Ref TObjectID="6739"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_164e5f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3725.500000 -569.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_158bb90" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3733.000000 -750.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1669a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4267.000000 -727.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1691550" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4268.000000 -798.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1634b80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3933.500000 -576.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1679b60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4157.500000 -569.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1666330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4365.500000 -569.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_167ea70" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4981.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_160b090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4796.000000 -725.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b9510" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4797.000000 -796.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1530940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4765.500000 -568.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b6d10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4990.500000 -568.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_159f710" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 5213.500000 -566.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c2ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -470.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c38d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -470.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="3633" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="4419" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="3841" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="4065" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="4273" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="4672" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="4897" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="5120" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="4561" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="5073" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6720" cx="4705" cy="-651" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="4176" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6719" cx="3825" cy="-652" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16887a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3744.000000 -902.000000) translate(0,15)">10kV I段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_164ee10" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3575.000000 -383.000000) translate(0,15)">10kV环城东路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f81b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3802.000000 -383.000000) translate(0,15)">10kV城东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_167a500" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4022.000000 -383.000000) translate(0,15)">10kV东郊线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_167ab90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4992.000000 -902.000000) translate(0,15)">10kV II段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1666e80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4232.000000 -383.000000) translate(0,15)">10kV南东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1644580" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4610.000000 -383.000000) translate(0,15)">10kV蔡家冲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15b79a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4823.000000 -383.500000) translate(0,15)">10kV楚城III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15a03a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 5065.000000 -383.000000) translate(0,15)">10kV东兴路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1531cc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4145.000000 -905.000000) translate(0,15)">10kV1号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15323c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4673.000000 -905.000000) translate(0,15)">10kV2号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1536a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3832.000000 -705.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1536e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5080.000000 -708.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1537670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -707.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15378c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -778.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1537ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4712.000000 -705.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1537d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4475.000000 -598.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1537f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4568.000000 -624.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1538150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -624.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1550450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4426.000000 -546.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1550670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4282.000000 -558.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15508b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -506.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1550af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -624.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1550f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -605.000000) translate(0,12)">07217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1551190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4074.000000 -558.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ee5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -506.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a0e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4072.000000 -624.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -564.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -512.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3848.000000 -630.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a19a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3862.000000 -612.000000) translate(0,12)">07417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3642.000000 -558.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16a1e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -506.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1552b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3640.000000 -624.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1552e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -605.000000) translate(0,12)">07517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15530a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4681.000000 -556.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15532e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -504.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1553520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4679.000000 -622.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1553760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4693.000000 -604.000000) translate(0,12)">08217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15539a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -556.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1553be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -504.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1553e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -622.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1554060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5129.000000 -554.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15542a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5127.000000 -502.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15544e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5127.000000 -620.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1554720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5142.000000 -602.000000) translate(0,12)">08417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1554960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3514.000000 -674.000000) translate(0,15)">10kV I母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1554ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -674.000000) translate(0,15)">10kV II母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_15fcdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3140.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1552910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_15b3d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3263.500000 -1165.500000) translate(0,16)">小姑英开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15af690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3744.000000 -765.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1590c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4212.000000 -760.000000) translate(0,12)">07117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1590e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4211.000000 -838.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1591030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -602.000000) translate(0,12)">07317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1591270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -548.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15914b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4937.000000 -602.000000) translate(0,12)">08317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15916f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4729.000000 -832.000000) translate(0,12)">08167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1591930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4737.000000 -757.000000) translate(0,12)">08117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1591b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4997.000000 -766.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1593760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -878.000000) translate(0,12)">500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1593c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4726.000000 -878.000000) translate(0,12)">500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17d6920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -780.000000) translate(0,12)">071</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1555150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4136.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15553c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4125.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1555770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1556020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 959.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15562b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 944.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15564c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 929.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1557090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3781.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1557300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1557510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157c870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157cc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157d020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157d4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157d8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4211.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157dba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157dde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4225.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4607.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4621.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157eb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4833.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157ede0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157f020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4847.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157f440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5059.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157f700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5048.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157f940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5073.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1581410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 342.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1581920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 327.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1581b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 312.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ac2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 754.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ad630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.500000 708.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ada90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3504.000000 694.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15adf20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 723.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ae440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 738.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ae7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5140.000000 737.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15aea20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5146.500000 691.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15aec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5132.000000 677.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15aeea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5140.000000 706.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15af0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5140.000000 721.000000) translate(0,12)">Ub（kV）：</text>
   <metadata/></g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_164e400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-580 3655,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6730@x" ObjectIDND1="6729@x" ObjectIDZND0="6732@0" Pin0InfoVect0LinkObjId="SW-87237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40910_0" Pin1InfoVect1LinkObjId="SW-40909_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-580 3655,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164ec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3691,-579 3730,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6732@1" ObjectIDZND0="g_164e5f0@0" Pin0InfoVect0LinkObjId="g_164e5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3691,-579 3730,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-803 3858,-785 3825,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_164f1f0@0" ObjectIDZND0="6724@x" ObjectIDZND1="11090@x" ObjectIDZND2="g_157fb80@0" Pin0InfoVect0LinkObjId="SW-40904_0" Pin0InfoVect1LinkObjId="SW-58681_0" Pin0InfoVect2LinkObjId="g_157fb80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_164f1f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-803 3858,-785 3825,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158b9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-680 3825,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6724@0" ObjectIDZND0="6719@0" Pin0InfoVect0LinkObjId="g_16a8d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-680 3825,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164acd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-785 3825,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_164f1f0@0" ObjectIDND1="g_157fb80@0" ObjectIDZND0="6724@x" ObjectIDZND1="11090@x" Pin0InfoVect0LinkObjId="SW-40904_0" Pin0InfoVect1LinkObjId="SW-58681_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_164f1f0_0" Pin1InfoVect1LinkObjId="g_157fb80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-785 3825,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_164aec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-740 3825,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_164f1f0@0" ObjectIDND1="g_157fb80@0" ObjectIDND2="11090@x" ObjectIDZND0="6724@1" Pin0InfoVect0LinkObjId="SW-40904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_164f1f0_0" Pin1InfoVect1LinkObjId="g_157fb80_0" Pin1InfoVect2LinkObjId="SW-58681_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-740 3825,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_162f240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-652 4176,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6719@0" ObjectIDZND0="6738@0" Pin0InfoVect0LinkObjId="SW-40918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_158b9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-652 4176,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_166a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4233,-737 4272,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11092@1" ObjectIDZND0="g_1669a50@0" Pin0InfoVect0LinkObjId="g_1669a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4233,-737 4272,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_166a3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4175,-737 4197,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6738@x" ObjectIDND1="6737@x" ObjectIDZND0="11092@0" Pin0InfoVect0LinkObjId="SW-87241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40918_0" Pin1InfoVect1LinkObjId="SW-40917_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4175,-737 4197,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_166acd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-718 4176,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6738@1" ObjectIDZND0="11092@x" ObjectIDZND1="6737@x" Pin0InfoVect0LinkObjId="SW-87241_0" Pin0InfoVect1LinkObjId="SW-40917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-718 4176,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1691d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4234,-808 4273,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11093@1" ObjectIDZND0="g_1691550@0" Pin0InfoVect0LinkObjId="g_1691550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58679_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4234,-808 4273,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1691ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-808 4198,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6737@x" ObjectIDND1="0@x" ObjectIDZND0="11093@0" Pin0InfoVect0LinkObjId="SW-58679_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40917_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-808 4198,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a7a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-737 4176,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11092@x" ObjectIDND1="6738@x" ObjectIDZND0="6737@0" Pin0InfoVect0LinkObjId="SW-40917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87241_0" Pin1InfoVect1LinkObjId="SW-40918_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-737 4176,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a7bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-786 4176,-808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="6737@1" ObjectIDZND0="11093@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-58679_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40917_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-786 4176,-808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a8b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4176,-808 4176,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="11093@x" ObjectIDND1="6737@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58679_0" Pin1InfoVect1LinkObjId="SW-40917_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4176,-808 4176,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a8d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-635 3633,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6730@1" ObjectIDZND0="6719@0" Pin0InfoVect0LinkObjId="g_158b9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40910_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-635 3633,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a8ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-517 3633,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6731@1" ObjectIDZND0="6729@0" Pin0InfoVect0LinkObjId="SW-40909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-517 3633,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1634990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-586 3863,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6726@x" ObjectIDND1="6725@x" ObjectIDZND0="6728@0" Pin0InfoVect0LinkObjId="SW-87236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40906_0" Pin1InfoVect1LinkObjId="SW-40905_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-586 3863,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f7fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3899,-586 3938,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6728@1" ObjectIDZND0="g_1634b80@0" Pin0InfoVect0LinkObjId="g_1634b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3899,-586 3938,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f84e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-641 3841,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6726@1" ObjectIDZND0="6719@0" Pin0InfoVect0LinkObjId="g_158b9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-641 3841,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f86d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-523 3841,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6727@1" ObjectIDZND0="6725@0" Pin0InfoVect0LinkObjId="SW-40905_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-523 3841,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1679970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-580 4087,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6722@x" ObjectIDND1="6721@x" ObjectIDZND0="11096@0" Pin0InfoVect0LinkObjId="SW-87235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40902_0" Pin1InfoVect1LinkObjId="SW-40901_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-580 4087,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167a310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-579 4162,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11096@1" ObjectIDZND0="g_1679b60@0" Pin0InfoVect0LinkObjId="g_1679b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-579 4162,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167a7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-635 4065,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6722@1" ObjectIDZND0="6719@0" Pin0InfoVect0LinkObjId="g_158b9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-635 4065,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167a9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-517 4065,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6723@1" ObjectIDZND0="6721@0" Pin0InfoVect0LinkObjId="SW-40901_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-517 4065,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1666110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-580 4295,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6734@x" ObjectIDND1="6733@x" ObjectIDZND0="6736@0" Pin0InfoVect0LinkObjId="SW-87234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40914_0" Pin1InfoVect1LinkObjId="SW-40913_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-580 4295,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1666c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4331,-579 4370,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6736@1" ObjectIDZND0="g_1666330@0" Pin0InfoVect0LinkObjId="g_1666330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4331,-579 4370,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167d1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5106,-807 5106,-789 5074,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1667250@0" ObjectIDZND0="6756@x" ObjectIDZND1="11091@x" ObjectIDZND2="g_1580530@0" Pin0InfoVect0LinkObjId="SW-40940_0" Pin0InfoVect1LinkObjId="SW-58680_0" Pin0InfoVect2LinkObjId="g_1580530_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1667250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5106,-807 5106,-789 5074,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_167e850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-683 5073,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6756@0" ObjectIDZND0="6720@0" Pin0InfoVect0LinkObjId="g_1644bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-683 5073,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-788 5073,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1667250@0" ObjectIDND1="g_1580530@0" ObjectIDZND0="6756@x" ObjectIDZND1="11091@x" Pin0InfoVect0LinkObjId="SW-40940_0" Pin0InfoVect1LinkObjId="SW-58680_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1667250_0" Pin1InfoVect1LinkObjId="g_1580530_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-788 5073,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160f790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-743 5073,-719 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1667250@0" ObjectIDND1="g_1580530@0" ObjectIDND2="11091@x" ObjectIDZND0="6756@1" Pin0InfoVect0LinkObjId="SW-40940_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1667250_0" Pin1InfoVect1LinkObjId="g_1580530_0" Pin1InfoVect2LinkObjId="SW-58680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-743 5073,-719 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1611c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-651 4705,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6720@0" ObjectIDZND0="6744@0" Pin0InfoVect0LinkObjId="SW-40928_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_167e850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-651 4705,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4762,-735 4801,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11094@1" ObjectIDZND0="g_160b090@0" Pin0InfoVect0LinkObjId="g_160b090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4762,-735 4801,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4704,-735 4726,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6744@x" ObjectIDND1="6743@x" ObjectIDZND0="11094@0" Pin0InfoVect0LinkObjId="SW-87242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40928_0" Pin1InfoVect1LinkObjId="SW-40927_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4704,-735 4726,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_160be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-716 4705,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6744@1" ObjectIDZND0="11094@x" ObjectIDZND1="6743@x" Pin0InfoVect0LinkObjId="SW-87242_0" Pin0InfoVect1LinkObjId="SW-40927_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40928_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-716 4705,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b9f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4763,-806 4802,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11095@1" ObjectIDZND0="g_15b9510@0" Pin0InfoVect0LinkObjId="g_15b9510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4763,-806 4802,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15ba1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-806 4727,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="6743@x" ObjectIDND1="0@x" ObjectIDZND0="11095@0" Pin0InfoVect0LinkObjId="SW-58678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40927_0" Pin1InfoVect1LinkObjId="TF-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-806 4727,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15ba400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-735 4705,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11094@x" ObjectIDND1="6744@x" ObjectIDZND0="6743@0" Pin0InfoVect0LinkObjId="SW-40927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87242_0" Pin1InfoVect1LinkObjId="SW-40928_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-735 4705,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15ba660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-784 4705,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="6743@1" ObjectIDZND0="11095@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-58678_0" Pin0InfoVect1LinkObjId="TF-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-784 4705,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15977f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-809 4705,-840 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="11095@x" ObjectIDND1="6743@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="TF-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-58678_0" Pin1InfoVect1LinkObjId="SW-40927_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-809 4705,-840 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1597a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-635 4273,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6734@1" ObjectIDZND0="6719@0" Pin0InfoVect0LinkObjId="g_158b9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-635 4273,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1597c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-517 4273,-537 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6735@1" ObjectIDZND0="6733@0" Pin0InfoVect0LinkObjId="SW-40913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-517 4273,-537 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15306e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-579 4695,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6746@x" ObjectIDND1="6745@x" ObjectIDZND0="6748@0" Pin0InfoVect0LinkObjId="SW-87238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40930_0" Pin1InfoVect1LinkObjId="SW-40929_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-579 4695,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4731,-578 4770,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6748@1" ObjectIDZND0="g_1530940@0" Pin0InfoVect0LinkObjId="g_1530940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4731,-578 4770,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-633 4672,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6746@1" ObjectIDZND0="6720@0" Pin0InfoVect0LinkObjId="g_167e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40930_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-633 4672,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1644da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-515 4672,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6747@1" ObjectIDZND0="6745@0" Pin0InfoVect0LinkObjId="SW-40929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40931_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-515 4672,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b6ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4898,-579 4920,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6754@x" ObjectIDND1="6753@x" ObjectIDZND0="11099@0" Pin0InfoVect0LinkObjId="SW-87239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40938_0" Pin1InfoVect1LinkObjId="SW-40937_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4898,-579 4920,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b7740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4956,-578 4995,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11099@1" ObjectIDZND0="g_15b6d10@0" Pin0InfoVect0LinkObjId="g_15b6d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4956,-578 4995,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b81a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-633 4897,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6754@1" ObjectIDZND0="6720@0" Pin0InfoVect0LinkObjId="g_167e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40938_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-633 4897,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15b8390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-515 4897,-535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6755@1" ObjectIDZND0="6753@0" Pin0InfoVect0LinkObjId="SW-40937_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40939_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-515 4897,-535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_159f4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5121,-577 5143,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6750@x" ObjectIDND1="6749@x" ObjectIDZND0="6752@0" Pin0InfoVect0LinkObjId="SW-87240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40934_0" Pin1InfoVect1LinkObjId="SW-40933_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5121,-577 5143,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5179,-576 5218,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6752@1" ObjectIDZND0="g_159f710@0" Pin0InfoVect0LinkObjId="g_159f710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-87240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5179,-576 5218,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a09d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-631 5120,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6750@1" ObjectIDZND0="6720@0" Pin0InfoVect0LinkObjId="g_167e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40934_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-631 5120,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a0bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-513 5120,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6751@1" ObjectIDZND0="6749@0" Pin0InfoVect0LinkObjId="SW-40933_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40935_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-513 5120,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1636b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-652 4419,-635 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6719@0" ObjectIDZND0="6740@1" Pin0InfoVect0LinkObjId="SW-40920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_158b9a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-652 4419,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1639310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-635 4561,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6741@1" ObjectIDZND0="6720@0" Pin0InfoVect0LinkObjId="g_167e850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-635 4561,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163b750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4473,-574 4419,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6739@1" ObjectIDZND0="6740@x" ObjectIDZND1="6742@x" Pin0InfoVect0LinkObjId="SW-40920_0" Pin0InfoVect1LinkObjId="SW-40922_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4473,-574 4419,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163b9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-574 4419,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6739@x" ObjectIDND1="6742@x" ObjectIDZND0="6740@0" Pin0InfoVect0LinkObjId="SW-40920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40919_0" Pin1InfoVect1LinkObjId="SW-40922_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-574 4419,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15bf3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-574 4419,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6739@x" ObjectIDND1="6740@x" ObjectIDZND0="6742@1" Pin0InfoVect0LinkObjId="SW-40922_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40919_0" Pin1InfoVect1LinkObjId="SW-40920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-574 4419,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15bf610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4419,-521 4419,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6742@0" ObjectIDZND0="g_15c2ee0@0" Pin0InfoVect0LinkObjId="g_15c2ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40922_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4419,-521 4419,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c0050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-599 4561,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6741@0" ObjectIDZND0="6739@x" ObjectIDZND1="11098@x" Pin0InfoVect0LinkObjId="SW-40919_0" Pin0InfoVect1LinkObjId="SW-58683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-599 4561,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c0290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-574 4500,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6741@x" ObjectIDND1="11098@x" ObjectIDZND0="6739@0" Pin0InfoVect0LinkObjId="SW-40919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40921_0" Pin1InfoVect1LinkObjId="SW-58683_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-574 4500,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c2a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-574 4561,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6741@x" ObjectIDND1="6739@x" ObjectIDZND0="11098@1" Pin0InfoVect0LinkObjId="SW-58683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40921_0" Pin1InfoVect1LinkObjId="SW-40919_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-574 4561,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4561,-519 4561,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11098@0" ObjectIDZND0="g_15c38d0@0" Pin0InfoVect0LinkObjId="g_15c38d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4561,-519 4561,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1580070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-785 3792,-785 3792,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_164f1f0@0" ObjectIDND1="6724@x" ObjectIDND2="11090@x" ObjectIDZND0="g_157fb80@0" Pin0InfoVect0LinkObjId="g_157fb80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_164f1f0_0" Pin1InfoVect1LinkObjId="SW-40904_0" Pin1InfoVect2LinkObjId="SW-58681_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-785 3792,-785 3792,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15802d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3792,-834 3792,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_157fb80@1" ObjectIDZND0="g_158abf0@0" Pin0InfoVect0LinkObjId="g_158abf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_157fb80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3792,-834 3792,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1580d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5074,-789 5040,-789 5040,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1667250@0" ObjectIDND1="6756@x" ObjectIDND2="11091@x" ObjectIDZND0="g_1580530@0" Pin0InfoVect0LinkObjId="g_1580530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1667250_0" Pin1InfoVect1LinkObjId="SW-40940_0" Pin1InfoVect2LinkObjId="SW-58680_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5074,-789 5040,-789 5040,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1580f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5040,-834 5040,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1580530@1" ObjectIDZND0="g_167d410@0" Pin0InfoVect0LinkObjId="g_167d410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1580530_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5040,-834 5040,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15842f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-740 3788,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_164f1f0@0" ObjectIDND1="g_157fb80@0" ObjectIDND2="6724@x" ObjectIDZND0="11090@1" Pin0InfoVect0LinkObjId="SW-58681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_164f1f0_0" Pin1InfoVect1LinkObjId="g_157fb80_0" Pin1InfoVect2LinkObjId="SW-40904_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-740 3788,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1584550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3752,-740 3728,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11090@0" ObjectIDZND0="g_158bb90@0" Pin0InfoVect0LinkObjId="g_158bb90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3752,-740 3728,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fbe50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5073,-743 5045,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1667250@0" ObjectIDND1="g_1580530@0" ObjectIDND2="6756@x" ObjectIDZND0="11091@1" Pin0InfoVect0LinkObjId="SW-58680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1667250_0" Pin1InfoVect1LinkObjId="g_1580530_0" Pin1InfoVect2LinkObjId="SW-40940_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5073,-743 5045,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15fc0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5009,-743 4976,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11091@0" ObjectIDZND0="g_167ea70@0" Pin0InfoVect0LinkObjId="g_167ea70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5009,-743 4976,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1594610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-599 3633,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6730@0" ObjectIDZND0="6732@x" ObjectIDZND1="6729@x" Pin0InfoVect0LinkObjId="SW-87237_0" Pin0InfoVect1LinkObjId="SW-40909_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-599 3633,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1594800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-580 3633,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6732@x" ObjectIDND1="6730@x" ObjectIDZND0="6729@1" Pin0InfoVect0LinkObjId="SW-40909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87237_0" Pin1InfoVect1LinkObjId="SW-40910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-580 3633,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-605 3841,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6726@0" ObjectIDZND0="6728@x" ObjectIDZND1="6725@x" Pin0InfoVect0LinkObjId="SW-87236_0" Pin0InfoVect1LinkObjId="SW-40905_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40906_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-605 3841,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-586 3841,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6728@x" ObjectIDND1="6726@x" ObjectIDZND0="6725@1" Pin0InfoVect0LinkObjId="SW-40905_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87236_0" Pin1InfoVect1LinkObjId="SW-40906_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-586 3841,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-599 4065,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6722@0" ObjectIDZND0="11096@x" ObjectIDZND1="6721@x" Pin0InfoVect0LinkObjId="SW-87235_0" Pin0InfoVect1LinkObjId="SW-40901_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-599 4065,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15961b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-580 4065,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11096@x" ObjectIDND1="6722@x" ObjectIDZND0="6721@1" Pin0InfoVect0LinkObjId="SW-40901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87235_0" Pin1InfoVect1LinkObjId="SW-40902_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-580 4065,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1596ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-599 4273,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6734@0" ObjectIDZND0="6736@x" ObjectIDZND1="6733@x" Pin0InfoVect0LinkObjId="SW-87234_0" Pin0InfoVect1LinkObjId="SW-40913_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-599 4273,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1596f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-580 4273,-564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6736@x" ObjectIDND1="6734@x" ObjectIDZND0="6733@1" Pin0InfoVect0LinkObjId="SW-40913_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87234_0" Pin1InfoVect1LinkObjId="SW-40914_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-580 4273,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15225d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-597 4672,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6746@0" ObjectIDZND0="6748@x" ObjectIDZND1="6745@x" Pin0InfoVect0LinkObjId="SW-87238_0" Pin0InfoVect1LinkObjId="SW-40929_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-597 4672,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1522830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-579 4672,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6748@x" ObjectIDND1="6746@x" ObjectIDZND0="6745@1" Pin0InfoVect0LinkObjId="SW-40929_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87238_0" Pin1InfoVect1LinkObjId="SW-40930_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-579 4672,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1523320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-597 4897,-579 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6754@0" ObjectIDZND0="11099@x" ObjectIDZND1="6753@x" Pin0InfoVect0LinkObjId="SW-87239_0" Pin0InfoVect1LinkObjId="SW-40937_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40938_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-597 4897,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1523580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-579 4897,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11099@x" ObjectIDND1="6754@x" ObjectIDZND0="6753@1" Pin0InfoVect0LinkObjId="SW-40937_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87239_0" Pin1InfoVect1LinkObjId="SW-40938_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-579 4897,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1524070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-595 5120,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="6750@0" ObjectIDZND0="6752@x" ObjectIDZND1="6749@x" Pin0InfoVect0LinkObjId="SW-87240_0" Pin0InfoVect1LinkObjId="SW-40933_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40934_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-595 5120,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15242d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-577 5120,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6752@x" ObjectIDND1="6750@x" ObjectIDZND0="6749@1" Pin0InfoVect0LinkObjId="SW-40933_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-87240_0" Pin1InfoVect1LinkObjId="SW-40934_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-577 5120,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1524530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-407 3841,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6727@0" Pin0InfoVect0LinkObjId="SW-40907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-407 3841,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1524790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4065,-401 4065,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6723@0" Pin0InfoVect0LinkObjId="SW-40903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4065,-401 4065,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15249f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4273,-401 4273,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6735@0" Pin0InfoVect0LinkObjId="SW-40915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4273,-401 4273,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1524c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-400 4672,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6747@0" Pin0InfoVect0LinkObjId="SW-40931_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-400 4672,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1524eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4897,-400 4897,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6755@0" Pin0InfoVect0LinkObjId="SW-40939_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4897,-400 4897,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1525110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-398 5120,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6751@0" Pin0InfoVect0LinkObjId="SW-40935_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-398 5120,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1525370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3633,-401 3633,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="6731@0" Pin0InfoVect0LinkObjId="SW-40911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3633,-401 3633,-481 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_XGY.CX_XGY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3514,-652 4441,-652 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6719" ObjectName="BS-CX_XGY.CX_XGY_9IM"/>
    <cge:TPSR_Ref TObjectID="6719"/></metadata>
   <polyline fill="none" opacity="0" points="3514,-652 4441,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_XGY.CX_XGY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4528,-651 5221,-651 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6720" ObjectName="BS-CX_XGY.CX_XGY_9IIM"/>
    <cge:TPSR_Ref TObjectID="6720"/></metadata>
   <polyline fill="none" opacity="0" points="4528,-651 5221,-651 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37327" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3424.000000 -1085.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5909" ObjectName="DYN-CX_XGY"/>
     <cge:Meas_Ref ObjectId="37327"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_XGY"/>
</svg>