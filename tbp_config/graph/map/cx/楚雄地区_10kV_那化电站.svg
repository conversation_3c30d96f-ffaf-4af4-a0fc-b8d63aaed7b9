<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-275" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1200 1359 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="32" stroke-width="0.5" width="15" x="1" y="6"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="32" stroke-width="0.5" width="15" x="2" y="6"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="33" stroke-width="0.5" width="16" x="1" y="5"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="17,5 1,38 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="18,38 2,5 " stroke-width="0.5"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <rect height="33" stroke-width="0.5" width="16" x="1" y="5"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="17,5 1,38 " stroke-width="0.509653"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="18,38 2,5 " stroke-width="0.5"/>
   </symbol>
   <symbol id="generator:shape3">
    <polyline arcFlag="1" points="26,-19 26,-18 26,-17 26,-16 25,-15 25,-14 24,-13 24,-13 23,-12 22,-12 21,-11 20,-11 19,-11 18,-10 17,-11 16,-11 15,-11 14,-12 14,-12 13,-13 12,-13 12,-14 11,-15 11,-16 11,-17 10,-18 11,-19 " stroke-width="0.06"/>
    <circle cx="25" cy="-20" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="41,-19 42,-20 41,-20 41,-21 41,-22 40,-23 40,-24 39,-25 38,-25 38,-26 37,-26 36,-27 35,-27 34,-27 33,-27 32,-27 31,-26 30,-26 29,-25 28,-25 28,-24 27,-23 27,-22 26,-21 26,-20 26,-20 26,-19 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape36">
    <rect height="19" stroke-width="0.75" width="8" x="4" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="6" x2="9" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="5" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="1" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="8" x2="8" y1="21" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="7" y1="46" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="7" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="8" x2="9" y1="27" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d87850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d87e00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d62930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de3480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cd2090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d43eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de99d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dea210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d9dda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf7160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf6f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cf6f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d04510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de06e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfb290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cfba30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1cf96f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1cd1590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfdfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d8bb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d30a50" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1da7e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0fbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d07230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e23260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d89910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1369" x="3112" y="-1205"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228516">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.561069 -1009.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37879" ObjectName="SW-CX_SDZ.CX_SDZ_NH_051"/>
     <cge:Meas_Ref ObjectId="228516"/>
    <cge:TPSR_Ref TObjectID="37879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228519">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.561069 -710.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37882" ObjectName="SW-CX_SDZ.CX_SDZ_NH_001"/>
     <cge:Meas_Ref ObjectId="228519"/>
    <cge:TPSR_Ref TObjectID="37882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228524">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4318.561069 -708.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37883" ObjectName="SW-CX_SDZ.CX_SDZ_NH_002"/>
     <cge:Meas_Ref ObjectId="228524"/>
    <cge:TPSR_Ref TObjectID="37883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228522">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.561069 -270.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37888" ObjectName="SW-CX_SDZ.CX_SDZ_NH_401"/>
     <cge:Meas_Ref ObjectId="228522"/>
    <cge:TPSR_Ref TObjectID="37888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228523">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.561069 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37889" ObjectName="SW-CX_SDZ.CX_SDZ_NH_402"/>
     <cge:Meas_Ref ObjectId="228523"/>
    <cge:TPSR_Ref TObjectID="37889"/></metadata>
   </g>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-38KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4301.858779 -174.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.583969 -189.000000)" xlink:href="#generator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4055.358779 -474.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-2KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4055.358779 -474.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4304.358779 -475.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-2KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4304.358779 -475.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3864.358779 -625.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3864.358779 -625.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_32419c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-1013 4108,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37879@1" ObjectIDZND0="37880@1" Pin0InfoVect0LinkObjId="SW-228517_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-1013 4108,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f79540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-1145 4108,-1110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fbbee0@0" ObjectIDZND0="37881@1" Pin0InfoVect0LinkObjId="SW-228518_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbbee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-1145 4108,-1110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23487c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-1074 4108,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37881@0" ObjectIDZND0="37879@0" Pin0InfoVect0LinkObjId="SW-228516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228518_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-1074 4108,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fbc7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-899 4151,-926 4108,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="g_3221a90@0" ObjectIDZND0="37880@x" ObjectIDZND1="0@0" Pin0InfoVect0LinkObjId="SW-228517_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3221a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-899 4151,-926 4108,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3217f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-830 4108,-926 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_3221a90@0" ObjectIDZND1="37880@x" Pin0InfoVect0LinkObjId="g_3221a90_0" Pin0InfoVect1LinkObjId="SW-228517_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-830 4108,-926 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2db6b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-926 4108,-955 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="g_3221a90@0" ObjectIDND1="0@0" ObjectIDZND0="37880@0" Pin0InfoVect0LinkObjId="SW-228517_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_3221a90_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-926 4108,-955 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fbd630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-830 3888,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SM-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-830 3888,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3210ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3847,-691 3847,-720 3888,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="g_2357300@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2357300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3847,-691 3847,-720 3888,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_303d0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-739 3888,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="g_2357300@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2357300_0" Pin0InfoVect1LinkObjId="SM-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-739 3888,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2fb93e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3888,-720 3888,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="g_2357300@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2357300_0" Pin1InfoVect1LinkObjId="SM-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3888,-720 3888,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3072210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-692 4079,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37887@1" ObjectIDZND0="37882@1" Pin0InfoVect0LinkObjId="SW-228519_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228521_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-692 4079,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3072470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-748 4079,-775 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37882@0" ObjectIDZND0="37884@0" Pin0InfoVect0LinkObjId="SW-228520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228519_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-748 4079,-775 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfb530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-811 4079,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37884@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-811 4079,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cfb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-809 4327,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="37885@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228526_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-809 4327,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3080390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-690 4328,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37886@1" ObjectIDZND0="37883@1" Pin0InfoVect0LinkObjId="SW-228524_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228527_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-690 4328,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_30805f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-746 4328,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="37883@0" ObjectIDZND0="37885@0" Pin0InfoVect0LinkObjId="SW-228526_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-746 4328,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_310c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-389 4078,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="37891@1" Pin0InfoVect0LinkObjId="SW-228525_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-389 4078,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_310c890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-390 4327,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="37890@1" Pin0InfoVect0LinkObjId="SW-228528_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-390 4327,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_3235870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-333 4078,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37891@0" ObjectIDZND0="37888@0" Pin0InfoVect0LinkObjId="SW-228522_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228525_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-333 4078,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3235ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-275 4078,-239 4078,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="generator" ObjectIDND0="37888@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228522_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-275 4078,-239 4078,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2f9a9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-333 4327,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="37890@0" ObjectIDZND0="37889@0" Pin0InfoVect0LinkObjId="SW-228523_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228528_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-333 4327,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-38KV" id="g_2f9ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4327,-276 4327,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="generator" ObjectIDND0="37889@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228523_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4327,-276 4327,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1d4b6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4043,-587 4043,-607 4079,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1e48610@0" ObjectIDZND0="37887@x" ObjectIDZND1="g_22a56d0@0" Pin0InfoVect0LinkObjId="SW-228521_0" Pin0InfoVect1LinkObjId="g_22a56d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e48610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4043,-587 4043,-607 4079,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33ae600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-607 4079,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e48610@0" ObjectIDND1="g_22a56d0@0" ObjectIDZND0="37887@0" Pin0InfoVect0LinkObjId="SW-228521_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e48610_0" Pin1InfoVect1LinkObjId="g_22a56d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-607 4079,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f65f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-654 4328,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="37886@0" ObjectIDZND0="g_2fbf960@0" ObjectIDZND1="g_2fbc510@0" Pin0InfoVect0LinkObjId="g_2fbf960_0" Pin0InfoVect1LinkObjId="g_2fbc510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-228527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-654 4328,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22a54e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-607 4282,-607 4282,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="37886@x" ObjectIDND1="g_2fbc510@0" ObjectIDZND0="g_2fbf960@0" Pin0InfoVect0LinkObjId="g_2fbf960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-228527_0" Pin1InfoVect1LinkObjId="g_2fbc510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-607 4282,-607 4282,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcf360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-607 4079,-540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e48610@0" ObjectIDND1="37887@x" ObjectIDZND0="g_22a56d0@1" Pin0InfoVect0LinkObjId="g_22a56d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e48610_0" Pin1InfoVect1LinkObjId="SW-228521_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-607 4079,-540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2fcf5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4079,-487 4079,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_22a56d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SM-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22a56d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4079,-487 4079,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3075e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-470 4328,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2fbc510@0" Pin0InfoVect0LinkObjId="g_2fbc510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-470 4328,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3076060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4328,-540 4328,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2fbc510@1" ObjectIDZND0="37886@x" ObjectIDZND1="g_2fbf960@0" Pin0InfoVect0LinkObjId="SW-228527_0" Pin0InfoVect1LinkObjId="g_2fbf960_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fbc510_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4328,-540 4328,-607 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4108" cy="-830" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3888" cy="-830" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4327" cy="-830" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4079" cy="-830" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,18)">10kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,40)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,62)">潭</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,84)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,106)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,128)">那</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,150)">化</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,172)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,194)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,216)">支</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_3097080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4045.000000 -1115.000000) translate(0,238)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30499f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -123.000000) translate(0,15)">#2F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_30499f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4317.000000 -123.000000) translate(0,33)">630kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2d75690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_321f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1dad460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3279.000000 -1166.500000) translate(0,16)">那化电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32110c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -128.000000) translate(0,15)">#1F</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_32110c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4061.000000 -128.000000) translate(0,33)">250kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e966b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -440.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e966b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4119.000000 -440.000000) translate(0,33)">S13-M-400kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3080dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -360.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8c810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -299.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8ca50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -681.000000) translate(0,15)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d8cc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4097.000000 -739.000000) translate(0,15)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9c380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -799.000000) translate(0,15)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4340.000000 -798.000000) translate(0,15)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f9c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -736.000000) translate(0,15)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fcd340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -681.000000) translate(0,15)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fcd580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -357.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2fcd7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4345.000000 -298.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cece10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -1098.000000) translate(0,15)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ced250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4128.000000 -1040.000000) translate(0,15)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1de4570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4122.000000 -977.000000) translate(0,15)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1de4780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3844.000000 -528.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1de4780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3844.000000 -528.000000) translate(0,33)">S11-M-30kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ddec40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3899.000000 -863.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232d250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -440.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_232d250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -440.000000) translate(0,33)">S13-M-800kVA</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-228528">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4312.358779 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37890" ObjectName="SW-CX_SDZ.CX_SDZ_NH_4021"/>
     <cge:Meas_Ref ObjectId="228528"/>
    <cge:TPSR_Ref TObjectID="37890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228518">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4093.061069 -1052.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37881" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0516"/>
     <cge:Meas_Ref ObjectId="228518"/>
    <cge:TPSR_Ref TObjectID="37881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228517">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4093.061069 -933.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37880" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0511"/>
     <cge:Meas_Ref ObjectId="228517"/>
    <cge:TPSR_Ref TObjectID="37880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3883.068702 -734.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228525">
    <use class="BV-38KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.083969 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37891" ObjectName="SW-CX_SDZ.CX_SDZ_NH_4011"/>
     <cge:Meas_Ref ObjectId="228525"/>
    <cge:TPSR_Ref TObjectID="37891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228520">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4064.061069 -753.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37884" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0011"/>
     <cge:Meas_Ref ObjectId="228520"/>
    <cge:TPSR_Ref TObjectID="37884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228521">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4064.061069 -634.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37887" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0016"/>
     <cge:Meas_Ref ObjectId="228521"/>
    <cge:TPSR_Ref TObjectID="37887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228527">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4313.061069 -632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37886" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0026"/>
     <cge:Meas_Ref ObjectId="228527"/>
    <cge:TPSR_Ref TObjectID="37886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-228526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4313.061069 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37885" ObjectName="SW-CX_SDZ.CX_SDZ_NH_0021"/>
     <cge:Meas_Ref ObjectId="228526"/>
    <cge:TPSR_Ref TObjectID="37885"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2fbbee0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4117.561069 -1164.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3221a90">
    <use class="BV-10KV" transform="matrix(1.320000 -0.000000 0.000000 -1.198438 4141.661069 -845.078125)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2357300">
    <use class="BV-0KV" transform="matrix(1.320000 -0.000000 0.000000 -1.198438 3838.168702 -637.078125)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e48610">
    <use class="BV-10KV" transform="matrix(1.320000 -0.000000 0.000000 -1.198438 4034.168702 -533.078125)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbf960">
    <use class="BV-10KV" transform="matrix(1.320000 -0.000000 0.000000 -1.198438 4273.168702 -536.078125)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22a56d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 -482.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fbc510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -482.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3246" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3197" y="-1194"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3153" y="-1199"/>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3234.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3843,-830 4422,-830 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3843,-830 4422,-830 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3246" y="-1177"/></g>
   <g href="cx_配调_配网接线图10.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3197" y="-1194"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>