<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-164" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-267 -1166 2213 1416">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape34">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="31" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="47" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="52" y1="12" y2="7"/>
    <circle cx="49" cy="9" fillStyle="0" r="3.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="64" x2="64" y1="10" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="63" x2="63" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="62" x2="62" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="53" x2="62" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="46" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="18" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="18" y2="0"/>
   </symbol>
   <symbol id="capacitor:shape9">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="21" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="51" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="14" x2="22" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="8" x2="18" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="18" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="0" y1="22" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="36" x2="28" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="42" x2="32" y1="29" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="32" y1="36" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="51" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="21" x2="21" y1="13" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="28" x2="28" y1="13" y2="0"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="5" y2="36"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="64" x2="64" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape17">
    <rect height="29" stroke-width="2" width="15" x="1" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="8" x2="11" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.00003" x1="9" x2="6" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.8" x1="8" x2="8" y1="8" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="6" x2="11" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="1" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.99999" x1="4" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="8" x2="8" y1="51" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape205">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,27 38,5 50,5 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="25" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="13" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape38">
    <rect height="8" stroke-width="0.75" width="18" x="21" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="1" x2="1" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="4" x2="4" y1="10" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="7" x2="7" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="21" x2="7" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="46" x2="26" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="28" x2="26" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="26" x2="28" y1="7" y2="5"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="33" x2="33" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="33"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape3_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape3_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape0">
    <circle cx="18" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="10" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="8" y2="6"/>
    <ellipse cx="15" cy="19" fillStyle="0" rx="6.5" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="25" cy="15" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <ellipse cx="16" cy="8" fillStyle="0" rx="6" ry="8.5" stroke-width="0.445094"/>
    <ellipse cx="7" cy="14" fillStyle="0" rx="6" ry="8" stroke-width="0.445094"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="22" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="25" x2="25" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="28" x2="25" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="13" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.977901" x1="19" x2="16" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.781768" x1="8" x2="9" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.787381" x1="6" x2="3" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.749893" x1="3" x2="9" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.97794" x1="16" x2="16" y1="6" y2="3"/>
   </symbol>
   <symbol id="voltageTransformer:shape54">
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="32" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2da4f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2da6100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da6ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da7aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da8bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2da9550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2daa070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2daaaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23ce560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_23ce560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dadea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dadea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dafd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dafd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2db0d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db2a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2db3650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2db4530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db4e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db6800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db7270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db7b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2db82f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db93d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2db9d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dba840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbb200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbc6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbd210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2dbe260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dbeeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2dcd1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2dcdc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dc16f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2dc2cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1426" width="2223" x="-272" y="-1171"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="528" x2="528" y1="-124" y2="-124"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1767" x2="1739" y1="-264" y2="-253"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,0)" fillStyle="1" height="19" stroke="rgb(255,0,0)" stroke-width="1" width="148" x="1768" y="-276"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="24" y="-963"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-113955">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -1008.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21623" ObjectName="SW-SB_TD.SB_TD_3536SW"/>
     <cge:Meas_Ref ObjectId="113955"/>
    <cge:TPSR_Ref TObjectID="21623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -1062.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21624" ObjectName="SW-SB_TD.SB_TD_35367SW"/>
     <cge:Meas_Ref ObjectId="113956"/>
    <cge:TPSR_Ref TObjectID="21624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -360.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21666" ObjectName="SW-SB_TD.SB_TD_00117SW"/>
     <cge:Meas_Ref ObjectId="114113"/>
    <cge:TPSR_Ref TObjectID="21666"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1611.000000 -392.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21671" ObjectName="SW-SB_TD.SB_TD_00217SW"/>
     <cge:Meas_Ref ObjectId="114143"/>
    <cge:TPSR_Ref TObjectID="21671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134925">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -738.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24443" ObjectName="SW-SB_TD.SB_TD_3901SW"/>
     <cge:Meas_Ref ObjectId="134925"/>
    <cge:TPSR_Ref TObjectID="24443"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114002">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 456.000000 -204.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21630" ObjectName="SW-SB_TD.SB_TD_05317SW"/>
     <cge:Meas_Ref ObjectId="114002"/>
    <cge:TPSR_Ref TObjectID="21630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 77.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21631" ObjectName="SW-SB_TD.SB_TD_0536SW"/>
     <cge:Meas_Ref ObjectId="114003"/>
    <cge:TPSR_Ref TObjectID="21631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114017">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21635" ObjectName="SW-SB_TD.SB_TD_05417SW"/>
     <cge:Meas_Ref ObjectId="114017"/>
    <cge:TPSR_Ref TObjectID="21635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114018">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 76.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21636" ObjectName="SW-SB_TD.SB_TD_0546SW"/>
     <cge:Meas_Ref ObjectId="114018"/>
    <cge:TPSR_Ref TObjectID="21636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114092">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -206.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21662" ObjectName="SW-SB_TD.SB_TD_05517SW"/>
     <cge:Meas_Ref ObjectId="114092"/>
    <cge:TPSR_Ref TObjectID="21662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114032">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 860.000000 -204.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21640" ObjectName="SW-SB_TD.SB_TD_05617SW"/>
     <cge:Meas_Ref ObjectId="114032"/>
    <cge:TPSR_Ref TObjectID="21640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114033">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 77.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21641" ObjectName="SW-SB_TD.SB_TD_0566SW"/>
     <cge:Meas_Ref ObjectId="114033"/>
    <cge:TPSR_Ref TObjectID="21641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114047">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21645" ObjectName="SW-SB_TD.SB_TD_05717SW"/>
     <cge:Meas_Ref ObjectId="114047"/>
    <cge:TPSR_Ref TObjectID="21645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114048">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 78.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21646" ObjectName="SW-SB_TD.SB_TD_0576SW"/>
     <cge:Meas_Ref ObjectId="114048"/>
    <cge:TPSR_Ref TObjectID="21646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114062">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1379.000000 -205.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21650" ObjectName="SW-SB_TD.SB_TD_05817SW"/>
     <cge:Meas_Ref ObjectId="114062"/>
    <cge:TPSR_Ref TObjectID="21650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114063">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 76.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21651" ObjectName="SW-SB_TD.SB_TD_0586SW"/>
     <cge:Meas_Ref ObjectId="114063"/>
    <cge:TPSR_Ref TObjectID="21651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21655" ObjectName="SW-SB_TD.SB_TD_05917SW"/>
     <cge:Meas_Ref ObjectId="114077"/>
    <cge:TPSR_Ref TObjectID="21655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114078">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 78.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21656" ObjectName="SW-SB_TD.SB_TD_0596SW"/>
     <cge:Meas_Ref ObjectId="114078"/>
    <cge:TPSR_Ref TObjectID="21656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114171">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21674" ObjectName="SW-SB_TD.SB_TD_09017SW"/>
     <cge:Meas_Ref ObjectId="114171"/>
    <cge:TPSR_Ref TObjectID="21674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114081">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1704.000000 -197.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21657" ObjectName="SW-SB_TD.SB_TD_06117SW"/>
     <cge:Meas_Ref ObjectId="114081"/>
    <cge:TPSR_Ref TObjectID="21657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21658" ObjectName="SW-SB_TD.SB_TD_0611SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="21658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114170">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.000000 -236.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21673" ObjectName="SW-SB_TD.SB_TD_0901SW"/>
     <cge:Meas_Ref ObjectId="114170"/>
    <cge:TPSR_Ref TObjectID="21673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114076">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21654" ObjectName="SW-SB_TD.SB_TD_0591SW"/>
     <cge:Meas_Ref ObjectId="114076"/>
    <cge:TPSR_Ref TObjectID="21654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114061">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21649" ObjectName="SW-SB_TD.SB_TD_0581SW"/>
     <cge:Meas_Ref ObjectId="114061"/>
    <cge:TPSR_Ref TObjectID="21649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114046">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21644" ObjectName="SW-SB_TD.SB_TD_0571SW"/>
     <cge:Meas_Ref ObjectId="114046"/>
    <cge:TPSR_Ref TObjectID="21644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114031">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21639" ObjectName="SW-SB_TD.SB_TD_0561SW"/>
     <cge:Meas_Ref ObjectId="114031"/>
    <cge:TPSR_Ref TObjectID="21639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114016">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21634" ObjectName="SW-SB_TD.SB_TD_0541SW"/>
     <cge:Meas_Ref ObjectId="114016"/>
    <cge:TPSR_Ref TObjectID="21634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21629" ObjectName="SW-SB_TD.SB_TD_0531SW"/>
     <cge:Meas_Ref ObjectId="114001"/>
    <cge:TPSR_Ref TObjectID="21629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114091">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21661" ObjectName="SW-SB_TD.SB_TD_0551SW"/>
     <cge:Meas_Ref ObjectId="114091"/>
    <cge:TPSR_Ref TObjectID="21661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -437.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21667" ObjectName="SW-SB_TD.SB_TD_0012SW"/>
     <cge:Meas_Ref ObjectId="114114"/>
    <cge:TPSR_Ref TObjectID="21667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114015">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -62.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21633" ObjectName="SW-SB_TD.SB_TD_0543SW"/>
     <cge:Meas_Ref ObjectId="114015"/>
    <cge:TPSR_Ref TObjectID="21633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114000">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21628" ObjectName="SW-SB_TD.SB_TD_0533SW"/>
     <cge:Meas_Ref ObjectId="114000"/>
    <cge:TPSR_Ref TObjectID="21628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -63.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21660" ObjectName="SW-SB_TD.SB_TD_0556SW"/>
     <cge:Meas_Ref ObjectId="114090"/>
    <cge:TPSR_Ref TObjectID="21660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114030">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21638" ObjectName="SW-SB_TD.SB_TD_0563SW"/>
     <cge:Meas_Ref ObjectId="114030"/>
    <cge:TPSR_Ref TObjectID="21638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114045">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -60.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21643" ObjectName="SW-SB_TD.SB_TD_0573SW"/>
     <cge:Meas_Ref ObjectId="114045"/>
    <cge:TPSR_Ref TObjectID="21643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114060">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 -62.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21648" ObjectName="SW-SB_TD.SB_TD_0583SW"/>
     <cge:Meas_Ref ObjectId="114060"/>
    <cge:TPSR_Ref TObjectID="21648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114075">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -60.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21653" ObjectName="SW-SB_TD.SB_TD_0593SW"/>
     <cge:Meas_Ref ObjectId="114075"/>
    <cge:TPSR_Ref TObjectID="21653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -312.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21665" ObjectName="SW-SB_TD.SB_TD_0011SW"/>
     <cge:Meas_Ref ObjectId="114112"/>
    <cge:TPSR_Ref TObjectID="21665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21670" ObjectName="SW-SB_TD.SB_TD_0021SW"/>
     <cge:Meas_Ref ObjectId="114142"/>
    <cge:TPSR_Ref TObjectID="21670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -449.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21672" ObjectName="SW-SB_TD.SB_TD_0022SW"/>
     <cge:Meas_Ref ObjectId="114144"/>
    <cge:TPSR_Ref TObjectID="21672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1424.000000 -921.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134750">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28752" ObjectName="SW-SB_TD.SB_TD_3011SW"/>
     <cge:Meas_Ref ObjectId="134750"/>
    <cge:TPSR_Ref TObjectID="28752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 -855.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28745" ObjectName="SW-SB_TD.SB_TD_35117SW"/>
     <cge:Meas_Ref ObjectId="188659"/>
    <cge:TPSR_Ref TObjectID="28745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134751">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.000000 -718.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28753" ObjectName="SW-SB_TD.SB_TD_30117SW"/>
     <cge:Meas_Ref ObjectId="134751"/>
    <cge:TPSR_Ref TObjectID="28753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134809">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -739.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28754" ObjectName="SW-SB_TD.SB_TD_3021SW"/>
     <cge:Meas_Ref ObjectId="134809"/>
    <cge:TPSR_Ref TObjectID="28754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134810">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -729.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28755" ObjectName="SW-SB_TD.SB_TD_30217SW"/>
     <cge:Meas_Ref ObjectId="134810"/>
    <cge:TPSR_Ref TObjectID="28755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188671">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1315.000000 -718.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28756" ObjectName="SW-SB_TD.SB_TD_39017SW"/>
     <cge:Meas_Ref ObjectId="188671"/>
    <cge:TPSR_Ref TObjectID="28756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28743" ObjectName="SW-SB_TD.SB_TD_3511SW"/>
     <cge:Meas_Ref ObjectId="134557"/>
    <cge:TPSR_Ref TObjectID="28743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28744" ObjectName="SW-SB_TD.SB_TD_3516SW"/>
     <cge:Meas_Ref ObjectId="134558"/>
    <cge:TPSR_Ref TObjectID="28744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 -854.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28748" ObjectName="SW-SB_TD.SB_TD_35217SW"/>
     <cge:Meas_Ref ObjectId="188662"/>
    <cge:TPSR_Ref TObjectID="28748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134578">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.000000 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28746" ObjectName="SW-SB_TD.SB_TD_3521SW"/>
     <cge:Meas_Ref ObjectId="134578"/>
    <cge:TPSR_Ref TObjectID="28746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 922.000000 -973.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28747" ObjectName="SW-SB_TD.SB_TD_3526SW"/>
     <cge:Meas_Ref ObjectId="113979"/>
    <cge:TPSR_Ref TObjectID="28747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-188665">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1273.000000 -836.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28751" ObjectName="SW-SB_TD.SB_TD_35317SW"/>
     <cge:Meas_Ref ObjectId="188665"/>
    <cge:TPSR_Ref TObjectID="28751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -813.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28749" ObjectName="SW-SB_TD.SB_TD_3531SW"/>
     <cge:Meas_Ref ObjectId="134600"/>
    <cge:TPSR_Ref TObjectID="28749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-134601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28750" ObjectName="SW-SB_TD.SB_TD_3533SW"/>
     <cge:Meas_Ref ObjectId="134601"/>
    <cge:TPSR_Ref TObjectID="28750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246545">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41366" ObjectName="SW-SB_TD.SB_TD_0511SW"/>
     <cge:Meas_Ref ObjectId="246545"/>
    <cge:TPSR_Ref TObjectID="41366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246549">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41367" ObjectName="SW-SB_TD.SB_TD_0513SW"/>
     <cge:Meas_Ref ObjectId="246549"/>
    <cge:TPSR_Ref TObjectID="41367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246550">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 81.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41368" ObjectName="SW-SB_TD.SB_TD_0516SW"/>
     <cge:Meas_Ref ObjectId="246550"/>
    <cge:TPSR_Ref TObjectID="41368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246546">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41369" ObjectName="SW-SB_TD.SB_TD_0521SW"/>
     <cge:Meas_Ref ObjectId="246546"/>
    <cge:TPSR_Ref TObjectID="41369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246547">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41370" ObjectName="SW-SB_TD.SB_TD_0523SW"/>
     <cge:Meas_Ref ObjectId="246547"/>
    <cge:TPSR_Ref TObjectID="41370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246548">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 331.000000 77.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41371" ObjectName="SW-SB_TD.SB_TD_0526SW"/>
     <cge:Meas_Ref ObjectId="246548"/>
    <cge:TPSR_Ref TObjectID="41371"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_TD.SB_TD_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-794 1814,-794 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21619" ObjectName="BS-SB_TD.SB_TD_3IM"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   <polyline fill="none" opacity="0" points="287,-794 1814,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_TD.SB_TD_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="94,-306 1946,-306 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21620" ObjectName="BS-SB_TD.SB_TD_9IM"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   <polyline fill="none" opacity="0" points="94,-306 1946,-306 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 -42.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 -44.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 578.000000 -48.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -43.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 726.000000 -47.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 -43.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -47.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-SB_TD.SB_TD_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 144.000000)" xlink:href="#capacitor:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40087" ObjectName="CB-SB_TD.SB_TD_Cb1"/>
    <cge:TPSR_Ref TObjectID="40087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.000000 -479.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1613.000000 -495.000000)" xlink:href="#capacitor:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_TD.SB_TD_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30336"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 409.000000 -560.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 409.000000 -560.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21676" ObjectName="TF-SB_TD.SB_TD_1T"/>
    <cge:TPSR_Ref TObjectID="21676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_TD.SB_TD_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30340"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -578.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 -578.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21677" ObjectName="TF-SB_TD.SB_TD_2T"/>
    <cge:TPSR_Ref TObjectID="21677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_TD.SB_TD_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="30344"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 -54.000000)" xlink:href="#transformer2:shape3_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 -54.000000)" xlink:href="#transformer2:shape3_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21678" ObjectName="TF-SB_TD.SB_TD_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_TD.SB_TD_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34638"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.978723 -0.000000 0.000000 0.989899 1415.500000 -909.500000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.978723 -0.000000 0.000000 0.989899 1415.500000 -909.500000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24539" ObjectName="TF-SB_TD.SB_TD_Zyb2"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2328fa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 441.000000 -493.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232e510">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1596.500000 -510.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2402150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1383.000000 -673.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2402980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.000000 -119.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23461d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 484.000000 151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2346ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2422250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 591.000000 -120.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232a710">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232b0e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 21.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f8060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 742.000000 -121.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23eff90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 20.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2417980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 877.000000 -119.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2383740">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 888.000000 151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23842f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2354440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -118.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2354ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2255e90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.000000 23.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234a890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -120.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234ac70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234b1e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 21.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23acbb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1554.000000 -118.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23acf30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ad460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1523.000000 23.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f26f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -121.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d01f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1035.000000 -126.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d0860">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1685.000000 -146.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23db6a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1376.000000 -922.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225cdc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -1074.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225e2b0">
    <use class="BV-35KV" transform="matrix(0.000000 0.923077 -1.000000 0.000000 354.000000 -1069.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22602f0">
    <use class="BV-35KV" transform="matrix(0.000000 0.923077 -1.000000 0.000000 856.000000 -1067.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2261c70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 864.000000 -1072.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22645a0">
    <use class="BV-35KV" transform="matrix(0.000000 0.923077 -1.000000 0.000000 1240.000000 -1083.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2265f20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1248.000000 -1088.000000)" xlink:href="#lightningRod:shape205"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226c910">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.000000 -648.000000)" xlink:href="#lightningRod:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2270410">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.000000 -358.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22999c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1523.000000 -390.000000)" xlink:href="#lightningRod:shape38"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222e670">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 21.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_222f3c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 270.000000 151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2230d40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2231a90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 374.000000 155.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223aa00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 11.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223c720">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 378.000000 11.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208953" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -127.000000 -818.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208953" ObjectName="SB_TD:SB_TD_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208954" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -127.000000 -777.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208954" ObjectName="SB_TD:SB_TD_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208953" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -897.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208953" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-208953" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -129.000000 -857.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="208953" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -438.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21664"/>
     <cge:Term_Ref ObjectID="30310"/>
    <cge:TPSR_Ref TObjectID="21664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -438.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21664"/>
     <cge:Term_Ref ObjectID="30310"/>
    <cge:TPSR_Ref TObjectID="21664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -438.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21664"/>
     <cge:Term_Ref ObjectID="30310"/>
    <cge:TPSR_Ref TObjectID="21664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1722.000000 -454.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21669"/>
     <cge:Term_Ref ObjectID="30320"/>
    <cge:TPSR_Ref TObjectID="21669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1722.000000 -454.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21669"/>
     <cge:Term_Ref ObjectID="30320"/>
    <cge:TPSR_Ref TObjectID="21669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1722.000000 -454.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21669"/>
     <cge:Term_Ref ObjectID="30320"/>
    <cge:TPSR_Ref TObjectID="21669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113863" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113863" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21627"/>
     <cge:Term_Ref ObjectID="30236"/>
    <cge:TPSR_Ref TObjectID="21627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21627"/>
     <cge:Term_Ref ObjectID="30236"/>
    <cge:TPSR_Ref TObjectID="21627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21627"/>
     <cge:Term_Ref ObjectID="30236"/>
    <cge:TPSR_Ref TObjectID="21627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113868" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 203.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113868" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21632"/>
     <cge:Term_Ref ObjectID="30246"/>
    <cge:TPSR_Ref TObjectID="21632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113869" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 203.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113869" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21632"/>
     <cge:Term_Ref ObjectID="30246"/>
    <cge:TPSR_Ref TObjectID="21632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113866" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 577.000000 203.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113866" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21632"/>
     <cge:Term_Ref ObjectID="30246"/>
    <cge:TPSR_Ref TObjectID="21632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 202.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21659"/>
     <cge:Term_Ref ObjectID="30300"/>
    <cge:TPSR_Ref TObjectID="21659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 202.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21659"/>
     <cge:Term_Ref ObjectID="30300"/>
    <cge:TPSR_Ref TObjectID="21659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 743.000000 202.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21659"/>
     <cge:Term_Ref ObjectID="30300"/>
    <cge:TPSR_Ref TObjectID="21659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113873" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21637"/>
     <cge:Term_Ref ObjectID="30256"/>
    <cge:TPSR_Ref TObjectID="21637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21637"/>
     <cge:Term_Ref ObjectID="30256"/>
    <cge:TPSR_Ref TObjectID="21637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 876.000000 205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21637"/>
     <cge:Term_Ref ObjectID="30256"/>
    <cge:TPSR_Ref TObjectID="21637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113878" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.000000 205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113878" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21642"/>
     <cge:Term_Ref ObjectID="30266"/>
    <cge:TPSR_Ref TObjectID="21642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.000000 205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113879" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21642"/>
     <cge:Term_Ref ObjectID="30266"/>
    <cge:TPSR_Ref TObjectID="21642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.000000 205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21642"/>
     <cge:Term_Ref ObjectID="30266"/>
    <cge:TPSR_Ref TObjectID="21642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 204.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21647"/>
     <cge:Term_Ref ObjectID="30276"/>
    <cge:TPSR_Ref TObjectID="21647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 204.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21647"/>
     <cge:Term_Ref ObjectID="30276"/>
    <cge:TPSR_Ref TObjectID="21647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1387.000000 204.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21647"/>
     <cge:Term_Ref ObjectID="30276"/>
    <cge:TPSR_Ref TObjectID="21647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 205.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21652"/>
     <cge:Term_Ref ObjectID="30286"/>
    <cge:TPSR_Ref TObjectID="21652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 205.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21652"/>
     <cge:Term_Ref ObjectID="30286"/>
    <cge:TPSR_Ref TObjectID="21652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1547.000000 205.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21652"/>
     <cge:Term_Ref ObjectID="30286"/>
    <cge:TPSR_Ref TObjectID="21652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-113939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 -606.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21676"/>
     <cge:Term_Ref ObjectID="30337"/>
    <cge:TPSR_Ref TObjectID="21676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-113940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.000000 -606.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21676"/>
     <cge:Term_Ref ObjectID="30337"/>
    <cge:TPSR_Ref TObjectID="21676"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-113927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -399.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21620"/>
     <cge:Term_Ref ObjectID="30223"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-113928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -399.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21620"/>
     <cge:Term_Ref ObjectID="30223"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-113929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -399.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21620"/>
     <cge:Term_Ref ObjectID="30223"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-134550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -399.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21620"/>
     <cge:Term_Ref ObjectID="30223"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-113925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -399.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21620"/>
     <cge:Term_Ref ObjectID="30223"/>
    <cge:TPSR_Ref TObjectID="21620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="1" id="ME-113922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -878.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21619"/>
     <cge:Term_Ref ObjectID="30222"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="1" id="ME-113923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -878.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21619"/>
     <cge:Term_Ref ObjectID="30222"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="1" id="ME-113924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -878.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21619"/>
     <cge:Term_Ref ObjectID="30222"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="1" id="ME-134547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -878.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="134547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21619"/>
     <cge:Term_Ref ObjectID="30222"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="1" id="ME-113920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 -878.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21619"/>
     <cge:Term_Ref ObjectID="30222"/>
    <cge:TPSR_Ref TObjectID="21619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-113941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1709.000000 -622.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21677"/>
     <cge:Term_Ref ObjectID="30338"/>
    <cge:TPSR_Ref TObjectID="21677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-113942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1709.000000 -622.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21677"/>
     <cge:Term_Ref ObjectID="30338"/>
    <cge:TPSR_Ref TObjectID="21677"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -929.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24539"/>
     <cge:Term_Ref ObjectID="34636"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135567" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -929.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24539"/>
     <cge:Term_Ref ObjectID="34636"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -929.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24539"/>
     <cge:Term_Ref ObjectID="34636"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -929.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24539"/>
     <cge:Term_Ref ObjectID="34636"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-135577" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135577" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21678"/>
     <cge:Term_Ref ObjectID="30345"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-135578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21678"/>
     <cge:Term_Ref ObjectID="30345"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-135571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21678"/>
     <cge:Term_Ref ObjectID="30345"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-135574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1702.000000 -15.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="135574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21678"/>
     <cge:Term_Ref ObjectID="30345"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113858" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 -960.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113858" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21626"/>
     <cge:Term_Ref ObjectID="30234"/>
    <cge:TPSR_Ref TObjectID="21626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113859" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 -960.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113859" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21626"/>
     <cge:Term_Ref ObjectID="30234"/>
    <cge:TPSR_Ref TObjectID="21626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113856" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 -960.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113856" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21626"/>
     <cge:Term_Ref ObjectID="30234"/>
    <cge:TPSR_Ref TObjectID="21626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113853" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -959.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113853" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21625"/>
     <cge:Term_Ref ObjectID="30232"/>
    <cge:TPSR_Ref TObjectID="21625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -959.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21625"/>
     <cge:Term_Ref ObjectID="30232"/>
    <cge:TPSR_Ref TObjectID="21625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1047.000000 -959.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21625"/>
     <cge:Term_Ref ObjectID="30232"/>
    <cge:TPSR_Ref TObjectID="21625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -946.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21622"/>
     <cge:Term_Ref ObjectID="30226"/>
    <cge:TPSR_Ref TObjectID="21622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -946.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21622"/>
     <cge:Term_Ref ObjectID="30226"/>
    <cge:TPSR_Ref TObjectID="21622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113846" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -946.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113846" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21622"/>
     <cge:Term_Ref ObjectID="30226"/>
    <cge:TPSR_Ref TObjectID="21622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -725.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21663"/>
     <cge:Term_Ref ObjectID="30308"/>
    <cge:TPSR_Ref TObjectID="21663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -725.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21663"/>
     <cge:Term_Ref ObjectID="30308"/>
    <cge:TPSR_Ref TObjectID="21663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -725.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21663"/>
     <cge:Term_Ref ObjectID="30308"/>
    <cge:TPSR_Ref TObjectID="21663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-113911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -728.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21668"/>
     <cge:Term_Ref ObjectID="30318"/>
    <cge:TPSR_Ref TObjectID="21668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-113912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -728.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21668"/>
     <cge:Term_Ref ObjectID="30318"/>
    <cge:TPSR_Ref TObjectID="21668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-113908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 -728.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="113908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21668"/>
     <cge:Term_Ref ObjectID="30318"/>
    <cge:TPSR_Ref TObjectID="21668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 200.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41365"/>
     <cge:Term_Ref ObjectID="62770"/>
    <cge:TPSR_Ref TObjectID="41365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 200.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41365"/>
     <cge:Term_Ref ObjectID="62770"/>
    <cge:TPSR_Ref TObjectID="41365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 200.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41365"/>
     <cge:Term_Ref ObjectID="62770"/>
    <cge:TPSR_Ref TObjectID="41365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-246555" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 199.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41364"/>
     <cge:Term_Ref ObjectID="62768"/>
    <cge:TPSR_Ref TObjectID="41364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-246556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 199.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41364"/>
     <cge:Term_Ref ObjectID="62768"/>
    <cge:TPSR_Ref TObjectID="41364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-246557" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 203.000000 199.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="246557" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41364"/>
     <cge:Term_Ref ObjectID="62768"/>
    <cge:TPSR_Ref TObjectID="41364"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="141" x="-151" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="70" x="479" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="70" x="479" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="73" x="1476" y="-636"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="73" x="1476" y="-636"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="456" y="-173"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="456" y="-173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="574" y="-174"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="574" y="-174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="725" y="-175"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="725" y="-175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="860" y="-173"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="860" y="-173"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1247" y="-172"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1247" y="-172"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1379" y="-174"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1379" y="-174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1537" y="-172"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1537" y="-172"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="442" y="-946"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="442" y="-946"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="941" y="-945"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="941" y="-945"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1323" y="-921"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1323" y="-921"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="97" y="-1079"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="97" y="-1079"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="99" y="-1037"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="99" y="-1037"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="-234" y="-667"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="-234" y="-667"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="24" y="-964"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="24" y="-964"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="250" y="-180"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="250" y="-180"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="357" y="-178"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="357" y="-178"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="35kV妥甸变35kV1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="70" x="479" y="-612"/></g>
   <g href="35kV妥甸变35kV2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="73" x="1476" y="-636"/></g>
   <g href="35kV妥甸变10kV水厂线053间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="456" y="-173"/></g>
   <g href="35kV妥甸变10kV环东线054间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="574" y="-174"/></g>
   <g href="35kV妥甸变10kV1号电容器组055间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="725" y="-175"/></g>
   <g href="35kV妥甸变10kV城区线056间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="860" y="-173"/></g>
   <g href="35kV妥甸变10kV旧街线057间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1247" y="-172"/></g>
   <g href="35kV妥甸变10kV朝阳线058间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1379" y="-174"/></g>
   <g href="35kV妥甸变10kV中山线059间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1537" y="-172"/></g>
   <g href="35kV妥甸变35kV双妥大线351间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="442" y="-946"/></g>
   <g href="35kV妥甸变35kV双小妥线352间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="941" y="-945"/></g>
   <g href="35kV妥甸变35kV白妥线353间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1323" y="-921"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="97" y="-1079"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="99" y="-1037"/></g>
   <g href="35kV妥甸变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="-234" y="-667"/></g>
   <g href="AVC妥甸站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="24" y="-964"/></g>
   <g href="35kV妥甸变10kV窝碑线051间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="250" y="-180"/></g>
   <g href="35kV妥甸变10kV桂花井线052间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="357" y="-178"/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1694,-124 1689,-134 1699,-134 1694,-124 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2366b90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 750.000000 80.000000)" xlink:href="#voltageTransformer:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f4010">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -79.000000)" xlink:href="#voltageTransformer:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_225efc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 297.000000 -1033.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2261060">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 -1031.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2265310">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.000000 -1047.000000)" xlink:href="#voltageTransformer:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_226dca0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1374.000000 -595.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.053Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 163.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34352" ObjectName="EC-SB_TD.053Ld"/>
    <cge:TPSR_Ref TObjectID="34352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 560.000000 162.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34353" ObjectName="EC-SB_TD.054Ld"/>
    <cge:TPSR_Ref TObjectID="34353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.056Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 846.000000 163.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34354" ObjectName="EC-SB_TD.056Ld"/>
    <cge:TPSR_Ref TObjectID="34354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.057Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.000000 164.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34355" ObjectName="EC-SB_TD.057Ld"/>
    <cge:TPSR_Ref TObjectID="34355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.058Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 162.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34356" ObjectName="EC-SB_TD.058Ld"/>
    <cge:TPSR_Ref TObjectID="34356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.059Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1523.000000 164.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34357" ObjectName="EC-SB_TD.059Ld"/>
    <cge:TPSR_Ref TObjectID="34357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.051Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.000000 163.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43480" ObjectName="EC-SB_TD.051Ld"/>
    <cge:TPSR_Ref TObjectID="43480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_TD.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 167.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43250" ObjectName="EC-SB_TD.052Ld"/>
    <cge:TPSR_Ref TObjectID="43250"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2417f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1067 1340,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21623@x" ObjectIDND1="g_2265f20@0" ObjectIDND2="29582@1" ObjectIDZND0="21624@0" Pin0InfoVect0LinkObjId="SW-113956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113955_0" Pin1InfoVect1LinkObjId="g_2265f20_0" Pin1InfoVect2LinkObjId="g_2268850_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1067 1340,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2418120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1376,-1067 1392,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21624@1" ObjectIDZND0="g_24188c0@0" Pin0InfoVect0LinkObjId="g_24188c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1376,-1067 1392,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-562 446,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="21676@1" ObjectIDZND0="g_2328fa0@0" Pin0InfoVect0LinkObjId="g_2328fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-562 446,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232df40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-442 446,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21667@0" ObjectIDZND0="21664@1" Pin0InfoVect0LinkObjId="SW-114110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-442 446,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232e130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-365 467,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2270410@0" ObjectIDZND0="21666@0" Pin0InfoVect0LinkObjId="SW-114113_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2270410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-365 467,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_232e320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="503,-365 524,-365 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21666@1" ObjectIDZND0="g_232d720@0" Pin0InfoVect0LinkObjId="g_232d720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="503,-365 524,-365 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_243c8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1652,-397 1673,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21671@1" ObjectIDZND0="g_243c280@0" Pin0InfoVect0LinkObjId="g_243c280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114143_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1652,-397 1673,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-306 447,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21629@1" Pin0InfoVect0LinkObjId="SW-114001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-306 447,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-245 447,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21629@0" ObjectIDZND0="21627@x" ObjectIDZND1="21630@x" Pin0InfoVect0LinkObjId="SW-113999_0" Pin0InfoVect1LinkObjId="SW-114002_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,-245 447,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-209 447,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21629@x" ObjectIDND1="21630@x" ObjectIDZND0="21627@1" Pin0InfoVect0LinkObjId="SW-113999_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114001_0" Pin1InfoVect1LinkObjId="SW-114002_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-209 447,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-209 461,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21627@x" ObjectIDND1="21629@x" ObjectIDZND0="21630@0" Pin0InfoVect0LinkObjId="SW-114002_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113999_0" Pin1InfoVect1LinkObjId="SW-114001_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-209 461,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24209f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-209 507,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21630@1" ObjectIDZND0="g_23c7500@0" Pin0InfoVect0LinkObjId="g_23c7500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="497,-209 507,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-126 477,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21627@x" ObjectIDND1="21628@x" ObjectIDZND0="g_2402980@0" Pin0InfoVect0LinkObjId="g_2402980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113999_0" Pin1InfoVect1LinkObjId="SW-114000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-126 477,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-152 447,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21627@0" ObjectIDZND0="g_2402980@0" ObjectIDZND1="21628@x" Pin0InfoVect0LinkObjId="g_2402980_0" Pin0InfoVect1LinkObjId="SW-114000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113999_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,-152 447,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2420fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-126 447,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2402980@0" ObjectIDND1="21627@x" ObjectIDZND0="21628@1" Pin0InfoVect0LinkObjId="SW-114000_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2402980_0" Pin1InfoVect1LinkObjId="SW-113999_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-126 447,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24211b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,17 447,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2346ba0@1" ObjectIDZND0="21631@1" Pin0InfoVect0LinkObjId="SW-114003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2346ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,17 447,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24213a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,93 491,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21631@x" ObjectIDND1="34352@x" ObjectIDZND0="g_23461d0@0" Pin0InfoVect0LinkObjId="g_23461d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114003_0" Pin1InfoVect1LinkObjId="EC-SB_TD.053Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,93 491,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2421590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,72 447,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21631@0" ObjectIDZND0="g_23461d0@0" ObjectIDZND1="34352@x" Pin0InfoVect0LinkObjId="g_23461d0_0" Pin0InfoVect1LinkObjId="EC-SB_TD.053Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,72 447,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2421780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,93 447,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_23461d0@0" ObjectIDND1="21631@x" ObjectIDZND0="34352@0" Pin0InfoVect0LinkObjId="EC-SB_TD.053Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23461d0_0" Pin1InfoVect1LinkObjId="SW-114003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,93 447,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f5e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-306 565,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21634@1" Pin0InfoVect0LinkObjId="SW-114016_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-306 565,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-246 565,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21634@0" ObjectIDZND0="21632@x" ObjectIDZND1="21635@x" Pin0InfoVect0LinkObjId="SW-114014_0" Pin0InfoVect1LinkObjId="SW-114017_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114016_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-246 565,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-210 565,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21634@x" ObjectIDND1="21635@x" ObjectIDZND0="21632@1" Pin0InfoVect0LinkObjId="SW-114014_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114016_0" Pin1InfoVect1LinkObjId="SW-114017_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-210 565,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-210 579,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21632@x" ObjectIDND1="21634@x" ObjectIDZND0="21635@0" Pin0InfoVect0LinkObjId="SW-114017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114014_0" Pin1InfoVect1LinkObjId="SW-114016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-210 579,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f65f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="615,-210 625,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21635@1" ObjectIDZND0="g_23afd80@0" Pin0InfoVect0LinkObjId="g_23afd80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="615,-210 625,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f67e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-127 595,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21632@x" ObjectIDND1="21633@x" ObjectIDZND0="g_2422250@0" Pin0InfoVect0LinkObjId="g_2422250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114014_0" Pin1InfoVect1LinkObjId="SW-114015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-127 595,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f69d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-153 565,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21632@0" ObjectIDZND0="g_2422250@0" ObjectIDZND1="21633@x" Pin0InfoVect0LinkObjId="g_2422250_0" Pin0InfoVect1LinkObjId="SW-114015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-153 565,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-127 565,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2422250@0" ObjectIDND1="21632@x" ObjectIDZND0="21633@1" Pin0InfoVect0LinkObjId="SW-114015_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2422250_0" Pin1InfoVect1LinkObjId="SW-114014_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-127 565,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,16 565,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_232b0e0@1" ObjectIDZND0="21636@1" Pin0InfoVect0LinkObjId="SW-114018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232b0e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,16 565,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f6fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,92 609,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21636@x" ObjectIDND1="34353@x" ObjectIDZND0="g_232a710@0" Pin0InfoVect0LinkObjId="g_232a710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114018_0" Pin1InfoVect1LinkObjId="EC-SB_TD.054Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,92 609,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,71 565,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21636@0" ObjectIDZND0="g_232a710@0" ObjectIDZND1="34353@x" Pin0InfoVect0LinkObjId="g_232a710_0" Pin0InfoVect1LinkObjId="EC-SB_TD.054Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,71 565,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,92 565,141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_232a710@0" ObjectIDND1="21636@x" ObjectIDZND0="34353@0" Pin0InfoVect0LinkObjId="EC-SB_TD.054Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_232a710_0" Pin1InfoVect1LinkObjId="SW-114018_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,92 565,141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d6f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-306 716,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21661@1" Pin0InfoVect0LinkObjId="SW-114091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-306 716,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-247 716,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21661@0" ObjectIDZND0="21659@x" ObjectIDZND1="21662@x" Pin0InfoVect0LinkObjId="SW-114089_0" Pin0InfoVect1LinkObjId="SW-114092_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="716,-247 716,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d73b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-211 716,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21661@x" ObjectIDND1="21662@x" ObjectIDZND0="21659@1" Pin0InfoVect0LinkObjId="SW-114089_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114091_0" Pin1InfoVect1LinkObjId="SW-114092_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-211 716,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d75d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-211 730,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21659@x" ObjectIDND1="21661@x" ObjectIDZND0="21662@0" Pin0InfoVect0LinkObjId="SW-114092_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114089_0" Pin1InfoVect1LinkObjId="SW-114091_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-211 730,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d77f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="766,-211 776,-211 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21662@1" ObjectIDZND0="g_23f22c0@0" Pin0InfoVect0LinkObjId="g_23f22c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114092_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="766,-211 776,-211 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-128 746,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21659@x" ObjectIDND1="21660@x" ObjectIDZND0="g_22f8060@0" Pin0InfoVect0LinkObjId="g_22f8060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114089_0" Pin1InfoVect1LinkObjId="SW-114090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-128 746,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-154 716,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21659@0" ObjectIDZND0="g_22f8060@0" ObjectIDZND1="21660@x" Pin0InfoVect0LinkObjId="g_22f8060_0" Pin0InfoVect1LinkObjId="SW-114090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114089_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="716,-154 716,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d7e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-128 716,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_22f8060@0" ObjectIDND1="21659@x" ObjectIDZND0="21660@1" Pin0InfoVect0LinkObjId="SW-114090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f8060_0" Pin1InfoVect1LinkObjId="SW-114089_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-128 716,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-306 851,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21639@1" Pin0InfoVect0LinkObjId="SW-114031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-306 851,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-245 851,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21639@0" ObjectIDZND0="21637@x" ObjectIDZND1="21640@x" Pin0InfoVect0LinkObjId="SW-114029_0" Pin0InfoVect1LinkObjId="SW-114032_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114031_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,-245 851,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-209 851,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21639@x" ObjectIDND1="21640@x" ObjectIDZND0="21637@1" Pin0InfoVect0LinkObjId="SW-114029_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114031_0" Pin1InfoVect1LinkObjId="SW-114032_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-209 851,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-209 865,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21637@x" ObjectIDND1="21639@x" ObjectIDZND0="21640@0" Pin0InfoVect0LinkObjId="SW-114032_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114029_0" Pin1InfoVect1LinkObjId="SW-114031_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-209 865,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23528b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-209 911,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21640@1" ObjectIDZND0="g_23e0640@0" Pin0InfoVect0LinkObjId="g_23e0640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114032_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-209 911,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-126 881,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21637@x" ObjectIDND1="21638@x" ObjectIDZND0="g_2417980@0" Pin0InfoVect0LinkObjId="g_2417980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114029_0" Pin1InfoVect1LinkObjId="SW-114030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-126 881,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-152 851,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21637@0" ObjectIDZND0="g_2417980@0" ObjectIDZND1="21638@x" Pin0InfoVect0LinkObjId="g_2417980_0" Pin0InfoVect1LinkObjId="SW-114030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,-152 851,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2352f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-126 851,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2417980@0" ObjectIDND1="21637@x" ObjectIDZND0="21638@1" Pin0InfoVect0LinkObjId="SW-114030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2417980_0" Pin1InfoVect1LinkObjId="SW-114029_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-126 851,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,17 851,36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_23842f0@1" ObjectIDZND0="21641@1" Pin0InfoVect0LinkObjId="SW-114033_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23842f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,17 851,36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,93 895,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21641@x" ObjectIDND1="34354@x" ObjectIDZND0="g_2383740@0" Pin0InfoVect0LinkObjId="g_2383740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114033_0" Pin1InfoVect1LinkObjId="EC-SB_TD.056Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,93 895,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,72 851,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21641@0" ObjectIDZND0="g_2383740@0" ObjectIDZND1="34354@x" Pin0InfoVect0LinkObjId="g_2383740_0" Pin0InfoVect1LinkObjId="EC-SB_TD.056Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114033_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,72 851,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2353790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,93 851,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2383740@0" ObjectIDND1="21641@x" ObjectIDZND0="34354@0" Pin0InfoVect0LinkObjId="EC-SB_TD.056Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2383740_0" Pin1InfoVect1LinkObjId="SW-114033_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,93 851,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dd700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-306 1238,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21644@1" Pin0InfoVect0LinkObjId="SW-114046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-306 1238,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dd920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-244 1238,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21644@0" ObjectIDZND0="21642@x" ObjectIDZND1="21645@x" Pin0InfoVect0LinkObjId="SW-114044_0" Pin0InfoVect1LinkObjId="SW-114047_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114046_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-244 1238,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ddb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-208 1238,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21644@x" ObjectIDND1="21645@x" ObjectIDZND0="21642@1" Pin0InfoVect0LinkObjId="SW-114044_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114046_0" Pin1InfoVect1LinkObjId="SW-114047_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-208 1238,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ddd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-208 1252,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21642@x" ObjectIDND1="21644@x" ObjectIDZND0="21645@0" Pin0InfoVect0LinkObjId="SW-114047_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114044_0" Pin1InfoVect1LinkObjId="SW-114046_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-208 1252,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ddf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-208 1298,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21645@1" ObjectIDZND0="g_2258190@0" Pin0InfoVect0LinkObjId="g_2258190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114047_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-208 1298,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22de1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-125 1268,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21642@x" ObjectIDND1="21643@x" ObjectIDZND0="g_2354440@0" Pin0InfoVect0LinkObjId="g_2354440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114044_0" Pin1InfoVect1LinkObjId="SW-114045_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-125 1268,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22de3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-151 1238,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21642@0" ObjectIDZND0="g_2354440@0" ObjectIDZND1="21643@x" Pin0InfoVect0LinkObjId="g_2354440_0" Pin0InfoVect1LinkObjId="SW-114045_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-151 1238,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22de5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-125 1238,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2354440@0" ObjectIDND1="21642@x" ObjectIDZND0="21643@1" Pin0InfoVect0LinkObjId="SW-114045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2354440_0" Pin1InfoVect1LinkObjId="SW-114044_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-125 1238,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22de800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,18 1238,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2255e90@1" ObjectIDZND0="21646@1" Pin0InfoVect0LinkObjId="SW-114048_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2255e90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,18 1238,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22dea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,94 1282,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21646@x" ObjectIDND1="34355@x" ObjectIDZND0="g_2354ff0@0" Pin0InfoVect0LinkObjId="g_2354ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114048_0" Pin1InfoVect1LinkObjId="EC-SB_TD.057Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,94 1282,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2349c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,73 1238,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21646@0" ObjectIDZND0="g_2354ff0@0" ObjectIDZND1="34355@x" Pin0InfoVect0LinkObjId="g_2354ff0_0" Pin0InfoVect1LinkObjId="EC-SB_TD.057Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114048_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,73 1238,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2349e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,94 1238,143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2354ff0@0" ObjectIDND1="21646@x" ObjectIDZND0="34355@0" Pin0InfoVect0LinkObjId="EC-SB_TD.057Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2354ff0_0" Pin1InfoVect1LinkObjId="SW-114048_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,94 1238,143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_234cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-306 1370,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21649@1" Pin0InfoVect0LinkObjId="SW-114061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-306 1370,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23aa8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-246 1370,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21649@0" ObjectIDZND0="21647@x" ObjectIDZND1="21650@x" Pin0InfoVect0LinkObjId="SW-114059_0" Pin0InfoVect1LinkObjId="SW-114062_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-246 1370,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23aab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-210 1370,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21649@x" ObjectIDND1="21650@x" ObjectIDZND0="21647@1" Pin0InfoVect0LinkObjId="SW-114059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114061_0" Pin1InfoVect1LinkObjId="SW-114062_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-210 1370,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23aad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-210 1384,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21647@x" ObjectIDND1="21649@x" ObjectIDZND0="21650@0" Pin0InfoVect0LinkObjId="SW-114062_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114059_0" Pin1InfoVect1LinkObjId="SW-114061_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-210 1384,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23aafe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1420,-210 1430,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21650@1" ObjectIDZND0="g_234b9e0@0" Pin0InfoVect0LinkObjId="g_234b9e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114062_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1420,-210 1430,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ab240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-127 1400,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21647@x" ObjectIDND1="21648@x" ObjectIDZND0="g_234a890@0" Pin0InfoVect0LinkObjId="g_234a890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114059_0" Pin1InfoVect1LinkObjId="SW-114060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-127 1400,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ab4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-153 1370,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21647@0" ObjectIDZND0="g_234a890@0" ObjectIDZND1="21648@x" Pin0InfoVect0LinkObjId="g_234a890_0" Pin0InfoVect1LinkObjId="SW-114060_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-153 1370,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ab700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-127 1370,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_234a890@0" ObjectIDND1="21647@x" ObjectIDZND0="21648@1" Pin0InfoVect0LinkObjId="SW-114060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_234a890_0" Pin1InfoVect1LinkObjId="SW-114059_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-127 1370,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ab960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,16 1370,35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_234b1e0@1" ObjectIDZND0="21651@1" Pin0InfoVect0LinkObjId="SW-114063_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_234b1e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,16 1370,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23abbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,92 1414,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21651@x" ObjectIDND1="34356@x" ObjectIDZND0="g_234ac70@0" Pin0InfoVect0LinkObjId="g_234ac70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114063_0" Pin1InfoVect1LinkObjId="EC-SB_TD.058Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,92 1414,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23abe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,71 1370,92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21651@0" ObjectIDZND0="g_234ac70@0" ObjectIDZND1="34356@x" Pin0InfoVect0LinkObjId="g_234ac70_0" Pin0InfoVect1LinkObjId="EC-SB_TD.058Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114063_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1370,71 1370,92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ac080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,92 1370,141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_234ac70@0" ObjectIDND1="21651@x" ObjectIDZND0="34356@0" Pin0InfoVect0LinkObjId="EC-SB_TD.058Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_234ac70_0" Pin1InfoVect1LinkObjId="SW-114063_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,92 1370,141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2364650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-306 1528,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21654@1" Pin0InfoVect0LinkObjId="SW-114076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-306 1528,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23648d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-244 1528,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21654@0" ObjectIDZND0="21652@x" ObjectIDZND1="21655@x" Pin0InfoVect0LinkObjId="SW-114074_0" Pin0InfoVect1LinkObjId="SW-114077_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-244 1528,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2364b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-208 1528,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21654@x" ObjectIDND1="21655@x" ObjectIDZND0="21652@1" Pin0InfoVect0LinkObjId="SW-114074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114076_0" Pin1InfoVect1LinkObjId="SW-114077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-208 1528,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2364d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-208 1542,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21652@x" ObjectIDND1="21654@x" ObjectIDZND0="21655@0" Pin0InfoVect0LinkObjId="SW-114077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114074_0" Pin1InfoVect1LinkObjId="SW-114076_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-208 1542,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2364fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-208 1588,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21655@1" ObjectIDZND0="g_2363440@0" Pin0InfoVect0LinkObjId="g_2363440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-208 1588,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-125 1558,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21652@x" ObjectIDND1="21653@x" ObjectIDZND0="g_23acbb0@0" Pin0InfoVect0LinkObjId="g_23acbb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114074_0" Pin1InfoVect1LinkObjId="SW-114075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-125 1558,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-151 1528,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="21652@0" ObjectIDZND0="g_23acbb0@0" ObjectIDZND1="21653@x" Pin0InfoVect0LinkObjId="g_23acbb0_0" Pin0InfoVect1LinkObjId="SW-114075_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-151 1528,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23656e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-125 1528,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_23acbb0@0" ObjectIDND1="21652@x" ObjectIDZND0="21653@1" Pin0InfoVect0LinkObjId="SW-114075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23acbb0_0" Pin1InfoVect1LinkObjId="SW-114074_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-125 1528,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,18 1528,37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_23ad460@1" ObjectIDZND0="21656@1" Pin0InfoVect0LinkObjId="SW-114078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23ad460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,18 1528,37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,94 1572,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21656@x" ObjectIDND1="34357@x" ObjectIDZND0="g_23acf30@0" Pin0InfoVect0LinkObjId="g_23acf30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114078_0" Pin1InfoVect1LinkObjId="EC-SB_TD.059Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,94 1572,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2365e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,73 1528,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21656@0" ObjectIDZND0="g_23acf30@0" ObjectIDZND1="34357@x" Pin0InfoVect0LinkObjId="g_23acf30_0" Pin0InfoVect1LinkObjId="EC-SB_TD.059Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,73 1528,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2366060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,94 1528,143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_23acf30@0" ObjectIDND1="21656@x" ObjectIDZND0="34357@0" Pin0InfoVect0LinkObjId="EC-SB_TD.059Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23acf30_0" Pin1InfoVect1LinkObjId="SW-114078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,94 1528,143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_235faa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,73 755,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" EndDevType0="voltageTransformer" ObjectIDND0="g_23eff90@0" ObjectIDND1="40087@x" ObjectIDZND0="g_2366b90@0" Pin0InfoVect0LinkObjId="g_2366b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23eff90_0" Pin1InfoVect1LinkObjId="CB-SB_TD.SB_TD_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,73 755,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_235fd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,109 716,73 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="40087@0" ObjectIDZND0="g_23eff90@0" ObjectIDZND1="g_2366b90@0" Pin0InfoVect0LinkObjId="g_23eff90_0" Pin0InfoVect1LinkObjId="g_2366b90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-SB_TD.SB_TD_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="716,109 716,73 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_235ff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,73 716,15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="40087@x" ObjectIDND1="g_2366b90@0" ObjectIDZND0="g_23eff90@1" Pin0InfoVect0LinkObjId="g_23eff90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-SB_TD.SB_TD_Cb1_0" Pin1InfoVect1LinkObjId="g_2366b90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,73 716,15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23601c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-306 1082,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21673@1" Pin0InfoVect0LinkObjId="SW-114170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-306 1082,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2360420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-306 1694,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="21658@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-306 1694,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23be5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-241 1082,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="21673@0" ObjectIDZND0="21674@x" ObjectIDZND1="g_22f26f0@0" ObjectIDZND2="g_23d01f0@0" Pin0InfoVect0LinkObjId="SW-114171_0" Pin0InfoVect1LinkObjId="g_22f26f0_0" Pin0InfoVect2LinkObjId="g_23d01f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-241 1082,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23be850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-234 1694,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="21658@0" ObjectIDZND0="21657@x" ObjectIDZND1="g_23d0860@0" Pin0InfoVect0LinkObjId="SW-114081_0" Pin0InfoVect1LinkObjId="g_23d0860_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-234 1694,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23beab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-208 1095,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="21673@x" ObjectIDND1="g_22f26f0@0" ObjectIDND2="g_23d01f0@0" ObjectIDZND0="21674@0" Pin0InfoVect0LinkObjId="SW-114171_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-114170_0" Pin1InfoVect1LinkObjId="g_22f26f0_0" Pin1InfoVect2LinkObjId="g_23d01f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-208 1095,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23bed10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1131,-208 1147,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21674@1" ObjectIDZND0="g_23bd0d0@0" Pin0InfoVect0LinkObjId="g_23bd0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1131,-208 1147,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f2230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-202 1709,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="21658@x" ObjectIDND1="g_23d0860@0" ObjectIDZND0="21657@0" Pin0InfoVect0LinkObjId="SW-114081_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_23d0860_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-202 1709,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f2490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-202 1756,-202 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21657@1" ObjectIDZND0="g_23bdb60@0" Pin0InfoVect0LinkObjId="g_23bdb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114081_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-202 1756,-202 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f30c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1114,-168 1114,-180 1082,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_22f26f0@0" ObjectIDZND0="21674@x" ObjectIDZND1="21673@x" ObjectIDZND2="g_23d01f0@0" Pin0InfoVect0LinkObjId="SW-114171_0" Pin0InfoVect1LinkObjId="SW-114170_0" Pin0InfoVect2LinkObjId="g_23d01f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f26f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1114,-168 1114,-180 1082,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f3320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-180 1082,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_22f26f0@0" ObjectIDND1="g_23d01f0@0" ObjectIDZND0="21674@x" ObjectIDZND1="21673@x" Pin0InfoVect0LinkObjId="SW-114171_0" Pin0InfoVect1LinkObjId="SW-114170_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_22f26f0_0" Pin1InfoVect1LinkObjId="g_23d01f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-180 1082,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-63 1083,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_22f26f0@0" ObjectIDZND0="g_22f3580@0" Pin0InfoVect0LinkObjId="g_22f3580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f26f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-63 1083,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1045,-79 1045,-63 1083,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" EndDevType1="lightningRod" ObjectIDZND0="g_22f3580@0" ObjectIDZND1="g_22f26f0@0" Pin0InfoVect0LinkObjId="g_22f3580_0" Pin0InfoVect1LinkObjId="g_22f26f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1045,-79 1045,-63 1083,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-63 1114,-63 1114,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_22f3580@0" ObjectIDZND0="g_22f26f0@1" Pin0InfoVect0LinkObjId="g_22f26f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22f3580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1083,-63 1114,-63 1114,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23dae30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-180 1044,-180 1044,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_22f26f0@0" ObjectIDND1="21674@x" ObjectIDND2="21673@x" ObjectIDZND0="g_23d01f0@0" Pin0InfoVect0LinkObjId="g_23d01f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_22f26f0_0" Pin1InfoVect1LinkObjId="SW-114171_0" Pin1InfoVect2LinkObjId="SW-114170_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-180 1044,-180 1044,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23db090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1044,-131 1044,-108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_23d01f0@1" ObjectIDZND0="g_22f4010@0" Pin0InfoVect0LinkObjId="g_22f4010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d01f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1044,-131 1044,-108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23db2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-202 1694,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="21657@x" ObjectIDND1="21658@x" ObjectIDZND0="g_23d0860@0" Pin0InfoVect0LinkObjId="g_23d0860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114081_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-202 1694,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_231b370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1694,-151 1694,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_23d0860@1" ObjectIDZND0="21678@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23d0860_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1694,-151 1694,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2376c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-317 446,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21665@0" ObjectIDZND0="21620@0" Pin0InfoVect0LinkObjId="g_229a990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-317 446,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ee5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-50 462,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21628@x" ObjectIDND1="g_2346ba0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114000_0" Pin1InfoVect1LinkObjId="g_2346ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-50 462,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ee800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-56 583,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21633@x" ObjectIDND1="g_232b0e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114015_0" Pin1InfoVect1LinkObjId="g_232b0e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-56 583,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22eea60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-55 731,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21660@x" ObjectIDND1="g_23eff90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114090_0" Pin1InfoVect1LinkObjId="g_23eff90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-55 731,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22eecc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-51 866,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21638@x" ObjectIDND1="g_23842f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114030_0" Pin1InfoVect1LinkObjId="g_23842f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-51 866,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22eef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-52 1253,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21643@x" ObjectIDND1="g_2255e90@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114045_0" Pin1InfoVect1LinkObjId="g_2255e90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-52 1253,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ef180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-66 447,-49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21628@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2346ba0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2346ba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,-66 447,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ef3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-49 447,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21628@x" ObjectIDZND0="g_2346ba0@0" Pin0InfoVect0LinkObjId="g_2346ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-49 447,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ef640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-67 565,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21633@0" ObjectIDZND0="0@x" ObjectIDZND1="g_232b0e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_232b0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114015_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="565,-67 565,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22ef8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="565,-55 565,-37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21633@x" ObjectIDZND0="g_232b0e0@0" Pin0InfoVect0LinkObjId="g_232b0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="565,-55 565,-37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22efb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-68 716,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21660@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23eff90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23eff90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="716,-68 716,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22efd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-54 716,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21660@x" ObjectIDZND0="g_23eff90@0" Pin0InfoVect0LinkObjId="g_23eff90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114090_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-54 716,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22effc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-66 851,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21638@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23842f0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23842f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="851,-66 851,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f0220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="851,-50 851,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21638@x" ObjectIDZND0="g_23842f0@0" Pin0InfoVect0LinkObjId="g_23842f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="851,-50 851,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f0480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-65 1238,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21643@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2255e90@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2255e90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-65 1238,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f06e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1238,-51 1238,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21643@x" ObjectIDZND0="g_2255e90@0" Pin0InfoVect0LinkObjId="g_2255e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114045_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1238,-51 1238,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22f1ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-51 1538,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21653@x" ObjectIDND1="g_23ad460@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114075_0" Pin1InfoVect1LinkObjId="g_23ad460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-51 1538,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2336b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1369,-55 1379,-55 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21648@x" ObjectIDND1="g_234b1e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114060_0" Pin1InfoVect1LinkObjId="g_234b1e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1369,-55 1379,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2336d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-67 1370,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21648@0" ObjectIDZND0="0@x" ObjectIDZND1="g_234b1e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_234b1e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-67 1370,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2336fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1370,-54 1370,-37 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21648@x" ObjectIDZND0="g_234b1e0@0" Pin0InfoVect0LinkObjId="g_234b1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1370,-54 1370,-37 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2337220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-65 1528,-50 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21653@0" ObjectIDZND0="0@x" ObjectIDZND1="g_23ad460@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23ad460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-65 1528,-50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2337480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1528,-50 1528,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21653@x" ObjectIDZND0="g_23ad460@0" Pin0InfoVect0LinkObjId="g_23ad460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114075_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1528,-50 1528,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2302d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-1079 412,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="28744@x" ObjectIDND1="34568@1" ObjectIDZND0="g_225cdc0@1" Pin0InfoVect0LinkObjId="g_225cdc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134558_0" Pin1InfoVect1LinkObjId="g_22472a0_1" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-1079 412,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2303540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-437 1601,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21669@1" ObjectIDZND0="21672@0" Pin0InfoVect0LinkObjId="SW-114144_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114140_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-437 1601,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2303730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-568 1601,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_232e510@0" ObjectIDZND0="21677@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_232e510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-568 1601,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2303920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1567,-397 1616,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22999c0@0" ObjectIDZND0="21671@0" Pin0InfoVect0LinkObjId="SW-114143_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22999c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1567,-397 1616,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22983d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,-723 360,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28753@0" ObjectIDZND0="g_2297940@0" Pin0InfoVect0LinkObjId="g_2297940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="379,-723 360,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c2570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-783 1601,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28754@1" ObjectIDZND0="21619@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134809_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-783 1601,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-719 1601,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21668@1" ObjectIDZND0="28754@x" ObjectIDZND1="28755@x" Pin0InfoVect0LinkObjId="SW-134809_0" Pin0InfoVect1LinkObjId="SW-134810_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114134_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-719 1601,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c2a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-734 1601,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21668@x" ObjectIDND1="28755@x" ObjectIDZND0="28754@0" Pin0InfoVect0LinkObjId="SW-134809_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114134_0" Pin1InfoVect1LinkObjId="SW-134810_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-734 1601,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c5c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-734 1569,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="28754@x" ObjectIDND1="21668@x" ObjectIDZND0="28755@1" Pin0InfoVect0LinkObjId="SW-134810_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134809_0" Pin1InfoVect1LinkObjId="SW-114134_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-734 1569,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c5eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1533,-734 1514,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28755@0" ObjectIDZND0="g_22c51c0@0" Pin0InfoVect0LinkObjId="g_22c51c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134810_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1533,-734 1514,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c6110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1392,-794 1392,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="24443@1" Pin0InfoVect0LinkObjId="SW-134925_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1392,-794 1392,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c9330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-723 415,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21663@x" ObjectIDND1="28752@x" ObjectIDZND0="28753@1" Pin0InfoVect0LinkObjId="SW-134751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114104_0" Pin1InfoVect1LinkObjId="SW-134750_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-723 415,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c9590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-708 447,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21663@1" ObjectIDZND0="28753@x" ObjectIDZND1="28752@x" Pin0InfoVect0LinkObjId="SW-134751_0" Pin0InfoVect1LinkObjId="SW-134750_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,-708 447,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c97f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-794 447,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="28752@1" Pin0InfoVect0LinkObjId="SW-134750_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-794 447,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22c9a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-733 447,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="28752@0" ObjectIDZND0="28753@x" ObjectIDZND1="21663@x" Pin0InfoVect0LinkObjId="SW-134751_0" Pin0InfoVect1LinkObjId="SW-114104_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134750_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="447,-733 447,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22472a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-1079 428,-1094 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="powerLine" ObjectIDND0="g_225cdc0@0" ObjectIDND1="28744@x" ObjectIDZND0="34568@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_225cdc0_0" Pin1InfoVect1LinkObjId="SW-134558_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-1079 428,-1094 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2247500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-952 428,-979 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21626@1" ObjectIDZND0="28744@0" Pin0InfoVect0LinkObjId="SW-134558_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-952 428,-979 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2247d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-794 428,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="28743@0" Pin0InfoVect0LinkObjId="SW-134557_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-794 428,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2249270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-905 428,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28743@x" ObjectIDND1="28745@x" ObjectIDZND0="21626@0" Pin0InfoVect0LinkObjId="SW-113984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-134557_0" Pin1InfoVect1LinkObjId="SW-188659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-905 428,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22494d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-855 428,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28743@1" ObjectIDZND0="21626@x" ObjectIDZND1="28745@x" Pin0InfoVect0LinkObjId="SW-113984_0" Pin0InfoVect1LinkObjId="SW-188659_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="428,-855 428,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2249730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,-844 396,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2247f90@0" ObjectIDZND0="28745@0" Pin0InfoVect0LinkObjId="SW-188659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2247f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="396,-844 396,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2249990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="396,-896 396,-905 428,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28745@1" ObjectIDZND0="21626@x" ObjectIDZND1="28743@x" Pin0InfoVect0LinkObjId="SW-113984_0" Pin0InfoVect1LinkObjId="SW-134557_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="396,-896 396,-905 428,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2253f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="932,-904 932,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28748@x" ObjectIDND1="28746@x" ObjectIDZND0="21625@0" Pin0InfoVect0LinkObjId="SW-113969_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188662_0" Pin1InfoVect1LinkObjId="SW-134578_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="932,-904 932,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2254170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-843 900,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22534c0@0" ObjectIDZND0="28748@0" Pin0InfoVect0LinkObjId="SW-188662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22534c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="900,-843 900,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22543d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-895 900,-904 932,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28748@1" ObjectIDZND0="21625@x" ObjectIDZND1="28746@x" Pin0InfoVect0LinkObjId="SW-113969_0" Pin0InfoVect1LinkObjId="SW-134578_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="900,-895 900,-904 932,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2254ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-949 931,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21625@1" ObjectIDZND0="28747@0" Pin0InfoVect0LinkObjId="SW-113979_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113969_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-949 931,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2255120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="932,-794 932,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="28746@0" Pin0InfoVect0LinkObjId="SW-134578_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="932,-794 932,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2255380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="932,-854 932,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28746@1" ObjectIDZND0="21625@x" ObjectIDZND1="28748@x" Pin0InfoVect0LinkObjId="SW-113969_0" Pin0InfoVect1LinkObjId="SW-188662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134578_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="932,-854 932,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_247fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-886 1313,-901 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="28751@x" ObjectIDND1="28749@x" ObjectIDZND0="21622@0" Pin0InfoVect0LinkObjId="SW-113954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-188665_0" Pin1InfoVect1LinkObjId="SW-134600_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-886 1313,-901 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_247ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-825 1282,-841 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_247f2a0@0" ObjectIDZND0="28751@0" Pin0InfoVect0LinkObjId="SW-188665_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_247f2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-825 1282,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24801b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1282,-877 1282,-886 1313,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28751@1" ObjectIDZND0="21622@x" ObjectIDZND1="28749@x" Pin0InfoVect0LinkObjId="SW-113954_0" Pin0InfoVect1LinkObjId="SW-134600_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188665_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1282,-877 1282,-886 1313,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2481530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1049 1313,-1067 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21623@1" ObjectIDZND0="21624@x" ObjectIDZND1="g_2265f20@0" ObjectIDZND2="29582@1" Pin0InfoVect0LinkObjId="SW-113956_0" Pin0InfoVect1LinkObjId="g_2265f20_0" Pin0InfoVect2LinkObjId="g_2268850_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113955_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1049 1313,-1067 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2482020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1013 1313,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21623@0" ObjectIDZND0="28750@x" ObjectIDZND1="g_23db6a0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-134601_0" Pin0InfoVect1LinkObjId="g_23db6a0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-113955_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1013 1313,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2482280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-794 1313,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21619@0" ObjectIDZND0="28749@0" Pin0InfoVect0LinkObjId="SW-134600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c2570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-794 1313,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24824e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-854 1313,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="28749@1" ObjectIDZND0="21622@x" ObjectIDZND1="28751@x" Pin0InfoVect0LinkObjId="SW-113954_0" Pin0InfoVect1LinkObjId="SW-188665_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-854 1313,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2482d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-995 1313,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="21623@x" ObjectIDND1="g_23db6a0@0" ObjectIDND2="0@x" ObjectIDZND0="28750@1" Pin0InfoVect0LinkObjId="SW-134601_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113955_0" Pin1InfoVect1LinkObjId="g_23db6a0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-995 1313,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2482f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-949 1313,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28750@0" ObjectIDZND0="21622@1" Pin0InfoVect0LinkObjId="SW-113954_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-949 1313,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23830e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-973 1384,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_23db6a0@0" ObjectIDZND0="21623@x" ObjectIDZND1="28750@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-113955_0" Pin0InfoVect1LinkObjId="SW-134601_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_23db6a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-973 1384,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23832d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1384,-995 1313,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_23db6a0@0" ObjectIDND1="0@x" ObjectIDZND0="21623@x" ObjectIDZND1="28750@x" Pin0InfoVect0LinkObjId="SW-113955_0" Pin0InfoVect1LinkObjId="SW-134601_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_23db6a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1384,-995 1313,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23834c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1431,-978 1431,-996 1430,-995 1384,-995 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_23db6a0@0" ObjectIDZND1="21623@x" ObjectIDZND2="28750@x" Pin0InfoVect0LinkObjId="g_23db6a0_0" Pin0InfoVect1LinkObjId="SW-113955_0" Pin0InfoVect2LinkObjId="SW-134601_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1431,-978 1431,-996 1430,-995 1384,-995 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_225b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1431,-926 1431,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="24539@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1431,-926 1431,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225d580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="346,-1079 346,-1065 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_225cdc0@0" ObjectIDND1="g_225efc0@0" ObjectIDZND0="g_225e2b0@0" Pin0InfoVect0LinkObjId="g_225e2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_225cdc0_0" Pin1InfoVect1LinkObjId="g_225efc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="346,-1079 346,-1065 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225e050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="367,-1079 346,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="g_225cdc0@0" ObjectIDZND0="g_225e2b0@0" ObjectIDZND1="g_225efc0@0" Pin0InfoVect0LinkObjId="g_225e2b0_0" Pin0InfoVect1LinkObjId="g_225efc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225cdc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="367,-1079 346,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225fbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-1064 306,-1079 346,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_225efc0@0" ObjectIDZND0="g_225cdc0@0" ObjectIDZND1="g_225e2b0@0" Pin0InfoVect0LinkObjId="g_225cdc0_0" Pin0InfoVect1LinkObjId="g_225e2b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_225efc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="306,-1064 306,-1079 346,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_225fe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-1015 428,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="28744@1" ObjectIDZND0="g_225cdc0@0" ObjectIDZND1="34568@1" Pin0InfoVect0LinkObjId="g_225cdc0_0" Pin0InfoVect1LinkObjId="g_22472a0_1" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="428,-1015 428,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2260090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="867,-1077 846,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_2261c70@0" ObjectIDZND0="g_2261060@0" ObjectIDZND1="g_22602f0@0" Pin0InfoVect0LinkObjId="g_2261060_0" Pin0InfoVect1LinkObjId="g_22602f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2261c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="867,-1077 846,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22628a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="930,-1077 914,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="powerLine" EndDevType0="lightningRod" ObjectIDND0="28747@x" ObjectIDND1="37758@1" ObjectIDZND0="g_2261c70@1" Pin0InfoVect0LinkObjId="g_2261c70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113979_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="930,-1077 914,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2263390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-1092 931,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="37758@1" ObjectIDZND0="g_2261c70@0" ObjectIDZND1="28747@x" Pin0InfoVect0LinkObjId="g_2261c70_0" Pin0InfoVect1LinkObjId="SW-113979_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="931,-1092 931,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22635f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="931,-1077 931,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" ObjectIDND0="g_2261c70@0" ObjectIDND1="37758@1" ObjectIDZND0="28747@1" Pin0InfoVect0LinkObjId="SW-113979_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2261c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="931,-1077 931,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22640e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="808,-1062 808,-1077 848,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2261060@0" ObjectIDZND0="g_2261c70@0" ObjectIDZND1="g_22602f0@0" Pin0InfoVect0LinkObjId="g_2261c70_0" Pin0InfoVect1LinkObjId="g_22602f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2261060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="808,-1062 808,-1077 848,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2264340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-1077 848,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2261c70@0" ObjectIDND1="g_2261060@0" ObjectIDZND0="g_22602f0@0" Pin0InfoVect0LinkObjId="g_22602f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2261c70_0" Pin1InfoVect1LinkObjId="g_2261060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-1077 848,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2266b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1093 1298,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="21624@x" ObjectIDND1="21623@x" ObjectIDND2="29582@1" ObjectIDZND0="g_2265f20@1" Pin0InfoVect0LinkObjId="g_2265f20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-113956_0" Pin1InfoVect1LinkObjId="SW-113955_0" Pin1InfoVect2LinkObjId="g_2268850_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1093 1298,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2266db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-1078 1192,-1093 1232,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2265310@0" ObjectIDZND0="g_22645a0@0" ObjectIDZND1="g_2265f20@0" Pin0InfoVect0LinkObjId="g_22645a0_0" Pin0InfoVect1LinkObjId="g_2265f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2265310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-1078 1192,-1093 1232,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2267010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1232,-1093 1232,-1079 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2265310@0" ObjectIDND1="g_2265f20@0" ObjectIDZND0="g_22645a0@0" Pin0InfoVect0LinkObjId="g_22645a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2265310_0" Pin1InfoVect1LinkObjId="g_2265f20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1232,-1093 1232,-1079 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2267270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1253,-1093 1232,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="g_2265f20@0" ObjectIDZND0="g_2265310@0" ObjectIDZND1="g_22645a0@0" Pin0InfoVect0LinkObjId="g_2265310_0" Pin0InfoVect1LinkObjId="g_22645a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2265f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1253,-1093 1232,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22685f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1067 1313,-1093 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21624@x" ObjectIDND1="21623@x" ObjectIDZND0="g_2265f20@0" ObjectIDZND1="29582@1" Pin0InfoVect0LinkObjId="g_2265f20_0" Pin0InfoVect1LinkObjId="g_2268850_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-113956_0" Pin1InfoVect1LinkObjId="SW-113955_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1067 1313,-1093 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2268850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1313,-1093 1313,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2265f20@0" ObjectIDND1="21624@x" ObjectIDND2="21623@x" ObjectIDZND0="29582@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2265f20_0" Pin1InfoVect1LinkObjId="SW-113956_0" Pin1InfoVect2LinkObjId="SW-113955_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1313,-1093 1313,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2268ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="447,-645 447,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21676@0" ObjectIDZND0="21663@0" Pin0InfoVect0LinkObjId="SW-114104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="447,-645 447,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226c4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-723 1320,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_22c88a0@0" ObjectIDZND0="28756@0" Pin0InfoVect0LinkObjId="SW-188671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22c88a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-723 1320,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226c6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1392,-743 1392,-709 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="24443@0" ObjectIDZND0="g_2402150@0" Pin0InfoVect0LinkObjId="g_2402150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-134925_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1392,-743 1392,-709 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1444,-699 1444,-723 1356,-723 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_226c910@0" ObjectIDZND0="28756@1" Pin0InfoVect0LinkObjId="SW-188671_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_226c910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1444,-699 1444,-723 1356,-723 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1392,-678 1392,-648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2402150@1" ObjectIDZND0="g_226dca0@0" Pin0InfoVect0LinkObjId="g_226dca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2402150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1392,-678 1392,-648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226fb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-664 1601,-691 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21677@1" ObjectIDZND0="21668@0" Pin0InfoVect0LinkObjId="SW-114134_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2303730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-664 1601,-691 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2270030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-385 1601,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21670@1" ObjectIDZND0="21669@0" Pin0InfoVect0LinkObjId="SW-114140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-385 1601,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2270220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-353 446,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21665@1" ObjectIDZND0="21664@0" Pin0InfoVect0LinkObjId="SW-114110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114112_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-353 446,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-349 1601,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21670@0" ObjectIDZND0="21620@0" Pin0InfoVect0LinkObjId="g_2376c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-349 1601,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229abf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="448,-487 463,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21667@x" ObjectIDND1="g_2328fa0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114114_0" Pin1InfoVect1LinkObjId="g_2328fa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="448,-487 463,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229c170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-478 446,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21667@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2328fa0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2328fa0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114114_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="446,-478 446,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229c3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="446,-487 446,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21667@x" ObjectIDZND0="g_2328fa0@1" Pin0InfoVect0LinkObjId="g_2328fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114114_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="446,-487 446,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229c630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-503 1618,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="21672@x" ObjectIDND1="g_232e510@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-114144_0" Pin1InfoVect1LinkObjId="g_232e510_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-503 1618,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229dc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-490 1601,-503 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" ObjectIDND0="21672@1" ObjectIDZND0="0@x" ObjectIDZND1="g_232e510@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_232e510_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-114144_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-490 1601,-503 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_229de90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-503 1601,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="21672@x" ObjectIDZND0="g_232e510@1" Pin0InfoVect0LinkObjId="g_232e510_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-114144_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-503 1601,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_22aa450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-307 234,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="41366@1" Pin0InfoVect0LinkObjId="SW-246545_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-307 234,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_227ca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-306 341,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21620@0" ObjectIDZND0="41369@1" Pin0InfoVect0LinkObjId="SW-246546_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2376c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-306 341,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2235420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,142 234,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43480@0" ObjectIDZND0="g_222f3c0@0" ObjectIDZND1="41368@x" Pin0InfoVect0LinkObjId="g_222f3c0_0" Pin0InfoVect1LinkObjId="SW-246550_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-SB_TD.051Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,142 234,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2235610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,93 278,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="43480@x" ObjectIDND1="41368@x" ObjectIDZND0="g_222f3c0@0" Pin0InfoVect0LinkObjId="g_222f3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-SB_TD.051Ld_0" Pin1InfoVect1LinkObjId="SW-246550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,93 278,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2236010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,97 340,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2231a90@0" ObjectIDZND0="43250@x" ObjectIDZND1="41371@x" Pin0InfoVect0LinkObjId="EC-SB_TD.052Ld_0" Pin0InfoVect1LinkObjId="SW-246548_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2231a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="384,97 340,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2236270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,97 340,146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2231a90@0" ObjectIDND1="41371@x" ObjectIDZND0="43250@0" Pin0InfoVect0LinkObjId="EC-SB_TD.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2231a90_0" Pin1InfoVect1LinkObjId="SW-246548_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="340,97 340,146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-179 234,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41364@1" ObjectIDZND0="41366@0" Pin0InfoVect0LinkObjId="SW-246545_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246541_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-179 234,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-179 341,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41365@1" ObjectIDZND0="41369@0" Pin0InfoVect0LinkObjId="SW-246546_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246543_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-179 341,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-102 234,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41367@1" ObjectIDZND0="41364@0" Pin0InfoVect0LinkObjId="SW-246541_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246549_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-102 234,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-102 341,-152 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41370@1" ObjectIDZND0="41365@0" Pin0InfoVect0LinkObjId="SW-246543_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246547_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-102 341,-152 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,16 234,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_222e670@1" ObjectIDZND0="41368@1" Pin0InfoVect0LinkObjId="SW-246550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_222e670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,16 234,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,76 234,93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="41368@0" ObjectIDZND0="43480@x" ObjectIDZND1="g_222f3c0@0" Pin0InfoVect0LinkObjId="EC-SB_TD.051Ld_0" Pin0InfoVect1LinkObjId="g_222f3c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,76 234,93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2238e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,97 340,72 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2231a90@0" ObjectIDND1="43250@x" ObjectIDZND0="41371@0" Pin0InfoVect0LinkObjId="SW-246548_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2231a90_0" Pin1InfoVect1LinkObjId="EC-SB_TD.052Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="340,97 340,72 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-47 278,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_222e670@0" ObjectIDND1="41367@x" ObjectIDZND0="g_223aa00@0" Pin0InfoVect0LinkObjId="g_223aa00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_222e670_0" Pin1InfoVect1LinkObjId="SW-246549_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-47 278,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223c260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-37 234,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_222e670@0" ObjectIDZND0="g_223aa00@0" ObjectIDZND1="41367@x" Pin0InfoVect0LinkObjId="g_223aa00_0" Pin0InfoVect1LinkObjId="SW-246549_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_222e670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="234,-37 234,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-47 234,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_223aa00@0" ObjectIDND1="g_222e670@0" ObjectIDZND0="41367@0" Pin0InfoVect0LinkObjId="SW-246549_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_223aa00_0" Pin1InfoVect1LinkObjId="g_222e670_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="234,-47 234,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223d490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-47 385,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="41371@x" ObjectIDND1="41370@x" ObjectIDZND0="g_223c720@0" Pin0InfoVect0LinkObjId="g_223c720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246548_0" Pin1InfoVect1LinkObjId="SW-246547_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="341,-47 385,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223df80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,36 340,-7 340,-46 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="41371@1" ObjectIDZND0="41370@x" ObjectIDZND1="g_223c720@0" Pin0InfoVect0LinkObjId="SW-246547_0" Pin0InfoVect1LinkObjId="g_223c720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-246548_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="340,36 340,-7 340,-46 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_223e1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="340,-46 341,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="41371@x" ObjectIDND1="g_223c720@0" ObjectIDZND0="41370@0" Pin0InfoVect0LinkObjId="SW-246547_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-246548_0" Pin1InfoVect1LinkObjId="g_223c720_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="340,-46 341,-66 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-111359" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 43.000000 -970.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21417" ObjectName="DYN-SB_TD"/>
     <cge:Meas_Ref ObjectId="111359"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236f9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 395.000000 -205.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b5dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 384.000000 -220.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b6c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 409.000000 -235.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b75e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1635.000000 621.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22b7e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1635.000000 603.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc4c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 493.000000 958.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.000000 943.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bc930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 928.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcc60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 958.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bcec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 943.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1003.000000 928.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 946.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1158.000000 931.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bd8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.000000 916.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bdc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 725.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22bde60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 710.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 695.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22be3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 439.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f8930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 508.000000 424.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f8b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 409.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f8ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 454.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f9100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1656.000000 439.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f9340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 424.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f9670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 729.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f98d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 714.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f9b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1706.000000 699.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22f9e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 518.000000 -203.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fa0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 507.000000 -218.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fa2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 -233.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fa610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -202.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fa870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 672.000000 -217.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22faab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -232.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -205.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fb040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 809.000000 -220.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fb280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -235.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fb5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 -205.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fb810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1189.000000 -220.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fba50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1214.000000 -235.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fbd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1331.000000 -204.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fbfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1320.000000 -219.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fc220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1345.000000 -234.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fc550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -205.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fc7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 -220.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fc9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -235.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fcd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 269.000000 605.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22fcf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 269.000000 587.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2304380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 401.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2304c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 387.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23051e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 373.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23057a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 181.000000 357.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2305a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 177.000000 341.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228b450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 877.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228b6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 863.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228b900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 849.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228bb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 188.000000 833.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228bd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 184.000000 817.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a27b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1490.000000 929.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a2c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.000000 915.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a2e70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1499.000000 901.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a30b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 884.000000) translate(0,12)">Ua(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a33e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1650.000000 13.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a3650" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1639.000000 -1.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a3890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1659.000000 -15.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a3ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1653.000000 -32.000000) translate(0,12)">Ua(V):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22365c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 149.000000 -196.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2236ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 -211.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2236d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 163.000000 -226.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2237040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 279.000000 -196.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22372a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 268.000000 -211.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22374e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 293.000000 -226.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_TD" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangtuodaTtd" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="428,-1092 428,-1129 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34568" ObjectName="AC-35kV.LN_shuangtuodaTtd"/>
    <cge:TPSR_Ref TObjectID="34568_SS-164"/></metadata>
   <polyline fill="none" opacity="0" points="428,-1092 428,-1129 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SB" endPointId="0" endStationName="SB_TD" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_shuangxiaotuo" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="931,-1092 931,-1131 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37758" ObjectName="AC-35kV.LN_shuangxiaotuo"/>
    <cge:TPSR_Ref TObjectID="37758_SS-164"/></metadata>
   <polyline fill="none" opacity="0" points="931,-1092 931,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_BLXC" endPointId="0" endStationName="SB_TD" flowDrawDirect="1" flowShape="0" id="AC-35kV.baituo_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1313,-1104 1313,-1143 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29582" ObjectName="AC-35kV.baituo_line"/>
    <cge:TPSR_Ref TObjectID="29582_SS-164"/></metadata>
   <polyline fill="none" opacity="0" points="1313,-1104 1313,-1143 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="428" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="932" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="1313" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="1392" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="447" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21619" cx="1601" cy="-794" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="447" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="565" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="716" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="851" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1238" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1370" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1528" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1082" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1694" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="446" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="1601" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="342" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21620" cx="235" cy="-306" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-114110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 437.000000 -388.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21664" ObjectName="SW-SB_TD.SB_TD_001BK"/>
     <cge:Meas_Ref ObjectId="114110"/>
    <cge:TPSR_Ref TObjectID="21664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114140">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -402.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21669" ObjectName="SW-SB_TD.SB_TD_002BK"/>
     <cge:Meas_Ref ObjectId="114140"/>
    <cge:TPSR_Ref TObjectID="21669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113999">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -144.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21627" ObjectName="SW-SB_TD.SB_TD_053BK"/>
     <cge:Meas_Ref ObjectId="113999"/>
    <cge:TPSR_Ref TObjectID="21627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114014">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -145.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21632" ObjectName="SW-SB_TD.SB_TD_054BK"/>
     <cge:Meas_Ref ObjectId="114014"/>
    <cge:TPSR_Ref TObjectID="21632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114089">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -146.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21659" ObjectName="SW-SB_TD.SB_TD_055BK"/>
     <cge:Meas_Ref ObjectId="114089"/>
    <cge:TPSR_Ref TObjectID="21659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114029">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 -144.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21637" ObjectName="SW-SB_TD.SB_TD_056BK"/>
     <cge:Meas_Ref ObjectId="114029"/>
    <cge:TPSR_Ref TObjectID="21637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114044">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 -143.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21642" ObjectName="SW-SB_TD.SB_TD_057BK"/>
     <cge:Meas_Ref ObjectId="114044"/>
    <cge:TPSR_Ref TObjectID="21642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114059">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 -145.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21647" ObjectName="SW-SB_TD.SB_TD_058BK"/>
     <cge:Meas_Ref ObjectId="114059"/>
    <cge:TPSR_Ref TObjectID="21647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114074">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 -143.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21652" ObjectName="SW-SB_TD.SB_TD_059BK"/>
     <cge:Meas_Ref ObjectId="114074"/>
    <cge:TPSR_Ref TObjectID="21652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114104">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 -673.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21663" ObjectName="SW-SB_TD.SB_TD_301BK"/>
     <cge:Meas_Ref ObjectId="114104"/>
    <cge:TPSR_Ref TObjectID="21663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-114134">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1592.000000 -683.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21668" ObjectName="SW-SB_TD.SB_TD_302BK"/>
     <cge:Meas_Ref ObjectId="114134"/>
    <cge:TPSR_Ref TObjectID="21668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113984">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.000000 -917.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21626" ObjectName="SW-SB_TD.SB_TD_351BK"/>
     <cge:Meas_Ref ObjectId="113984"/>
    <cge:TPSR_Ref TObjectID="21626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113969">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 923.000000 -916.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21625" ObjectName="SW-SB_TD.SB_TD_352BK"/>
     <cge:Meas_Ref ObjectId="113969"/>
    <cge:TPSR_Ref TObjectID="21625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-113954">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1304.000000 -893.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21622" ObjectName="SW-SB_TD.SB_TD_353BK"/>
     <cge:Meas_Ref ObjectId="113954"/>
    <cge:TPSR_Ref TObjectID="21622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246541">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 225.000000 -144.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41364" ObjectName="SW-SB_TD.SB_TD_051BK"/>
     <cge:Meas_Ref ObjectId="246541"/>
    <cge:TPSR_Ref TObjectID="41364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-246543">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 332.000000 -144.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41365" ObjectName="SW-SB_TD.SB_TD_052BK"/>
     <cge:Meas_Ref ObjectId="246543"/>
    <cge:TPSR_Ref TObjectID="41365"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_242e400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 -901.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2085830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_217e0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">妥甸变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23e6b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 395.000000 -1158.000000) translate(0,15)">双妥大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23b94f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1279.000000 -1165.000000) translate(0,15)">白妥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20bd280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 308.000000 -820.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_209cb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 286.000000 -331.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_20cac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 890.000000 -1158.000000) translate(0,15)">双小妥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_209ee60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 819.000000 -1001.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2087a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1205.000000 -1019.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2402740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1268.000000 -628.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2382ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 175.000000) translate(0,15)">#1电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_23d8810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.000000 -16.000000) translate(0,18)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2338df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -115.000000) translate(0,17)">7711610</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2339f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -1038.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233a950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1338.000000 -1095.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233abd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -434.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233af10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1608.000000 -478.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233b370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1606.000000 -374.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233b5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1619.000000 -391.000000) translate(0,12)">00217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233b7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 457.000000 -418.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233ba30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -467.000000) translate(0,12)">0012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233bc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -342.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233beb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -391.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233c0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -173.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233c330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -270.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233c570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -235.000000) translate(0,12)">05317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2368fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -91.000000) translate(0,12)">0533</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2369200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 46.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2369440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 574.000000 -174.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23696f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -271.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2369b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -236.000000) translate(0,12)">05417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2369d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 -92.000000) translate(0,12)">0543</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2369fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 572.000000 45.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 -175.000000) translate(0,12)">055</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236a450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 729.000000 -237.000000) translate(0,12)">05517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -272.000000) translate(0,12)">0551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236a8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -93.000000) translate(0,12)">0556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236ab10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 860.000000 -173.000000) translate(0,12)">056</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236ad50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 864.000000 -235.000000) translate(0,12)">05617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236af90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 -270.000000) translate(0,12)">0561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236b1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 -91.000000) translate(0,12)">0563</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236b410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 858.000000 46.000000) translate(0,12)">0566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236b650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -172.000000) translate(0,12)">057</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236b890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1251.000000 -234.000000) translate(0,12)">05717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236bad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -269.000000) translate(0,12)">0571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 -90.000000) translate(0,12)">0573</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236bf50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1245.000000 47.000000) translate(0,12)">0576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236c190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1379.000000 -174.000000) translate(0,12)">058</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236c4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1383.000000 -236.000000) translate(0,12)">05817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -271.000000) translate(0,12)">0581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236cb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 45.000000) translate(0,12)">0586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236cdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -92.000000) translate(0,12)">0583</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236cff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1537.000000 -172.000000) translate(0,12)">059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236d330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -234.000000) translate(0,12)">05917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -269.000000) translate(0,12)">0591</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236d9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 -90.000000) translate(0,12)">0593</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236dc10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1535.000000 47.000000) translate(0,12)">0596</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236de50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1089.000000 -266.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236e090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1093.000000 -234.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236e2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1701.000000 -259.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236e510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1707.000000 -228.000000) translate(0,12)">06117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_236e750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 483.000000 -609.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_236f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1482.000000 -634.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2302f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -1002.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228c970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.000000 -762.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228cbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -44.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24837a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 442.000000 -946.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2483dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -844.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2484010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -1004.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2484970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 342.000000 -884.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2484c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 -921.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2484e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1320.000000 -974.000000) translate(0,12)">3533</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24850c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1321.000000 -843.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -868.000000) translate(0,12)">35317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 941.000000 -945.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 938.000000 -1003.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24859c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 939.000000 -843.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2485c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.000000 -885.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24860c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 454.000000 -758.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24864a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -749.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24866e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 456.000000 -702.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1610.000000 -713.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1608.000000 -769.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2486da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1531.000000 -760.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2488680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1771.000000 -634.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2488680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1771.000000 -634.000000) translate(0,33)">SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_248a540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -1071.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_248b610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -1030.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_225b640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -234.000000 -667.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,12)">#SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,27)">35±3×2.5/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,57)">标准电流比</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,72)">66/219.9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2268d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 -606.000000) translate(0,87)">Ud=7.47%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1317.000000 -747.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,12)">#SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,27)">35±3×2.5/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,42)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,57)">标准电流比</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,72)">66/219.9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226fd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1461.000000 -586.000000) translate(0,87)">Ud=7.54%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a00e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 -101.000000) translate(0,12)">SC9-50</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a00e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 -101.000000) translate(0,27)">10.5±5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22a00e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1719.000000 -101.000000) translate(0,42)">Ud=4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a08e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1771.000000 -273.500000) translate(0,12)">现场未接线，未采集。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_22a3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -267.000000 -70.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_22a3d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -267.000000 -70.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_22a6060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -80.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_22a6060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -80.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_22a6060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -80.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_22a7260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.500000 -952.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22a9b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1470.000000 -851.000000) translate(0,15)">35kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227cca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 252.000000 -181.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227d2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -270.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227d510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 -128.000000) translate(0,12)">0513</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227d750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 241.000000 45.500000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227d990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 359.000000 -178.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227dbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -268.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227de10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 -126.000000) translate(0,12)">0523</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_227e050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 46.500000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2233470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 207.000000 170.000000) translate(0,15)">窝碑线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2234100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 304.000000 163.000000) translate(0,15)">桂花井线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_223e440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 171.000000) translate(0,14)">水厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_223f190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 171.000000) translate(0,15)">环东线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_223fb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 825.000000 175.000000) translate(0,15)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22403b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1213.000000 172.000000) translate(0,15)">旧街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2240c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 174.000000) translate(0,15)">朝阳线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_22414b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 175.000000) translate(0,15)">中山线</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_24188c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1388.000000 -1061.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_232d720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -359.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_243c280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.000000 -391.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c7500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 503.000000 -203.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23afd80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.000000 -204.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23f22c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 -205.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e0640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 -203.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2258190" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 -202.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234b9e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -204.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2363440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1584.000000 -202.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23bd0d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -202.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23bdb60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1752.000000 -196.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22f3580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1077.000000 -29.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2297940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 342.000000 -717.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c51c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 -728.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22c88a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.000000 -717.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2247f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 390.000000 -826.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22534c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 894.000000 -825.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_247f2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1276.000000 -807.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_TD"/>
</svg>