<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-264" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="416 -792 3240 1083">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <rect height="28" stroke-width="1" width="14" x="0" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="4" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="56" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="45" y2="26"/>
   </symbol>
   <symbol id="load:shape13">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="6" x2="6" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,22 6,28 11,22 " stroke-width="1.14286"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer:shape5_0">
    <circle cx="38" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="29" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="22" y2="29"/>
   </symbol>
   <symbol id="transformer:shape5_1">
    <circle cx="38" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape5-2">
    <circle cx="68" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="54" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="84" x2="69" y1="45" y2="54"/>
   </symbol>
   <symbol id="voltageTransformer:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="35" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="18" x2="29" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.22074" x1="18" x2="29" y1="62" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="41" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="34" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="21" x2="25" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="20" x2="27" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="18" x2="29" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.229325" x1="41" x2="41" y1="40" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.239135" x1="34" x2="34" y1="40" y2="51"/>
    <circle cx="24" cy="26" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="24" cy="10" fillStyle="0" r="9" stroke-width="1"/>
    <circle cx="10" cy="18" fillStyle="0" r="9" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape78">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="40" x2="40" y1="14" y2="1"/>
    <polyline points="6,39 6,23 29,23 " stroke-width="0.587413"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="41" x2="41" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.755439" x1="38" x2="41" y1="43" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.7538" x1="38" x2="41" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="30" x2="30" y1="41" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="48" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="45" x2="45" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="45" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="25" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="30" y2="33"/>
    <circle cx="30" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="45" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="40" cy="41" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="38" x2="35" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="35" x2="35" y1="23" y2="26"/>
    <circle cx="25" cy="30" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="32" x2="35" y1="21" y2="23"/>
    <circle cx="35" cy="23" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="23" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="47" x2="47" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="47" y1="7" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="23" x2="23" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="30" x2="30" y1="14" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="30" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="23" y1="8" y2="8"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c44ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c456b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c45ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c46f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c48200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c48ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c49a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4a440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4acb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4b690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4d480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c4d480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2c4e830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c504e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c51060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c51950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c52290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c627e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c63020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2917e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c64280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c65460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c65de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c668d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c6b890" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c6c4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c68590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2c69b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2c6a860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c78bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2c6e950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1093" width="3250" x="411" y="-797"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="434" y="-791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="433" y="-671"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1526.000000 -405.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1522.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1609.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1580.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1708.000000 -405.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1704.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1728.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1762.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214292">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1885.000000 -405.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31921" ObjectName="SW-CX_LS.CX_LS_28567SW"/>
     <cge:Meas_Ref ObjectId="214292"/>
    <cge:TPSR_Ref TObjectID="31921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214291">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1886.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31920" ObjectName="SW-CX_LS.CX_LS_28560SW"/>
     <cge:Meas_Ref ObjectId="214291"/>
    <cge:TPSR_Ref TObjectID="31920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214290">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1881.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31919" ObjectName="SW-CX_LS.CX_LS_28517SW"/>
     <cge:Meas_Ref ObjectId="214290"/>
    <cge:TPSR_Ref TObjectID="31919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214288">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1905.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31917" ObjectName="SW-CX_LS.CX_LS_2852SW"/>
     <cge:Meas_Ref ObjectId="214288"/>
    <cge:TPSR_Ref TObjectID="31917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214287">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1968.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31916" ObjectName="SW-CX_LS.CX_LS_2851SW"/>
     <cge:Meas_Ref ObjectId="214287"/>
    <cge:TPSR_Ref TObjectID="31916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214289">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31918" ObjectName="SW-CX_LS.CX_LS_2856SW"/>
     <cge:Meas_Ref ObjectId="214289"/>
    <cge:TPSR_Ref TObjectID="31918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214391">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2066.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31928" ObjectName="SW-CX_LS.CX_LS_28667SW"/>
     <cge:Meas_Ref ObjectId="214391"/>
    <cge:TPSR_Ref TObjectID="31928"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214390">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2067.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31927" ObjectName="SW-CX_LS.CX_LS_28660SW"/>
     <cge:Meas_Ref ObjectId="214390"/>
    <cge:TPSR_Ref TObjectID="31927"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214389">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2062.000000 -251.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31926" ObjectName="SW-CX_LS.CX_LS_28617SW"/>
     <cge:Meas_Ref ObjectId="214389"/>
    <cge:TPSR_Ref TObjectID="31926"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214387">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2086.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31924" ObjectName="SW-CX_LS.CX_LS_2862SW"/>
     <cge:Meas_Ref ObjectId="214387"/>
    <cge:TPSR_Ref TObjectID="31924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214386">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2149.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31923" ObjectName="SW-CX_LS.CX_LS_2861SW"/>
     <cge:Meas_Ref ObjectId="214386"/>
    <cge:TPSR_Ref TObjectID="31923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214388">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31925" ObjectName="SW-CX_LS.CX_LS_2866SW"/>
     <cge:Meas_Ref ObjectId="214388"/>
    <cge:TPSR_Ref TObjectID="31925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2473.000000 -405.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2474.000000 -331.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2469.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2493.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2556.000000 -172.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2527.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2656.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2657.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2652.000000 -251.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2676.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2739.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2710.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214733">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1429.000000 -45.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31958" ObjectName="SW-CX_LS.CX_LS_21017SW"/>
     <cge:Meas_Ref ObjectId="214733"/>
    <cge:TPSR_Ref TObjectID="31958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214726">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2240.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31952" ObjectName="SW-CX_LS.CX_LS_2242SW"/>
     <cge:Meas_Ref ObjectId="214726"/>
    <cge:TPSR_Ref TObjectID="31952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214727">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2349.000000 -171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31953" ObjectName="SW-CX_LS.CX_LS_2244SW"/>
     <cge:Meas_Ref ObjectId="214727"/>
    <cge:TPSR_Ref TObjectID="31953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214728">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2240.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31954" ObjectName="SW-CX_LS.CX_LS_22427SW"/>
     <cge:Meas_Ref ObjectId="214728"/>
    <cge:TPSR_Ref TObjectID="31954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214729">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2349.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31955" ObjectName="SW-CX_LS.CX_LS_22447SW"/>
     <cge:Meas_Ref ObjectId="214729"/>
    <cge:TPSR_Ref TObjectID="31955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214722">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31948" ObjectName="SW-CX_LS.CX_LS_2131SW"/>
     <cge:Meas_Ref ObjectId="214722"/>
    <cge:TPSR_Ref TObjectID="31948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214724">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2242.000000 -0.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31950" ObjectName="SW-CX_LS.CX_LS_21317SW"/>
     <cge:Meas_Ref ObjectId="214724"/>
    <cge:TPSR_Ref TObjectID="31950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214725">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2355.000000 1.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31951" ObjectName="SW-CX_LS.CX_LS_21337SW"/>
     <cge:Meas_Ref ObjectId="214725"/>
    <cge:TPSR_Ref TObjectID="31951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214723">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2355.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31949" ObjectName="SW-CX_LS.CX_LS_2133SW"/>
     <cge:Meas_Ref ObjectId="214723"/>
    <cge:TPSR_Ref TObjectID="31949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214737">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3174.000000 -49.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31962" ObjectName="SW-CX_LS.CX_LS_23017SW"/>
     <cge:Meas_Ref ObjectId="214737"/>
    <cge:TPSR_Ref TObjectID="31962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214738">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3172.000000 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31963" ObjectName="SW-CX_LS.CX_LS_24017SW"/>
     <cge:Meas_Ref ObjectId="214738"/>
    <cge:TPSR_Ref TObjectID="31963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214080">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31907" ObjectName="SW-CX_LS.CX_LS_28167SW"/>
     <cge:Meas_Ref ObjectId="214080"/>
    <cge:TPSR_Ref TObjectID="31907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214079">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31906" ObjectName="SW-CX_LS.CX_LS_28160SW"/>
     <cge:Meas_Ref ObjectId="214079"/>
    <cge:TPSR_Ref TObjectID="31906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214078">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1101.000000 -251.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31905" ObjectName="SW-CX_LS.CX_LS_28117SW"/>
     <cge:Meas_Ref ObjectId="214078"/>
    <cge:TPSR_Ref TObjectID="31905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214076">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31903" ObjectName="SW-CX_LS.CX_LS_2812SW"/>
     <cge:Meas_Ref ObjectId="214076"/>
    <cge:TPSR_Ref TObjectID="31903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214077">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31904" ObjectName="SW-CX_LS.CX_LS_2816SW"/>
     <cge:Meas_Ref ObjectId="214077"/>
    <cge:TPSR_Ref TObjectID="31904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214186">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31914" ObjectName="SW-CX_LS.CX_LS_28267SW"/>
     <cge:Meas_Ref ObjectId="214186"/>
    <cge:TPSR_Ref TObjectID="31914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214185">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1289.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31913" ObjectName="SW-CX_LS.CX_LS_28260SW"/>
     <cge:Meas_Ref ObjectId="214185"/>
    <cge:TPSR_Ref TObjectID="31913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214184">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.000000 -251.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31912" ObjectName="SW-CX_LS.CX_LS_28217SW"/>
     <cge:Meas_Ref ObjectId="214184"/>
    <cge:TPSR_Ref TObjectID="31912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214182">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31910" ObjectName="SW-CX_LS.CX_LS_2822SW"/>
     <cge:Meas_Ref ObjectId="214182"/>
    <cge:TPSR_Ref TObjectID="31910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214181">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1371.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31909" ObjectName="SW-CX_LS.CX_LS_2821SW"/>
     <cge:Meas_Ref ObjectId="214181"/>
    <cge:TPSR_Ref TObjectID="31909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214183">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31911" ObjectName="SW-CX_LS.CX_LS_2826SW"/>
     <cge:Meas_Ref ObjectId="214183"/>
    <cge:TPSR_Ref TObjectID="31911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214736">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1431.000000 -180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31961" ObjectName="SW-CX_LS.CX_LS_22017SW"/>
     <cge:Meas_Ref ObjectId="214736"/>
    <cge:TPSR_Ref TObjectID="31961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214497">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2835.000000 -407.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31935" ObjectName="SW-CX_LS.CX_LS_28967SW"/>
     <cge:Meas_Ref ObjectId="214497"/>
    <cge:TPSR_Ref TObjectID="31935"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214496">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2836.000000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31934" ObjectName="SW-CX_LS.CX_LS_28960SW"/>
     <cge:Meas_Ref ObjectId="214496"/>
    <cge:TPSR_Ref TObjectID="31934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214495">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2831.000000 -252.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31933" ObjectName="SW-CX_LS.CX_LS_28937SW"/>
     <cge:Meas_Ref ObjectId="214495"/>
    <cge:TPSR_Ref TObjectID="31933"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214493">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2855.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31931" ObjectName="SW-CX_LS.CX_LS_2894SW"/>
     <cge:Meas_Ref ObjectId="214493"/>
    <cge:TPSR_Ref TObjectID="31931"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214492">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2918.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31930" ObjectName="SW-CX_LS.CX_LS_2893SW"/>
     <cge:Meas_Ref ObjectId="214492"/>
    <cge:TPSR_Ref TObjectID="31930"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214494">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2889.000000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31932" ObjectName="SW-CX_LS.CX_LS_2896SW"/>
     <cge:Meas_Ref ObjectId="214494"/>
    <cge:TPSR_Ref TObjectID="31932"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214596">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3018.000000 -408.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31942" ObjectName="SW-CX_LS.CX_LS_29167SW"/>
     <cge:Meas_Ref ObjectId="214596"/>
    <cge:TPSR_Ref TObjectID="31942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214595">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3019.000000 -334.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31941" ObjectName="SW-CX_LS.CX_LS_29160SW"/>
     <cge:Meas_Ref ObjectId="214595"/>
    <cge:TPSR_Ref TObjectID="31941"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214594">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3014.000000 -253.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31940" ObjectName="SW-CX_LS.CX_LS_29137SW"/>
     <cge:Meas_Ref ObjectId="214594"/>
    <cge:TPSR_Ref TObjectID="31940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214592">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3038.000000 -175.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31938" ObjectName="SW-CX_LS.CX_LS_2914SW"/>
     <cge:Meas_Ref ObjectId="214592"/>
    <cge:TPSR_Ref TObjectID="31938"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214591">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3101.000000 -175.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31937" ObjectName="SW-CX_LS.CX_LS_2913SW"/>
     <cge:Meas_Ref ObjectId="214591"/>
    <cge:TPSR_Ref TObjectID="31937"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214593">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3072.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31939" ObjectName="SW-CX_LS.CX_LS_2916SW"/>
     <cge:Meas_Ref ObjectId="214593"/>
    <cge:TPSR_Ref TObjectID="31939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3252.000000 -406.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3253.000000 -332.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3248.000000 -251.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3272.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3335.000000 -173.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3306.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3435.000000 -407.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 -333.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3431.000000 -252.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3518.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2677.000000 -50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2739.000000 -50.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2708.000000 96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2640.000000 -19.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2642.000000 43.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2642.000000 119.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2087.000000 -53.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2149.000000 -53.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2118.000000 93.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2050.000000 -22.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2052.000000 40.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2052.000000 116.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1730.000000 -55.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1792.000000 -55.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1761.000000 91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1693.000000 -24.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 38.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 114.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214683">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3359.000000 -61.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31945" ObjectName="SW-CX_LS.CX_LS_2344SW"/>
     <cge:Meas_Ref ObjectId="214683"/>
    <cge:TPSR_Ref TObjectID="31945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214685">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3291.000000 -46.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31947" ObjectName="SW-CX_LS.CX_LS_23447SW"/>
     <cge:Meas_Ref ObjectId="214685"/>
    <cge:TPSR_Ref TObjectID="31947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214684">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3293.000000 16.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31946" ObjectName="SW-CX_LS.CX_LS_23437SW"/>
     <cge:Meas_Ref ObjectId="214684"/>
    <cge:TPSR_Ref TObjectID="31946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214682">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3359.000000 72.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31944" ObjectName="SW-CX_LS.CX_LS_2343SW"/>
     <cge:Meas_Ref ObjectId="214682"/>
    <cge:TPSR_Ref TObjectID="31944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 -59.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1509.000000 -44.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.000000 18.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 74.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1635.000000 251.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1569.000000 166.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1927.000000 168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2516.000000 171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1992.000000 253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2582.000000 256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214075">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 -174.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31902" ObjectName="SW-CX_LS.CX_LS_2811SW"/>
     <cge:Meas_Ref ObjectId="214075"/>
    <cge:TPSR_Ref TObjectID="31902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214731">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1146.000000 -39.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31956" ObjectName="SW-CX_LS.CX_LS_2901SW"/>
     <cge:Meas_Ref ObjectId="214731"/>
    <cge:TPSR_Ref TObjectID="31956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214732">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -15.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31957" ObjectName="SW-CX_LS.CX_LS_29017SW"/>
     <cge:Meas_Ref ObjectId="214732"/>
    <cge:TPSR_Ref TObjectID="31957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214734">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1936.000000 -43.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31959" ObjectName="SW-CX_LS.CX_LS_2902SW"/>
     <cge:Meas_Ref ObjectId="214734"/>
    <cge:TPSR_Ref TObjectID="31959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214735">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1858.000000 -19.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31960" ObjectName="SW-CX_LS.CX_LS_29027SW"/>
     <cge:Meas_Ref ObjectId="214735"/>
    <cge:TPSR_Ref TObjectID="31960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2524.000000 -44.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2446.000000 -20.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3064.000000 -35.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2986.000000 -11.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2933360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1513.000000 -436.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294b210">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1695.000000 -436.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2965b90">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1872.000000 -436.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2980510">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2053.000000 -437.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2999df0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2460.000000 -436.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b17e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2643.000000 -437.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f5a50">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -437.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0dba0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -437.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2ea30">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2822.000000 -438.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a46540">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3005.000000 -439.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a5ee50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3239.000000 -437.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a76840">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3422.000000 -438.000000)" xlink:href="#voltageTransformer:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1f3f0">
    <use class="BV-220KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1148.000000 66.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b38350">
    <use class="BV-220KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 1938.000000 62.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b4c520">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 2526.000000 61.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b56cf0">
    <use class="BV-0KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 3066.000000 70.000000)" xlink:href="#voltageTransformer:shape78"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1589,-503 1589,-561 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1589,-503 1589,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1771,-505 1771,-560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1771,-505 1771,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LuC" endPointId="0" endStationName="CX_LS" flowDrawDirect="1" flowShape="0" id="AC-220kV.luli_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1948,-506 1948,-559 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33521" ObjectName="AC-220kV.luli_line"/>
    <cge:TPSR_Ref TObjectID="33521_SS-264"/></metadata>
   <polyline fill="none" opacity="0" points="1948,-506 1948,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2536,-507 2536,-560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2536,-507 2536,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2719,-508 2719,-559 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="2719,-508 2719,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_FS" endPointId="0" endStationName="CX_LS" flowDrawDirect="1" flowShape="0" id="AC-220kV.fangli_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2898,-509 2898,-559 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33513" ObjectName="AC-220kV.fangli_line"/>
    <cge:TPSR_Ref TObjectID="33513_SS-264"/></metadata>
   <polyline fill="none" opacity="0" points="2898,-509 2898,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_LS" flowDrawDirect="1" flowShape="0" id="AC-220kV.yuanli_line" runFlow="0">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3081,-510 3081,-560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="33522" ObjectName="AC-220kV.yuanli_line"/>
    <cge:TPSR_Ref TObjectID="33522_SS-264"/></metadata>
   <polyline fill="none" opacity="0" points="3081,-510 3081,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3315,-507 3315,-560 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3315,-507 3315,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3498,-508 3498,-559 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3498,-508 3498,-559 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29313b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -404.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2931e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1491.000000 -330.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29328d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -249.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2935790" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1619.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2949260" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1675.000000 -404.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2949cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.000000 -330.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294a780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1671.000000 -249.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294d640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1801.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2963be0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1852.000000 -404.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2964670" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1850.000000 -330.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2965100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1848.000000 -249.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2967fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297e560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2033.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297eff0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297fa80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2029.000000 -250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2982940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2159.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2999360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2440.000000 -404.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299c220" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2566.000000 -384.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29af830" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2623.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b02c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2621.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b0d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2619.000000 -250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b3c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2749.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bd7e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1432.000000 -22.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29be230" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2245.000000 27.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bec80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2358.000000 30.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bf6d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3177.000000 -24.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c0120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 -293.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c0bb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2352.000000 -296.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c1640" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3175.000000 -229.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e23f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2441.000000 -330.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e2bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2439.000000 -249.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f3aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1072.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f4530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1070.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f4fc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f7e80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1198.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0bbf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1255.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0c680" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1253.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0d110" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0ffd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1381.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a10ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1434.000000 -227.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2dfa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2802.000000 -406.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a30ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2928.000000 -386.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a44590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2985.000000 -407.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a45020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2983.000000 -333.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a45ab0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2981.000000 -252.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a48970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3111.000000 -387.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4ae60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2803.000000 -332.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4b6e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2801.000000 -251.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a5e3c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3219.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a61280" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3345.000000 -385.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a74890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3402.000000 -406.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a75320" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3400.000000 -332.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a75db0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3398.000000 -251.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a78c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3528.000000 -386.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7a760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3220.000000 -331.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7b1f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3218.000000 -250.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a97600" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2612.000000 -18.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a98090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2611.000000 44.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a98b20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2612.000000 120.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aadef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2022.000000 -21.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aae980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2021.000000 41.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aaf410" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2022.000000 117.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac47e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 -23.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac5270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1664.000000 39.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac5d00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1665.000000 115.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad3e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3263.000000 -45.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad48d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3262.000000 17.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae3bd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.000000 -43.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae4660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 19.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b01b20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1638.000000 286.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b07160" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1572.000000 201.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0bf10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1930.000000 203.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b10f20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2519.000000 206.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15f30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1995.000000 288.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1adb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2585.000000 291.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_291cae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-225 1589,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-225 1589,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_291de60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-481 1625,-481 1625,-465 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2933360@0" ObjectIDZND0="g_2934dc0@0" Pin0InfoVect0LinkObjId="g_2934dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2933360_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-481 1625,-481 1625,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_291eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-410 1589,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2934dc0@0" ObjectIDZND1="g_2933360@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2934dc0_0" Pin0InfoVect1LinkObjId="g_2933360_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-410 1589,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_291edd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-481 1589,-507 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2934dc0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2934dc0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-481 1589,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2921160">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-255 1589,-279 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-255 1589,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29213c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-306 1589,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-306 1589,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2923b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-410 1567,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2934dc0@0" ObjectIDND1="g_2933360@0" ObjectIDND2="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2934dc0_0" Pin1InfoVect1LinkObjId="g_2933360_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-410 1567,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2923dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1531,-410 1511,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29313b0@0" Pin0InfoVect0LinkObjId="g_29313b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1531,-410 1511,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2926500">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-336 1568,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-336 1568,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2926760">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1532,-336 1509,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2931e40@0" Pin0InfoVect0LinkObjId="g_2931e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1532,-336 1509,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2928e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-255 1563,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-255 1563,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29290f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1527,-255 1507,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29328d0@0" Pin0InfoVect0LinkObjId="g_29328d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1527,-255 1507,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_292baf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1555,-213 1555,-225 1589,-225 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1555,-213 1555,-225 1589,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_292e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-225 1618,-225 1618,-213 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-225 1618,-225 1618,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2930ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-336 1589,-354 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-336 1589,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2931150">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-390 1589,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="g_2934dc0@0" ObjectIDZND1="g_2933360@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2934dc0_0" Pin0InfoVect1LinkObjId="g_2933360_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-390 1589,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29361e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1625,-415 1625,-402 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2934dc0@1" ObjectIDZND0="g_2935790@0" Pin0InfoVect0LinkObjId="g_2935790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2934dc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-415 1625,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2936440">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1589,-481 1565,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2934dc0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2933360@0" Pin0InfoVect0LinkObjId="g_2933360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2934dc0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-481 1565,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29366a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-225 1771,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-225 1771,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2936900">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-481 1807,-481 1807,-465 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_294b210@0" ObjectIDZND0="g_294cc70@0" Pin0InfoVect0LinkObjId="g_294cc70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_294b210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-481 1807,-481 1807,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2936b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-410 1771,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_294cc70@0" ObjectIDZND1="g_294b210@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_294cc70_0" Pin0InfoVect1LinkObjId="g_294b210_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-410 1771,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2936dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-481 1771,-507 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_294cc70@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_294cc70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-481 1771,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2939090">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-255 1771,-279 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-255 1771,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29392f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-306 1771,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-306 1771,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_293ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-410 1749,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_294cc70@0" ObjectIDND1="g_294b210@0" ObjectIDND2="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_294cc70_0" Pin1InfoVect1LinkObjId="g_294b210_0" Pin1InfoVect2LinkObjId="SW-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-410 1749,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_293bc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1713,-410 1693,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2949260@0" Pin0InfoVect0LinkObjId="g_2949260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1713,-410 1693,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_293e3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-336 1750,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-336 1750,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_293e610">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1714,-336 1691,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2949cf0@0" Pin0InfoVect0LinkObjId="g_2949cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1714,-336 1691,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2940d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-255 1745,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-255 1745,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2940fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1709,-255 1689,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_294a780@0" Pin0InfoVect0LinkObjId="g_294a780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1709,-255 1689,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29439a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1737,-213 1737,-225 1771,-225 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1737,-213 1737,-225 1771,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29463a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-225 1800,-225 1800,-213 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-225 1800,-225 1800,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2948da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-336 1771,-354 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-336 1771,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2949000">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-390 1771,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@1" ObjectIDZND0="g_294cc70@0" ObjectIDZND1="g_294b210@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_294cc70_0" Pin0InfoVect1LinkObjId="g_294b210_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-390 1771,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294e090">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1807,-415 1807,-402 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_294cc70@1" ObjectIDZND0="g_294d640@0" Pin0InfoVect0LinkObjId="g_294d640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_294cc70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1807,-415 1807,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_294e2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1771,-481 1747,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_294cc70@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_294b210@0" Pin0InfoVect0LinkObjId="g_294b210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_294cc70_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1771,-481 1747,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2951240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-410 1948,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="31921@x" ObjectIDND1="31918@x" ObjectIDZND0="g_2965b90@0" ObjectIDZND1="g_29675f0@0" ObjectIDZND2="33521@1" Pin0InfoVect0LinkObjId="g_2965b90_0" Pin0InfoVect1LinkObjId="g_29675f0_0" Pin0InfoVect2LinkObjId="g_29514a0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214292_0" Pin1InfoVect1LinkObjId="SW-214289_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-410 1948,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29514a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-481 1948,-507 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="31921@x" ObjectIDND1="31918@x" ObjectIDND2="g_2965b90@0" ObjectIDZND0="33521@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214292_0" Pin1InfoVect1LinkObjId="SW-214289_0" Pin1InfoVect2LinkObjId="g_2965b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-481 1948,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29537d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-255 1948,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31919@x" ObjectIDND1="31917@x" ObjectIDND2="31916@x" ObjectIDZND0="31915@0" Pin0InfoVect0LinkObjId="SW-214284_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214290_0" Pin1InfoVect1LinkObjId="SW-214288_0" Pin1InfoVect2LinkObjId="SW-214287_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-255 1948,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2953a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-306 1948,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31915@1" ObjectIDZND0="31920@x" ObjectIDZND1="31918@x" Pin0InfoVect0LinkObjId="SW-214291_0" Pin0InfoVect1LinkObjId="SW-214289_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214284_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-306 1948,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29561c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-410 1926,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2965b90@0" ObjectIDND1="g_29675f0@0" ObjectIDND2="33521@1" ObjectIDZND0="31921@1" Pin0InfoVect0LinkObjId="SW-214292_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2965b90_0" Pin1InfoVect1LinkObjId="g_29675f0_0" Pin1InfoVect2LinkObjId="g_29514a0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-410 1926,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2956420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1890,-410 1870,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31921@0" ObjectIDZND0="g_2963be0@0" Pin0InfoVect0LinkObjId="g_2963be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214292_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1890,-410 1870,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2958bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-336 1927,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31915@x" ObjectIDND1="31918@x" ObjectIDZND0="31920@1" Pin0InfoVect0LinkObjId="SW-214291_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214284_0" Pin1InfoVect1LinkObjId="SW-214289_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-336 1927,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2958e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1891,-336 1868,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31920@0" ObjectIDZND0="g_2964670@0" Pin0InfoVect0LinkObjId="g_2964670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214291_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1891,-336 1868,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295b5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-255 1922,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31915@x" ObjectIDND1="31917@x" ObjectIDND2="31916@x" ObjectIDZND0="31919@1" Pin0InfoVect0LinkObjId="SW-214290_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214284_0" Pin1InfoVect1LinkObjId="SW-214288_0" Pin1InfoVect2LinkObjId="SW-214287_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-255 1922,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295b800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1886,-255 1866,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31919@0" ObjectIDZND0="g_2965100@0" Pin0InfoVect0LinkObjId="g_2965100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1886,-255 1866,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295e260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1914,-213 1914,-225 1948,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="31917@1" ObjectIDZND0="31916@x" ObjectIDZND1="31915@x" ObjectIDZND2="31919@x" Pin0InfoVect0LinkObjId="SW-214287_0" Pin0InfoVect1LinkObjId="SW-214284_0" Pin0InfoVect2LinkObjId="SW-214290_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214288_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1914,-213 1914,-225 1948,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2960cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-225 1977,-225 1977,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31917@x" ObjectIDND1="31915@x" ObjectIDND2="31919@x" ObjectIDZND0="31916@1" Pin0InfoVect0LinkObjId="SW-214287_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214288_0" Pin1InfoVect1LinkObjId="SW-214284_0" Pin1InfoVect2LinkObjId="SW-214290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-225 1977,-225 1977,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2963720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-336 1948,-354 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31915@x" ObjectIDND1="31920@x" ObjectIDZND0="31918@0" Pin0InfoVect0LinkObjId="SW-214289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214284_0" Pin1InfoVect1LinkObjId="SW-214291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-336 1948,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2963980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-390 1948,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="31918@1" ObjectIDZND0="g_2965b90@0" ObjectIDZND1="g_29675f0@0" ObjectIDZND2="33521@1" Pin0InfoVect0LinkObjId="g_2965b90_0" Pin0InfoVect1LinkObjId="g_29675f0_0" Pin0InfoVect2LinkObjId="g_29514a0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214289_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-390 1948,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2968a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1984,-415 1984,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29675f0@1" ObjectIDZND0="g_2967fc0@0" Pin0InfoVect0LinkObjId="g_2967fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29675f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1984,-415 1984,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2968c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-481 1924,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="31921@x" ObjectIDND1="31918@x" ObjectIDND2="g_29675f0@0" ObjectIDZND0="g_2965b90@0" Pin0InfoVect0LinkObjId="g_2965b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214292_0" Pin1InfoVect1LinkObjId="SW-214289_0" Pin1InfoVect2LinkObjId="g_29675f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-481 1924,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2968ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-225 1948,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31917@x" ObjectIDND1="31916@x" ObjectIDZND0="31915@x" ObjectIDZND1="31919@x" Pin0InfoVect0LinkObjId="SW-214284_0" Pin0InfoVect1LinkObjId="SW-214290_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214288_0" Pin1InfoVect1LinkObjId="SW-214287_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-225 1948,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2969130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1948,-481 1984,-481 1984,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="31921@x" ObjectIDND1="31918@x" ObjectIDND2="g_2965b90@0" ObjectIDZND0="g_29675f0@0" Pin0InfoVect0LinkObjId="g_29675f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214292_0" Pin1InfoVect1LinkObjId="SW-214289_0" Pin1InfoVect2LinkObjId="g_2965b90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1948,-481 1984,-481 1984,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296e150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-256 2129,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31926@x" ObjectIDND1="31924@x" ObjectIDND2="31923@x" ObjectIDZND0="31922@0" Pin0InfoVect0LinkObjId="SW-214383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214389_0" Pin1InfoVect1LinkObjId="SW-214387_0" Pin1InfoVect2LinkObjId="SW-214386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-256 2129,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296e3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-307 2129,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31922@1" ObjectIDZND0="31927@x" ObjectIDZND1="31925@x" Pin0InfoVect0LinkObjId="SW-214390_0" Pin0InfoVect1LinkObjId="SW-214388_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-307 2129,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2970b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-411 2107,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31925@x" ObjectIDND1="g_2980510@0" ObjectIDND2="g_2981f70@0" ObjectIDZND0="31928@1" Pin0InfoVect0LinkObjId="SW-214391_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214388_0" Pin1InfoVect1LinkObjId="g_2980510_0" Pin1InfoVect2LinkObjId="g_2981f70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-411 2107,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2970da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2071,-411 2051,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31928@0" ObjectIDZND0="g_297e560@0" Pin0InfoVect0LinkObjId="g_297e560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2071,-411 2051,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2973530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-337 2108,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31922@x" ObjectIDND1="31925@x" ObjectIDZND0="31927@1" Pin0InfoVect0LinkObjId="SW-214390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214383_0" Pin1InfoVect1LinkObjId="SW-214388_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-337 2108,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2973790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2072,-337 2049,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31927@0" ObjectIDZND0="g_297eff0@0" Pin0InfoVect0LinkObjId="g_297eff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2072,-337 2049,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2975f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-256 2103,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31922@x" ObjectIDND1="31924@x" ObjectIDND2="31923@x" ObjectIDZND0="31926@1" Pin0InfoVect0LinkObjId="SW-214389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214383_0" Pin1InfoVect1LinkObjId="SW-214387_0" Pin1InfoVect2LinkObjId="SW-214386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-256 2103,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2976180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2067,-256 2047,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31926@0" ObjectIDZND0="g_297fa80@0" Pin0InfoVect0LinkObjId="g_297fa80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2067,-256 2047,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2978be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2095,-214 2095,-226 2129,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="31924@1" ObjectIDZND0="31923@x" ObjectIDZND1="31922@x" ObjectIDZND2="31926@x" Pin0InfoVect0LinkObjId="SW-214386_0" Pin0InfoVect1LinkObjId="SW-214383_0" Pin0InfoVect2LinkObjId="SW-214389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214387_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2095,-214 2095,-226 2129,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_297b640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-226 2158,-226 2158,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31924@x" ObjectIDND1="31922@x" ObjectIDND2="31926@x" ObjectIDZND0="31923@1" Pin0InfoVect0LinkObjId="SW-214386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214387_0" Pin1InfoVect1LinkObjId="SW-214383_0" Pin1InfoVect2LinkObjId="SW-214389_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-226 2158,-226 2158,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_297e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-337 2129,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31922@x" ObjectIDND1="31927@x" ObjectIDZND0="31925@0" Pin0InfoVect0LinkObjId="SW-214388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214383_0" Pin1InfoVect1LinkObjId="SW-214390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-337 2129,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_297e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-391 2129,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="31925@1" ObjectIDZND0="31928@x" ObjectIDZND1="g_2980510@0" ObjectIDZND2="g_2981f70@0" Pin0InfoVect0LinkObjId="SW-214391_0" Pin0InfoVect1LinkObjId="g_2980510_0" Pin0InfoVect2LinkObjId="g_2981f70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214388_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-391 2129,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2165,-416 2165,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2981f70@1" ObjectIDZND0="g_2982940@0" Pin0InfoVect0LinkObjId="g_2982940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2981f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2165,-416 2165,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29835f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-482 2105,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2981f70@0" ObjectIDND1="31928@x" ObjectIDND2="31925@x" ObjectIDZND0="g_2980510@0" Pin0InfoVect0LinkObjId="g_2980510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2981f70_0" Pin1InfoVect1LinkObjId="SW-214391_0" Pin1InfoVect2LinkObjId="SW-214388_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-482 2105,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-226 2129,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31924@x" ObjectIDND1="31923@x" ObjectIDZND0="31922@x" ObjectIDZND1="31926@x" Pin0InfoVect0LinkObjId="SW-214383_0" Pin0InfoVect1LinkObjId="SW-214389_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214387_0" Pin1InfoVect1LinkObjId="SW-214386_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-226 2129,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-482 2165,-482 2165,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2980510@0" ObjectIDND1="31928@x" ObjectIDND2="31925@x" ObjectIDZND0="g_2981f70@0" Pin0InfoVect0LinkObjId="g_2981f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2980510_0" Pin1InfoVect1LinkObjId="SW-214391_0" Pin1InfoVect2LinkObjId="SW-214388_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-482 2165,-482 2165,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-411 2129,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="31928@x" ObjectIDND1="31925@x" ObjectIDZND0="g_2980510@0" ObjectIDZND1="g_2981f70@0" ObjectIDZND2="33520@x" Pin0InfoVect0LinkObjId="g_2980510_0" Pin0InfoVect1LinkObjId="g_2981f70_0" Pin0InfoVect2LinkObjId="EC-CX_LS.CX_LS_286Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214391_0" Pin1InfoVect1LinkObjId="SW-214388_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-411 2129,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2983f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2129,-482 2129,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2980510@0" ObjectIDND1="g_2981f70@0" ObjectIDND2="31928@x" ObjectIDZND0="33520@0" Pin0InfoVect0LinkObjId="EC-CX_LS.CX_LS_286Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2980510_0" Pin1InfoVect1LinkObjId="g_2981f70_0" Pin1InfoVect2LinkObjId="SW-214391_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2129,-482 2129,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2986ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-481 2536,-507 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2999df0@0" ObjectIDND1="g_299b850@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2999df0_0" Pin1InfoVect1LinkObjId="g_299b850_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-481 2536,-507 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2989190">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-255 2536,-279 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-255 2536,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29893f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-306 2536,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-306 2536,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_298bb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-410 2514,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2999df0@0" ObjectIDND2="g_299b850@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2999df0_0" Pin1InfoVect2LinkObjId="g_299b850_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-410 2514,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_298bd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2478,-410 2458,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2999360@0" Pin0InfoVect0LinkObjId="g_2999360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2478,-410 2458,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_298e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-336 2515,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-336 2515,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_298e710">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2479,-336 2459,-336 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29e23f0@0" Pin0InfoVect0LinkObjId="g_29e23f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2479,-336 2459,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2990e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-255 2510,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-255 2510,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29910a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2474,-255 2457,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29e2bd0@0" Pin0InfoVect0LinkObjId="g_29e2bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2474,-255 2457,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2993aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2502,-213 2502,-225 2536,-225 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2502,-213 2502,-225 2536,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29964a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-225 2565,-225 2565,-213 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-225 2565,-225 2565,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2998ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-336 2536,-354 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-336 2536,-354 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2999100">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-390 2536,-410 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2999df0@0" ObjectIDZND2="g_299b850@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2999df0_0" Pin0InfoVect2LinkObjId="g_299b850_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-390 2536,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299cc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2572,-415 2572,-402 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_299b850@1" ObjectIDZND0="g_299c220@0" Pin0InfoVect0LinkObjId="g_299c220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_299b850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2572,-415 2572,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299ced0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-481 2512,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_299b850@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2999df0@0" Pin0InfoVect0LinkObjId="g_2999df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_299b850_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-481 2512,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299d130">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-225 2536,-255 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-225 2536,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299d390">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-481 2572,-481 2572,-465 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2999df0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_299b850@0" Pin0InfoVect0LinkObjId="g_299b850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2999df0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-481 2572,-481 2572,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299f660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-256 2719,-280 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-256 2719,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_299f8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-307 2719,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-307 2719,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a1ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-411 2697,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_29b17e0@0" ObjectIDND2="g_29b3240@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_29b17e0_0" Pin1InfoVect2LinkObjId="g_29b3240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-411 2697,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a2250">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2661,-411 2641,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29af830@0" Pin0InfoVect0LinkObjId="g_29af830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2661,-411 2641,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a4980">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-337 2698,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-337 2698,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a4be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2662,-337 2639,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29b02c0@0" Pin0InfoVect0LinkObjId="g_29b02c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2662,-337 2639,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a7310">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-256 2693,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-256 2693,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a7570">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2657,-256 2637,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_29b0d50@0" Pin0InfoVect0LinkObjId="g_29b0d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2657,-256 2637,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29a9f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2685,-214 2685,-226 2719,-226 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2685,-214 2685,-226 2719,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29ac970">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-226 2748,-226 2748,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-226 2748,-226 2748,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29af370">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-337 2719,-355 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-337 2719,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29af5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-391 2719,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_29b17e0@0" ObjectIDZND2="g_29b3240@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_29b17e0_0" Pin0InfoVect2LinkObjId="g_29b3240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-391 2719,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b4660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2755,-416 2755,-403 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29b3240@1" ObjectIDZND0="g_29b3c10@0" Pin0InfoVect0LinkObjId="g_29b3c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b3240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2755,-416 2755,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b48c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-482 2695,-482 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_29b3240@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_29b17e0@0" Pin0InfoVect0LinkObjId="g_29b17e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29b3240_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-482 2695,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b4b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-226 2719,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-226 2719,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b4d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-482 2755,-482 2755,-466 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29b17e0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_29b3240@0" Pin0InfoVect0LinkObjId="g_29b3240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29b17e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-482 2755,-482 2755,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b4fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-411 2719,-482 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_29b17e0@0" ObjectIDZND1="g_29b3240@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_29b17e0_0" Pin0InfoVect1LinkObjId="g_29b3240_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-411 2719,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b5240">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2719,-482 2719,-508 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_29b17e0@0" ObjectIDND1="g_29b3240@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29b17e0_0" Pin1InfoVect1LinkObjId="g_29b3240_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2719,-482 2719,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29b54a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2536,-410 2536,-481 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2999df0@0" ObjectIDZND1="g_299b850@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2999df0_0" Pin0InfoVect1LinkObjId="g_299b850_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2536,-410 2536,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29bb0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2249,-225 2358,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31952@x" ObjectIDND1="31954@x" ObjectIDZND0="31953@x" ObjectIDZND1="31955@x" Pin0InfoVect0LinkObjId="SW-214727_0" Pin0InfoVect1LinkObjId="SW-214729_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214726_0" Pin1InfoVect1LinkObjId="SW-214728_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2249,-225 2358,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29bc460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2251,-52 2364,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31948@x" ObjectIDND1="31950@x" ObjectIDZND0="31951@x" ObjectIDZND1="31949@x" Pin0InfoVect0LinkObjId="SW-214725_0" Pin0InfoVect1LinkObjId="SW-214723_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214722_0" Pin1InfoVect1LinkObjId="SW-214724_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-52 2364,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29c48d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1438,-50 1438,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31958@0" ObjectIDZND0="g_29bd7e0@0" Pin0InfoVect0LinkObjId="g_29bd7e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1438,-50 1438,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29c7330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2249,-209 2249,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31952@1" ObjectIDZND0="31953@x" ObjectIDZND1="31955@x" ObjectIDZND2="31954@x" Pin0InfoVect0LinkObjId="SW-214727_0" Pin0InfoVect1LinkObjId="SW-214729_0" Pin0InfoVect2LinkObjId="SW-214728_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214726_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2249,-209 2249,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29c9d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2358,-212 2358,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31953@1" ObjectIDZND0="31952@x" ObjectIDZND1="31954@x" ObjectIDZND2="31955@x" Pin0InfoVect0LinkObjId="SW-214726_0" Pin0InfoVect1LinkObjId="SW-214728_0" Pin0InfoVect2LinkObjId="SW-214729_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2358,-212 2358,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29cc7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2249,-225 2249,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31953@x" ObjectIDND1="31955@x" ObjectIDND2="31952@x" ObjectIDZND0="31954@0" Pin0InfoVect0LinkObjId="SW-214728_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214727_0" Pin1InfoVect1LinkObjId="SW-214729_0" Pin1InfoVect2LinkObjId="SW-214726_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2249,-225 2249,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29cca50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2249,-282 2249,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31954@1" ObjectIDZND0="g_29c0120@0" Pin0InfoVect0LinkObjId="g_29c0120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214728_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2249,-282 2249,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29cf4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2358,-225 2358,-246 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31952@x" ObjectIDND1="31954@x" ObjectIDND2="31953@x" ObjectIDZND0="31955@0" Pin0InfoVect0LinkObjId="SW-214729_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214726_0" Pin1InfoVect1LinkObjId="SW-214728_0" Pin1InfoVect2LinkObjId="SW-214727_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2358,-225 2358,-246 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29cf710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2358,-282 2358,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31955@1" ObjectIDZND0="g_29c0bb0@0" Pin0InfoVect0LinkObjId="g_29c0bb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214729_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2358,-282 2358,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d2170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2251,-64 2251,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31948@0" ObjectIDZND0="31951@x" ObjectIDZND1="31949@x" ObjectIDZND2="31950@x" Pin0InfoVect0LinkObjId="SW-214725_0" Pin0InfoVect1LinkObjId="SW-214723_0" Pin0InfoVect2LinkObjId="SW-214724_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-64 2251,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d4bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2251,-52 2251,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31951@x" ObjectIDND1="31949@x" ObjectIDND2="31948@x" ObjectIDZND0="31950@1" Pin0InfoVect0LinkObjId="SW-214724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214725_0" Pin1InfoVect1LinkObjId="SW-214723_0" Pin1InfoVect2LinkObjId="SW-214722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-52 2251,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d4e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2251,-5 2251,9 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31950@0" ObjectIDZND0="g_29be230@0" Pin0InfoVect0LinkObjId="g_29be230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-5 2251,9 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d7890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2364,-52 2364,-40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31948@x" ObjectIDND1="31950@x" ObjectIDND2="31949@x" ObjectIDZND0="31951@1" Pin0InfoVect0LinkObjId="SW-214725_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214722_0" Pin1InfoVect1LinkObjId="SW-214724_0" Pin1InfoVect2LinkObjId="SW-214723_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2364,-52 2364,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29d7af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2364,-4 2364,12 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31951@0" ObjectIDZND0="g_29bec80@0" Pin0InfoVect0LinkObjId="g_29bec80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214725_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2364,-4 2364,12 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29da550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2364,-66 2364,-52 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31949@0" ObjectIDZND0="31948@x" ObjectIDZND1="31950@x" ObjectIDZND2="31951@x" Pin0InfoVect0LinkObjId="SW-214722_0" Pin0InfoVect1LinkObjId="SW-214724_0" Pin0InfoVect2LinkObjId="SW-214725_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214723_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2364,-66 2364,-52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29dcfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3183,-54 3183,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31962@0" ObjectIDZND0="g_29bf6d0@0" Pin0InfoVect0LinkObjId="g_29bf6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3183,-54 3183,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29dfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3181,-219 3181,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31963@1" ObjectIDZND0="g_29c1640@0" Pin0InfoVect0LinkObjId="g_29c1640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214738_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3181,-219 3181,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e35d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-226 1168,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31903@x" ObjectIDND1="31902@x" ObjectIDZND0="31901@x" ObjectIDZND1="31905@x" Pin0InfoVect0LinkObjId="SW-214072_0" Pin0InfoVect1LinkObjId="SW-214078_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214076_0" Pin1InfoVect1LinkObjId="SW-214075_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-226 1168,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e3830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-482 1204,-482 1204,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="31907@x" ObjectIDND1="31904@x" ObjectIDND2="g_29f5a50@0" ObjectIDZND0="g_29f74b0@0" Pin0InfoVect0LinkObjId="g_29f74b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214080_0" Pin1InfoVect1LinkObjId="SW-214077_0" Pin1InfoVect2LinkObjId="g_29f5a50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-482 1204,-482 1204,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e3a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-411 1168,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="load" ObjectIDND0="31907@x" ObjectIDND1="31904@x" ObjectIDZND0="g_29f74b0@0" ObjectIDZND1="g_29f5a50@0" ObjectIDZND2="33518@x" Pin0InfoVect0LinkObjId="g_29f74b0_0" Pin0InfoVect1LinkObjId="g_29f5a50_0" Pin0InfoVect2LinkObjId="EC-CX_LS.CX_LS_281Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214080_0" Pin1InfoVect1LinkObjId="SW-214077_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-411 1168,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e3cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-482 1168,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_29f74b0@0" ObjectIDND1="31907@x" ObjectIDND2="31904@x" ObjectIDZND0="33518@0" Pin0InfoVect0LinkObjId="EC-CX_LS.CX_LS_281Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f74b0_0" Pin1InfoVect1LinkObjId="SW-214080_0" Pin1InfoVect2LinkObjId="SW-214077_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-482 1168,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e6020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-256 1168,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31905@x" ObjectIDND1="31903@x" ObjectIDND2="31902@x" ObjectIDZND0="31901@0" Pin0InfoVect0LinkObjId="SW-214072_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214078_0" Pin1InfoVect1LinkObjId="SW-214076_0" Pin1InfoVect2LinkObjId="SW-214075_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-256 1168,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e6280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-307 1168,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31901@1" ObjectIDZND0="31906@x" ObjectIDZND1="31904@x" Pin0InfoVect0LinkObjId="SW-214079_0" Pin0InfoVect1LinkObjId="SW-214077_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214072_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-307 1168,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e8a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-411 1146,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_29f74b0@0" ObjectIDND1="g_29f5a50@0" ObjectIDND2="33518@x" ObjectIDZND0="31907@1" Pin0InfoVect0LinkObjId="SW-214080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f74b0_0" Pin1InfoVect1LinkObjId="g_29f5a50_0" Pin1InfoVect2LinkObjId="EC-CX_LS.CX_LS_281Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-411 1146,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29e8c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1110,-411 1090,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31907@0" ObjectIDZND0="g_29f3aa0@0" Pin0InfoVect0LinkObjId="g_29f3aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1110,-411 1090,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29eb400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-337 1147,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31901@x" ObjectIDND1="31904@x" ObjectIDZND0="31906@1" Pin0InfoVect0LinkObjId="SW-214079_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214072_0" Pin1InfoVect1LinkObjId="SW-214077_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-337 1147,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29eb660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1111,-337 1088,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31906@0" ObjectIDZND0="g_29f4530@0" Pin0InfoVect0LinkObjId="g_29f4530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214079_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1111,-337 1088,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29edc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-256 1142,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31901@x" ObjectIDND1="31903@x" ObjectIDND2="31902@x" ObjectIDZND0="31905@1" Pin0InfoVect0LinkObjId="SW-214078_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214072_0" Pin1InfoVect1LinkObjId="SW-214076_0" Pin1InfoVect2LinkObjId="SW-214075_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-256 1142,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29edec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1106,-256 1086,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31905@0" ObjectIDZND0="g_29f4fc0@0" Pin0InfoVect0LinkObjId="g_29f4fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214078_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1106,-256 1086,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f0920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-214 1134,-226 1168,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31903@1" ObjectIDZND0="31901@x" ObjectIDZND1="31905@x" ObjectIDZND2="31902@x" Pin0InfoVect0LinkObjId="SW-214072_0" Pin0InfoVect1LinkObjId="SW-214078_0" Pin0InfoVect2LinkObjId="SW-214075_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1134,-214 1134,-226 1168,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f0b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-226 1197,-226 1197,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31903@x" ObjectIDND1="31901@x" ObjectIDND2="31905@x" ObjectIDZND0="31902@1" Pin0InfoVect0LinkObjId="SW-214075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214076_0" Pin1InfoVect1LinkObjId="SW-214072_0" Pin1InfoVect2LinkObjId="SW-214078_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-226 1197,-226 1197,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f35e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-337 1168,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31901@x" ObjectIDND1="31906@x" ObjectIDZND0="31904@0" Pin0InfoVect0LinkObjId="SW-214077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214072_0" Pin1InfoVect1LinkObjId="SW-214079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-337 1168,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f3840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-391 1168,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="load" ObjectIDND0="31904@1" ObjectIDZND0="g_29f74b0@0" ObjectIDZND1="g_29f5a50@0" ObjectIDZND2="33518@x" Pin0InfoVect0LinkObjId="g_29f74b0_0" Pin0InfoVect1LinkObjId="g_29f5a50_0" Pin0InfoVect2LinkObjId="EC-CX_LS.CX_LS_281Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-391 1168,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f88d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1204,-416 1204,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_29f74b0@1" ObjectIDZND0="g_29f7e80@0" Pin0InfoVect0LinkObjId="g_29f7e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f74b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1204,-416 1204,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1168,-482 1144,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_29f74b0@0" ObjectIDND1="31907@x" ObjectIDND2="31904@x" ObjectIDZND0="g_29f5a50@0" Pin0InfoVect0LinkObjId="g_29f5a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29f74b0_0" Pin1InfoVect1LinkObjId="SW-214080_0" Pin1InfoVect2LinkObjId="SW-214077_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1168,-482 1144,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f8d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-226 1351,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31910@x" ObjectIDND1="31909@x" ObjectIDZND0="31908@x" ObjectIDZND1="31912@x" Pin0InfoVect0LinkObjId="SW-214178_0" Pin0InfoVect1LinkObjId="SW-214184_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214182_0" Pin1InfoVect1LinkObjId="SW-214181_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-226 1351,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f8ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-482 1387,-482 1387,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="31914@x" ObjectIDND1="31911@x" ObjectIDND2="g_2a0dba0@0" ObjectIDZND0="g_2a0f600@0" Pin0InfoVect0LinkObjId="g_2a0f600_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214186_0" Pin1InfoVect1LinkObjId="SW-214183_0" Pin1InfoVect2LinkObjId="g_2a0dba0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-482 1387,-482 1387,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f9250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-411 1351,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="load" ObjectIDND0="31914@x" ObjectIDND1="31911@x" ObjectIDZND0="g_2a0f600@0" ObjectIDZND1="g_2a0dba0@0" ObjectIDZND2="33519@x" Pin0InfoVect0LinkObjId="g_2a0f600_0" Pin0InfoVect1LinkObjId="g_2a0dba0_0" Pin0InfoVect2LinkObjId="EC-CX_LS.CX_LS_282Ld_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214186_0" Pin1InfoVect1LinkObjId="SW-214183_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-411 1351,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29f94b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-482 1351,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2a0f600@0" ObjectIDND1="31914@x" ObjectIDND2="31911@x" ObjectIDZND0="33519@0" Pin0InfoVect0LinkObjId="EC-CX_LS.CX_LS_282Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a0f600_0" Pin1InfoVect1LinkObjId="SW-214186_0" Pin1InfoVect2LinkObjId="SW-214183_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-482 1351,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29fb7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-256 1351,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31910@x" ObjectIDND1="31909@x" ObjectIDND2="31912@x" ObjectIDZND0="31908@0" Pin0InfoVect0LinkObjId="SW-214178_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214182_0" Pin1InfoVect1LinkObjId="SW-214181_0" Pin1InfoVect2LinkObjId="SW-214184_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-256 1351,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29fba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-307 1351,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31908@1" ObjectIDZND0="31913@x" ObjectIDZND1="31911@x" Pin0InfoVect0LinkObjId="SW-214185_0" Pin0InfoVect1LinkObjId="SW-214183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214178_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-307 1351,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29fe1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-411 1329,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2a0f600@0" ObjectIDND1="g_2a0dba0@0" ObjectIDND2="33519@x" ObjectIDZND0="31914@1" Pin0InfoVect0LinkObjId="SW-214186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a0f600_0" Pin1InfoVect1LinkObjId="g_2a0dba0_0" Pin1InfoVect2LinkObjId="EC-CX_LS.CX_LS_282Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-411 1329,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29fe430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1293,-411 1273,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31914@0" ObjectIDZND0="g_2a0bbf0@0" Pin0InfoVect0LinkObjId="g_2a0bbf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214186_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1293,-411 1273,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a00bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-337 1330,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31908@x" ObjectIDND1="31911@x" ObjectIDZND0="31913@1" Pin0InfoVect0LinkObjId="SW-214185_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214178_0" Pin1InfoVect1LinkObjId="SW-214183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-337 1330,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a00e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1294,-337 1271,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31913@0" ObjectIDZND0="g_2a0c680@0" Pin0InfoVect0LinkObjId="g_2a0c680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214185_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1294,-337 1271,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a035b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-256 1325,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="31910@x" ObjectIDND1="31909@x" ObjectIDND2="31908@x" ObjectIDZND0="31912@1" Pin0InfoVect0LinkObjId="SW-214184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214182_0" Pin1InfoVect1LinkObjId="SW-214181_0" Pin1InfoVect2LinkObjId="SW-214178_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-256 1325,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a03810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1289,-256 1269,-256 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31912@0" ObjectIDZND0="g_2a0d110@0" Pin0InfoVect0LinkObjId="g_2a0d110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1289,-256 1269,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a06270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-214 1317,-226 1351,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="31910@1" ObjectIDZND0="31908@x" ObjectIDZND1="31912@x" ObjectIDZND2="31909@x" Pin0InfoVect0LinkObjId="SW-214178_0" Pin0InfoVect1LinkObjId="SW-214184_0" Pin0InfoVect2LinkObjId="SW-214181_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-214 1317,-226 1351,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a08cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-226 1380,-226 1380,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31908@x" ObjectIDND1="31912@x" ObjectIDND2="31910@x" ObjectIDZND0="31909@1" Pin0InfoVect0LinkObjId="SW-214181_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214178_0" Pin1InfoVect1LinkObjId="SW-214184_0" Pin1InfoVect2LinkObjId="SW-214182_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-226 1380,-226 1380,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a0b730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-337 1351,-355 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31908@x" ObjectIDND1="31913@x" ObjectIDZND0="31911@0" Pin0InfoVect0LinkObjId="SW-214183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214178_0" Pin1InfoVect1LinkObjId="SW-214185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-337 1351,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a0b990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-391 1351,-411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="load" ObjectIDND0="31911@1" ObjectIDZND0="g_2a0f600@0" ObjectIDZND1="g_2a0dba0@0" ObjectIDZND2="33519@x" Pin0InfoVect0LinkObjId="g_2a0f600_0" Pin0InfoVect1LinkObjId="g_2a0dba0_0" Pin0InfoVect2LinkObjId="EC-CX_LS.CX_LS_282Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-391 1351,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a10a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1387,-416 1387,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a0f600@1" ObjectIDZND0="g_2a0ffd0@0" Pin0InfoVect0LinkObjId="g_2a0ffd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a0f600_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1387,-416 1387,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a10c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1351,-482 1327,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a0f600@0" ObjectIDND1="31914@x" ObjectIDND2="31911@x" ObjectIDZND0="g_2a0dba0@0" Pin0InfoVect0LinkObjId="g_2a0dba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a0f600_0" Pin1InfoVect1LinkObjId="SW-214186_0" Pin1InfoVect2LinkObjId="SW-214183_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1351,-482 1327,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a14170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-221 1440,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31961@1" ObjectIDZND0="g_2a10ee0@0" Pin0InfoVect0LinkObjId="g_2a10ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-221 1440,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a1bb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-483 2898,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2a2ea30@0" ObjectIDND1="31935@x" ObjectIDND2="31932@x" ObjectIDZND0="33513@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a2ea30_0" Pin1InfoVect1LinkObjId="SW-214497_0" Pin1InfoVect2LinkObjId="SW-214494_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-483 2898,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a1db90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-257 2898,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31933@x" ObjectIDND1="31931@x" ObjectIDND2="31930@x" ObjectIDZND0="31929@0" Pin0InfoVect0LinkObjId="SW-214489_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214495_0" Pin1InfoVect1LinkObjId="SW-214493_0" Pin1InfoVect2LinkObjId="SW-214492_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-257 2898,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a1ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-308 2898,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31929@1" ObjectIDZND0="31934@x" ObjectIDZND1="31932@x" Pin0InfoVect0LinkObjId="SW-214496_0" Pin0InfoVect1LinkObjId="SW-214494_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-308 2898,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a20580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-412 2876,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31932@x" ObjectIDND1="g_2a2ea30@0" ObjectIDND2="g_2a30310@0" ObjectIDZND0="31935@1" Pin0InfoVect0LinkObjId="SW-214497_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214494_0" Pin1InfoVect1LinkObjId="g_2a2ea30_0" Pin1InfoVect2LinkObjId="g_2a30310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-412 2876,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a207e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2840,-412 2820,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31935@0" ObjectIDZND0="g_2a2dfa0@0" Pin0InfoVect0LinkObjId="g_2a2dfa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214497_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2840,-412 2820,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a22f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-338 2877,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31929@x" ObjectIDND1="31932@x" ObjectIDZND0="31934@1" Pin0InfoVect0LinkObjId="SW-214496_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214489_0" Pin1InfoVect1LinkObjId="SW-214494_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-338 2877,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a231d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2841,-338 2821,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31934@0" ObjectIDZND0="g_2a4ae60@0" Pin0InfoVect0LinkObjId="g_2a4ae60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214496_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2841,-338 2821,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a25960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-257 2872,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31929@x" ObjectIDND1="31931@x" ObjectIDND2="31930@x" ObjectIDZND0="31933@1" Pin0InfoVect0LinkObjId="SW-214495_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214489_0" Pin1InfoVect1LinkObjId="SW-214493_0" Pin1InfoVect2LinkObjId="SW-214492_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-257 2872,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a25bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2836,-257 2819,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31933@0" ObjectIDZND0="g_2a4b6e0@0" Pin0InfoVect0LinkObjId="g_2a4b6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214495_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2836,-257 2819,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a28620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2864,-215 2864,-227 2898,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" EndDevType2="switch" ObjectIDND0="31931@1" ObjectIDZND0="31930@x" ObjectIDZND1="31929@x" ObjectIDZND2="31933@x" Pin0InfoVect0LinkObjId="SW-214492_0" Pin0InfoVect1LinkObjId="SW-214489_0" Pin0InfoVect2LinkObjId="SW-214495_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214493_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2864,-215 2864,-227 2898,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a2b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-227 2927,-227 2927,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="31931@x" ObjectIDND1="31929@x" ObjectIDND2="31933@x" ObjectIDZND0="31930@1" Pin0InfoVect0LinkObjId="SW-214492_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214493_0" Pin1InfoVect1LinkObjId="SW-214489_0" Pin1InfoVect2LinkObjId="SW-214495_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-227 2927,-227 2927,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a2dae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-338 2898,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="31929@x" ObjectIDND1="31934@x" ObjectIDZND0="31932@0" Pin0InfoVect0LinkObjId="SW-214494_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214489_0" Pin1InfoVect1LinkObjId="SW-214496_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-338 2898,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a2dd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-392 2898,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="31932@1" ObjectIDZND0="31935@x" ObjectIDZND1="g_2a2ea30@0" ObjectIDZND2="g_2a30310@0" Pin0InfoVect0LinkObjId="SW-214497_0" Pin0InfoVect1LinkObjId="g_2a2ea30_0" Pin0InfoVect2LinkObjId="g_2a30310_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214494_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-392 2898,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a31730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2934,-417 2934,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a30310@1" ObjectIDZND0="g_2a30ce0@0" Pin0InfoVect0LinkObjId="g_2a30ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a30310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2934,-417 2934,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a31990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-483 2874,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="31935@x" ObjectIDND1="31932@x" ObjectIDND2="g_2a30310@0" ObjectIDZND0="g_2a2ea30@0" Pin0InfoVect0LinkObjId="g_2a2ea30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214497_0" Pin1InfoVect1LinkObjId="SW-214494_0" Pin1InfoVect2LinkObjId="g_2a30310_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-483 2874,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a31bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-227 2898,-257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31931@x" ObjectIDND1="31930@x" ObjectIDZND0="31929@x" ObjectIDZND1="31933@x" Pin0InfoVect0LinkObjId="SW-214489_0" Pin0InfoVect1LinkObjId="SW-214495_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214493_0" Pin1InfoVect1LinkObjId="SW-214492_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-227 2898,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a31e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-483 2934,-483 2934,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a2ea30@0" ObjectIDND1="31935@x" ObjectIDND2="31932@x" ObjectIDZND0="g_2a30310@0" Pin0InfoVect0LinkObjId="g_2a30310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a2ea30_0" Pin1InfoVect1LinkObjId="SW-214497_0" Pin1InfoVect2LinkObjId="SW-214494_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-483 2934,-483 2934,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a34180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-258 3081,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="31940@x" ObjectIDND1="31938@x" ObjectIDND2="31937@x" ObjectIDZND0="31936@0" Pin0InfoVect0LinkObjId="SW-214588_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214594_0" Pin1InfoVect1LinkObjId="SW-214592_0" Pin1InfoVect2LinkObjId="SW-214591_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-258 3081,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a343e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-309 3081,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31936@1" ObjectIDZND0="31941@x" ObjectIDZND1="31939@x" Pin0InfoVect0LinkObjId="SW-214595_0" Pin0InfoVect1LinkObjId="SW-214593_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214588_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-309 3081,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a36b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-413 3059,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="31939@x" ObjectIDND1="g_2a46540@0" ObjectIDND2="g_2a47fa0@0" ObjectIDZND0="31942@1" Pin0InfoVect0LinkObjId="SW-214596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214593_0" Pin1InfoVect1LinkObjId="g_2a46540_0" Pin1InfoVect2LinkObjId="g_2a47fa0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-413 3059,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a36dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3023,-413 3003,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31942@0" ObjectIDZND0="g_2a44590@0" Pin0InfoVect0LinkObjId="g_2a44590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3023,-413 3003,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a39560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-339 3060,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31939@x" ObjectIDND1="31936@x" ObjectIDZND0="31941@1" Pin0InfoVect0LinkObjId="SW-214595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214593_0" Pin1InfoVect1LinkObjId="SW-214588_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-339 3060,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a397c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3024,-339 3001,-339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31941@0" ObjectIDZND0="g_2a45020@0" Pin0InfoVect0LinkObjId="g_2a45020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3024,-339 3001,-339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a3bf50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-258 3055,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="31938@x" ObjectIDND1="31937@x" ObjectIDND2="31936@x" ObjectIDZND0="31940@1" Pin0InfoVect0LinkObjId="SW-214594_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214592_0" Pin1InfoVect1LinkObjId="SW-214591_0" Pin1InfoVect2LinkObjId="SW-214588_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-258 3055,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a3c1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3019,-258 2999,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="31940@0" ObjectIDZND0="g_2a45ab0@0" Pin0InfoVect0LinkObjId="g_2a45ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3019,-258 2999,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a3ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3047,-216 3047,-228 3081,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="31938@1" ObjectIDZND0="31937@x" ObjectIDZND1="31940@x" ObjectIDZND2="31936@x" Pin0InfoVect0LinkObjId="SW-214591_0" Pin0InfoVect1LinkObjId="SW-214594_0" Pin0InfoVect2LinkObjId="SW-214588_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214592_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3047,-216 3047,-228 3081,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a41670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-228 3110,-228 3110,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="31938@x" ObjectIDND1="31940@x" ObjectIDND2="31936@x" ObjectIDZND0="31937@1" Pin0InfoVect0LinkObjId="SW-214591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-214592_0" Pin1InfoVect1LinkObjId="SW-214594_0" Pin1InfoVect2LinkObjId="SW-214588_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-228 3110,-228 3110,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a440d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-339 3081,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31941@x" ObjectIDND1="31936@x" ObjectIDZND0="31939@0" Pin0InfoVect0LinkObjId="SW-214593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214595_0" Pin1InfoVect1LinkObjId="SW-214588_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-339 3081,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a44330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-393 3081,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="31939@1" ObjectIDZND0="31942@x" ObjectIDZND1="g_2a46540@0" ObjectIDZND2="g_2a47fa0@0" Pin0InfoVect0LinkObjId="SW-214596_0" Pin0InfoVect1LinkObjId="g_2a46540_0" Pin0InfoVect2LinkObjId="g_2a47fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-393 3081,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a493c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3117,-418 3117,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a47fa0@1" ObjectIDZND0="g_2a48970@0" Pin0InfoVect0LinkObjId="g_2a48970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a47fa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3117,-418 3117,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a49620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-484 3057,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a47fa0@0" ObjectIDND1="31942@x" ObjectIDND2="31939@x" ObjectIDZND0="g_2a46540@0" Pin0InfoVect0LinkObjId="g_2a46540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a47fa0_0" Pin1InfoVect1LinkObjId="SW-214596_0" Pin1InfoVect2LinkObjId="SW-214593_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-484 3057,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a49880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-228 3081,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="31938@x" ObjectIDND1="31937@x" ObjectIDZND0="31940@x" ObjectIDZND1="31936@x" Pin0InfoVect0LinkObjId="SW-214594_0" Pin0InfoVect1LinkObjId="SW-214588_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214592_0" Pin1InfoVect1LinkObjId="SW-214591_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-228 3081,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a49ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-484 3117,-484 3117,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a46540@0" ObjectIDND1="31942@x" ObjectIDND2="31939@x" ObjectIDZND0="g_2a47fa0@0" Pin0InfoVect0LinkObjId="g_2a47fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a46540_0" Pin1InfoVect1LinkObjId="SW-214596_0" Pin1InfoVect2LinkObjId="SW-214593_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-484 3117,-484 3117,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a49d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-413 3081,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="31942@x" ObjectIDND1="31939@x" ObjectIDZND0="g_2a46540@0" ObjectIDZND1="g_2a47fa0@0" ObjectIDZND2="33522@1" Pin0InfoVect0LinkObjId="g_2a46540_0" Pin0InfoVect1LinkObjId="g_2a47fa0_0" Pin0InfoVect2LinkObjId="g_2a49fa0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214596_0" Pin1InfoVect1LinkObjId="SW-214593_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-413 3081,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a49fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3081,-484 3081,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2a46540@0" ObjectIDND1="g_2a47fa0@0" ObjectIDND2="31942@x" ObjectIDZND0="33522@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a46540_0" Pin1InfoVect1LinkObjId="g_2a47fa0_0" Pin1InfoVect2LinkObjId="SW-214596_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3081,-484 3081,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2a4a200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2898,-412 2898,-483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="31935@x" ObjectIDND1="31932@x" ObjectIDZND0="g_2a2ea30@0" ObjectIDZND1="g_2a30310@0" ObjectIDZND2="33513@1" Pin0InfoVect0LinkObjId="g_2a2ea30_0" Pin0InfoVect1LinkObjId="g_2a30310_0" Pin0InfoVect2LinkObjId="g_2a1bb10_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214497_0" Pin1InfoVect1LinkObjId="SW-214494_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2898,-412 2898,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a4c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-482 3315,-508 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2a608b0@0" ObjectIDND1="g_2a5ee50@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a608b0_0" Pin1InfoVect1LinkObjId="g_2a5ee50_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-482 3315,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a4e3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-256 3315,-280 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-256 3315,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a4e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-307 3315,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-307 3315,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a50b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-411 3293,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2a608b0@0" ObjectIDND2="g_2a5ee50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a608b0_0" Pin1InfoVect2LinkObjId="g_2a5ee50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-411 3293,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a50de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3257,-411 3237,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a5e3c0@0" Pin0InfoVect0LinkObjId="g_2a5e3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3257,-411 3237,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a53510">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-337 3294,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-337 3294,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a53770">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3258,-337 3238,-337 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a7a760@0" Pin0InfoVect0LinkObjId="g_2a7a760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3258,-337 3238,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a55ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-256 3289,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-256 3289,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a56100">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3253,-256 3236,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a7b1f0@0" Pin0InfoVect0LinkObjId="g_2a7b1f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3253,-256 3236,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a58b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3281,-214 3281,-226 3315,-226 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3281,-214 3281,-226 3315,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a5b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-226 3344,-226 3344,-214 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-226 3344,-226 3344,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a5df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-337 3315,-355 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-337 3315,-355 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a5e160">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-391 3315,-411 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2a608b0@0" ObjectIDZND2="g_2a5ee50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2a608b0_0" Pin0InfoVect2LinkObjId="g_2a5ee50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-391 3315,-411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a61cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3351,-416 3351,-403 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a608b0@1" ObjectIDZND0="g_2a61280@0" Pin0InfoVect0LinkObjId="g_2a61280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a608b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3351,-416 3351,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a61f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-482 3291,-482 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a608b0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a5ee50@0" Pin0InfoVect0LinkObjId="g_2a5ee50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a608b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-482 3291,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a62190">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-226 3315,-256 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-226 3315,-256 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a623f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-482 3351,-482 3351,-466 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a5ee50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a608b0@0" Pin0InfoVect0LinkObjId="g_2a608b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a5ee50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-482 3351,-482 3351,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a646c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-257 3498,-281 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-257 3498,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a64920">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-308 3498,-338 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-308 3498,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a67050">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-412 3476,-412 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2a782a0@0" ObjectIDND2="g_2a76840@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a782a0_0" Pin1InfoVect2LinkObjId="g_2a76840_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-412 3476,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a672b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3440,-412 3420,-412 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a74890@0" Pin0InfoVect0LinkObjId="g_2a74890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3440,-412 3420,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a699e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-338 3477,-338 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-338 3477,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a69c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3441,-338 3418,-338 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a75320@0" Pin0InfoVect0LinkObjId="g_2a75320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3441,-338 3418,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a6c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-257 3472,-257 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-257 3472,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a6c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3436,-257 3416,-257 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2a75db0@0" Pin0InfoVect0LinkObjId="g_2a75db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3436,-257 3416,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a6efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3464,-215 3464,-227 3498,-227 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3464,-215 3464,-227 3498,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a719d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-227 3527,-227 3527,-215 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-227 3527,-227 3527,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a743d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-338 3498,-356 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-338 3498,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a74630">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-392 3498,-412 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="g_2a782a0@0" ObjectIDZND2="g_2a76840@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2a782a0_0" Pin0InfoVect2LinkObjId="g_2a76840_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-392 3498,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a796c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3534,-417 3534,-404 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2a782a0@1" ObjectIDZND0="g_2a78c70@0" Pin0InfoVect0LinkObjId="g_2a78c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a782a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3534,-417 3534,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a79920">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-483 3474,-483 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2a782a0@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a76840@0" Pin0InfoVect0LinkObjId="g_2a76840_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a782a0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-483 3474,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a79b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-227 3498,-257 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-227 3498,-257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a79de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-483 3534,-483 3534,-467 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a76840@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_2a782a0@0" Pin0InfoVect0LinkObjId="g_2a782a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a76840_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-483 3534,-483 3534,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-412 3498,-483 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2a782a0@0" ObjectIDZND1="g_2a76840@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2a782a0_0" Pin0InfoVect1LinkObjId="g_2a76840_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-412 3498,-483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7a2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3498,-483 3498,-509 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2a782a0@0" ObjectIDND1="g_2a76840@0" ObjectIDND2="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a782a0_0" Pin1InfoVect1LinkObjId="g_2a76840_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-483 3498,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7a500">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3315,-411 3315,-482 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2a608b0@0" ObjectIDZND1="g_2a5ee50@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_2a608b0_0" Pin0InfoVect1LinkObjId="g_2a5ee50_0" Pin0InfoVect2LinkObjId="SW-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3315,-411 3315,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a995b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2629,38 2647,38 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a98090@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a98090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2629,38 2647,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a99810">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2630,114 2647,114 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a98b20@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a98b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,114 2647,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a99a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2630,-24 2645,-24 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a97600@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a97600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2630,-24 2645,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9a560">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2686,-55 2686,-42 2717,-42 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-55 2686,-42 2717,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,-42 2748,-42 2748,-55 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2717,-42 2748,-42 2748,-55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2681,-24 2717,-24 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2681,-24 2717,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9b510">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,-7 2717,-24 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2717,-7 2717,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,-24 2717,-42 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2717,-24 2717,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2683,38 2717,38 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2683,38 2717,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9c4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,19 2717,38 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2717,19 2717,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9c720">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,38 2717,55 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2717,38 2717,55 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9c980">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2683,114 2717,114 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2683,114 2717,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,91 2717,114 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2717,91 2717,114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a9d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2717,114 2717,154 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2717,114 2717,154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aafea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2039,35 2057,35 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aae980@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aae980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2039,35 2057,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2040,111 2057,111 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aaf410@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aaf410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2040,111 2057,111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0360">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2040,-27 2055,-27 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aadef0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aadef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2040,-27 2055,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab05c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2096,-58 2096,-45 2127,-45 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2096,-58 2096,-45 2127,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,-45 2158,-45 2158,-58 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2127,-45 2158,-45 2158,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2091,-27 2127,-27 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2091,-27 2127,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,-10 2127,-27 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2127,-10 2127,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab0f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,-27 2127,-45 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2127,-27 2127,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2093,35 2127,35 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2093,35 2127,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab1400">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,16 2127,35 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2127,16 2127,35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,35 2127,52 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2127,35 2127,52 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab18c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2093,111 2127,111 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2093,111 2127,111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab1b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,88 2127,111 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2127,88 2127,111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab1d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2127,111 2127,151 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2127,111 2127,151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac6790">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1682,33 1700,33 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ac5270@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac5270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1682,33 1700,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac69f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1683,109 1700,109 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ac5d00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac5d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,109 1700,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac6c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1683,-29 1698,-29 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ac47e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac47e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1683,-29 1698,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1739,-60 1739,-47 1770,-47 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-60 1739,-47 1770,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7110">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,-47 1801,-47 1801,-60 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1770,-47 1801,-47 1801,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7370">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1734,-29 1770,-29 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1734,-29 1770,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac75d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,-12 1770,-29 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1770,-12 1770,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7830">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,-29 1770,-47 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1770,-29 1770,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1736,33 1770,33 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1736,33 1770,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,14 1770,33 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1770,14 1770,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac7f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,33 1770,50 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1770,33 1770,50 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac81b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1736,109 1770,109 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1736,109 1770,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac8410">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,86 1770,109 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1770,86 1770,109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ac8670">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1770,109 1770,149 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1770,109 1770,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad5360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3280,11 3298,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ad48d0@0" ObjectIDZND0="31946@0" Pin0InfoVect0LinkObjId="SW-214684_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad48d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3280,11 3298,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad55c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3281,-51 3296,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ad3e40@0" ObjectIDZND0="31947@0" Pin0InfoVect0LinkObjId="SW-214685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad3e40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3281,-51 3296,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad8020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3332,-51 3368,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31947@1" ObjectIDZND0="31943@x" ObjectIDZND1="31945@x" Pin0InfoVect0LinkObjId="SW-214681_0" Pin0InfoVect1LinkObjId="SW-214683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3332,-51 3368,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad8b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,-34 3368,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31943@1" ObjectIDZND0="31947@x" ObjectIDZND1="31945@x" Pin0InfoVect0LinkObjId="SW-214685_0" Pin0InfoVect1LinkObjId="SW-214683_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3368,-34 3368,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad8d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,-51 3368,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31947@x" ObjectIDND1="31943@x" ObjectIDZND0="31945@0" Pin0InfoVect0LinkObjId="SW-214683_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214685_0" Pin1InfoVect1LinkObjId="SW-214681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3368,-51 3368,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad8fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3334,11 3368,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="31946@1" ObjectIDZND0="31943@x" ObjectIDZND1="31944@x" Pin0InfoVect0LinkObjId="SW-214681_0" Pin0InfoVect1LinkObjId="SW-214682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214684_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3334,11 3368,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad9ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,-7 3368,11 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="31943@0" ObjectIDZND0="31946@x" ObjectIDZND1="31944@x" Pin0InfoVect0LinkObjId="SW-214684_0" Pin0InfoVect1LinkObjId="SW-214682_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3368,-7 3368,11 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad9d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,11 3368,31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="31946@x" ObjectIDND1="31943@x" ObjectIDZND0="31944@1" Pin0InfoVect0LinkObjId="SW-214682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214684_0" Pin1InfoVect1LinkObjId="SW-214681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3368,11 3368,31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ad9f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,-102 3368,-162 3369,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31945@1" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2af3270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214683_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3368,-102 3368,-162 3369,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ada1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3368,67 3368,80 3414,80 3414,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31944@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2af2a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3368,67 3368,80 3414,80 3414,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae50f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1498,13 1516,13 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ae4660@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,13 1516,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae5350">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1499,-49 1514,-49 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ae3bd0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae3bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-49 1514,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae7d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1550,-49 1586,-49 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1550,-49 1586,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae7fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,-32 1586,-49 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1586,-32 1586,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae8210">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,-49 1586,-64 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1586,-49 1586,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae8470">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1552,13 1586,13 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1552,13 1586,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae86d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,-5 1586,13 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1586,-5 1586,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae8930">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,13 1586,33 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1586,13 1586,33 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ae8b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,-100 1586,-160 1587,-161 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2aeb440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1586,-100 1586,-160 1587,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2ae8df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1586,69 1586,82 1632,82 1632,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2aed030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1586,69 1586,82 1632,82 1632,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aeb440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-178 1134,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31903@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,-178 1134,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aeb630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1317,-178 1317,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31910@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1317,-178 1317,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aeb820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1440,-185 1440,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31961@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1440,-185 1440,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aed030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1438,-86 1438,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31958@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214733_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1438,-86 1438,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aed860">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1555,-177 1555,-161 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1555,-177 1555,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aee090">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1737,-177 1737,-161 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1737,-177 1737,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aee8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1739,-96 1739,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1739,-96 1739,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aef0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1801,-96 1801,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1801,-96 1801,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2aef920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1914,-177 1914,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31917@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214288_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1914,-177 1914,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af0150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2095,-178 2095,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31924@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2095,-178 2095,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af0980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2249,-173 2249,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31952@0" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2249,-173 2249,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af11b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2096,-94 2096,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2096,-94 2096,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af19e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2158,-94 2158,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2158,-94 2158,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af2210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2251,-100 2251,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31948@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214722_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2251,-100 2251,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af2a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2364,-102 2364,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31949@1" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214723_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2364,-102 2364,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af3270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2358,-176 2358,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31953@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2358,-176 2358,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af34d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2502,-177 2502,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2502,-177 2502,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af48a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2685,-178 2685,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2685,-178 2685,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af50a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2864,-179 2864,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31931@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214493_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2864,-179 2864,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af5800">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2748,-91 2748,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2748,-91 2748,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af6030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3047,-180 3047,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31938@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3047,-180 3047,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af6860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3183,-90 3183,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31962@1" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3183,-90 3183,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af7090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3181,-183 3181,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31963@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3181,-183 3181,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af78c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3281,-178 3281,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3281,-178 3281,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2af80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3464,-179 3464,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3464,-179 3464,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b02570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1644,210 1644,203 1674,203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b01400@0" ObjectIDZND1="g_2b03520@0" Pin0InfoVect0LinkObjId="g_2b01400_0" Pin0InfoVect1LinkObjId="g_2b03520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1644,210 1644,203 1674,203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b027d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,210 1674,203 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b01400@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2b03520@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2b03520_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b01400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1674,210 1674,203 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b02a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,203 1706,203 1705,216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b01400@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b03520@1" Pin0InfoVect0LinkObjId="g_2b03520_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b01400_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,203 1706,203 1705,216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b06530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1644,260 1644,268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b01400@0" ObjectIDZND0="g_2b01b20@0" Pin0InfoVect0LinkObjId="g_2b01b20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b01400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1644,260 1644,268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b07bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,125 1608,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b06790@0" ObjectIDZND0="g_2b07e10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b07e10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b06790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1608,125 1608,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,175 1578,183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b06790@0" ObjectIDZND0="g_2b07160@0" Pin0InfoVect0LinkObjId="g_2b07160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b06790_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,175 1578,183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,168 1769,168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,168 1769,168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1707,212 1769,212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1707,212 1769,212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1936,127 1936,120 1966,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b0b540@0" ObjectIDZND1="g_2b0d080@0" Pin0InfoVect0LinkObjId="g_2b0b540_0" Pin0InfoVect1LinkObjId="g_2b0d080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1936,127 1936,120 1966,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1966,127 1966,120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b0b540@0" ObjectIDZND0="g_2b0d080@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b0d080_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0b540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1966,127 1966,120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1966,120 1998,120 1997,133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b0b540@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b0d080@1" Pin0InfoVect0LinkObjId="g_2b0d080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b0b540_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1966,120 1998,120 1997,133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b10090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1936,177 1936,185 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b0b540@0" ObjectIDZND0="g_2b0bf10@0" Pin0InfoVect0LinkObjId="g_2b0bf10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b0b540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1936,177 1936,185 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b102f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1997,170 2127,170 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1997,170 2127,170 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b11970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,130 2525,123 2555,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b10550@0" ObjectIDZND1="g_2b12090@0" Pin0InfoVect0LinkObjId="g_2b10550_0" Pin0InfoVect1LinkObjId="g_2b12090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2525,130 2525,123 2555,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b11bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,130 2555,123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b10550@0" ObjectIDZND0="g_2b12090@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b12090_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b10550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2555,130 2555,123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b11e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,123 2587,123 2586,136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b10550@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b12090@1" Pin0InfoVect0LinkObjId="g_2b12090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b10550_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2555,123 2587,123 2586,136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b150a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,180 2525,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b10550@0" ObjectIDZND0="g_2b10f20@0" Pin0InfoVect0LinkObjId="g_2b10f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b10550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2525,180 2525,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b15300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2586,173 2716,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2586,173 2716,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b167f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,212 2001,205 2031,205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b15560@0" ObjectIDZND1="g_2b16f10@0" Pin0InfoVect0LinkObjId="g_2b15560_0" Pin0InfoVect1LinkObjId="g_2b16f10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2001,212 2001,205 2031,205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b16a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,212 2031,205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b15560@0" ObjectIDZND0="g_2b16f10@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b16f10_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b15560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2031,212 2031,205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b16cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,205 2063,205 2062,218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b15560@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b16f10@1" Pin0InfoVect0LinkObjId="g_2b16f10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b15560_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,205 2063,205 2062,218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b19f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,262 2001,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b15560@0" ObjectIDZND0="g_2b15f30@0" Pin0InfoVect0LinkObjId="g_2b15f30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b15560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2001,262 2001,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2064,214 2126,214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2064,214 2126,214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1b800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2591,215 2591,208 2621,208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2b1a3e0@0" ObjectIDZND1="g_2b1bf20@0" Pin0InfoVect0LinkObjId="g_2b1a3e0_0" Pin0InfoVect1LinkObjId="g_2b1bf20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2591,215 2591,208 2621,208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1ba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2621,215 2621,208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b1a3e0@0" ObjectIDZND0="g_2b1bf20@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b1bf20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1a3e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2621,215 2621,208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2621,208 2653,208 2652,221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b1a3e0@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b1bf20@1" Pin0InfoVect0LinkObjId="g_2b1bf20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b1a3e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2621,208 2653,208 2652,221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2591,265 2591,273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="earth" ObjectIDND0="0@x" ObjectIDND1="g_2b1a3e0@0" ObjectIDZND0="g_2b1adb0@0" Pin0InfoVect0LinkObjId="g_2b1adb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b1a3e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2591,265 2591,273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1f190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2654,217 2716,217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer" ObjectIDZND0="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2654,217 2716,217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b22b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1380,-178 1380,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31909@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1380,-178 1380,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b23bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1644,250 1644,260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b01b20@0" ObjectIDZND1="g_2b01400@0" Pin0InfoVect0LinkObjId="g_2b01b20_0" Pin0InfoVect1LinkObjId="g_2b01400_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1644,250 1644,260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b23e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1644,260 1674,260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b01b20@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b01400@1" Pin0InfoVect0LinkObjId="g_2b01400_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b01b20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1644,260 1674,260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b24920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,165 1578,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2b06790@0" ObjectIDZND1="g_2b07160@0" Pin0InfoVect0LinkObjId="g_2b06790_0" Pin0InfoVect1LinkObjId="g_2b07160_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1578,165 1578,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b24b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,175 1608,175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b07160@0" ObjectIDZND0="g_2b06790@1" Pin0InfoVect0LinkObjId="g_2b06790_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b07160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,175 1608,175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b25670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1936,167 1936,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2b0b540@0" ObjectIDZND1="g_2b0bf10@0" Pin0InfoVect0LinkObjId="g_2b0b540_0" Pin0InfoVect1LinkObjId="g_2b0bf10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1936,167 1936,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b258d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1936,177 1966,177 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b0bf10@0" ObjectIDZND0="g_2b0b540@1" Pin0InfoVect0LinkObjId="g_2b0b540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b0bf10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1936,177 1966,177 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,170 2525,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2b10550@0" ObjectIDZND1="g_2b10f20@0" Pin0InfoVect0LinkObjId="g_2b10550_0" Pin0InfoVect1LinkObjId="g_2b10f20_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2525,170 2525,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b26eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2525,180 2555,180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b10f20@0" ObjectIDZND0="g_2b10550@1" Pin0InfoVect0LinkObjId="g_2b10550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b10f20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2525,180 2555,180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b28230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,252 2001,262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2b15560@0" ObjectIDZND1="g_2b15f30@0" Pin0InfoVect0LinkObjId="g_2b15560_0" Pin0InfoVect1LinkObjId="g_2b15f30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2001,252 2001,262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b28490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2001,262 2031,262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b15f30@0" ObjectIDZND0="g_2b15560@1" Pin0InfoVect0LinkObjId="g_2b15560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b15f30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2001,262 2031,262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b29810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2591,255 2591,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2b1a3e0@0" ObjectIDZND1="g_2b1adb0@0" Pin0InfoVect0LinkObjId="g_2b1a3e0_0" Pin0InfoVect1LinkObjId="g_2b1adb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2591,255 2591,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b29a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2591,265 2621,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="earth" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b1adb0@0" ObjectIDZND0="g_2b1a3e0@1" Pin0InfoVect0LinkObjId="g_2b1a3e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b1adb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2591,265 2621,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2a560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1674,260 1705,260 1705,252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b03520@0" Pin0InfoVect0LinkObjId="g_2b03520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1674,260 1705,260 1705,252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,175 1639,175 1639,167 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b07e10@0" Pin0InfoVect0LinkObjId="g_2b07e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,175 1639,175 1639,167 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1966,177 1997,177 1997,169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b0d080@0" Pin0InfoVect0LinkObjId="g_2b0d080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1966,177 1997,177 1997,169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2ac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2555,180 2586,180 2586,172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b12090@0" Pin0InfoVect0LinkObjId="g_2b12090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2555,180 2586,180 2586,172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2aee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2031,262 2062,262 2062,254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b16f10@0" Pin0InfoVect0LinkObjId="g_2b16f10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2031,262 2062,262 2062,254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2b140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2621,265 2652,265 2652,257 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2b1bf20@0" Pin0InfoVect0LinkObjId="g_2b1bf20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2621,265 2652,265 2652,257 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,131 1640,118 1608,118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b07e10@1" ObjectIDZND0="g_2b06790@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b06790_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b07e10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1639,131 1640,118 1608,118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,118 1578,118 1578,125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b06790@0" ObjectIDND1="g_2b07e10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b06790_0" Pin1InfoVect1LinkObjId="g_2b07e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,118 1578,118 1578,125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b2e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1197,-179 1197,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31902@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214075_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1197,-179 1197,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b31920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1155,-80 1155,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31956@1" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214731_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1155,-80 1155,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b352e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-22 1155,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="31957@0" ObjectIDZND0="g_2b1f3f0@0" ObjectIDZND1="31956@x" Pin0InfoVect0LinkObjId="g_2b1f3f0_0" Pin0InfoVect1LinkObjId="SW-214731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-22 1155,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b35dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1155,7 1155,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b1f3f0@0" ObjectIDZND0="31957@x" ObjectIDZND1="31956@x" Pin0InfoVect0LinkObjId="SW-214732_0" Pin0InfoVect1LinkObjId="SW-214731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1f3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1155,7 1155,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b36030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1155,-22 1155,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" EndDevType0="switch" ObjectIDND0="31957@x" ObjectIDND1="g_2b1f3f0@0" ObjectIDZND0="31956@0" Pin0InfoVect0LinkObjId="SW-214731_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-214732_0" Pin1InfoVect1LinkObjId="g_2b1f3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1155,-22 1155,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b36290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1977,-177 1977,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31916@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214287_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1977,-177 1977,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b36ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1618,-177 1618,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1618,-177 1618,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b372f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="1800,-177 1800,-115 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-177 1800,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b37b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2158,-178 2158,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31923@0" ObjectIDZND0="31965@0" Pin0InfoVect0LinkObjId="g_2ae8df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2158,-178 2158,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b41400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1909,-26 1945,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="31960@0" ObjectIDZND0="g_2b38350@0" ObjectIDZND1="31959@x" Pin0InfoVect0LinkObjId="g_2b38350_0" Pin0InfoVect1LinkObjId="SW-214734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1909,-26 1945,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b41660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,3 1945,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b38350@0" ObjectIDZND0="31960@x" ObjectIDZND1="31959@x" Pin0InfoVect0LinkObjId="SW-214735_0" Pin0InfoVect1LinkObjId="SW-214734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b38350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1945,3 1945,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b418c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-26 1945,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b38350@0" ObjectIDND1="31960@x" ObjectIDZND0="31959@0" Pin0InfoVect0LinkObjId="SW-214734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b38350_0" Pin1InfoVect1LinkObjId="SW-214735_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-26 1945,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b423b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-84 1945,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31959@1" ObjectIDZND0="31966@0" Pin0InfoVect0LinkObjId="g_2ae8b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214734_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-84 1945,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b42be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2565,-177 2565,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2565,-177 2565,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b43410">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2748,-178 2748,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2748,-178 2748,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b43c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2686,-91 2686,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2686,-91 2686,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b44470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2927,-179 2927,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31930@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214492_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2927,-179 2927,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b44ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3110,-180 3110,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="31937@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-214591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3110,-180 3110,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b454d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3344,-178 3344,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3344,-178 3344,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b45d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3527,-179 3527,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3527,-179 3527,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b4be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2497,-27 2533,-27 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b4c520@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b4c520_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2497,-27 2533,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b4c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2533,2 2533,-27 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b4c520@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b4c520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2533,2 2533,-27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b4c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2533,-27 2533,-49 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b4c520@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b4c520_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2533,-27 2533,-49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b504d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="2533,-85 2533,-162 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31968@0" Pin0InfoVect0LinkObjId="g_2ad9f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2533,-85 2533,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b565d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3037,-18 3073,-18 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2b56cf0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2b56cf0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3037,-18 3073,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b56830">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3073,11 3073,-18 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b56cf0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b56cf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3073,11 3073,-18 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b56a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3073,-18 3073,-40 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b56cf0@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b56cf0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3073,-18 3073,-40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2b5ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="3" points="3073,-76 3073,-116 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="31967@0" Pin0InfoVect0LinkObjId="g_2ada1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3073,-76 3073,-116 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3414" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1632" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1587" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1134" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1317" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1440" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1438" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1555" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1737" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1739" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1801" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1914" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="2095" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="2249" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="2096" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="2158" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="2251" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="2364" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="3369" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2358" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2502" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2685" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2864" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="2748" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="3047" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3183" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="3181" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="3281" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="3464" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1380" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1197" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1155" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1977" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1618" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="1800" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31965" cx="2158" cy="-115" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31966" cx="1945" cy="-161" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="2565" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="2748" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2686" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="2927" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3110" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3344" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3527" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31968" cx="2533" cy="-162" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="31967" cx="3073" cy="-116" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-214002" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 -676.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31890" ObjectName="DYN-CX_LS"/>
     <cge:Meas_Ref ObjectId="214002"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28f5a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 443.000000 -625.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_290c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.000000 -182.000000) translate(0,374)">联系方式：0878-3204740</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="18" graphid="g_29184f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 -759.500000) translate(0,15)">力石变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29dfc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 -549.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29dfc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 -549.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29dfc70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 -549.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2917470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2143.000000 -569.000000) translate(0,16)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2917470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2143.000000 -569.000000) translate(0,36)">谋</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2917470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2143.000000 -569.000000) translate(0,56)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2917470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2143.000000 -569.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,16)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,36)">树</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,56)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,76)">Ⅱ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,96)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a143d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1183.000000 -609.000000) translate(0,116)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a153d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1290.000000 -278.000000) translate(0,12)">28217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,16)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,36)">树</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,56)">牵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,76)">Ⅰ</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,96)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a15760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1364.000000 -609.000000) translate(0,116)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a1b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -549.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a1b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -549.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a1b660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -549.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -556.000000) translate(0,16)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -556.000000) translate(0,36)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2a4a460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -556.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aea750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2546.000000 -556.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aea750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2546.000000 -556.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aea750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2546.000000 -556.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aead80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2730.000000 -556.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aead80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2730.000000 -556.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aead80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2730.000000 -556.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeafc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3327.000000 -556.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeafc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3327.000000 -556.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeafc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3327.000000 -556.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeb200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -556.000000) translate(0,16)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeb200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -556.000000) translate(0,36)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2aeb200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3508.000000 -556.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2afe8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1751.000000 234.000000) translate(0,16)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2afeef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1733.000000 254.000000) translate(0,16)">(#1主变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b00270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2108.000000 241.000000) translate(0,16)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b006b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2090.000000 261.000000) translate(0,16)">(#2主变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b00bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2705.000000 244.000000) translate(0,16)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b00e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2687.000000 264.000000) translate(0,16)">(#3主变)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5cae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1180.000000 -301.000000) translate(0,12)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b60530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -203.000000) translate(0,12)">2812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b609e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -204.000000) translate(0,12)">2811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b60c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1104.000000 -280.000000) translate(0,12)">28117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b60e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 -331.000000) translate(0,12)">28160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1175.000000 -379.000000) translate(0,12)">2816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1107.000000 -404.000000) translate(0,12)">28167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1363.000000 -302.000000) translate(0,12)">282</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b61ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -203.000000) translate(0,12)">2822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1386.000000 -203.000000) translate(0,12)">2821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1289.000000 -333.000000) translate(0,12)">28260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1360.000000 -378.000000) translate(0,12)">2826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b627c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1288.000000 -403.000000) translate(0,12)">28267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1960.000000 -298.000000) translate(0,12)">285</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b62c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1873.000000 -202.000000) translate(0,12)">2852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b631b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1980.000000 -203.000000) translate(0,12)">2851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b633f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1884.000000 -250.000000) translate(0,12)">28517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="Kaiti_GB2312" font-size="16" graphid="g_2b65360" transform="matrix(0.979920 -0.000000 -0.000000 0.832895 904.280636 -781.821279) translate(0,13)">220kV力石变外接站用电电源进线示意图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b691e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -123.000000) translate(0,12)">220kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6a0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.000000 -169.000000) translate(0,12)">220kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6a750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2141.000000 -301.000000) translate(0,12)">286</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6a9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2165.000000 -203.000000) translate(0,12)">2861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ac10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2051.000000 -206.000000) translate(0,12)">2862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2060.000000 -250.000000) translate(0,12)">28617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2067.000000 -332.000000) translate(0,12)">28660</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2136.000000 -380.000000) translate(0,12)">2866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2066.000000 -406.000000) translate(0,12)">28667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2364.000000 -200.000000) translate(0,12)">2244</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6b990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2256.000000 -198.000000) translate(0,12)">2242</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6bbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2255.000000 -270.000000) translate(0,12)">22427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6be10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2362.000000 -271.000000) translate(0,12)">22447</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2258.000000 -88.000000) translate(0,12)">2131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2375.000000 -90.000000) translate(0,12)">2133</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2258.000000 -30.000000) translate(0,12)">21317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ca20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2371.000000 -29.000000) translate(0,12)">21337</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2907.000000 -302.000000) translate(0,12)">289</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6cfa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2825.000000 -201.000000) translate(0,12)">2894</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2934.000000 -204.000000) translate(0,12)">2893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2833.000000 -283.000000) translate(0,12)">28937</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2839.000000 -331.000000) translate(0,12)">28960</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6dac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2837.000000 -406.000000) translate(0,12)">28967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6dd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2905.000000 -381.000000) translate(0,12)">2896</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3090.000000 -303.000000) translate(0,12)">291</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3007.000000 -204.000000) translate(0,12)">2914</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3117.000000 -205.000000) translate(0,12)">2913</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3017.000000 -284.000000) translate(0,12)">29137</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6e840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3016.000000 -332.000000) translate(0,12)">29160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3088.000000 -382.000000) translate(0,12)">2916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ecc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3018.000000 -409.000000) translate(0,12)">29167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6ef00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3377.000000 -28.000000) translate(0,12)">234</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6f140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3375.000000 -91.000000) translate(0,12)">2344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3375.000000 41.000000) translate(0,12)">2343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6f5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3296.000000 -15.000000) translate(0,12)">23437</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3294.000000 -77.000000) translate(0,12)">23447</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1162.000000 -69.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 -15.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6fec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.000000 -209.000000) translate(0,12)">22017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b70100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1445.000000 -75.000000) translate(0,12)">21017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b70340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1952.000000 -73.000000) translate(0,12)">2902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b70580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1872.000000 -52.000000) translate(0,12)">29027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b707c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3188.000000 -208.000000) translate(0,12)">24017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b70a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3190.000000 -79.000000) translate(0,12)">23017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b75840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3587.000000 -124.000000) translate(0,12)">220kVⅢ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b75db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3586.000000 -170.000000) translate(0,12)">220kVⅣ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,16)">1、全站停电检修前应挂“全站停电检修”牌，复电后</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,36)">方可摘除“全站停电检修”牌。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,76)">2、各类间隔工作时，停电完成后应在相应间隔挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,96)">“禁止合闸，有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,136)">3、线路工作时，停电完成后应在相应间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,156)">挂“禁止合闸，线路有人工作”牌，复电前方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,196)">4、现场工作影响对应间隔四遥信息正确性的，应挂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,216)">“禁止刷新”牌，工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,256)">5、现场开展相应间隔四遥信息核对前，应挂“调试一”牌，</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2b76310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 416.000000 -152.000000) translate(0,276)">核对工作结束后方可摘除。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1889.000000 -328.000000) translate(0,12)">28560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b88480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 -400.000000) translate(0,12)">28567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b886c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1955.000000 -379.000000) translate(0,12)">2856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b89600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 449.000000 -459.000000) translate(0,16)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b8a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2909.000000 -563.000000) translate(0,16)">方</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b8a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2909.000000 -563.000000) translate(0,36)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_2b8a210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2909.000000 -563.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b8b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1960.000000 -561.000000) translate(0,15)">鹿</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b8b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1960.000000 -561.000000) translate(0,33)">力</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b8b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1960.000000 -561.000000) translate(0,51)">线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1580.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1762.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214284">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31915" ObjectName="SW-CX_LS.CX_LS_285BK"/>
     <cge:Meas_Ref ObjectId="214284"/>
    <cge:TPSR_Ref TObjectID="31915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214383">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2120.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31922" ObjectName="SW-CX_LS.CX_LS_286BK"/>
     <cge:Meas_Ref ObjectId="214383"/>
    <cge:TPSR_Ref TObjectID="31922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2527.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2710.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214072">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1159.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31901" ObjectName="SW-CX_LS.CX_LS_281BK"/>
     <cge:Meas_Ref ObjectId="214072"/>
    <cge:TPSR_Ref TObjectID="31901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214178">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1342.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31908" ObjectName="SW-CX_LS.CX_LS_282BK"/>
     <cge:Meas_Ref ObjectId="214178"/>
    <cge:TPSR_Ref TObjectID="31908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214489">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2889.000000 -273.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31929" ObjectName="SW-CX_LS.CX_LS_289BK"/>
     <cge:Meas_Ref ObjectId="214489"/>
    <cge:TPSR_Ref TObjectID="31929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214588">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3072.000000 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31936" ObjectName="SW-CX_LS.CX_LS_291BK"/>
     <cge:Meas_Ref ObjectId="214588"/>
    <cge:TPSR_Ref TObjectID="31936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3306.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3489.000000 -273.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2708.000000 28.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2118.000000 25.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1761.000000 23.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-214681">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3359.000000 1.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="31943" ObjectName="SW-CX_LS.CX_LS_234BK"/>
     <cge:Meas_Ref ObjectId="214681"/>
    <cge:TPSR_Ref TObjectID="31943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1577.000000 3.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LS.CX_LS_2IIM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,-161 2276,-161 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31966" ObjectName="BS-CX_LS.CX_LS_2IIM"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   <polyline fill="none" opacity="0" points="1047,-161 2276,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LS.CX_LS_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,-115 2276,-115 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31965" ObjectName="BS-CX_LS.CX_LS_2IM"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   <polyline fill="none" opacity="0" points="1047,-115 2276,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LS.CX_LS_2ⅣM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-162 3578,-162 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31968" ObjectName="BS-CX_LS.CX_LS_2ⅣM"/>
    <cge:TPSR_Ref TObjectID="31968"/></metadata>
   <polyline fill="none" opacity="0" points="2332,-162 3578,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LS.CX_LS_2ⅢM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2332,-116 3577,-116 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="31967" ObjectName="BS-CX_LS.CX_LS_2ⅢM"/>
    <cge:TPSR_Ref TObjectID="31967"/></metadata>
   <polyline fill="none" opacity="0" points="2332,-116 3577,-116 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2934dc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1618.000000 -410.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294cc70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 -410.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29675f0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 -410.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2981f70">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2158.000000 -411.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299b850">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2565.000000 -410.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b3240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2748.000000 -411.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f74b0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1197.000000 -411.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0f600">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.000000 -411.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a30310">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2927.000000 -412.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a47fa0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3110.000000 -413.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a608b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3344.000000 -411.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a782a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3527.000000 -412.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b01400">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 265.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b03520">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1700.000000 257.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b06790">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1601.000000 180.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b07e10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1634.000000 172.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0b540">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1959.000000 182.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0d080">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1992.000000 174.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b10550">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2548.000000 185.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b12090">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2581.000000 177.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15560">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2024.000000 267.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b16f10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 259.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1a3e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2614.000000 270.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1bf20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2647.000000 262.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31901"/>
     <cge:Term_Ref ObjectID="46043"/>
    <cge:TPSR_Ref TObjectID="31901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214822" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214822" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31901"/>
     <cge:Term_Ref ObjectID="46043"/>
    <cge:TPSR_Ref TObjectID="31901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214824" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214824" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31901"/>
     <cge:Term_Ref ObjectID="46043"/>
    <cge:TPSR_Ref TObjectID="31901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31908"/>
     <cge:Term_Ref ObjectID="46057"/>
    <cge:TPSR_Ref TObjectID="31908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214812" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214812" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31908"/>
     <cge:Term_Ref ObjectID="46057"/>
    <cge:TPSR_Ref TObjectID="31908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1324.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31908"/>
     <cge:Term_Ref ObjectID="46057"/>
    <cge:TPSR_Ref TObjectID="31908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214861" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1913.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214861" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31915"/>
     <cge:Term_Ref ObjectID="46071"/>
    <cge:TPSR_Ref TObjectID="31915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214862" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1913.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214862" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31915"/>
     <cge:Term_Ref ObjectID="46071"/>
    <cge:TPSR_Ref TObjectID="31915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214864" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1913.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214864" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31915"/>
     <cge:Term_Ref ObjectID="46071"/>
    <cge:TPSR_Ref TObjectID="31915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214851" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214851" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31922"/>
     <cge:Term_Ref ObjectID="46085"/>
    <cge:TPSR_Ref TObjectID="31922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214852" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214852" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31922"/>
     <cge:Term_Ref ObjectID="46085"/>
    <cge:TPSR_Ref TObjectID="31922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214854" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2096.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31922"/>
     <cge:Term_Ref ObjectID="46085"/>
    <cge:TPSR_Ref TObjectID="31922"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214831" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2862.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214831" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31929"/>
     <cge:Term_Ref ObjectID="46099"/>
    <cge:TPSR_Ref TObjectID="31929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214832" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2862.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214832" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31929"/>
     <cge:Term_Ref ObjectID="46099"/>
    <cge:TPSR_Ref TObjectID="31929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2862.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31929"/>
     <cge:Term_Ref ObjectID="46099"/>
    <cge:TPSR_Ref TObjectID="31929"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-214841" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3056.000000 -671.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214841" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31936"/>
     <cge:Term_Ref ObjectID="46113"/>
    <cge:TPSR_Ref TObjectID="31936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-214842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3056.000000 -671.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31936"/>
     <cge:Term_Ref ObjectID="46113"/>
    <cge:TPSR_Ref TObjectID="31936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-214844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3056.000000 -671.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31936"/>
     <cge:Term_Ref ObjectID="46113"/>
    <cge:TPSR_Ref TObjectID="31936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-214892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-214893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-214894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214894" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-214896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-214889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-214895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 974.000000 -265.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31966"/>
     <cge:Term_Ref ObjectID="46170"/>
    <cge:TPSR_Ref TObjectID="31966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-214884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-214885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-214886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-214888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-214881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-214887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -102.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="214887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="31965"/>
     <cge:Term_Ref ObjectID="46169"/>
    <cge:TPSR_Ref TObjectID="31965"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_局属变220.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="551" y="-770"/></g>
   <g href="cx_索引_接线图_局属变220.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="502" y="-787"/></g>
   <g href="220kV力石变外接站用电电源进线示意图.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" stroke="rgb(255,255,255)" width="293" x="900" y="-785"/></g>
   <g href="220kV力石变力树牵II回线281间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1180" y="-301"/></g>
   <g href="220kV力石变力树牵I回线282间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1363" y="-302"/></g>
   <g href="220kV力石变鹿力线285间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1958" y="-299"/></g>
   <g href="220kV力石变力谋牵线286间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2141" y="-301"/></g>
   <g href="220kV力石变方力线289间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2907" y="-302"/></g>
   <g href="220kV力石变元力线291间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3090" y="-303"/></g>
   <g href="220kV力石变母联234间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3377" y="-28"/></g>
   <g href="220kV力石变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="90" x="446" y="-460"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b73150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1081.000000 643.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b74430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 673.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b74fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1056.000000 658.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b83e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 895.000000 269.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b84a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 895.000000 254.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b85270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 895.000000 238.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b85770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 903.500000 187.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 221.000000) translate(0,12)">3U0（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 203.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b86eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 107.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 92.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 76.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.500000 25.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b877c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 59.000000) translate(0,12)">3U0（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b87a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 41.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_LS.CX_LS_281Ld">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1162.000000 -503.000000)" xlink:href="#load:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33518" ObjectName="EC-CX_LS.CX_LS_281Ld"/>
    <cge:TPSR_Ref TObjectID="33518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LS.CX_LS_282Ld">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1345.000000 -503.000000)" xlink:href="#load:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33519" ObjectName="EC-CX_LS.CX_LS_282Ld"/>
    <cge:TPSR_Ref TObjectID="33519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_LS.CX_LS_286Ld">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2123.000000 -503.000000)" xlink:href="#load:shape13"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33520" ObjectName="EC-CX_LS.CX_LS_286Ld"/>
    <cge:TPSR_Ref TObjectID="33520"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="551" y="-770"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="551" y="-770"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="502" y="-787"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="502" y="-787"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/chinaz5.png" UpImage="image/an.png" imageHeight="100" imageWidth="400">
    <a>
     
     <polygon fill="rgb(0,150,0)" points="900,-785 897,-788 897,-761 900,-764 900,-785" stroke="rgb(0,150,0)"/>
     <polygon fill="rgb(0,150,0)" points="900,-785 897,-788 1196,-788 1193,-785 900,-785" stroke="rgb(0,150,0)"/>
     <polygon fill="rgb(0,50,0)" points="900,-764 897,-761 1196,-761 1193,-764 900,-764" stroke="rgb(0,50,0)"/>
     <polygon fill="rgb(0,50,0)" points="1193,-785 1196,-788 1196,-761 1193,-764 1193,-785" stroke="rgb(0,50,0)"/>
     <rect fill="rgb(0,100,0)" height="21" stroke="rgb(0,100,0)" width="293" x="900" y="-785"/>
     <rect fill="none" height="21" qtmmishow="hidden" stroke="rgb(255,255,255)" width="293" x="900" y="-785"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1180" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1180" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1363" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1363" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1958" y="-299"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1958" y="-299"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2141" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2141" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2907" y="-302"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2907" y="-302"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3090" y="-303"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3090" y="-303"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3377" y="-28"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3377" y="-28"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="90" x="446" y="-460"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="90" x="446" y="-460"/></g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 234.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 234.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1732.000000 234.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2089.000000 236.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2089.000000 236.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2089.000000 236.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 239.000000)" xlink:href="#transformer:shape5_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 239.000000)" xlink:href="#transformer:shape5_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2679.000000 239.000000)" xlink:href="#transformer:shape5-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 539.000000 -711.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LS"/>
</svg>