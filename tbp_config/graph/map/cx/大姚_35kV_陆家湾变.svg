<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-265" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="-768 -1433 2114 1427">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape30_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="31" x2="14" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape30-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="12" x2="34" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="32" x2="41" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape24_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="switch2:shape24-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="11" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape14_0">
    <circle cx="37" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="84" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="70" x2="68" y1="84" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="45" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="28" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="28" x2="45" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape14_1">
    <ellipse cx="37" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="67" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="59" y2="67"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape104">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="36" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="38" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="37" y1="43" y2="43"/>
    <ellipse cx="8" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="10" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="10" y2="14"/>
    <ellipse cx="19" cy="12" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <ellipse cx="19" cy="24" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="44" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="28" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="39" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="39" y1="12" y2="18"/>
    <rect height="13" stroke-width="1" width="5" x="37" y="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37689c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37691e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3769be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376aac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376bde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_376c9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376d430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376dc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376e500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_376ee90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37704d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_37704d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3771010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3772cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3773900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3774150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3774b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3776fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_37779c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3778ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3779520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377a010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377f460" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3780140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_377d2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_377e030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3781530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3781b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1437" width="2124" x="-773" y="-1438"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-239" x2="-195" y1="-264" y2="-264"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-191" x2="-191" y1="-102" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-192" x2="-262" y1="-90" y2="-90"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="-262" x2="-262" y1="-91" y2="-248"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-216772">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 418.241796 -849.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32232" ObjectName="SW-DY_LJW.DY_LJW_301BK"/>
     <cge:Meas_Ref ObjectId="216772"/>
    <cge:TPSR_Ref TObjectID="32232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216734">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 102.241796 -1089.716049)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32221" ObjectName="SW-DY_LJW.DY_LJW_331BK"/>
     <cge:Meas_Ref ObjectId="216734"/>
    <cge:TPSR_Ref TObjectID="32221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216890">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 -438.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32252" ObjectName="SW-DY_LJW.DY_LJW_035BK"/>
     <cge:Meas_Ref ObjectId="216890"/>
    <cge:TPSR_Ref TObjectID="32252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216867">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 86.000000 -422.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32247" ObjectName="SW-DY_LJW.DY_LJW_034BK"/>
     <cge:Meas_Ref ObjectId="216867"/>
    <cge:TPSR_Ref TObjectID="32247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216844">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 463.000000 -421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32242" ObjectName="SW-DY_LJW.DY_LJW_033BK"/>
     <cge:Meas_Ref ObjectId="216844"/>
    <cge:TPSR_Ref TObjectID="32242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216821">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 857.000000 -425.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32237" ObjectName="SW-DY_LJW.DY_LJW_032BK"/>
     <cge:Meas_Ref ObjectId="216821"/>
    <cge:TPSR_Ref TObjectID="32237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 -585.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32231" ObjectName="SW-DY_LJW.DY_LJW_001BK"/>
     <cge:Meas_Ref ObjectId="216771"/>
    <cge:TPSR_Ref TObjectID="32231"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_420cf70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 210.000000 -1282.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e06380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -1198.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3437270">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -695.000000)" xlink:href="#voltageTransformer:shape104"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="234,-1218 234,-1198 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="234,-1218 234,-1198 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="DY_WB" endPointId="0" endStationName="DY_LJW" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wanlu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="111,-1361 111,-1392 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38088" ObjectName="AC-35kV.LN_wanlu"/>
    <cge:TPSR_Ref TObjectID="38088_SS-265"/></metadata>
   <polyline fill="none" opacity="0" points="111,-1361 111,-1392 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJW.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.761290 -95.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37640" ObjectName="EC-DY_LJW.034Ld"/>
    <cge:TPSR_Ref TObjectID="37640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJW.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.761290 -94.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37639" ObjectName="EC-DY_LJW.033Ld"/>
    <cge:TPSR_Ref TObjectID="37639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-DY_LJW.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.761290 -98.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37638" ObjectName="EC-DY_LJW.032Ld"/>
    <cge:TPSR_Ref TObjectID="37638"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_44ad740" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 794.000000 -1194.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4573900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 189.544703 -1071.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4286630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -13.000000 -1225.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_451ca80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -14.000000 -1143.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41b0700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.544703 -1017.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_45814e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.544703 -901.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4490c10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -125.000000 -259.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d45900" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 -243.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4551f90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 -242.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3c463b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.000000 -246.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_447d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-858 427,-834 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="32232@0" ObjectIDZND0="32262@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216772_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-858 427,-834 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42761f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-987 428,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32218@0" ObjectIDZND0="32235@1" Pin0InfoVect0LinkObjId="SW-216774_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_42776d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-987 428,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fcce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="131,-150 131,-165 95,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2dd1bc0@0" ObjectIDZND0="32250@x" ObjectIDZND1="37640@x" Pin0InfoVect0LinkObjId="SW-216869_0" Pin0InfoVect1LinkObjId="EC-DY_LJW.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dd1bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="131,-150 131,-165 95,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4168b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-183 95,-165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32250@0" ObjectIDZND0="g_2dd1bc0@0" ObjectIDZND1="37640@x" Pin0InfoVect0LinkObjId="g_2dd1bc0_0" Pin0InfoVect1LinkObjId="EC-DY_LJW.034Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216869_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="95,-183 95,-165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44cc260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-165 95,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32250@x" ObjectIDND1="g_2dd1bc0@0" ObjectIDZND0="37640@0" Pin0InfoVect0LinkObjId="EC-DY_LJW.034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216869_0" Pin1InfoVect1LinkObjId="g_2dd1bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-165 95,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_491eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1203 880,-1188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2e06380@0" ObjectIDZND0="g_4522050@1" Pin0InfoVect0LinkObjId="g_4522050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e06380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1203 880,-1188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_491d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1127 933,-1127 933,-1164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_4522050@0" ObjectIDND1="32228@x" ObjectIDND2="32230@x" ObjectIDZND0="g_35facd0@0" Pin0InfoVect0LinkObjId="g_35facd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4522050_0" Pin1InfoVect1LinkObjId="SW-216764_0" Pin1InfoVect2LinkObjId="SW-216766_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1127 933,-1127 933,-1164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_412b100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1144 880,-1127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_4522050@0" ObjectIDZND0="g_35facd0@0" ObjectIDZND1="32228@x" ObjectIDZND2="32230@x" Pin0InfoVect0LinkObjId="g_35facd0_0" Pin0InfoVect1LinkObjId="SW-216764_0" Pin0InfoVect2LinkObjId="SW-216766_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4522050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1144 880,-1127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44a2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="800,-1180 800,-1199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32230@1" ObjectIDZND0="g_44ad740@0" Pin0InfoVect0LinkObjId="g_44ad740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216766_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="800,-1180 800,-1199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4122c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1086 880,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32228@1" ObjectIDZND0="g_35facd0@0" ObjectIDZND1="g_4522050@0" ObjectIDZND2="32230@x" Pin0InfoVect0LinkObjId="g_35facd0_0" Pin0InfoVect1LinkObjId="g_4522050_0" Pin0InfoVect2LinkObjId="SW-216766_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1086 880,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bcbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1127 800,-1127 800,-1145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_35facd0@0" ObjectIDND1="g_4522050@0" ObjectIDND2="32228@x" ObjectIDZND0="32230@0" Pin0InfoVect0LinkObjId="SW-216766_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_35facd0_0" Pin1InfoVect1LinkObjId="g_4522050_0" Pin1InfoVect2LinkObjId="SW-216764_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1127 800,-1127 800,-1145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44bf030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="112,-1077 144,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="32221@x" ObjectIDND1="32222@x" ObjectIDZND0="32225@1" Pin0InfoVect0LinkObjId="SW-216738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216734_0" Pin1InfoVect1LinkObjId="SW-216735_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="112,-1077 144,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_491fa60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="180,-1077 194,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32225@0" ObjectIDZND0="g_4573900@0" Pin0InfoVect0LinkObjId="g_4573900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="180,-1077 194,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4272fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1046 111,-1077 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="32222@1" ObjectIDZND0="32221@x" ObjectIDZND1="32225@x" Pin0InfoVect0LinkObjId="SW-216734_0" Pin0InfoVect1LinkObjId="SW-216738_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216735_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1046 111,-1077 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_453c600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1077 111,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32225@x" ObjectIDND1="32222@x" ObjectIDZND0="32221@0" Pin0InfoVect0LinkObjId="SW-216734_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216738_0" Pin1InfoVect1LinkObjId="SW-216735_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1077 111,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_42776d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1008 111,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32222@0" ObjectIDZND0="32218@0" Pin0InfoVect0LinkObjId="g_492a220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216735_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1008 111,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d6ef50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="76,-1348 110,-1348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_3916e30@0" ObjectIDZND0="32227@x" ObjectIDZND1="32223@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-216740_0" Pin0InfoVect1LinkObjId="SW-216736_0" Pin0InfoVect2LinkObjId="g_420cf70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3916e30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="76,-1348 110,-1348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d656e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="25,-1231 5,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32227@1" ObjectIDZND0="g_4286630@0" Pin0InfoVect0LinkObjId="g_4286630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="25,-1231 5,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d4e6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="61,-1231 111,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32227@0" ObjectIDZND0="32223@x" ObjectIDZND1="g_3916e30@0" ObjectIDZND2="32224@x" Pin0InfoVect0LinkObjId="SW-216736_0" Pin0InfoVect1LinkObjId="g_3916e30_0" Pin0InfoVect2LinkObjId="SW-216737_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="61,-1231 111,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4953210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1212 111,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="32223@1" ObjectIDZND0="32227@x" ObjectIDZND1="g_3916e30@0" ObjectIDZND2="32224@x" Pin0InfoVect0LinkObjId="SW-216740_0" Pin0InfoVect1LinkObjId="g_3916e30_0" Pin0InfoVect2LinkObjId="SW-216737_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216736_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1212 111,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3bc0a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="234,-1263 111,-1263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="32227@x" ObjectIDZND1="32223@x" ObjectIDZND2="g_3916e30@0" Pin0InfoVect0LinkObjId="SW-216740_0" Pin0InfoVect1LinkObjId="SW-216736_0" Pin0InfoVect2LinkObjId="g_3916e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_420cf70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="234,-1263 111,-1263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1ea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1231 111,-1263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="32227@x" ObjectIDND1="32223@x" ObjectIDZND0="g_3916e30@0" ObjectIDZND1="32224@x" ObjectIDZND2="38088@1" Pin0InfoVect0LinkObjId="g_3916e30_0" Pin0InfoVect1LinkObjId="SW-216737_0" Pin0InfoVect2LinkObjId="g_4489a20_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216740_0" Pin1InfoVect1LinkObjId="SW-216736_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1231 111,-1263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_452bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="24,-1149 4,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32226@1" ObjectIDZND0="g_451ca80@0" Pin0InfoVect0LinkObjId="g_451ca80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216739_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="24,-1149 4,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_373a470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="60,-1149 110,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32226@0" ObjectIDZND0="32223@x" ObjectIDZND1="32221@x" Pin0InfoVect0LinkObjId="SW-216736_0" Pin0InfoVect1LinkObjId="SW-216734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="60,-1149 110,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_494d680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1176 111,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32223@0" ObjectIDZND0="32226@x" ObjectIDZND1="32221@x" Pin0InfoVect0LinkObjId="SW-216739_0" Pin0InfoVect1LinkObjId="SW-216734_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1176 111,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4121a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1149 111,-1125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32226@x" ObjectIDND1="32223@x" ObjectIDZND0="32221@1" Pin0InfoVect0LinkObjId="SW-216734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216739_0" Pin1InfoVect1LinkObjId="SW-216736_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1149 111,-1125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4489a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1348 111,-1363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_3916e30@0" ObjectIDND1="32227@x" ObjectIDND2="32223@x" ObjectIDZND0="38088@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3916e30_0" Pin1InfoVect1LinkObjId="SW-216740_0" Pin1InfoVect2LinkObjId="SW-216736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1348 111,-1363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_44c85b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1263 111,-1348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="32227@x" ObjectIDND1="32223@x" ObjectIDND2="0@x" ObjectIDZND0="g_3916e30@0" ObjectIDZND1="32224@x" ObjectIDZND2="38088@1" Pin0InfoVect0LinkObjId="g_3916e30_0" Pin0InfoVect1LinkObjId="SW-216737_0" Pin0InfoVect2LinkObjId="g_4489a20_1" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216740_0" Pin1InfoVect1LinkObjId="SW-216736_0" Pin1InfoVect2LinkObjId="g_420cf70_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1263 111,-1348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3d65a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="948,-1023 962,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32229@0" ObjectIDZND0="g_41b0700@0" Pin0InfoVect0LinkObjId="g_41b0700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="948,-1023 962,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4937040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1023 912,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="32228@x" ObjectIDND1="32218@0" ObjectIDZND0="32229@1" Pin0InfoVect0LinkObjId="SW-216765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216764_0" Pin1InfoVect1LinkObjId="g_42776d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1023 912,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_48309b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1050 880,-1023 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="32228@0" ObjectIDZND0="32229@x" ObjectIDZND1="32218@0" Pin0InfoVect0LinkObjId="SW-216765_0" Pin0InfoVect1LinkObjId="g_42776d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1050 880,-1023 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_492a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-1023 880,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="32229@x" ObjectIDND1="32228@x" ObjectIDZND0="32218@0" Pin0InfoVect0LinkObjId="g_42776d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216765_0" Pin1InfoVect1LinkObjId="SW-216764_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-1023 880,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_378c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="520,-907 534,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32236@0" ObjectIDZND0="g_45814e0@0" Pin0InfoVect0LinkObjId="g_45814e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216775_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="520,-907 534,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3b3acf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="428,-907 484,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="32235@x" ObjectIDND1="32232@x" ObjectIDZND0="32236@1" Pin0InfoVect0LinkObjId="SW-216775_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216774_0" Pin1InfoVect1LinkObjId="SW-216772_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="428,-907 484,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4912660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-928 427,-907 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="32235@0" ObjectIDZND0="32236@x" ObjectIDZND1="32232@x" Pin0InfoVect0LinkObjId="SW-216775_0" Pin0InfoVect1LinkObjId="SW-216772_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216774_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="427,-928 427,-907 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4515f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="427,-907 427,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="32236@x" ObjectIDND1="32235@x" ObjectIDZND0="32232@1" Pin0InfoVect0LinkObjId="SW-216772_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216775_0" Pin1InfoVect1LinkObjId="SW-216774_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="427,-907 427,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_482fd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-700 990,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_3437270@0" ObjectIDZND0="g_456b5c0@0" Pin0InfoVect0LinkObjId="g_456b5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3437270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-700 990,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_40e6a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1036,-663 1036,-631 990,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_44f5a60@0" ObjectIDZND0="g_456b5c0@0" ObjectIDZND1="32259@x" Pin0InfoVect0LinkObjId="g_456b5c0_0" Pin0InfoVect1LinkObjId="SW-216912_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44f5a60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1036,-663 1036,-631 990,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4431080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-631 990,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_44f5a60@0" ObjectIDND1="32259@x" ObjectIDZND0="g_456b5c0@1" Pin0InfoVect0LinkObjId="g_456b5c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_44f5a60_0" Pin1InfoVect1LinkObjId="SW-216912_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-631 990,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4281750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-634 990,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_44f5a60@0" ObjectIDND1="g_456b5c0@0" ObjectIDZND0="32259@0" Pin0InfoVect0LinkObjId="SW-216912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_44f5a60_0" Pin1InfoVect1LinkObjId="g_456b5c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-634 990,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d47ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-543 990,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32258@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_4544c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-543 990,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4934320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="111,-1348 143,-1348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_3916e30@0" ObjectIDND1="32227@x" ObjectIDND2="32223@x" ObjectIDZND0="32224@0" Pin0InfoVect0LinkObjId="SW-216737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3916e30_0" Pin1InfoVect1LinkObjId="SW-216740_0" Pin1InfoVect2LinkObjId="SW-216736_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="111,-1348 143,-1348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4428ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="179,-1348 218,-1348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="32224@1" ObjectIDZND0="g_420cf70@0" Pin0InfoVect0LinkObjId="g_420cf70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="179,-1348 218,-1348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_450ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-753 425,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="32262@0" ObjectIDZND0="g_41a6320@1" Pin0InfoVect0LinkObjId="g_41a6320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_447d920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-753 425,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_44b0780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="502,-653 502,-668 426,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41dd780@0" ObjectIDZND0="g_41a6320@0" ObjectIDZND1="32234@x" Pin0InfoVect0LinkObjId="g_41a6320_0" Pin0InfoVect1LinkObjId="SW-216773_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41dd780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="502,-653 502,-668 426,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4953a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-688 425,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_41a6320@0" ObjectIDZND0="g_41dd780@0" ObjectIDZND1="32234@x" Pin0InfoVect0LinkObjId="g_41dd780_0" Pin0InfoVect1LinkObjId="SW-216773_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_41a6320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="425,-688 425,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4277c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-668 425,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_41dd780@0" ObjectIDND1="g_41a6320@0" ObjectIDZND0="32234@0" Pin0InfoVect0LinkObjId="SW-216773_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_41dd780_0" Pin1InfoVect1LinkObjId="g_41a6320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-668 425,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4544c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-559 425,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32233@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_2d47ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-559 425,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_452f810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-301 -192,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fb4390@0" ObjectIDZND0="32255@1" Pin0InfoVect0LinkObjId="SW-216892_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2fb4390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-301 -192,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d32c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-247 95,-219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_454b990@0" ObjectIDZND0="32250@1" Pin0InfoVect0LinkObjId="SW-216869_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_454b990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-247 95,-219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d7d010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-397 1277,-412 1201,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_4190270@0" ObjectIDZND0="g_2dc1d10@0" ObjectIDZND1="32261@x" Pin0InfoVect0LinkObjId="g_2dc1d10_0" Pin0InfoVect1LinkObjId="SW-216919_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4190270_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-397 1277,-412 1201,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4557de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-353 1201,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2dc1d10@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_420cf70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dc1d10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-353 1201,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41bfc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-412 1201,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_4190270@0" ObjectIDND1="32261@x" ObjectIDZND0="g_2dc1d10@1" Pin0InfoVect0LinkObjId="g_2dc1d10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4190270_0" Pin1InfoVect1LinkObjId="SW-216919_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-412 1201,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d68500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-313,-337 -313,-369 -191,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_418f3f0@0" ObjectIDZND0="g_2fb4390@0" ObjectIDZND1="32254@x" ObjectIDZND2="32256@x" Pin0InfoVect0LinkObjId="g_2fb4390_0" Pin0InfoVect1LinkObjId="SW-216891_0" Pin0InfoVect2LinkObjId="SW-216893_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_418f3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-313,-337 -313,-369 -191,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41be140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-369 -192,-340 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="32254@x" ObjectIDND1="32256@x" ObjectIDND2="g_418f3f0@0" ObjectIDZND0="g_2fb4390@1" Pin0InfoVect0LinkObjId="g_2fb4390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216891_0" Pin1InfoVect1LinkObjId="SW-216893_0" Pin1InfoVect2LinkObjId="g_418f3f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-369 -192,-340 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d76360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-119,-299 -119,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32256@0" ObjectIDZND0="g_4490c10@0" Pin0InfoVect0LinkObjId="g_4490c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216893_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-119,-299 -119,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e06760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-527 -192,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="32219@0" ObjectIDZND0="32253@0" Pin0InfoVect0LinkObjId="SW-216891_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d47ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-527 -192,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_45672c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-484 -192,-473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32253@1" ObjectIDZND0="32252@1" Pin0InfoVect0LinkObjId="SW-216890_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216891_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-484 -192,-473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41cf790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-446 -192,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32252@0" ObjectIDZND0="32254@1" Pin0InfoVect0LinkObjId="SW-216891_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216890_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-446 -192,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41fee30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-416 -192,-369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32254@0" ObjectIDZND0="g_2fb4390@0" ObjectIDZND1="32256@x" ObjectIDZND2="g_418f3f0@0" Pin0InfoVect0LinkObjId="g_2fb4390_0" Pin0InfoVect1LinkObjId="SW-216893_0" Pin0InfoVect2LinkObjId="g_418f3f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216891_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-416 -192,-369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41ef5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-369 -119,-369 -119,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2fb4390@0" ObjectIDND1="32254@x" ObjectIDND2="g_418f3f0@0" ObjectIDZND0="32256@1" Pin0InfoVect0LinkObjId="SW-216893_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2fb4390_0" Pin1InfoVect1LinkObjId="SW-216891_0" Pin1InfoVect2LinkObjId="g_418f3f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-369 -119,-369 -119,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d456a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="44,-320 44,-353 94,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_427afb0@0" ObjectIDZND0="32249@x" ObjectIDZND1="32251@x" ObjectIDZND2="g_454b990@0" Pin0InfoVect0LinkObjId="SW-216868_0" Pin0InfoVect1LinkObjId="SW-216870_0" Pin0InfoVect2LinkObjId="g_454b990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_427afb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="44,-320 44,-353 94,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_37397f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="168,-283 168,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32251@0" ObjectIDZND0="g_2d45900@0" Pin0InfoVect0LinkObjId="g_2d45900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="168,-283 168,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3739a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-468 95,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32248@1" ObjectIDZND0="32247@1" Pin0InfoVect0LinkObjId="SW-216867_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216868_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-468 95,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-430 95,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32247@0" ObjectIDZND0="32249@1" Pin0InfoVect0LinkObjId="SW-216868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216867_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-430 95,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-400 95,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="32249@0" ObjectIDZND0="g_427afb0@0" ObjectIDZND1="32251@x" ObjectIDZND2="g_454b990@0" Pin0InfoVect0LinkObjId="g_427afb0_0" Pin0InfoVect1LinkObjId="SW-216870_0" Pin0InfoVect2LinkObjId="g_454b990_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="95,-400 95,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_379b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-353 168,-353 168,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="32249@x" ObjectIDND1="g_427afb0@0" ObjectIDND2="g_454b990@0" ObjectIDZND0="32251@1" Pin0InfoVect0LinkObjId="SW-216870_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216868_0" Pin1InfoVect1LinkObjId="g_427afb0_0" Pin1InfoVect2LinkObjId="g_454b990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-353 168,-353 168,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4c50b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-302 95,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_454b990@0" ObjectIDND1="32249@x" ObjectIDND2="g_427afb0@0" ObjectIDZND0="g_454b990@1" Pin0InfoVect0LinkObjId="g_454b990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_454b990_0" Pin1InfoVect1LinkObjId="SW-216868_0" Pin1InfoVect2LinkObjId="g_427afb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-302 95,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4c511c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_454b990@0" ObjectIDND1="32249@x" ObjectIDND2="g_427afb0@0" ObjectIDZND0="g_454b990@0" ObjectIDZND1="32249@x" ObjectIDZND2="g_427afb0@0" Pin0InfoVect0LinkObjId="g_454b990_0" Pin0InfoVect1LinkObjId="SW-216868_0" Pin0InfoVect2LinkObjId="g_427afb0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_454b990_0" Pin1InfoVect1LinkObjId="SW-216868_0" Pin1InfoVect2LinkObjId="g_427afb0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="95,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4548310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-299 95,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_454b990@0" ObjectIDND1="g_454b990@0" ObjectIDND2="32249@x" ObjectIDZND0="32249@x" ObjectIDZND1="g_427afb0@0" ObjectIDZND2="32251@x" Pin0InfoVect0LinkObjId="SW-216868_0" Pin0InfoVect1LinkObjId="g_427afb0_0" Pin0InfoVect2LinkObjId="SW-216870_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_454b990_0" Pin1InfoVect1LinkObjId="g_454b990_0" Pin1InfoVect2LinkObjId="SW-216868_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="95,-299 95,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4548570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="95,-485 95,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32248@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_2d47ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="95,-485 95,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3733b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="508,-149 508,-164 472,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_3b4cad0@0" ObjectIDZND0="32245@x" ObjectIDZND1="37639@x" Pin0InfoVect0LinkObjId="SW-216846_0" Pin0InfoVect1LinkObjId="EC-DY_LJW.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3b4cad0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="508,-149 508,-164 472,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3733d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-182 472,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="32245@0" ObjectIDZND0="g_3b4cad0@0" ObjectIDZND1="37639@x" Pin0InfoVect0LinkObjId="g_3b4cad0_0" Pin0InfoVect1LinkObjId="EC-DY_LJW.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="472,-182 472,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ef0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-164 472,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32245@x" ObjectIDND1="g_3b4cad0@0" ObjectIDZND0="37639@0" Pin0InfoVect0LinkObjId="EC-DY_LJW.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216846_0" Pin1InfoVect1LinkObjId="g_3b4cad0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-164 472,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_378b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-246 472,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_3ef0f50@0" ObjectIDZND0="32245@1" Pin0InfoVect0LinkObjId="SW-216846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3ef0f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-246 472,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c6cd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="421,-319 421,-352 471,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_4558410@0" ObjectIDZND0="32244@x" ObjectIDZND1="g_3ef0f50@0" ObjectIDZND2="g_3ef0f50@0" Pin0InfoVect0LinkObjId="SW-216845_0" Pin0InfoVect1LinkObjId="g_3ef0f50_0" Pin0InfoVect2LinkObjId="g_3ef0f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4558410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="421,-319 421,-352 471,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d8eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="545,-282 545,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32246@0" ObjectIDZND0="g_4551f90@0" Pin0InfoVect0LinkObjId="g_4551f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216847_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="545,-282 545,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d8f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-467 472,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32243@1" ObjectIDZND0="32242@1" Pin0InfoVect0LinkObjId="SW-216844_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216845_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-467 472,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41bbab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-429 472,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32242@0" ObjectIDZND0="32244@1" Pin0InfoVect0LinkObjId="SW-216845_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216844_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-429 472,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41bbd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-399 472,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="32244@0" ObjectIDZND0="g_4558410@0" ObjectIDZND1="g_3ef0f50@0" ObjectIDZND2="g_3ef0f50@0" Pin0InfoVect0LinkObjId="g_4558410_0" Pin0InfoVect1LinkObjId="g_3ef0f50_0" Pin0InfoVect2LinkObjId="g_3ef0f50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="472,-399 472,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41bbf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-352 545,-352 545,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="32244@x" ObjectIDND1="g_4558410@0" ObjectIDND2="g_3ef0f50@0" ObjectIDZND0="32246@1" Pin0InfoVect0LinkObjId="SW-216847_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216845_0" Pin1InfoVect1LinkObjId="g_4558410_0" Pin1InfoVect2LinkObjId="g_3ef0f50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-352 545,-352 545,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d62750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-301 472,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="32244@x" ObjectIDND1="g_4558410@0" ObjectIDND2="32246@x" ObjectIDZND0="g_3ef0f50@1" Pin0InfoVect0LinkObjId="g_3ef0f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216845_0" Pin1InfoVect1LinkObjId="g_4558410_0" Pin1InfoVect2LinkObjId="SW-216847_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-301 472,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d629b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_3ef0f50@0" ObjectIDND1="32244@x" ObjectIDND2="g_4558410@0" ObjectIDZND0="g_3ef0f50@0" ObjectIDZND1="32244@x" ObjectIDZND2="g_4558410@0" Pin0InfoVect0LinkObjId="g_3ef0f50_0" Pin0InfoVect1LinkObjId="SW-216845_0" Pin0InfoVect2LinkObjId="g_4558410_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ef0f50_0" Pin1InfoVect1LinkObjId="SW-216845_0" Pin1InfoVect2LinkObjId="g_4558410_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="472,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d62c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-298 472,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3ef0f50@0" ObjectIDND1="g_3ef0f50@0" ObjectIDND2="32244@x" ObjectIDZND0="32244@x" ObjectIDZND1="g_4558410@0" ObjectIDZND2="32246@x" Pin0InfoVect0LinkObjId="SW-216845_0" Pin0InfoVect1LinkObjId="g_4558410_0" Pin0InfoVect2LinkObjId="SW-216847_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3ef0f50_0" Pin1InfoVect1LinkObjId="g_3ef0f50_0" Pin1InfoVect2LinkObjId="SW-216845_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="472,-298 472,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d7a010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="472,-484 472,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32243@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_2d47ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216845_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="472,-484 472,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d74230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-153 902,-168 866,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_338e910@0" ObjectIDZND0="32240@x" ObjectIDZND1="37638@x" Pin0InfoVect0LinkObjId="SW-216823_0" Pin0InfoVect1LinkObjId="EC-DY_LJW.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_338e910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-153 902,-168 866,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d74490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-186 866,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="32240@0" ObjectIDZND0="37638@x" ObjectIDZND1="g_338e910@0" Pin0InfoVect0LinkObjId="EC-DY_LJW.032Ld_0" Pin0InfoVect1LinkObjId="g_338e910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216823_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="866,-186 866,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_494ac90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-168 866,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="32240@x" ObjectIDND1="g_338e910@0" ObjectIDZND0="37638@0" Pin0InfoVect0LinkObjId="EC-DY_LJW.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216823_0" Pin1InfoVect1LinkObjId="g_338e910_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-168 866,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bbba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-250 866,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_44a9eb0@0" ObjectIDZND0="32240@1" Pin0InfoVect0LinkObjId="SW-216823_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_44a9eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-250 866,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3c46150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="815,-323 815,-356 865,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="g_2ca31e0@0" ObjectIDZND0="32239@x" ObjectIDZND1="g_44a9eb0@0" ObjectIDZND2="g_44a9eb0@0" Pin0InfoVect0LinkObjId="SW-216822_0" Pin0InfoVect1LinkObjId="g_44a9eb0_0" Pin0InfoVect2LinkObjId="g_44a9eb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ca31e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="815,-323 815,-356 865,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4107c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="939,-286 939,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="32241@0" ObjectIDZND0="g_3c463b0@0" Pin0InfoVect0LinkObjId="g_3c463b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216824_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="939,-286 939,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd0690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-471 866,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32238@1" ObjectIDZND0="32237@1" Pin0InfoVect0LinkObjId="SW-216821_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216822_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-471 866,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd08f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-433 866,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="32237@0" ObjectIDZND0="32239@1" Pin0InfoVect0LinkObjId="SW-216822_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216821_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-433 866,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd0b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-403 866,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="32239@0" ObjectIDZND0="g_2ca31e0@0" ObjectIDZND1="g_44a9eb0@0" ObjectIDZND2="g_44a9eb0@0" Pin0InfoVect0LinkObjId="g_2ca31e0_0" Pin0InfoVect1LinkObjId="g_44a9eb0_0" Pin0InfoVect2LinkObjId="g_44a9eb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-403 866,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3bd0db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-356 939,-356 939,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="32239@x" ObjectIDND1="g_2ca31e0@0" ObjectIDND2="g_44a9eb0@0" ObjectIDZND0="32241@1" Pin0InfoVect0LinkObjId="SW-216824_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216822_0" Pin1InfoVect1LinkObjId="g_2ca31e0_0" Pin1InfoVect2LinkObjId="g_44a9eb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-356 939,-356 939,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a4890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-305 866,-289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="32239@x" ObjectIDND1="g_2ca31e0@0" ObjectIDND2="32241@x" ObjectIDZND0="g_44a9eb0@1" Pin0InfoVect0LinkObjId="g_44a9eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-216822_0" Pin1InfoVect1LinkObjId="g_2ca31e0_0" Pin1InfoVect2LinkObjId="SW-216824_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-305 866,-289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a4ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-305 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_44a9eb0@0" ObjectIDND1="32239@x" ObjectIDND2="g_2ca31e0@0" ObjectIDZND0="g_44a9eb0@0" ObjectIDZND1="32239@x" ObjectIDZND2="g_2ca31e0@0" Pin0InfoVect0LinkObjId="g_44a9eb0_0" Pin0InfoVect1LinkObjId="SW-216822_0" Pin0InfoVect2LinkObjId="g_2ca31e0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_44a9eb0_0" Pin1InfoVect1LinkObjId="SW-216822_0" Pin1InfoVect2LinkObjId="g_2ca31e0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-305 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a4d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-302 866,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_44a9eb0@0" ObjectIDND1="g_44a9eb0@0" ObjectIDND2="32239@x" ObjectIDZND0="32239@x" ObjectIDZND1="g_2ca31e0@0" ObjectIDZND2="32241@x" Pin0InfoVect0LinkObjId="SW-216822_0" Pin0InfoVect1LinkObjId="g_2ca31e0_0" Pin0InfoVect2LinkObjId="SW-216824_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_44a9eb0_0" Pin1InfoVect1LinkObjId="g_44a9eb0_0" Pin1InfoVect2LinkObjId="SW-216822_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-302 866,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_41a4f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-488 866,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32238@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_2d47ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216822_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-488 866,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cf9710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-423 1201,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="32261@0" ObjectIDZND0="g_4190270@0" ObjectIDZND1="g_2dc1d10@0" Pin0InfoVect0LinkObjId="g_4190270_0" Pin0InfoVect1LinkObjId="g_2dc1d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216919_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-423 1201,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_427d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-508 1201,-527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="32260@0" ObjectIDZND0="32219@0" Pin0InfoVect0LinkObjId="g_2d47ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216919_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-508 1201,-527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca4ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-634 425,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32234@1" ObjectIDZND0="32231@1" Pin0InfoVect0LinkObjId="SW-216771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-634 425,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3ca5150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="425,-576 425,-593 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="32233@1" ObjectIDZND0="32231@0" Pin0InfoVect0LinkObjId="SW-216771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216773_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="425,-576 425,-593 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_454b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="990,-595 990,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32259@1" ObjectIDZND0="32258@1" Pin0InfoVect0LinkObjId="SW-216912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="990,-595 990,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3d51360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1201,-491 1201,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="32260@1" ObjectIDZND0="32261@1" Pin0InfoVect0LinkObjId="SW-216919_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1201,-491 1201,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_454f500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-202 -192,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="32263@0" ObjectIDZND0="32257@x" ObjectIDZND1="32255@x" Pin0InfoVect0LinkObjId="SW-216894_0" Pin0InfoVect1LinkObjId="SW-216892_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-DY_LJW.DY_LJW_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-202 -192,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_33683b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-233,-246 -233,-234 -192,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="32257@1" ObjectIDZND0="32255@x" ObjectIDZND1="32263@x" Pin0InfoVect0LinkObjId="SW-216892_0" Pin0InfoVect1LinkObjId="CB-DY_LJW.DY_LJW_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-216894_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-233,-246 -233,-234 -192,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_495c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-192,-234 -192,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="32257@x" ObjectIDND1="32263@x" ObjectIDZND0="32255@0" Pin0InfoVect0LinkObjId="SW-216892_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-216894_0" Pin1InfoVect1LinkObjId="CB-DY_LJW.DY_LJW_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-192,-234 -192,-248 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="32218" cx="880" cy="-986" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32218" cx="111" cy="-986" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="95" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="472" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="866" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="425" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="990" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="32219" cx="1201" cy="-527" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-216572" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -468.000000 -1262.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32199" ObjectName="DYN-DY_LJW"/>
     <cge:Meas_Ref ObjectId="216572"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_419d8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -768.000000 -1264.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_41fdc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -627.000000 -1403.500000) translate(0,16)">陆家湾变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_3d65cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -710.000000 -662.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4197ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.096525 -236.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_45843f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 75.000000 -81.000000) translate(0,12)">湾陆联络线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_455bc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.096525 -748.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44efff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 845.096525 -1268.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_449d190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -296.000000 -1010.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_419f3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -299.000000 -550.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_416f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 336.000000 -829.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41bb8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 90.000000 -1412.000000) translate(0,12)">湾陆线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2d5f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -797.000000) translate(0,16)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2d5f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -797.000000) translate(0,35)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2d5f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -797.000000) translate(0,54)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="19" graphid="g_2d5f630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 185.000000 -797.000000) translate(0,73)">7%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_41a2250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -483.000000 -1384.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3811120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -483.000000 -1419.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2de1d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 -1117.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3733ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 -80.000000) translate(0,12)">丙海线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d746f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 846.000000 -84.000000) translate(0,12)">瓦窑线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c427c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -1119.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_452d8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -1035.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_452daa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 142.000000 -1103.000000) translate(0,12)">33117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_452dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 22.000000 -1175.000000) translate(0,12)">33160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3716f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -1201.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3717190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 23.000000 -1257.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37173d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 145.000000 -1374.000000) translate(0,12)">3319</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3717610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 887.000000 -1075.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4e670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 910.000000 -1049.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4e8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 807.000000 -1170.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4eaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 -879.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d4ed30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 435.000000 -953.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c3f930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 482.000000 -933.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ca53b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 434.000000 -614.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_454b400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -586.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c71bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -184.000000 -467.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 104.000000 -451.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c72130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 175.000000 -308.000000) translate(0,12)">03467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41b47b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -208.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41b49c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -185.000000 -273.000000) translate(0,12)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41b4c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -112.000000 -324.000000) translate(0,12)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e2e070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -320.000000 -271.000000) translate(0,12)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4284c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -450.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4284e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 552.000000 -307.000000) translate(0,12)">03367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4285090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 479.000000 -207.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42852d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -454.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4285510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 946.000000 -311.000000) translate(0,12)">03267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_44e3b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 873.000000 -211.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e04a0" transform="matrix(1.000000 -0.000000 -0.000000 0.865385 -308.500000 -42.557692) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e0720" transform="matrix(1.000000 -0.000000 -0.000000 0.865385 -283.000000 -26.980769) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_345c170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -811.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_345c890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.500000 -788.000000) translate(0,12)">油温(℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4935440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -467.500000 -1338.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33633b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -271.000000 -68.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_3ec9e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -698.000000 -912.000000) translate(0,20)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="22" graphid="g_3b7b000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -758.000000 -269.000000) translate(0,18)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_37642d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -604.000000 -279.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_37642d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -604.000000 -279.500000) translate(0,38)">13987885824</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-484" y="-1349"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="24" stroke="rgb(0,255,0)" stroke-width="1" width="10" x="1196" y="-477"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-DY_LJW.DY_LJW_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-300,-986 1346,-986 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32218" ObjectName="BS-DY_LJW.DY_LJW_3IM"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   <polyline fill="none" opacity="0" points="-300,-986 1346,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-DY_LJW.DY_LJW_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-301,-527 1346,-527 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="32219" ObjectName="BS-DY_LJW.DY_LJW_9IM"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   <polyline fill="none" opacity="0" points="-301,-527 1346,-527 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-DY_LJW.DY_LJW_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="46590"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.418267 -748.000000)" xlink:href="#transformer2:shape14_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.418267 -748.000000)" xlink:href="#transformer2:shape14_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="32262" ObjectName="TF-DY_LJW.DY_LJW_1T"/>
    <cge:TPSR_Ref TObjectID="32262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 -1105.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 -1105.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1186.000000 -248.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1186.000000 -248.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -677.000000 -1340.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -695.000000 -1261.000000) translate(0,15)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-216651" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -650.000000 -1219.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216651" ObjectName="DY_LJW:DY_LJW_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-216652" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -650.000000 -1177.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216652" ObjectName="DY_LJW:DY_LJW_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-216653" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -871.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216653" ObjectName="DY_LJW:DY_LJW_301BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-216665" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 357.000000 -586.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216665" ObjectName="DY_LJW:DY_LJW_001BK_Cos"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-494" y="-1392"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-494" y="-1392"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-665" y="-1416"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-665" y="-1416"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-714" y="-1433"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-714" y="-1433"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-494" y="-1427"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-494" y="-1427"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="121" y="-1119"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="121" y="-1119"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="336" y="-829"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="336" y="-829"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="-184" y="-467"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="-184" y="-467"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="104" y="-451"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="104" y="-451"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="481" y="-450"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="481" y="-450"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="875" y="-454"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="875" y="-454"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-484" y="-1350"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-484" y="-1350"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="101" x="-701" y="-913"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="101" x="-701" y="-913"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 1.000000 -188.229358)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddcc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 1067.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_325bf30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1120.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3cb9920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 1015.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4271380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1101.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41fe1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 1050.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_421cd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1084.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_490a1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 1032.000000) translate(0,12)">Ubc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.825688 -2.000000 288.770642)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_335ba90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.000000 1067.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cee70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1120.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cf040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 1015.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cf250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1101.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_41cf460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 1050.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4560430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1223.000000 1084.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4560600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 1032.000000) translate(0,12)">Ubc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 185.000000 913.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d67970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c562b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36dfda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 157.000000 -157.096154)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_427be00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_427c0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_427c2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d51760" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 278.000000 886.980769) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3407b20" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 264.000000 919.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3407d60" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 282.000000 870.980769) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3408640" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 252.500000 902.557692) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_420c990" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 280.500000 615.557692) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_420cc00" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 306.000000 599.980769) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d35840" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 292.000000 632.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d35a80" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 312.000000 585.980769) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 588.000000 912.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c84c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c850c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c85300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -0.865385 982.000000 910.903846)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3c85630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -199.500000 1096.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b7ab80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -174.000000 1078.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b7adc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -188.000000 1115.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-DY_LJW.DY_LJW_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -217.000000 -93.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32263" ObjectName="CB-DY_LJW.DY_LJW_Cb1"/>
    <cge:TPSR_Ref TObjectID="32263"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-216636" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-216637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-216638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-216642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-216639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-216640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-216643" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1282.000000 -1115.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32218"/>
     <cge:Term_Ref ObjectID="46502"/>
    <cge:TPSR_Ref TObjectID="32218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-216666" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-216667" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-216668" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216668" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-216672" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-216669" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-216670" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216670" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-216673" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -638.000000) translate(0,102)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216673" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32219"/>
     <cge:Term_Ref ObjectID="46503"/>
    <cge:TPSR_Ref TObjectID="32219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 30.000000 -1121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32221"/>
     <cge:Term_Ref ObjectID="46506"/>
    <cge:TPSR_Ref TObjectID="32221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216634" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 30.000000 -1121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32221"/>
     <cge:Term_Ref ObjectID="46506"/>
    <cge:TPSR_Ref TObjectID="32221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216630" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 30.000000 -1121.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32221"/>
     <cge:Term_Ref ObjectID="46506"/>
    <cge:TPSR_Ref TObjectID="32221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216651" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -919.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32232"/>
     <cge:Term_Ref ObjectID="46528"/>
    <cge:TPSR_Ref TObjectID="32232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216652" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -919.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32232"/>
     <cge:Term_Ref ObjectID="46528"/>
    <cge:TPSR_Ref TObjectID="32232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216648" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 327.000000 -919.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32232"/>
     <cge:Term_Ref ObjectID="46528"/>
    <cge:TPSR_Ref TObjectID="32232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216663" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -629.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216663" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32231"/>
     <cge:Term_Ref ObjectID="46526"/>
    <cge:TPSR_Ref TObjectID="32231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216664" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -629.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32231"/>
     <cge:Term_Ref ObjectID="46526"/>
    <cge:TPSR_Ref TObjectID="32231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216660" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 355.000000 -629.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216660" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32231"/>
     <cge:Term_Ref ObjectID="46526"/>
    <cge:TPSR_Ref TObjectID="32231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216695" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -42.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32252"/>
     <cge:Term_Ref ObjectID="46568"/>
    <cge:TPSR_Ref TObjectID="32252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216692" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 -42.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32252"/>
     <cge:Term_Ref ObjectID="46568"/>
    <cge:TPSR_Ref TObjectID="32252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216689" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32247"/>
     <cge:Term_Ref ObjectID="46558"/>
    <cge:TPSR_Ref TObjectID="32247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216690" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32247"/>
     <cge:Term_Ref ObjectID="46558"/>
    <cge:TPSR_Ref TObjectID="32247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216686" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216686" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32247"/>
     <cge:Term_Ref ObjectID="46558"/>
    <cge:TPSR_Ref TObjectID="32247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216683" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 462.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32242"/>
     <cge:Term_Ref ObjectID="46548"/>
    <cge:TPSR_Ref TObjectID="32242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216684" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 462.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32242"/>
     <cge:Term_Ref ObjectID="46548"/>
    <cge:TPSR_Ref TObjectID="32242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216680" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 462.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32242"/>
     <cge:Term_Ref ObjectID="46548"/>
    <cge:TPSR_Ref TObjectID="32242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-216677" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -52.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216677" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32237"/>
     <cge:Term_Ref ObjectID="46538"/>
    <cge:TPSR_Ref TObjectID="32237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-216678" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -52.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216678" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32237"/>
     <cge:Term_Ref ObjectID="46538"/>
    <cge:TPSR_Ref TObjectID="32237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-216674" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 859.000000 -52.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216674" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32237"/>
     <cge:Term_Ref ObjectID="46538"/>
    <cge:TPSR_Ref TObjectID="32237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-216654" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.188119 -0.000000 -0.000000 1.333333 535.500000 -811.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32262"/>
     <cge:Term_Ref ObjectID="46591"/>
    <cge:TPSR_Ref TObjectID="32262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-216655" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.188119 -0.000000 -0.000000 1.333333 535.500000 -811.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="216655" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="32262"/>
     <cge:Term_Ref ObjectID="46591"/>
    <cge:TPSR_Ref TObjectID="32262"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-216774">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 419.241796 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32235" ObjectName="SW-DY_LJW.DY_LJW_3011SW"/>
     <cge:Meas_Ref ObjectId="216774"/>
    <cge:TPSR_Ref TObjectID="32235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -201.238710 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32255" ObjectName="SW-DY_LJW.DY_LJW_0356SW"/>
     <cge:Meas_Ref ObjectId="216892"/>
    <cge:TPSR_Ref TObjectID="32255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216869">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.761290 -178.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32250" ObjectName="SW-DY_LJW.DY_LJW_0346SW"/>
     <cge:Meas_Ref ObjectId="216869"/>
    <cge:TPSR_Ref TObjectID="32250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216764">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.881701 -1045.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32228" ObjectName="SW-DY_LJW.DY_LJW_3901SW"/>
     <cge:Meas_Ref ObjectId="216764"/>
    <cge:TPSR_Ref TObjectID="32228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216766">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.881701 -1140.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32230" ObjectName="SW-DY_LJW.DY_LJW_39017SW"/>
     <cge:Meas_Ref ObjectId="216766"/>
    <cge:TPSR_Ref TObjectID="32230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216738">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.544703 -1072.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32225" ObjectName="SW-DY_LJW.DY_LJW_33117SW"/>
     <cge:Meas_Ref ObjectId="216738"/>
    <cge:TPSR_Ref TObjectID="32225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216735">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 101.881701 -1005.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32222" ObjectName="SW-DY_LJW.DY_LJW_3311SW"/>
     <cge:Meas_Ref ObjectId="216735"/>
    <cge:TPSR_Ref TObjectID="32222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216736">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 101.881701 -1171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32223" ObjectName="SW-DY_LJW.DY_LJW_3316SW"/>
     <cge:Meas_Ref ObjectId="216736"/>
    <cge:TPSR_Ref TObjectID="32223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 229.203031 -1213.000000)" xlink:href="#switch2:shape36_1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216740">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 19.544703 -1226.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32227" ObjectName="SW-DY_LJW.DY_LJW_33167SW"/>
     <cge:Meas_Ref ObjectId="216740"/>
    <cge:TPSR_Ref TObjectID="32227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 18.544703 -1144.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32226" ObjectName="SW-DY_LJW.DY_LJW_33160SW"/>
     <cge:Meas_Ref ObjectId="216739"/>
    <cge:TPSR_Ref TObjectID="32226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216765">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.544703 -1018.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32229" ObjectName="SW-DY_LJW.DY_LJW_39010SW"/>
     <cge:Meas_Ref ObjectId="216765"/>
    <cge:TPSR_Ref TObjectID="32229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216775">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 478.544703 -902.000000)" xlink:href="#switch2:shape30_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32236" ObjectName="SW-DY_LJW.DY_LJW_30117SW"/>
     <cge:Meas_Ref ObjectId="216775"/>
    <cge:TPSR_Ref TObjectID="32236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216737">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 -1343.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32224" ObjectName="SW-DY_LJW.DY_LJW_3319SW"/>
     <cge:Meas_Ref ObjectId="216737"/>
    <cge:TPSR_Ref TObjectID="32224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216891">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 -477.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32253" ObjectName="SW-DY_LJW.DY_LJW_035XC"/>
     <cge:Meas_Ref ObjectId="216891"/>
    <cge:TPSR_Ref TObjectID="32253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216891">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 -409.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32254" ObjectName="SW-DY_LJW.DY_LJW_035XC1"/>
     <cge:Meas_Ref ObjectId="216891"/>
    <cge:TPSR_Ref TObjectID="32254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216893">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -128.238710 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32256" ObjectName="SW-DY_LJW.DY_LJW_03560SW"/>
     <cge:Meas_Ref ObjectId="216893"/>
    <cge:TPSR_Ref TObjectID="32256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 -461.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32248" ObjectName="SW-DY_LJW.DY_LJW_034XC"/>
     <cge:Meas_Ref ObjectId="216868"/>
    <cge:TPSR_Ref TObjectID="32248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 -393.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32249" ObjectName="SW-DY_LJW.DY_LJW_034XC1"/>
     <cge:Meas_Ref ObjectId="216868"/>
    <cge:TPSR_Ref TObjectID="32249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216870">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 158.761290 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32251" ObjectName="SW-DY_LJW.DY_LJW_03467SW"/>
     <cge:Meas_Ref ObjectId="216870"/>
    <cge:TPSR_Ref TObjectID="32251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -460.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32243" ObjectName="SW-DY_LJW.DY_LJW_033XC"/>
     <cge:Meas_Ref ObjectId="216845"/>
    <cge:TPSR_Ref TObjectID="32243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216845">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -392.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32244" ObjectName="SW-DY_LJW.DY_LJW_033XC1"/>
     <cge:Meas_Ref ObjectId="216845"/>
    <cge:TPSR_Ref TObjectID="32244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216847">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.761290 -277.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32246" ObjectName="SW-DY_LJW.DY_LJW_03367SW"/>
     <cge:Meas_Ref ObjectId="216847"/>
    <cge:TPSR_Ref TObjectID="32246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.761290 -177.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32245" ObjectName="SW-DY_LJW.DY_LJW_0336SW"/>
     <cge:Meas_Ref ObjectId="216846"/>
    <cge:TPSR_Ref TObjectID="32245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -464.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32238" ObjectName="SW-DY_LJW.DY_LJW_032XC"/>
     <cge:Meas_Ref ObjectId="216822"/>
    <cge:TPSR_Ref TObjectID="32238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216822">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -396.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32239" ObjectName="SW-DY_LJW.DY_LJW_032XC1"/>
     <cge:Meas_Ref ObjectId="216822"/>
    <cge:TPSR_Ref TObjectID="32239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216824">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 929.761290 -281.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32241" ObjectName="SW-DY_LJW.DY_LJW_03267SW"/>
     <cge:Meas_Ref ObjectId="216824"/>
    <cge:TPSR_Ref TObjectID="32241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216823">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.761290 -181.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32240" ObjectName="SW-DY_LJW.DY_LJW_0326SW"/>
     <cge:Meas_Ref ObjectId="216823"/>
    <cge:TPSR_Ref TObjectID="32240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 -627.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32234" ObjectName="SW-DY_LJW.DY_LJW_001XC1"/>
     <cge:Meas_Ref ObjectId="216773"/>
    <cge:TPSR_Ref TObjectID="32234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 -552.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32233" ObjectName="SW-DY_LJW.DY_LJW_001XC"/>
     <cge:Meas_Ref ObjectId="216773"/>
    <cge:TPSR_Ref TObjectID="32233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.000000 -588.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32259" ObjectName="SW-DY_LJW.DY_LJW_0901XC1"/>
     <cge:Meas_Ref ObjectId="216912"/>
    <cge:TPSR_Ref TObjectID="32259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 980.000000 -536.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32258" ObjectName="SW-DY_LJW.DY_LJW_0901XC"/>
     <cge:Meas_Ref ObjectId="216912"/>
    <cge:TPSR_Ref TObjectID="32258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216894">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -225.500000 -241.500000)" xlink:href="#switch2:shape24_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32257" ObjectName="SW-DY_LJW.DY_LJW_03567SW"/>
     <cge:Meas_Ref ObjectId="216894"/>
    <cge:TPSR_Ref TObjectID="32257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -484.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32260" ObjectName="SW-DY_LJW.DY_LJW_0201XC"/>
     <cge:Meas_Ref ObjectId="216919"/>
    <cge:TPSR_Ref TObjectID="32260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-216919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -416.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="32261" ObjectName="SW-DY_LJW.DY_LJW_0201XC1"/>
     <cge:Meas_Ref ObjectId="216919"/>
    <cge:TPSR_Ref TObjectID="32261"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_大姚.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-494" y="-1392"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-665" y="-1416"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-714" y="-1433"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-494" y="-1427"/></g>
   <g href="35kV陆家湾变DY_LJW_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="121" y="-1119"/></g>
   <g href="35kV陆家湾变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="336" y="-829"/></g>
   <g href="35kV陆家湾变DY_LJW_035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="-184" y="-467"/></g>
   <g href="35kV陆家湾变DY_LJW_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="104" y="-451"/></g>
   <g href="35kV陆家湾变DY_LJW_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="481" y="-450"/></g>
   <g href="35kV陆家湾变DY_LJW_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="875" y="-454"/></g>
   <g href="AVC陆家湾站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-484" y="-1350"/></g>
   <g href="35kV陆家湾变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="101" x="-701" y="-913"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2dd1bc0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 123.676442 -96.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44f5a60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1029.000000 -659.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4522050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -1139.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_35facd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 926.000000 -1160.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3916e30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 69.000000 -1343.716049)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_456b5c0">
    <use class="BV-10KV" transform="matrix(0.777778 -0.000000 0.000000 -0.904762 983.000000 -642.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41a6320">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 -683.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_41dd780">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 494.676442 -599.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2fb4390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -202.000000 -296.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_454b990">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 85.000000 -242.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4190270">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1269.676442 -343.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dc1d10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1191.000000 -348.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_418f3f0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 -320.323558 -283.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_427afb0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 36.676442 -266.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3b4cad0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 500.676442 -95.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3ef0f50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 462.000000 -241.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4558410">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 413.676442 -265.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_44a9eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 856.000000 -245.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ca31e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 807.676442 -269.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_338e910">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 894.676442 -99.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="DY_LJW"/>
</svg>