<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="" aopId="0" id="thSvg" viewBox="4126 -2972 4209 2974">
 
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    
   </symbol>
   <symbol id="Tag:shape27">
    
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(127,127,127);fill:none}
.BKBV-0KV { stroke:rgb(127,127,127);fill:rgb(127,127,127)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(0,72,216);fill:none}
.BKBV-10KV { stroke:rgb(0,72,216);fill:rgb(0,72,216)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(0,255,0);fill:none}
.BKBV-20KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(213,0,0);fill:none}
.BKBV-110KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-220KV { stroke:rgb(255,0,255);fill:none}
.BKBV-220KV { stroke:rgb(255,0,255);fill:rgb(255,0,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(255,0,0);fill:none}
.BKBV-500KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-750KV { stroke:rgb(255,0,0);fill:none}
.BKBV-750KV { stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.BV-22KV { stroke:rgb(255,255,255);fill:none}
.BKBV-22KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-380KV { stroke:rgb(255,255,255);fill:none}
.BKBV-380KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="2984" width="4219" x="4121" y="-2977"/>
  </g><g id="ArcThreePoints_Layer">
   <polyline DF8003:Layer="主干线" fill="none" points="4708,-2017 4709,-2017 4710,-2017 4711,-2017 4713,-2018 4714,-2018 4714,-2019 4715,-2020 4716,-2021 4717,-2022 4717,-2023 4718,-2024 4718,-2025 4718,-2026 4718,-2027 4718,-2028 4717,-2029 4717,-2030 4716,-2031 4715,-2032 4714,-2033 4714,-2034 4713,-2034 4711,-2035 4710,-2035 4709,-2035 4708,-2035 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4708,-2035 4709,-2035 4710,-2035 4711,-2035 4713,-2036 4714,-2036 4714,-2037 4715,-2038 4716,-2039 4717,-2040 4717,-2041 4718,-2042 4718,-2043 4718,-2044 4718,-2045 4718,-2046 4717,-2047 4717,-2048 4716,-2049 4715,-2050 4714,-2051 4714,-2052 4713,-2052 4711,-2053 4710,-2053 4709,-2053 4708,-2053 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5225,-1680 5225,-1679 5225,-1678 5225,-1677 5226,-1676 5227,-1675 5227,-1674 5228,-1673 5229,-1672 5230,-1672 5231,-1671 5232,-1671 5233,-1671 5234,-1671 5235,-1671 5236,-1671 5237,-1671 5238,-1672 5239,-1672 5240,-1673 5241,-1674 5242,-1675 5242,-1676 5243,-1677 5243,-1678 5243,-1679 5243,-1680 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5243,-1680 5243,-1679 5243,-1678 5243,-1677 5244,-1676 5245,-1675 5245,-1674 5246,-1673 5247,-1672 5248,-1672 5249,-1671 5250,-1671 5251,-1671 5252,-1671 5253,-1671 5254,-1671 5255,-1671 5256,-1672 5257,-1672 5258,-1673 5259,-1674 5260,-1675 5260,-1676 5261,-1677 5261,-1678 5261,-1679 5261,-1680 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5046,-1624 5047,-1624 5048,-1624 5049,-1624 5050,-1625 5051,-1626 5052,-1626 5053,-1627 5054,-1628 5054,-1629 5055,-1630 5055,-1631 5055,-1632 5055,-1633 5055,-1634 5055,-1635 5055,-1636 5054,-1637 5054,-1638 5053,-1639 5052,-1640 5051,-1641 5050,-1641 5049,-1642 5048,-1642 5047,-1642 5046,-1642 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5046,-1642 5047,-1642 5048,-1642 5049,-1642 5050,-1643 5051,-1644 5052,-1644 5053,-1645 5054,-1646 5054,-1647 5055,-1648 5055,-1649 5055,-1650 5055,-1651 5055,-1652 5055,-1653 5055,-1654 5054,-1655 5054,-1656 5053,-1657 5052,-1658 5051,-1659 5050,-1659 5049,-1660 5048,-1660 5047,-1660 5046,-1660 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5488,-1581 5488,-1580 5488,-1579 5489,-1578 5489,-1577 5490,-1576 5491,-1575 5492,-1574 5493,-1574 5494,-1573 5495,-1573 5496,-1573 5497,-1573 5498,-1573 5499,-1573 5500,-1573 5501,-1574 5502,-1574 5503,-1575 5504,-1576 5505,-1577 5505,-1578 5506,-1579 5506,-1580 5506,-1581 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5506,-1581 5506,-1580 5506,-1579 5507,-1578 5507,-1577 5508,-1576 5509,-1575 5510,-1574 5511,-1574 5512,-1573 5513,-1573 5514,-1573 5515,-1573 5516,-1573 5517,-1573 5518,-1573 5519,-1574 5520,-1574 5521,-1575 5522,-1576 5523,-1577 5523,-1578 5524,-1579 5524,-1580 5524,-1581 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5506,-1173 5506,-1172 5506,-1171 5506,-1170 5507,-1169 5508,-1168 5508,-1167 5509,-1166 5510,-1165 5511,-1165 5512,-1164 5513,-1164 5514,-1164 5515,-1164 5516,-1164 5517,-1164 5518,-1164 5519,-1165 5520,-1165 5521,-1166 5522,-1167 5523,-1168 5523,-1169 5524,-1170 5524,-1171 5524,-1172 5524,-1173 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5524,-1173 5524,-1172 5524,-1171 5524,-1170 5525,-1169 5526,-1168 5526,-1167 5527,-1166 5528,-1165 5529,-1165 5530,-1164 5531,-1164 5532,-1164 5533,-1164 5534,-1164 5535,-1164 5536,-1164 5537,-1165 5538,-1165 5539,-1166 5540,-1167 5541,-1168 5541,-1169 5542,-1170 5542,-1171 5542,-1172 5542,-1173 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5481,-887 5481,-886 5481,-885 5481,-884 5482,-882 5482,-881 5483,-881 5484,-880 5485,-879 5486,-878 5487,-878 5488,-877 5489,-877 5490,-877 5491,-877 5492,-877 5493,-878 5494,-878 5495,-879 5496,-880 5497,-881 5498,-881 5498,-882 5499,-884 5499,-885 5499,-886 5499,-887 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5499,-887 5499,-886 5499,-885 5499,-883 5499,-882 5500,-881 5500,-880 5501,-879 5502,-878 5503,-878 5504,-877 5505,-877 5506,-876 5507,-876 5509,-876 5510,-876 5511,-877 5512,-877 5513,-878 5514,-878 5515,-879 5516,-880 5516,-881 5517,-882 5517,-883 5517,-885 5517,-886 5517,-887 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5394,-887 5394,-886 5394,-885 5394,-884 5395,-882 5395,-881 5396,-881 5397,-880 5398,-879 5399,-878 5400,-878 5401,-877 5402,-877 5403,-877 5404,-877 5405,-877 5406,-878 5407,-878 5408,-879 5409,-880 5410,-881 5411,-881 5411,-882 5412,-884 5412,-885 5412,-886 5412,-887 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5412,-887 5412,-886 5412,-885 5412,-883 5412,-882 5413,-881 5413,-880 5414,-879 5415,-878 5416,-878 5417,-877 5418,-877 5419,-876 5420,-876 5422,-876 5423,-876 5424,-877 5425,-877 5426,-878 5427,-878 5428,-879 5429,-880 5429,-881 5430,-882 5430,-883 5430,-885 5430,-886 5430,-887 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="6718,-2011 6719,-2011 6720,-2011 6721,-2011 6723,-2012 6724,-2012 6724,-2013 6725,-2014 6726,-2015 6727,-2016 6727,-2017 6728,-2018 6728,-2019 6728,-2020 6728,-2021 6728,-2022 6727,-2023 6727,-2024 6726,-2025 6725,-2026 6724,-2027 6724,-2028 6723,-2028 6721,-2029 6720,-2029 6719,-2029 6718,-2029 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="6718,-2029 6719,-2029 6720,-2029 6722,-2029 6723,-2029 6724,-2030 6725,-2030 6726,-2031 6727,-2032 6727,-2033 6728,-2034 6728,-2035 6729,-2036 6729,-2037 6729,-2039 6729,-2040 6728,-2041 6728,-2042 6727,-2043 6727,-2044 6726,-2045 6725,-2046 6724,-2046 6723,-2047 6722,-2047 6720,-2047 6719,-2047 6718,-2047 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7814,-799 7815,-799 7816,-799 7817,-799 7818,-800 7819,-801 7820,-801 7821,-802 7822,-803 7822,-804 7823,-805 7823,-806 7823,-807 7823,-808 7823,-809 7823,-810 7823,-811 7822,-812 7822,-813 7821,-814 7820,-815 7819,-816 7818,-816 7817,-817 7816,-817 7815,-817 7814,-817 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7814,-817 7815,-817 7816,-817 7817,-817 7818,-818 7819,-819 7820,-819 7821,-820 7822,-821 7822,-822 7823,-823 7823,-824 7823,-825 7823,-826 7823,-827 7823,-828 7823,-829 7822,-830 7822,-831 7821,-832 7820,-833 7819,-834 7818,-834 7817,-835 7816,-835 7815,-835 7814,-835 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7853,-1674 7853,-1673 7853,-1672 7853,-1671 7854,-1670 7855,-1669 7855,-1668 7856,-1667 7857,-1666 7858,-1666 7859,-1665 7860,-1665 7861,-1665 7862,-1665 7863,-1665 7864,-1665 7865,-1665 7866,-1666 7867,-1666 7868,-1667 7869,-1668 7870,-1669 7870,-1670 7871,-1671 7871,-1672 7871,-1673 7871,-1674 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7871,-1674 7871,-1673 7871,-1672 7871,-1671 7872,-1670 7873,-1669 7873,-1668 7874,-1667 7875,-1666 7876,-1666 7877,-1665 7878,-1665 7879,-1665 7880,-1665 7881,-1665 7882,-1665 7883,-1665 7884,-1666 7885,-1666 7886,-1667 7887,-1668 7888,-1669 7888,-1670 7889,-1671 7889,-1672 7889,-1673 7889,-1674 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8114,-1268 8114,-1267 8114,-1266 8115,-1265 8115,-1264 8116,-1263 8117,-1262 8118,-1261 8119,-1261 8120,-1260 8121,-1260 8122,-1260 8123,-1260 8124,-1260 8125,-1260 8126,-1260 8127,-1261 8128,-1261 8129,-1262 8130,-1263 8131,-1264 8131,-1265 8132,-1266 8132,-1267 8132,-1268 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8132,-1268 8132,-1267 8132,-1266 8132,-1265 8133,-1264 8134,-1263 8134,-1262 8135,-1261 8136,-1260 8137,-1260 8138,-1259 8139,-1259 8140,-1259 8141,-1259 8142,-1259 8143,-1259 8144,-1259 8145,-1260 8146,-1260 8147,-1261 8148,-1262 8149,-1263 8149,-1264 8150,-1265 8150,-1266 8150,-1267 8150,-1268 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7167,-2083 7168,-2083 7169,-2083 7170,-2084 7171,-2084 7172,-2085 7173,-2086 7174,-2087 7174,-2088 7175,-2089 7175,-2090 7175,-2091 7175,-2092 7175,-2093 7175,-2094 7175,-2095 7174,-2096 7174,-2097 7173,-2098 7172,-2099 7171,-2100 7170,-2100 7169,-2101 7168,-2101 7167,-2101 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7167,-2101 7168,-2101 7169,-2101 7170,-2102 7171,-2102 7172,-2103 7173,-2104 7174,-2105 7174,-2106 7175,-2107 7175,-2108 7175,-2109 7175,-2110 7175,-2111 7175,-2112 7175,-2113 7174,-2114 7174,-2115 7173,-2116 7172,-2117 7171,-2118 7170,-2118 7169,-2119 7168,-2119 7167,-2119 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4822,-1090 4823,-1090 4824,-1090 4826,-1090 4827,-1090 4828,-1091 4829,-1091 4830,-1092 4831,-1093 4831,-1094 4832,-1095 4832,-1096 4833,-1097 4833,-1098 4833,-1100 4833,-1101 4832,-1102 4832,-1103 4831,-1104 4831,-1105 4830,-1106 4829,-1107 4828,-1107 4827,-1108 4826,-1108 4824,-1108 4823,-1108 4822,-1108 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4822,-1109 4823,-1109 4824,-1109 4825,-1109 4826,-1109 4828,-1110 4829,-1110 4829,-1111 4830,-1112 4831,-1113 4831,-1114 4832,-1115 4832,-1116 4832,-1117 4832,-1118 4832,-1119 4832,-1120 4831,-1121 4831,-1122 4830,-1123 4829,-1124 4829,-1125 4828,-1125 4826,-1126 4825,-1126 4824,-1126 4823,-1126 4822,-1126 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="6512,-2226 6512,-2225 6512,-2224 6512,-2223 6513,-2222 6513,-2221 6514,-2220 6515,-2219 6515,-2218 6516,-2218 6517,-2217 6518,-2217 6519,-2217 6521,-2217 6522,-2217 6523,-2217 6524,-2217 6525,-2218 6526,-2218 6526,-2219 6527,-2220 6528,-2221 6528,-2222 6529,-2223 6529,-2224 6529,-2225 6529,-2226 6529,-2227 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="6529,-2228 6529,-2227 6529,-2226 6530,-2225 6530,-2224 6531,-2223 6532,-2222 6533,-2221 6534,-2221 6535,-2220 6536,-2220 6537,-2219 6538,-2219 6539,-2219 6540,-2220 6541,-2220 6542,-2220 6543,-2221 6544,-2222 6545,-2223 6546,-2223 6546,-2224 6547,-2226 6547,-2227 6547,-2228 6547,-2229 6547,-2230 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5414,-2163 5414,-2162 5414,-2161 5415,-2160 5415,-2159 5416,-2158 5417,-2157 5418,-2156 5419,-2156 5420,-2155 5421,-2155 5422,-2155 5423,-2155 5424,-2155 5425,-2155 5426,-2155 5427,-2156 5428,-2156 5429,-2157 5430,-2158 5431,-2159 5431,-2160 5432,-2161 5432,-2162 5432,-2163 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5432,-2163 5432,-2162 5432,-2161 5432,-2160 5433,-2159 5434,-2158 5434,-2157 5435,-2156 5436,-2155 5437,-2155 5438,-2154 5439,-2154 5440,-2154 5441,-2154 5442,-2154 5443,-2154 5444,-2154 5445,-2155 5446,-2155 5447,-2156 5448,-2157 5449,-2158 5449,-2159 5450,-2160 5450,-2161 5450,-2162 5450,-2163 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5070,-2061 5071,-2061 5072,-2061 5073,-2061 5074,-2062 5075,-2063 5076,-2063 5077,-2064 5078,-2065 5078,-2066 5079,-2067 5079,-2068 5079,-2069 5079,-2070 5079,-2071 5079,-2072 5079,-2073 5078,-2074 5078,-2075 5077,-2076 5076,-2077 5075,-2078 5074,-2078 5073,-2079 5072,-2079 5071,-2079 5070,-2079 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5070,-2079 5071,-2079 5072,-2079 5073,-2079 5074,-2080 5075,-2081 5076,-2081 5077,-2082 5078,-2083 5078,-2084 5079,-2085 5079,-2086 5079,-2087 5079,-2088 5079,-2089 5079,-2090 5079,-2091 5078,-2092 5078,-2093 5077,-2094 5076,-2095 5075,-2096 5074,-2096 5073,-2097 5072,-2097 5071,-2097 5070,-2097 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5464,-1802 5464,-1801 5464,-1800 5465,-1799 5465,-1798 5466,-1797 5467,-1796 5468,-1795 5469,-1795 5470,-1794 5471,-1794 5472,-1794 5473,-1794 5474,-1794 5475,-1794 5476,-1794 5477,-1795 5478,-1795 5479,-1796 5480,-1797 5481,-1798 5481,-1799 5482,-1800 5482,-1801 5482,-1802 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5482,-1802 5482,-1801 5482,-1800 5482,-1799 5483,-1798 5484,-1797 5484,-1796 5485,-1795 5486,-1794 5487,-1794 5488,-1793 5489,-1793 5490,-1793 5491,-1793 5492,-1793 5493,-1793 5494,-1793 5495,-1794 5496,-1794 5497,-1795 5498,-1796 5499,-1797 5499,-1798 5500,-1799 5500,-1800 5500,-1801 5500,-1802 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4824,-809 4824,-808 4824,-807 4824,-806 4825,-805 4826,-804 4826,-803 4827,-802 4828,-801 4829,-801 4830,-800 4831,-800 4832,-800 4833,-800 4834,-800 4835,-800 4836,-800 4837,-801 4838,-801 4839,-802 4840,-803 4841,-804 4841,-805 4842,-806 4842,-807 4842,-808 4842,-809 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4842,-809 4842,-808 4842,-807 4842,-806 4843,-805 4844,-804 4844,-803 4845,-802 4846,-801 4847,-801 4848,-800 4849,-800 4850,-800 4851,-800 4852,-800 4853,-800 4854,-800 4855,-801 4856,-801 4857,-802 4858,-803 4859,-804 4859,-805 4860,-806 4860,-807 4860,-808 4860,-809 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4806,-826 4807,-826 4808,-826 4809,-827 4810,-827 4811,-828 4812,-829 4813,-830 4813,-831 4814,-832 4814,-833 4814,-834 4814,-835 4814,-836 4814,-837 4814,-838 4813,-839 4813,-840 4812,-841 4811,-842 4810,-843 4809,-843 4808,-844 4807,-844 4806,-844 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4806,-844 4807,-844 4808,-844 4809,-844 4810,-845 4811,-846 4812,-846 4813,-847 4814,-848 4814,-849 4815,-850 4815,-851 4815,-852 4815,-853 4815,-854 4815,-855 4815,-856 4814,-857 4814,-858 4813,-859 4812,-860 4811,-861 4810,-861 4809,-862 4808,-862 4807,-862 4806,-862 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5180,-365 5180,-364 5180,-363 5180,-362 5181,-360 5181,-359 5182,-359 5183,-358 5184,-357 5185,-356 5186,-356 5187,-355 5188,-355 5189,-355 5190,-355 5191,-355 5192,-356 5193,-356 5194,-357 5195,-358 5196,-359 5197,-359 5197,-360 5198,-362 5198,-363 5198,-364 5198,-365 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5199,-365 5199,-364 5199,-363 5199,-362 5199,-361 5200,-359 5200,-358 5201,-358 5202,-357 5203,-356 5204,-356 5205,-355 5206,-355 5207,-355 5208,-355 5209,-355 5210,-355 5211,-356 5212,-356 5213,-357 5214,-358 5215,-358 5215,-359 5216,-361 5216,-362 5216,-363 5216,-364 5216,-365 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5482,-513 5482,-512 5482,-511 5482,-510 5483,-509 5484,-508 5484,-507 5485,-506 5486,-505 5487,-505 5488,-504 5489,-504 5490,-504 5491,-504 5492,-504 5493,-504 5494,-504 5495,-505 5496,-505 5497,-506 5498,-507 5499,-508 5499,-509 5500,-510 5500,-511 5500,-512 5500,-513 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5500,-513 5500,-512 5500,-511 5500,-510 5501,-509 5502,-508 5502,-507 5503,-506 5504,-505 5505,-505 5506,-504 5507,-504 5508,-504 5509,-504 5510,-504 5511,-504 5512,-504 5513,-505 5514,-505 5515,-506 5516,-507 5517,-508 5517,-509 5518,-510 5518,-511 5518,-512 5518,-513 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7400,-1345 7400,-1344 7400,-1343 7400,-1342 7401,-1341 7402,-1340 7402,-1339 7403,-1338 7404,-1337 7405,-1337 7406,-1336 7407,-1336 7408,-1336 7409,-1336 7410,-1336 7411,-1336 7412,-1336 7413,-1337 7414,-1337 7415,-1338 7416,-1339 7417,-1340 7417,-1341 7418,-1342 7418,-1343 7418,-1344 7418,-1345 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7418,-1345 7418,-1344 7418,-1343 7418,-1342 7419,-1341 7420,-1340 7420,-1339 7421,-1338 7422,-1337 7423,-1337 7424,-1336 7425,-1336 7426,-1336 7427,-1336 7428,-1336 7429,-1336 7430,-1336 7431,-1337 7432,-1337 7433,-1338 7434,-1339 7435,-1340 7435,-1341 7436,-1342 7436,-1343 7436,-1344 7436,-1345 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8163,-921 8164,-921 8165,-921 8166,-922 8167,-922 8168,-923 8169,-924 8170,-925 8170,-926 8171,-927 8171,-928 8171,-929 8171,-930 8171,-931 8171,-932 8171,-933 8170,-934 8170,-935 8169,-936 8168,-937 8167,-938 8166,-938 8165,-939 8164,-939 8163,-939 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8163,-939 8164,-939 8165,-939 8166,-940 8167,-940 8168,-941 8169,-942 8170,-943 8170,-944 8171,-945 8171,-946 8171,-947 8171,-948 8171,-949 8171,-950 8171,-951 8170,-952 8170,-953 8169,-954 8168,-955 8167,-956 8166,-956 8165,-957 8164,-957 8163,-957 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8162,-762 8163,-762 8164,-762 8165,-762 8166,-763 8167,-764 8168,-764 8169,-765 8170,-766 8170,-767 8171,-768 8171,-769 8171,-770 8171,-771 8171,-772 8171,-773 8171,-774 8170,-775 8170,-776 8169,-777 8168,-778 8167,-779 8166,-779 8165,-780 8164,-780 8163,-780 8162,-780 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="8162,-780 8163,-780 8164,-780 8165,-780 8166,-781 8167,-782 8168,-782 8169,-783 8170,-784 8170,-785 8171,-786 8171,-787 8171,-788 8171,-789 8171,-790 8171,-791 8171,-792 8170,-793 8170,-794 8169,-795 8168,-796 8167,-797 8166,-797 8165,-798 8164,-798 8163,-798 8162,-798 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7556,-2101 7557,-2101 7558,-2101 7559,-2102 7560,-2102 7561,-2103 7562,-2104 7563,-2105 7563,-2106 7564,-2107 7564,-2108 7564,-2109 7564,-2110 7564,-2111 7564,-2112 7564,-2113 7563,-2114 7563,-2115 7562,-2116 7561,-2117 7560,-2118 7559,-2118 7558,-2119 7557,-2119 7556,-2119 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="7556,-2119 7557,-2119 7558,-2119 7559,-2119 7560,-2120 7561,-2121 7562,-2121 7563,-2122 7564,-2123 7564,-2124 7565,-2125 7565,-2126 7565,-2127 7565,-2128 7565,-2129 7565,-2130 7565,-2131 7564,-2132 7564,-2133 7563,-2134 7562,-2135 7561,-2136 7560,-2136 7559,-2137 7558,-2137 7557,-2137 7556,-2137 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5274,-2061 5275,-2061 5276,-2061 5277,-2061 5278,-2062 5279,-2063 5280,-2063 5281,-2064 5282,-2065 5282,-2066 5283,-2067 5283,-2068 5283,-2069 5283,-2070 5283,-2071 5283,-2072 5283,-2073 5282,-2074 5282,-2075 5281,-2076 5280,-2077 5279,-2078 5278,-2078 5277,-2079 5276,-2079 5275,-2079 5274,-2079 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5274,-2079 5275,-2079 5276,-2079 5277,-2079 5278,-2080 5279,-2081 5280,-2081 5281,-2082 5282,-2083 5282,-2084 5283,-2085 5283,-2086 5283,-2087 5283,-2088 5283,-2089 5283,-2090 5283,-2091 5282,-2092 5282,-2093 5281,-2094 5280,-2095 5279,-2096 5278,-2096 5277,-2097 5276,-2097 5275,-2097 5274,-2097 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5197,-461 5198,-461 5199,-461 5200,-462 5201,-462 5202,-463 5203,-464 5204,-465 5204,-466 5205,-467 5205,-468 5205,-469 5205,-470 5205,-471 5205,-472 5205,-473 5204,-474 5204,-475 5203,-476 5202,-477 5201,-478 5200,-478 5199,-479 5198,-479 5197,-479 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5197,-479 5198,-479 5199,-479 5200,-480 5201,-480 5202,-481 5203,-482 5204,-483 5204,-484 5205,-485 5205,-486 5205,-487 5205,-488 5205,-489 5205,-490 5205,-491 5204,-492 5204,-493 5203,-494 5202,-495 5201,-496 5200,-496 5199,-497 5198,-497 5197,-497 " stroke="rgb(255,255,255)" stroke-width="0.02875"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5375,-461 5376,-461 5377,-461 5378,-461 5379,-462 5380,-463 5381,-463 5382,-464 5383,-465 5383,-466 5384,-467 5384,-468 5384,-469 5384,-470 5384,-471 5384,-472 5384,-473 5383,-474 5383,-475 5382,-476 5381,-477 5380,-478 5379,-478 5378,-479 5377,-479 5376,-479 5375,-479 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5375,-479 5376,-479 5377,-479 5378,-479 5379,-480 5380,-481 5381,-481 5382,-482 5383,-483 5383,-484 5384,-485 5384,-486 5384,-487 5384,-488 5384,-489 5384,-490 5384,-491 5383,-492 5383,-493 5382,-494 5381,-495 5380,-496 5379,-496 5378,-497 5377,-497 5376,-497 5375,-497 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4694,-809 4694,-808 4694,-807 4694,-806 4695,-805 4696,-804 4696,-803 4697,-802 4698,-801 4699,-801 4700,-800 4701,-800 4702,-800 4703,-800 4704,-800 4705,-800 4706,-800 4707,-801 4708,-801 4709,-802 4710,-803 4711,-804 4711,-805 4712,-806 4712,-807 4712,-808 4712,-809 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="4712,-809 4712,-808 4712,-807 4712,-806 4713,-805 4714,-804 4714,-803 4715,-802 4716,-801 4717,-801 4718,-800 4719,-800 4720,-800 4721,-800 4722,-800 4723,-800 4724,-800 4725,-801 4726,-801 4727,-802 4728,-803 4729,-804 4729,-805 4730,-806 4730,-807 4730,-808 4730,-809 " stroke="rgb(255,255,255)" stroke-width="0.03"/>
   <polyline DF8003:Layer="主干线" fill="none" points="6274,-2410 6274,-2412 6274,-2413 6273,-2415 6272,-2416 6272,-2417 6271,-2418 6269,-2420 6268,-2420 6267,-2421 6265,-2422 6264,-2422 6262,-2422 6261,-2422 6259,-2422 6258,-2422 6256,-2421 6255,-2420 6254,-2420 6252,-2418 6251,-2417 6251,-2416 6250,-2415 6249,-2413 6249,-2412 6249,-2410 " stroke="rgb(255,255,255)" stroke-width="0.04125"/>
   <polyline DF8003:Layer="主干线" fill="none" points="5907,-2389 5907,-2391 5907,-2392 5906,-2394 5905,-2395 5905,-2396 5904,-2397 5902,-2399 5901,-2399 5900,-2400 5898,-2401 5897,-2401 5895,-2401 5894,-2401 5892,-2401 5891,-2401 5889,-2400 5888,-2399 5887,-2399 5885,-2397 5884,-2396 5884,-2395 5883,-2394 5882,-2392 5882,-2391 5882,-2389 " stroke="rgb(255,255,255)" stroke-width="0.04125"/>
   <polyline DF8003:Layer="支线" fill="none" points="7534,-33 7534,-32 7534,-32 7534,-31 7535,-31 7535,-30 7535,-30 7536,-29 7536,-29 7537,-28 7537,-28 7538,-28 7539,-28 7539,-28 7540,-28 7541,-28 7541,-28 7542,-29 7542,-29 7543,-30 7543,-30 7543,-31 7544,-31 7544,-32 7544,-32 7544,-33 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
   <polyline DF8003:Layer="支线" fill="none" points="7544,-33 7544,-32 7544,-32 7544,-31 7545,-31 7545,-30 7545,-30 7546,-29 7546,-29 7547,-28 7547,-28 7548,-28 7549,-28 7549,-28 7550,-28 7551,-28 7551,-28 7552,-29 7552,-29 7553,-30 7553,-30 7553,-31 7554,-31 7554,-32 7554,-32 7554,-33 " stroke="rgb(255,255,255)" stroke-width="0.01625"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 0.000000 0.000000 1.000000 7443.000000 -1210.000000) translate(0,9)">1号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 0.000000 0.000000 1.000000 7682.000000 -1225.000000) translate(0,9)">2号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="11" transform="matrix(1.000000 0.000000 0.000000 1.000000 7459.000000 -1343.000000) translate(0,9)">1号杆</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 8102.000000 -2465.000000) translate(0,20)">CT:50/5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 8097.000000 -2555.000000) translate(0,20)"> 封山牌</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 8097.000000 -2555.000000) translate(0,45)">{\W1.01849; 计量}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7437.000000 -778.000000) translate(0,20)">土墙计量</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 5808.000000 -2888.000000) translate(0,40)">35kV三街变10kV母线</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5483.000000 -2552.000000) translate(0,20)">036</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5483.000000 -2604.000000) translate(0,20)">0362</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5486.000000 -2492.000000) translate(0,20)">0366</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5929.000000 -2606.000000) translate(0,20)">0372</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5930.000000 -2558.000000) translate(0,20)">037</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5932.000000 -2500.000000) translate(0,20)">0376</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6300.000000 -2600.000000) translate(0,20)">0321</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6302.000000 -2547.000000) translate(0,20)">032</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6303.000000 -2498.000000) translate(0,20)">0326</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6749.000000 -2607.000000) translate(0,20)">0331</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6755.000000 -2556.000000) translate(0,20)">033</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6757.000000 -2508.000000) translate(0,20)">0336</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 8068.000000 -2364.000000) translate(0,20)">F0781</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5361.000000 -2685.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5361.000000 -2685.000000) translate(0,45)">{\W0.431662; 黑</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5361.000000 -2685.000000) translate(0,70)">\W0.396506; 泥</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5361.000000 -2685.000000) translate(0,95)">\W0.419962; 线}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5781.000000 -2698.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5781.000000 -2698.000000) translate(0,45)">{\W0.376987; 集</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5781.000000 -2698.000000) translate(0,70)">\W0.376987; 镇</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5781.000000 -2698.000000) translate(0,95)">\W0.419962; 线}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6181.000000 -2693.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6181.000000 -2693.000000) translate(0,45)">{\W0.388687; 秀</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6181.000000 -2693.000000) translate(0,70)">\W0.392596; 水</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6181.000000 -2693.000000) translate(0,95)">\W0.392596; 塘</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6181.000000 -2693.000000) translate(0,120)">\W0.419962; 线}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6619.000000 -2689.000000) translate(0,20)">10kV</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6619.000000 -2689.000000) translate(0,45)">{\W0.54104; 中</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6619.000000 -2689.000000) translate(0,70)">\W0.376987; 三</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6619.000000 -2689.000000) translate(0,95)">\W0.376987; 树</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6619.000000 -2689.000000) translate(0,120)">\W0.419962; 线}</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5482.000000 -2323.000000) translate(0,20)">F0521</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5466.000000 -2354.000000) translate(0,12)">8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5466.000000 -2278.000000) translate(0,12)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5466.000000 -2172.000000) translate(0,12)">17</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5472.000000 -2067.000000) translate(0,12)">20</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5192.000000 -2054.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5121.000000 -2055.000000) translate(0,12)">11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5079.000000 -2073.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5024.000000 -2074.000000) translate(0,12)">13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4900.000000 -2079.000000) translate(0,12)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4699.000000 -2079.000000) translate(0,12)">28</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4689.000000 -2303.000000) translate(0,12)">38</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4687.000000 -2401.000000) translate(0,12)">49</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4652.000000 -2241.000000) translate(0,12)">34</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4688.000000 -2176.000000) translate(0,12)">32</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4636.000000 -2046.000000) translate(0,12)">BZ055</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5234.000000 -1665.000000) translate(0,12)">BZ054</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5390.000000 -2042.000000) translate(0,12)">Z0521</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5330.000000 -2043.000000) translate(0,12)">Z052</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5479.000000 -1562.000000) translate(0,12)">BZ058</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5498.000000 -1154.000000) translate(0,12)">BZ059</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5473.000000 -879.000000) translate(0,12)">BZ061</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5386.000000 -876.000000) translate(0,12)">BZ062</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5161.000000 -428.000000) translate(0,12)">Z0631</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4982.000000 -424.000000) translate(0,12)">K0692</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4985.000000 -233.000000) translate(0,12)">K039</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4984.000000 -388.000000) translate(0,12)">K0691</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5244.000000 -955.000000) translate(0,10)">K062</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4985.000000 -194.000000) translate(0,12)">K0391</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4898.000000 -793.000000) translate(0,12)">Z0661</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4985.000000 -968.000000) translate(0,12)">Z0671</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 4691.000000 -1009.000000) translate(0,20)">F0591</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5865.000000 -2070.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5864.000000 -1878.000000) translate(0,12)">11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6282.000000 -2221.000000) translate(0,12)">Z0631</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6284.000000 -1378.000000) translate(0,20)">Z0661</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6237.000000 -2341.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6232.000000 -2236.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6599.000000 -2268.000000) translate(0,12)">11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6235.000000 -1868.000000) translate(0,12)">9</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6225.000000 -1417.000000) translate(0,12)">13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6493.000000 -1430.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6471.000000 -1307.000000) translate(0,12)">13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6450.000000 -2249.000000) translate(0,12)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6676.000000 -2056.000000) translate(0,12)">103</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7832.000000 -824.000000) translate(0,12)">BZ088</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7845.000000 -1655.000000) translate(0,12)">BZ089</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7843.000000 -1966.000000) translate(0,12)">Z0871</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7413.000000 -1908.000000) translate(0,20)">F0751</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7416.000000 -1872.000000) translate(0,20)">F075</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7415.000000 -1128.000000) translate(0,20)">F0741</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7413.000000 -1090.000000) translate(0,20)">F074</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7408.000000 -669.000000) translate(0,20)">F0731</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6722.000000 -2446.000000) translate(0,12)">108</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7219.000000 -2310.000000) translate(0,12)">Z0842</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7389.000000 -2315.000000) translate(0,12)">Z0843</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7175.000000 -2343.000000) translate(0,12)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7185.000000 -2108.000000) translate(0,12)">BZ084</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6917.000000 -2082.000000) translate(0,12)">94</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7016.000000 -2042.000000) translate(0,12)">88</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7158.000000 -2046.000000) translate(0,12)">87</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7008.000000 -2179.000000) translate(0,12)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7294.000000 -2042.000000) translate(0,12)">82</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7378.000000 -2080.000000) translate(0,12)">81</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7787.000000 -2081.000000) translate(0,12)">81T10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8071.000000 -2401.000000) translate(0,12)">81T16</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6734.000000 -2032.000000) translate(0,12)">BZ086</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6944.000000 -2019.000000) translate(0,12)">Z0851</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7065.000000 -1810.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6905.000000 -1804.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6693.000000 -1926.000000) translate(0,12)">6</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7358.000000 -1939.000000) translate(0,12)">80</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7407.000000 -1732.000000) translate(0,12)">74</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7405.000000 -1630.000000) translate(0,12)">70</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7362.000000 -1366.000000) translate(0,12)">65</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7405.000000 -1216.000000) translate(0,12)">62</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7400.000000 -905.000000) translate(0,12)">57</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7796.000000 -1981.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7799.000000 -1697.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7788.000000 -1349.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7824.000000 -1156.000000) translate(0,12)">13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7826.000000 -1045.000000) translate(0,12)">17</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7788.000000 -960.000000) translate(0,12)">18</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7823.000000 -880.000000) translate(0,12)">25</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7824.000000 -652.000000) translate(0,12)">31</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8169.000000 -1587.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8168.000000 -1452.000000) translate(0,12)">10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8171.000000 -1276.000000) translate(0,12)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8173.000000 -1113.000000) translate(0,12)">15</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8171.000000 -989.000000) translate(0,12)">20</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8175.000000 -839.000000) translate(0,12)">24</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8174.000000 -662.000000) translate(0,12)">33</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7821.000000 -1479.000000) translate(0,12)">9</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7790.000000 -751.000000) translate(0,12)">27</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7822.000000 -856.000000) translate(0,12)">26</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4719.000000 -1817.000000) translate(0,12)">6</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -1693.000000) translate(0,12)">9</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4716.000000 -1573.000000) translate(0,12)">16</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4718.000000 -1348.000000) translate(0,12)">27</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5024.000000 -1776.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5017.000000 -1688.000000) translate(0,12)">23</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5112.000000 -1702.000000) translate(0,12)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5219.000000 -1702.000000) translate(0,12)">8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5277.000000 -1700.000000) translate(0,12)">15</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -1452.000000) translate(0,12)">29</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5020.000000 -1337.000000) translate(0,12)">35</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5019.000000 -1219.000000) translate(0,12)">37</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -1092.000000) translate(0,12)">43</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5028.000000 -1881.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5426.000000 -1932.000000) translate(0,12)">22</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5422.000000 -1811.000000) translate(0,12)">25</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -1708.000000) translate(0,12)">26</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5415.000000 -1595.000000) translate(0,12)">30</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5644.000000 -1606.000000) translate(0,12)">10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5705.000000 -1611.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5418.000000 -1334.000000) translate(0,12)">31</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5420.000000 -1184.000000) translate(0,12)">34</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5655.000000 -1198.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5699.000000 -1198.000000) translate(0,12)">10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5432.000000 -908.000000) translate(0,12)">35</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5531.000000 -912.000000) translate(0,12)">3</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5643.000000 -911.000000) translate(0,12)">6</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5705.000000 -911.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5350.000000 -910.000000) translate(0,12)">4</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5263.000000 -887.000000) translate(0,12)">6</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5157.000000 -908.000000) translate(0,12)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5420.000000 -636.000000) translate(0,12)">36</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5422.000000 -523.000000) translate(0,12)">38</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5366.000000 -449.000000) translate(0,12)">39</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5254.000000 -449.000000) translate(0,12)">41</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5186.000000 -448.000000) translate(0,12)">42</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5150.000000 -478.000000) translate(0,12)">44</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5077.000000 -478.000000) translate(0,12)">46</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4942.000000 -470.000000) translate(0,12)">47</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5142.000000 -374.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4948.000000 -394.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4767.000000 -1193.000000) translate(0,12)">Z0682</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4759.000000 -1109.000000) translate(0,12)">BZ068</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -1085.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4812.000000 -1020.000000) translate(0,12)">59</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4920.000000 -1021.000000) translate(0,12)">58</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4544.000000 -1050.000000) translate(0,12)">66</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -1061.000000) translate(0,12)">70</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4942.000000 -979.000000) translate(0,12)">55</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4944.000000 -835.000000) translate(0,12)">52</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4782.000000 -807.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4749.000000 -832.000000) translate(0,12)">15</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4665.000000 -834.000000) translate(0,12)">28</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4978.000000 -624.000000) translate(0,12)">50</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4976.000000 -557.000000) translate(0,12)">49</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4978.000000 -498.000000) translate(0,12)">48</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6016.000000 -2779.000000) translate(0,20)">012</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 5925.000000 -2782.000000) translate(0,20)">0122</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 6091.000000 -2783.000000) translate(0,20)">0121</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 6608.000000 -2828.000000) translate(0,40)">母线I段</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(1.000000 0.000000 0.000000 1.000000 5301.000000 -2817.000000) translate(0,40)">母线II段</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5466.000000 -2324.000000) translate(0,12)">8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5054.000000 -1624.000000) translate(0,12)">23</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7357.000000 -1134.000000) translate(0,12)">62</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6237.000000 -969.000000) translate(0,12)">14</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4983.000000 -1651.000000) translate(0,12)">BZ056</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4942.000000 -430.000000) translate(0,12)">47</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4908.000000 -1753.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7405.000000 -1488.000000) translate(0,12)">67</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6508.000000 -2253.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5412.000000 -2155.000000) translate(0,12)">17</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5051.000000 -1740.000000) translate(0,12)">17</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4976.000000 -925.000000) translate(0,12)">54</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4872.000000 -808.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4854.000000 -826.000000) translate(0,12)">6</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4810.000000 -836.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5183.000000 -358.000000) translate(0,12)">3</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5478.000000 -507.000000) translate(0,12)">38</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7395.000000 -1337.000000) translate(0,12)">65</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7355.000000 -1091.000000) translate(0,12)">61</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7800.000000 -1795.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8137.000000 -960.000000) translate(0,12)">21</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8136.000000 -804.000000) translate(0,12)">25</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7917.000000 -761.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5154.000000 -2108.000000) translate(0,12)">Z0571</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5117.000000 -917.000000) translate(0,12)">15</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5177.000000 -873.000000) translate(0,10)">Z0625</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7779.000000 -823.000000) translate(0,12)">26</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8179.000000 -783.000000) translate(0,12)">BZ096</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8177.000000 -939.000000) translate(0,12)">BZ095</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8099.000000 -1296.000000) translate(0,12)">BZ097</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8129.000000 -1252.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7873.000000 -1697.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7336.000000 -1521.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7303.000000 -1505.000000) translate(0,12)">Z0831</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7149.000000 -2115.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7420.000000 -2295.000000) translate(0,12)">13</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7244.000000 -2293.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7531.000000 -2043.000000) translate(0,12)">81T07</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5274.000000 -2053.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5189.000000 -2091.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -2098.000000) translate(0,12)">BZ052</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5119.000000 -2078.000000) translate(0,12)">Z0561</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5124.000000 -2099.000000) translate(0,12)">11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5400.000000 -2189.000000) translate(0,12)">BZ051</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4921.000000 -1711.000000) translate(0,12)">Z0535</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4900.000000 -1714.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5139.000000 -787.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5405.000000 -916.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5492.000000 -914.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5516.000000 -1172.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5495.000000 -1580.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5202.000000 -481.000000) translate(0,12)">BZ071</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5390.000000 -490.000000) translate(0,12)">BZ069</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5127.000000 -447.000000) translate(0,12)">44</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4752.000000 -861.000000) translate(0,12)">BZ065</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4810.000000 -799.000000) translate(0,12)">BZ066</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4917.000000 -830.000000) translate(0,12)">52</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4801.000000 -1126.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4809.000000 -1172.000000) translate(0,12)">9</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4717.000000 -1049.000000) translate(0,12)">60</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5465.000000 -1831.000000) translate(0,12)">BZ057</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5472.000000 -539.000000) translate(0,12)">BZ064</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6492.000000 -2217.000000) translate(0,12)">BZ064</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5426.000000 -1978.000000) translate(0,12)">21</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5469.000000 -2007.000000) translate(0,12)">F0551</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5477.000000 -1970.000000) translate(0,12)">F055</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7838.000000 -1934.000000) translate(0,12)">Z087</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -849.000000) translate(0,8)">高压计量</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5287.000000 -2098.000000) translate(0,12)">BZ067</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7576.000000 -2129.000000) translate(0,12)">BZ083</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7396.000000 -1374.000000) translate(0,12)">BZ082</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7363.000000 -2295.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5199.000000 -350.000000) translate(0,12)">BZ063</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4692.000000 -805.000000) translate(0,12)">16</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4941.000000 -1057.000000) translate(0,12)">Z0731</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4913.000000 -1055.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4690.000000 -833.000000) translate(0,12)">BZ072</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5373.000000 -2186.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5570.000000 -1825.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5386.000000 -2293.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5386.000000 -2373.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5216.000000 -2159.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5129.000000 -2206.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5083.000000 -2138.000000) translate(0,12)">8</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5233.000000 -1620.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4942.000000 -1095.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -1160.000000) translate(0,12)">９</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4746.000000 -1245.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4742.000000 -1174.000000) translate(0,12)">11</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5109.000000 -1236.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5036.000000 -997.000000) translate(0,12)">６</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4817.000000 -877.000000) translate(0,12)">８</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4871.000000 -543.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5068.000000 -383.000000) translate(0,12)">４</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5302.000000 -359.000000) translate(0,12)">８</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5386.000000 -548.000000) translate(0,12)">７</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5549.000000 -537.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5546.000000 -646.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7477.000000 -2362.000000) translate(0,12)">1７</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7261.000000 -1646.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7263.000000 -1505.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7264.000000 -1370.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7566.000000 -1362.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7266.000000 -1234.000000) translate(0,12)">２</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7713.000000 -1063.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7877.000000 -971.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8054.000000 -1292.000000) translate(0,12)">３</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7887.000000 -27.000000) translate(0,10)">丁志刚</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7888.000000 -61.000000) translate(0,10)">者光兴</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5236.000000 -915.000000) translate(0,10)">K0621</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5241.000000 -985.000000) translate(0,8)">高压计量</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4987.000000 -362.000000) translate(0,12)">K069</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5228.000000 -888.000000) translate(0,12)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5201.000000 -916.000000) translate(0,8)">12</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5207.000000 -942.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5232.000000 -1008.000000) translate(0,8)">04</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5368.000000 -860.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6903.000000 -1868.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5000.000000 -647.000000) translate(0,12)">K0673</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7490.000000 -1215.000000) translate(0,12)">K0821</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4930.000000 -215.000000) translate(0,12)">54</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4972.000000 -139.000000) translate(0,12)">51</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7319.000000 -883.000000) translate(0,12)">Z0791</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7247.000000 -922.000000) translate(0,12)">7</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7335.000000 -922.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7294.000000 -926.000000) translate(0,12)">2</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7218.000000 -799.000000) translate(0,12)">１</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5061.000000 -2015.000000) translate(0,12)">Z055</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5053.000000 -2045.000000) translate(0,12)">Z0551</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4908.000000 -687.000000) translate(0,12)">F0561</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4916.000000 -718.000000) translate(0,12)">F056</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4944.000000 -746.000000) translate(0,12)">51</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6269.000000 -2437.000000) translate(0,12)">L0321</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5940.000000 -2430.000000) translate(0,12)">L0371</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5489.000000 -2408.000000) translate(0,12)">L0361</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5445.000000 -2397.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5879.000000 -2421.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6245.000000 -2450.000000) translate(0,12)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6659.000000 -2435.000000) translate(0,12)">L0131</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6359.000000 -2403.000000) translate(0,12)">L0132</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5113.000000 -669.000000) translate(0,8)">10</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5037.000000 -657.000000) translate(0,8)">1</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5065.000000 -644.000000) translate(0,12)">K0674</text>
   <text DF8003:Layer="主干线" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5110.000000 -570.000000) translate(0,8)">3</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5866.000000 -2263.000000) translate(0,12)">5</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5910.000000 -2249.000000) translate(0,12)">Z0711</text>
   <text DF8003:Layer="主干线" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6071.000000 -2274.000000) translate(0,12)">5</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5493.000000 -714.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5496.000000 -736.000000) translate(0,12)">许家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5628.000000 -714.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5631.000000 -736.000000) translate(0,12)">刘家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5320.000000 -774.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5312.000000 -796.000000) translate(0,12)">周家大村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5208.000000 -777.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5211.000000 -797.000000) translate(0,12)">吴家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5016.000000 -893.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5007.000000 -916.000000) translate(0,12)">秧鸡扎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5724.000000 -831.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5718.000000 -855.000000) translate(0,12)">马鞍山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4688.000000 -889.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4688.000000 -911.000000) translate(0,12)">武甲地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5312.000000 -2314.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5310.000000 -2336.000000) translate(0,12)">倪家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5299.000000 -2251.000000) translate(0,12)">上陈家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5299.000000 -2146.000000) translate(0,12)">力格么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5302.000000 -2226.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5310.000000 -2124.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5153.000000 -1962.000000) translate(0,12)">沙河箐公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5157.000000 -1936.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5118.000000 -1852.000000) translate(0,12)">干蚂蝗私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5122.000000 -1826.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5181.000000 -1784.000000) translate(0,12)">背阴大平掌私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5189.000000 -1757.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5073.000000 -2308.000000) translate(0,12)">移动私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5078.000000 -2281.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5009.000000 -2231.000000) translate(0,12)">联通私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -2204.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4867.000000 -1933.000000) translate(0,12)">黄草岭私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4872.000000 -1907.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4762.000000 -2207.000000) translate(0,12)">上五家庄公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4766.000000 -2181.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4524.000000 -2143.000000) translate(0,12)">下五家庄公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4528.000000 -2116.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4514.000000 -2270.000000) translate(0,12)">闪片房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4519.000000 -2244.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4506.000000 -2372.000000) translate(0,12)">大田丫口公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4511.000000 -2346.000000) translate(0,12)">S9-160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4536.000000 -1785.000000) translate(0,12)">蚂蝗箐私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4541.000000 -1759.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4535.000000 -1664.000000) translate(0,12)">力白么私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4540.000000 -1638.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4549.000000 -1538.000000) translate(0,12)">大箐私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4554.000000 -1512.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4540.000000 -1319.000000) translate(0,12)">务打拉私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4544.000000 -1292.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4782.000000 -1715.000000) translate(0,12)">孔家私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4787.000000 -1689.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5077.000000 -1549.000000) translate(0,12)">大丫口私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -1523.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5280.000000 -1641.000000) translate(0,12)">背阴村委会</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5285.000000 -1615.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5119.000000 -1421.000000) translate(0,12)">钱家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5124.000000 -1395.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5113.000000 -1308.000000) translate(0,12)">旧地基公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5126.000000 -1281.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5120.000000 -1191.000000) translate(0,12)">背阴山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5125.000000 -1165.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5180.000000 -1116.000000) translate(0,12)">田房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5185.000000 -1089.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4903.000000 -1176.000000) translate(0,12)">杨梅树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4908.000000 -1150.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4825.000000 -972.000000) translate(0,12)">母希姑公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -946.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4529.000000 -907.000000) translate(0,12)">茶花树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4534.000000 -881.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4606.000000 -777.000000) translate(0,12)">县官村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4611.000000 -751.000000) translate(0,12)">S11-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4682.000000 -691.000000) translate(0,12)">希希扎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4687.000000 -665.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 -564.000000) translate(0,12)">普嘎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4714.000000 -543.000000) translate(0,12)">S13-M-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4775.000000 -478.000000) translate(0,12)">凹子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4779.000000 -452.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5018.000000 -796.000000) translate(0,12)">张罗家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5023.000000 -770.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4809.000000 -596.000000) translate(0,12)">杨何家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4808.000000 -648.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5101.000000 -430.000000) translate(0,12)">聚龙山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5047.000000 -325.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5338.000000 -587.000000) translate(0,12)">小竹箐私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5342.000000 -609.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5532.000000 -602.000000) translate(0,12)">孔家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5537.000000 -576.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5626.000000 -1019.000000) translate(0,12)">施家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5631.000000 -993.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5718.000000 -1131.000000) translate(0,12)">大平掌公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5723.000000 -1105.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5545.000000 -1306.000000) translate(0,12)">黑泥村委会公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5550.000000 -1280.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5609.000000 -1426.000000) translate(0,12)">咪咪一社公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5613.000000 -1400.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5720.000000 -1527.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5636.000000 -1715.000000) translate(0,12)">者家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5641.000000 -1689.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5632.000000 -1821.000000) translate(0,12)">塘房公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5637.000000 -1795.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5304.000000 -347.000000) translate(0,12)">李家箐煤矿私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5308.000000 -321.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5117.000000 -280.000000) translate(0,12)">李罗普家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5122.000000 -254.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6448.000000 -1216.000000) translate(0,12)">马鞍山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6453.000000 -1190.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6581.000000 -1377.000000) translate(0,12)">大麦地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6586.000000 -1350.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6408.000000 -1829.000000) translate(0,12)">王家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6413.000000 -1803.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5986.000000 -2045.000000) translate(0,12)">集镇2号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5990.000000 -2019.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6669.000000 -1825.000000) translate(0,12)">旧房子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6674.000000 -1799.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7043.000000 -1719.000000) translate(0,12)">花西郎公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7052.000000 -1692.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7176.000000 -1706.000000) translate(0,12)">诺苴拉公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7181.000000 -1680.000000) translate(0,12)">S7-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7176.000000 -1313.000000) translate(0,12)">多依树村委会私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7181.000000 -1287.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7173.000000 -1188.000000) translate(0,12)">大岔路私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7178.000000 -1162.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7640.000000 -618.000000) translate(0,12)">大丫口公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7645.000000 -592.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7615.000000 -845.000000) translate(0,12)">上新房村委会变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7620.000000 -819.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7624.000000 -1020.000000) translate(0,12)">大坟山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7629.000000 -994.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7629.000000 -1457.000000) translate(0,12)">对门公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7634.000000 -1431.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7644.000000 -1644.000000) translate(0,12)">丁家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7642.000000 -1618.000000) translate(0,12)">S8-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7998.000000 -1763.000000) translate(0,12)">蚂蚁咪公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8003.000000 -1737.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7990.000000 -1546.000000) translate(0,12)">大麻地公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7995.000000 -1520.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7989.000000 -1405.000000) translate(0,12)">再可么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7993.000000 -1378.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7987.000000 -1248.000000) translate(0,12)">尼尔公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7988.000000 -1222.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7977.000000 -1085.000000) translate(0,12)">尼尔山公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7982.000000 -1059.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7975.000000 -962.000000) translate(0,12)">罗把树公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7980.000000 -936.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7968.000000 -809.000000) translate(0,12)">上下坪子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7973.000000 -783.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7867.000000 -926.000000) translate(0,12)">哈拉公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7872.000000 -900.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7113.000000 -2482.000000) translate(0,12)">孔王家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7118.000000 -2456.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7298.000000 -2484.000000) translate(0,12)">\W0.40432; \W1;白泥潭煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7298.000000 -2484.000000) translate(0,27)">{\W1.83009; 1号私变}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7308.000000 -2429.000000) translate(0,12)">S7-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7439.000000 -2429.000000) translate(0,12)">S8-160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5199.000000 -1549.000000) translate(0,12)">小广庄私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5204.000000 -1523.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5629.000000 -1941.000000) translate(0,12)">扯苴公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5634.000000 -1915.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5714.000000 -1558.000000) translate(0,12)">咪咪二社公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5542.000000 -488.000000) translate(0,12)">寇家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5546.000000 -462.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5281.000000 -587.000000) translate(0,12)">酒簸村委会私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5246.000000 -607.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4797.000000 -104.000000) translate(0,12)">自可么公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4802.000000 -78.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4937.000000 -107.000000) translate(0,12)">至五街河一级电站</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4937.000000 -107.000000) translate(0,27)">{\W1.4286; 10kV煤矿线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5012.000000 -955.000000) translate(0,10)">马鹿塘煤矿私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5026.000000 -934.000000) translate(0,10)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4411.000000 -908.000000) translate(0,12)">徐家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4416.000000 -882.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5967.000000 -1846.000000) translate(0,12)">集镇1号公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5971.000000 -1820.000000) translate(0,12)">S11-M-400kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6386.000000 -2315.000000) translate(0,12)">秀水塘公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6391.000000 -2289.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6218.000000 -848.000000) translate(0,12)">岔河公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6223.000000 -822.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6298.000000 -1442.000000) translate(0,12)">10kV大麦地支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6278.000000 -2256.000000) translate(0,12)">10kV石照壁支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7948.000000 -2705.000000) translate(0,20)">至五街河一级电站</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6872.000000 -1715.000000) translate(0,12)">花西郎丁家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6875.000000 -1685.000000) translate(0,12)">S7-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7188.000000 -1603.000000) translate(0,12)">凹布公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7193.000000 -1577.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7544.000000 -1402.000000) translate(0,12)">支柏田移动机站私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7559.000000 -1333.000000) translate(0,12)">S7-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7296.000000 -523.000000) translate(0,20)">至中山变电站</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7887.000000 -1309.000000) translate(0,12)">凹子公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7880.000000 -1281.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7899.000000 -722.000000) translate(0,12)">哈拉箐公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7904.000000 -696.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7820.000000 -792.000000) translate(0,12)">\W0.285; \W1;10kV哈拉箐</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7820.000000 -792.000000) translate(0,27)">{\W2.24203; 分支线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7979.000000 -631.000000) translate(0,12)">德苴公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7984.000000 -605.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7630.000000 -1120.000000) translate(0,12)">小干坝公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7635.000000 -1094.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6989.000000 -2298.000000) translate(0,12)">厦家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6994.000000 -2272.000000) translate(0,12)">S9-20kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7422.000000 -2485.000000) translate(0,12)">\W0.40432; \W1;白泥潭煤矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7422.000000 -2485.000000) translate(0,27)">{\W1.76765; 2号私变}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,27)">{\W0.907359; 孔</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,42)">\W0.919078; 王</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,57)">\W0.926859; 家</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,72)">\W0.91514; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7114.000000 -2427.000000) translate(0,87)">\W0.942515; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7213.000000 -2270.000000) translate(0,12)">\W0.1695; \W1;10kV白泥潭煤矿分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,27)">{\W0.907359; 花</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,42)">\W0.922968; 西</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,57)">\W1.005; 郎</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,72)">\W0.91514; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6870.000000 -2019.000000) translate(0,87)">\W0.942515; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,27)">{\W1.08703; 旧</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,42)">\W0.938578; 房</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,57)">\W0.922968; 子</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,72)">\W0.91514; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6647.000000 -2014.000000) translate(0,87)">\W0.942515; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,27)">{\W0.577406; 大</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,42)">\W0.56175; 麻</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,57)">\W0.557859; 地</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,72)">\W0.573468; 分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,87)">\W0.573468; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 8199.000000 -1527.000000) translate(0,102)">\W0.600843; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,27)">{\W0.565687; 上</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,42)">\W0.573468; 新</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,57)">\W0.596906; 房</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,72)">\W0.573468; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7743.000000 -2012.000000) translate(0,87)">\W0.600843; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,27)">{\W0.585187; 务</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,42)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,57)">\W0.581296; 打</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,72)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,87)">\W0.569578; 拉</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,102)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,117)">\W0.573468; 分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,132)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,147)">\W0.573468; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,162)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4730.000000 -1977.000000) translate(0,177)">\W0.600843; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,27)">{\W0.596906; 背</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,42)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,57)">\W0.714093; 阴</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,72)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,87)">\W0.573468; 分</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,102)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,117)">\W0.573468; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,132)">\W1.47975; </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4988.000000 -2019.000000) translate(0,147)">\W0.600843; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5227.000000 -2024.000000) translate(0,12)">10kV 蚂 蟥 箐 支 线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5067.000000 -1724.000000) translate(0,12)">10kV背阴村委会分支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5472.000000 -1618.000000) translate(0,12)">10kV三爷庙支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5471.000000 -1212.000000) translate(0,12)">10kV大平掌支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5502.000000 -932.000000) translate(0,12)">10kV许家支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5299.000000 -932.000000) translate(0,10)">10kV秧鸡扎支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5164.000000 -387.000000) translate(0,10)">10kV李家箐煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,12)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,27)">{\W0.549328; 三 </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,42)">\W0.549328; 街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,57)">\W0.545437; 煤</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,72)">\W0.5415; 矿</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,87)">\W1.1587; I</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,102)">\W0.670406; 回</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4901.000000 -466.000000) translate(0,117)">\W0.592312; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -264.000000) translate(0,12)">三街煤矿私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4765.000000 -238.000000) translate(0,12)">3×160kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4522.000000 -1242.000000) translate(0,12)">\W0.18955; \W1;旧村煤矿2号变私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4524.000000 -1214.000000) translate(0,12)">S9-200kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4516.000000 -1168.000000) translate(0,12)">\W0.39858; \W1;旧村煤矿1号变私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4519.000000 -1139.000000) translate(0,12)">S9-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4612.000000 -1100.000000) translate(0,12)">旧村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4617.000000 -1073.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4580.000000 -855.000000) translate(0,12)">10kV县官村支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 4971.000000 -1019.000000) translate(0,10)">10kV马鹿塘煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4845.000000 -1212.000000) translate(0,12)">10kV旧村煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7257.000000 -1881.000000) translate(0,20)">范家村</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="25" transform="matrix(1.000000 0.000000 0.000000 1.000000 7222.000000 -1103.000000) translate(0,20)">多依树</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7259.000000 -2235.000000) translate(0,12)">范家村公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7264.000000 -2208.000000) translate(0,12)">S9-30kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4838.000000 -1606.000000) translate(0,12)">孔家电信机站私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4878.000000 -1574.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7155.000000 -1461.000000) translate(0,12)">五笔山电信机站私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7193.000000 -1435.000000) translate(0,12)">S9-5kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6405.000000 -2047.000000) translate(0,12)">杨圈凹公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6410.000000 -2021.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6567.000000 -2211.000000) translate(0,12)">石照壁公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6572.000000 -2185.000000) translate(0,12)">S9-10kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5167.000000 -2265.000000) translate(0,12)">国土资源局</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5172.000000 -2238.000000) translate(0,12)">S11-250kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5170.000000 -736.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5124.000000 -759.000000) translate(0,12)">孔德贤煤矿私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5241.000000 -1053.000000) translate(0,10)">三街煤业专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5255.000000 -1032.000000) translate(0,10)">S11-125kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,10)">10kV</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,22)">{\W0.549323; 三 </text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,34)">\W0.549323; 街</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,46)">\W0.545442; 煤</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,58)">\W0.541504; 业</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,70)">\W0.670401; 支</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 5176.000000 -1059.000000) translate(0,82)">\W0.592312; 线}</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5008.000000 -692.000000) translate(0,8)">10kV国土资源局2号专变支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5163.000000 -710.000000) translate(0,12)">10kV国土资源局2号专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5170.000000 -646.000000) translate(0,12)">S11-M-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7812.000000 -257.000000) translate(0,10)">注：红色部份为本次修改设备。</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7494.000000 -1264.000000) translate(0,12)">10kV武壁山煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7622.000000 -1270.000000) translate(0,12)">10kV武壁山煤矿专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7624.000000 -1206.000000) translate(0,12)">S11-M-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7406.000000 -1422.000000) translate(0,12)">10kV支柏田移动机站支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7135.000000 -876.000000) translate(0,12)">木处兰煤矿私变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7140.000000 -850.000000) translate(0,12)">S9-200kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7186.000000 -948.000000) translate(0,12)">10kV木处兰煤矿支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7103.000000 -753.000000) translate(0,12)">10kV木处兰煤矿2号专变支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6986.000000 -818.000000) translate(0,12)">木处兰煤矿2号专变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 7010.000000 -788.000000) translate(0,12)">S7-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4809.000000 -684.000000) translate(0,12)">S9-50kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4814.000000 -704.000000) translate(0,12)">新明公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5122.000000 -629.000000) translate(0,8)">10kV母希姑1号井支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5063.000000 -606.000000) translate(0,12)">母希姑1号井变</text>
   <text DF8003:Layer="公变" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5023.000000 -522.000000) translate(0,12)">S11-M-100kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6047.000000 -2237.000000) translate(0,12)">下陈家公变</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 6052.000000 -2211.000000) translate(0,12)">S9-80kVA</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 5919.000000 -2274.000000) translate(0,8)">10kV下陈家支线</text>
   <text DF8003:Layer="公变" fill="rgb(255,0,0)" font-family="SimSun" font-size="10" transform="matrix(1.000000 0.000000 0.000000 1.000000 4889.000000 -546.000000) translate(0,8)">10kV普嘎村委会支线</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7621.000000 -103.000000) translate(0,10)">电容器组</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7621.000000 -245.000000) translate(0,10)">隔离开关</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7622.000000 -206.000000) translate(0,10)">断路器</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7621.000000 -69.000000) translate(0,10)">线路电缆</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7622.000000 -42.000000) translate(0,10)">跌落保险</text>
   <text DF8003:Layer="自变" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7611.000000 -151.000000) translate(0,10)">高压计量箱</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 8012.000000 -24.000000) translate(0,10)">更新日期</text>
   <text DF8003:Layer="图框（细实线）" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7996.000000 -100.000000) translate(0,10)">  三街供电所区域电网接线图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7558.000000 -281.000000) translate(0,10)"> 图  例</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 7802.000000 -196.000000) translate(0,18)">楚 雄 市 供 电 有 限 公 司</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7756.000000 -98.000000) translate(0,10)">审　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7749.000000 -26.000000) translate(0,10)">CAD制图</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7757.000000 -64.000000) translate(0,10)">校　核</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 7755.000000 -141.000000) translate(0,10)">审　定</text>
   <text DF8003:Layer="标注、文字" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" transform="matrix(1.000000 0.000000 0.000000 1.000000 8157.000000 -23.000000) translate(0,10)">2012年11月22日</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8333" x2="7733" y1="-149" y2="-150"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8333" x2="7733" y1="-210" y2="-210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8002" x2="7733" y1="-115" y2="-115"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8333" x2="7733" y1="-34" y2="-34"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7733" x2="8333" y1="0" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8002" x2="7733" y1="-73" y2="-73"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7848" x2="7848" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7733" x2="7733" y1="-210" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8002" x2="8002" y1="-150" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8108" x2="8108" y1="-34" y2="0"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5038" x2="5136" y1="-657" y2="-657"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5167" x2="5198" y1="-657" y2="-657"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5139" x2="5146" y1="-630" y2="-637"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5160" x2="5167" y1="-651" y2="-657"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5136" x2="5136" y1="-663" y2="-653"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5023" x2="5002" y1="-657" y2="-657"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5146" x2="5160" y1="-637" y2="-651"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5038" x2="5025" y1="-657" y2="-650"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5023" x2="5023" y1="-661" y2="-654"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7491" x2="7478" y1="-1217" y2="-1210"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7476" x2="7476" y1="-1221" y2="-1213"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7491" x2="7589" y1="-1217" y2="-1217"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7621" x2="7651" y1="-1217" y2="-1217"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7600" x2="7614" y1="-1196" y2="-1211"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7593" x2="7600" y1="-1189" y2="-1196"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7614" x2="7621" y1="-1211" y2="-1217"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7589" x2="7589" y1="-1223" y2="-1212"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7476" x2="7455" y1="-1217" y2="-1217"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7298" x2="7200" y1="-770" y2="-770"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7168" x2="7137" y1="-770" y2="-770"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7189" x2="7175" y1="-791" y2="-777"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7196" x2="7189" y1="-798" y2="-791"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7175" x2="7168" y1="-777" y2="-770"/>
   <line DF8003:Layer="0" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7200" x2="7200" y1="-764" y2="-775"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="4" x1="4835" x2="6022" y1="-2728" y2="-2728"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5894" x2="5894" y1="-2728" y2="-2566"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5457" x2="5457" y1="-2728" y2="-2562"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6262" x2="6262" y1="-2728" y2="-2560"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6717" x2="6717" y1="-2728" y2="-2558"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6717" x2="8043" y1="-2052" y2="-2052"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="8043" x2="8043" y1="-2052" y2="-2643"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6718" x2="6718" y1="-2052" y2="-1875"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6927" x2="6927" y1="-2052" y2="-1759"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6927" x2="7089" y1="-1857" y2="-1857"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7089" x2="7089" y1="-1857" y2="-1765"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7027" x2="7027" y1="-2052" y2="-2202"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7167" x2="7167" y1="-2052" y2="-2367"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7303" x2="7303" y1="-2052" y2="-2146"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7387" x2="7387" y1="-2052" y2="-1880"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7880" x2="7876" y1="-2052" y2="-2053"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7814" y1="-2053" y2="-1951"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="8162" y1="-1674" y2="-1674"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8162" y1="-1674" y2="-652"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5451" x2="5465" y1="-2599" y2="-2588"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5450" x2="5464" y1="-2490" y2="-2478"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5889" x2="5903" y1="-2600" y2="-2588"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5889" x2="5903" y1="-2492" y2="-2481"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6256" x2="6271" y1="-2599" y2="-2588"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6256" x2="6271" y1="-2498" y2="-2487"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6712" x2="6726" y1="-2599" y2="-2587"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6712" x2="6726" y1="-2495" y2="-2484"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5466" x2="5466" y1="-2562" y2="-2525"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5446" x2="5446" y1="-2562" y2="-2525"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5466" x2="5446" y1="-2525" y2="-2525"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5446" x2="5466" y1="-2562" y2="-2562"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5905" x2="5905" y1="-2566" y2="-2528"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5885" x2="5885" y1="-2566" y2="-2528"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5905" x2="5885" y1="-2528" y2="-2528"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5885" x2="5905" y1="-2566" y2="-2566"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6272" x2="6272" y1="-2560" y2="-2522"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6252" x2="6252" y1="-2560" y2="-2522"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6272" x2="6252" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6252" x2="6272" y1="-2560" y2="-2560"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6727" x2="6727" y1="-2558" y2="-2520"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6707" x2="6707" y1="-2558" y2="-2520"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6727" x2="6707" y1="-2520" y2="-2520"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6707" x2="6727" y1="-2558" y2="-2558"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5457" x2="5457" y1="-2525" y2="-1982"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5894" x2="5894" y1="-2528" y2="-1865"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6262" x2="6262" y1="-2522" y2="-957"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6717" x2="6717" y1="-2520" y2="-2052"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5371" y1="-2350" y2="-2350"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5368" y1="-2270" y2="-2270"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5366" y1="-2163" y2="-2163"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5369" y1="-2056" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5588" y1="-1922" y2="-1922"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5458" x2="5589" y1="-1801" y2="-1801"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5459" x2="5592" y1="-1696" y2="-1696"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5734" y1="-1581" y2="-1581"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5582" y1="-1324" y2="-1324"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5655" x2="5655" y1="-1481" y2="-1581"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5461" x2="5738" y1="-1173" y2="-1173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5659" x2="5659" y1="-1073" y2="-1173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5458" x2="5735" y1="-887" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5656" x2="5656" y1="-787" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5456" x2="5146" y1="-887" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5258" x2="5258" y1="-838" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5356" x2="5356" y1="-838" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5537" x2="5537" y1="-788" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5565" y1="-622" y2="-622"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5565" y1="-514" y2="-514"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5331" x2="5369" y1="-2066" y2="-2066"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5331" x2="5369" y1="-2046" y2="-2046"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5369" x2="5369" y1="-2066" y2="-2046"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5331" x2="5331" y1="-2046" y2="-2066"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5397" x2="5412" y1="-2061" y2="-2050"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5331" x2="4680" y1="-2056" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5117" x2="5117" y1="-2216" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5205" x2="5205" y1="-2002" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5071" x2="5071" y1="-2139" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5046" y1="-2056" y2="-2027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5142" y1="-1872" y2="-1872"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5142" y1="-1768" y2="-1768"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5300" y1="-1680" y2="-1680"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5117" x2="5117" y1="-1601" y2="-1680"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5222" x2="5222" y1="-1599" y2="-1680"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="4853" y1="-1729" y2="-1729"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5136" y1="-1443" y2="-1443"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5136" y1="-1327" y2="-1327"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5136" y1="-1212" y2="-1212"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5136" y1="-1087" y2="-1087"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5458" x2="5091" y1="-456" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5376" x2="5376" y1="-552" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5264" x2="5264" y1="-548" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5086" x2="5086" y1="-365" y2="-457"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5157" x2="5157" y1="-323" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5157" x2="5320" y1="-364" y2="-364"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4708" y1="-2055" y2="-1338"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4708" y1="-2035" y2="-2035"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4612" y1="-1810" y2="-1810"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4612" y1="-1689" y2="-1689"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4612" y1="-1564" y2="-1564"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4708" x2="4606" y1="-1338" y2="-1338"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4680" x2="4680" y1="-2056" y2="-2395"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4680" x2="4589" y1="-2167" y2="-2167"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4680" x2="4585" y1="-2295" y2="-2295"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4680" x2="4581" y1="-2395" y2="-2395"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4680" x2="4796" y1="-2231" y2="-2231"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4968" y1="-118" y2="-207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4851" y1="-492" y2="-492"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4847" y1="-615" y2="-615"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4967" x2="4870" y1="-549" y2="-549"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5053" x2="4640" y1="-808" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4869" x2="4869" y1="-745" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4756" x2="4756" y1="-737" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4806" x2="4806" y1="-872" y2="-808"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4883" x2="4968" y1="-917" y2="-917"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4931" x2="4931" y1="-1095" y2="-1027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4550" x2="4550" y1="-960" y2="-1027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4453" x2="4968" y1="-1027" y2="-1027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4961" x2="4976" y1="-423" y2="-412"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4960" x2="4975" y1="-392" y2="-380"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5236" x2="5236" y1="-958" y2="-921"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5216" x2="5216" y1="-958" y2="-921"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5236" x2="5216" y1="-921" y2="-921"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5216" x2="5236" y1="-958" y2="-958"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4978" x2="4978" y1="-244" y2="-207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4958" x2="4958" y1="-244" y2="-207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4978" x2="4958" y1="-207" y2="-207"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4958" x2="4978" y1="-244" y2="-244"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4961" x2="4976" y1="-195" y2="-184"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4968" y1="-244" y2="-335"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4860" y1="-283" y2="-283"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4858" y1="-130" y2="-130"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4968" x2="4968" y1="-456" y2="-690"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5894" x2="6021" y1="-2063" y2="-2063"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6412" y1="-2330" y2="-2330"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6598" y1="-2228" y2="-2228"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6456" x2="6456" y1="-2090" y2="-2228"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6430" y1="-1858" y2="-1858"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6606" y1="-1402" y2="-1402"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6499" x2="6499" y1="-1264" y2="-1402"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7702" x2="7479" y1="-292" y2="-292"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7702" x2="7702" y1="-15" y2="-292"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7479" x2="7702" y1="-15" y2="-15"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7501" x2="7593" y1="-234" y2="-234"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7537" x2="7557" y1="-224" y2="-244"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7501" x2="7538" y1="-199" y2="-199"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7568" x2="7568" y1="-209" y2="-189"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7538" x2="7538" y1="-189" y2="-209"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7568" x2="7538" y1="-189" y2="-189"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7538" x2="7568" y1="-209" y2="-209"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7568" x2="7593" y1="-199" y2="-199"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7553" x2="7553" y1="-67" y2="-57"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7553" x2="7548" y1="-57" y2="-62"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7548" x2="7553" y1="-62" y2="-67"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7538" x2="7533" y1="-62" y2="-67"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7533" x2="7538" y1="-57" y2="-62"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7533" x2="7533" y1="-67" y2="-57"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7499" x2="7593" y1="-33" y2="-33"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7538" x2="7592" y1="-62" y2="-62"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7592" x2="7498" y1="-62" y2="-62"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7566" x2="7566" y1="-94" y2="-85"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7563" x2="7563" y1="-94" y2="-85"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7531" x2="7548" y1="-94" y2="-94"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7531" x2="7531" y1="-85" y2="-94"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7548" x2="7548" y1="-94" y2="-85"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7531" x2="7548" y1="-85" y2="-85"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7548" x2="7548" y1="-89" y2="-89"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7563" x2="7548" y1="-89" y2="-89"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7566" x2="7591" y1="-89" y2="-89"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7531" x2="7500" y1="-89" y2="-89"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7479" x2="7479" y1="-292" y2="-15"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7568" x2="7531" y1="-134" y2="-134"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7568" x2="7532" y1="-153" y2="-153"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7500" x2="7592" y1="-143" y2="-143"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6920" x2="6935" y1="-2024" y2="-2012"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7167" x2="7474" y1="-2276" y2="-2276"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7359" x2="7359" y1="-2365" y2="-2276"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7474" x2="7474" y1="-2367" y2="-2276"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="8036" x2="8051" y1="-2364" y2="-2353"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8012" x2="8012" y1="-2443" y2="-2525"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8074" x2="8074" y1="-2443" y2="-2525"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7247" y1="-1723" y2="-1723"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7247" y1="-1622" y2="-1622"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7247" y1="-1346" y2="-1346"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7250" y1="-1209" y2="-1209"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7355" x2="7355" y1="-708" y2="-790"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7417" x2="7417" y1="-708" y2="-790"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7397" x2="7397" y1="-1102" y2="-1065"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7377" x2="7377" y1="-1102" y2="-1065"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7397" x2="7377" y1="-1065" y2="-1065"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7377" x2="7397" y1="-1102" y2="-1102"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7398" x2="7398" y1="-1880" y2="-1843"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7378" x2="7378" y1="-1880" y2="-1843"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7398" x2="7378" y1="-1843" y2="-1843"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7378" x2="7398" y1="-1880" y2="-1880"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7380" x2="7394" y1="-1913" y2="-1901"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7387" x2="7387" y1="-1843" y2="-1102"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7387" x2="7387" y1="-1065" y2="-578"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7600" y1="-1346" y2="-1346"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7808" x2="7822" y1="-1977" y2="-1965"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7701" y1="-1674" y2="-1674"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7691" y1="-1471" y2="-1471"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7694" y1="-1145" y2="-1145"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7697" y1="-1039" y2="-1039"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7697" y1="-867" y2="-867"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7700" y1="-644" y2="-644"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7903" y1="-1339" y2="-1339"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7895" y1="-947" y2="-947"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7931" y1="-742" y2="-742"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8051" y1="-1580" y2="-1580"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8051" y1="-1438" y2="-1438"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8045" y1="-1268" y2="-1268"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8042" y1="-1106" y2="-1106"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8035" y1="-981" y2="-981"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8035" y1="-830" y2="-830"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8035" y1="-652" y2="-652"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4453" x2="4453" y1="-959" y2="-1027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4968" x2="4968" y1="-373" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5894" x2="6021" y1="-1864" y2="-1864"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5449" x2="5464" y1="-2307" y2="-2295"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4910" x2="4910" y1="-1981" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5243" x2="5243" y1="-1680" y2="-1680"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5046" y1="-1642" y2="-1642"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5506" x2="5506" y1="-1581" y2="-1581"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5524" x2="5524" y1="-1173" y2="-1173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5499" x2="5499" y1="-887" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5412" x2="5412" y1="-887" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5197" x2="5208" y1="-515" y2="-535"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5222" x2="5199" y1="-559" y2="-559"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5172" x2="5183" y1="-559" y2="-539"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5204" x2="5217" y1="-543" y2="-535"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5201" x2="5214" y1="-539" y2="-531"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5179" x2="5192" y1="-531" y2="-539"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5176" x2="5189" y1="-536" y2="-543"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5194" x2="5194" y1="-566" y2="-551"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5199" x2="5199" y1="-566" y2="-551"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5185" x2="5197" y1="-535" y2="-515"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5210" x2="5222" y1="-539" y2="-559"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5194" x2="5172" y1="-559" y2="-559"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5126" x2="4968" y1="-456" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5150" x2="5165" y1="-442" y2="-430"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4916" x2="4930" y1="-814" y2="-802"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4994" x2="5009" y1="-979" y2="-968"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4711" x2="4725" y1="-1033" y2="-1022"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6297" x2="6312" y1="-2234" y2="-2223"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6300" x2="6315" y1="-1407" y2="-1396"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6262" y1="-957" y2="-901"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6718" x2="6718" y1="-2029" y2="-2029"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7380" x2="7394" y1="-1131" y2="-1120"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="7380" x2="7395" y1="-669" y2="-658"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7814" y1="-817" y2="-817"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7871" x2="7871" y1="-1674" y2="-1674"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8132" x2="8132" y1="-1268" y2="-1268"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7240" x2="7255" y1="-2282" y2="-2270"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7409" x2="7423" y1="-2282" y2="-2270"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7167" x2="7167" y1="-2101" y2="-2101"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4822" x2="4822" y1="-1028" y2="-1220"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4737" x2="4822" y1="-1220" y2="-1220"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4738" x2="4822" y1="-1151" y2="-1151"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4737" x2="4822" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4815" x2="4830" y1="-1175" y2="-1163"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4822" x2="4822" y1="-1108" y2="-1109"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6122" x2="6111" y1="-2735" y2="-2720"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5978" x2="5966" y1="-2735" y2="-2720"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6060" x2="6022" y1="-2719" y2="-2719"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6060" x2="6022" y1="-2739" y2="-2739"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6022" x2="6022" y1="-2719" y2="-2739"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="6060" x2="6060" y1="-2739" y2="-2719"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="4" x1="6060" x2="7273" y1="-2728" y2="-2728"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5197" x2="5196" y1="-516" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="8020" y1="-1786" y2="-1786"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5046" y1="-2035" y2="-2035"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4914" x2="4914" y1="-1652" y2="-1731"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4907" x2="4921" y1="-1717" y2="-1705"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7387" x2="7247" y1="-1481" y2="-1481"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7334" x2="7349" y1="-1486" y2="-1475"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6456" x2="6456" y1="-2186" y2="-2186"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6531" x2="6531" y1="-2224" y2="-2223"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5414" x2="5415" y1="-2163" y2="-2163"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5070" x2="5070" y1="-2079" y2="-2079"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5112" x2="5126" y1="-2091" y2="-2079"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5482" x2="5482" y1="-1802" y2="-1802"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4969" x2="5049" y1="-973" y2="-973"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4842" x2="4842" y1="-809" y2="-809"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4806" x2="4806" y1="-844" y2="-844"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5198" x2="5199" y1="-365" y2="-365"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5500" x2="5500" y1="-513" y2="-513"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7436" x2="7436" y1="-1345" y2="-1345"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8163" x2="8163" y1="-939" y2="-939"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8162" x2="8162" y1="-780" y2="-780"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5206" x2="5206" y1="-2177" y2="-2056"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5198" x2="5213" y1="-2087" y2="-2076"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5167" x2="5167" y1="-797" y2="-887"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5160" x2="5174" y1="-872" y2="-861"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7556" x2="7556" y1="-2052" y2="-2190"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7556" x2="7556" y1="-2119" y2="-2119"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7556" x2="7567" y1="-2189" y2="-2209"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7581" x2="7559" y1="-2232" y2="-2232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7531" x2="7542" y1="-2232" y2="-2213"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7563" x2="7576" y1="-2217" y2="-2209"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7561" x2="7574" y1="-2212" y2="-2205"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7538" x2="7551" y1="-2205" y2="-2213"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7536" x2="7549" y1="-2210" y2="-2217"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7553" x2="7553" y1="-2240" y2="-2225"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7558" x2="7558" y1="-2240" y2="-2225"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7545" x2="7556" y1="-2209" y2="-2189"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7570" x2="7581" y1="-2213" y2="-2232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7554" x2="7531" y1="-2232" y2="-2232"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5274" x2="5274" y1="-2056" y2="-2150"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5274" x2="5274" y1="-2079" y2="-2079"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5275" x2="5286" y1="-2149" y2="-2169"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5300" x2="5277" y1="-2192" y2="-2192"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5250" x2="5261" y1="-2192" y2="-2173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5282" x2="5295" y1="-2177" y2="-2169"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5279" x2="5292" y1="-2172" y2="-2165"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5257" x2="5270" y1="-2165" y2="-2173"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5254" x2="5267" y1="-2170" y2="-2177"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5272" x2="5272" y1="-2200" y2="-2185"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5277" x2="5277" y1="-2200" y2="-2185"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5263" x2="5275" y1="-2169" y2="-2149"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5288" x2="5300" y1="-2173" y2="-2192"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5272" x2="5250" y1="-2192" y2="-2192"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5448" x2="5448" y1="-1945" y2="-1982"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5468" x2="5468" y1="-1945" y2="-1982"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5448" x2="5468" y1="-1982" y2="-1982"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5468" x2="5448" y1="-1945" y2="-1945"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5449" x2="5464" y1="-2007" y2="-1995"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="5457" x2="5457" y1="-1945" y2="-456"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7825" x2="7825" y1="-1951" y2="-1914"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7805" x2="7805" y1="-1951" y2="-1914"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7825" x2="7805" y1="-1914" y2="-1914"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7805" x2="7825" y1="-1951" y2="-1951"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7814" x2="7814" y1="-1914" y2="-644"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5177" x2="5177" y1="-853" y2="-825"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5158" x2="5158" y1="-853" y2="-825"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4924" x2="4939" y1="-1050" y2="-1039"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5219" x2="5233" y1="-915" y2="-903"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5236" x2="5236" y1="-993" y2="-965"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5217" x2="5217" y1="-993" y2="-965"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4978" x2="4978" y1="-373" y2="-335"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4958" x2="4958" y1="-373" y2="-335"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4978" x2="4958" y1="-335" y2="-335"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4958" x2="4978" y1="-373" y2="-373"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5227" x2="5227" y1="-887" y2="-921"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5227" x2="5227" y1="-1011" y2="-958"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5002" x2="4968" y1="-657" y2="-807"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7455" x2="7455" y1="-1217" y2="-1346"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7384" x2="7238" y1="-898" y2="-898"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7328" x2="7343" y1="-904" y2="-893"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="7297" x2="7297" y1="-769" y2="-898"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5047" x2="5047" y1="-2038" y2="-2038"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5036" x2="5036" y1="-1990" y2="-2027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5056" x2="5056" y1="-1990" y2="-2027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5036" x2="5056" y1="-2027" y2="-2027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5056" x2="5036" y1="-1990" y2="-1990"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5039" x2="5054" y1="-2046" y2="-2035"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5046" x2="5046" y1="-1990" y2="-1087"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4959" x2="4959" y1="-690" y2="-727"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4979" x2="4979" y1="-690" y2="-727"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4959" x2="4979" y1="-727" y2="-727"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4979" x2="4959" y1="-690" y2="-690"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4961" x2="4975" y1="-680" y2="-668"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="3" x1="4968" x2="4968" y1="-727" y2="-1027"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6262" x2="6717" y1="-2443" y2="-2443"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6681" x2="6695" y1="-2449" y2="-2437"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6294" x2="6308" y1="-2449" y2="-2437"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6426" x2="6426" y1="-2410" y2="-2443"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6426" x2="6274" y1="-2410" y2="-2410"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6249" x2="5894" y1="-2410" y2="-2410"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5925" x2="5939" y1="-2417" y2="-2405"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5457" x2="5882" y1="-2389" y2="-2389"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5907" x2="6057" y1="-2389" y2="-2389"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6057" x2="6057" y1="-2389" y2="-2410"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5473" x2="5488" y1="-2395" y2="-2384"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="6380" x2="6395" y1="-2417" y2="-2405"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5119" x2="5119" y1="-558" y2="-657"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,255,255)" stroke-width="0.5" x1="5112" x2="5126" y1="-650" y2="-639"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="5895" x2="6082" y1="-2255" y2="-2255"/>
   <line DF8003:Layer="主干线" stroke="rgb(255,0,0)" stroke-width="0.5" x1="5908" x2="5922" y1="-2262" y2="-2251"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8333" x2="4128" y1="0" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4128" x2="4128" y1="0" y2="-2970"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="8333" x2="8333" y1="-2970" y2="0"/>
   <line DF8003:Layer="图框（粗实线）" stroke="rgb(255,255,255)" stroke-width="0.5" x1="4128" x2="8333" y1="-2970" y2="-2970"/>
  </g><g id="Circle_Layer">
   <circle DF8003:Layer="0" cx="5205" cy="-657" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="5215" cy="-657" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="7658" cy="-1217" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="7669" cy="-1217" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="7129" cy="-769" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="0" cx="7119" cy="-769" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5358" cy="-2349" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5344" cy="-2349" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5355" cy="-2269" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5342" cy="-2269" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5353" cy="-2163" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5340" cy="-2163" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5600" cy="-1922" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5614" cy="-1922" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5601" cy="-1801" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5614" cy="-1801" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5604" cy="-1695" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5617" cy="-1695" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5746" cy="-1581" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5759" cy="-1581" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5594" cy="-1323" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5607" cy="-1323" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5654" cy="-1455" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5654" cy="-1468" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5750" cy="-1172" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5763" cy="-1172" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5658" cy="-1047" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5658" cy="-1060" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5747" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5760" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5655" cy="-761" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5655" cy="-774" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5133" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5120" cy="-886" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5258" cy="-812" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5258" cy="-825" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5355" cy="-812" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5356" cy="-825" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5537" cy="-762" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5537" cy="-775" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5577" cy="-622" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5590" cy="-622" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5577" cy="-513" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5590" cy="-513" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-2241" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-2228" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5205" cy="-1975" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5205" cy="-1989" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5071" cy="-2164" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5071" cy="-2151" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5154" cy="-1872" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5167" cy="-1872" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5154" cy="-1768" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5167" cy="-1768" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-1575" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-1588" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5222" cy="-1573" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5222" cy="-1586" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4840" cy="-1728" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4827" cy="-1728" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5148" cy="-1442" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5161" cy="-1442" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5148" cy="-1327" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5161" cy="-1327" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5148" cy="-1211" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5161" cy="-1211" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5148" cy="-1087" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5161" cy="-1086" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5375" cy="-577" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5375" cy="-564" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5264" cy="-573" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5263" cy="-560" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5086" cy="-339" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5086" cy="-352" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5156" cy="-297" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5156" cy="-310" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5332" cy="-363" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5345" cy="-363" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4599" cy="-1810" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4586" cy="-1810" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4599" cy="-1689" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4586" cy="-1689" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4599" cy="-1564" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4586" cy="-1564" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4593" cy="-1337" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4580" cy="-1337" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4576" cy="-2166" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4563" cy="-2166" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4572" cy="-2294" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4559" cy="-2295" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4568" cy="-2395" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4555" cy="-2395" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4808" cy="-2230" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4821" cy="-2230" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4838" cy="-492" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4825" cy="-492" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4834" cy="-614" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4821" cy="-615" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4627" cy="-808" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4614" cy="-808" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4857" cy="-548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4844" cy="-548" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5065" cy="-808" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5079" cy="-808" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4869" cy="-719" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4869" cy="-732" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4755" cy="-711" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4755" cy="-724" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4805" cy="-897" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4805" cy="-884" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4870" cy="-916" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4854" cy="-916" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5061" cy="-973" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5076" cy="-973" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4931" cy="-1120" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4931" cy="-1107" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4550" cy="-934" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4550" cy="-947" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4847" cy="-283" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4834" cy="-283" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4845" cy="-130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4832" cy="-130" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6033" cy="-2062" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6046" cy="-2062" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6424" cy="-2330" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6437" cy="-2330" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6610" cy="-2228" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6623" cy="-2228" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6456" cy="-2064" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6456" cy="-2077" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6442" cy="-1857" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6455" cy="-1857" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6618" cy="-1401" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6631" cy="-1401" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6498" cy="-1238" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6498" cy="-1251" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6718" cy="-1862" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6718" cy="-1852" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6927" cy="-1733" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6927" cy="-1746" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7088" cy="-1738" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7088" cy="-1752" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7026" cy="-2226" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7026" cy="-2214" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7302" cy="-2172" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7302" cy="-2158" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7166" cy="-2379" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7166" cy="-2392" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7358" cy="-2390" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7358" cy="-2377" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7473" cy="-2392" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7473" cy="-2379" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8011" cy="-2487" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8074" cy="-2488" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7234" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7221" cy="-1723" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7234" cy="-1622" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7221" cy="-1622" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7234" cy="-1346" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7221" cy="-1346" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7237" cy="-1209" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7224" cy="-1209" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7354" cy="-752" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7417" cy="-753" fill="none" r="7.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7612" cy="-1346" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7625" cy="-1346" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8032" cy="-1785" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8045" cy="-1785" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7688" cy="-1673" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7675" cy="-1673" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7678" cy="-1470" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7665" cy="-1470" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7681" cy="-1144" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7668" cy="-1144" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7684" cy="-1038" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7671" cy="-1038" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7684" cy="-867" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7671" cy="-867" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7687" cy="-644" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7674" cy="-644" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7915" cy="-1339" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7929" cy="-1339" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7907" cy="-947" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7920" cy="-947" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7943" cy="-741" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7956" cy="-741" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8038" cy="-1579" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8025" cy="-1579" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8038" cy="-1438" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8025" cy="-1438" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8032" cy="-1268" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8019" cy="-1268" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8029" cy="-1106" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8016" cy="-1106" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8022" cy="-980" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8009" cy="-980" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8022" cy="-829" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8009" cy="-830" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8022" cy="-651" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8009" cy="-652" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4452" cy="-933" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4452" cy="-946" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6033" cy="-1864" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6046" cy="-1864" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-2349" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5457" cy="-2269" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-2163" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-2056" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5205" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4909" cy="-1955" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4909" cy="-1968" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5071" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4910" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4708" cy="-2055" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4679" cy="-2166" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4679" cy="-2230" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4679" cy="-2294" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4679" cy="-2394" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5312" cy="-1679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5325" cy="-1679" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-876" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-888" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-2330" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-2226" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6457" cy="-2228" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-1403" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-962" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6498" cy="-1402" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6498" cy="-1299" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6719" cy="-2053" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6926" cy="-2051" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7028" cy="-2055" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7166" cy="-2057" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7302" cy="-2053" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7387" cy="-2053" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-2053" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7166" cy="-2328" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6926" cy="-1794" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7088" cy="-1797" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6718" cy="-1918" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6717" cy="-2466" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7026" cy="-2164" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8044" cy="-2392" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7389" cy="-1933" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7388" cy="-1723" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7385" cy="-1622" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7389" cy="-1348" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7388" cy="-1210" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7389" cy="-898" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-1988" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-1673" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-1579" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-1438" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-1266" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-1105" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-980" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-831" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-653" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-1469" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-1337" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7811" cy="-1145" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-1039" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-946" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-868" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7813" cy="-645" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-741" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-848" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4708" cy="-1810" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4708" cy="-1689" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4708" cy="-1563" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4708" cy="-1338" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1872" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5047" cy="-1767" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1679" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5117" cy="-1679" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5222" cy="-1679" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5286" cy="-1679" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1442" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1327" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1211" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1087" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5458" cy="-1800" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5458" cy="-1695" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5656" cy="-1579" fill="none" r="1.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5715" cy="-1581" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5458" cy="-1324" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-1173" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5659" cy="-1172" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5706" cy="-1172" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-887" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5537" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5656" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5716" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5355" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5258" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5166" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5457" cy="-622" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5457" cy="-1582" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5458" cy="-1922" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5458" cy="-514" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5376" cy="-456" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5264" cy="-456" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5196" cy="-456" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5157" cy="-455" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5086" cy="-457" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4969" cy="-458" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5156" cy="-364" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4967" cy="-492" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4969" cy="-615" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4967" cy="-549" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4968" cy="-809" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4806" cy="-808" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4755" cy="-808" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4669" cy="-808" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4968" cy="-973" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4931" cy="-1028" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4711" cy="-1220" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4724" cy="-1220" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4712" cy="-1150" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4725" cy="-1150" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4711" cy="-1078" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4724" cy="-1078" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4823" cy="-1029" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4550" cy="-1028" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4454" cy="-1029" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4822" cy="-1078" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5456" cy="-2319" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1615" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7388" cy="-1150" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6262" cy="-1858" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5895" cy="-1864" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5895" cy="-2062" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4913" cy="-1626" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4913" cy="-1639" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4913" cy="-1728" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7234" cy="-1480" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7221" cy="-1480" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7385" cy="-1481" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6511" cy="-2228" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5046" cy="-1728" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4968" cy="-918" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4869" cy="-808" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4860" cy="-808" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4806" cy="-825" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5180" cy="-364" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5481" cy="-511" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7388" cy="-1112" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7814" cy="-1785" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-955" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8161" cy="-797" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7917" cy="-742" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5206" cy="-2202" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5205" cy="-2189" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5166" cy="-771" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5166" cy="-784" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7494" cy="-2051" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7556" cy="-2052" fill="none" r="5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5177" cy="-840" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5157" cy="-840" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5376" cy="-2162" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5573" cy="-1800" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5389" cy="-2269" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5389" cy="-2349" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5205" cy="-2149" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5118" cy="-2196" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5072" cy="-2128" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5222" cy="-1610" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4931" cy="-1085" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4821" cy="-1150" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4749" cy="-1220" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4749" cy="-1151" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5112" cy="-1211" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5039" cy="-973" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4805" cy="-867" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="4880" cy="-549" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5085" cy="-376" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5305" cy="-363" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5375" cy="-538" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5552" cy="-513" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5549" cy="-621" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7474" cy="-2353" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7264" cy="-1622" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7266" cy="-1480" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7267" cy="-1346" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7581" cy="-1345" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7269" cy="-1209" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7717" cy="-1039" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7880" cy="-946" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="8057" cy="-1268" fill="none" r="3.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5226" cy="-887" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5226" cy="-1023" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5226" cy="-1036" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5236" cy="-980" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5216" cy="-980" fill="none" r="4.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5226" cy="-1001" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5356" cy="-851" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6927" cy="-1857" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7225" cy="-898" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7211" cy="-898" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7251" cy="-898" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7297" cy="-897" fill="none" r="2.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="7219" cy="-769" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5118" cy="-532" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5119" cy="-545" fill="none" r="12.5" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6094" cy="-2254" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="6107" cy="-2254" fill="none" r="12.5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="主干线" cx="5895" cy="-2255" fill="none" r="5" stroke="rgb(255,0,0)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="7550" cy="-134" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="7550" cy="-134" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="7550" cy="-153" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
   <circle DF8003:Layer="公变" cx="7550" cy="-153" fill="none" r="4" stroke="rgb(255,255,255)" stroke-width="0.5"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="支线:0.000000 0.000000" layer11="支线开关:0.000000 0.000000" layer12="支线油开关:0.000000 0.000000" layer13="公变:0.000000 0.000000" layer14="自变:0.000000 0.000000" layer15="图框（细实线）:0.000000 0.000000" layer16="标注、文字:0.000000 0.000000" layer17="图框（粗实线）:0.000000 0.000000" layer18="实线:0.000000 0.000000" layer19="粗线:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="10KV线路:0.000000 0.000000" layer21="虚线:0.000000 0.000000" layer22="Defpoints:0.000000 0.000000" layer23="设备（实线）:0.000000 0.000000" layer24="标注线层:0.000000 0.000000" layer25="文字层:0.000000 0.000000" layer26="10kV母线:0.000000 0.000000" layer27="0:0.000000 0.000000" layer28="主干线:0.000000 0.000000" layer29="主干线开关:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer30="主干线油开关:0.000000 0.000000" layer31="次干线:0.000000 0.000000" layer32="次干线开关:0.000000 0.000000" layer33="次干线油开关:0.000000 0.000000" layer34="支线:0.000000 0.000000" layer35="支线开关:0.000000 0.000000" layer36="支线油开关:0.000000 0.000000" layer37="公变:0.000000 0.000000" layer38="自变:0.000000 0.000000" layer39="图框（细实线）:0.000000 0.000000" layer4="主干线:0.000000 0.000000" layer40="标注、文字:0.000000 0.000000" layer41="图框（粗实线）:0.000000 0.000000" layer42="实线:0.000000 0.000000" layer43="粗线:0.000000 0.000000" layer44="10KV线路:0.000000 0.000000" layer45="虚线:0.000000 0.000000" layer46="Defpoints:0.000000 0.000000" layer47="设备（实线）:0.000000 0.000000" layer48="标注线层:0.000000 0.000000" layer49="文字层:0.000000 0.000000" layer5="主干线开关:0.000000 0.000000" layer50="10kV母线:0.000000 0.000000" layer6="主干线油开关:0.000000 0.000000" layer7="次干线:0.000000 0.000000" layer8="次干线开关:0.000000 0.000000" layer9="次干线油开关:0.000000 0.000000" layerN="51"/>
</svg>