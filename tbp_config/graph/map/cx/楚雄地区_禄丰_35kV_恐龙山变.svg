<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-92" aopId="256" id="thSvg" viewBox="3115 -1198 1985 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape32">
    <ellipse cx="7" cy="23" rx="6.5" ry="6" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="8" x2="8" y1="17" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="28" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="30" x2="29" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="29" y1="12" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="29" x2="8" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="29" x2="29" y1="30" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="34" x2="24" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="31" x2="27" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="30" x2="28" y1="44" y2="44"/>
    <rect height="14" stroke-width="0.571429" width="6" x="26" y="16"/>
    <circle cx="14" cy="28" r="6.5" stroke-width="0.45993"/>
    <circle cx="7" cy="32" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape34">
    <ellipse cx="15" cy="26" rx="6" ry="6.5" stroke-width="0.45993"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="37" y1="28" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="34" x2="35" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="35" x2="35" y1="13" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="35" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="35" x2="35" y1="32" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="30" x2="40" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.258981" x1="33" x2="38" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.212121" x1="34" x2="37" y1="46" y2="46"/>
    <rect height="14" stroke-width="0.571429" width="6" x="33" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.571429" x1="15" x2="15" y1="19" y2="5"/>
    <circle cx="7" cy="30" r="6.5" stroke-width="0.45993"/>
    <circle cx="14" cy="34" r="6.5" stroke-width="0.45993"/>
    <circle cx="21" cy="29" r="6.5" stroke-width="0.45993"/>
   </symbol>
   <symbol id="lightningRod:shape110">
    <ellipse cx="13" cy="51" rx="13" ry="12.5" stroke-width="0.265306"/>
    <circle cx="13" cy="68" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="45" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="49" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="53" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="75" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="67" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="37" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="49" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="42" x2="30" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="35" x2="37" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="33" x2="39" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="39" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="36" y1="23" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="32" y2="32"/>
   </symbol>
   <symbol id="lightningRod:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="17" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="52" x2="52" y1="52" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="19" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="26" x2="26" y1="16" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="43" x2="43" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="8" x2="8" y1="2" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="63" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="64" y2="64"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="32"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.501492" x1="26" x2="26" y1="74" y2="24"/>
    <polyline fill="none" points="43,42 44,42 45,42 45,42 46,43 46,43 47,44 47,44 48,45 48,45 48,46 48,47 49,48 49,49 49,49 48,50 48,51 48,52 48,52 47,53 47,53 46,54 46,54 45,55 45,55 44,55 43,55 "/>
    <polyline fill="none" points="43,29 44,29 45,29 45,29 46,30 46,30 47,31 47,31 48,32 48,32 48,33 48,34 49,35 49,36 49,36 48,37 48,38 48,39 48,39 47,40 47,40 46,41 46,41 45,42 45,42 44,42 43,42 "/>
    <polyline fill="none" points="43,17 44,17 45,17 45,17 46,18 46,18 47,19 47,19 48,20 48,20 48,21 48,22 49,23 49,24 49,24 48,25 48,26 48,27 48,27 47,28 47,28 46,29 46,29 45,30 45,30 44,30 43,30 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="63" y2="55"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline fill="none" points="1,13 9,1 17,13 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape4_0">
    <circle cx="35" cy="29" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="57" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="85" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="88" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="46" x2="30" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="30" y1="33" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape4_1">
    <ellipse cx="35" cy="60" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1211" width="1995" x="3110" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3116" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3116" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3116" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54097">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -786.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9562" ObjectName="SW-CX_KLS.CX_KLS_3511SW"/>
     <cge:Meas_Ref ObjectId="54097"/>
    <cge:TPSR_Ref TObjectID="9562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54171">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -781.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9587" ObjectName="SW-CX_KLS.CX_KLS_3522SW"/>
     <cge:Meas_Ref ObjectId="54171"/>
    <cge:TPSR_Ref TObjectID="9587"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54169">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9585" ObjectName="SW-CX_KLS.CX_KLS_3526SW"/>
     <cge:Meas_Ref ObjectId="54169"/>
    <cge:TPSR_Ref TObjectID="9585"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54058">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9550" ObjectName="SW-CX_KLS.CX_KLS_3529SW"/>
     <cge:Meas_Ref ObjectId="54058"/>
    <cge:TPSR_Ref TObjectID="9550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9591" ObjectName="SW-CX_KLS.CX_KLS_3121SW"/>
     <cge:Meas_Ref ObjectId="54184"/>
    <cge:TPSR_Ref TObjectID="9591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54086">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -705.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9555" ObjectName="SW-CX_KLS.CX_KLS_3022SW"/>
     <cge:Meas_Ref ObjectId="54086"/>
    <cge:TPSR_Ref TObjectID="9555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -421.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9553" ObjectName="SW-CX_KLS.CX_KLS_0901SW"/>
     <cge:Meas_Ref ObjectId="54080"/>
    <cge:TPSR_Ref TObjectID="9553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -535.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -1028.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9548" ObjectName="SW-CX_KLS.CX_KLS_3519SW"/>
     <cge:Meas_Ref ObjectId="54054"/>
    <cge:TPSR_Ref TObjectID="9548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54096">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -805.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9561" ObjectName="SW-CX_KLS.CX_KLS_35117SW"/>
     <cge:Meas_Ref ObjectId="54096"/>
    <cge:TPSR_Ref TObjectID="9561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4463.000000 -991.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9551" ObjectName="SW-CX_KLS.CX_KLS_35297SW"/>
     <cge:Meas_Ref ObjectId="54059"/>
    <cge:TPSR_Ref TObjectID="9551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -955.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54182">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4414.000000 -704.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9589" ObjectName="SW-CX_KLS.CX_KLS_3122SW"/>
     <cge:Meas_Ref ObjectId="54182"/>
    <cge:TPSR_Ref TObjectID="9589"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9554" ObjectName="SW-CX_KLS.CX_KLS_0902SW"/>
     <cge:Meas_Ref ObjectId="54083"/>
    <cge:TPSR_Ref TObjectID="9554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -539.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54119">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9571" ObjectName="SW-CX_KLS.CX_KLS_0721SW"/>
     <cge:Meas_Ref ObjectId="54119"/>
    <cge:TPSR_Ref TObjectID="9571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9574" ObjectName="SW-CX_KLS.CX_KLS_0731SW"/>
     <cge:Meas_Ref ObjectId="54128"/>
    <cge:TPSR_Ref TObjectID="9574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -346.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9577" ObjectName="SW-CX_KLS.CX_KLS_0741SW"/>
     <cge:Meas_Ref ObjectId="54137"/>
    <cge:TPSR_Ref TObjectID="9577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54202">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9596" ObjectName="SW-CX_KLS.CX_KLS_0826SW"/>
     <cge:Meas_Ref ObjectId="54202"/>
    <cge:TPSR_Ref TObjectID="9596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54203">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9597" ObjectName="SW-CX_KLS.CX_KLS_0822SW"/>
     <cge:Meas_Ref ObjectId="54203"/>
    <cge:TPSR_Ref TObjectID="9597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54118">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9570" ObjectName="SW-CX_KLS.CX_KLS_0726SW"/>
     <cge:Meas_Ref ObjectId="54118"/>
    <cge:TPSR_Ref TObjectID="9570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54109">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -239.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9567" ObjectName="SW-CX_KLS.CX_KLS_0716SW"/>
     <cge:Meas_Ref ObjectId="54109"/>
    <cge:TPSR_Ref TObjectID="9567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9573" ObjectName="SW-CX_KLS.CX_KLS_0736SW"/>
     <cge:Meas_Ref ObjectId="54127"/>
    <cge:TPSR_Ref TObjectID="9573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54136">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -241.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9576" ObjectName="SW-CX_KLS.CX_KLS_0746SW"/>
     <cge:Meas_Ref ObjectId="54136"/>
    <cge:TPSR_Ref TObjectID="9576"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3713.000000 -344.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9568" ObjectName="SW-CX_KLS.CX_KLS_0711SW"/>
     <cge:Meas_Ref ObjectId="54110"/>
    <cge:TPSR_Ref TObjectID="9568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -1037.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9549" ObjectName="SW-CX_KLS.CX_KLS_35197SW"/>
     <cge:Meas_Ref ObjectId="54055"/>
    <cge:TPSR_Ref TObjectID="9549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54108">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3747.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9566" ObjectName="SW-CX_KLS.CX_KLS_07167SW"/>
     <cge:Meas_Ref ObjectId="54108"/>
    <cge:TPSR_Ref TObjectID="9566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54095">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -946.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9560" ObjectName="SW-CX_KLS.CX_KLS_3516SW"/>
     <cge:Meas_Ref ObjectId="54095"/>
    <cge:TPSR_Ref TObjectID="9560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9563" ObjectName="SW-CX_KLS.CX_KLS_35160SW"/>
     <cge:Meas_Ref ObjectId="54101"/>
    <cge:TPSR_Ref TObjectID="9563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.000000 -953.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9559" ObjectName="SW-CX_KLS.CX_KLS_35167SW"/>
     <cge:Meas_Ref ObjectId="54094"/>
    <cge:TPSR_Ref TObjectID="9559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54170">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9586" ObjectName="SW-CX_KLS.CX_KLS_35227SW"/>
     <cge:Meas_Ref ObjectId="54170"/>
    <cge:TPSR_Ref TObjectID="9586"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54168">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4544.000000 -893.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9584" ObjectName="SW-CX_KLS.CX_KLS_35267SW"/>
     <cge:Meas_Ref ObjectId="54168"/>
    <cge:TPSR_Ref TObjectID="9584"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.000000 -831.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3970.000000 -650.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9558" ObjectName="SW-CX_KLS.CX_KLS_30117SW"/>
     <cge:Meas_Ref ObjectId="54091"/>
    <cge:TPSR_Ref TObjectID="9558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54090">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9557" ObjectName="SW-CX_KLS.CX_KLS_3011SW"/>
     <cge:Meas_Ref ObjectId="54090"/>
    <cge:TPSR_Ref TObjectID="9557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54087">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4541.000000 -648.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9556" ObjectName="SW-CX_KLS.CX_KLS_30227SW"/>
     <cge:Meas_Ref ObjectId="54087"/>
    <cge:TPSR_Ref TObjectID="9556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4099.000000 -624.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9590" ObjectName="SW-CX_KLS.CX_KLS_35217SW"/>
     <cge:Meas_Ref ObjectId="54183"/>
    <cge:TPSR_Ref TObjectID="9590"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4458.000000 -625.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9583" ObjectName="SW-CX_KLS.CX_KLS_0011SW"/>
     <cge:Meas_Ref ObjectId="54161"/>
    <cge:TPSR_Ref TObjectID="9583"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9580" ObjectName="SW-CX_KLS.CX_KLS_0751SW"/>
     <cge:Meas_Ref ObjectId="54146"/>
    <cge:TPSR_Ref TObjectID="9580"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54145">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -240.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9579" ObjectName="SW-CX_KLS.CX_KLS_0756SW"/>
     <cge:Meas_Ref ObjectId="54145"/>
    <cge:TPSR_Ref TObjectID="9579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54228">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4513.000000 -425.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9601" ObjectName="SW-CX_KLS.CX_KLS_0122SW"/>
     <cge:Meas_Ref ObjectId="54228"/>
    <cge:TPSR_Ref TObjectID="9601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54227">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4383.000000 -427.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9600" ObjectName="SW-CX_KLS.CX_KLS_0121SW"/>
     <cge:Meas_Ref ObjectId="54227"/>
    <cge:TPSR_Ref TObjectID="9600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54193">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -245.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9593" ObjectName="SW-CX_KLS.CX_KLS_0816SW"/>
     <cge:Meas_Ref ObjectId="54193"/>
    <cge:TPSR_Ref TObjectID="9593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54194">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -350.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9594" ObjectName="SW-CX_KLS.CX_KLS_0812SW"/>
     <cge:Meas_Ref ObjectId="54194"/>
    <cge:TPSR_Ref TObjectID="9594"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -39.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -37.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -39.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 -37.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4584.000000 -1150.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4014.000000 -1131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4323.000000 -36.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -41.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e004c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -782.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0d500">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3762.000000 -309.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e531e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3973.000000 -627.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e5c3c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -601.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f87180">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -929.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f87bd0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -1015.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f88660">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4466.000000 -967.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f890f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4547.000000 -869.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f89b80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4635.000000 -804.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8a610">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4545.000000 -764.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8b0a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4461.000000 -601.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8bb30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4543.000000 -624.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f8c830">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4065.000000 -856.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1ddcfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-568 4021,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9602@0" ObjectIDZND0="9582@1" Pin0InfoVect0LinkObjId="SW-54159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e53e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-568 4021,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddf530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-305 4776,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9598@0" ObjectIDZND0="9596@1" Pin0InfoVect0LinkObjId="SW-54202_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54208_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-305 4776,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddf720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-412 4776,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9597@1" Pin0InfoVect0LinkObjId="SW-54203_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e39be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-412 4776,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddf910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-353 4776,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9597@0" ObjectIDZND0="9598@1" Pin0InfoVect0LinkObjId="SW-54208_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54203_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-353 4776,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddfb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-158 4798,-158 4798,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9596@x" ObjectIDZND0="g_1f33470@0" Pin0InfoVect0LinkObjId="g_1f33470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-158 4798,-158 4798,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ddfcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-158 4776,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f33470@0" ObjectIDND1="9596@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f33470_0" Pin1InfoVect1LinkObjId="SW-54202_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-158 4776,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de1050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-305 3855,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9572@0" ObjectIDZND0="9570@1" Pin0InfoVect0LinkObjId="SW-54118_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54124_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-305 3855,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-412 3855,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9571@1" Pin0InfoVect0LinkObjId="SW-54119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-412 3855,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-353 3855,-332 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9571@0" ObjectIDZND0="9572@1" Pin0InfoVect0LinkObjId="SW-54124_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-353 3855,-332 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de1620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-158 3877,-158 3877,-146 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9570@x" ObjectIDZND0="g_1f30b60@0" Pin0InfoVect0LinkObjId="g_1f30b60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54118_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-158 3877,-158 3877,-146 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1de1810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-158 3855,-66 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f30b60@0" ObjectIDND1="9570@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f30b60_0" Pin1InfoVect1LinkObjId="SW-54118_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-158 3855,-66 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1ded7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-479 3732,-479 3732,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1df9e10@0" ObjectIDND1="9553@x" ObjectIDZND0="g_1dd4550@0" Pin0InfoVect0LinkObjId="g_1dd4550_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1df9e10_0" Pin1InfoVect1LinkObjId="SW-54080_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-479 3732,-479 3732,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1defb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-248 3855,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9570@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f30b60@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f30b60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54118_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-248 3855,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-303 4020,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9575@0" ObjectIDZND0="9573@1" Pin0InfoVect0LinkObjId="SW-54127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-303 4020,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-412 4020,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9574@1" Pin0InfoVect0LinkObjId="SW-54128_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-412 4020,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-351 4020,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9574@0" ObjectIDZND0="9575@1" Pin0InfoVect0LinkObjId="SW-54133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-351 4020,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df1fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-156 4042,-156 4042,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9573@x" ObjectIDZND0="g_1f31910@0" Pin0InfoVect0LinkObjId="g_1f31910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-156 4042,-156 4042,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df21f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-156 4020,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f31910@0" ObjectIDND1="9573@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f31910_0" Pin1InfoVect1LinkObjId="SW-54127_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-156 4020,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df2410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4020,-246 4020,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9573@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f31910@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f31910_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4020,-246 4020,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df2630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4776,-248 4776,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9596@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f33470@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f33470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54202_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4776,-248 4776,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df4430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-412 3722,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9568@1" Pin0InfoVect0LinkObjId="SW-54110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-412 3722,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-349 3722,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9568@0" ObjectIDZND0="9569@1" Pin0InfoVect0LinkObjId="SW-54115_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-349 3722,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df7650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-303 4180,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9578@0" ObjectIDZND0="9576@1" Pin0InfoVect0LinkObjId="SW-54136_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-303 4180,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df7870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-412 4180,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9577@1" Pin0InfoVect0LinkObjId="SW-54137_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-412 4180,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df7a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-351 4180,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9577@0" ObjectIDZND0="9578@1" Pin0InfoVect0LinkObjId="SW-54142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54137_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-351 4180,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df7cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-156 4202,-156 4202,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9576@x" ObjectIDZND0="g_1f326c0@0" Pin0InfoVect0LinkObjId="g_1f326c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-156 4202,-156 4202,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df7ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-156 4180,-64 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f326c0@0" ObjectIDND1="9576@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f326c0_0" Pin1InfoVect1LinkObjId="SW-54136_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-156 4180,-64 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1df80f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4180,-246 4180,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9576@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f326c0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f326c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54136_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4180,-246 4180,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e04bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4472,-985 4472,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f88660@0" ObjectIDZND0="9551@0" Pin0InfoVect0LinkObjId="SW-54059_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f88660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4472,-985 4472,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e0a1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1050 4659,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e081a0@1" ObjectIDZND0="g_1e079e0@1" Pin0InfoVect0LinkObjId="g_1e079e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e081a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1050 4659,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-1083 4566,-1083 4566,-1066 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9585@x" ObjectIDND2="9584@x" ObjectIDZND0="g_1f2fdb0@0" Pin0InfoVect0LinkObjId="g_1f2fdb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54169_0" Pin1InfoVect2LinkObjId="SW-54168_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-1083 4566,-1083 4566,-1066 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e0a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-1123 4593,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_1f2fdb0@0" ObjectIDZND1="9585@x" ObjectIDZND2="9584@x" Pin0InfoVect0LinkObjId="g_1f2fdb0_0" Pin0InfoVect1LinkObjId="SW-54169_0" Pin0InfoVect2LinkObjId="SW-54168_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-1123 4593,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e12b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-488 4864,-488 4864,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1e16920@0" ObjectIDND1="9554@x" ObjectIDZND0="g_1f36d90@0" Pin0InfoVect0LinkObjId="g_1f36d90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e16920_0" Pin1InfoVect1LinkObjId="SW-54083_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-488 4864,-488 4864,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-989 4451,-948 4659,-948 4659,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="9550@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54058_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-989 4451,-948 4659,-948 4659,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e1ad60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-996 4659,-1007 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_1e079e0@0" Pin0InfoVect0LinkObjId="g_1e079e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-996 4659,-1007 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e1afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-760 4023,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9562@0" Pin0InfoVect0LinkObjId="SW-54097_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-760 4023,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e34eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-760 4593,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9547@0" ObjectIDZND0="9587@0" Pin0InfoVect0LinkObjId="SW-54171_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e56d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-760 4593,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e36a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-237 3722,-220 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9567@x" ObjectIDND1="9566@x" ObjectIDZND0="g_1e0df30@0" Pin0InfoVect0LinkObjId="g_1e0df30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54109_0" Pin1InfoVect1LinkObjId="SW-54108_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-237 3722,-220 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e36cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-301 3722,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9569@0" ObjectIDZND0="9567@1" Pin0InfoVect0LinkObjId="SW-54109_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54115_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-301 3722,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e36f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-244 3722,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9567@0" ObjectIDZND0="g_1e0df30@0" ObjectIDZND1="9566@x" Pin0InfoVect0LinkObjId="g_1e0df30_0" Pin0InfoVect1LinkObjId="SW-54108_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-244 3722,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e371b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-1104 4023,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-1104 4023,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e39980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-1042 4093,-1033 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9549@0" ObjectIDZND0="g_1f87bd0@0" Pin0InfoVect0LinkObjId="g_1f87bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-1042 4093,-1033 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e39be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-389 4987,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="17339@0" Pin0InfoVect0LinkObjId="g_1e3a560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-389 4987,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e39e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-260 4987,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_1e170e0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e170e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-260 4987,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e3a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-329 4987,-353 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-329 4987,-353 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3a300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-488 4830,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f36d90@0" ObjectIDND1="g_1e16920@0" ObjectIDZND0="9554@1" Pin0InfoVect0LinkObjId="SW-54083_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f36d90_0" Pin1InfoVect1LinkObjId="g_1e16920_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-488 4830,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3a560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-432 4830,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9554@0" ObjectIDZND0="17339@0" Pin0InfoVect0LinkObjId="g_1e39be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-432 4830,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e3a7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-599 4830,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1e152e0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e152e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-599 4830,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3aa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-544 4830,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1e16920@1" Pin0InfoVect0LinkObjId="g_1e16920_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-544 4830,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3ac80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4830,-499 4830,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1e16920@0" ObjectIDZND0="g_1f36d90@0" ObjectIDZND1="9554@x" Pin0InfoVect0LinkObjId="g_1f36d90_0" Pin0InfoVect1LinkObjId="SW-54083_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e16920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4830,-499 4830,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-237 3756,-237 3756,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1e0df30@0" ObjectIDND1="9567@x" ObjectIDZND0="9566@0" Pin0InfoVect0LinkObjId="SW-54108_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1e0df30_0" Pin1InfoVect1LinkObjId="SW-54109_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-237 3756,-237 3756,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3d6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3756,-281 3756,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9566@1" ObjectIDZND0="g_1e0d500@0" Pin0InfoVect0LinkObjId="g_1e0d500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54108_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3756,-281 3756,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3d910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-479 3698,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1df9e10@0" ObjectIDND1="g_1dd4550@0" ObjectIDZND0="9553@1" Pin0InfoVect0LinkObjId="SW-54080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1df9e10_0" Pin1InfoVect1LinkObjId="g_1dd4550_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-479 3698,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-426 3698,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9553@0" ObjectIDZND0="17338@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-426 3698,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e3ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-590 3698,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1df8910@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1df8910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-590 3698,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3e030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-540 3698,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1df9e10@1" Pin0InfoVect0LinkObjId="g_1df9e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-540 3698,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e3e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3698,-492 3698,-479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1df9e10@0" ObjectIDZND0="g_1dd4550@0" ObjectIDZND1="9553@x" Pin0InfoVect0LinkObjId="g_1dd4550_0" Pin0InfoVect1LinkObjId="SW-54080_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1df9e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3698,-492 3698,-479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-799 3982,-810 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e004c0@0" ObjectIDZND0="9561@0" Pin0InfoVect0LinkObjId="SW-54096_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e004c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-799 3982,-810 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3e750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3982,-846 3982,-853 4022,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9561@1" ObjectIDZND0="9564@x" ObjectIDZND1="9562@x" Pin0InfoVect0LinkObjId="SW-54104_0" Pin0InfoVect1LinkObjId="SW-54097_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54096_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3982,-846 3982,-853 4022,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3e9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-760 4151,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9591@1" Pin0InfoVect0LinkObjId="SW-54184_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-760 4151,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-760 4423,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9547@0" ObjectIDZND0="9589@1" Pin0InfoVect0LinkObjId="SW-54182_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e56d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-760 4423,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1069 4122,-1089 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9548@1" ObjectIDZND0="g_1f35230@0" ObjectIDZND1="g_1f2f070@0" ObjectIDZND2="9549@x" Pin0InfoVect0LinkObjId="g_1f35230_0" Pin0InfoVect1LinkObjId="g_1f2f070_0" Pin0InfoVect2LinkObjId="SW-54055_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54054_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1069 4122,-1089 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e3f0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1089 4093,-1089 4093,-1078 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1f35230@0" ObjectIDND1="g_1f2f070@0" ObjectIDND2="9548@x" ObjectIDZND0="9549@1" Pin0InfoVect0LinkObjId="SW-54055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f35230_0" Pin1InfoVect1LinkObjId="g_1f2f070_0" Pin1InfoVect2LinkObjId="SW-54054_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1089 4093,-1089 4093,-1078 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e418a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-1095 4023,-1013 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f34480@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f34480_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-1095 4023,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e41b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1033 4122,-1014 4023,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9548@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f34480@0" ObjectIDZND2="9560@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f34480_0" Pin0InfoVect2LinkObjId="SW-54095_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1033 4122,-1014 4023,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e41d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-951 4023,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9560@0" ObjectIDZND0="9564@x" ObjectIDZND1="9563@x" Pin0InfoVect0LinkObjId="SW-54104_0" Pin0InfoVect1LinkObjId="SW-54101_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54095_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-951 4023,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e44530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-936 4071,-936 4071,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9564@x" ObjectIDND1="9560@x" ObjectIDZND0="9563@1" Pin0InfoVect0LinkObjId="SW-54101_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54104_0" Pin1InfoVect1LinkObjId="SW-54095_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-936 4071,-936 4071,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e44790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4071,-888 4071,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9563@0" ObjectIDZND0="g_1f8c830@0" Pin0InfoVect0LinkObjId="g_1f8c830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4071,-888 4071,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e449f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-897 4023,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9564@1" ObjectIDZND0="9560@x" ObjectIDZND1="9563@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54101_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-897 4023,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e44c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-827 4023,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9562@1" ObjectIDZND0="9564@x" ObjectIDZND1="9561@x" Pin0InfoVect0LinkObjId="SW-54104_0" Pin0InfoVect1LinkObjId="SW-54096_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54097_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-827 4023,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e44eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-853 4023,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9562@x" ObjectIDND1="9561@x" ObjectIDZND0="9564@0" Pin0InfoVect0LinkObjId="SW-54104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54097_0" Pin1InfoVect1LinkObjId="SW-54096_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-853 4023,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e47680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-947 3983,-958 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f87180@0" ObjectIDZND0="9559@0" Pin0InfoVect0LinkObjId="SW-54094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f87180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-947 3983,-958 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e478e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3983,-994 3983,-1001 4023,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9559@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1f34480@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f34480_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3983,-994 3983,-1001 4023,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e47b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-987 4023,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9560@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1f34480@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f34480_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54095_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-987 4023,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e47da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-1001 4023,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9560@x" ObjectIDND1="9559@x" ObjectIDZND0="0@x" ObjectIDZND1="g_1f34480@0" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f34480_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54095_0" Pin1InfoVect1LinkObjId="SW-54094_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-1001 4023,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4a570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4551,-782 4551,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f8a610@0" ObjectIDZND0="9586@0" Pin0InfoVect0LinkObjId="SW-54170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f8a610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4551,-782 4551,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-887 4553,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f890f0@0" ObjectIDZND0="9584@0" Pin0InfoVect0LinkObjId="SW-54168_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f890f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-887 4553,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4aa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-934 4553,-941 4593,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9584@1" ObjectIDZND0="0@x" ObjectIDZND1="g_1f2fdb0@0" ObjectIDZND2="9585@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f2fdb0_0" Pin0InfoVect2LinkObjId="SW-54169_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-934 4553,-941 4593,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-1083 4593,-941 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f2fdb0@0" ObjectIDZND0="9585@x" ObjectIDZND1="9584@x" Pin0InfoVect0LinkObjId="SW-54169_0" Pin0InfoVect1LinkObjId="SW-54168_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f2fdb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-1083 4593,-941 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-941 4593,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f2fdb0@0" ObjectIDND2="9584@x" ObjectIDZND0="9585@1" Pin0InfoVect0LinkObjId="SW-54169_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f2fdb0_0" Pin1InfoVect2LinkObjId="SW-54168_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-941 4593,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-822 4593,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9587@1" ObjectIDZND0="9588@x" ObjectIDZND1="9586@x" Pin0InfoVect0LinkObjId="SW-54173_0" Pin0InfoVect1LinkObjId="SW-54170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54171_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-822 4593,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-836 4551,-836 4551,-829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9588@x" ObjectIDND1="9587@x" ObjectIDZND0="9586@1" Pin0InfoVect0LinkObjId="SW-54170_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="SW-54171_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-836 4551,-836 4551,-829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4db80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-848 4593,-836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9588@0" ObjectIDZND0="9587@x" ObjectIDZND1="9586@x" Pin0InfoVect0LinkObjId="SW-54171_0" Pin0InfoVect1LinkObjId="SW-54170_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-848 4593,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e502f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-884 4641,-884 4641,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="9588@x" ObjectIDND1="9585@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54173_0" Pin1InfoVect1LinkObjId="SW-54169_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-884 4641,-884 4641,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e50550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4641,-836 4641,-822 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1f89b80@0" Pin0InfoVect0LinkObjId="g_1f89b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4641,-836 4641,-822 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e507b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-894 4593,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9585@0" ObjectIDZND0="9588@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-54173_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54169_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-894 4593,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e50a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-884 4593,-875 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9585@x" ObjectIDND1="0@x" ObjectIDZND0="9588@1" Pin0InfoVect0LinkObjId="SW-54173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54169_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-884 4593,-875 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e53c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-644 3979,-655 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e531e0@0" ObjectIDZND0="9558@0" Pin0InfoVect0LinkObjId="SW-54091_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e531e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-644 3979,-655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e53e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3979,-691 3979,-698 4023,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="9558@1" ObjectIDZND0="9602@x" ObjectIDZND1="9557@x" Pin0InfoVect0LinkObjId="g_1e56aa0_0" Pin0InfoVect1LinkObjId="SW-54090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54091_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3979,-691 3979,-698 4023,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e565e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-648 4023,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="9602@1" ObjectIDZND0="9558@x" ObjectIDZND1="9557@x" Pin0InfoVect0LinkObjId="SW-54091_0" Pin0InfoVect1LinkObjId="SW-54090_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e53e70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-648 4023,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e56840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-760 4023,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9546@0" ObjectIDZND0="9557@1" Pin0InfoVect0LinkObjId="SW-54090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-760 4023,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e56aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4023,-714 4023,-698 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="9557@0" ObjectIDZND0="9602@x" ObjectIDZND1="9558@x" Pin0InfoVect0LinkObjId="g_1e53e70_0" Pin0InfoVect1LinkObjId="SW-54091_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4023,-714 4023,-698 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e56d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-746 4593,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9555@1" ObjectIDZND0="9547@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54086_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-746 4593,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e594d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-642 4549,-653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f8bb30@0" ObjectIDZND0="9556@0" Pin0InfoVect0LinkObjId="SW-54087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f8bb30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-642 4549,-653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e59730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4592,-696 4550,-696 4550,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="9555@x" ObjectIDZND0="9556@1" Pin0InfoVect0LinkObjId="SW-54087_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4592,-696 4550,-696 4550,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e59990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-652 4593,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" EndDevType1="switch" ObjectIDZND0="9555@x" ObjectIDZND1="9556@x" Pin0InfoVect0LinkObjId="SW-54086_0" Pin0InfoVect1LinkObjId="SW-54087_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-652 4593,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e59bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4593,-696 4593,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="9556@x" ObjectIDZND0="9555@0" Pin0InfoVect0LinkObjId="SW-54086_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54087_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4593,-696 4593,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e5cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-618 4108,-629 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1e5c3c0@0" ObjectIDZND0="9590@0" Pin0InfoVect0LinkObjId="SW-54183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e5c3c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-618 4108,-629 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e5d050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-665 4108,-672 4152,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9590@1" ObjectIDZND0="9592@x" ObjectIDZND1="9591@x" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="SW-54184_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-665 4108,-672 4152,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e5d2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-714 4151,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9591@0" ObjectIDZND0="9592@x" ObjectIDZND1="9590@x" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="SW-54183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-714 4151,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e5d510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-672 4151,-660 4269,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9591@x" ObjectIDND1="9590@x" ObjectIDZND0="9592@1" Pin0InfoVect0LinkObjId="SW-54186_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54184_0" Pin1InfoVect1LinkObjId="SW-54183_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-672 4151,-660 4269,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e5fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4467,-619 4467,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f8b0a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f8b0a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4467,-619 4467,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e5fec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4467,-666 4467,-673 4424,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="9592@x" ObjectIDZND1="9589@x" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="SW-54182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4467,-666 4467,-673 4424,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e60120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-709 4423,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="9589@0" ObjectIDZND0="9592@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-709 4423,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e60380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4423,-673 4423,-660 4296,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9589@x" ObjectIDND1="0@x" ObjectIDZND0="9592@0" Pin0InfoVect0LinkObjId="SW-54186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54182_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4423,-673 4423,-660 4296,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2e120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1089 4122,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9548@x" ObjectIDND1="9549@x" ObjectIDZND0="g_1f35230@0" ObjectIDZND1="g_1f2f070@0" Pin0InfoVect0LinkObjId="g_1f35230_0" Pin0InfoVect1LinkObjId="g_1f2f070_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="SW-54055_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1089 4122,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2e310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1096 4159,-1096 4159,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9548@x" ObjectIDND1="9549@x" ObjectIDND2="g_1f2f070@0" ObjectIDZND0="g_1f35230@0" Pin0InfoVect0LinkObjId="g_1f35230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-54054_0" Pin1InfoVect1LinkObjId="SW-54055_0" Pin1InfoVect2LinkObjId="g_1f2f070_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1096 4159,-1096 4159,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-1025 4451,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9550@1" ObjectIDZND0="g_1f2e500@0" Pin0InfoVect0LinkObjId="g_1f2e500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54058_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-1025 4451,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2ee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-1077 4451,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f2e500@1" ObjectIDZND0="g_1e00ef0@0" Pin0InfoVect0LinkObjId="g_1e00ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f2e500_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-1077 4451,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1096 4122,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1f35230@0" ObjectIDND1="9548@x" ObjectIDND2="9549@x" ObjectIDZND0="g_1f2f070@0" Pin0InfoVect0LinkObjId="g_1f2f070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f35230_0" Pin1InfoVect1LinkObjId="SW-54054_0" Pin1InfoVect2LinkObjId="SW-54055_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1096 4122,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f2fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4122,-1139 4122,-1149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f2f070@1" ObjectIDZND0="g_1dfa5b0@0" Pin0InfoVect0LinkObjId="g_1dfa5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f2f070_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4122,-1139 4122,-1149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f34220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3968,-1080 3968,-1095 4023,-1095 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f34480@0" ObjectIDZND0="9560@x" ObjectIDZND1="9559@x" ObjectIDZND2="9548@x" Pin0InfoVect0LinkObjId="SW-54095_0" Pin0InfoVect1LinkObjId="SW-54094_0" Pin0InfoVect2LinkObjId="SW-54054_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f34480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3968,-1080 3968,-1095 4023,-1095 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd3720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-412 4021,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9583@0" Pin0InfoVect0LinkObjId="SW-54161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-412 4021,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1dd3980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-469 4021,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9583@1" ObjectIDZND0="9582@0" Pin0InfoVect0LinkObjId="SW-54159_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-469 4021,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f551a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-302 4332,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9581@0" ObjectIDZND0="9579@1" Pin0InfoVect0LinkObjId="SW-54145_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-302 4332,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f55400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-412 4332,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9580@1" Pin0InfoVect0LinkObjId="SW-54146_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-412 4332,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f55660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-350 4332,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9580@0" ObjectIDZND0="9581@1" Pin0InfoVect0LinkObjId="SW-54151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54146_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-350 4332,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f558c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-155 4354,-155 4354,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9579@x" ObjectIDZND0="g_1f5b6e0@0" Pin0InfoVect0LinkObjId="g_1f5b6e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54145_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-155 4354,-155 4354,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f55b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-155 4332,-63 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f5b6e0@0" ObjectIDND1="9579@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f5b6e0_0" Pin1InfoVect1LinkObjId="SW-54145_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-155 4332,-63 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f55d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4332,-245 4332,-155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9579@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f5b6e0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f5b6e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54145_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4332,-245 4332,-155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f5cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4392,-412 4392,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17338@0" ObjectIDZND0="9600@0" Pin0InfoVect0LinkObjId="SW-54227_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e3db70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4392,-412 4392,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f5f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-412 4522,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9601@0" Pin0InfoVect0LinkObjId="SW-54228_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e39be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-412 4522,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f63ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4522,-466 4522,-489 4468,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9601@1" ObjectIDZND0="9599@0" Pin0InfoVect0LinkObjId="SW-54214_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54228_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4522,-466 4522,-489 4468,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f64250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4441,-489 4392,-489 4392,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9599@1" ObjectIDZND0="9600@1" Pin0InfoVect0LinkObjId="SW-54227_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4441,-489 4392,-489 4392,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f66bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-307 4583,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9595@0" ObjectIDZND0="9593@1" Pin0InfoVect0LinkObjId="SW-54193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54196_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-307 4583,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f66e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-412 4583,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17339@0" ObjectIDZND0="9594@1" Pin0InfoVect0LinkObjId="SW-54194_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e39be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-412 4583,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f67080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-355 4583,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9594@0" ObjectIDZND0="9595@1" Pin0InfoVect0LinkObjId="SW-54196_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54194_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-355 4583,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f672e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-160 4605,-160 4605,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9593@x" ObjectIDZND0="g_1f6d100@0" Pin0InfoVect0LinkObjId="g_1f6d100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-54193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-160 4605,-160 4605,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f67540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-160 4583,-68 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1f6d100@0" ObjectIDND1="9593@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f6d100_0" Pin1InfoVect1LinkObjId="SW-54193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-160 4583,-68 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f677a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-250 4583,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="9593@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f6d100@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f6d100_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-54193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-250 4583,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f8c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-1046 4404,-1039 4472,-1039 4472,-1032 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1f35fe0@0" ObjectIDZND0="9551@1" Pin0InfoVect0LinkObjId="SW-54059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f35fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-1046 4404,-1039 4472,-1039 4472,-1032 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-54104">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4013.877221 -862.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9564" ObjectName="SW-CX_KLS.CX_KLS_351BK"/>
     <cge:Meas_Ref ObjectId="54104"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.573670 -479.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9582" ObjectName="SW-CX_KLS.CX_KLS_001BK"/>
     <cge:Meas_Ref ObjectId="54159"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54208">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4766.596732 -297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9598" ObjectName="SW-CX_KLS.CX_KLS_082BK"/>
     <cge:Meas_Ref ObjectId="54208"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54124">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3845.185567 -297.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9572" ObjectName="SW-CX_KLS.CX_KLS_072BK"/>
     <cge:Meas_Ref ObjectId="54124"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54173">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4584.000000 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9588" ObjectName="SW-CX_KLS.CX_KLS_352BK"/>
     <cge:Meas_Ref ObjectId="54173"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.185567 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9575" ObjectName="SW-CX_KLS.CX_KLS_073BK"/>
     <cge:Meas_Ref ObjectId="54133"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54115">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.185567 -293.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9569" ObjectName="SW-CX_KLS.CX_KLS_071BK"/>
     <cge:Meas_Ref ObjectId="54115"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.185567 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9578" ObjectName="SW-CX_KLS.CX_KLS_074BK"/>
     <cge:Meas_Ref ObjectId="54142"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4978.000000 -294.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54186">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4260.000000 -650.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9592" ObjectName="SW-CX_KLS.CX_KLS_312BK"/>
     <cge:Meas_Ref ObjectId="54186"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.185567 -294.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9581" ObjectName="SW-CX_KLS.CX_KLS_075BK"/>
     <cge:Meas_Ref ObjectId="54151"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54214">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4432.000000 -479.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9599" ObjectName="SW-CX_KLS.CX_KLS_012BK"/>
     <cge:Meas_Ref ObjectId="54214"/>
    <cge:TPSR_Ref TObjectID="9599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-54196">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4573.856727 -299.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9595" ObjectName="SW-CX_KLS.CX_KLS_081BK"/>
     <cge:Meas_Ref ObjectId="54196"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_KLS.CX_KLS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13647"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.573670 -563.000000)" xlink:href="#transformer2:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.573670 -563.000000)" xlink:href="#transformer2:shape4_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9602" ObjectName="TF-CX_KLS.CX_KLS_1T"/>
    <cge:TPSR_Ref TObjectID="9602"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">恐龙山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3663.000000 -132.000000) translate(0,16)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3818.000000 -25.000000) translate(0,16)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3979.000000 -25.000000) translate(0,16)">恐龙谷I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4150.000000 -25.000000) translate(0,16)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4553.000000 -19.000000) translate(0,16)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4307.000000 -21.000000) translate(0,16)">阿纳线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4908.000000 -167.000000) translate(0,16)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4949.000000 -444.000000) translate(0,16)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.087019 0.000000 0.000000 1.000000 3562.910128 -444.000000) translate(0,16)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4032.000000 -891.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4030.000000 -816.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -835.000000) translate(0,12)">35117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4078.000000 -913.000000) translate(0,12)">35160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4030.000000 -976.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -983.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4130.000000 -1058.000000) translate(0,12)">3519</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4040.000000 -1067.000000) translate(0,12)">35197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4602.000000 -869.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4600.000000 -811.000000) translate(0,12)">3522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4500.000000 -818.000000) translate(0,12)">35227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4600.000000 -919.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4504.000000 -923.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4408.000000 -1014.000000) translate(0,12)">3529</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4479.000000 -1021.000000) translate(0,12)">35297</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4609.000000 -1153.000000) translate(0,16)">35kV #1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4179.000000 -1153.000000) translate(0,16)">35kV线路PT</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3830.500000 -761.000000) translate(0,12)">35kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.000000 -764.000000) translate(0,12)">35kVIIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4270.000000 -684.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4158.000000 -739.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4413.000000 -653.000000) translate(0,12)">31227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4430.000000 -734.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -654.000000) translate(0,12)">35217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4030.000000 -739.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3927.000000 -680.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4600.000000 -735.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4497.000000 -682.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4030.000000 -508.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4028.000000 -458.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4837.000000 -457.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3705.000000 -451.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3686.000000 -322.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3677.000000 -269.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3678.000000 -374.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3763.000000 -270.000000) translate(0,12)">07167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3864.000000 -326.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -273.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -378.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4029.000000 -324.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4027.000000 -271.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4027.000000 -376.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4189.000000 -324.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4187.000000 -271.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4187.000000 -376.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4442.000000 -513.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4399.000000 -457.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4529.000000 -455.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4341.000000 -323.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4339.000000 -270.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4339.000000 -375.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4592.000000 -328.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4590.000000 -275.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4590.000000 -380.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4785.000000 -326.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4783.000000 -273.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4783.000000 -378.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3929.000000 -615.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4527.000000 -1174.000000) translate(0,16)">35kV恐龙山支线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3965.000000 -1159.000000) translate(0,16)">35kV备用线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4727.000000 -22.000000) translate(0,16)">恐龙谷II回线</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 894.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 864.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 879.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 894.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.000000 864.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4672.000000 879.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 647.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4259.000000 617.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4234.000000 632.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3823.000000 785.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4654.000000 785.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.000000 521.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 491.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 506.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4407.000000 564.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 534.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4396.000000 549.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 89.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 74.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3890.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4066.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4197.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4357.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4371.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4613.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4627.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4602.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 232.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 202.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 217.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1df8910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -585.000000)" xlink:href="#lightningRod:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1df9e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3689.000000 -487.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dfa5b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4107.000000 -1144.000000)" xlink:href="#lightningRod:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e00ef0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -1094.000000)" xlink:href="#lightningRod:shape34"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e079e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -1002.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e081a0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4672.000000 -1131.000000)" xlink:href="#lightningRod:shape110"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e0df30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3696.000000 -147.000000)" xlink:href="#lightningRod:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e152e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4822.000000 -594.000000)" xlink:href="#lightningRod:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e16920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4821.000000 -494.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e170e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4974.000000 -180.000000)" xlink:href="#lightningRod:shape110"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2e500">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4442.000000 -1041.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2f070">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4113.000000 -1103.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f2fdb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4559.000000 -1008.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f30b60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -88.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f31910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4035.000000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f326c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4195.000000 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f33470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 -88.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f34480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -1022.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f35230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -1103.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f35fe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4397.000000 -1041.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f36d90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4857.000000 -495.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dd4550">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3725.000000 -486.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5b6e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4347.000000 -85.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6d100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4598.000000 -90.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-LF_KLS.LF_KLS_10_IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-412 5100,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17339" ObjectName="BS-LF_KLS.LF_KLS_10_IIM"/>
    <cge:TPSR_Ref TObjectID="17339"/></metadata>
   <polyline fill="none" opacity="0" points="4488,-412 5100,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_KLS.CX_KLS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-760 4218,-760 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9546" ObjectName="BS-CX_KLS.CX_KLS_3IM"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   <polyline fill="none" opacity="0" points="3936,-760 4218,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-LF_KLS.LF_KLS_10_IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3645,-412 4447,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17338" ObjectName="BS-LF_KLS.LF_KLS_10_IM"/>
    <cge:TPSR_Ref TObjectID="17338"/></metadata>
   <polyline fill="none" opacity="0" points="3645,-412 4447,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_KLS.CX_KLS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4351,-760 4688,-760 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9547" ObjectName="BS-CX_KLS.CX_KLS_3IIM"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   <polyline fill="none" opacity="0" points="4351,-760 4688,-760 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53887" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4173.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53888" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4173.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53885" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4173.000000 -894.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53885" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9564"/>
     <cge:Term_Ref ObjectID="13569"/>
    <cge:TPSR_Ref TObjectID="9564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53978" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4743.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53979" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4743.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53979" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53975" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4743.000000 -894.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9588"/>
     <cge:Term_Ref ObjectID="13617"/>
    <cge:TPSR_Ref TObjectID="9588"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53990" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4308.000000 -647.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53991" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4308.000000 -647.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53987" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4308.000000 -647.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53987" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9592"/>
     <cge:Term_Ref ObjectID="13625"/>
    <cge:TPSR_Ref TObjectID="9592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53843" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3911.000000 -785.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9546"/>
     <cge:Term_Ref ObjectID="13535"/>
    <cge:TPSR_Ref TObjectID="9546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-53854" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4750.000000 -785.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53854" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9547"/>
     <cge:Term_Ref ObjectID="13536"/>
    <cge:TPSR_Ref TObjectID="9547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53966" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -521.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53967" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -521.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53967" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53963" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4142.000000 -521.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53963" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9582"/>
     <cge:Term_Ref ObjectID="13605"/>
    <cge:TPSR_Ref TObjectID="9582"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54024" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4469.000000 -564.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9599"/>
     <cge:Term_Ref ObjectID="13639"/>
    <cge:TPSR_Ref TObjectID="9599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54025" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4469.000000 -564.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9599"/>
     <cge:Term_Ref ObjectID="13639"/>
    <cge:TPSR_Ref TObjectID="9599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54021" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4469.000000 -564.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9599"/>
     <cge:Term_Ref ObjectID="13639"/>
    <cge:TPSR_Ref TObjectID="9599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54012" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4866.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54013" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4866.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-54010" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4866.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54010" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9598"/>
     <cge:Term_Ref ObjectID="13637"/>
    <cge:TPSR_Ref TObjectID="9598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-54001" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4676.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54001" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-54002" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4676.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="54002" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53999" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4676.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53999" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9595"/>
     <cge:Term_Ref ObjectID="13631"/>
    <cge:TPSR_Ref TObjectID="9595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53942" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4420.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53943" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4420.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53940" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4420.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9581"/>
     <cge:Term_Ref ObjectID="13603"/>
    <cge:TPSR_Ref TObjectID="9581"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53931" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4276.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53932" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4276.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53929" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4276.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9578"/>
     <cge:Term_Ref ObjectID="13597"/>
    <cge:TPSR_Ref TObjectID="9578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53920" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53921" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53918" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4115.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9575"/>
     <cge:Term_Ref ObjectID="13591"/>
    <cge:TPSR_Ref TObjectID="9575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-53909" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -232.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53910" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -232.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53907" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -232.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9572"/>
     <cge:Term_Ref ObjectID="13585"/>
    <cge:TPSR_Ref TObjectID="9572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-53899" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -89.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9569"/>
     <cge:Term_Ref ObjectID="13579"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-53896" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -89.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="53896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9569"/>
     <cge:Term_Ref ObjectID="13579"/>
    <cge:TPSR_Ref TObjectID="9569"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="LF_KLS"/>
</svg>