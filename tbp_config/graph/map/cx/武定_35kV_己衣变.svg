<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-176" aopId="3949070" id="thSvg" product="E8000V2" version="1.0" viewBox="3115 -1225 2344 1148">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.592016" x1="2" x2="51" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="37" x2="18" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.294304" x1="37" x2="18" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="2" x2="2" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="27" x2="27" y1="15" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.607595" x1="51" x2="51" y1="15" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape199">
    <circle cx="17" cy="41" fillStyle="0" r="17" stroke-width="0.406414"/>
    <ellipse cx="17" cy="17" fillStyle="0" rx="17" ry="16.5" stroke-width="0.406414"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="24" x2="17" y1="39" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="17" x2="17" y1="44" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="10" x2="17" y1="39" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="17" x2="17" y1="15" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="10" x2="17" y1="9" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325255" x1="24" x2="17" y1="9" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="139" y2="58"/>
    <rect height="24" stroke-width="0.379884" width="14" x="10" y="104"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape178">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="131" y2="123"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="107"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,120 31,98 31,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="122" y2="122"/>
    <circle cx="30" cy="71" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="71" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="27" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="77" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="43" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,49 6,49 6,20 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,6 26,19 39,19 32,6 32,7 32,6 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="34" y2="0"/>
    <circle cx="30" cy="49" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="29" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="54"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape51_0">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="39" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="97" x2="97" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="100" x2="94" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="98" x2="96" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="91" y1="54" y2="54"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
   </symbol>
   <symbol id="transformer2:shape51_1">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape52_0">
    <circle cx="28" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="80" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="87" x2="85" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="89" x2="83" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="86" x2="86" y1="54" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.643357" x1="86" x2="28" y1="75" y2="75"/>
    <polyline DF8003:Layer="PUBLIC" points="28,19 21,34 36,34 28,19 28,19 28,19 "/>
   </symbol>
   <symbol id="transformer2:shape52_1">
    <circle cx="28" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="28" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="16" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape94">
    <rect height="24" stroke-width="0.379884" width="14" x="2" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="21" y1="14" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="23" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="23" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="9" x2="9" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="9" x2="6" y1="8" y2="11"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="22" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="12" x2="9" y1="26" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="25" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="75" y2="31"/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1de2ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de4140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de4b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de57d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de6a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de76a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1de8240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1de8b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_163a1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_163a1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1577920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1577920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1579390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1579390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1dee000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1defb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1df07c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1df16a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1df1f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df3740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df4260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df49e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1df51a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df6280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df6c00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1df76f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1df80b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1df9550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dfa0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dfb0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfbd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0a500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dfe070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dff590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1158" width="2354" x="3110" y="-1230"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1550dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 623.000000) translate(0,16)">档位(档)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1551230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4900.000000 603.000000) translate(0,16)">油温(℃)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151b070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 805.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4175.000000 790.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_151ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 774.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161cb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4161.000000 820.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161d4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 802.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161d760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4926.000000 787.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161d9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4931.000000 771.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161dbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4912.000000 817.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161e280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 545.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161e4b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 530.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161e6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 514.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161e930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4171.000000 560.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161ec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4902.000000 534.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161eed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4927.000000 519.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161f110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 503.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_161f350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4913.000000 549.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.989247 -0.000000 0.000000 -0.954736 1417.870968 381.768495)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ba740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 889.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bb210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 904.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bb7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 919.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bba50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 933.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.989247 -0.000000 0.000000 -1.038485 979.870968 40.906082)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bc280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 889.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bc520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 904.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bc760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 919.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bc9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3902.000000 933.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1478e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 629.000000) translate(0,16)">档位(档)：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1479090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 609.000000) translate(0,16)">油温(℃)：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c9e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3748.000000 136.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147cca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3737.000000 121.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147cee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 106.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147d300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 135.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147d5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 120.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147d800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 105.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147dc20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4170.000000 137.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147dee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 122.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147e120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 107.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147e540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4378.000000 135.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147e800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.000000 120.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ea40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 105.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147ee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4561.000000 143.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 128.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147f360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 113.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147f780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4827.000000 129.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147fa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 114.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4841.000000 99.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_147ffb0" transform="matrix(1.614035 -0.000000 0.000000 -1.423729 5023.912281 111.000000) translate(0,8)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="10" graphid="g_14dfb60" transform="matrix(1.614035 -0.000000 0.000000 -1.423729 4998.000000 127.355932) translate(0,8)">Q(MVar):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="404" x="3116" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="3550" y="-1095"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-121538">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22613" ObjectName="SW-WD_JY.WD_JY_3011SW"/>
     <cge:Meas_Ref ObjectId="121538"/>
    <cge:TPSR_Ref TObjectID="22613"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121557">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22618" ObjectName="SW-WD_JY.WD_JY_3021SW"/>
     <cge:Meas_Ref ObjectId="121557"/>
    <cge:TPSR_Ref TObjectID="22618"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121585">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22625" ObjectName="SW-WD_JY.WD_JY_3901SW"/>
     <cge:Meas_Ref ObjectId="121585"/>
    <cge:TPSR_Ref TObjectID="22625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121560">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 -905.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22621" ObjectName="SW-WD_JY.WD_JY_3916SW"/>
     <cge:Meas_Ref ObjectId="121560"/>
    <cge:TPSR_Ref TObjectID="22621"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-186526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4582.000000 -936.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28400" ObjectName="SW-WD_JY.WD_JY_39167SW"/>
     <cge:Meas_Ref ObjectId="186526"/>
    <cge:TPSR_Ref TObjectID="28400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121541">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 -784.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22616" ObjectName="SW-WD_JY.WD_JY_3921SW"/>
     <cge:Meas_Ref ObjectId="121541"/>
    <cge:TPSR_Ref TObjectID="22616"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121586">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4574.000000 -720.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22626" ObjectName="SW-WD_JY.WD_JY_39017SW"/>
     <cge:Meas_Ref ObjectId="121586"/>
    <cge:TPSR_Ref TObjectID="22626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121537">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -472.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22612" ObjectName="SW-WD_JY.WD_JY_0011SW"/>
     <cge:Meas_Ref ObjectId="121537"/>
    <cge:TPSR_Ref TObjectID="22612"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121556">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22617" ObjectName="SW-WD_JY.WD_JY_0021SW"/>
     <cge:Meas_Ref ObjectId="121556"/>
    <cge:TPSR_Ref TObjectID="22617"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121268">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22592" ObjectName="SW-WD_JY.WD_JY_0911SW"/>
     <cge:Meas_Ref ObjectId="121268"/>
    <cge:TPSR_Ref TObjectID="22592"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22593" ObjectName="SW-WD_JY.WD_JY_0916SW"/>
     <cge:Meas_Ref ObjectId="121269"/>
    <cge:TPSR_Ref TObjectID="22593"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121294">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -380.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22595" ObjectName="SW-WD_JY.WD_JY_0921SW"/>
     <cge:Meas_Ref ObjectId="121294"/>
    <cge:TPSR_Ref TObjectID="22595"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22596" ObjectName="SW-WD_JY.WD_JY_0926SW"/>
     <cge:Meas_Ref ObjectId="121295"/>
    <cge:TPSR_Ref TObjectID="22596"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -378.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22598" ObjectName="SW-WD_JY.WD_JY_0931SW"/>
     <cge:Meas_Ref ObjectId="121320"/>
    <cge:TPSR_Ref TObjectID="22598"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121321">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -271.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22599" ObjectName="SW-WD_JY.WD_JY_0936SW"/>
     <cge:Meas_Ref ObjectId="121321"/>
    <cge:TPSR_Ref TObjectID="22599"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -377.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22601" ObjectName="SW-WD_JY.WD_JY_0941SW"/>
     <cge:Meas_Ref ObjectId="121348"/>
    <cge:TPSR_Ref TObjectID="22601"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121349">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -270.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22602" ObjectName="SW-WD_JY.WD_JY_0946SW"/>
     <cge:Meas_Ref ObjectId="121349"/>
    <cge:TPSR_Ref TObjectID="22602"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -376.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22604" ObjectName="SW-WD_JY.WD_JY_0951SW"/>
     <cge:Meas_Ref ObjectId="121376"/>
    <cge:TPSR_Ref TObjectID="22604"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -262.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22605" ObjectName="SW-WD_JY.WD_JY_0956SW"/>
     <cge:Meas_Ref ObjectId="121377"/>
    <cge:TPSR_Ref TObjectID="22605"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121404">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22607" ObjectName="SW-WD_JY.WD_JY_0961SW"/>
     <cge:Meas_Ref ObjectId="121404"/>
    <cge:TPSR_Ref TObjectID="22607"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121405">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -266.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22608" ObjectName="SW-WD_JY.WD_JY_0966SW"/>
     <cge:Meas_Ref ObjectId="121405"/>
    <cge:TPSR_Ref TObjectID="22608"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121432">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -380.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22610" ObjectName="SW-WD_JY.WD_JY_0971SW"/>
     <cge:Meas_Ref ObjectId="121432"/>
    <cge:TPSR_Ref TObjectID="22610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-124631">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5257.000000 -381.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22890" ObjectName="SW-WD_JY.WD_JY_0901SW"/>
     <cge:Meas_Ref ObjectId="124631"/>
    <cge:TPSR_Ref TObjectID="22890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237120">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4756.000000 -775.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39486" ObjectName="SW-WD_JY.WD_JY_30217SW"/>
     <cge:Meas_Ref ObjectId="237120"/>
    <cge:TPSR_Ref TObjectID="39486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237119">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4016.000000 -781.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39485" ObjectName="SW-WD_JY.WD_JY_30117SW"/>
     <cge:Meas_Ref ObjectId="237119"/>
    <cge:TPSR_Ref TObjectID="39485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187461">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4478.000000 -871.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28401" ObjectName="SW-WD_JY.WD_JY_39160SW"/>
     <cge:Meas_Ref ObjectId="187461"/>
    <cge:TPSR_Ref TObjectID="28401"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-237121">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4332.000000 -775.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39487" ObjectName="SW-WD_JY.WD_JY_39217SW"/>
     <cge:Meas_Ref ObjectId="237121"/>
    <cge:TPSR_Ref TObjectID="39487"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-187462">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -280.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28403" ObjectName="SW-WD_JY.WD_JY_0976SW"/>
     <cge:Meas_Ref ObjectId="187462"/>
    <cge:TPSR_Ref TObjectID="28403"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-WD_JY.WD_JY.WD_JY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3882,-856 4994,-856 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22737" ObjectName="BS-WD_JY.WD_JY.WD_JY_3IM"/>
    <cge:TPSR_Ref TObjectID="22737"/></metadata>
   <polyline fill="none" opacity="0" points="3882,-856 4994,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-WD_JY.WD_JY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3709,-441 5431,-441 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="22591" ObjectName="BS-WD_JY.WD_JY_9IM"/>
    <cge:TPSR_Ref TObjectID="22591"/></metadata>
   <polyline fill="none" opacity="0" points="3709,-441 5431,-441 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-WD_JY.WD_JY_cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -215.000000)" xlink:href="#capacitor:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39488" ObjectName="CB-WD_JY.WD_JY_cb1"/>
    <cge:TPSR_Ref TObjectID="39488"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-WD_JY.WD_JY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31795"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -622.000000)" xlink:href="#transformer2:shape51_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -622.000000)" xlink:href="#transformer2:shape51_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22629" ObjectName="TF-WD_JY.WD_JY_2T"/>
    <cge:TPSR_Ref TObjectID="22629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-WD_JY.WD_JY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="31791"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -605.000000)" xlink:href="#transformer2:shape52_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4052.000000 -605.000000)" xlink:href="#transformer2:shape52_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="22628" ObjectName="TF-WD_JY.WD_JY_1T"/>
    <cge:TPSR_Ref TObjectID="22628"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_15815b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -701.136364)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15da120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -951.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1579ea0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4300.000000 -631.000000)" xlink:href="#lightningRod:shape199"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_157b250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e8610">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1546db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4160.000000 -152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_163ac10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15c2f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -181.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d5b70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 -152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1598eb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5217.000000 -274.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1599760">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5305.000000 -282.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f3ce0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -221.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1553f90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.000000 -105.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1556040">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4734.000000 -125.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3289.000000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145076" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3346.538462 -951.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145076" ObjectName="WD_JY:WD_JY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145367" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3346.538462 -909.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145367" ObjectName="WD_JY:WD_JY_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-121253" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -601.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121253" ObjectName="WD_JY:WD_JY_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-121264" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -621.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121264" ObjectName="WD_JY:WD_JY_GG_U_107"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-121240" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4254.000000 -607.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121240" ObjectName="WD_JY:WD_JY_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145076" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3345.538462 -1028.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145076" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-145076" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3345.538462 -989.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145076" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-237224" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -633.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="237224" ObjectName="WD_JY:WD_JY_1T_Tp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121245" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -815.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121245" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22619"/>
     <cge:Term_Ref ObjectID="31771"/>
    <cge:TPSR_Ref TObjectID="22619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121246" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -815.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121246" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22619"/>
     <cge:Term_Ref ObjectID="31771"/>
    <cge:TPSR_Ref TObjectID="22619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121241" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -815.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121241" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22619"/>
     <cge:Term_Ref ObjectID="31771"/>
    <cge:TPSR_Ref TObjectID="22619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121244" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4968.000000 -815.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121244" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22619"/>
     <cge:Term_Ref ObjectID="31771"/>
    <cge:TPSR_Ref TObjectID="22619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -819.500000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22614"/>
     <cge:Term_Ref ObjectID="31761"/>
    <cge:TPSR_Ref TObjectID="22614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121224" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -819.500000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121224" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22614"/>
     <cge:Term_Ref ObjectID="31761"/>
    <cge:TPSR_Ref TObjectID="22614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -819.500000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22614"/>
     <cge:Term_Ref ObjectID="31761"/>
    <cge:TPSR_Ref TObjectID="22614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4222.000000 -819.500000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22614"/>
     <cge:Term_Ref ObjectID="31761"/>
    <cge:TPSR_Ref TObjectID="22614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -559.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22615"/>
     <cge:Term_Ref ObjectID="31763"/>
    <cge:TPSR_Ref TObjectID="22615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121236" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -559.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121236" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22615"/>
     <cge:Term_Ref ObjectID="31763"/>
    <cge:TPSR_Ref TObjectID="22615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -559.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22615"/>
     <cge:Term_Ref ObjectID="31763"/>
    <cge:TPSR_Ref TObjectID="22615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4232.000000 -559.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22615"/>
     <cge:Term_Ref ObjectID="31763"/>
    <cge:TPSR_Ref TObjectID="22615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121251" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -549.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121251" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22620"/>
     <cge:Term_Ref ObjectID="31773"/>
    <cge:TPSR_Ref TObjectID="22620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121252" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -549.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121252" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22620"/>
     <cge:Term_Ref ObjectID="31773"/>
    <cge:TPSR_Ref TObjectID="22620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121247" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -549.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121247" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22620"/>
     <cge:Term_Ref ObjectID="31773"/>
    <cge:TPSR_Ref TObjectID="22620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-121250" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -549.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121250" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22620"/>
     <cge:Term_Ref ObjectID="31773"/>
    <cge:TPSR_Ref TObjectID="22620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-121231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -510.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22591"/>
     <cge:Term_Ref ObjectID="31716"/>
    <cge:TPSR_Ref TObjectID="22591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-121232" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -510.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121232" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22591"/>
     <cge:Term_Ref ObjectID="31716"/>
    <cge:TPSR_Ref TObjectID="22591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-121233" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -510.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121233" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22591"/>
     <cge:Term_Ref ObjectID="31716"/>
    <cge:TPSR_Ref TObjectID="22591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-121237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5337.000000 -510.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22591"/>
     <cge:Term_Ref ObjectID="31716"/>
    <cge:TPSR_Ref TObjectID="22591"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -135.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22594"/>
     <cge:Term_Ref ObjectID="31721"/>
    <cge:TPSR_Ref TObjectID="22594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -135.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22594"/>
     <cge:Term_Ref ObjectID="31721"/>
    <cge:TPSR_Ref TObjectID="22594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -135.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22594"/>
     <cge:Term_Ref ObjectID="31721"/>
    <cge:TPSR_Ref TObjectID="22594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121189" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -135.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121189" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22597"/>
     <cge:Term_Ref ObjectID="31727"/>
    <cge:TPSR_Ref TObjectID="22597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121190" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -135.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121190" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22597"/>
     <cge:Term_Ref ObjectID="31727"/>
    <cge:TPSR_Ref TObjectID="22597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4019.000000 -135.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22597"/>
     <cge:Term_Ref ObjectID="31727"/>
    <cge:TPSR_Ref TObjectID="22597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121194" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -136.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121194" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22600"/>
     <cge:Term_Ref ObjectID="31733"/>
    <cge:TPSR_Ref TObjectID="22600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121593" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -136.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121593" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22600"/>
     <cge:Term_Ref ObjectID="31733"/>
    <cge:TPSR_Ref TObjectID="22600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121191" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4228.000000 -136.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121191" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22600"/>
     <cge:Term_Ref ObjectID="31733"/>
    <cge:TPSR_Ref TObjectID="22600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121199" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -136.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121199" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22603"/>
     <cge:Term_Ref ObjectID="31739"/>
    <cge:TPSR_Ref TObjectID="22603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121200" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -136.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121200" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22603"/>
     <cge:Term_Ref ObjectID="31739"/>
    <cge:TPSR_Ref TObjectID="22603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121196" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4433.000000 -136.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121196" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22603"/>
     <cge:Term_Ref ObjectID="31739"/>
    <cge:TPSR_Ref TObjectID="22603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121204" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -142.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121204" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22606"/>
     <cge:Term_Ref ObjectID="31745"/>
    <cge:TPSR_Ref TObjectID="22606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121205" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -142.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121205" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22606"/>
     <cge:Term_Ref ObjectID="31745"/>
    <cge:TPSR_Ref TObjectID="22606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121201" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -142.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121201" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22606"/>
     <cge:Term_Ref ObjectID="31745"/>
    <cge:TPSR_Ref TObjectID="22606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-121209" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -128.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121209" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22609"/>
     <cge:Term_Ref ObjectID="31751"/>
    <cge:TPSR_Ref TObjectID="22609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121210" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -128.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121210" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22609"/>
     <cge:Term_Ref ObjectID="31751"/>
    <cge:TPSR_Ref TObjectID="22609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121206" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4883.000000 -128.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121206" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22609"/>
     <cge:Term_Ref ObjectID="31751"/>
    <cge:TPSR_Ref TObjectID="22609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-121215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -126.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22611"/>
     <cge:Term_Ref ObjectID="31755"/>
    <cge:TPSR_Ref TObjectID="22611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-121211" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5072.000000 -126.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="121211" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22611"/>
     <cge:Term_Ref ObjectID="31755"/>
    <cge:TPSR_Ref TObjectID="22611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-186519" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -925.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22737"/>
     <cge:Term_Ref ObjectID="31972"/>
    <cge:TPSR_Ref TObjectID="22737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-186520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -925.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22737"/>
     <cge:Term_Ref ObjectID="31972"/>
    <cge:TPSR_Ref TObjectID="22737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-186521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -925.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22737"/>
     <cge:Term_Ref ObjectID="31972"/>
    <cge:TPSR_Ref TObjectID="22737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-186522" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -925.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="186522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="22737"/>
     <cge:Term_Ref ObjectID="31972"/>
    <cge:TPSR_Ref TObjectID="22737"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="58" qtmmishow="hidden" width="213" x="3309" y="-1189"/>
    </a>
   <metadata/><rect fill="white" height="58" opacity="0" stroke="white" transform="" width="213" x="3309" y="-1189"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="113" qtmmishow="hidden" width="124" x="3221" y="-1225"/>
    </a>
   <metadata/><rect fill="white" height="113" opacity="0" stroke="white" transform="" width="124" x="3221" y="-1225"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3822" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3822" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4025" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4025" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4233" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4233" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4438" y="-356"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4438" y="-356"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4651" y="-350"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4651" y="-350"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4864" y="-352"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4864" y="-352"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5069" y="-357"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5069" y="-357"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="81" x="4132" y="-708"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="81" x="4132" y="-708"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="82" x="4694" y="-711"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="82" x="4694" y="-711"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3257" y="-730"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3257" y="-730"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3536" y="-1167"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3536" y="-1167"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3536" y="-1202"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3536" y="-1202"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="3550" y="-1096"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="3550" y="-1096"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="58" qtmmishow="hidden" width="213" x="3309" y="-1189"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="113" qtmmishow="hidden" width="124" x="3221" y="-1225"/></g>
   <g href="35kV己衣变WD_JY_091间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3822" y="-356"/></g>
   <g href="35kV己衣变WD_JY_092间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4025" y="-356"/></g>
   <g href="35kV己衣变WD_JY_093间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4233" y="-357"/></g>
   <g href="35kV己衣变WD_JY_094间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4438" y="-356"/></g>
   <g href="35kV己衣变WD_JY_095间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4651" y="-350"/></g>
   <g href="35kV己衣变WD_JY_096间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4864" y="-352"/></g>
   <g href="35kV己衣变WD_JY_097间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5069" y="-357"/></g>
   <g href="35kV己衣变己衣变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="81" x="4132" y="-708"/></g>
   <g href="35kV己衣变己衣变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="82" x="4694" y="-711"/></g>
   <g href="35kV己衣变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3257" y="-730"/></g>
   <g href="cx_配调_配网接线图35_武定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3536" y="-1167"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3536" y="-1202"/></g>
   <g href="AVC己衣站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="3550" y="-1096"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5450" x2="5459" y1="-485" y2="-485"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1580330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4534.000000 -668.863636)" xlink:href="#voltageTransformer:shape94"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1519dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5209.000000 -194.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -158.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33942" ObjectName="EC-WD_JY.091Ld"/>
    <cge:TPSR_Ref TObjectID="33942"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -158.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33943" ObjectName="EC-WD_JY.092Ld"/>
    <cge:TPSR_Ref TObjectID="33943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -159.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33944" ObjectName="EC-WD_JY.093Ld"/>
    <cge:TPSR_Ref TObjectID="33944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.094Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -158.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33945" ObjectName="EC-WD_JY.094Ld"/>
    <cge:TPSR_Ref TObjectID="33945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.095Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 -155.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33946" ObjectName="EC-WD_JY.095Ld"/>
    <cge:TPSR_Ref TObjectID="33946"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-WD_JY.096Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -154.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33947" ObjectName="EC-WD_JY.096Ld"/>
    <cge:TPSR_Ref TObjectID="33947"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_15b70e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-829 4080,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22613@1" ObjectIDZND0="22737@0" Pin0InfoVect0LinkObjId="g_1572540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121538_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-829 4080,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1573dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-746 4821,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22619@0" ObjectIDZND0="22629@0" Pin0InfoVect0LinkObjId="g_151ce90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121558_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-746 4821,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15811d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-771 4502,-771 4502,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1580330@0" ObjectIDND1="22626@x" ObjectIDND2="22625@x" ObjectIDZND0="g_15815b0@0" Pin0InfoVect0LinkObjId="g_15815b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1580330_0" Pin1InfoVect1LinkObjId="SW-121586_0" Pin1InfoVect2LinkObjId="SW-121585_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-771 4502,-771 4502,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15813c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-743 4543,-771 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1580330@0" ObjectIDZND0="g_15815b0@0" ObjectIDZND1="22626@x" ObjectIDZND2="22625@x" Pin0InfoVect0LinkObjId="g_15815b0_0" Pin0InfoVect1LinkObjId="SW-121586_0" Pin0InfoVect2LinkObjId="SW-121585_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1580330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-743 4543,-771 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1574420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-946 4543,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="22621@1" ObjectIDZND0="28400@x" ObjectIDZND1="g_15da120@0" ObjectIDZND2="42840@1" Pin0InfoVect0LinkObjId="SW-186526_0" Pin0InfoVect1LinkObjId="g_15da120_0" Pin0InfoVect2LinkObjId="g_15daab0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-946 4543,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1581ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-987 4591,-987 4591,-977 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="22621@x" ObjectIDND1="g_15da120@0" ObjectIDND2="42840@1" ObjectIDZND0="28400@1" Pin0InfoVect0LinkObjId="SW-186526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-121560_0" Pin1InfoVect1LinkObjId="g_15da120_0" Pin1InfoVect2LinkObjId="g_15daab0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-987 4591,-987 4591,-977 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15820b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4591,-941 4591,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28400@0" ObjectIDZND0="g_15822a0@0" Pin0InfoVect0LinkObjId="g_15822a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-186526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4591,-941 4591,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d96e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-1026 4496,-1026 4496,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="22621@x" ObjectIDND1="28400@x" ObjectIDND2="42840@1" ObjectIDZND0="g_15da120@0" Pin0InfoVect0LinkObjId="g_15da120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-121560_0" Pin1InfoVect1LinkObjId="SW-186526_0" Pin1InfoVect2LinkObjId="g_15daab0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-1026 4496,-1026 4496,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-987 4543,-1026 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="22621@x" ObjectIDND1="28400@x" ObjectIDZND0="g_15da120@0" ObjectIDZND1="42840@1" Pin0InfoVect0LinkObjId="g_15da120_0" Pin0InfoVect1LinkObjId="g_15daab0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121560_0" Pin1InfoVect1LinkObjId="SW-186526_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-987 4543,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15daab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-1026 4543,-1072 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="22621@x" ObjectIDND1="28400@x" ObjectIDND2="g_15da120@0" ObjectIDZND0="42840@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-121560_0" Pin1InfoVect1LinkObjId="SW-186526_0" Pin1InfoVect2LinkObjId="g_15da120_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-1026 4543,-1072 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1579cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-856 4317,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="22737@0" ObjectIDZND0="22616@1" Pin0InfoVect0LinkObjId="SW-121541_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b70e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-856 4317,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_157b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-224 3756,-224 3756,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33942@x" ObjectIDND1="22593@x" ObjectIDZND0="g_157b250@0" Pin0InfoVect0LinkObjId="g_157b250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JY.091Ld_0" Pin1InfoVect1LinkObjId="SW-121269_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-224 3756,-224 3756,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1570bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-771 4583,-771 4583,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_15815b0@0" ObjectIDND1="g_1580330@0" ObjectIDND2="22625@x" ObjectIDZND0="22626@1" Pin0InfoVect0LinkObjId="SW-121586_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15815b0_0" Pin1InfoVect1LinkObjId="g_1580330_0" Pin1InfoVect2LinkObjId="SW-121585_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-771 4583,-771 4583,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1570dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4583,-725 4583,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="22626@0" ObjectIDZND0="g_1570fb0@0" Pin0InfoVect0LinkObjId="g_1570fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121586_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4583,-725 4583,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1572350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-771 4543,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_15815b0@0" ObjectIDND1="g_1580330@0" ObjectIDND2="22626@x" ObjectIDZND0="22625@0" Pin0InfoVect0LinkObjId="SW-121585_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15815b0_0" Pin1InfoVect1LinkObjId="g_1580330_0" Pin1InfoVect2LinkObjId="SW-121586_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-771 4543,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1572540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-829 4543,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22625@1" ObjectIDZND0="22737@0" Pin0InfoVect0LinkObjId="g_15b70e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121585_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-829 4543,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15514a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4123,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4123,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1551690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-752 4080,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="22614@0" ObjectIDZND0="22628@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121539_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-752 4080,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-610 4080,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="22628@0" ObjectIDZND0="22615@1" Pin0InfoVect0LinkObjId="SW-121540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1551690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-610 4080,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a4800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-546 4080,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22615@0" ObjectIDZND0="22612@1" Pin0InfoVect0LinkObjId="SW-121537_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-546 4080,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-477 4080,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22612@0" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a6d70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121537_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-477 4080,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a6930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-627 4821,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="22629@1" ObjectIDZND0="22620@x" ObjectIDZND1="22629@x" Pin0InfoVect0LinkObjId="SW-121559_0" Pin0InfoVect1LinkObjId="g_1573dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1573dd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-627 4821,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a6b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4820,-532 4820,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22620@0" ObjectIDZND0="22617@1" Pin0InfoVect0LinkObjId="SW-121556_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121559_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4820,-532 4820,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15a6d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4820,-469 4820,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22617@0" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121556_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4820,-469 4820,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e81d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-179 3813,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33942@0" ObjectIDZND0="g_157b250@0" ObjectIDZND1="22593@x" Pin0InfoVect0LinkObjId="g_157b250_0" Pin0InfoVect1LinkObjId="SW-121269_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.091Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-179 3813,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e83f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-224 3959,-224 3959,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33943@x" ObjectIDND1="22596@x" ObjectIDZND0="g_15e8610@0" Pin0InfoVect0LinkObjId="g_15e8610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JY.092Ld_0" Pin1InfoVect1LinkObjId="SW-121295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-224 3959,-224 3959,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1525a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-179 4016,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33943@0" ObjectIDZND0="g_15e8610@0" ObjectIDZND1="22596@x" Pin0InfoVect0LinkObjId="g_15e8610_0" Pin0InfoVect1LinkObjId="SW-121295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.092Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-179 4016,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1525cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-225 4167,-225 4167,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33944@x" ObjectIDND1="22599@x" ObjectIDZND0="g_1546db0@0" Pin0InfoVect0LinkObjId="g_1546db0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JY.093Ld_0" Pin1InfoVect1LinkObjId="SW-121321_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-225 4167,-225 4167,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-180 4224,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33944@0" ObjectIDZND0="g_1546db0@0" ObjectIDZND1="22599@x" Pin0InfoVect0LinkObjId="g_1546db0_0" Pin0InfoVect1LinkObjId="SW-121321_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.093Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-180 4224,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_163a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-224 4372,-224 4372,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="33945@x" ObjectIDND1="22602@x" ObjectIDZND0="g_163ac10@0" Pin0InfoVect0LinkObjId="g_163ac10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-WD_JY.094Ld_0" Pin1InfoVect1LinkObjId="SW-121349_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-224 4372,-224 4372,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15c2cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-179 4429,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33945@0" ObjectIDZND0="g_163ac10@0" ObjectIDZND1="22602@x" Pin0InfoVect0LinkObjId="g_163ac10_0" Pin0InfoVect1LinkObjId="SW-121349_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.094Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-179 4429,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1599540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5226,-247 5226,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1519dd0@0" ObjectIDZND0="g_1598eb0@1" Pin0InfoVect0LinkObjId="g_1598eb0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1519dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5226,-247 5226,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-825 4821,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22618@1" ObjectIDZND0="22737@0" Pin0InfoVect0LinkObjId="g_15b70e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121557_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-825 4821,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15bd360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-336 5312,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1598eb0@0" ObjectIDND1="22890@x" ObjectIDZND0="g_1599760@0" Pin0InfoVect0LinkObjId="g_1599760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1598eb0_0" Pin1InfoVect1LinkObjId="SW-124631_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-336 5312,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15bd550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5226,-311 5226,-336 5266,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1598eb0@0" ObjectIDZND0="g_1599760@0" ObjectIDZND1="22890@x" Pin0InfoVect0LinkObjId="g_1599760_0" Pin0InfoVect1LinkObjId="SW-124631_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1598eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5226,-311 5226,-336 5266,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1594750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-362 3813,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22594@1" ObjectIDZND0="22592@0" Pin0InfoVect0LinkObjId="SW-121268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121272_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-362 3813,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15949b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-422 3813,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22592@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-422 3813,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1594c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-335 3813,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22594@0" ObjectIDZND0="22593@1" Pin0InfoVect0LinkObjId="SW-121269_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121272_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-335 3813,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1594e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-274 3813,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="22593@0" ObjectIDZND0="33942@x" ObjectIDZND1="g_157b250@0" Pin0InfoVect0LinkObjId="EC-WD_JY.091Ld_0" Pin0InfoVect1LinkObjId="g_157b250_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-274 3813,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15956a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-362 4016,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22597@1" ObjectIDZND0="22595@0" Pin0InfoVect0LinkObjId="SW-121294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-362 4016,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-421 4016,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22595@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-421 4016,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-335 4016,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22597@0" ObjectIDZND0="22596@1" Pin0InfoVect0LinkObjId="SW-121295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121298_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-335 4016,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1595dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4016,-276 4016,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22596@0" ObjectIDZND0="g_15e8610@0" ObjectIDZND1="33943@x" Pin0InfoVect0LinkObjId="g_15e8610_0" Pin0InfoVect1LinkObjId="EC-WD_JY.092Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121295_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4016,-276 4016,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15965f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-363 4224,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22600@1" ObjectIDZND0="22598@0" Pin0InfoVect0LinkObjId="SW-121320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121324_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-363 4224,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1596850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-419 4224,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22598@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121320_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-419 4224,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1587ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-336 4224,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22600@0" ObjectIDZND0="22599@1" Pin0InfoVect0LinkObjId="SW-121321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-336 4224,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1587d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-276 4224,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22599@0" ObjectIDZND0="g_1546db0@0" ObjectIDZND1="33944@x" Pin0InfoVect0LinkObjId="g_1546db0_0" Pin0InfoVect1LinkObjId="EC-WD_JY.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-276 4224,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1588530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-362 4429,-382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22603@1" ObjectIDZND0="22601@0" Pin0InfoVect0LinkObjId="SW-121348_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121352_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-362 4429,-382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1588790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-418 4429,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22601@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121348_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-418 4429,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15889f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-335 4429,-311 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22603@0" ObjectIDZND0="22602@1" Pin0InfoVect0LinkObjId="SW-121349_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121352_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-335 4429,-311 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1588c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4429,-275 4429,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="22602@0" ObjectIDZND0="g_163ac10@0" ObjectIDZND1="33945@x" Pin0InfoVect0LinkObjId="g_163ac10_0" Pin0InfoVect1LinkObjId="EC-WD_JY.094Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121349_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4429,-275 4429,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1589460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-356 4642,-381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22606@1" ObjectIDZND0="22604@0" Pin0InfoVect0LinkObjId="SW-121376_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121380_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-356 4642,-381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15896a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-417 4642,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22604@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-417 4642,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1589900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-329 4642,-303 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22606@0" ObjectIDZND0="22605@1" Pin0InfoVect0LinkObjId="SW-121377_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-329 4642,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-358 4855,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22609@1" ObjectIDZND0="22607@0" Pin0InfoVect0LinkObjId="SW-121404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-358 4855,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-422 4855,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22607@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-422 4855,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_158a5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-331 4855,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22609@0" ObjectIDZND0="22608@1" Pin0InfoVect0LinkObjId="SW-121405_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121408_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-331 4855,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f46b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-216 5003,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_14f3ce0@1" Pin0InfoVect0LinkObjId="g_14f3ce0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-216 5003,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f56d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5003,-245 5003,-268 5060,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="capacitor" EndDevType1="switch" ObjectIDZND0="39488@x" ObjectIDZND1="28403@x" Pin0InfoVect0LinkObjId="CB-WD_JY.WD_JY_cb1_0" Pin0InfoVect1LinkObjId="SW-187462_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5003,-245 5003,-268 5060,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f61c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-268 5060,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="28403@x" ObjectIDZND0="39488@0" Pin0InfoVect0LinkObjId="CB-WD_JY.WD_JY_cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187462_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-268 5060,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f6420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-363 5060,-385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="22611@1" ObjectIDZND0="22610@0" Pin0InfoVect0LinkObjId="SW-121432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-363 5060,-385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f6680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-421 5060,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22610@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-421 5060,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f6eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-337 5266,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1599760@0" ObjectIDND1="g_1598eb0@0" ObjectIDZND0="22890@0" Pin0InfoVect0LinkObjId="SW-124631_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1599760_0" Pin1InfoVect1LinkObjId="g_1598eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-337 5266,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14f7110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5266,-422 5266,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="22890@1" ObjectIDZND0="22591@0" Pin0InfoVect0LinkObjId="g_15a4a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-124631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5266,-422 5266,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-560 4821,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="transformer2" ObjectIDND0="22620@1" ObjectIDZND0="22629@x" ObjectIDZND1="22629@x" Pin0InfoVect0LinkObjId="g_1573dd0_0" Pin0InfoVect1LinkObjId="g_1573dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121559_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-560 4821,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-612 4821,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="22629@x" ObjectIDND1="22620@x" ObjectIDZND0="22629@x" Pin0InfoVect0LinkObjId="g_1573dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1573dd0_0" Pin1InfoVect1LinkObjId="SW-121559_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-612 4821,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151da80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-773 4821,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22619@1" ObjectIDZND0="22618@x" ObjectIDZND1="39486@x" Pin0InfoVect0LinkObjId="SW-121557_0" Pin0InfoVect1LinkObjId="SW-237120_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121558_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-773 4821,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151dce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-780 4821,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22619@x" ObjectIDND1="39486@x" ObjectIDZND0="22618@0" Pin0InfoVect0LinkObjId="SW-121557_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121558_0" Pin1InfoVect1LinkObjId="SW-237120_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-780 4821,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151e7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-779 4080,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22614@1" ObjectIDZND0="22613@x" ObjectIDZND1="39485@x" Pin0InfoVect0LinkObjId="SW-121538_0" Pin0InfoVect1LinkObjId="SW-237119_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121539_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-779 4080,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_151ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-786 4080,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22614@x" ObjectIDND1="39485@x" ObjectIDZND0="22613@0" Pin0InfoVect0LinkObjId="SW-121538_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121539_0" Pin1InfoVect1LinkObjId="SW-237119_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-786 4080,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1521110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4821,-780 4797,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22619@x" ObjectIDND1="22618@x" ObjectIDZND0="39486@1" Pin0InfoVect0LinkObjId="SW-237120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121558_0" Pin1InfoVect1LinkObjId="SW-121557_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4821,-780 4797,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1521370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4761,-780 4742,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39486@0" ObjectIDZND0="g_1436de0@0" Pin0InfoVect0LinkObjId="g_1436de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4761,-780 4742,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1436920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4080,-786 4057,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22614@x" ObjectIDND1="22613@x" ObjectIDZND0="39485@1" Pin0InfoVect0LinkObjId="SW-237119_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-121539_0" Pin1InfoVect1LinkObjId="SW-121538_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4080,-786 4057,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1436b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4021,-786 3998,-786 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="39485@0" ObjectIDZND0="g_1437870@0" Pin0InfoVect0LinkObjId="g_1437870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237119_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4021,-786 3998,-786 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-876 4519,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="22737@0" ObjectIDND1="22621@x" ObjectIDZND0="28401@1" Pin0InfoVect0LinkObjId="SW-187461_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15b70e0_0" Pin1InfoVect1LinkObjId="SW-121560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-876 4519,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143af10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4483,-876 4464,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="28401@0" ObjectIDZND0="g_143b170@0" Pin0InfoVect0LinkObjId="g_143b170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187461_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4483,-876 4464,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143c870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-856 4543,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="22737@0" ObjectIDZND0="28401@x" ObjectIDZND1="22621@x" Pin0InfoVect0LinkObjId="SW-187461_0" Pin0InfoVect1LinkObjId="SW-121560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b70e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-856 4543,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143ca60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4543,-876 4543,-910 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="28401@x" ObjectIDND1="22737@0" ObjectIDZND0="22621@0" Pin0InfoVect0LinkObjId="SW-121560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-187461_0" Pin1InfoVect1LinkObjId="g_15b70e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4543,-876 4543,-910 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-780 4373,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_152c530@0" ObjectIDZND0="39487@1" Pin0InfoVect0LinkObjId="SW-237121_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_152c530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-780 4373,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4337,-780 4318,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="39487@0" ObjectIDZND0="22616@x" ObjectIDZND1="g_1579ea0@0" Pin0InfoVect0LinkObjId="SW-121541_0" Pin0InfoVect1LinkObjId="g_1579ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-237121_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4337,-780 4318,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152c070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-789 4317,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="22616@0" ObjectIDZND0="39487@x" ObjectIDZND1="g_1579ea0@0" Pin0InfoVect0LinkObjId="SW-237121_0" Pin0InfoVect1LinkObjId="g_1579ea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-121541_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-789 4317,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_152c2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4317,-780 4317,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="39487@x" ObjectIDND1="22616@x" ObjectIDZND0="g_1579ea0@0" Pin0InfoVect0LinkObjId="g_1579ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-237121_0" Pin1InfoVect1LinkObjId="SW-121541_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4317,-780 4317,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147be50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-268 5060,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" ObjectIDND0="39488@x" ObjectIDZND0="28403@0" Pin0InfoVect0LinkObjId="SW-187462_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-WD_JY.WD_JY_cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-268 5060,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_147c0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-321 5060,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28403@1" ObjectIDZND0="22611@0" Pin0InfoVect0LinkObjId="SW-121434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-187462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-321 5060,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e7060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4799,-206 4799,-224 4855,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_15d5b70@0" ObjectIDZND0="33947@x" ObjectIDZND1="22608@x" Pin0InfoVect0LinkObjId="EC-WD_JY.096Ld_0" Pin0InfoVect1LinkObjId="SW-121405_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15d5b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4799,-206 4799,-224 4855,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e7b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-175 4855,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33947@0" ObjectIDZND0="g_15d5b70@0" ObjectIDZND1="22608@x" Pin0InfoVect0LinkObjId="g_15d5b70_0" Pin0InfoVect1LinkObjId="SW-121405_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.096Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-175 4855,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14e7db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-224 4855,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_15d5b70@0" ObjectIDND1="33947@x" ObjectIDZND0="22608@0" Pin0InfoVect0LinkObjId="SW-121405_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15d5b70_0" Pin1InfoVect1LinkObjId="EC-WD_JY.096Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-224 4855,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1552b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-235 4582,-252 4642,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_15c2f10@0" ObjectIDZND0="33946@x" ObjectIDZND1="22605@x" ObjectIDZND2="g_1553f90@0" Pin0InfoVect0LinkObjId="EC-WD_JY.095Ld_0" Pin0InfoVect1LinkObjId="SW-121377_0" Pin0InfoVect2LinkObjId="g_1553f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15c2f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-235 4582,-252 4642,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1553870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-176 4642,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="33946@0" ObjectIDZND0="g_15c2f10@0" ObjectIDZND1="22605@x" ObjectIDZND2="g_1553f90@0" Pin0InfoVect0LinkObjId="g_15c2f10_0" Pin0InfoVect1LinkObjId="SW-121377_0" Pin0InfoVect2LinkObjId="g_1553f90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-WD_JY.095Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-176 4642,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1553ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-252 4642,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_15c2f10@0" ObjectIDND1="33946@x" ObjectIDND2="g_1553f90@0" ObjectIDZND0="22605@0" Pin0InfoVect0LinkObjId="SW-121377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15c2f10_0" Pin1InfoVect1LinkObjId="EC-WD_JY.095Ld_0" Pin1InfoVect2LinkObjId="g_1553f90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-252 4642,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1553d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4642,-252 4705,-252 4705,-236 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_15c2f10@0" ObjectIDND1="33946@x" ObjectIDND2="22605@x" ObjectIDZND0="g_1553f90@0" Pin0InfoVect0LinkObjId="g_1553f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15c2f10_0" Pin1InfoVect1LinkObjId="EC-WD_JY.095Ld_0" Pin1InfoVect2LinkObjId="SW-121377_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4642,-252 4705,-252 4705,-236 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1556d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4705,-196 4741,-196 4741,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1556040@0" Pin0InfoVect0LinkObjId="g_1556040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4705,-196 4741,-196 4741,-179 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-119712" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3490.500000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22309" ObjectName="DYN-WD_JY"/>
     <cge:Meas_Ref ObjectId="119712"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="5003" cy="-209" fill="none" fillStyle="0" r="6.5" stroke="rgb(118,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5000" cy="-202" fill="none" fillStyle="0" r="6.5" stroke="rgb(118,238,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="5009" cy="-202" fill="none" fillStyle="0" r="6.5" stroke="rgb(118,238,0)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="WD_JY" endPointId="0" endStationName="WD_DP" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dongji" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4542,-1136 4542,-1068 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="42840" ObjectName="AC-35kV.LN_dongji"/>
    <cge:TPSR_Ref TObjectID="42840_SS-176"/></metadata>
   <polyline fill="none" opacity="0" points="4542,-1136 4542,-1068 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="22737" cx="4543" cy="-856" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="3813" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4016" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4224" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4429" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4642" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4855" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="5060" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="5266" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22737" cx="4080" cy="-856" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4080" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22737" cx="4317" cy="-856" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22737" cx="4543" cy="-856" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22591" cx="4820" cy="-441" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="22737" cx="4821" cy="-856" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-121539">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -744.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22614" ObjectName="SW-WD_JY.WD_JY_301BK"/>
     <cge:Meas_Ref ObjectId="121539"/>
    <cge:TPSR_Ref TObjectID="22614"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121558">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -738.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22619" ObjectName="SW-WD_JY.WD_JY_302BK"/>
     <cge:Meas_Ref ObjectId="121558"/>
    <cge:TPSR_Ref TObjectID="22619"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121540">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 -538.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22615" ObjectName="SW-WD_JY.WD_JY_001BK"/>
     <cge:Meas_Ref ObjectId="121540"/>
    <cge:TPSR_Ref TObjectID="22615"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121559">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4812.000000 -525.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22620" ObjectName="SW-WD_JY.WD_JY_002BK"/>
     <cge:Meas_Ref ObjectId="121559"/>
    <cge:TPSR_Ref TObjectID="22620"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121272">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22594" ObjectName="SW-WD_JY.WD_JY_091BK"/>
     <cge:Meas_Ref ObjectId="121272"/>
    <cge:TPSR_Ref TObjectID="22594"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4007.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22597" ObjectName="SW-WD_JY.WD_JY_092BK"/>
     <cge:Meas_Ref ObjectId="121298"/>
    <cge:TPSR_Ref TObjectID="22597"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121324">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22600" ObjectName="SW-WD_JY.WD_JY_093BK"/>
     <cge:Meas_Ref ObjectId="121324"/>
    <cge:TPSR_Ref TObjectID="22600"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -327.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22603" ObjectName="SW-WD_JY.WD_JY_094BK"/>
     <cge:Meas_Ref ObjectId="121352"/>
    <cge:TPSR_Ref TObjectID="22603"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121380">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4633.000000 -321.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22606" ObjectName="SW-WD_JY.WD_JY_095BK"/>
     <cge:Meas_Ref ObjectId="121380"/>
    <cge:TPSR_Ref TObjectID="22606"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121408">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -323.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22609" ObjectName="SW-WD_JY.WD_JY_096BK"/>
     <cge:Meas_Ref ObjectId="121408"/>
    <cge:TPSR_Ref TObjectID="22609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-121434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5051.000000 -328.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22611" ObjectName="SW-WD_JY.WD_JY_097BK"/>
     <cge:Meas_Ref ObjectId="121434"/>
    <cge:TPSR_Ref TObjectID="22611"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,16)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,56)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,96)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,136)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_159ef80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -1028.000000) translate(0,176)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,16)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,36)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,56)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,76)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,96)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,116)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,136)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,156)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,176)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,196)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,216)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,236)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,256)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,276)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,296)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,316)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_140f8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3237.000000 -589.000000) translate(0,336)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="40" graphid="g_12c4900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3358.000000 -1178.500000) translate(0,32)">己衣变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1076ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -692.000000) translate(0,16)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1076ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -692.000000) translate(0,36)">SZ11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1076ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -692.000000) translate(0,56)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1076ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -692.000000) translate(0,76)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1076ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3831.000000 -692.000000) translate(0,96)">Ud=7.15%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -736.000000) translate(0,16)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -736.000000) translate(0,36)">S11-2500/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -736.000000) translate(0,56)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -736.000000) translate(0,76)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1573fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4904.000000 -736.000000) translate(0,96)">Ud=7.09%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1638130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4480.000000 -662.000000) translate(0,16)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_157acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -682.000000) translate(0,16)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_157acd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4343.000000 -682.000000) translate(0,36)">S8-50/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1570240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -219.000000) translate(0,16)">己</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1570240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -219.000000) translate(0,36)">衣</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1570240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -219.000000) translate(0,56)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1570240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -219.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1571720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -750.000000) translate(0,16)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1571960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -821.000000) translate(0,16)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1571b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -825.000000) translate(0,16)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1571cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4099.000000 -776.000000) translate(0,16)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1571e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.000000 -937.000000) translate(0,16)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1572020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -817.000000) translate(0,16)">3921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_1572730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5316.000000 -424.000000) translate(0,20)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_15500c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4999.000000 -861.000000) translate(0,20)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1523b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -219.000000) translate(0,16)">板</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1523b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -219.000000) translate(0,36)">桥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1523b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -219.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1548380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -220.000000) translate(0,16)">水</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1548380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -220.000000) translate(0,36)">库</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1548380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -220.000000) translate(0,56)">专</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1548380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -220.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_163c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -219.000000) translate(0,16)">以</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_163c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -219.000000) translate(0,36)">吐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_163c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -219.000000) translate(0,56)">庄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_163c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4446.000000 -219.000000) translate(0,76)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15c4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -216.000000) translate(0,16)">万</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15c4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -216.000000) translate(0,36)">德</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15c4510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4615.000000 -216.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15d7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -215.000000) translate(0,16)">新</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15d7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -215.000000) translate(0,36)">民</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_15d7170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4872.000000 -215.000000) translate(0,56)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1596b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5038.000000 -200.000000) translate(0,16)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1598a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5171.000000 -183.000000) translate(0,16)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14b3270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4093.000000 -567.000000) translate(0,16)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14b3640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -770.000000) translate(0,16)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14b3820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4833.000000 -817.000000) translate(0,16)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14b3a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4835.000000 -557.000000) translate(0,16)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14b3be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4598.000000 -966.000000) translate(0,16)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3822.000000 -356.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b3fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4025.000000 -356.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4233.000000 -357.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -356.000000) translate(0,12)">094</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4651.000000 -350.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -352.000000) translate(0,12)">096</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5069.000000 -357.000000) translate(0,12)">097</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_14f1240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4698.000000 -712.000000) translate(0,17)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f1700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -729.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14f1e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -501.000000) translate(0,16)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1519670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -493.000000) translate(0,16)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_151a1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -1159.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_151a680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3547.000000 -1194.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f7370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5273.000000 -411.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f7860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5067.000000 -410.000000) translate(0,12)">0971</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f7aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4862.000000 -411.000000) translate(0,12)">0961</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4862.000000 -296.000000) translate(0,12)">0966</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f7f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -406.000000) translate(0,12)">0951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4649.000000 -292.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f83a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4436.000000 -407.000000) translate(0,12)">0941</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f85e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4436.000000 -300.000000) translate(0,12)">0946</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -408.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4231.000000 -301.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -410.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f8ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4023.000000 -301.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f9120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3820.000000 -411.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14f9360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3820.000000 -299.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1438300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -814.000000) translate(0,16)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14387f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4759.000000 -806.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_143bc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4470.000000 -904.000000) translate(0,16)">39160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_152cfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -774.000000) translate(0,16)">39217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_152f680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3566.500000 -1084.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1479700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -705.000000) translate(0,16)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_147c310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5070.000000 -307.000000) translate(0,12)">0976</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e0bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3204.000000 -216.500000) translate(0,16)">武定巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3343.000000 -236.500000) translate(0,16)">18787878990</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3343.000000 -236.500000) translate(0,36)">18787842893</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e2200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3343.000000 -236.500000) translate(0,56)">13987880311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e2790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3343.000000 -265.000000) translate(0,16)">8863188</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1135.000000) translate(0,16)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1135.000000) translate(0,36)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1135.000000) translate(0,56)">东</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1135.000000) translate(0,76)">己</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14e4960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -1135.000000) translate(0,96)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1556fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -97.000000) translate(0,16)">2号站用变</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_15822a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -910.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1570fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4577.000000 -692.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1436de0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -774.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1437870" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 -780.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_143b170" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4446.000000 -870.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_152c530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4389.000000 -774.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="WD_JY"/>
</svg>