<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-215" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-199 -1068 2068 1133">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape6_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="38" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="38" y2="13"/>
   </symbol>
   <symbol id="switch2:shape6_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="13" y2="14"/>
   </symbol>
   <symbol id="switch2:shape6-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="12" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="11" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="54" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape122">
    <ellipse cx="27" cy="15" rx="7" ry="7.5" stroke-width="0.726474"/>
    <ellipse cx="7" cy="15" rx="7" ry="7.5" stroke-width="0.726474"/>
    <ellipse cx="17" cy="22" rx="7" ry="7.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="27" x2="27" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="24" x2="27" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="30" x2="27" y1="18" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="17" x2="17" y1="7" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="14" x2="17" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="20" x2="17" y1="9" y2="7"/>
    <ellipse cx="17" cy="8" rx="7" ry="7.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="4" x2="8" y1="16" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="4" x2="8" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="8" x2="8" y1="13" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="17" x2="17" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="14" x2="17" y1="25" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="20" x2="17" y1="25" y2="23"/>
   </symbol>
   <symbol id="voltageTransformer:shape73">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="4" y2="4"/>
    <circle cx="25" cy="25" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="22" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="12" x2="28" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="28" x2="28" y1="32" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="51" y1="23" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="67" x2="59" y1="30" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="59" x2="59" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="32" y1="59" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="48" x2="40" y1="66" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="40" y1="50" y2="59"/>
    <circle cx="57" cy="26" fillStyle="0" r="25" stroke-width="0.520408"/>
    <circle cx="41" cy="55" fillStyle="0" r="25" stroke-width="0.520408"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_511a8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_511b510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_511bc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_511c910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_511d850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_511e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_511ebb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_511f670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_5120890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_5120890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5122270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5122270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_51236a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_51236a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_51243a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5125fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5126b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5127950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_5128290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5129950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_512a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_512aad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_512b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_512c370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_512ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_512d7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_512e1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_512f520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5130090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_5130fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_51319d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_51401a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_5132fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_5133d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_5134ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1143" width="2078" x="-204" y="-1073"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1612" x2="1615" y1="-745" y2="-742"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-145183">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 -701.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25502" ObjectName="SW-YA_QC.YA_QC_3011SW"/>
     <cge:Meas_Ref ObjectId="145183"/>
    <cge:TPSR_Ref TObjectID="25502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145220">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 -388.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25508" ObjectName="SW-YA_QC.YA_QC_0011SW"/>
     <cge:Meas_Ref ObjectId="145220"/>
    <cge:TPSR_Ref TObjectID="25508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145179">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25499" ObjectName="SW-YA_QC.YA_QC_3211SW"/>
     <cge:Meas_Ref ObjectId="145179"/>
    <cge:TPSR_Ref TObjectID="25499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145180">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -845.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25500" ObjectName="SW-YA_QC.YA_QC_32117SW"/>
     <cge:Meas_Ref ObjectId="145180"/>
    <cge:TPSR_Ref TObjectID="25500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145217">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 474.000000 -650.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25506" ObjectName="SW-YA_QC.YA_QC_39017SW"/>
     <cge:Meas_Ref ObjectId="145217"/>
    <cge:TPSR_Ref TObjectID="25506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145215">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 397.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25504" ObjectName="SW-YA_QC.YA_QC_3901SW"/>
     <cge:Meas_Ref ObjectId="145215"/>
    <cge:TPSR_Ref TObjectID="25504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145216">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1121.000000 -795.000000)" xlink:href="#switch2:shape6_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25505" ObjectName="SW-YA_QC.YA_QC_32110SW"/>
     <cge:Meas_Ref ObjectId="145216"/>
    <cge:TPSR_Ref TObjectID="25505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145184">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 797.000000 -661.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25503" ObjectName="SW-YA_QC.YA_QC_30117SW"/>
     <cge:Meas_Ref ObjectId="145184"/>
    <cge:TPSR_Ref TObjectID="25503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.357143 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25517" ObjectName="SW-YA_QC.YA_QC_0431SW"/>
     <cge:Meas_Ref ObjectId="145281"/>
    <cge:TPSR_Ref TObjectID="25517"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.357143 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25518" ObjectName="SW-YA_QC.YA_QC_0436SW"/>
     <cge:Meas_Ref ObjectId="145282"/>
    <cge:TPSR_Ref TObjectID="25518"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145265">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.214286 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25514" ObjectName="SW-YA_QC.YA_QC_0411SW"/>
     <cge:Meas_Ref ObjectId="145265"/>
    <cge:TPSR_Ref TObjectID="25514"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145266">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.214286 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25515" ObjectName="SW-YA_QC.YA_QC_0416SW"/>
     <cge:Meas_Ref ObjectId="145266"/>
    <cge:TPSR_Ref TObjectID="25515"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.528571 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.528571 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145297">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.842857 -290.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25520" ObjectName="SW-YA_QC.YA_QC_0421SW"/>
     <cge:Meas_Ref ObjectId="145297"/>
    <cge:TPSR_Ref TObjectID="25520"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145298">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.842857 -130.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25521" ObjectName="SW-YA_QC.YA_QC_0426SW"/>
     <cge:Meas_Ref ObjectId="145298"/>
    <cge:TPSR_Ref TObjectID="25521"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.400000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25509" ObjectName="SW-YA_QC.YA_QC_0901SW"/>
     <cge:Meas_Ref ObjectId="145245"/>
    <cge:TPSR_Ref TObjectID="25509"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145311">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1648.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25522" ObjectName="SW-YA_QC.YA_QC_0471SW"/>
     <cge:Meas_Ref ObjectId="145311"/>
    <cge:TPSR_Ref TObjectID="25522"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29038" ObjectName="SW-YA_QC.YA_QC_3021SW"/>
     <cge:Meas_Ref ObjectId="192046"/>
    <cge:TPSR_Ref TObjectID="29038"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192057">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -396.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29041" ObjectName="SW-YA_QC.YA_QC_0021SW"/>
     <cge:Meas_Ref ObjectId="192057"/>
    <cge:TPSR_Ref TObjectID="29041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192047">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1360.000000 -669.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29039" ObjectName="SW-YA_QC.YA_QC_30217SW"/>
     <cge:Meas_Ref ObjectId="192047"/>
    <cge:TPSR_Ref TObjectID="29039"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192137">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 962.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29043" ObjectName="SW-YA_QC.YA_QC_0121SW"/>
     <cge:Meas_Ref ObjectId="192137"/>
    <cge:TPSR_Ref TObjectID="29043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192190">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1098.000000 -250.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29050" ObjectName="SW-YA_QC.YA_QC_0122SW"/>
     <cge:Meas_Ref ObjectId="192190"/>
    <cge:TPSR_Ref TObjectID="29050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192043">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1801.400000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29036" ObjectName="SW-YA_QC.YA_QC_0902SW"/>
     <cge:Meas_Ref ObjectId="192043"/>
    <cge:TPSR_Ref TObjectID="29036"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192146">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.685714 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29045" ObjectName="SW-YA_QC.YA_QC_0441SW"/>
     <cge:Meas_Ref ObjectId="192146"/>
    <cge:TPSR_Ref TObjectID="29045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192147">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.685714 -129.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29046" ObjectName="SW-YA_QC.YA_QC_0446SW"/>
     <cge:Meas_Ref ObjectId="192147"/>
    <cge:TPSR_Ref TObjectID="29046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192167">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.685714 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29048" ObjectName="SW-YA_QC.YA_QC_0451SW"/>
     <cge:Meas_Ref ObjectId="192167"/>
    <cge:TPSR_Ref TObjectID="29048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192168">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.685714 -131.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29049" ObjectName="SW-YA_QC.YA_QC_0456SW"/>
     <cge:Meas_Ref ObjectId="192168"/>
    <cge:TPSR_Ref TObjectID="29049"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_QC.YA_QC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-779 1792,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25496" ObjectName="BS-YA_QC.YA_QC_3M"/>
    <cge:TPSR_Ref TObjectID="25496"/></metadata>
   <polyline fill="none" opacity="0" points="352,-779 1792,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_QC.YA_QC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-363 1001,-363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25497" ObjectName="BS-YA_QC.YA_QC_9IM"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   <polyline fill="none" opacity="0" points="276,-363 1001,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_QC.YA_QC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-363 1853,-363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="29057" ObjectName="BS-YA_QC.YA_QC_9IIM"/>
    <cge:TPSR_Ref TObjectID="29057"/></metadata>
   <polyline fill="none" opacity="0" points="1102,-363 1853,-363 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_QC.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.357143 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34272" ObjectName="EC-YA_QC.043Ld"/>
    <cge:TPSR_Ref TObjectID="34272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_QC.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 448.214286 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34270" ObjectName="EC-YA_QC.041Ld"/>
    <cge:TPSR_Ref TObjectID="34270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1224.528571 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_QC.042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 640.842857 -27.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34271" ObjectName="EC-YA_QC.042Ld"/>
    <cge:TPSR_Ref TObjectID="34271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_QC.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.685714 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34273" ObjectName="EC-YA_QC.044Ld"/>
    <cge:TPSR_Ref TObjectID="34273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_QC.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.685714 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34274" ObjectName="EC-YA_QC.045Ld"/>
    <cge:TPSR_Ref TObjectID="34274"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_50bf200">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1635.000000 -766.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_50c65a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.800000 -919.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cc2770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 878.357143 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4ccc870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 490.214286 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4cd6b50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1266.528571 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4ce02d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 682.842857 -43.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d1dee0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.685714 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d286b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1535.685714 -44.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d36a10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 397.000000 -652.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d37a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 326.000000 -643.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d3a110">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -202.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d3b180">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 261.000000 -211.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d3e490">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1806.000000 -198.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d3f500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1756.000000 -207.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d4c990">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1284.000000 -189.528571)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d4d470">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1285.000000 -273.528571)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -975.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217889" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -812.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217889" ObjectName="YA_QC:YA_QC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219740" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -771.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219740" ObjectName="YA_QC:YA_QC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217889" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -895.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217889" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217889" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -850.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217889" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-245670" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -450.000000) translate(0,14)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="245670" ObjectName="YA_QC:YA_QC_002BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-245671" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -434.000000) translate(0,14)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="245671" ObjectName="YA_QC:YA_QC_002BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-245672" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -418.000000) translate(0,14)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="245672" ObjectName="YA_QC:YA_QC_002BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-192212" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -404.000000) translate(0,14)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192212" ObjectName="YA_QC:YA_QC_9IIM_U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-245673" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="17" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1663.000000 -389.000000) translate(0,14)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="245673" ObjectName="YA_QC:YA_QC_002BK_Uab"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-145095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -850.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25496"/>
     <cge:Term_Ref ObjectID="35923"/>
    <cge:TPSR_Ref TObjectID="25496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-145096" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -850.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145096" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25496"/>
     <cge:Term_Ref ObjectID="35923"/>
    <cge:TPSR_Ref TObjectID="25496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-145097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -850.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25496"/>
     <cge:Term_Ref ObjectID="35923"/>
    <cge:TPSR_Ref TObjectID="25496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-145098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 -850.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25496"/>
     <cge:Term_Ref ObjectID="35923"/>
    <cge:TPSR_Ref TObjectID="25496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-230136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -452.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25497"/>
     <cge:Term_Ref ObjectID="35924"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-230137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -452.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25497"/>
     <cge:Term_Ref ObjectID="35924"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-230138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -452.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25497"/>
     <cge:Term_Ref ObjectID="35924"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-145115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -452.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25497"/>
     <cge:Term_Ref ObjectID="35924"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-230139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 481.000000 -452.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="230139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25497"/>
     <cge:Term_Ref ObjectID="35924"/>
    <cge:TPSR_Ref TObjectID="25497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -668.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25501"/>
     <cge:Term_Ref ObjectID="35931"/>
    <cge:TPSR_Ref TObjectID="25501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -668.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25501"/>
     <cge:Term_Ref ObjectID="35931"/>
    <cge:TPSR_Ref TObjectID="25501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145089" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 726.000000 -668.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145089" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25501"/>
     <cge:Term_Ref ObjectID="35931"/>
    <cge:TPSR_Ref TObjectID="25501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -496.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25507"/>
     <cge:Term_Ref ObjectID="35943"/>
    <cge:TPSR_Ref TObjectID="25507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145107" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -496.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145107" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25507"/>
     <cge:Term_Ref ObjectID="35943"/>
    <cge:TPSR_Ref TObjectID="25507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145103" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 722.000000 -496.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145103" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25507"/>
     <cge:Term_Ref ObjectID="35943"/>
    <cge:TPSR_Ref TObjectID="25507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 17.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25513"/>
     <cge:Term_Ref ObjectID="35955"/>
    <cge:TPSR_Ref TObjectID="25513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 17.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25513"/>
     <cge:Term_Ref ObjectID="35955"/>
    <cge:TPSR_Ref TObjectID="25513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 439.000000 17.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25513"/>
     <cge:Term_Ref ObjectID="35955"/>
    <cge:TPSR_Ref TObjectID="25513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25519"/>
     <cge:Term_Ref ObjectID="35967"/>
    <cge:TPSR_Ref TObjectID="25519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25519"/>
     <cge:Term_Ref ObjectID="35967"/>
    <cge:TPSR_Ref TObjectID="25519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25519"/>
     <cge:Term_Ref ObjectID="35967"/>
    <cge:TPSR_Ref TObjectID="25519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-145132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25516"/>
     <cge:Term_Ref ObjectID="35961"/>
    <cge:TPSR_Ref TObjectID="25516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-145133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25516"/>
     <cge:Term_Ref ObjectID="35961"/>
    <cge:TPSR_Ref TObjectID="25516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-145129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 830.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="145129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25516"/>
     <cge:Term_Ref ObjectID="35961"/>
    <cge:TPSR_Ref TObjectID="25516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192012" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 -675.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192012" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29037"/>
     <cge:Term_Ref ObjectID="41401"/>
    <cge:TPSR_Ref TObjectID="29037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192013" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 -675.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192013" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29037"/>
     <cge:Term_Ref ObjectID="41401"/>
    <cge:TPSR_Ref TObjectID="29037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192009" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1271.000000 -675.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192009" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29037"/>
     <cge:Term_Ref ObjectID="41401"/>
    <cge:TPSR_Ref TObjectID="29037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192018" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192018" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29040"/>
     <cge:Term_Ref ObjectID="41407"/>
    <cge:TPSR_Ref TObjectID="29040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192019" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192019" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29040"/>
     <cge:Term_Ref ObjectID="41407"/>
    <cge:TPSR_Ref TObjectID="29040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192015" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192015" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29040"/>
     <cge:Term_Ref ObjectID="41407"/>
    <cge:TPSR_Ref TObjectID="29040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192030" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1342.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192030" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29044"/>
     <cge:Term_Ref ObjectID="41415"/>
    <cge:TPSR_Ref TObjectID="29044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192031" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1342.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192031" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29044"/>
     <cge:Term_Ref ObjectID="41415"/>
    <cge:TPSR_Ref TObjectID="29044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192027" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1342.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192027" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29044"/>
     <cge:Term_Ref ObjectID="41415"/>
    <cge:TPSR_Ref TObjectID="29044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192036" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192036" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29047"/>
     <cge:Term_Ref ObjectID="41421"/>
    <cge:TPSR_Ref TObjectID="29047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192037" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192037" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29047"/>
     <cge:Term_Ref ObjectID="41421"/>
    <cge:TPSR_Ref TObjectID="29047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192033" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1484.000000 18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192033" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29047"/>
     <cge:Term_Ref ObjectID="41421"/>
    <cge:TPSR_Ref TObjectID="29047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-192039" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -589.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192039" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29051"/>
     <cge:Term_Ref ObjectID="41429"/>
    <cge:TPSR_Ref TObjectID="29051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-192040" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -589.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192040" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29051"/>
     <cge:Term_Ref ObjectID="41429"/>
    <cge:TPSR_Ref TObjectID="29051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-192024" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -211.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192024" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29042"/>
     <cge:Term_Ref ObjectID="41411"/>
    <cge:TPSR_Ref TObjectID="29042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-192025" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -211.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192025" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29042"/>
     <cge:Term_Ref ObjectID="41411"/>
    <cge:TPSR_Ref TObjectID="29042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-192021" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 -211.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="192021" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29042"/>
     <cge:Term_Ref ObjectID="41411"/>
    <cge:TPSR_Ref TObjectID="29042"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="30" qtmmishow="hidden" width="120" x="-148" y="-689"/>
    </a>
   <metadata/><rect fill="white" height="30" opacity="0" stroke="white" transform="" width="120" x="-148" y="-689"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="171" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="171" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="171" y="-1068"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="171" y="-1068"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="463" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="463" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="655" y="-262"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="655" y="-262"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="851" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="851" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1369" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1369" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1508" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1508" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1041" y="-238"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1041" y="-238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="103,-1054 100,-1057 100,-1004 103,-1007 103,-1054" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="103,-1054 100,-1057 151,-1057 148,-1054 103,-1054" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="103,-1007 100,-1004 151,-1004 148,-1007 103,-1007" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="148,-1054 151,-1057 151,-1004 148,-1007 148,-1054" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="103" y="-1054"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="103" y="-1054"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="820" y="-580"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="820" y="-580"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1377" y="-589"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1377" y="-589"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="35kV前场变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="30" qtmmishow="hidden" width="120" x="-148" y="-689"/></g>
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="171" y="-1029"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="171" y="-1068"/></g>
   <g href="35kV前场变YA_QC_041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="463" y="-263"/></g>
   <g href="35kV前场变YA_QC_042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="655" y="-262"/></g>
   <g href="35kV前场变10kV适中线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="851" y="-263"/></g>
   <g href="35kV前场变10kV楚姚高速Ⅰ回线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1369" y="-263"/></g>
   <g href="35kV前场变10kV楚姚高速Ⅱ回线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1508" y="-263"/></g>
   <g href="35kV前场变10kV分段012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1041" y="-238"/></g>
   <g href="AVC前场站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="103" y="-1054"/></g>
   <g href="35kV前场变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="820" y="-580"/></g>
   <g href="35kV前场变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1377" y="-589"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-145181">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 772.000000 -619.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25501" ObjectName="SW-YA_QC.YA_QC_301BK"/>
     <cge:Meas_Ref ObjectId="145181"/>
    <cge:TPSR_Ref TObjectID="25501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145218">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 -455.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25507" ObjectName="SW-YA_QC.YA_QC_001BK"/>
     <cge:Meas_Ref ObjectId="145218"/>
    <cge:TPSR_Ref TObjectID="25507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145279">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.357143 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25516" ObjectName="SW-YA_QC.YA_QC_043BK"/>
     <cge:Meas_Ref ObjectId="145279"/>
    <cge:TPSR_Ref TObjectID="25516"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145263">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 444.214286 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25513" ObjectName="SW-YA_QC.YA_QC_041BK"/>
     <cge:Meas_Ref ObjectId="145263"/>
    <cge:TPSR_Ref TObjectID="25513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1220.528571 -215.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-145295">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 636.842857 -233.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25519" ObjectName="SW-YA_QC.YA_QC_042BK"/>
     <cge:Meas_Ref ObjectId="145295"/>
    <cge:TPSR_Ref TObjectID="25519"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -627.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29037" ObjectName="SW-YA_QC.YA_QC_302BK"/>
     <cge:Meas_Ref ObjectId="192044"/>
    <cge:TPSR_Ref TObjectID="29037"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192055">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.000000 -463.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29040" ObjectName="SW-YA_QC.YA_QC_002BK"/>
     <cge:Meas_Ref ObjectId="192055"/>
    <cge:TPSR_Ref TObjectID="29040"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192135">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1031.000000 -245.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29042" ObjectName="SW-YA_QC.YA_QC_012BK"/>
     <cge:Meas_Ref ObjectId="192135"/>
    <cge:TPSR_Ref TObjectID="29042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.685714 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29044" ObjectName="SW-YA_QC.YA_QC_044BK"/>
     <cge:Meas_Ref ObjectId="192144"/>
    <cge:TPSR_Ref TObjectID="29044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-192165">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.685714 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29047" ObjectName="SW-YA_QC.YA_QC_045BK"/>
     <cge:Meas_Ref ObjectId="192165"/>
    <cge:TPSR_Ref TObjectID="29047"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_QC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_qianchang" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1052,-1028 1052,-982 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37774" ObjectName="AC-35kV.LN_qianchang"/>
    <cge:TPSR_Ref TObjectID="37774_SS-215"/></metadata>
   <polyline fill="none" opacity="0" points="1052,-1028 1052,-982 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_50af5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-779 781,-742 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25496@0" ObjectIDZND0="25502@1" Pin0InfoVect0LinkObjId="SW-145183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cb58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="781,-779 781,-742 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_50b1c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="780,-463 780,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25507@0" ObjectIDZND0="25508@1" Pin0InfoVect0LinkObjId="SW-145220_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145218_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="780,-463 780,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50b1e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-627 781,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25501@0" ObjectIDZND0="25523@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="781,-627 781,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_50b20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="780,-525 780,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25523@0" ObjectIDZND0="25507@1" Pin0InfoVect0LinkObjId="SW-145218_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_50b1e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="780,-525 780,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50b5dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="971,-896 1052,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25500@0" ObjectIDZND0="25499@x" ObjectIDZND1="g_50c65a0@0" ObjectIDZND2="37774@1" Pin0InfoVect0LinkObjId="SW-145179_0" Pin0InfoVect1LinkObjId="g_50c65a0_0" Pin0InfoVect2LinkObjId="g_50b68c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="971,-896 1052,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50b68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-973 1052,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_50c65a0@0" ObjectIDND1="25500@x" ObjectIDND2="25499@x" ObjectIDZND0="37774@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_50c65a0_0" Pin1InfoVect1LinkObjId="SW-145180_0" Pin1InfoVect2LinkObjId="SW-145179_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-973 1052,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50b6b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-973 1131,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="25500@x" ObjectIDND1="25499@x" ObjectIDND2="37774@1" ObjectIDZND0="g_50c65a0@0" Pin0InfoVect0LinkObjId="g_50c65a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145180_0" Pin1InfoVect1LinkObjId="SW-145179_0" Pin1InfoVect2LinkObjId="g_50b68c0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-973 1131,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50bd230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-779 406,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25496@0" ObjectIDZND0="25504@1" Pin0InfoVect0LinkObjId="SW-145215_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cb58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="406,-779 406,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_50bfdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1630,-716 1630,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_50bf200@1" ObjectIDZND0="25524@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_50bf200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1630,-716 1630,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb1910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-858 1052,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25499@1" ObjectIDZND0="25500@x" ObjectIDZND1="g_50c65a0@0" ObjectIDZND2="37774@1" Pin0InfoVect0LinkObjId="SW-145180_0" Pin0InfoVect1LinkObjId="g_50c65a0_0" Pin0InfoVect2LinkObjId="g_50b68c0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145179_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-858 1052,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb1b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-896 1052,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="25500@x" ObjectIDND1="25499@x" ObjectIDZND0="g_50c65a0@0" ObjectIDZND1="37774@1" Pin0InfoVect0LinkObjId="g_50c65a0_0" Pin0InfoVect1LinkObjId="g_50b68c0_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145180_0" Pin1InfoVect1LinkObjId="SW-145179_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-896 1052,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb4b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1130,-800 1052,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25505@0" ObjectIDZND0="25499@x" ObjectIDZND1="25496@0" Pin0InfoVect0LinkObjId="SW-145179_0" Pin0InfoVect1LinkObjId="g_4cb58c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1130,-800 1052,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-823 1052,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="25499@0" ObjectIDZND0="25505@x" ObjectIDZND1="25496@0" Pin0InfoVect0LinkObjId="SW-145216_0" Pin0InfoVect1LinkObjId="g_4cb58c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145179_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-823 1052,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-800 1052,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="25505@x" ObjectIDND1="25499@x" ObjectIDZND0="25496@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145216_0" Pin1InfoVect1LinkObjId="SW-145179_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-800 1052,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,-701 406,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25506@0" ObjectIDZND0="25504@x" ObjectIDZND1="g_4d36a10@0" ObjectIDZND2="g_4d37a00@0" Pin0InfoVect0LinkObjId="SW-145215_0" Pin0InfoVect1LinkObjId="g_4d36a10_0" Pin0InfoVect2LinkObjId="g_4d37a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145217_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="483,-701 406,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb6810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-718 406,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25504@0" ObjectIDZND0="25506@x" ObjectIDZND1="g_4d36a10@0" ObjectIDZND2="g_4d37a00@0" Pin0InfoVect0LinkObjId="SW-145217_0" Pin0InfoVect1LinkObjId="g_4d36a10_0" Pin0InfoVect2LinkObjId="g_4d37a00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="406,-718 406,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cb9750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="802,-668 781,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25503@0" ObjectIDZND0="25501@x" ObjectIDZND1="25502@x" Pin0InfoVect0LinkObjId="SW-145181_0" Pin0InfoVect1LinkObjId="SW-145183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145184_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="802,-668 781,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cb99b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-269 841,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25516@1" ObjectIDZND0="25517@0" Pin0InfoVect0LinkObjId="SW-145281_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145279_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-269 841,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cb9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-172 841,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25518@1" ObjectIDZND0="25516@0" Pin0InfoVect0LinkObjId="SW-145279_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-172 841,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc1df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-332 841,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25517@1" ObjectIDZND0="25497@0" Pin0InfoVect0LinkObjId="g_4ccc150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145281_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-332 841,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc2050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="885,-98 841,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_4cc2770@0" ObjectIDZND0="34272@x" ObjectIDZND1="25518@x" Pin0InfoVect0LinkObjId="EC-YA_QC.043Ld_0" Pin0InfoVect1LinkObjId="SW-145282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cc2770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="885,-98 841,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc22b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-49 841,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34272@0" ObjectIDZND0="g_4cc2770@0" ObjectIDZND1="25518@x" Pin0InfoVect0LinkObjId="g_4cc2770_0" Pin0InfoVect1LinkObjId="SW-145282_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_QC.043Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="841,-49 841,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc2510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="841,-98 841,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4cc2770@0" ObjectIDND1="34272@x" ObjectIDZND0="25518@0" Pin0InfoVect0LinkObjId="SW-145282_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4cc2770_0" Pin1InfoVect1LinkObjId="EC-YA_QC.043Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="841,-98 841,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc34a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,-269 453,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25513@1" ObjectIDZND0="25514@0" Pin0InfoVect0LinkObjId="SW-145265_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="453,-269 453,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc3700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,-172 453,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25515@1" ObjectIDZND0="25513@0" Pin0InfoVect0LinkObjId="SW-145263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="453,-172 453,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cc4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="497,-98 453,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_4ccc870@0" ObjectIDZND0="25515@x" ObjectIDZND1="34270@x" Pin0InfoVect0LinkObjId="SW-145266_0" Pin0InfoVect1LinkObjId="EC-YA_QC.041Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4ccc870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="497,-98 453,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ccc150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,-332 453,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25514@1" ObjectIDZND0="25497@0" Pin0InfoVect0LinkObjId="g_4cc1df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145265_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="453,-332 453,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ccc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,-98 453,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4ccc870@0" ObjectIDND1="34270@x" ObjectIDZND0="25515@0" Pin0InfoVect0LinkObjId="SW-145266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4ccc870_0" Pin1InfoVect1LinkObjId="EC-YA_QC.041Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="453,-98 453,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ccc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="453,-49 453,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34270@0" ObjectIDZND0="g_4ccc870@0" ObjectIDZND1="25515@x" Pin0InfoVect0LinkObjId="g_4ccc870_0" Pin0InfoVect1LinkObjId="SW-145266_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_QC.041Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="453,-49 453,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccd5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1230,-250 1230,-294 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-250 1230,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4ccd800">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1230,-170 1230,-223 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-170 1230,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cce670">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1274,-98 1230,-98 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_4cd6b50@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cd6b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1274,-98 1230,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cd3f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1230,-330 1230,-363 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="29057@0" Pin0InfoVect0LinkObjId="g_4d04820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-330 1230,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cd6690">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1230,-98 1230,-134 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_4cd6b50@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_4cd6b50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-98 1230,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_4cd68f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1230,-49 1230,-98 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_4cd6b50@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_4cd6b50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1230,-49 1230,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cd84b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="690,-97 646,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_4ce02d0@0" ObjectIDZND0="25521@x" ObjectIDZND1="34271@x" Pin0InfoVect0LinkObjId="SW-145298_0" Pin0InfoVect1LinkObjId="EC-YA_QC.042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4ce02d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="690,-97 646,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cddc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-331 646,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25520@1" ObjectIDZND0="25497@0" Pin0InfoVect0LinkObjId="g_4cc1df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-331 646,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce1000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-268 646,-295 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25519@1" ObjectIDZND0="25520@0" Pin0InfoVect0LinkObjId="SW-145297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-268 646,-295 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce1260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-97 646,-133 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4ce02d0@0" ObjectIDND1="34271@x" ObjectIDZND0="25521@0" Pin0InfoVect0LinkObjId="SW-145298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4ce02d0_0" Pin1InfoVect1LinkObjId="EC-YA_QC.042Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-97 646,-133 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce14c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-48 646,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34271@0" ObjectIDZND0="g_4ce02d0@0" ObjectIDZND1="25521@x" Pin0InfoVect0LinkObjId="g_4ce02d0_0" Pin0InfoVect1LinkObjId="SW-145298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_QC.042Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="646,-48 646,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce1720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="646,-171 646,-241 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25521@1" ObjectIDZND0="25519@0" Pin0InfoVect0LinkObjId="SW-145295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="646,-171 646,-241 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce4180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-363 315,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25497@0" ObjectIDZND0="25509@1" Pin0InfoVect0LinkObjId="SW-145245_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cc1df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-363 315,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4ce4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-295 1657,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="25522@0" ObjectIDZND0="25525@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145311_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-295 1657,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4cea6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1657,-363 1657,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29057@0" ObjectIDZND0="25522@1" Pin0InfoVect0LinkObjId="SW-145311_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cd3f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1657,-363 1657,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cf60b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1630,-779 1630,-761 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25496@0" ObjectIDZND0="g_50bf200@0" Pin0InfoVect0LinkObjId="g_50bf200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cb58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1630,-779 1630,-761 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cf62a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-654 781,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25501@1" ObjectIDZND0="25503@x" ObjectIDZND1="25502@x" Pin0InfoVect0LinkObjId="SW-145184_0" Pin0InfoVect1LinkObjId="SW-145183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="781,-654 781,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4cf6490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="781,-668 781,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25503@x" ObjectIDND1="25501@x" ObjectIDZND0="25502@0" Pin0InfoVect0LinkObjId="SW-145183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145184_0" Pin1InfoVect1LinkObjId="SW-145181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="781,-668 781,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d00300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-471 1344,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29040@0" ObjectIDZND0="29041@1" Pin0InfoVect0LinkObjId="SW-192057_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-471 1344,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d004f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-635 1344,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="29037@0" ObjectIDZND0="29051@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-635 1344,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d030c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1365,-676 1344,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29039@0" ObjectIDZND0="29037@x" ObjectIDZND1="29038@x" Pin0InfoVect0LinkObjId="SW-192044_0" Pin0InfoVect1LinkObjId="SW-192046_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192047_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1365,-676 1344,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d04250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-662 1344,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29037@1" ObjectIDZND0="29039@x" ObjectIDZND1="29038@x" Pin0InfoVect0LinkObjId="SW-192047_0" Pin0InfoVect1LinkObjId="SW-192046_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-662 1344,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d04440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-676 1344,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="29039@x" ObjectIDND1="29037@x" ObjectIDZND0="29038@0" Pin0InfoVect0LinkObjId="SW-192046_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192047_0" Pin1InfoVect1LinkObjId="SW-192044_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-676 1344,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d04630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-779 1344,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25496@0" ObjectIDZND0="29038@1" Pin0InfoVect0LinkObjId="SW-192046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cb58c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-779 1344,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d04820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-401 1344,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29041@0" ObjectIDZND0="29057@0" Pin0InfoVect0LinkObjId="g_4cd3f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192057_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-401 1344,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d074a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="780,-393 780,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25508@0" ObjectIDZND0="25497@0" Pin0InfoVect0LinkObjId="g_4cc1df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145220_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="780,-393 780,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d0a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="928,-363 928,-255 967,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25497@0" ObjectIDZND0="29043@0" Pin0InfoVect0LinkObjId="SW-192137_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cc1df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="928,-363 928,-255 967,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d0a7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1139,-255 1175,-255 1175,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29050@1" ObjectIDZND0="29057@0" Pin0InfoVect0LinkObjId="g_4cd3f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1139,-255 1175,-255 1175,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d0aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-255 1040,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29043@1" ObjectIDZND0="29042@1" Pin0InfoVect0LinkObjId="SW-192135_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192137_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-255 1040,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d0acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1067,-255 1103,-255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29042@0" ObjectIDZND0="29050@0" Pin0InfoVect0LinkObjId="SW-192190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192135_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1067,-255 1103,-255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d12a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-363 1810,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="29057@0" ObjectIDZND0="29036@1" Pin0InfoVect0LinkObjId="SW-192043_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4cd3f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-363 1810,-327 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d14a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-172 1360,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29046@1" ObjectIDZND0="29044@0" Pin0InfoVect0LinkObjId="SW-192144_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192147_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-172 1360,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d15750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1404,-98 1360,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_4d1dee0@0" ObjectIDZND0="29046@x" ObjectIDZND1="34273@x" Pin0InfoVect0LinkObjId="SW-192147_0" Pin0InfoVect1LinkObjId="EC-YA_QC.044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d1dee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1404,-98 1360,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d1b8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-332 1360,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29045@1" ObjectIDZND0="29057@0" Pin0InfoVect0LinkObjId="g_4cd3f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192146_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-332 1360,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d1ec10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-98 1360,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4d1dee0@0" ObjectIDND1="34273@x" ObjectIDZND0="29046@0" Pin0InfoVect0LinkObjId="SW-192147_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4d1dee0_0" Pin1InfoVect1LinkObjId="EC-YA_QC.044Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-98 1360,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d1ee70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-49 1360,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34273@0" ObjectIDZND0="29046@x" ObjectIDZND1="g_4d1dee0@0" Pin0InfoVect0LinkObjId="SW-192147_0" Pin0InfoVect1LinkObjId="g_4d1dee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_QC.044Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-49 1360,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d1f0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1360,-269 1360,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29044@1" ObjectIDZND0="29045@0" Pin0InfoVect0LinkObjId="SW-192146_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192144_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1360,-269 1360,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d20ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1543,-98 1499,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_4d286b0@0" ObjectIDZND0="29049@x" ObjectIDZND1="34274@x" Pin0InfoVect0LinkObjId="SW-192168_0" Pin0InfoVect1LinkObjId="EC-YA_QC.045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d286b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1543,-98 1499,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d26020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-332 1499,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29048@1" ObjectIDZND0="29057@0" Pin0InfoVect0LinkObjId="g_4cd3f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192167_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-332 1499,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d293e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-98 1499,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_4d286b0@0" ObjectIDND1="34274@x" ObjectIDZND0="29049@0" Pin0InfoVect0LinkObjId="SW-192168_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4d286b0_0" Pin1InfoVect1LinkObjId="EC-YA_QC.045Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-98 1499,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d29640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-49 1499,-98 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34274@0" ObjectIDZND0="29049@x" ObjectIDZND1="g_4d286b0@0" Pin0InfoVect0LinkObjId="SW-192168_0" Pin0InfoVect1LinkObjId="g_4d286b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_QC.045Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-49 1499,-98 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d298a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-269 1499,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="29047@1" ObjectIDZND0="29048@0" Pin0InfoVect0LinkObjId="SW-192167_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192165_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-269 1499,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d2a5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1499,-172 1499,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="29049@1" ObjectIDZND0="29047@0" Pin0InfoVect0LinkObjId="SW-192165_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192168_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1499,-172 1499,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d2e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-534 1344,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="29051@1" ObjectIDZND0="29040@1" Pin0InfoVect0LinkObjId="SW-192055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d004f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-534 1344,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d372e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-701 406,-689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25506@x" ObjectIDND1="25504@x" ObjectIDND2="g_4d37a00@0" ObjectIDZND0="g_4d36a10@0" Pin0InfoVect0LinkObjId="g_4d36a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145217_0" Pin1InfoVect1LinkObjId="SW-145215_0" Pin1InfoVect2LinkObjId="g_4d37a00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="406,-701 406,-689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d37540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-657 406,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_4d36a10@1" ObjectIDZND0="g_4d34fc0@0" Pin0InfoVect0LinkObjId="g_4d34fc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d36a10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="406,-657 406,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_4d377a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="406,-701 332,-701 332,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25506@x" ObjectIDND1="25504@x" ObjectIDND2="g_4d36a10@0" ObjectIDZND0="g_4d37a00@0" Pin0InfoVect0LinkObjId="g_4d37a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-145217_0" Pin1InfoVect1LinkObjId="SW-145215_0" Pin1InfoVect2LinkObjId="g_4d36a10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="406,-701 332,-701 332,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3acc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-207 315,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_4d3a110@0" ObjectIDZND0="g_4d38880@0" Pin0InfoVect0LinkObjId="g_4d38880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d3a110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-207 315,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3af20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-273 267,-273 267,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25509@x" ObjectIDND1="g_4d3a110@0" ObjectIDZND0="g_4d3b180@0" Pin0InfoVect0LinkObjId="g_4d3b180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145245_0" Pin1InfoVect1LinkObjId="g_4d3a110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-273 267,-273 267,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-292 315,-273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25509@0" ObjectIDZND0="g_4d3a110@0" ObjectIDZND1="g_4d3b180@0" Pin0InfoVect0LinkObjId="g_4d3a110_0" Pin0InfoVect1LinkObjId="g_4d3b180_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-145245_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="315,-292 315,-273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="315,-273 315,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25509@x" ObjectIDND1="g_4d3b180@0" ObjectIDZND0="g_4d3a110@1" Pin0InfoVect0LinkObjId="g_4d3a110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-145245_0" Pin1InfoVect1LinkObjId="g_4d3b180_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="315,-273 315,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3f040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-203 1811,-164 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_4d3e490@0" ObjectIDZND0="g_4d3cc00@0" Pin0InfoVect0LinkObjId="g_4d3cc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_4d3e490_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-203 1811,-164 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d3f2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1811,-269 1763,-269 1763,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="29036@x" ObjectIDND1="g_4d3e490@0" ObjectIDZND0="g_4d3f500@0" Pin0InfoVect0LinkObjId="g_4d3f500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-192043_0" Pin1InfoVect1LinkObjId="g_4d3e490_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1811,-269 1763,-269 1763,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d40230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-269 1810,-248 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" ObjectIDND0="g_4d3f500@0" ObjectIDND1="29036@x" ObjectIDND2="g_4d3e490@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_4d3f500_0" Pin1InfoVect1LinkObjId="SW-192043_0" Pin1InfoVect2LinkObjId="g_4d3e490_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-269 1810,-248 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d40f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-291 1810,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="29036@0" ObjectIDZND0="g_4d3f500@0" ObjectIDZND1="g_4d3e490@0" Pin0InfoVect0LinkObjId="g_4d3f500_0" Pin0InfoVect1LinkObjId="g_4d3e490_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-192043_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-291 1810,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_4d411a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-269 1810,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_4d3f500@0" ObjectIDND1="29036@x" ObjectIDZND0="g_4d3e490@1" Pin0InfoVect0LinkObjId="g_4d3e490_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_4d3f500_0" Pin1InfoVect1LinkObjId="SW-192043_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-269 1810,-251 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-145077" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 125.500000 -945.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25485" ObjectName="DYN-YA_QC"/>
     <cge:Meas_Ref ObjectId="145077"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ceff10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 834.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf0c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 849.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf1310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 353.000000 806.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf1590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 820.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf1c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 362.000000 -19.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf2ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -34.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf37a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 376.000000 -49.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d2d380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 574.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d2df70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 589.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d32f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 -20.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d33160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1272.000000 -35.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d333a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1292.000000 -50.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d336d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 989.000000 211.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d33930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 978.000000 196.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d33b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.000000 181.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d42eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 393.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d43230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 425.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d43470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 408.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d436b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 439.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d438f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 392.000000 454.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d43c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1574.000000 391.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d43e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 423.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d440d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1582.000000 406.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d44310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 437.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d44550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 452.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d45680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -20.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d45b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -35.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d45dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 588.000000 -50.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d461f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 766.000000 -18.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d464b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d466f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -48.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d46b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1426.000000 -18.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d46dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1415.000000 -33.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d47010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1440.000000 -48.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d4f4f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1216.000000 676.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d4fb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1205.000000 661.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d4fd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1230.000000 646.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d501a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1218.000000 504.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d50460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1207.000000 489.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d506a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1232.000000 474.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d50ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 670.000000 669.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d50d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 659.000000 654.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d50fc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 684.000000 639.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d513e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 665.000000 496.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d516a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 654.000000 481.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d518e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 679.000000 466.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_QC.YA_QC_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35981"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -589.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -589.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25524" ObjectName="TF-YA_QC.YA_QC_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_QC.YA_QC_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35985"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -169.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1626.000000 -169.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25525" ObjectName="TF-YA_QC.YA_QC_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_QC.YA_QC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41431"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.885076 -0.000000 0.000000 -1.000000 1310.650000 -529.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.885076 -0.000000 0.000000 -1.000000 1310.650000 -529.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="29051" ObjectName="TF-YA_QC.YA_QC_2T"/>
    <cge:TPSR_Ref TObjectID="29051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_QC.YA_QC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35977"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -520.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 756.000000 -520.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25523" ObjectName="TF-YA_QC.YA_QC_1T"/>
    <cge:TPSR_Ref TObjectID="25523"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_5099560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">前场变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_509a670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1015.000000 -1048.000000) translate(0,12)">35kV前场线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_509bb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1801.000000 -787.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a4ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 280.000000 -391.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -604.000000) translate(0,12)">S9-11/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -604.000000) translate(0,27)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -604.000000) translate(0,42)">2500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -604.000000) translate(0,57)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50a5ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 606.000000 -604.000000) translate(0,72)">Ud=6.42%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50a9940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_50abb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50ba070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -787.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_50ba6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -573.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_50c0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -684.000000) translate(0,10)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_50c0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -684.000000) translate(0,22)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_50c0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -684.000000) translate(0,34)">S9-50/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_50c0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -684.000000) translate(0,46)">35±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="12" graphid="g_50c0010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1661.000000 -684.000000) translate(0,58)">Y,yn0Ud=6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cbf380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 819.357143 -13.400000) translate(0,12)">适中线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cc90c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.214286 -13.400000) translate(0,12)">庄科线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cd30e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.528571 -13.400000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cdcfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 624.842857 -12.400000) translate(0,12)">小河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce43e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 258.000000 -85.600000) translate(0,12)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -139.000000) translate(0,12)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -139.000000) translate(0,27)">S11-50/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -139.000000) translate(0,42)">10±2×2.5%/0.4kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce4c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 -139.000000) translate(0,57)">D.yn11 Uk=4%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ce9830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1624.000000 -158.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cec9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 322.000000 -317.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cecfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 848.000000 -321.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ced210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -263.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ced450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 848.000000 -159.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ced690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -262.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ced8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 653.000000 -160.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cedb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 653.000000 -320.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cedd50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.000000 -324.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cee2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 463.000000 -263.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cee560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -159.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cee7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 460.000000 -321.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cee9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -418.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ceec20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 789.000000 -484.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4ceee60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 805.000000 -694.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cef0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 788.000000 -731.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cef2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -648.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cef520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 -686.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cef760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 414.000000 -743.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cef9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -882.000000) translate(0,12)">32117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cefbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -848.000000) translate(0,12)">3211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cf5e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1143.000000 -828.000000) translate(0,12)">32110</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cff590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -604.000000) translate(0,12)">SZ11-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cff590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -604.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cff590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -604.000000) translate(0,42)">10000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cff590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -604.000000) translate(0,57)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4cff590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1171.000000 -604.000000) translate(0,72)">Ud=7.3%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d03320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -426.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d03950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -492.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d03b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1368.000000 -702.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d03dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1351.000000 -739.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d04010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -656.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d05cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 -392.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d0f820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -238.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d0fe50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1107.000000 -238.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d12cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1764.000000 -101.600000) translate(0,12)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d13330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1817.000000 -316.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d1a280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1308.685714 -13.400000) translate(0,12)">楚姚高速Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d1f330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1369.000000 -263.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d1f960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1367.000000 -321.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d1fba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1367.000000 -159.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d259f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1446.685714 -13.400000) translate(0,12)">楚姚高速Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d29b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1508.000000 -263.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d2a130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -321.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d2a370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -159.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_4d2efb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -148.000000 -689.000000) translate(0,24)">公共间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4d30160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 182.000000 -1022.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4d31370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 182.000000 -1059.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d320b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -238.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d41400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1779.000000 -81.600000) translate(0,12)">JSZW-10WF</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d41400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1779.000000 -81.600000) translate(0,27)">10/√3/0.1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_4d448d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 107.000000 -1040.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_4d47250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -199.000000 -42.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4d485c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -48.000000 -49.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4d485c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -48.000000 -49.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4d499c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -113.000000) translate(0,17)">4663</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_4d499c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -113.000000) translate(0,38)">5841440</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d4bb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -580.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4d4c570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1377.000000 -589.000000) translate(0,12)">2号主变</text>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_4d34fc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 389.000000 -614.000000)" xlink:href="#voltageTransformer:shape122"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d38880">
    <use class="BV-10KV" transform="matrix(0.731707 -0.000000 0.000000 -0.702381 286.000000 -112.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_4d3cc00">
    <use class="BV-10KV" transform="matrix(0.731707 -0.000000 0.000000 -0.702381 1782.000000 -108.000000)" xlink:href="#voltageTransformer:shape73"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25496" cx="1052" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25496" cx="1344" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29057" cx="1343" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25496" cx="781" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="780" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="928" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29057" cx="1175" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="315" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29057" cx="1810" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="453" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="646" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25497" cx="841" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="29057" cx="1657" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g areaN="0" bayName="YA_QC_461" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_QC"/>
</svg>