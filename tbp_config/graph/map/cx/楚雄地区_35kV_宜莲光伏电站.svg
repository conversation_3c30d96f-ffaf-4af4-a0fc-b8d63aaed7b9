<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-307" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-330 -957 2474 1295">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="19" y2="19"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape99">
    <polyline DF8003:Layer="PUBLIC" points="25,29 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="8" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="41" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="41" y1="46" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="46" y2="46"/>
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.5"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="7" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="24" y1="19" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="15" y1="24" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="24" y1="17" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="10" y1="17" y2="24"/>
    <circle cx="17" cy="17" fillStyle="0" r="16" stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="17" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="switch2:shape40_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="19" y1="37" y2="37"/>
    <circle cx="32" cy="20" fillStyle="0" r="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="44" x2="20" y1="9" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="13" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="22" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="19" y2="19"/>
   </symbol>
   <symbol id="switch2:shape40-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="14" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="21" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="23" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="10" x2="18" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="45" x2="18" y1="36" y2="36"/>
    <ellipse cx="32" cy="19" fillStyle="0" rx="4" ry="4.5" stroke-width="0.680952"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="32" x2="32" y1="37" y2="4"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape35_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="51" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 10,26 22,26 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="56" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="51"/>
   </symbol>
   <symbol id="transformer2:shape35_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="81" y2="76"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1305" width="2484" x="-335" y="-962"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="562" x2="698" y1="336" y2="336"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-271687">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 238.000000 -192.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43736" ObjectName="SW-CX_YLGF.CX_YLGF_361BK"/>
     <cge:Meas_Ref ObjectId="271687"/>
    <cge:TPSR_Ref TObjectID="43736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271692">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -196.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43739" ObjectName="SW-CX_YLGF.CX_YLGF_362BK"/>
     <cge:Meas_Ref ObjectId="271692"/>
    <cge:TPSR_Ref TObjectID="43739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271704">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 683.000000 -195.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43747" ObjectName="SW-CX_YLGF.CX_YLGF_364BK"/>
     <cge:Meas_Ref ObjectId="271704"/>
    <cge:TPSR_Ref TObjectID="43747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271714">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -195.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43753" ObjectName="SW-CX_YLGF.CX_YLGF_366BK"/>
     <cge:Meas_Ref ObjectId="271714"/>
    <cge:TPSR_Ref TObjectID="43753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271698">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 445.000000 -427.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43743" ObjectName="SW-CX_YLGF.CX_YLGF_363BK"/>
     <cge:Meas_Ref ObjectId="271698"/>
    <cge:TPSR_Ref TObjectID="43743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271709">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 808.000000 -455.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43750" ObjectName="SW-CX_YLGF.CX_YLGF_365BK"/>
     <cge:Meas_Ref ObjectId="271709"/>
    <cge:TPSR_Ref TObjectID="43750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271726">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -196.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43761" ObjectName="SW-CX_YLGF.CX_YLGF_371BK"/>
     <cge:Meas_Ref ObjectId="271726"/>
    <cge:TPSR_Ref TObjectID="43761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271731">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -201.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43764" ObjectName="SW-CX_YLGF.CX_YLGF_372BK"/>
     <cge:Meas_Ref ObjectId="271731"/>
    <cge:TPSR_Ref TObjectID="43764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271743">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1766.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43772" ObjectName="SW-CX_YLGF.CX_YLGF_374BK"/>
     <cge:Meas_Ref ObjectId="271743"/>
    <cge:TPSR_Ref TObjectID="43772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271753">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1944.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43778" ObjectName="SW-CX_YLGF.CX_YLGF_376BK"/>
     <cge:Meas_Ref ObjectId="271753"/>
    <cge:TPSR_Ref TObjectID="43778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271737">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1528.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43768" ObjectName="SW-CX_YLGF.CX_YLGF_373BK"/>
     <cge:Meas_Ref ObjectId="271737"/>
    <cge:TPSR_Ref TObjectID="43768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271748">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1891.000000 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43775" ObjectName="SW-CX_YLGF.CX_YLGF_375BK"/>
     <cge:Meas_Ref ObjectId="271748"/>
    <cge:TPSR_Ref TObjectID="43775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271719">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 -450.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43756" ObjectName="SW-CX_YLGF.CX_YLGF_312BK"/>
     <cge:Meas_Ref ObjectId="271719"/>
    <cge:TPSR_Ref TObjectID="43756"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_15ebc20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.000000 -704.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1372170">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1705.000000 -708.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YLGF.CX_YLGF_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,-330 1037,-330 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43788" ObjectName="BS-CX_YLGF.CX_YLGF_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   <polyline fill="none" opacity="0" points="187,-330 1037,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YLGF.CX_YLGF_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1270,-334 2120,-334 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43786" ObjectName="BS-CX_YLGF.CX_YLGF_3IIM"/>
    <cge:TPSR_Ref TObjectID="43786"/></metadata>
   <polyline fill="none" opacity="0" points="1270,-334 2120,-334 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 225.000000 -159.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 392.000000 -163.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 669.000000 -162.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 847.000000 -162.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 669.000000 -381.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 681.000000 -583.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 855.000000 -571.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 1307.000000 -163.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 1475.000000 -167.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 1752.000000 -166.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(-0.548387 -0.000000 -0.000000 0.625000 1930.000000 -166.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 1752.000000 -385.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 1764.000000 -587.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(0.548387 0.000000 0.000000 -0.625000 1938.000000 -575.000000)" xlink:href="#capacitor:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1521.000000 -720.000000)" xlink:href="#transformer2:shape35_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1521.000000 -720.000000)" xlink:href="#transformer2:shape35_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1d70120">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -109.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c5fa0">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 226.000000 -136.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20551b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 -146.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1502350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -79.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b3c8e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 -113.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b54690">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 394.000000 -140.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1b39eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -150.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d1390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 410.000000 -45.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_128eba0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1af07f0">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 671.000000 -139.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12981f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 758.000000 -149.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16492c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -82.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d97010">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 906.000000 -112.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12945b0">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 849.000000 -139.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16541e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 936.000000 -149.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1613840">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 -82.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_136f550">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 449.000000 -484.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1673300">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 471.000000 -621.000000)" xlink:href="#lightningRod:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1662730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 388.000000 -704.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1625df0">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 669.000000 -405.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19f30b0">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 681.000000 -607.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1665560">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -610.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16660c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 583.000000 -587.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1295b00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -572.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12968b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 812.000000 -572.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1367e30">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 855.000000 -595.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1378570">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1366.000000 -113.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ce57f0">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 1309.000000 -140.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1293330">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -150.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1500380">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 -83.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f8db0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1534.000000 -117.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cdfa90">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 1477.000000 -144.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e0a40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1564.000000 -154.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129c2f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1493.000000 -49.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d22f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1811.000000 -116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d3850">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 1754.000000 -143.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ff0e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1841.000000 -153.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d15730">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 -86.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f8ae0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1989.000000 -116.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13fa0e0">
    <use class="BV-0KV" transform="matrix(-0.326531 -0.000000 -0.000000 0.310345 1932.000000 -143.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140cd00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2019.000000 -153.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_132ab80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -86.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16568d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1532.000000 -488.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1600830">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1471.000000 -708.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ae3e0">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 1752.000000 -409.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13dcb10">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 1764.000000 -611.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13dda60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 -614.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13de5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1666.000000 -591.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1670390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1853.000000 -576.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1670fd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1895.000000 -576.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19dc690">
    <use class="BV-0KV" transform="matrix(0.326531 0.000000 0.000000 -0.310345 1938.000000 -599.000000)" xlink:href="#lightningRod:shape99"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -239.000000 -881.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -206.461538 -748.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -206.461538 -707.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271850" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -474.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271850" ObjectName="CX_YLGF:CX_YLGF_3IIM_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271851" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -458.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271851" ObjectName="CX_YLGF:CX_YLGF_3IIM_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271852" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -442.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271852" ObjectName="CX_YLGF:CX_YLGF_3IIM_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271856" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -427.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271856" ObjectName="CX_YLGF:CX_YLGF_3IIM_U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271853" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -413.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271853" ObjectName="CX_YLGF:CX_YLGF_3IIM_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-271857" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2093.000000 -398.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271857" ObjectName="CX_YLGF:CX_YLGF_3IIM_F"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="247" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="415" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="454" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="641" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="692" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="817" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="870" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1330" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1498" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1537" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1724" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1775" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1900" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1953" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43788" cx="988" cy="-330" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="43786" cx="1301" cy="-334" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-271670" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -28.000000 -836.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43720" ObjectName="DYN-CX_YLGF"/>
     <cge:Meas_Ref ObjectId="271670"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2054c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：6718669</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_12ce5a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -788.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1aedb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -200.000000 -929.500000) translate(0,16)">宜莲光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1b02430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 193.000000 18.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16025f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 383.000000 -830.000000) translate(0,15)">1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1602f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 592.000000 -830.000000) translate(0,17)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1602f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 592.000000 -830.000000) translate(0,38)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_165c9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -806.000000) translate(0,17)">1号静态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a1190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 777.000000 -831.000000) translate(0,15)">35MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a15e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 669.000000 24.000000) translate(0,15)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 828.000000 26.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12a20e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 50.000000) translate(0,15)">（1、2号光伏方阵）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_163f700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 348.000000 53.000000) translate(0,15)">（至110kV莲池变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16403a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 49.000000) translate(0,15)">（72、73、74、75、</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_16403a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 49.000000) translate(0,33)">76、77、81号光伏方阵）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1379020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1276.000000 14.000000) translate(0,15)">3号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19dd5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1462.000000 -834.000000) translate(0,15)">2号接地变兼站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19dde80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1675.000000 -834.000000) translate(0,17)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19dde80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1675.000000 -834.000000) translate(0,38)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_19de400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -843.000000) translate(0,17)">2号静态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19de650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1867.000000 -813.000000) translate(0,15)">35MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_19de880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1752.000000 20.000000) translate(0,15)">备用二</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14310b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1911.000000 22.000000) translate(0,15)">4号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14314b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1176.000000 44.000000) translate(0,15)">（42 、43、44、45号光伏方阵）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14318f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1431.000000 49.000000) translate(0,15)">（至110kV莲池变）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1431b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1857.000000 49.000000) translate(0,15)">（87、89、90、92、93号光伏方阵）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" font-family="SimSun" font-size="15" graphid="g_1431d40" transform="matrix(1.000000 0.000000 -0.000000 1.000000 1885.500000 -741.500000) translate(0,12)">SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1493540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 471.000000 -456.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1493a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -392.000000) translate(0,12)">3631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1493c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 402.000000 -682.000000) translate(0,12)">3630</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1493eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 664.000000 -481.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1416870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 834.000000 -478.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1416ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 842.000000 -400.000000) translate(0,12)">3651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1416cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 -448.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1416f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1128.000000 -495.000000) translate(0,12)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1417540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 930.000000 -384.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14179a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1327.000000 -400.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1417be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -350.000000) translate(0,12)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1418040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2007.000000 -363.000000) translate(0,12)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14185a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -393.000000) translate(0,12)">3732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1418820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1552.000000 -459.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1418a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1485.000000 -685.000000) translate(0,12)">3730</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1418ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1737.000000 -487.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(60,120,255)" font-family="SimSun" font-size="15" graphid="g_145e250" transform="matrix(1.000000 0.000000 -0.000000 1.000000 802.500000 -748.500000) translate(0,12)">SVG</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144ac40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1921.000000 -407.000000) translate(0,12)">3752</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144b130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1909.000000 -488.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144b370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 263.000000 -220.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_144b5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 267.000000 -276.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1428140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 442.000000 -286.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14285f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.000000 -225.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1428830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 421.000000 -162.000000) translate(0,12)">3623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142a960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -224.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 707.000000 -282.000000) translate(0,12)">3641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c958f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 899.000000 -281.000000) translate(0,12)">3661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c95da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 -224.000000) translate(0,12)">366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d74ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1339.000000 -225.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d74f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -289.000000) translate(0,12)">3712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d751d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 -286.000000) translate(0,12)">3722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d75410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1521.000000 -232.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1405e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -167.000000) translate(0,12)">3723</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1408430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1799.000000 -288.000000) translate(0,12)">3742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14088e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1784.000000 -228.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d8df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1973.000000 -293.000000) translate(0,12)">3762</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d9260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -228.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ccc000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 21.000000) translate(0,15)">宜莲Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ccc660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1471.000000 17.000000) translate(0,15)">宜莲Ⅱ回线</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(60,120,255)" stroke-width="1" width="35" x="1883" y="-744"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="19" stroke="rgb(60,120,255)" stroke-width="1" width="35" x="800" y="-751"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_YLGF.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 242.000000 -20.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44660" ObjectName="SM-CX_YLGF.P1"/>
    <cge:TPSR_Ref TObjectID="44660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 687.000000 -23.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YLGF.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.000000 -23.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44661" ObjectName="SM-CX_YLGF.P2"/>
    <cge:TPSR_Ref TObjectID="44661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YLGF.P3">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1325.000000 -24.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44662" ObjectName="SM-CX_YLGF.P3"/>
    <cge:TPSR_Ref TObjectID="44662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1770.000000 -27.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_YLGF.P4">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1948.000000 -27.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44663" ObjectName="SM-CX_YLGF.P4"/>
    <cge:TPSR_Ref TObjectID="44663"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 390.000000 -760.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1473.000000 -764.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_1b1ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="217,-105 217,-119 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_19f16c0@0" ObjectIDZND0="g_12c5fa0@0" Pin0InfoVect0LinkObjId="g_12c5fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f16c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="217,-105 217,-119 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b1dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="217,-147 217,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_12c5fa0@1" Pin0InfoVect0LinkObjId="g_12c5fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="217,-147 217,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b4c1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="247,-84 247,-41 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1502350@0" ObjectIDZND0="44660@0" Pin0InfoVect0LinkObjId="SM-CX_YLGF.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1502350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="247,-84 247,-41 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b3c660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="247,-256 247,-227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43737@0" ObjectIDZND0="43736@1" Pin0InfoVect0LinkObjId="SW-271687_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271688_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="247,-256 247,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15fa580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="247,-288 247,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43737@1" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_1b099f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271688_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="247,-288 247,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1604770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-109 385,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_130a2a0@0" ObjectIDZND0="g_1b54690@0" Pin0InfoVect0LinkObjId="g_1b54690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_130a2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-109 385,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1b39c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="385,-151 385,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1b54690@1" Pin0InfoVect0LinkObjId="g_1b54690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="385,-151 385,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b09790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-260 415,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43740@0" ObjectIDZND0="43739@1" Pin0InfoVect0LinkObjId="SW-271692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271693_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-260 415,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1b099f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-292 415,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43740@1" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-292 415,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_128e6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-138 415,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43741@0" ObjectIDZND0="g_15d1390@1" Pin0InfoVect0LinkObjId="g_15d1390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271694_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-138 415,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_128e940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-50 415,-20 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_15d1390@0" ObjectIDZND0="43826@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15d1390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-50 415,-20 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1297d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-108 662,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1646a30@0" ObjectIDZND0="g_1af07f0@0" Pin0InfoVect0LinkObjId="g_1af07f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1646a30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-108 662,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1297f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="662,-150 662,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1af07f0@1" Pin0InfoVect0LinkObjId="g_1af07f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="662,-150 662,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ded00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-87 692,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_16492c0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16492c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-87 692,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d96db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-259 692,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43748@0" ObjectIDZND0="43747@1" Pin0InfoVect0LinkObjId="SW-271704_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271705_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-259 692,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164a600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-108 840,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1649b90@0" ObjectIDZND0="g_12945b0@0" Pin0InfoVect0LinkObjId="g_12945b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1649b90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-108 840,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_164a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-150 840,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_12945b0@1" Pin0InfoVect0LinkObjId="g_12945b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-150 840,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-259 870,-230 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43754@0" ObjectIDZND0="43753@1" Pin0InfoVect0LinkObjId="SW-271714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271715_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-259 870,-230 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a33570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-369 454,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43744@0" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271699_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-369 454,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_136f2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-435 454,-401 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43743@0" ObjectIDZND0="43744@1" Pin0InfoVect0LinkObjId="SW-271699_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-435 454,-401 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1672e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-622 454,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_136f550@1" Pin0InfoVect0LinkObjId="g_136f550_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-622 454,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16730a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="454,-489 454,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_136f550@0" ObjectIDZND0="43743@1" Pin0InfoVect0LinkObjId="SW-271698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_136f550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="454,-489 454,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12d07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-765 395,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1662730@0" Pin0InfoVect0LinkObjId="g_1662730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,-765 395,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12d0a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-709 395,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1662730@1" ObjectIDZND0="43746@1" Pin0InfoVect0LinkObjId="SW-271701_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1662730_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,-709 395,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12d0c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="395,-656 395,-638 438,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43746@0" ObjectIDZND0="g_1673300@0" Pin0InfoVect0LinkObjId="g_1673300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271701_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="395,-656 395,-638 438,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_12d0ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="438,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="438,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16252c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="676,-435 676,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_12d1120@0" ObjectIDZND0="g_1625df0@0" Pin0InfoVect0LinkObjId="g_1625df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d1120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="676,-435 676,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1625520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-393 677,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1625df0@1" Pin0InfoVect0LinkObjId="g_1625df0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="677,-393 677,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16055b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="677,-383 677,-360 641,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="43788@0" ObjectIDZND1="43781@x" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="SW-271758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="677,-383 677,-360 641,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-360 641,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="43781@x" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-360 641,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_134ecd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-457 641,-360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="43781@0" ObjectIDZND0="0@x" ObjectIDZND1="43788@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="g_15fa580_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="641,-457 641,-360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19f2300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="688,-637 688,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_134ef30@0" ObjectIDZND0="g_19f30b0@0" Pin0InfoVect0LinkObjId="g_19f30b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_134ef30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="688,-637 688,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19f2560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-595 689,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19f30b0@1" Pin0InfoVect0LinkObjId="g_19f30b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="689,-595 689,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1665e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-709 641,-659 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_15ebc20@0" ObjectIDZND0="g_1665560@1" Pin0InfoVect0LinkObjId="g_1665560_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-709 641,-659 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1299140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-291 692,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43748@1" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271705_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-291 692,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_136bbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-382 817,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43751@0" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-382 817,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12958a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-463 817,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43750@0" ObjectIDZND0="43751@1" Pin0InfoVect0LinkObjId="SW-271710_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271709_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-463 817,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15ca150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-291 870,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43754@1" ObjectIDZND0="43788@0" Pin0InfoVect0LinkObjId="g_15fa580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271715_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-291 870,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15cb410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-625 863,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_15ca980@0" ObjectIDZND0="g_1367e30@0" Pin0InfoVect0LinkObjId="g_1367e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ca980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-625 863,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13672e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-583 863,-596 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1367e30@1" Pin0InfoVect0LinkObjId="g_1367e30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="863,-583 863,-596 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_165a730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1300,-109 1300,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1659ce0@0" ObjectIDZND0="g_1ce57f0@0" Pin0InfoVect0LinkObjId="g_1ce57f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1659ce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1300,-109 1300,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_165a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1300,-151 1300,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1ce57f0@1" Pin0InfoVect0LinkObjId="g_1ce57f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1300,-151 1300,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1500da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-88 1330,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1500380@0" ObjectIDZND0="44662@0" Pin0InfoVect0LinkObjId="SM-CX_YLGF.P3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1500380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-88 1330,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15f88f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-260 1330,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43762@0" ObjectIDZND0="43761@1" Pin0InfoVect0LinkObjId="SW-271726_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271727_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-260 1330,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15f8b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-292 1330,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43762@1" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_129c090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271727_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-292 1330,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce12b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-113 1468,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1ce0820@0" ObjectIDZND0="g_1cdfa90@0" Pin0InfoVect0LinkObjId="g_1cdfa90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ce0820_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-113 1468,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ce1510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1468,-155 1468,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1cdfa90@1" Pin0InfoVect0LinkObjId="g_1cdfa90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1468,-155 1468,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_129be30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-264 1498,-235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43765@0" ObjectIDZND0="43764@1" Pin0InfoVect0LinkObjId="SW-271731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-264 1498,-235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_129c090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-296 1498,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43765@1" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-296 1498,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12d2090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-142 1498,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43766@0" ObjectIDZND0="g_129c2f0@1" Pin0InfoVect0LinkObjId="g_129c2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-142 1498,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15fec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-112 1745,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_15fe190@0" ObjectIDZND0="g_12d3850@0" Pin0InfoVect0LinkObjId="g_12d3850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fe190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-112 1745,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_15fee80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1745,-154 1745,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_12d3850@1" Pin0InfoVect0LinkObjId="g_12d3850_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1745,-154 1745,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14151d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-263 1775,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43773@0" ObjectIDZND0="43772@1" Pin0InfoVect0LinkObjId="SW-271743_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-263 1775,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_140c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1923,-112 1923,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_140bdb0@0" ObjectIDZND0="g_13fa0e0@0" Pin0InfoVect0LinkObjId="g_13fa0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_140bdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1923,-112 1923,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_140caa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1923,-154 1923,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_13fa0e0@1" Pin0InfoVect0LinkObjId="g_13fa0e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1923,-154 1923,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16173f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-263 1953,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43779@0" ObjectIDZND0="43778@1" Pin0InfoVect0LinkObjId="SW-271753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-263 1953,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15c19a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-373 1537,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43769@0" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-373 1537,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1656670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-439 1537,-405 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43768@0" ObjectIDZND0="43769@1" Pin0InfoVect0LinkObjId="SW-271738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271737_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-439 1537,-405 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16572f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-626 1537,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_16568d0@1" Pin0InfoVect0LinkObjId="g_16568d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-626 1537,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16293a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,-493 1537,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_16568d0@0" ObjectIDZND0="43768@1" Pin0InfoVect0LinkObjId="SW-271737_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16568d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1537,-493 1537,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1602030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-769 1478,-747 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1600830@0" Pin0InfoVect0LinkObjId="g_1600830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-769 1478,-747 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1602290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-713 1478,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1600830@1" ObjectIDZND0="43770@1" Pin0InfoVect0LinkObjId="SW-271739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1600830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-713 1478,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ac6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1478,-660 1478,-642 1521,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="43770@0" ObjectIDZND0="43770@x" Pin0InfoVect0LinkObjId="SW-271739_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1478,-660 1478,-642 1521,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ac940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1521,-640 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="43770@x" ObjectIDZND0="43770@x" Pin0InfoVect0LinkObjId="SW-271739_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1521,-640 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ad630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-439 1760,-425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_14acba0@0" ObjectIDZND0="g_14ae3e0@0" Pin0InfoVect0LinkObjId="g_14ae3e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14acba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-439 1760,-425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14ad890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-397 1760,-410 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_14ae3e0@1" Pin0InfoVect0LinkObjId="g_14ae3e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-397 1760,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ba3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,-387 1760,-364 1724,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="43786@0" ObjectIDZND1="43783@x" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="SW-271760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,-387 1760,-364 1724,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ba620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-364 1724,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="0@x" ObjectIDND1="43783@x" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-364 1724,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13bcec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-461 1724,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="busSection" ObjectIDND0="43783@0" ObjectIDZND0="0@x" ObjectIDZND1="43786@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="g_15f8b50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-461 1724,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13bdbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1772,-641 1772,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_13bd120@0" ObjectIDZND0="g_13dcb10@0" Pin0InfoVect0LinkObjId="g_13dcb10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13bd120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1772,-641 1772,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_13bde10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1772,-599 1772,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_13dcb10@1" Pin0InfoVect0LinkObjId="g_13dcb10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1772,-599 1772,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13de360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-713 1724,-663 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_1372170@0" ObjectIDZND0="g_13dda60@1" Pin0InfoVect0LinkObjId="g_13dda60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1372170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-713 1724,-663 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1de7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-295 1775,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43773@1" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271744_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-295 1775,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ce2010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-386 1900,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43776@0" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-386 1900,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1670130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-467 1900,-418 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43775@0" ObjectIDZND0="43776@1" Pin0InfoVect0LinkObjId="SW-271749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271748_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-467 1900,-418 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16719f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-295 1953,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43779@1" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271754_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-295 1953,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_16726e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1945,-629 1945,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1671c50@0" ObjectIDZND0="g_19dc690@0" Pin0InfoVect0LinkObjId="g_19dc690_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1671c50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1945,-629 1945,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1672940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1946,-587 1946,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_19dc690@1" Pin0InfoVect0LinkObjId="g_19dc690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1946,-587 1946,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1432130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-727 1900,-634 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1670fd0@1" Pin0InfoVect0LinkObjId="g_1670fd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-727 1900,-634 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1492bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-330 988,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="43788@0" ObjectIDZND0="43757@0" Pin0InfoVect0LinkObjId="SW-271720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15fa580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-330 988,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1492e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-410 988,-460 1128,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43757@1" ObjectIDZND0="43756@1" Pin0InfoVect0LinkObjId="SW-271719_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-410 988,-460 1128,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1493080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1155,-460 1301,-460 1301,-415 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43756@0" ObjectIDZND0="43759@1" Pin0InfoVect0LinkObjId="SW-271722_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271719_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1155,-460 1301,-460 1301,-415 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14932e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1301,-383 1301,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43759@0" ObjectIDZND0="43786@0" Pin0InfoVect0LinkObjId="g_15f8b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1301,-383 1301,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-619 1724,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_13dda60@0" ObjectIDZND0="43783@x" ObjectIDZND1="g_13dda60@0" ObjectIDZND2="43783@x" Pin0InfoVect0LinkObjId="SW-271760_0" Pin0InfoVect1LinkObjId="g_13dda60_0" Pin0InfoVect2LinkObjId="SW-271760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13dda60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-619 1724,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145b390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-573 1724,-493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_13dda60@0" ObjectIDND1="g_13dda60@0" ObjectIDND2="43783@x" ObjectIDZND0="43783@1" Pin0InfoVect0LinkObjId="SW-271760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13dda60_0" Pin1InfoVect1LinkObjId="g_13dda60_0" Pin1InfoVect2LinkObjId="SW-271760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-573 1724,-493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145b580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1673,-595 1673,-573 1724,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13de5c0@0" ObjectIDZND0="g_13dda60@0" ObjectIDZND1="43783@x" ObjectIDZND2="g_13dda60@0" Pin0InfoVect0LinkObjId="g_13dda60_0" Pin0InfoVect1LinkObjId="SW-271760_0" Pin0InfoVect2LinkObjId="g_13dda60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13de5c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1673,-595 1673,-573 1724,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1724,-573 1724,-572 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_13dda60@0" ObjectIDND1="43783@x" ObjectIDND2="g_13de5c0@0" ObjectIDZND0="g_13dda60@0" ObjectIDZND1="43783@x" ObjectIDZND2="g_13de5c0@0" Pin0InfoVect0LinkObjId="g_13dda60_0" Pin0InfoVect1LinkObjId="SW-271760_0" Pin0InfoVect2LinkObjId="g_13de5c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13dda60_0" Pin1InfoVect1LinkObjId="SW-271760_0" Pin1InfoVect2LinkObjId="g_13de5c0_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1724,-573 1724,-572 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145b9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1772,-589 1772,-573 1724,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_13dda60@0" ObjectIDZND1="43783@x" ObjectIDZND2="g_13dda60@0" Pin0InfoVect0LinkObjId="g_13dda60_0" Pin0InfoVect1LinkObjId="SW-271760_0" Pin0InfoVect2LinkObjId="g_13dda60_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1772,-589 1772,-573 1724,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145bc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-591 590,-574 641,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_16660c0@0" ObjectIDZND0="g_1665560@0" ObjectIDZND1="43781@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1665560_0" Pin0InfoVect1LinkObjId="SW-271758_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16660c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="590,-591 590,-574 641,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145c960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-615 641,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_1665560@0" ObjectIDZND0="g_16660c0@0" ObjectIDZND1="43781@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_16660c0_0" Pin0InfoVect1LinkObjId="SW-271758_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1665560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="641,-615 641,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="641,-574 641,-489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="g_16660c0@0" ObjectIDND1="g_1665560@0" ObjectIDND2="0@x" ObjectIDZND0="43781@1" Pin0InfoVect0LinkObjId="SW-271758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16660c0_0" Pin1InfoVect1LinkObjId="g_1665560_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="641,-574 641,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145ce20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="689,-585 689,-574 641,-574 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_16660c0@0" ObjectIDZND1="g_1665560@0" ObjectIDZND2="43781@x" Pin0InfoVect0LinkObjId="g_16660c0_0" Pin0InfoVect1LinkObjId="g_1665560_0" Pin0InfoVect2LinkObjId="SW-271758_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="689,-585 689,-574 641,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="777,-576 777,-555 817,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="capacitor" ObjectIDND0="g_1295b00@0" ObjectIDZND0="g_12968b0@0" ObjectIDZND1="43750@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_12968b0_0" Pin0InfoVect1LinkObjId="SW-271709_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1295b00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="777,-576 777,-555 817,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145dd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-577 817,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="capacitor" ObjectIDND0="g_12968b0@0" ObjectIDZND0="g_1295b00@0" ObjectIDZND1="43750@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1295b00_0" Pin0InfoVect1LinkObjId="SW-271709_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12968b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="817,-577 817,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_145dff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-555 817,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="breaker" ObjectIDND0="g_1295b00@0" ObjectIDND1="g_12968b0@0" ObjectIDND2="0@x" ObjectIDZND0="43750@1" Pin0InfoVect0LinkObjId="SW-271709_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1295b00_0" Pin1InfoVect1LinkObjId="g_12968b0_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-555 817,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1449030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-555 862,-555 862,-573 863,-573 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="capacitor" ObjectIDND0="g_1295b00@0" ObjectIDND1="g_12968b0@0" ObjectIDND2="43750@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_15ebc20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1295b00_0" Pin1InfoVect1LinkObjId="g_12968b0_0" Pin1InfoVect2LinkObjId="SW-271709_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-555 862,-555 862,-573 863,-573 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1449220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="817,-734 817,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_12968b0@1" Pin0InfoVect0LinkObjId="g_12968b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="817,-734 817,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144a060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-581 1900,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_1670fd0@0" ObjectIDZND0="43775@x" ObjectIDZND1="g_1670fd0@0" ObjectIDZND2="43775@x" Pin0InfoVect0LinkObjId="SW-271748_0" Pin0InfoVect1LinkObjId="g_1670fd0_0" Pin0InfoVect2LinkObjId="SW-271748_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1670fd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-581 1900,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144a2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1900,-553 1900,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="breaker" ObjectIDND0="g_1670fd0@0" ObjectIDND1="g_1670fd0@0" ObjectIDND2="43775@x" ObjectIDZND0="43775@1" Pin0InfoVect0LinkObjId="SW-271748_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1670fd0_0" Pin1InfoVect1LinkObjId="g_1670fd0_0" Pin1InfoVect2LinkObjId="SW-271748_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1900,-553 1900,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1901,-555 1946,-555 1946,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_1670fd0@0" ObjectIDND1="43775@x" ObjectIDND2="g_1670fd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_15ebc20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1670fd0_0" Pin1InfoVect1LinkObjId="SW-271748_0" Pin1InfoVect2LinkObjId="g_1670fd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1901,-555 1946,-555 1946,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1860,-580 1860,-555 1901,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_1670390@0" ObjectIDZND0="g_1670fd0@0" ObjectIDZND1="43775@x" ObjectIDZND2="g_1670fd0@0" Pin0InfoVect0LinkObjId="g_1670fd0_0" Pin0InfoVect1LinkObjId="SW-271748_0" Pin0InfoVect2LinkObjId="g_1670fd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1670390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1860,-580 1860,-555 1901,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_144a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1901,-555 1900,-553 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="capacitor" ObjectIDND0="g_1670fd0@0" ObjectIDND1="43775@x" ObjectIDND2="0@x" ObjectIDZND0="g_1670fd0@0" ObjectIDZND1="43775@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1670fd0_0" Pin0InfoVect1LinkObjId="SW-271748_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1670fd0_0" Pin1InfoVect1LinkObjId="SW-271748_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1901,-555 1900,-553 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="247,-200 247,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="43736@0" ObjectIDZND0="g_1502350@0" ObjectIDZND1="43736@x" ObjectIDZND2="g_1502350@0" Pin0InfoVect0LinkObjId="g_1502350_0" Pin0InfoVect1LinkObjId="SW-271687_0" Pin0InfoVect2LinkObjId="g_1502350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271687_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="247,-200 247,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3e290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="247,-180 247,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43736@x" ObjectIDND1="43736@x" ObjectIDND2="g_1502350@0" ObjectIDZND0="g_1502350@1" Pin0InfoVect0LinkObjId="g_1502350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271687_0" Pin1InfoVect1LinkObjId="SW-271687_0" Pin1InfoVect2LinkObjId="g_1502350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="247,-180 247,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3e480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="217,-156 217,-181 246,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="0@1" ObjectIDZND0="43736@x" ObjectIDZND1="g_1502350@0" ObjectIDZND2="43736@x" Pin0InfoVect0LinkObjId="SW-271687_0" Pin0InfoVect1LinkObjId="g_1502350_0" Pin0InfoVect2LinkObjId="SW-271687_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="217,-156 217,-181 246,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="246,-181 246,-182 249,-180 247,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="43736@x" ObjectIDND1="g_1502350@0" ObjectIDND2="0@x" ObjectIDZND0="43736@x" ObjectIDZND1="g_1502350@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-271687_0" Pin0InfoVect1LinkObjId="g_1502350_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271687_0" Pin1InfoVect1LinkObjId="g_1502350_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="246,-181 246,-182 249,-180 247,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="290,-163 290,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_1d70120@0" ObjectIDZND0="43736@x" ObjectIDZND1="g_1502350@0" ObjectIDZND2="43736@x" Pin0InfoVect0LinkObjId="SW-271687_0" Pin0InfoVect1LinkObjId="g_1502350_0" Pin0InfoVect2LinkObjId="SW-271687_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d70120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="290,-163 290,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3f350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="246,-181 290,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43736@x" ObjectIDND1="g_1502350@0" ObjectIDND2="43736@x" ObjectIDZND0="g_1d70120@0" ObjectIDZND1="g_20551b0@0" Pin0InfoVect0LinkObjId="g_1d70120_0" Pin0InfoVect1LinkObjId="g_20551b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271687_0" Pin1InfoVect1LinkObjId="g_1502350_0" Pin1InfoVect2LinkObjId="SW-271687_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="246,-181 290,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3f5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="290,-181 328,-181 328,-171 326,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1d70120@0" ObjectIDND1="43736@x" ObjectIDND2="g_1502350@0" ObjectIDZND0="g_20551b0@0" Pin0InfoVect0LinkObjId="g_20551b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d70120_0" Pin1InfoVect1LinkObjId="SW-271687_0" Pin1InfoVect2LinkObjId="g_1502350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="290,-181 328,-181 328,-171 326,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3f820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="384,-160 384,-183 415,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43739@x" ObjectIDZND1="43741@x" ObjectIDZND2="g_1b3c8e0@0" Pin0InfoVect0LinkObjId="SW-271692_0" Pin0InfoVect1LinkObjId="SW-271694_0" Pin0InfoVect2LinkObjId="g_1b3c8e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="384,-160 384,-183 415,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d40530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-205 415,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43739@0" ObjectIDZND0="43741@x" ObjectIDZND1="0@x" ObjectIDZND2="g_1b3c8e0@0" Pin0InfoVect0LinkObjId="SW-271694_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="g_1b3c8e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="415,-205 415,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d40790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-183 415,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43739@x" ObjectIDND1="0@x" ObjectIDND2="g_1b3c8e0@0" ObjectIDZND0="43741@1" Pin0InfoVect0LinkObjId="SW-271694_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271692_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="g_1b3c8e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,-183 415,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d409f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-167 458,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_1b3c8e0@0" ObjectIDZND0="43739@x" ObjectIDZND1="43741@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-271692_0" Pin0InfoVect1LinkObjId="SW-271694_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1b3c8e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="458,-167 458,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1427c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,-183 458,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43739@x" ObjectIDND1="43741@x" ObjectIDND2="0@x" ObjectIDZND0="g_1b3c8e0@0" ObjectIDZND1="g_1b39eb0@0" Pin0InfoVect0LinkObjId="g_1b3c8e0_0" Pin0InfoVect1LinkObjId="g_1b39eb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271692_0" Pin1InfoVect1LinkObjId="SW-271694_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="415,-183 458,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1427ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="458,-183 494,-183 494,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1b3c8e0@0" ObjectIDND1="43739@x" ObjectIDND2="43741@x" ObjectIDZND0="g_1b39eb0@0" Pin0InfoVect0LinkObjId="g_1b39eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1b3c8e0_0" Pin1InfoVect1LinkObjId="SW-271692_0" Pin1InfoVect2LinkObjId="SW-271694_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="458,-183 494,-183 494,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1428a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="661,-159 661,-188 692,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43747@x" ObjectIDZND1="g_16492c0@0" ObjectIDZND2="g_12981f0@0" Pin0InfoVect0LinkObjId="SW-271704_0" Pin0InfoVect1LinkObjId="g_16492c0_0" Pin0InfoVect2LinkObjId="g_12981f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="661,-159 661,-188 692,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14295d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-203 692,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43747@0" ObjectIDZND0="g_16492c0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_12981f0@0" Pin0InfoVect0LinkObjId="g_16492c0_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="g_12981f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271704_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="692,-203 692,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14297c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-188 692,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43747@x" ObjectIDND1="0@x" ObjectIDND2="g_12981f0@0" ObjectIDZND0="g_16492c0@1" Pin0InfoVect0LinkObjId="g_16492c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271704_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="g_12981f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="692,-188 692,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14299d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="692,-188 735,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43747@x" ObjectIDND1="g_16492c0@0" ObjectIDND2="0@x" ObjectIDZND0="g_12981f0@0" ObjectIDZND1="g_128eba0@0" Pin0InfoVect0LinkObjId="g_12981f0_0" Pin0InfoVect1LinkObjId="g_128eba0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271704_0" Pin1InfoVect1LinkObjId="g_16492c0_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="692,-188 735,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_142a4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="771,-175 771,-188 735,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_12981f0@0" ObjectIDZND0="g_128eba0@0" ObjectIDZND1="43747@x" ObjectIDZND2="g_16492c0@0" Pin0InfoVect0LinkObjId="g_128eba0_0" Pin0InfoVect1LinkObjId="SW-271704_0" Pin0InfoVect2LinkObjId="g_16492c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12981f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="771,-175 771,-188 735,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_142a700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="735,-188 735,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12981f0@0" ObjectIDND1="43747@x" ObjectIDND2="g_16492c0@0" ObjectIDZND0="g_128eba0@0" Pin0InfoVect0LinkObjId="g_128eba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12981f0_0" Pin1InfoVect1LinkObjId="SW-271704_0" Pin1InfoVect2LinkObjId="g_16492c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="735,-188 735,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c93a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="839,-159 839,-188 870,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43753@x" ObjectIDZND1="g_1613840@0" ObjectIDZND2="g_16541e0@0" Pin0InfoVect0LinkObjId="SW-271714_0" Pin0InfoVect1LinkObjId="g_1613840_0" Pin0InfoVect2LinkObjId="g_16541e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="839,-159 839,-188 870,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c94560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-203 870,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43753@0" ObjectIDZND0="g_1613840@0" ObjectIDZND1="0@x" ObjectIDZND2="g_16541e0@0" Pin0InfoVect0LinkObjId="g_1613840_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="g_16541e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271714_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="870,-203 870,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c94750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-188 870,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43753@x" ObjectIDND1="0@x" ObjectIDND2="g_16541e0@0" ObjectIDZND0="g_1613840@1" Pin0InfoVect0LinkObjId="g_1613840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271714_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="g_16541e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-188 870,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c94960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-188 913,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43753@x" ObjectIDND1="g_1613840@0" ObjectIDND2="0@x" ObjectIDZND0="g_16541e0@0" ObjectIDZND1="g_1d97010@0" Pin0InfoVect0LinkObjId="g_16541e0_0" Pin0InfoVect1LinkObjId="g_1d97010_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271714_0" Pin1InfoVect1LinkObjId="g_1613840_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="870,-188 913,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c95430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="949,-175 949,-188 913,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="g_16541e0@0" ObjectIDZND0="43753@x" ObjectIDZND1="g_1613840@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-271714_0" Pin0InfoVect1LinkObjId="g_1613840_0" Pin0InfoVect2LinkObjId="g_15ebc20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16541e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="949,-175 949,-188 913,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c95690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="913,-188 913,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="lightningRod" ObjectIDND0="43753@x" ObjectIDND1="g_1613840@0" ObjectIDND2="0@x" ObjectIDZND0="g_1d97010@0" Pin0InfoVect0LinkObjId="g_1d97010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271714_0" Pin1InfoVect1LinkObjId="g_1613840_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="913,-188 913,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c95fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="870,-87 870,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1613840@0" ObjectIDZND0="44661@0" Pin0InfoVect0LinkObjId="SM-CX_YLGF.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1613840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="870,-87 870,-44 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c961d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1299,-160 1299,-187 1330,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43761@x" ObjectIDZND1="g_1500380@0" ObjectIDZND2="g_1378570@0" Pin0InfoVect0LinkObjId="SW-271726_0" Pin0InfoVect1LinkObjId="g_1500380_0" Pin0InfoVect2LinkObjId="g_1378570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1299,-160 1299,-187 1330,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c96d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-204 1330,-187 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43761@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1500380@0" ObjectIDZND2="g_1378570@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="g_1500380_0" Pin0InfoVect2LinkObjId="g_1378570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271726_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-204 1330,-187 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d738f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-187 1330,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="43761@x" ObjectIDND2="g_1378570@0" ObjectIDZND0="g_1500380@1" Pin0InfoVect0LinkObjId="g_1500380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271726_0" Pin1InfoVect2LinkObjId="g_1378570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-187 1330,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d73b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1330,-187 1373,-187 1373,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="43761@x" ObjectIDND2="g_1500380@0" ObjectIDZND0="g_1378570@0" ObjectIDZND1="g_1293330@0" Pin0InfoVect0LinkObjId="g_1378570_0" Pin0InfoVect1LinkObjId="g_1293330_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271726_0" Pin1InfoVect2LinkObjId="g_1500380_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1330,-187 1373,-187 1373,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d74620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-167 1373,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="breaker" ObjectIDND0="g_1378570@0" ObjectIDZND0="g_1293330@0" ObjectIDZND1="0@x" ObjectIDZND2="43761@x" Pin0InfoVect0LinkObjId="g_1293330_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="SW-271726_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1378570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-167 1373,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d74880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-189 1409,-189 1409,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_1378570@0" ObjectIDND1="0@x" ObjectIDND2="43761@x" ObjectIDZND0="g_1293330@0" Pin0InfoVect0LinkObjId="g_1293330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1378570_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="SW-271726_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-189 1409,-189 1409,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d75650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1467,-164 1467,-194 1498,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43764@x" ObjectIDZND1="43766@x" ObjectIDZND2="g_15f8db0@0" Pin0InfoVect0LinkObjId="SW-271731_0" Pin0InfoVect1LinkObjId="SW-271733_0" Pin0InfoVect2LinkObjId="g_15f8db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1467,-164 1467,-194 1498,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d761b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-209 1498,-194 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="capacitor" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43764@0" ObjectIDZND0="0@x" ObjectIDZND1="43766@x" ObjectIDZND2="g_15f8db0@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="SW-271733_0" Pin0InfoVect2LinkObjId="g_15f8db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-209 1498,-194 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d763a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-194 1498,-178 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="43764@x" ObjectIDND2="g_15f8db0@0" ObjectIDZND0="43766@1" Pin0InfoVect0LinkObjId="SW-271733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271731_0" Pin1InfoVect2LinkObjId="g_15f8db0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-194 1498,-178 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d765b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-194 1540,-194 1541,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="43764@x" ObjectIDND2="43766@x" ObjectIDZND0="g_15f8db0@0" ObjectIDZND1="g_15e0a40@0" Pin0InfoVect0LinkObjId="g_15f8db0_0" Pin0InfoVect1LinkObjId="g_15e0a40_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271731_0" Pin1InfoVect2LinkObjId="SW-271733_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-194 1540,-194 1541,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1405970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-171 1541,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="breaker" ObjectIDND0="g_15f8db0@0" ObjectIDZND0="g_15e0a40@0" ObjectIDZND1="0@x" ObjectIDZND2="43764@x" Pin0InfoVect0LinkObjId="g_15e0a40_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="SW-271731_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f8db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-171 1541,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1405bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1541,-193 1577,-193 1577,-180 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_15f8db0@0" ObjectIDND1="0@x" ObjectIDND2="43764@x" ObjectIDZND0="g_15e0a40@0" Pin0InfoVect0LinkObjId="g_15e0a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15f8db0_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="SW-271731_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1541,-193 1577,-193 1577,-180 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14062e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1498,-54 1498,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_129c2f0@0" ObjectIDZND0="43827@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129c2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1498,-54 1498,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14064d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1744,-163 1744,-191 1775,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43772@x" ObjectIDZND1="g_1d15730@0" ObjectIDZND2="g_12d22f0@0" Pin0InfoVect0LinkObjId="SW-271743_0" Pin0InfoVect1LinkObjId="g_1d15730_0" Pin0InfoVect2LinkObjId="g_12d22f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1744,-163 1744,-191 1775,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1407030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-207 1775,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43772@0" ObjectIDZND0="g_1d15730@0" ObjectIDZND1="0@x" ObjectIDZND2="g_12d22f0@0" Pin0InfoVect0LinkObjId="g_1d15730_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="g_12d22f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271743_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-207 1775,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1407240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-191 1775,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="capacitor" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43772@x" ObjectIDND1="0@x" ObjectIDND2="g_12d22f0@0" ObjectIDZND0="g_1d15730@1" Pin0InfoVect0LinkObjId="g_1d15730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271743_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="g_12d22f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-191 1775,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14074a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-191 1818,-191 1818,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43772@x" ObjectIDND1="g_1d15730@0" ObjectIDND2="0@x" ObjectIDZND0="g_12d22f0@0" ObjectIDZND1="g_15ff0e0@0" Pin0InfoVect0LinkObjId="g_12d22f0_0" Pin0InfoVect1LinkObjId="g_15ff0e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271743_0" Pin1InfoVect1LinkObjId="g_1d15730_0" Pin1InfoVect2LinkObjId="g_15ebc20_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-191 1818,-191 1818,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1407f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1818,-170 1818,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="g_12d22f0@0" ObjectIDZND0="g_15ff0e0@0" ObjectIDZND1="43772@x" ObjectIDZND2="g_1d15730@0" Pin0InfoVect0LinkObjId="g_15ff0e0_0" Pin0InfoVect1LinkObjId="SW-271743_0" Pin0InfoVect2LinkObjId="g_1d15730_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12d22f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1818,-170 1818,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14081d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1818,-192 1854,-192 1854,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12d22f0@0" ObjectIDND1="43772@x" ObjectIDND2="g_1d15730@0" ObjectIDZND0="g_15ff0e0@0" Pin0InfoVect0LinkObjId="g_15ff0e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12d22f0_0" Pin1InfoVect1LinkObjId="SW-271743_0" Pin1InfoVect2LinkObjId="g_1d15730_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1818,-192 1854,-192 1854,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1408b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1775,-91 1775,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_1d15730@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d15730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1775,-91 1775,-48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1408d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1922,-163 1922,-192 1953,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="43778@x" ObjectIDZND1="g_132ab80@0" ObjectIDZND2="g_13f8ae0@0" Pin0InfoVect0LinkObjId="SW-271753_0" Pin0InfoVect1LinkObjId="g_132ab80_0" Pin0InfoVect2LinkObjId="g_13f8ae0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15ebc20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1922,-163 1922,-192 1953,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d79f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-207 1953,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43778@0" ObjectIDZND0="0@x" ObjectIDZND1="g_132ab80@0" ObjectIDZND2="g_13f8ae0@0" Pin0InfoVect0LinkObjId="g_15ebc20_0" Pin0InfoVect1LinkObjId="g_132ab80_0" Pin0InfoVect2LinkObjId="g_13f8ae0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-207 1953,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d7c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-192 1953,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="43778@x" ObjectIDND2="g_13f8ae0@0" ObjectIDZND0="g_132ab80@1" Pin0InfoVect0LinkObjId="g_132ab80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271753_0" Pin1InfoVect2LinkObjId="g_13f8ae0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-192 1953,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d7e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-192 1996,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="43778@x" ObjectIDND2="g_132ab80@0" ObjectIDZND0="g_13f8ae0@0" ObjectIDZND1="g_140cd00@0" Pin0InfoVect0LinkObjId="g_13f8ae0_0" Pin0InfoVect1LinkObjId="g_140cd00_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15ebc20_0" Pin1InfoVect1LinkObjId="SW-271753_0" Pin1InfoVect2LinkObjId="g_132ab80_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-192 1996,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d8930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1996,-170 1996,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" EndDevType2="breaker" ObjectIDND0="g_13f8ae0@0" ObjectIDZND0="g_140cd00@0" ObjectIDZND1="0@x" ObjectIDZND2="43778@x" Pin0InfoVect0LinkObjId="g_140cd00_0" Pin0InfoVect1LinkObjId="g_15ebc20_0" Pin0InfoVect2LinkObjId="SW-271753_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_13f8ae0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1996,-170 1996,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d8b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1996,-192 2032,-192 2032,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="capacitor" BeginDevType2="breaker" EndDevType0="lightningRod" ObjectIDND0="g_13f8ae0@0" ObjectIDND1="0@x" ObjectIDND2="43778@x" ObjectIDZND0="g_140cd00@0" Pin0InfoVect0LinkObjId="g_140cd00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_13f8ae0_0" Pin1InfoVect1LinkObjId="g_15ebc20_0" Pin1InfoVect2LinkObjId="SW-271753_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1996,-192 2032,-192 2032,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19d94a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1953,-91 1953,-48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="generator" ObjectIDND0="g_132ab80@0" ObjectIDZND0="44663@0" Pin0InfoVect0LinkObjId="SM-CX_YLGF.P4_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_132ab80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1953,-91 1953,-48 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271778" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -894.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271778" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43743"/>
     <cge:Term_Ref ObjectID="19997"/>
    <cge:TPSR_Ref TObjectID="43743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271779" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -894.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43743"/>
     <cge:Term_Ref ObjectID="19997"/>
    <cge:TPSR_Ref TObjectID="43743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271775" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 453.000000 -894.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43743"/>
     <cge:Term_Ref ObjectID="19997"/>
    <cge:TPSR_Ref TObjectID="43743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-271842" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271842" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-271843" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271843" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-271844" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271844" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-271848" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271848" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-271845" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271845" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-271849" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 222.000000 -450.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271849" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43788"/>
     <cge:Term_Ref ObjectID="20082"/>
    <cge:TPSR_Ref TObjectID="43788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271790" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -905.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43750"/>
     <cge:Term_Ref ObjectID="20011"/>
    <cge:TPSR_Ref TObjectID="43750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271791" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -905.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43750"/>
     <cge:Term_Ref ObjectID="20011"/>
    <cge:TPSR_Ref TObjectID="43750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271787" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 815.000000 -905.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43750"/>
     <cge:Term_Ref ObjectID="20011"/>
    <cge:TPSR_Ref TObjectID="43750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271820" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1538.000000 -904.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271820" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43768"/>
     <cge:Term_Ref ObjectID="20047"/>
    <cge:TPSR_Ref TObjectID="43768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271821" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1538.000000 -904.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271821" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43768"/>
     <cge:Term_Ref ObjectID="20047"/>
    <cge:TPSR_Ref TObjectID="43768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271817" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1538.000000 -904.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271817" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43768"/>
     <cge:Term_Ref ObjectID="20047"/>
    <cge:TPSR_Ref TObjectID="43768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271833" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -905.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271833" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43775"/>
     <cge:Term_Ref ObjectID="20061"/>
    <cge:TPSR_Ref TObjectID="43775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271834" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -905.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271834" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43775"/>
     <cge:Term_Ref ObjectID="20061"/>
    <cge:TPSR_Ref TObjectID="43775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271830" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -905.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271830" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43775"/>
     <cge:Term_Ref ObjectID="20061"/>
    <cge:TPSR_Ref TObjectID="43775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271766" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 85.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43736"/>
     <cge:Term_Ref ObjectID="19983"/>
    <cge:TPSR_Ref TObjectID="43736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 85.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43736"/>
     <cge:Term_Ref ObjectID="19983"/>
    <cge:TPSR_Ref TObjectID="43736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271763" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 245.000000 85.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43736"/>
     <cge:Term_Ref ObjectID="19983"/>
    <cge:TPSR_Ref TObjectID="43736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271772" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 86.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271772" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43739"/>
     <cge:Term_Ref ObjectID="19989"/>
    <cge:TPSR_Ref TObjectID="43739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271773" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 86.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271773" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43739"/>
     <cge:Term_Ref ObjectID="19989"/>
    <cge:TPSR_Ref TObjectID="43739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271769" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 423.000000 86.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43739"/>
     <cge:Term_Ref ObjectID="19989"/>
    <cge:TPSR_Ref TObjectID="43739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271784" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 85.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271784" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43747"/>
     <cge:Term_Ref ObjectID="20005"/>
    <cge:TPSR_Ref TObjectID="43747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271785" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 85.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271785" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43747"/>
     <cge:Term_Ref ObjectID="20005"/>
    <cge:TPSR_Ref TObjectID="43747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271781" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 678.000000 85.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43747"/>
     <cge:Term_Ref ObjectID="20005"/>
    <cge:TPSR_Ref TObjectID="43747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271796" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 88.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43753"/>
     <cge:Term_Ref ObjectID="20017"/>
    <cge:TPSR_Ref TObjectID="43753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271797" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 88.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271797" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43753"/>
     <cge:Term_Ref ObjectID="20017"/>
    <cge:TPSR_Ref TObjectID="43753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271793" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 878.000000 88.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271793" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43753"/>
     <cge:Term_Ref ObjectID="20017"/>
    <cge:TPSR_Ref TObjectID="43753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271808" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 80.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43761"/>
     <cge:Term_Ref ObjectID="20033"/>
    <cge:TPSR_Ref TObjectID="43761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271809" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 80.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271809" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43761"/>
     <cge:Term_Ref ObjectID="20033"/>
    <cge:TPSR_Ref TObjectID="43761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271805" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 80.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271805" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43761"/>
     <cge:Term_Ref ObjectID="20033"/>
    <cge:TPSR_Ref TObjectID="43761"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271814" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 79.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271814" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43764"/>
     <cge:Term_Ref ObjectID="20039"/>
    <cge:TPSR_Ref TObjectID="43764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271815" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 79.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271815" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43764"/>
     <cge:Term_Ref ObjectID="20039"/>
    <cge:TPSR_Ref TObjectID="43764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271811" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1503.000000 79.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271811" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43764"/>
     <cge:Term_Ref ObjectID="20039"/>
    <cge:TPSR_Ref TObjectID="43764"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271826" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 80.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271826" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43772"/>
     <cge:Term_Ref ObjectID="20055"/>
    <cge:TPSR_Ref TObjectID="43772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271827" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 80.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271827" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43772"/>
     <cge:Term_Ref ObjectID="20055"/>
    <cge:TPSR_Ref TObjectID="43772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271823" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1767.000000 80.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271823" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43772"/>
     <cge:Term_Ref ObjectID="20055"/>
    <cge:TPSR_Ref TObjectID="43772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271839" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1975.000000 88.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271839" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43778"/>
     <cge:Term_Ref ObjectID="20067"/>
    <cge:TPSR_Ref TObjectID="43778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271840" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1975.000000 88.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271840" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43778"/>
     <cge:Term_Ref ObjectID="20067"/>
    <cge:TPSR_Ref TObjectID="43778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271836" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1975.000000 88.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271836" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43778"/>
     <cge:Term_Ref ObjectID="20067"/>
    <cge:TPSR_Ref TObjectID="43778"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1622ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 187.000000 -82.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2d1f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.000000 -97.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ab140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 201.000000 -112.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_167d3e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 822.000000 -88.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_167d6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -103.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0b8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -118.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0baf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 390.000000 896.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d961b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 379.000000 881.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d963b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 404.000000 866.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1b1e050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 154.000000 390.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ce9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 171.000000 375.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1df59f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 404.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2d5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 163.000000 420.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2d7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 435.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2054f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 162.000000 450.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13795d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1274.000000 -79.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1379830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 -94.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1379a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -109.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1379da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.000000 -87.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1906.000000 -102.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1931.000000 -117.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1482.000000 904.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1471.000000 889.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ce4f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1496.000000 874.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_165ad20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2023.000000 414.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2040.000000 399.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292a30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 428.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2032.000000 444.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1292eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 459.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12930f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2031.000000 474.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c850e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 905.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c85350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 748.000000 890.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c85590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.000000 875.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c86430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1846.000000 904.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c86660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 889.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c868a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1860.000000 874.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c87740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 366.000000 -84.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c87970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.000000 -99.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c87bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 380.000000 -114.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c87ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 620.000000 -85.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c88140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -100.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c88380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -115.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc9630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 -79.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc9850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1435.000000 -94.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc9a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -109.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc9dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1713.000000 -80.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cca020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1702.000000 -95.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cca260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1727.000000 -110.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LC" endPointId="0" endStationName="CX_YLGF" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yilian1" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="415,-27 415,13 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43826" ObjectName="AC-35kV.LN_yilian1"/>
    <cge:TPSR_Ref TObjectID="43826_SS-307"/></metadata>
   <polyline fill="none" opacity="0" points="415,-27 415,13 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LC" endPointId="0" endStationName="CX_YLGF" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yilian2" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1498,-36 1498,11 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43827" ObjectName="AC-35kV.LN_yilian2"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1498,-36 1498,11 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-271688">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 215.000000 -251.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43737" ObjectName="SW-CX_YLGF.CX_YLGF_3611SW"/>
     <cge:Meas_Ref ObjectId="271688"/>
    <cge:TPSR_Ref TObjectID="43737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271693">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -255.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43740" ObjectName="SW-CX_YLGF.CX_YLGF_3621SW"/>
     <cge:Meas_Ref ObjectId="271693"/>
    <cge:TPSR_Ref TObjectID="43740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271694">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 406.000000 -133.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43741" ObjectName="SW-CX_YLGF.CX_YLGF_3623SW"/>
     <cge:Meas_Ref ObjectId="271694"/>
    <cge:TPSR_Ref TObjectID="43741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271705">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 660.000000 -254.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43748" ObjectName="SW-CX_YLGF.CX_YLGF_3641SW"/>
     <cge:Meas_Ref ObjectId="271705"/>
    <cge:TPSR_Ref TObjectID="43748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271715">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 838.000000 -254.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43754" ObjectName="SW-CX_YLGF.CX_YLGF_3661SW"/>
     <cge:Meas_Ref ObjectId="271715"/>
    <cge:TPSR_Ref TObjectID="43754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271699">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -364.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43744" ObjectName="SW-CX_YLGF.CX_YLGF_3631SW"/>
     <cge:Meas_Ref ObjectId="271699"/>
    <cge:TPSR_Ref TObjectID="43744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271701">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 386.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43746" ObjectName="SW-CX_YLGF.CX_YLGF_3630SW"/>
     <cge:Meas_Ref ObjectId="271701"/>
    <cge:TPSR_Ref TObjectID="43746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271758">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 609.000000 -452.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43781" ObjectName="SW-CX_YLGF.CX_YLGF_3901SW"/>
     <cge:Meas_Ref ObjectId="271758"/>
    <cge:TPSR_Ref TObjectID="43781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271710">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -377.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43751" ObjectName="SW-CX_YLGF.CX_YLGF_3651SW"/>
     <cge:Meas_Ref ObjectId="271710"/>
    <cge:TPSR_Ref TObjectID="43751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271727">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.000000 -255.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43762" ObjectName="SW-CX_YLGF.CX_YLGF_3712SW"/>
     <cge:Meas_Ref ObjectId="271727"/>
    <cge:TPSR_Ref TObjectID="43762"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271732">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -259.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43765" ObjectName="SW-CX_YLGF.CX_YLGF_3722SW"/>
     <cge:Meas_Ref ObjectId="271732"/>
    <cge:TPSR_Ref TObjectID="43765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271733">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1489.000000 -137.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43766" ObjectName="SW-CX_YLGF.CX_YLGF_3723SW"/>
     <cge:Meas_Ref ObjectId="271733"/>
    <cge:TPSR_Ref TObjectID="43766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271744">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1743.000000 -258.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43773" ObjectName="SW-CX_YLGF.CX_YLGF_3742SW"/>
     <cge:Meas_Ref ObjectId="271744"/>
    <cge:TPSR_Ref TObjectID="43773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271754">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1921.000000 -258.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43779" ObjectName="SW-CX_YLGF.CX_YLGF_3762SW"/>
     <cge:Meas_Ref ObjectId="271754"/>
    <cge:TPSR_Ref TObjectID="43779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271738">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1505.000000 -368.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43769" ObjectName="SW-CX_YLGF.CX_YLGF_3732SW"/>
     <cge:Meas_Ref ObjectId="271738"/>
    <cge:TPSR_Ref TObjectID="43769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271739">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 -655.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43770" ObjectName="SW-CX_YLGF.CX_YLGF_3730SW"/>
     <cge:Meas_Ref ObjectId="271739"/>
    <cge:TPSR_Ref TObjectID="43770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271760">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1692.000000 -456.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43783" ObjectName="SW-CX_YLGF.CX_YLGF_3902SW"/>
     <cge:Meas_Ref ObjectId="271760"/>
    <cge:TPSR_Ref TObjectID="43783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271749">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1868.000000 -381.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43776" ObjectName="SW-CX_YLGF.CX_YLGF_3752SW"/>
     <cge:Meas_Ref ObjectId="271749"/>
    <cge:TPSR_Ref TObjectID="43776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271720">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 -373.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43757" ObjectName="SW-CX_YLGF.CX_YLGF_3121SW"/>
     <cge:Meas_Ref ObjectId="271720"/>
    <cge:TPSR_Ref TObjectID="43757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271722">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1269.000000 -378.000000)" xlink:href="#switch2:shape40_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43759" ObjectName="SW-CX_YLGF.CX_YLGF_3122SW"/>
     <cge:Meas_Ref ObjectId="271722"/>
    <cge:TPSR_Ref TObjectID="43759"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-227" y="-940"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-227" y="-940"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-276" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-276" y="-957"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19f16c0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 223.000000 -110.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_130a2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 391.000000 -114.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1646a30" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 668.000000 -113.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1649b90" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 846.000000 -113.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d1120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 670.000000 -430.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_134ef30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 682.000000 -632.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15ca980" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 857.000000 -620.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1659ce0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1306.000000 -114.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ce0820" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1474.000000 -118.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15fe190" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1751.000000 -117.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_140bdb0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1929.000000 -117.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14acba0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1754.000000 -434.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13bd120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1766.000000 -636.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1671c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1939.000000 -624.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YLGF"/>
</svg>