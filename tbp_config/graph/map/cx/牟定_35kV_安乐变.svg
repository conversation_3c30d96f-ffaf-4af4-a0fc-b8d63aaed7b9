<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-201" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-570 -1150 1797 1197">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape170">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="16,30 7,30 " stroke-width="1"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="15,10 6,10 " stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape25_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="58" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape25_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="12,76 19,76 16,83 12,76 "/>
   </symbol>
   <symbol id="transformer2:shape96_0">
    <circle cx="35" cy="81" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="56" y1="109" y2="140"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="56" x2="56" y1="137" y2="137"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="54" y1="140" y2="135"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="56" y1="140" y2="140"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="34,57 34,42 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="34" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="59" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="63" x2="55" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="53" x2="65" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="34,57 59,57 59,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="34,14 40,27 28,27 34,14 34,15 34,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="35" y1="69" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="35" x2="43" y1="77" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="27" x2="35" y1="85" y2="77"/>
   </symbol>
   <symbol id="transformer2:shape96_1">
    <ellipse cx="35" cy="112" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="34" y1="109" y2="117"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="34" x2="42" y1="117" y2="125"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="125" y2="117"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="voltageTransformer:shape136">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="4" y1="9" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="7" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="5" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="6" y1="7" y2="7"/>
    <ellipse cx="36" cy="17" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="24" cy="18" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="29" cy="8" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="4" y1="19" y2="19"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c44f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c45730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c45e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c46570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c475e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c480a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c489e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c492e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c49c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c4a430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c4a430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c4c220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c4c220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1c4d0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b3de90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b3eac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b3f2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b3fc80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b46d80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b47570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b47b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b48540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b496c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b4a040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b4ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_146f090" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b455d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b45e50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b43090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b43a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1c152b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b410e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1207" width="1807" x="-575" y="-1155"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="25" stroke="rgb(255,255,0)" stroke-width="1" width="11" x="1122" y="-876"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="-166" y="-1104"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -396.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133308">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 117.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24384" ObjectName="SW-MD_AL.MD_AL_0711SW"/>
     <cge:Meas_Ref ObjectId="133308"/>
    <cge:TPSR_Ref TObjectID="24384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133309">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 117.000000 -122.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24385" ObjectName="SW-MD_AL.MD_AL_0716SW"/>
     <cge:Meas_Ref ObjectId="133309"/>
    <cge:TPSR_Ref TObjectID="24385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133325">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24387" ObjectName="SW-MD_AL.MD_AL_0721SW"/>
     <cge:Meas_Ref ObjectId="133325"/>
    <cge:TPSR_Ref TObjectID="24387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133326">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24388" ObjectName="SW-MD_AL.MD_AL_0726SW"/>
     <cge:Meas_Ref ObjectId="133326"/>
    <cge:TPSR_Ref TObjectID="24388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133342">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24390" ObjectName="SW-MD_AL.MD_AL_0731SW"/>
     <cge:Meas_Ref ObjectId="133342"/>
    <cge:TPSR_Ref TObjectID="24390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -120.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24391" ObjectName="SW-MD_AL.MD_AL_0736SW"/>
     <cge:Meas_Ref ObjectId="133343"/>
    <cge:TPSR_Ref TObjectID="24391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133359">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24393" ObjectName="SW-MD_AL.MD_AL_0741SW"/>
     <cge:Meas_Ref ObjectId="133359"/>
    <cge:TPSR_Ref TObjectID="24393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133360">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 -117.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24394" ObjectName="SW-MD_AL.MD_AL_0746SW"/>
     <cge:Meas_Ref ObjectId="133360"/>
    <cge:TPSR_Ref TObjectID="24394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133376">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24396" ObjectName="SW-MD_AL.MD_AL_0752SW"/>
     <cge:Meas_Ref ObjectId="133376"/>
    <cge:TPSR_Ref TObjectID="24396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -125.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24397" ObjectName="SW-MD_AL.MD_AL_0756SW"/>
     <cge:Meas_Ref ObjectId="133377"/>
    <cge:TPSR_Ref TObjectID="24397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133212">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -794.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24367" ObjectName="SW-MD_AL.MD_AL_3721SW"/>
     <cge:Meas_Ref ObjectId="133212"/>
    <cge:TPSR_Ref TObjectID="24367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133214">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -931.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24369" ObjectName="SW-MD_AL.MD_AL_3726SW"/>
     <cge:Meas_Ref ObjectId="133214"/>
    <cge:TPSR_Ref TObjectID="24369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -922.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24364" ObjectName="SW-MD_AL.MD_AL_3716SW"/>
     <cge:Meas_Ref ObjectId="133193"/>
    <cge:TPSR_Ref TObjectID="24364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -794.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24363" ObjectName="SW-MD_AL.MD_AL_3711SW"/>
     <cge:Meas_Ref ObjectId="133192"/>
    <cge:TPSR_Ref TObjectID="24363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133216">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 346.000000 -985.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24371" ObjectName="SW-MD_AL.MD_AL_37267SW"/>
     <cge:Meas_Ref ObjectId="133216"/>
    <cge:TPSR_Ref TObjectID="24371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133194">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 798.000000 -975.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24365" ObjectName="SW-MD_AL.MD_AL_37167SW"/>
     <cge:Meas_Ref ObjectId="133194"/>
    <cge:TPSR_Ref TObjectID="24365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133213">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 347.000000 -850.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24368" ObjectName="SW-MD_AL.MD_AL_37217SW"/>
     <cge:Meas_Ref ObjectId="133213"/>
    <cge:TPSR_Ref TObjectID="24368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133215">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 349.000000 -911.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24370" ObjectName="SW-MD_AL.MD_AL_37260SW"/>
     <cge:Meas_Ref ObjectId="133215"/>
    <cge:TPSR_Ref TObjectID="24370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133263">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24377" ObjectName="SW-MD_AL.MD_AL_3021SW"/>
     <cge:Meas_Ref ObjectId="133263"/>
    <cge:TPSR_Ref TObjectID="24377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 -706.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24373" ObjectName="SW-MD_AL.MD_AL_3011SW"/>
     <cge:Meas_Ref ObjectId="133234"/>
    <cge:TPSR_Ref TObjectID="24373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24380" ObjectName="SW-MD_AL.MD_AL_3901SW"/>
     <cge:Meas_Ref ObjectId="133300"/>
    <cge:TPSR_Ref TObjectID="24380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -835.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24381" ObjectName="SW-MD_AL.MD_AL_39017SW"/>
     <cge:Meas_Ref ObjectId="133301"/>
    <cge:TPSR_Ref TObjectID="24381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133305">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 68.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24382" ObjectName="SW-MD_AL.MD_AL_0901SW"/>
     <cge:Meas_Ref ObjectId="133305"/>
    <cge:TPSR_Ref TObjectID="24382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133269">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -410.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24379" ObjectName="SW-MD_AL.MD_AL_0021SW"/>
     <cge:Meas_Ref ObjectId="133269"/>
    <cge:TPSR_Ref TObjectID="24379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -409.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24375" ObjectName="SW-MD_AL.MD_AL_0011SW"/>
     <cge:Meas_Ref ObjectId="133240"/>
    <cge:TPSR_Ref TObjectID="24375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -707.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -1031.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 -1016.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-MD_AL.MD_AL_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="87,-780 1165,-780 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24359" ObjectName="BS-MD_AL.MD_AL_3IM"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   <polyline fill="none" opacity="0" points="87,-780 1165,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-MD_AL.MD_AL_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-110,-380 1226,-380 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="24360" ObjectName="BS-MD_AL.MD_AL_9IM"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   <polyline fill="none" opacity="0" points="-110,-380 1226,-380 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-MD_AL.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.625000 117.000000 -54.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34278" ObjectName="EC-MD_AL.071Ld"/>
    <cge:TPSR_Ref TObjectID="34278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_AL.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -58.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34279" ObjectName="EC-MD_AL.072Ld"/>
    <cge:TPSR_Ref TObjectID="34279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_AL.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -57.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34280" ObjectName="EC-MD_AL.073Ld"/>
    <cge:TPSR_Ref TObjectID="34280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_AL.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 -58.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34281" ObjectName="EC-MD_AL.074Ld"/>
    <cge:TPSR_Ref TObjectID="34281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.000000 -526.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-MD_AL.075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -54.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34282" ObjectName="EC-MD_AL.075Ld"/>
    <cge:TPSR_Ref TObjectID="34282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 -553.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1423890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -988.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14242c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -914.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1424cf0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 271.000000 -853.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14256e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 727.000000 -978.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_145e930" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -881.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_155e510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-335 126,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24384@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155eb00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133308_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="126,-335 126,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_155e700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-299 126,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24384@0" ObjectIDZND0="24383@1" Pin0InfoVect0LinkObjId="SW-133306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="126,-299 126,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_155e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-223 126,-163 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24383@0" ObjectIDZND0="24385@1" Pin0InfoVect0LinkObjId="SW-133309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="126,-223 126,-163 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_155eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-330 336,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24387@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-330 336,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_155ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-294 336,-250 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24387@0" ObjectIDZND0="24386@1" Pin0InfoVect0LinkObjId="SW-133323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-294 336,-250 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149bee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-223 336,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24386@0" ObjectIDZND0="24388@1" Pin0InfoVect0LinkObjId="SW-133326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-223 336,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149c110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-333 1013,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24396@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133376_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-333 1013,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149c340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-297 1013,-249 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24396@0" ObjectIDZND0="24395@1" Pin0InfoVect0LinkObjId="SW-133374_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133376_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-297 1013,-249 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-222 1013,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24395@0" ObjectIDZND0="24397@1" Pin0InfoVect0LinkObjId="SW-133377_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133374_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-222 1013,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1138,-397 1138,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1138,-397 1138,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_149c9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1138,-449 1138,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1138,-449 1138,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149cc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-415 790,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24379@0" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133269_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-415 790,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149ce90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="790,-451 790,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24379@1" ObjectIDZND0="24378@0" Pin0InfoVect0LinkObjId="SW-133267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133269_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="790,-451 790,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_149d0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-685 791,-714 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24376@1" ObjectIDZND0="24377@0" Pin0InfoVect0LinkObjId="SW-133263_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-685 791,-714 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_149d350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-750 791,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24377@1" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d5b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133263_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-750 791,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_149d5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-747 330,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24373@1" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-747 330,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_149d810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-711 330,-684 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24373@0" ObjectIDZND0="24372@1" Pin0InfoVect0LinkObjId="SW-133232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-711 330,-684 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149da70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-492 329,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24374@0" ObjectIDZND0="24375@1" Pin0InfoVect0LinkObjId="SW-133240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133238_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-492 329,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149dcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="329,-414 329,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24375@0" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="329,-414 329,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149df30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="77,-414 77,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24382@0" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="77,-414 77,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149e190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="40,-465 41,-458 77,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_14af260@0" ObjectIDZND0="24382@x" ObjectIDZND1="g_1227980@0" Pin0InfoVect0LinkObjId="SW-133305_0" Pin0InfoVect1LinkObjId="g_1227980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14af260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="40,-465 41,-458 77,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="77,-450 77,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="24382@1" ObjectIDZND0="g_14af260@0" ObjectIDZND1="g_1227980@0" Pin0InfoVect0LinkObjId="g_14af260_0" Pin0InfoVect1LinkObjId="g_1227980_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133305_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="77,-450 77,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149eb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="77,-458 77,-484 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_14af260@0" ObjectIDND1="24382@x" ObjectIDZND0="g_1227980@0" Pin0InfoVect0LinkObjId="g_1227980_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14af260_0" Pin1InfoVect1LinkObjId="SW-133305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="77,-458 77,-484 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1426ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="305,-994 289,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24371@1" ObjectIDZND0="g_1423890@0" Pin0InfoVect0LinkObjId="g_1423890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133216_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="305,-994 289,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1426f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="308,-920 294,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24370@1" ObjectIDZND0="g_14242c0@0" Pin0InfoVect0LinkObjId="g_14242c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133215_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="308,-920 294,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14271a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="306,-859 289,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24368@1" ObjectIDZND0="g_1424cf0@0" Pin0InfoVect0LinkObjId="g_1424cf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133213_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="306,-859 289,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1427400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-252 553,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24389@1" ObjectIDZND0="24390@0" Pin0InfoVect0LinkObjId="SW-133342_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="553,-252 553,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1427660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-330 553,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24390@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133342_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="553,-330 553,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14278c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-158 773,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24394@1" ObjectIDZND0="24392@0" Pin0InfoVect0LinkObjId="SW-133357_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133360_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="773,-158 773,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1427b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-250 773,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24392@1" ObjectIDZND0="24393@0" Pin0InfoVect0LinkObjId="SW-133359_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133357_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="773,-250 773,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1427d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-161 553,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24391@1" ObjectIDZND0="24389@0" Pin0InfoVect0LinkObjId="SW-133340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133343_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="553,-161 553,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1428680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="167,-107 167,-116 126,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1439950@0" ObjectIDZND0="24385@x" ObjectIDZND1="34278@x" Pin0InfoVect0LinkObjId="SW-133309_0" Pin0InfoVect1LinkObjId="EC-MD_AL.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1439950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="167,-107 167,-116 126,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14288e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-127 126,-116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24385@0" ObjectIDZND0="g_1439950@0" ObjectIDZND1="34278@x" Pin0InfoVect0LinkObjId="g_1439950_0" Pin0InfoVect1LinkObjId="EC-MD_AL.071Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="126,-127 126,-116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1428b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="126,-116 126,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1439950@0" ObjectIDND1="24385@x" ObjectIDZND0="34278@0" Pin0InfoVect0LinkObjId="EC-MD_AL.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1439950_0" Pin1InfoVect1LinkObjId="SW-133309_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="126,-116 126,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1428da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="379,-107 379,-117 336,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1581250@0" ObjectIDZND0="24388@x" ObjectIDZND1="34279@x" Pin0InfoVect0LinkObjId="SW-133326_0" Pin0InfoVect1LinkObjId="EC-MD_AL.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1581250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="379,-107 379,-117 336,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145c6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-125 336,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24388@0" ObjectIDZND0="g_1581250@0" ObjectIDZND1="34279@x" Pin0InfoVect0LinkObjId="g_1581250_0" Pin0InfoVect1LinkObjId="EC-MD_AL.072Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="336,-125 336,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145c900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="336,-117 336,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1581250@0" ObjectIDND1="24388@x" ObjectIDZND0="34279@0" Pin0InfoVect0LinkObjId="EC-MD_AL.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1581250_0" Pin1InfoVect1LinkObjId="SW-133326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="336,-117 336,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145cb60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-107 593,-117 553,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_15805e0@0" ObjectIDZND0="24391@x" ObjectIDZND1="34280@x" Pin0InfoVect0LinkObjId="SW-133343_0" Pin0InfoVect1LinkObjId="EC-MD_AL.073Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15805e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="593,-107 593,-117 553,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145cdc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-84 553,-117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34280@0" ObjectIDZND0="g_15805e0@0" ObjectIDZND1="24391@x" Pin0InfoVect0LinkObjId="g_15805e0_0" Pin0InfoVect1LinkObjId="SW-133343_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-MD_AL.073Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="553,-84 553,-117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-117 553,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_15805e0@0" ObjectIDND1="34280@x" ObjectIDZND0="24391@0" Pin0InfoVect0LinkObjId="SW-133343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15805e0_0" Pin1InfoVect1LinkObjId="EC-MD_AL.073Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="553,-117 553,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145d280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="808,-107 808,-114 773,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_143a290@0" ObjectIDZND0="24394@x" ObjectIDZND1="34281@x" Pin0InfoVect0LinkObjId="SW-133360_0" Pin0InfoVect1LinkObjId="EC-MD_AL.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_143a290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="808,-107 808,-114 773,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-122 773,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="24394@0" ObjectIDZND0="g_143a290@0" ObjectIDZND1="34281@x" Pin0InfoVect0LinkObjId="g_143a290_0" Pin0InfoVect1LinkObjId="EC-MD_AL.074Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133360_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="773,-122 773,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145dfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-114 773,-85 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_143a290@0" ObjectIDND1="24394@x" ObjectIDZND0="34281@0" Pin0InfoVect0LinkObjId="EC-MD_AL.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_143a290_0" Pin1InfoVect1LinkObjId="SW-133360_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="773,-114 773,-85 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145e210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1055,-107 1055,-118 1013,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1581ec0@0" ObjectIDZND0="24397@x" ObjectIDZND1="34282@x" Pin0InfoVect0LinkObjId="SW-133377_0" Pin0InfoVect1LinkObjId="EC-MD_AL.075Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1581ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1055,-107 1055,-118 1013,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145e470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-81 1013,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34282@0" ObjectIDZND0="24397@x" ObjectIDZND1="g_1581ec0@0" Pin0InfoVect0LinkObjId="SW-133377_0" Pin0InfoVect1LinkObjId="g_1581ec0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-MD_AL.075Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-81 1013,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_145e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1013,-118 1013,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_1581ec0@0" ObjectIDND1="34282@x" ObjectIDZND0="24397@0" Pin0InfoVect0LinkObjId="SW-133377_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1581ec0_0" Pin1InfoVect1LinkObjId="EC-MD_AL.075Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1013,-118 1013,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d6fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-799 360,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24367@0" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133212_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="360,-799 360,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="342,-859 360,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="24368@0" ObjectIDZND0="24366@x" ObjectIDZND1="24367@x" Pin0InfoVect0LinkObjId="SW-133210_0" Pin0InfoVect1LinkObjId="SW-133212_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133213_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="342,-859 360,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d8230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-869 360,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="24366@0" ObjectIDZND0="24368@x" ObjectIDZND1="24367@x" Pin0InfoVect0LinkObjId="SW-133213_0" Pin0InfoVect1LinkObjId="SW-133212_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="360,-869 360,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d8490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-859 360,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="24368@x" ObjectIDND1="24366@x" ObjectIDZND0="24367@1" Pin0InfoVect0LinkObjId="SW-133212_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133213_0" Pin1InfoVect1LinkObjId="SW-133210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="360,-859 360,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="341,-994 360,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24371@0" ObjectIDZND0="24369@x" ObjectIDZND1="g_1448c40@0" ObjectIDZND2="37800@1" Pin0InfoVect0LinkObjId="SW-133214_0" Pin0InfoVect1LinkObjId="g_1448c40_0" Pin0InfoVect2LinkObjId="g_1498370_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133216_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="341,-994 360,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d9130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-972 360,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24369@1" ObjectIDZND0="24371@x" ObjectIDZND1="g_1448c40@0" ObjectIDZND2="37800@1" Pin0InfoVect0LinkObjId="SW-133216_0" Pin0InfoVect1LinkObjId="g_1448c40_0" Pin0InfoVect2LinkObjId="g_1498370_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133214_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="360,-972 360,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d9390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="344,-920 360,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24370@0" ObjectIDZND0="24369@x" ObjectIDZND1="24366@x" Pin0InfoVect0LinkObjId="SW-133214_0" Pin0InfoVect1LinkObjId="SW-133210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133215_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="344,-920 360,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15487c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-936 360,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="24369@0" ObjectIDZND0="24370@x" ObjectIDZND1="24366@x" Pin0InfoVect0LinkObjId="SW-133215_0" Pin0InfoVect1LinkObjId="SW-133210_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133214_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="360,-936 360,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1548a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-920 360,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="24370@x" ObjectIDND1="24369@x" ObjectIDZND0="24366@1" Pin0InfoVect0LinkObjId="SW-133210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133215_0" Pin1InfoVect1LinkObjId="SW-133214_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="360,-920 360,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1548c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-799 807,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24363@0" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-799 807,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1549490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-835 807,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="24363@1" ObjectIDZND0="24362@0" Pin0InfoVect0LinkObjId="SW-133190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133192_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-835 807,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1549f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,-1029 769,-1017 807,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_153fb00@0" ObjectIDZND0="24365@x" ObjectIDZND1="24364@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-133194_0" Pin0InfoVect1LinkObjId="SW-133193_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_153fb00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="769,-1029 769,-1017 807,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_154aa50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-1017 807,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_153fb00@0" ObjectIDND1="24365@x" ObjectIDND2="24364@x" ObjectIDZND0="37796@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_153fb00_0" Pin1InfoVect1LinkObjId="SW-133194_0" Pin1InfoVect2LinkObjId="SW-133193_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-1017 807,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_154acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="309,-1033 309,-1024 360,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1448c40@0" ObjectIDZND0="24371@x" ObjectIDZND1="24369@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-133216_0" Pin0InfoVect1LinkObjId="SW-133214_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1448c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="309,-1033 309,-1024 360,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1498370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-1024 360,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1448c40@0" ObjectIDND1="24371@x" ObjectIDND2="24369@x" ObjectIDZND0="37800@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1448c40_0" Pin1InfoVect1LinkObjId="SW-133216_0" Pin1InfoVect2LinkObjId="SW-133214_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="360,-1024 360,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14985d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-792 1128,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24380@0" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-792 1128,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1498e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1077,-886 1076,-876 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_145e930@0" ObjectIDZND0="24381@1" Pin0InfoVect0LinkObjId="SW-133301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_145e930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1077,-886 1076,-876 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1499060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-840 1077,-835 1128,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="24381@0" ObjectIDZND0="24380@x" ObjectIDZND1="g_150f5c0@0" Pin0InfoVect0LinkObjId="SW-133300_0" Pin0InfoVect1LinkObjId="g_150f5c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-840 1077,-835 1128,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1499b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-828 1128,-835 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="24380@1" ObjectIDZND0="24381@x" ObjectIDZND1="g_150f5c0@0" Pin0InfoVect0LinkObjId="SW-133301_0" Pin0InfoVect1LinkObjId="g_150f5c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-828 1128,-835 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1499db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="773,-335 773,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="24393@1" ObjectIDZND0="24360@0" Pin0InfoVect0LinkObjId="g_155e510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133359_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="773,-335 773,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_155b360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-984 793,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="24364@x" ObjectIDND1="g_153fb00@0" ObjectIDND2="37796@1" ObjectIDZND0="24365@0" Pin0InfoVect0LinkObjId="SW-133194_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133193_0" Pin1InfoVect1LinkObjId="g_153fb00_0" Pin1InfoVect2LinkObjId="g_154aa50_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-984 793,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_155b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="757,-984 745,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="24365@1" ObjectIDZND0="g_14256e0@0" Pin0InfoVect0LinkObjId="g_14256e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133194_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="757,-984 745,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_155b740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-897 807,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="24362@1" ObjectIDZND0="24364@0" Pin0InfoVect0LinkObjId="SW-133193_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="807,-897 807,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_155b930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-963 807,-984 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="24364@1" ObjectIDZND0="24365@x" ObjectIDZND1="g_153fb00@0" ObjectIDZND2="37796@1" Pin0InfoVect0LinkObjId="SW-133194_0" Pin0InfoVect1LinkObjId="g_153fb00_0" Pin0InfoVect2LinkObjId="g_154aa50_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133193_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="807,-963 807,-984 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1560a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-760 561,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="24359@0" Pin0InfoVect0LinkObjId="g_149d350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-760 561,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1560c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="562,-706 562,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="562,-706 562,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-994 360,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="24371@x" ObjectIDND1="24369@x" ObjectIDZND0="g_1448c40@0" ObjectIDZND1="37800@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1448c40_0" Pin0InfoVect1LinkObjId="g_1498370_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133216_0" Pin1InfoVect1LinkObjId="SW-133214_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="360,-994 360,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f8060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="360,-1010 360,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="24371@x" ObjectIDND1="24369@x" ObjectIDND2="0@x" ObjectIDZND0="g_1448c40@0" ObjectIDZND1="37800@1" Pin0InfoVect0LinkObjId="g_1448c40_0" Pin0InfoVect1LinkObjId="g_1498370_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133216_0" Pin1InfoVect1LinkObjId="SW-133214_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="360,-1010 360,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f8250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-1100 416,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14f8680@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f8680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="416,-1100 416,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f8460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="416,-1036 416,-1011 415,-1010 360,-1010 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="24371@x" ObjectIDZND1="24369@x" ObjectIDZND2="g_1448c40@0" Pin0InfoVect0LinkObjId="SW-133216_0" Pin0InfoVect1LinkObjId="SW-133214_0" Pin0InfoVect2LinkObjId="g_1448c40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="416,-1036 416,-1011 415,-1010 360,-1010 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14f91d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1088 866,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_14f9430@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14f9430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1088 866,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fa890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-984 807,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="24365@x" ObjectIDND1="24364@x" ObjectIDZND0="g_153fb00@0" ObjectIDZND1="37796@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_153fb00_0" Pin0InfoVect1LinkObjId="g_154aa50_1" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133194_0" Pin1InfoVect1LinkObjId="SW-133193_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="807,-984 807,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14faaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="807,-1004 807,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="24365@x" ObjectIDND1="24364@x" ObjectIDND2="0@x" ObjectIDZND0="g_153fb00@0" ObjectIDZND1="37796@1" Pin0InfoVect0LinkObjId="g_153fb00_0" Pin0InfoVect1LinkObjId="g_154aa50_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-133194_0" Pin1InfoVect1LinkObjId="SW-133193_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="807,-1004 807,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-1021 866,-1004 807,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="24365@x" ObjectIDZND1="24364@x" ObjectIDZND2="g_153fb00@0" Pin0InfoVect0LinkObjId="SW-133194_0" Pin0InfoVect1LinkObjId="SW-133193_0" Pin0InfoVect2LinkObjId="g_153fb00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="866,-1021 866,-1004 807,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14fb110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-835 1128,-896 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="24380@x" ObjectIDND1="24381@x" ObjectIDZND0="g_150f5c0@0" Pin0InfoVect0LinkObjId="g_150f5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-133300_0" Pin1InfoVect1LinkObjId="SW-133301_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-835 1128,-896 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a4550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-657 330,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24372@0" ObjectIDZND0="24398@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-657 330,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a47b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-545 330,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24398@0" ObjectIDZND0="24374@1" Pin0InfoVect0LinkObjId="SW-133238_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a4550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="330,-545 330,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_14a6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-516 791,-538 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="24378@1" ObjectIDZND0="24399@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-133267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-516 791,-538 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a6380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="791,-630 791,-658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="24399@1" ObjectIDZND0="24376@0" Pin0InfoVect0LinkObjId="SW-133261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14a6120_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="791,-630 791,-658 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="330" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="791" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="360" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="807" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="1128" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="126" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="336" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="1013" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="790" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="329" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="77" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="553" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="773" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24360" cx="1138" cy="-380" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="24359" cx="561" cy="-780" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-133072" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -273.000000 -1008.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24346" ObjectName="DYN-MD_AL"/>
     <cge:Meas_Ref ObjectId="133072"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152f0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -814.000000) translate(0,12)">3721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152f710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 298.000000 -878.000000) translate(0,12)">37217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152f950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.500000 -941.000000) translate(0,12)">37260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152fb90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 293.500000 -1015.000000) translate(0,12)">37267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_152fdd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 370.500000 -951.000000) translate(0,12)">3726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1530010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -814.000000) translate(0,12)">3711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154b150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.500000 -951.000000) translate(0,12)">3716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154b340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -1006.000000) translate(0,12)">37167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154b580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1082.000000 -819.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154b7c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1020.000000 -867.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154ba00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -739.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154bc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -679.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154be80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -512.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154c0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 -433.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154c300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -739.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154c540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -680.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154c780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -512.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -430.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 85.000000 -439.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.500000 -324.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -152.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.000000 -324.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154d500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.500000 -152.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_154d740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -324.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14380b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.500000 -152.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14382e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.000000 -324.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1438520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 781.500000 -152.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1438760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -324.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14389a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.500000 -152.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1438be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1105.000000 -604.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1438e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 9.000000 -549.000000) translate(0,15)">10kV电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1439190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 80.000000 -769.000000) translate(0,15)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1439590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -112.000000 -402.000000) translate(0,12)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155ca80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 95.000000 -45.000000) translate(0,15)">10kV蒙恩线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155d1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 293.500000 -45.000000) translate(0,15)">10kV安益线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155d540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.500000 -45.000000) translate(0,15)">10kV钒钛铁线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155d750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 733.000000 -45.000000) translate(0,15)">10kV斌胜线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155d9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -45.000000) translate(0,15)">10kV猫街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155dc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -521.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155ded0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -580.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155ded0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -580.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155ded0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -580.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155ded0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -580.000000) translate(0,57)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155e0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 254.000000 -1150.000000) translate(0,15)">35kV新戌安Ⅱ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_155e290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 721.000000 -1144.000000) translate(0,15)">35kV新戌安Ⅰ回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_149f790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -420.000000 -1088.500000) translate(0,16)">安乐变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -947.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -561.000000 -509.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1560ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -243.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1561500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 346.000000 -243.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1561950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 564.000000 -246.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1561da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 784.000000 -244.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15621f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1025.000000 -243.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1562640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 371.000000 -890.000000) translate(0,12)">372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14092d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 818.000000 -891.000000) translate(0,12)">371</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1468f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -557.000000 -740.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1469e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -1074.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_146a480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -293.000000 -1109.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14fcda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -576.000000) translate(0,12)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14fcda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -576.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14fcda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -576.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14fcda0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -576.000000) translate(0,57)">yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_142a7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -570.000000 -105.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_142a7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -570.000000 -105.000000) translate(0,38)">心变运二班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_142ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -438.000000 -114.000000) translate(0,16)">13508785260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_142ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -438.000000 -114.000000) translate(0,36)">18787879001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_142ac70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -438.000000 -114.000000) translate(0,56)">18787879002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_142b1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -438.000000 -162.000000) translate(0,16)">5321071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_142b1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -438.000000 -162.000000) translate(0,36)">2261</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a65e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 232.000000 -615.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a6ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -595.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1701480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -149.500000 -1092.000000) translate(0,16)">AVC</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-133210">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 351.000000 -861.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24366" ObjectName="SW-MD_AL.MD_AL_372BK"/>
     <cge:Meas_Ref ObjectId="133210"/>
    <cge:TPSR_Ref TObjectID="24366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -862.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24362" ObjectName="SW-MD_AL.MD_AL_371BK"/>
     <cge:Meas_Ref ObjectId="133190"/>
    <cge:TPSR_Ref TObjectID="24362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 321.000000 -649.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24372" ObjectName="SW-MD_AL.MD_AL_301BK"/>
     <cge:Meas_Ref ObjectId="133232"/>
    <cge:TPSR_Ref TObjectID="24372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133238">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 320.000000 -484.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24374" ObjectName="SW-MD_AL.MD_AL_001BK"/>
     <cge:Meas_Ref ObjectId="133238"/>
    <cge:TPSR_Ref TObjectID="24374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133261">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 782.000000 -650.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24376" ObjectName="SW-MD_AL.MD_AL_302BK"/>
     <cge:Meas_Ref ObjectId="133261"/>
    <cge:TPSR_Ref TObjectID="24376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133267">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -480.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24378" ObjectName="SW-MD_AL.MD_AL_002BK"/>
     <cge:Meas_Ref ObjectId="133267"/>
    <cge:TPSR_Ref TObjectID="24378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133306">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 117.000000 -215.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24383" ObjectName="SW-MD_AL.MD_AL_071BK"/>
     <cge:Meas_Ref ObjectId="133306"/>
    <cge:TPSR_Ref TObjectID="24383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -215.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24386" ObjectName="SW-MD_AL.MD_AL_072BK"/>
     <cge:Meas_Ref ObjectId="133323"/>
    <cge:TPSR_Ref TObjectID="24386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 544.000000 -217.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24389" ObjectName="SW-MD_AL.MD_AL_073BK"/>
     <cge:Meas_Ref ObjectId="133340"/>
    <cge:TPSR_Ref TObjectID="24389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133357">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.000000 -215.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24392" ObjectName="SW-MD_AL.MD_AL_074BK"/>
     <cge:Meas_Ref ObjectId="133357"/>
    <cge:TPSR_Ref TObjectID="24392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-133374">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -214.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24395" ObjectName="SW-MD_AL.MD_AL_075BK"/>
     <cge:Meas_Ref ObjectId="133374"/>
    <cge:TPSR_Ref TObjectID="24395"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="MD_AL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinxuan2Tal" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="360,-1117 360,-1070 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37800" ObjectName="AC-35kV.LN_xinxuan2Tal"/>
    <cge:TPSR_Ref TObjectID="37800_SS-201"/></metadata>
   <polyline fill="none" opacity="0" points="360,-1117 360,-1070 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="MD_AL" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xinxuan1Tal" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="807,-1117 807,-1070 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37796" ObjectName="AC-35kV.LN_xinxuan1Tal"/>
    <cge:TPSR_Ref TObjectID="37796_SS-201"/></metadata>
   <polyline fill="none" opacity="0" points="807,-1117 807,-1070 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1448c40">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 316.000000 -1087.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_153fb00">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 776.000000 -1083.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14af260">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 47.000000 -519.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1439950">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 160.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_143a290">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 801.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15805e0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 586.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1581250">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 372.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1581ec0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1048.000000 -53.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f8680">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 404.000000 -1136.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f9430">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 854.000000 -1124.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -902.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24366"/>
     <cge:Term_Ref ObjectID="34379"/>
    <cge:TPSR_Ref TObjectID="24366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133121" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -902.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133121" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24366"/>
     <cge:Term_Ref ObjectID="34379"/>
    <cge:TPSR_Ref TObjectID="24366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 489.000000 -902.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24366"/>
     <cge:Term_Ref ObjectID="34379"/>
    <cge:TPSR_Ref TObjectID="24366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133115" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -896.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133115" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24362"/>
     <cge:Term_Ref ObjectID="34371"/>
    <cge:TPSR_Ref TObjectID="24362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133116" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -896.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133116" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24362"/>
     <cge:Term_Ref ObjectID="34371"/>
    <cge:TPSR_Ref TObjectID="24362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 937.000000 -896.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24362"/>
     <cge:Term_Ref ObjectID="34371"/>
    <cge:TPSR_Ref TObjectID="24362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -691.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24372"/>
     <cge:Term_Ref ObjectID="34391"/>
    <cge:TPSR_Ref TObjectID="24372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -691.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24372"/>
     <cge:Term_Ref ObjectID="34391"/>
    <cge:TPSR_Ref TObjectID="24372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -691.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24372"/>
     <cge:Term_Ref ObjectID="34391"/>
    <cge:TPSR_Ref TObjectID="24372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -691.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24372"/>
     <cge:Term_Ref ObjectID="34391"/>
    <cge:TPSR_Ref TObjectID="24372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133140" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -690.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133140" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24376"/>
     <cge:Term_Ref ObjectID="34399"/>
    <cge:TPSR_Ref TObjectID="24376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -690.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24376"/>
     <cge:Term_Ref ObjectID="34399"/>
    <cge:TPSR_Ref TObjectID="24376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -690.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24376"/>
     <cge:Term_Ref ObjectID="34399"/>
    <cge:TPSR_Ref TObjectID="24376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -690.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24376"/>
     <cge:Term_Ref ObjectID="34399"/>
    <cge:TPSR_Ref TObjectID="24376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -527.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24374"/>
     <cge:Term_Ref ObjectID="34395"/>
    <cge:TPSR_Ref TObjectID="24374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -527.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24374"/>
     <cge:Term_Ref ObjectID="34395"/>
    <cge:TPSR_Ref TObjectID="24374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -527.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24374"/>
     <cge:Term_Ref ObjectID="34395"/>
    <cge:TPSR_Ref TObjectID="24374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 466.000000 -527.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24374"/>
     <cge:Term_Ref ObjectID="34395"/>
    <cge:TPSR_Ref TObjectID="24374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -526.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24378"/>
     <cge:Term_Ref ObjectID="34403"/>
    <cge:TPSR_Ref TObjectID="24378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -526.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24378"/>
     <cge:Term_Ref ObjectID="34403"/>
    <cge:TPSR_Ref TObjectID="24378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -526.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24378"/>
     <cge:Term_Ref ObjectID="34403"/>
    <cge:TPSR_Ref TObjectID="24378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-133148" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 931.000000 -526.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133148" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24378"/>
     <cge:Term_Ref ObjectID="34403"/>
    <cge:TPSR_Ref TObjectID="24378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -16.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24383"/>
     <cge:Term_Ref ObjectID="34413"/>
    <cge:TPSR_Ref TObjectID="24383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -16.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24383"/>
     <cge:Term_Ref ObjectID="34413"/>
    <cge:TPSR_Ref TObjectID="24383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -16.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24383"/>
     <cge:Term_Ref ObjectID="34413"/>
    <cge:TPSR_Ref TObjectID="24383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-133164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -16.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24383"/>
     <cge:Term_Ref ObjectID="34413"/>
    <cge:TPSR_Ref TObjectID="24383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24386"/>
     <cge:Term_Ref ObjectID="34419"/>
    <cge:TPSR_Ref TObjectID="24386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24386"/>
     <cge:Term_Ref ObjectID="34419"/>
    <cge:TPSR_Ref TObjectID="24386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24386"/>
     <cge:Term_Ref ObjectID="34419"/>
    <cge:TPSR_Ref TObjectID="24386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-133169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24386"/>
     <cge:Term_Ref ObjectID="34419"/>
    <cge:TPSR_Ref TObjectID="24386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24389"/>
     <cge:Term_Ref ObjectID="34425"/>
    <cge:TPSR_Ref TObjectID="24389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24389"/>
     <cge:Term_Ref ObjectID="34425"/>
    <cge:TPSR_Ref TObjectID="24389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133173" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133173" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24389"/>
     <cge:Term_Ref ObjectID="34425"/>
    <cge:TPSR_Ref TObjectID="24389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-133174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 547.000000 -14.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24389"/>
     <cge:Term_Ref ObjectID="34425"/>
    <cge:TPSR_Ref TObjectID="24389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 765.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133180" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24392"/>
     <cge:Term_Ref ObjectID="34431"/>
    <cge:TPSR_Ref TObjectID="24392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 765.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133181" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24392"/>
     <cge:Term_Ref ObjectID="34431"/>
    <cge:TPSR_Ref TObjectID="24392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 765.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24392"/>
     <cge:Term_Ref ObjectID="34431"/>
    <cge:TPSR_Ref TObjectID="24392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-133179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 765.000000 -13.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24392"/>
     <cge:Term_Ref ObjectID="34431"/>
    <cge:TPSR_Ref TObjectID="24392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-133185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -16.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133185" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24395"/>
     <cge:Term_Ref ObjectID="34437"/>
    <cge:TPSR_Ref TObjectID="24395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-133186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -16.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133186" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24395"/>
     <cge:Term_Ref ObjectID="34437"/>
    <cge:TPSR_Ref TObjectID="24395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-133183" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -16.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133183" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24395"/>
     <cge:Term_Ref ObjectID="34437"/>
    <cge:TPSR_Ref TObjectID="24395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-133184" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -16.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133184" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24395"/>
     <cge:Term_Ref ObjectID="34437"/>
    <cge:TPSR_Ref TObjectID="24395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-133151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-133152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-133153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-133156" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133156" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-133154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-133420" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 121.000000 -876.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133420" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24359"/>
     <cge:Term_Ref ObjectID="34367"/>
    <cge:TPSR_Ref TObjectID="24359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-133157" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133157" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-133158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-133159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-133162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-133160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-133421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -505.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133421" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24360"/>
     <cge:Term_Ref ObjectID="34368"/>
    <cge:TPSR_Ref TObjectID="24360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-133135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 -593.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24398"/>
     <cge:Term_Ref ObjectID="34446"/>
    <cge:TPSR_Ref TObjectID="24398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-133136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 452.000000 -593.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24398"/>
     <cge:Term_Ref ObjectID="34446"/>
    <cge:TPSR_Ref TObjectID="24398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-133149" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.000000 -592.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133149" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24399"/>
     <cge:Term_Ref ObjectID="34447"/>
    <cge:TPSR_Ref TObjectID="24399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-133150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.000000 -592.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="133150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="24399"/>
     <cge:Term_Ref ObjectID="34447"/>
    <cge:TPSR_Ref TObjectID="24399"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="-459" y="-1099"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-507" y="-1116"/></g>
   <g href="35kV安乐变10kV蒙恩线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="137" y="-243"/></g>
   <g href="35kV安乐变10kV安益线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="346" y="-243"/></g>
   <g href="35kV安乐变10kV钒钛铁线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="564" y="-246"/></g>
   <g href="35kV安乐变10kV斌胜线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="784" y="-244"/></g>
   <g href="35kV安乐变10kV猫街线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1025" y="-243"/></g>
   <g href="35kV安乐变35kV新戌安Ⅱ回线372间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="371" y="-890"/></g>
   <g href="35kV安乐变35kV新戌安Ⅰ回线371间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="818" y="-891"/></g>
   <g href="35kV安乐变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="-557" y="-740"/></g>
   <g href="35kV安乐变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="23" qtmmishow="hidden" width="65" x="226" y="-620"/></g>
   <g href="35kV安乐变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="66" x="682" y="-599"/></g>
   <g href="cx_配调_配网接线图35_牟定.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-304" y="-1082"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="-304" y="-1117"/></g>
   <g href="AVC安乐站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="-165" y="-1104"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149a250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 424.000000 900.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149a760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 885.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_149a9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 438.000000 870.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14cb500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 877.000000 898.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14cb7c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 883.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14cba00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 868.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155a970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 578.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155abc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 381.000000 593.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155aec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 578.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155b120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 842.000000 593.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155bc50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 849.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155c050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 864.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155c290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 880.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_155c4d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 64.000000 833.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15302d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 66.000000 803.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1530ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 50.000000 819.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1531230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.000000 477.500000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15314b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.000000 492.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15316f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -98.000000 508.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1531930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.000000 461.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1531b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -90.000000 431.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1531db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -106.000000 447.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1409880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -11.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1409c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.000000 4.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1409ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 40.000000 19.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140a110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 54.000000 -27.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140a440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 644.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140a6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 662.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140a8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 692.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140ab30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 677.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140ae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 420.000000 482.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140b0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 416.000000 500.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140b310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.000000 530.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140b550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 391.000000 515.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140b880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 644.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140baf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 662.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140bd30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 692.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140bf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 677.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140c2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 891.000000 477.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140c510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 887.000000 495.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140c750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 525.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_140c990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 862.000000 510.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1506dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -14.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15072c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 258.000000 1.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1507500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 269.000000 16.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1507740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 283.000000 -30.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1507a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 -12.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1507ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 475.000000 3.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1507f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 486.000000 18.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1508160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 500.000000 -28.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1508490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -15.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14296d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 0.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1429910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 15.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1429b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 -31.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1429e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -11.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142a0f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 938.000000 4.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142a330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 949.000000 19.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_142a570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -27.000000) translate(0,12)">Ic(A):</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1123.000000 -560.000000)" xlink:href="#transformer2:shape25_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1123.000000 -560.000000)" xlink:href="#transformer2:shape25_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.000000 -550.000000)" xlink:href="#transformer2:shape96_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.000000 -550.000000)" xlink:href="#transformer2:shape96_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_AL.MD_AL_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34445"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -540.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 291.000000 -540.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24398" ObjectName="TF-MD_AL.MD_AL_1T"/>
    <cge:TPSR_Ref TObjectID="24398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-MD_AL.MD_AL_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="34449"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -533.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 -533.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="24399" ObjectName="TF-MD_AL.MD_AL_2T"/>
    <cge:TPSR_Ref TObjectID="24399"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="-459" y="-1099"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="-459" y="-1099"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-507" y="-1116"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-507" y="-1116"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="137" y="-243"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="137" y="-243"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="346" y="-243"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="346" y="-243"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="564" y="-246"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="564" y="-246"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="784" y="-244"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="784" y="-244"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1025" y="-243"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1025" y="-243"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="371" y="-890"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="371" y="-890"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="818" y="-891"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="818" y="-891"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="-557" y="-740"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="-557" y="-740"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="23" qtmmishow="hidden" width="65" x="226" y="-620"/>
    </a>
   <metadata/><rect fill="white" height="23" opacity="0" stroke="white" transform="" width="65" x="226" y="-620"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="19" qtmmishow="hidden" width="66" x="682" y="-599"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="66" x="682" y="-599"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-304" y="-1082"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-304" y="-1082"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="-304" y="-1117"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="-304" y="-1117"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="-165" y="-1104"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="-165" y="-1104"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1227980">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 58.000000 -479.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_150f5c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1106.000000 -923.000000)" xlink:href="#voltageTransformer:shape136"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -471.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215177" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -448.000000 -943.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215177" ObjectName="MD_AL:MD_AL_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-215177" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -448.000000 -904.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="215177" ObjectName="MD_AL:MD_AL_sumP"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="MD_AL"/>
</svg>