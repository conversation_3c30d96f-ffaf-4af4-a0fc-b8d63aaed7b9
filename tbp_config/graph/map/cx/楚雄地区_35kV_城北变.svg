<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-102" aopId="0" id="thSvg" viewBox="3116 -1198 2152 1263">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line stroke-width="0.5" x1="15" x2="3" y1="35" y2="10"/>
    <line stroke-width="0.5" x1="14" x2="14" y1="9" y2="9"/>
    <line stroke-width="0.5" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.208305" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="earth:shape3">
    <line stroke-width="0.185606" x1="29" x2="29" y1="7" y2="11"/>
    <line stroke-width="0.226608" x1="4" x2="22" y1="9" y2="9"/>
    <line stroke-width="0.226608" x1="22" x2="22" y1="0" y2="18"/>
    <line stroke-width="0.226608" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape1">
    <line stroke-width="0.185606" x1="7" x2="11" y1="30" y2="30"/>
    <line stroke-width="0.226608" x1="9" x2="9" y1="5" y2="23"/>
    <line stroke-width="0.226608" x1="0" x2="18" y1="23" y2="23"/>
    <line stroke-width="0.226608" x1="6" x2="13" y1="26" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line stroke-width="0.5" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="0.5" width="26" x="18" y="1"/>
    <line stroke-width="0.125" x1="1" x2="1" y1="5" y2="8"/>
    <line stroke-width="0.196875" x1="5" x2="5" y1="3" y2="10"/>
    <line stroke-width="0.125874" x1="9" x2="17" y1="7" y2="7"/>
    <line stroke-width="0.305732" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line stroke-width="0.5" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" rx="8.5" ry="7.5"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="10"/>
    <line stroke-width="0.0778547" x1="25" x2="20" y1="8" y2="5"/>
    <line stroke-width="0.0778547" x1="20" x2="20" y1="10" y2="5"/>
    <line stroke-width="0.0778547" x1="8" x2="5" y1="7" y2="9"/>
    <line stroke-width="0.0778547" x1="10" x2="8" y1="9" y2="7"/>
    <line stroke-width="0.0778547" x1="8" x2="8" y1="4" y2="7"/>
    <line stroke-width="0.0778547" x1="14" x2="11" y1="18" y2="20"/>
    <line stroke-width="0.0778547" x1="16" x2="14" y1="20" y2="18"/>
    <line stroke-width="0.0778547" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" rx="8.5" ry="7.5"/>
    <ellipse cx="14" cy="16" rx="9" ry="7.5"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.25" width="16" x="1" y="5"/>
    <line stroke-width="0.5" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line stroke-width="0.5" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="20"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="63" y2="63"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="60" y2="60"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="55" y2="47"/>
    <line stroke-width="0.305732" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line stroke-width="0.305732" x1="55" x2="55" y1="12" y2="1"/>
    <line stroke-width="0.125874" x1="54" x2="46" y1="8" y2="8"/>
    <line stroke-width="0.196875" x1="59" x2="59" y1="3" y2="10"/>
    <line stroke-width="0.125" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="0.5" width="26" x="19" y="1"/>
    <line stroke-width="0.5" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line stroke-width="0.222222" x1="5" x2="5" y1="5" y2="13"/>
    <line stroke-width="0.111111" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line stroke-width="0.222222" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line stroke-width="0.305732" x1="2" x2="13" y1="8" y2="8"/>
    <line stroke-width="0.125874" x1="7" x2="7" y1="9" y2="17"/>
    <line stroke-width="0.196875" x1="11" x2="4" y1="4" y2="4"/>
    <line stroke-width="0.125" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="0.5" width="12" x="1" y="18"/>
    <line stroke-width="0.5" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line stroke-width="0.5" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="7" y="26"/>
    <polyline fill="none" points="27,39 5,17 5,5 "/>
    <line stroke-width="0.5" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line stroke-width="0.5" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="0.5" width="19" x="-15" y="26"/>
    <polyline fill="none" points="-16,39 6,17 6,5 "/>
    <line stroke-width="0.5" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line stroke-width="0.234885" x1="18" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="27" y2="25"/>
    <line stroke-width="0.234885" x1="-9" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line stroke-width="0.234885" x1="-8" x2="0" y1="26" y2="26"/>
    <line stroke-width="0.1875" x1="18" x2="18" y1="24" y2="27"/>
    <line stroke-width="0.234885" x1="19" x2="27" y1="26" y2="26"/>
    <line stroke-width="0.162432" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line stroke-width="0.162432" x1="7" x2="15" y1="48" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line stroke-width="0.162432" x1="15" x2="15" y1="51" y2="31"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="22" y2="31"/>
    <line stroke-width="0.1875" x1="14" x2="16" y1="49" y2="49"/>
    <line stroke-width="0.234885" x1="15" x2="15" y1="49" y2="58"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" rx="13" ry="12.5"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="40" y2="36"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="36" y2="40"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" r="13"/>
    <line stroke-width="0.132653" x1="9" x2="13" y1="20" y2="16"/>
    <line stroke-width="0.132653" x1="13" x2="17" y1="16" y2="20"/>
    <line stroke-width="0.132653" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" r="24"/>
    <line stroke-width="0.255102" x1="26" x2="17" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="26" x2="34" y1="34" y2="18"/>
    <line stroke-width="0.255102" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" rx="24" ry="24.5"/>
    <line stroke-width="0.255102" x1="24" x2="24" y1="58" y2="66"/>
    <line stroke-width="0.255102" x1="24" x2="32" y1="66" y2="74"/>
    <line stroke-width="0.255102" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="2.07143" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="0.5" width="111" x="0" y="0"/>
    <line stroke="rgb(50,205,50)" stroke-width="1.5" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 19.000000) translate(0,12)">禁止刷新</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1273" width="2162" x="3111" y="-1203"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="1200" stroke="rgb(213,0,0)" stroke-width="0.5" width="2150" x="3117" y="-1197"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-1077"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(213,0,0)" stroke-width="0.5" width="360" x="3117" y="-597"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3806.000000 -1076.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3806.000000 -1021.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52817">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -957.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9381" ObjectName="SW-CX_CB.CX_CB_35167SW"/>
     <cge:Meas_Ref ObjectId="52817"/>
    <cge:TPSR_Ref TObjectID="9381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9380" ObjectName="SW-CX_CB.CX_CB_3516SW"/>
     <cge:Meas_Ref ObjectId="52816"/>
    <cge:TPSR_Ref TObjectID="9380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52818">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9382" ObjectName="SW-CX_CB.CX_CB_3511SW"/>
     <cge:Meas_Ref ObjectId="52818"/>
    <cge:TPSR_Ref TObjectID="9382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9393" ObjectName="SW-CX_CB.CX_CB_3901SW"/>
     <cge:Meas_Ref ObjectId="52904"/>
    <cge:TPSR_Ref TObjectID="9393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4146.000000 -880.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9394" ObjectName="SW-CX_CB.CX_CB_39017SW"/>
     <cge:Meas_Ref ObjectId="52907"/>
    <cge:TPSR_Ref TObjectID="9394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4860.000000 -1077.000000)" xlink:href="#switch2:shape19-UnNor1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52801">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 -957.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9376" ObjectName="SW-CX_CB.CX_CB_35267SW"/>
     <cge:Meas_Ref ObjectId="52801"/>
    <cge:TPSR_Ref TObjectID="9376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52800">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -908.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9375" ObjectName="SW-CX_CB.CX_CB_3526SW"/>
     <cge:Meas_Ref ObjectId="52800"/>
    <cge:TPSR_Ref TObjectID="9375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52802">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4776.000000 -814.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9377" ObjectName="SW-CX_CB.CX_CB_3521SW"/>
     <cge:Meas_Ref ObjectId="52802"/>
    <cge:TPSR_Ref TObjectID="9377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52865">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.707493 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9389" ObjectName="SW-CX_CB.CX_CB_3021SW"/>
     <cge:Meas_Ref ObjectId="52865"/>
    <cge:TPSR_Ref TObjectID="9389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52871">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.707493 -561.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9391" ObjectName="SW-CX_CB.CX_CB_4026SW"/>
     <cge:Meas_Ref ObjectId="52871"/>
    <cge:TPSR_Ref TObjectID="9391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4810.707493 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9392" ObjectName="SW-CX_CB.CX_CB_4021SW"/>
     <cge:Meas_Ref ObjectId="52872"/>
    <cge:TPSR_Ref TObjectID="9392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52912">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4647.900576 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9397" ObjectName="SW-CX_CB.CX_CB_4902SW"/>
     <cge:Meas_Ref ObjectId="52912"/>
    <cge:TPSR_Ref TObjectID="9397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52915">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4609.900576 -528.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9398" ObjectName="SW-CX_CB.CX_CB_49027SW"/>
     <cge:Meas_Ref ObjectId="52915"/>
    <cge:TPSR_Ref TObjectID="9398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52796">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.759366 -497.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9373" ObjectName="SW-CX_CB.CX_CB_4122SW"/>
     <cge:Meas_Ref ObjectId="52796"/>
    <cge:TPSR_Ref TObjectID="9373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52794">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.759366 -466.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9372" ObjectName="SW-CX_CB.CX_CB_4121SW"/>
     <cge:Meas_Ref ObjectId="52794"/>
    <cge:TPSR_Ref TObjectID="9372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4020.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9395" ObjectName="SW-CX_CB.CX_CB_4901SW"/>
     <cge:Meas_Ref ObjectId="52908"/>
    <cge:TPSR_Ref TObjectID="9395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52911">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3982.000000 -528.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9396" ObjectName="SW-CX_CB.CX_CB_49017SW"/>
     <cge:Meas_Ref ObjectId="52911"/>
    <cge:TPSR_Ref TObjectID="9396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52833">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -751.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9384" ObjectName="SW-CX_CB.CX_CB_3011SW"/>
     <cge:Meas_Ref ObjectId="52833"/>
    <cge:TPSR_Ref TObjectID="9384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52839">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -561.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9386" ObjectName="SW-CX_CB.CX_CB_4016SW"/>
     <cge:Meas_Ref ObjectId="52839"/>
    <cge:TPSR_Ref TObjectID="9386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52840">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3723.000000 -464.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9387" ObjectName="SW-CX_CB.CX_CB_4011SW"/>
     <cge:Meas_Ref ObjectId="52840"/>
    <cge:TPSR_Ref TObjectID="9387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9358" ObjectName="SW-CX_CB.CX_CB_4511SW"/>
     <cge:Meas_Ref ObjectId="52771"/>
    <cge:TPSR_Ref TObjectID="9358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52772">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3695.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9359" ObjectName="SW-CX_CB.CX_CB_45117SW"/>
     <cge:Meas_Ref ObjectId="52772"/>
    <cge:TPSR_Ref TObjectID="9359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9357" ObjectName="SW-CX_CB.CX_CB_4512SW"/>
     <cge:Meas_Ref ObjectId="52770"/>
    <cge:TPSR_Ref TObjectID="9357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52773">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9360" ObjectName="SW-CX_CB.CX_CB_4516SW"/>
     <cge:Meas_Ref ObjectId="52773"/>
    <cge:TPSR_Ref TObjectID="9360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.666667 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9353" ObjectName="SW-CX_CB.CX_CB_4521SW"/>
     <cge:Meas_Ref ObjectId="52763"/>
    <cge:TPSR_Ref TObjectID="9353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3885.666667 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9354" ObjectName="SW-CX_CB.CX_CB_45217SW"/>
     <cge:Meas_Ref ObjectId="52764"/>
    <cge:TPSR_Ref TObjectID="9354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.666667 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9352" ObjectName="SW-CX_CB.CX_CB_4522SW"/>
     <cge:Meas_Ref ObjectId="52762"/>
    <cge:TPSR_Ref TObjectID="9352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.666667 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9355" ObjectName="SW-CX_CB.CX_CB_4526SW"/>
     <cge:Meas_Ref ObjectId="52765"/>
    <cge:TPSR_Ref TObjectID="9355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.333333 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9348" ObjectName="SW-CX_CB.CX_CB_4531SW"/>
     <cge:Meas_Ref ObjectId="52755"/>
    <cge:TPSR_Ref TObjectID="9348"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.333333 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9349" ObjectName="SW-CX_CB.CX_CB_45317SW"/>
     <cge:Meas_Ref ObjectId="52756"/>
    <cge:TPSR_Ref TObjectID="9349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.333333 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9347" ObjectName="SW-CX_CB.CX_CB_4532SW"/>
     <cge:Meas_Ref ObjectId="52754"/>
    <cge:TPSR_Ref TObjectID="9347"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52757">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4032.333333 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9350" ObjectName="SW-CX_CB.CX_CB_4536SW"/>
     <cge:Meas_Ref ObjectId="52757"/>
    <cge:TPSR_Ref TObjectID="9350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52747">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9343" ObjectName="SW-CX_CB.CX_CB_4541SW"/>
     <cge:Meas_Ref ObjectId="52747"/>
    <cge:TPSR_Ref TObjectID="9343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52748">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9344" ObjectName="SW-CX_CB.CX_CB_45417SW"/>
     <cge:Meas_Ref ObjectId="52748"/>
    <cge:TPSR_Ref TObjectID="9344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52746">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9342" ObjectName="SW-CX_CB.CX_CB_4542SW"/>
     <cge:Meas_Ref ObjectId="52746"/>
    <cge:TPSR_Ref TObjectID="9342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4222.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9345" ObjectName="SW-CX_CB.CX_CB_4546SW"/>
     <cge:Meas_Ref ObjectId="52749"/>
    <cge:TPSR_Ref TObjectID="9345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52779">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9363" ObjectName="SW-CX_CB.CX_CB_4581SW"/>
     <cge:Meas_Ref ObjectId="52779"/>
    <cge:TPSR_Ref TObjectID="9363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52780">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5096.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9364" ObjectName="SW-CX_CB.CX_CB_45817SW"/>
     <cge:Meas_Ref ObjectId="52780"/>
    <cge:TPSR_Ref TObjectID="9364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9362" ObjectName="SW-CX_CB.CX_CB_4582SW"/>
     <cge:Meas_Ref ObjectId="52778"/>
    <cge:TPSR_Ref TObjectID="9362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5054.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9365" ObjectName="SW-CX_CB.CX_CB_4586SW"/>
     <cge:Meas_Ref ObjectId="52781"/>
    <cge:TPSR_Ref TObjectID="9365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.666667 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9368" ObjectName="SW-CX_CB.CX_CB_4571SW"/>
     <cge:Meas_Ref ObjectId="52787"/>
    <cge:TPSR_Ref TObjectID="9368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52788">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4907.666667 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9369" ObjectName="SW-CX_CB.CX_CB_45717SW"/>
     <cge:Meas_Ref ObjectId="52788"/>
    <cge:TPSR_Ref TObjectID="9369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.666667 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9367" ObjectName="SW-CX_CB.CX_CB_4572SW"/>
     <cge:Meas_Ref ObjectId="52786"/>
    <cge:TPSR_Ref TObjectID="9367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52789">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4865.666667 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9370" ObjectName="SW-CX_CB.CX_CB_4576SW"/>
     <cge:Meas_Ref ObjectId="52789"/>
    <cge:TPSR_Ref TObjectID="9370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52731">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.333333 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9333" ObjectName="SW-CX_CB.CX_CB_4561SW"/>
     <cge:Meas_Ref ObjectId="52731"/>
    <cge:TPSR_Ref TObjectID="9333"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4716.333333 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9334" ObjectName="SW-CX_CB.CX_CB_45617SW"/>
     <cge:Meas_Ref ObjectId="52732"/>
    <cge:TPSR_Ref TObjectID="9334"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.333333 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9332" ObjectName="SW-CX_CB.CX_CB_4562SW"/>
     <cge:Meas_Ref ObjectId="52730"/>
    <cge:TPSR_Ref TObjectID="9332"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4674.333333 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9335" ObjectName="SW-CX_CB.CX_CB_4566SW"/>
     <cge:Meas_Ref ObjectId="52733"/>
    <cge:TPSR_Ref TObjectID="9335"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52739">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9338" ObjectName="SW-CX_CB.CX_CB_4551SW"/>
     <cge:Meas_Ref ObjectId="52739"/>
    <cge:TPSR_Ref TObjectID="9338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -335.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9339" ObjectName="SW-CX_CB.CX_CB_45517SW"/>
     <cge:Meas_Ref ObjectId="52740"/>
    <cge:TPSR_Ref TObjectID="9339"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52738">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9337" ObjectName="SW-CX_CB.CX_CB_4552SW"/>
     <cge:Meas_Ref ObjectId="52738"/>
    <cge:TPSR_Ref TObjectID="9337"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52741">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4485.000000 -103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9340" ObjectName="SW-CX_CB.CX_CB_4556SW"/>
     <cge:Meas_Ref ObjectId="52741"/>
    <cge:TPSR_Ref TObjectID="9340"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1082 3737,-1120 " stroke-width="0.5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3737,-1082 3737,-1120 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2635f70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3795.000000 -973.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_263f110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -948.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_260d900">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -973.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25df2c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4615.900576 -596.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2597b80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 -596.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2600c80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3724.000000 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b7910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.666667 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25684a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.333333 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257c180">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.000000 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27667d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5125.000000 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_277bc30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.666667 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2790090">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4745.333333 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a4e60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -351.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_265c200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1050 3728,-1050 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="9381@x" ObjectIDND2="9380@x" ObjectIDZND0="g_23d2400@0" Pin0InfoVect0LinkObjId="g_23d2400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="SW-52817_0" Pin1InfoVect2LinkObjId="SW-52816_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1050 3728,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_25f1310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1082 3818,-1082 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_25f1500@0" Pin0InfoVect0LinkObjId="g_25f1500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1082 3818,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26717e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1026 3756,-1026 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_23d2400@0" ObjectIDND1="0@x" ObjectIDND2="9381@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23d2400_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-52817_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1026 3756,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26719d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-1026 3818,-1026 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-1026 3818,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2611150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1026 3737,-1050 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="9381@x" ObjectIDND2="9380@x" ObjectIDZND0="g_23d2400@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_23d2400_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-52817_0" Pin1InfoVect2LinkObjId="SW-52816_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1026 3737,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26352b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-983 3754,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_23d2400@0" ObjectIDND2="0@x" ObjectIDZND0="9381@0" Pin0InfoVect0LinkObjId="SW-52817_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_23d2400_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-983 3754,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26354a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3790,-983 3800,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9381@1" ObjectIDZND0="g_2635f70@0" Pin0InfoVect0LinkObjId="g_2635f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52817_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3790,-983 3800,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2635d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-983 3737,-1026 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9381@x" ObjectIDND1="9380@x" ObjectIDZND0="0@x" ObjectIDZND1="g_23d2400@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23d2400_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52817_0" Pin1InfoVect1LinkObjId="SW-52816_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-983 3737,-1026 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2638b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-966 3737,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="9380@1" ObjectIDZND0="0@x" ObjectIDZND1="g_23d2400@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_23d2400_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-966 3737,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-914 3737,-930 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9379@1" ObjectIDZND0="9380@0" Pin0InfoVect0LinkObjId="SW-52816_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52815_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-914 3737,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263c0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-821 3737,-836 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9382@0" Pin0InfoVect0LinkObjId="SW-52818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-821 3737,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-872 3737,-887 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9382@1" ObjectIDZND0="9379@0" Pin0InfoVect0LinkObjId="SW-52815_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52818_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-872 3737,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263c6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-821 4199,-841 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9393@0" Pin0InfoVect0LinkObjId="SW-52904_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-821 4199,-841 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-877 4199,-893 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="9393@1" ObjectIDZND0="9394@x" ObjectIDZND1="g_263f8c0@0" ObjectIDZND2="g_25a8c90@0" Pin0InfoVect0LinkObjId="SW-52907_0" Pin0InfoVect1LinkObjId="g_263f8c0_0" Pin0InfoVect2LinkObjId="g_25a8c90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52904_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-877 4199,-893 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263ed30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-893 4161,-893 4161,-902 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9393@x" ObjectIDND1="g_263f8c0@0" ObjectIDND2="g_25a8c90@0" ObjectIDZND0="9394@0" Pin0InfoVect0LinkObjId="SW-52907_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="g_263f8c0_0" Pin1InfoVect2LinkObjId="g_25a8c90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-893 4161,-893 4161,-902 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263ef20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-938 4161,-953 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9394@1" ObjectIDZND0="g_263f110@0" Pin0InfoVect0LinkObjId="g_263f110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52907_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-938 4161,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_263feb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-952 4199,-978 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_263f8c0@1" ObjectIDZND0="g_25a96a0@0" Pin0InfoVect0LinkObjId="g_25a96a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_263f8c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-952 4199,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26400a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-909 4234,-909 4234,-931 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDND2="g_263f8c0@0" ObjectIDZND0="g_25a8c90@0" Pin0InfoVect0LinkObjId="g_25a8c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="g_263f8c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-909 4234,-909 4234,-931 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a88b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-893 4199,-909 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDZND0="g_263f8c0@0" ObjectIDZND1="g_25a8c90@0" Pin0InfoVect0LinkObjId="g_263f8c0_0" Pin0InfoVect1LinkObjId="g_25a8c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-893 4199,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a8aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4199,-909 4199,-921 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9393@x" ObjectIDND1="9394@x" ObjectIDND2="g_25a8c90@0" ObjectIDZND0="g_263f8c0@0" Pin0InfoVect0LinkObjId="g_263f8c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52904_0" Pin1InfoVect1LinkObjId="SW-52907_0" Pin1InfoVect2LinkObjId="g_25a8c90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4199,-909 4199,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25aabc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1050 4782,-1050 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="9376@x" ObjectIDND2="9375@x" ObjectIDZND0="g_25aadb0@0" Pin0InfoVect0LinkObjId="g_25aadb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-52801_0" Pin1InfoVect2LinkObjId="SW-52800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1050 4782,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1121 4791,-1082 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_25aadb0@0" ObjectIDZND1="9376@x" ObjectIDZND2="9375@x" Pin0InfoVect0LinkObjId="g_25aadb0_0" Pin0InfoVect1LinkObjId="SW-52801_0" Pin0InfoVect2LinkObjId="SW-52800_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1121 4791,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25ab970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1082 4791,-1050 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@x" ObjectIDZND0="g_25aadb0@0" ObjectIDZND1="9376@x" ObjectIDZND2="9375@x" Pin0InfoVect0LinkObjId="g_25aadb0_0" Pin0InfoVect1LinkObjId="SW-52801_0" Pin0InfoVect2LinkObjId="SW-52800_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1082 4791,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1082 4810,-1082 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25aadb0@0" ObjectIDND1="9376@x" ObjectIDND2="9375@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25aadb0_0" Pin1InfoVect1LinkObjId="SW-52801_0" Pin1InfoVect2LinkObjId="SW-52800_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1082 4810,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_260b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4855,-1082 4872,-1082 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_260bbe0@0" Pin0InfoVect0LinkObjId="g_260bbe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4855,-1082 4872,-1082 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260d4c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-983 4808,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9375@x" ObjectIDND1="0@x" ObjectIDND2="g_25aadb0@0" ObjectIDZND0="9376@0" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52800_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_25aadb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-983 4808,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_260d6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4844,-983 4854,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9376@1" ObjectIDZND0="g_260d900@0" Pin0InfoVect0LinkObjId="g_260d900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52801_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4844,-983 4854,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2663680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-966 4791,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="9375@1" ObjectIDZND0="9376@x" ObjectIDZND1="0@x" ObjectIDZND2="g_25aadb0@0" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_25aadb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52800_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-966 4791,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2665590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-914 4791,-930 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9374@1" ObjectIDZND0="9375@0" Pin0InfoVect0LinkObjId="SW-52800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52799_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-914 4791,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2667ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-821 4791,-836 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9377@0" Pin0InfoVect0LinkObjId="SW-52802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-821 4791,-836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2675300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-872 4791,-887 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9377@1" ObjectIDZND0="9374@0" Pin0InfoVect0LinkObjId="SW-52799_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52802_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-872 4791,-887 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2676e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4791,-1050 4791,-983 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_25aadb0@0" ObjectIDZND0="9376@x" ObjectIDZND1="9375@x" Pin0InfoVect0LinkObjId="SW-52801_0" Pin0InfoVect1LinkObjId="SW-52800_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_25aadb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4791,-1050 4791,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2679aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-821 4826,-809 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9389@1" Pin0InfoVect0LinkObjId="SW-52865_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-821 4826,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2634910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-635 4826,-619 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="9400@0" ObjectIDZND0="9391@1" Pin0InfoVect0LinkObjId="SW-52871_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-635 4826,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2634b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-583 4826,-564 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9391@0" ObjectIDZND0="9390@1" Pin0InfoVect0LinkObjId="SW-52868_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52871_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-583 4826,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2590eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-537 4826,-522 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9390@0" ObjectIDZND0="9392@1" Pin0InfoVect0LinkObjId="SW-52872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52868_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-537 4826,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2591110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-486 4826,-471 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9392@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_2622ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-486 4826,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2593a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-471 4663,-489 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDZND0="9397@0" Pin0InfoVect0LinkObjId="SW-52912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-471 4663,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2593c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-525 4663,-541 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="9397@1" ObjectIDZND0="9398@x" ObjectIDZND1="g_25e0e30@0" ObjectIDZND2="g_25dfcd0@0" Pin0InfoVect0LinkObjId="SW-52915_0" Pin0InfoVect1LinkObjId="g_25e0e30_0" Pin0InfoVect2LinkObjId="g_25dfcd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-525 4663,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25dee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-541 4625,-541 4625,-550 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9397@x" ObjectIDND1="g_25e0e30@0" ObjectIDND2="g_25dfcd0@0" ObjectIDZND0="9398@0" Pin0InfoVect0LinkObjId="SW-52915_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52912_0" Pin1InfoVect1LinkObjId="g_25e0e30_0" Pin1InfoVect2LinkObjId="g_25dfcd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-541 4625,-541 4625,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25df060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4625,-586 4625,-601 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9398@1" ObjectIDZND0="g_25df2c0@0" Pin0InfoVect0LinkObjId="g_25df2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52915_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4625,-586 4625,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e04b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-600 4663,-626 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25dfcd0@1" ObjectIDZND0="g_25e1b00@0" Pin0InfoVect0LinkObjId="g_25e1b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25dfcd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-600 4663,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e0710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-557 4698,-557 4698,-579 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9398@x" ObjectIDND1="9397@x" ObjectIDND2="g_25dfcd0@0" ObjectIDZND0="g_25e0e30@0" Pin0InfoVect0LinkObjId="g_25e0e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52915_0" Pin1InfoVect1LinkObjId="SW-52912_0" Pin1InfoVect2LinkObjId="g_25dfcd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-557 4698,-557 4698,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e0970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-541 4663,-557 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9398@x" ObjectIDND1="9397@x" ObjectIDZND0="g_25e0e30@0" ObjectIDZND1="g_25dfcd0@0" Pin0InfoVect0LinkObjId="g_25e0e30_0" Pin0InfoVect1LinkObjId="g_25dfcd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52915_0" Pin1InfoVect1LinkObjId="SW-52912_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-541 4663,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25e0bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4663,-557 4663,-569 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9398@x" ObjectIDND1="9397@x" ObjectIDND2="g_25e0e30@0" ObjectIDZND0="g_25dfcd0@0" Pin0InfoVect0LinkObjId="g_25dfcd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52915_0" Pin1InfoVect1LinkObjId="SW-52912_0" Pin1InfoVect2LinkObjId="g_25e0e30_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4663,-557 4663,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2622ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4455,-519 4455,-471 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9373@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_2591110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52796_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4455,-519 4455,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2622d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-570 4294,-590 4455,-590 4455,-555 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9371@1" ObjectIDZND0="9373@1" Pin0InfoVect0LinkObjId="SW-52796_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52792_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-570 4294,-590 4455,-590 4455,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2625630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-471 4294,-488 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDZND0="9372@0" Pin0InfoVect0LinkObjId="SW-52794_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-471 4294,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2625890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-524 4294,-543 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9372@1" ObjectIDZND0="9371@0" Pin0InfoVect0LinkObjId="SW-52792_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52794_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-524 4294,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26281d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-471 4035,-489 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDZND0="9395@0" Pin0InfoVect0LinkObjId="SW-52908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-471 4035,-489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2628430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-525 4035,-541 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="9395@1" ObjectIDZND0="9396@x" ObjectIDZND1="g_25991f0@0" ObjectIDZND2="g_2598570@0" Pin0InfoVect0LinkObjId="SW-52911_0" Pin0InfoVect1LinkObjId="g_25991f0_0" Pin0InfoVect2LinkObjId="g_2598570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-525 4035,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25976c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-541 3997,-541 3997,-550 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="9395@x" ObjectIDND1="g_25991f0@0" ObjectIDND2="g_2598570@0" ObjectIDZND0="9396@0" Pin0InfoVect0LinkObjId="SW-52911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52908_0" Pin1InfoVect1LinkObjId="g_25991f0_0" Pin1InfoVect2LinkObjId="g_2598570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-541 3997,-541 3997,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2597920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3997,-586 3997,-601 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9396@1" ObjectIDZND0="g_2597b80@0" Pin0InfoVect0LinkObjId="g_2597b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52911_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3997,-586 3997,-601 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2598d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-557 4070,-557 4070,-579 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9396@x" ObjectIDND1="9395@x" ObjectIDND2="g_2598570@0" ObjectIDZND0="g_25991f0@0" Pin0InfoVect0LinkObjId="g_25991f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52911_0" Pin1InfoVect1LinkObjId="SW-52908_0" Pin1InfoVect2LinkObjId="g_2598570_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-557 4070,-557 4070,-579 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2598f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-541 4035,-557 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9396@x" ObjectIDND1="9395@x" ObjectIDZND0="g_25991f0@0" ObjectIDZND1="g_2598570@0" Pin0InfoVect0LinkObjId="g_25991f0_0" Pin0InfoVect1LinkObjId="g_2598570_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52911_0" Pin1InfoVect1LinkObjId="SW-52908_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-541 4035,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a17e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-821 3738,-809 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="9328@0" ObjectIDZND0="9384@1" Pin0InfoVect0LinkObjId="SW-52833_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-821 3738,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-635 3738,-619 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="9399@0" ObjectIDZND0="9386@1" Pin0InfoVect0LinkObjId="SW-52839_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-635 3738,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e5c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-583 3738,-564 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="9386@0" ObjectIDZND0="9385@1" Pin0InfoVect0LinkObjId="SW-52836_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52839_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-583 3738,-564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e87a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-537 3738,-522 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9385@0" ObjectIDZND0="9387@1" Pin0InfoVect0LinkObjId="SW-52840_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52836_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-537 3738,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e8a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-486 3738,-471 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="9387@0" ObjectIDZND0="9329@0" Pin0InfoVect0LinkObjId="g_27c8e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52840_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-486 3738,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e9800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-715 3738,-730 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9399@1" ObjectIDZND0="9383@0" Pin0InfoVect0LinkObjId="SW-52830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-715 3738,-730 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e9a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3738,-757 3738,-773 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9383@1" ObjectIDZND0="9384@0" Pin0InfoVect0LinkObjId="SW-52833_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52830_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3738,-757 3738,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e9cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-715 4826,-729 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="9400@1" ObjectIDZND0="9388@0" Pin0InfoVect0LinkObjId="SW-52862_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-715 4826,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26e9f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-756 4826,-773 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="9388@1" ObjectIDZND0="9389@0" Pin0InfoVect0LinkObjId="SW-52865_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52862_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-756 4826,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ea180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-427 3680,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9358@x" ObjectIDZND0="g_26ea640@0" Pin0InfoVect0LinkObjId="g_26ea640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="SW-52771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-427 3680,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ea3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-444 3668,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_26ea640@0" ObjectIDZND1="9358@x" Pin0InfoVect0LinkObjId="g_26ea640_0" Pin0InfoVect1LinkObjId="SW-52771_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-444 3668,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fd850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-427 3668,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_26ea640@0" ObjectIDZND0="9358@1" Pin0InfoVect0LinkObjId="SW-52771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="g_26ea640_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-427 3668,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fdab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-374 3668,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9358@0" ObjectIDZND0="9359@x" ObjectIDZND1="9356@0" Pin0InfoVect0LinkObjId="SW-52772_0" Pin0InfoVect1LinkObjId="SW-52768_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52771_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-374 3668,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26007c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-361 3686,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9358@x" ObjectIDND1="9356@0" ObjectIDZND0="9359@0" Pin0InfoVect0LinkObjId="SW-52772_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52771_0" Pin1InfoVect1LinkObjId="SW-52768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-361 3686,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2600a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3722,-361 3729,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9359@1" ObjectIDZND0="g_2600c80@0" Pin0InfoVect0LinkObjId="g_2600c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52772_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3722,-361 3729,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26037e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-361 3668,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9359@x" ObjectIDND1="9358@x" ObjectIDZND0="9356@1" Pin0InfoVect0LinkObjId="SW-52768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52772_0" Pin1InfoVect1LinkObjId="SW-52771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-361 3668,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2603a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-303 3680,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9356@x" ObjectIDND1="9357@x" ObjectIDZND0="g_2603ca0@0" Pin0InfoVect0LinkObjId="g_2603ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="SW-52770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-303 3680,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2604a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-318 3668,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9356@0" ObjectIDZND0="g_2603ca0@0" ObjectIDZND1="9357@x" Pin0InfoVect0LinkObjId="g_2603ca0_0" Pin0InfoVect1LinkObjId="SW-52770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-318 3668,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2607560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-303 3668,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9356@x" ObjectIDND1="g_2603ca0@0" ObjectIDZND0="9357@1" Pin0InfoVect0LinkObjId="SW-52770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52768_0" Pin1InfoVect1LinkObjId="g_2603ca0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-303 3668,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260adc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-174 3668,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_26077c0@1" ObjectIDZND0="9360@1" Pin0InfoVect0LinkObjId="SW-52773_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26077c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-174 3668,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_260b020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-113 3699,-113 3699,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9360@x" ObjectIDZND0="g_25ae280@0" Pin0InfoVect0LinkObjId="g_25ae280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-113 3699,-113 3699,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25addc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-125 3668,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9360@0" ObjectIDZND0="g_25ae280@0" Pin0InfoVect0LinkObjId="g_25ae280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52773_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-125 3668,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ae020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-113 3668,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9360@x" ObjectIDND1="g_25ae280@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52773_0" Pin1InfoVect1LinkObjId="g_25ae280_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-113 3668,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26120b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-240 3653,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9357@x" ObjectIDND1="g_26077c0@0" ObjectIDZND0="g_25afde0@0" Pin0InfoVect0LinkObjId="g_25afde0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52770_0" Pin1InfoVect1LinkObjId="g_26077c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-240 3653,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26122a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-251 3668,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9357@0" ObjectIDZND0="g_25afde0@0" ObjectIDZND1="g_26077c0@0" Pin0InfoVect0LinkObjId="g_25afde0_0" Pin0InfoVect1LinkObjId="g_26077c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-251 3668,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25afbf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3668,-240 3668,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9357@x" ObjectIDND1="g_25afde0@0" ObjectIDZND0="g_26077c0@0" Pin0InfoVect0LinkObjId="g_26077c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52770_0" Pin1InfoVect1LinkObjId="g_25afde0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3668,-240 3668,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b1240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-427 3870,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9353@x" ObjectIDZND0="g_25b1620@0" Pin0InfoVect0LinkObjId="g_25b1620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="SW-52763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-427 3870,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b1430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-444 3858,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_25b1620@0" ObjectIDZND1="9353@x" Pin0InfoVect0LinkObjId="g_25b1620_0" Pin0InfoVect1LinkObjId="SW-52763_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-444 3858,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b47e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-427 3858,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_25b1620@0" ObjectIDZND0="9353@1" Pin0InfoVect0LinkObjId="SW-52763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="g_25b1620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-427 3858,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b4a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-374 3858,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9353@0" ObjectIDZND0="9354@x" ObjectIDZND1="9351@x" Pin0InfoVect0LinkObjId="SW-52764_0" Pin0InfoVect1LinkObjId="SW-52760_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-374 3858,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b7450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-361 3876,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9353@x" ObjectIDND1="9351@x" ObjectIDZND0="9354@0" Pin0InfoVect0LinkObjId="SW-52764_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52763_0" Pin1InfoVect1LinkObjId="SW-52760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-361 3876,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b76b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-361 3919,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9354@1" ObjectIDZND0="g_25b7910@0" Pin0InfoVect0LinkObjId="g_25b7910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52764_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-361 3919,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25b9f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-361 3858,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9354@x" ObjectIDND1="9353@x" ObjectIDZND0="9351@1" Pin0InfoVect0LinkObjId="SW-52760_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52764_0" Pin1InfoVect1LinkObjId="SW-52763_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-361 3858,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25ba170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-303 3870,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9351@x" ObjectIDND1="9352@x" ObjectIDZND0="g_25ba3d0@0" Pin0InfoVect0LinkObjId="g_25ba3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="SW-52762_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-303 3870,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bb080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-318 3858,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9351@0" ObjectIDZND0="g_25ba3d0@0" ObjectIDZND1="9352@x" Pin0InfoVect0LinkObjId="g_25ba3d0_0" Pin0InfoVect1LinkObjId="SW-52762_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-318 3858,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25bd900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-303 3858,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9351@x" ObjectIDND1="g_25ba3d0@0" ObjectIDZND0="9352@1" Pin0InfoVect0LinkObjId="SW-52762_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52760_0" Pin1InfoVect1LinkObjId="g_25ba3d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-303 3858,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-174 3858,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_25bdb60@1" ObjectIDZND0="9355@1" Pin0InfoVect0LinkObjId="SW-52765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25bdb60_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-174 3858,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-113 3889,-113 3889,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9355@x" ObjectIDZND0="g_25c1730@0" Pin0InfoVect0LinkObjId="g_25c1730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-113 3889,-113 3889,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c1270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-125 3858,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9355@0" ObjectIDZND0="g_25c1730@0" Pin0InfoVect0LinkObjId="g_25c1730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-125 3858,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c14d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-113 3858,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9355@x" ObjectIDND1="g_25c1730@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52765_0" Pin1InfoVect1LinkObjId="g_25c1730_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-113 3858,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c2f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-240 3843,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9352@x" ObjectIDND1="g_25bdb60@0" ObjectIDZND0="g_25c3500@0" Pin0InfoVect0LinkObjId="g_25c3500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52762_0" Pin1InfoVect1LinkObjId="g_25bdb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-240 3843,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c3120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-251 3858,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9352@0" ObjectIDZND0="g_25c3500@0" ObjectIDZND1="g_25bdb60@0" Pin0InfoVect0LinkObjId="g_25c3500_0" Pin0InfoVect1LinkObjId="g_25bdb60_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-251 3858,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c3310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-240 3858,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9352@x" ObjectIDND1="g_25c3500@0" ObjectIDZND0="g_25bdb60@0" Pin0InfoVect0LinkObjId="g_25bdb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52762_0" Pin1InfoVect1LinkObjId="g_25c3500_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-240 3858,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2561fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-427 4059,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9348@x" ObjectIDZND0="g_25623a0@0" Pin0InfoVect0LinkObjId="g_25623a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="SW-52755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-427 4059,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25621b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-444 4047,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_25623a0@0" ObjectIDZND1="9348@x" Pin0InfoVect0LinkObjId="g_25623a0_0" Pin0InfoVect1LinkObjId="SW-52755_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-444 4047,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2565370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-427 4047,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_25623a0@0" ObjectIDZND0="9348@1" Pin0InfoVect0LinkObjId="SW-52755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="g_25623a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-427 4047,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25655d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-374 4047,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9348@0" ObjectIDZND0="9349@x" ObjectIDZND1="9346@x" Pin0InfoVect0LinkObjId="SW-52756_0" Pin0InfoVect1LinkObjId="SW-52752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-374 4047,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2567fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-361 4065,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9348@x" ObjectIDND1="9346@x" ObjectIDZND0="9349@0" Pin0InfoVect0LinkObjId="SW-52756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52755_0" Pin1InfoVect1LinkObjId="SW-52752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-361 4065,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2568240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4101,-361 4108,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9349@1" ObjectIDZND0="g_25684a0@0" Pin0InfoVect0LinkObjId="g_25684a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4101,-361 4108,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-361 4047,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9349@x" ObjectIDND1="9348@x" ObjectIDZND0="9346@1" Pin0InfoVect0LinkObjId="SW-52752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52756_0" Pin1InfoVect1LinkObjId="SW-52755_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-361 4047,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256ad00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-303 4059,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9346@x" ObjectIDND1="9347@x" ObjectIDZND0="g_256af60@0" Pin0InfoVect0LinkObjId="g_256af60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="SW-52754_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-303 4059,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-318 4047,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9346@0" ObjectIDZND0="g_256af60@0" ObjectIDZND1="9347@x" Pin0InfoVect0LinkObjId="g_256af60_0" Pin0InfoVect1LinkObjId="SW-52754_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-318 4047,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_256e490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-303 4047,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9346@x" ObjectIDND1="g_256af60@0" ObjectIDZND0="9347@1" Pin0InfoVect0LinkObjId="SW-52754_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52752_0" Pin1InfoVect1LinkObjId="g_256af60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-303 4047,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2571b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-174 4047,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_256e6f0@1" ObjectIDZND0="9350@1" Pin0InfoVect0LinkObjId="SW-52757_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_256e6f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-174 4047,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2571d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-113 4078,-113 4078,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9350@x" ObjectIDZND0="g_25724b0@0" Pin0InfoVect0LinkObjId="g_25724b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-113 4078,-113 4078,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2571ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-125 4047,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9350@0" ObjectIDZND0="g_25724b0@0" Pin0InfoVect0LinkObjId="g_25724b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52757_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-125 4047,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2572250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-113 4047,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9350@x" ObjectIDND1="g_25724b0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52757_0" Pin1InfoVect1LinkObjId="g_25724b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-113 4047,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2573f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-240 4032,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9347@x" ObjectIDND1="g_256e6f0@0" ObjectIDZND0="g_2574520@0" Pin0InfoVect0LinkObjId="g_2574520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52754_0" Pin1InfoVect1LinkObjId="g_256e6f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-240 4032,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2574120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-251 4047,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9347@0" ObjectIDZND0="g_2574520@0" ObjectIDZND1="g_256e6f0@0" Pin0InfoVect0LinkObjId="g_2574520_0" Pin0InfoVect1LinkObjId="g_256e6f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52754_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-251 4047,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2574310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4047,-240 4047,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9347@x" ObjectIDND1="g_2574520@0" ObjectIDZND0="g_256e6f0@0" Pin0InfoVect0LinkObjId="g_256e6f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52754_0" Pin1InfoVect1LinkObjId="g_2574520_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4047,-240 4047,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2575610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-427 4249,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9329@0" ObjectIDND1="9343@x" ObjectIDZND0="g_25759f0@0" Pin0InfoVect0LinkObjId="g_25759f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="SW-52747_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-427 4249,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2575800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-444 4237,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9329@0" ObjectIDZND0="g_25759f0@0" ObjectIDZND1="9343@x" Pin0InfoVect0LinkObjId="g_25759f0_0" Pin0InfoVect1LinkObjId="SW-52747_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-444 4237,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2578d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-427 4237,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9329@0" ObjectIDND1="g_25759f0@0" ObjectIDZND0="9343@1" Pin0InfoVect0LinkObjId="SW-52747_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="g_25759f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-427 4237,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2578fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-374 4237,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9343@0" ObjectIDZND0="9344@x" ObjectIDZND1="9341@x" Pin0InfoVect0LinkObjId="SW-52748_0" Pin0InfoVect1LinkObjId="SW-52744_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52747_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-374 4237,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-361 4255,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9343@x" ObjectIDND1="9341@x" ObjectIDZND0="9344@0" Pin0InfoVect0LinkObjId="SW-52748_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52747_0" Pin1InfoVect1LinkObjId="SW-52744_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-361 4255,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257bf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4291,-361 4298,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9344@1" ObjectIDZND0="g_257c180@0" Pin0InfoVect0LinkObjId="g_257c180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52748_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4291,-361 4298,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-361 4237,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9344@x" ObjectIDND1="9343@x" ObjectIDZND0="9341@1" Pin0InfoVect0LinkObjId="SW-52744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52748_0" Pin1InfoVect1LinkObjId="SW-52747_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-361 4237,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-303 4249,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9341@x" ObjectIDND1="9342@x" ObjectIDZND0="g_257f1a0@0" Pin0InfoVect0LinkObjId="g_257f1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="SW-52746_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-303 4249,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_257ff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-318 4237,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9341@0" ObjectIDZND0="g_257f1a0@0" ObjectIDZND1="9342@x" Pin0InfoVect0LinkObjId="g_257f1a0_0" Pin0InfoVect1LinkObjId="SW-52746_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-318 4237,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2582a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-303 4237,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9341@x" ObjectIDND1="g_257f1a0@0" ObjectIDZND0="9342@1" Pin0InfoVect0LinkObjId="SW-52746_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52744_0" Pin1InfoVect1LinkObjId="g_257f1a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-303 4237,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25862c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-174 4237,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2582cc0@1" ObjectIDZND0="9345@1" Pin0InfoVect0LinkObjId="SW-52749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2582cc0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-174 4237,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2586520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-113 4268,-113 4268,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9345@x" ObjectIDZND0="g_2586c40@0" Pin0InfoVect0LinkObjId="g_2586c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-113 4268,-113 4268,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2586780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-125 4237,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9345@0" ObjectIDZND0="g_2586c40@0" Pin0InfoVect0LinkObjId="g_2586c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-125 4237,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25869e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-113 4237,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9345@x" ObjectIDND1="g_2586c40@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52749_0" Pin1InfoVect1LinkObjId="g_2586c40_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-113 4237,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2588020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-240 4222,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9342@x" ObjectIDND1="g_2582cc0@0" ObjectIDZND0="g_25885f0@0" Pin0InfoVect0LinkObjId="g_25885f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52746_0" Pin1InfoVect1LinkObjId="g_2582cc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-240 4222,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2588210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-251 4237,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9342@0" ObjectIDZND0="g_25885f0@0" ObjectIDZND1="g_2582cc0@0" Pin0InfoVect0LinkObjId="g_25885f0_0" Pin0InfoVect1LinkObjId="g_2582cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52746_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-251 4237,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2588400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4237,-240 4237,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9342@x" ObjectIDND1="g_25885f0@0" ObjectIDZND0="g_2582cc0@0" Pin0InfoVect0LinkObjId="g_2582cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52746_0" Pin1InfoVect1LinkObjId="g_25885f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4237,-240 4237,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2589730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-427 5081,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9363@x" ObjectIDZND0="g_2589b10@0" Pin0InfoVect0LinkObjId="g_2589b10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="SW-52779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-427 5081,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2589920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-444 5069,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_2589b10@0" ObjectIDZND1="9363@x" Pin0InfoVect0LinkObjId="g_2589b10_0" Pin0InfoVect1LinkObjId="SW-52779_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-444 5069,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258cfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-427 5069,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_2589b10@0" ObjectIDZND0="9363@1" Pin0InfoVect0LinkObjId="SW-52779_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="g_2589b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-427 5069,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258d200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-374 5069,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9363@0" ObjectIDZND0="9364@x" ObjectIDZND1="9361@x" Pin0InfoVect0LinkObjId="SW-52780_0" Pin0InfoVect1LinkObjId="SW-52776_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52779_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-374 5069,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2766310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-361 5087,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9363@x" ObjectIDND1="9361@x" ObjectIDZND0="9364@0" Pin0InfoVect0LinkObjId="SW-52780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52779_0" Pin1InfoVect1LinkObjId="SW-52776_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-361 5087,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2766570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5123,-361 5130,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9364@1" ObjectIDZND0="g_27667d0@0" Pin0InfoVect0LinkObjId="g_27667d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52780_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5123,-361 5130,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2769330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-361 5069,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9364@x" ObjectIDND1="9363@x" ObjectIDZND0="9361@1" Pin0InfoVect0LinkObjId="SW-52776_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52780_0" Pin1InfoVect1LinkObjId="SW-52779_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-361 5069,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2769590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-303 5081,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9361@x" ObjectIDND1="9362@x" ObjectIDZND0="g_27697f0@0" Pin0InfoVect0LinkObjId="g_27697f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="SW-52778_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-303 5081,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_276a5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-318 5069,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9361@0" ObjectIDZND0="g_27697f0@0" ObjectIDZND1="9362@x" Pin0InfoVect0LinkObjId="g_27697f0_0" Pin0InfoVect1LinkObjId="SW-52778_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-318 5069,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_276d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-303 5069,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9361@x" ObjectIDND1="g_27697f0@0" ObjectIDZND0="9362@1" Pin0InfoVect0LinkObjId="SW-52778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52776_0" Pin1InfoVect1LinkObjId="g_27697f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-303 5069,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2770910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-174 5069,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_276d310@1" ObjectIDZND0="9365@1" Pin0InfoVect0LinkObjId="SW-52781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_276d310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-174 5069,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2770b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-113 5100,-113 5100,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9365@x" ObjectIDZND0="g_2771290@0" Pin0InfoVect0LinkObjId="g_2771290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-113 5100,-113 5100,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2770dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-125 5069,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9365@0" ObjectIDZND0="g_2771290@0" Pin0InfoVect0LinkObjId="g_2771290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-125 5069,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2771030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-113 5069,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9365@x" ObjectIDND1="g_2771290@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52781_0" Pin1InfoVect1LinkObjId="g_2771290_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-113 5069,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25af3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-240 5054,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9362@x" ObjectIDND1="g_276d310@0" ObjectIDZND0="g_25af970@0" Pin0InfoVect0LinkObjId="g_25af970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52778_0" Pin1InfoVect1LinkObjId="g_276d310_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-240 5054,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25af590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-251 5069,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9362@0" ObjectIDZND0="g_25af970@0" ObjectIDZND1="g_276d310@0" Pin0InfoVect0LinkObjId="g_25af970_0" Pin0InfoVect1LinkObjId="g_276d310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-251 5069,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25af780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5069,-240 5069,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9362@x" ObjectIDND1="g_25af970@0" ObjectIDZND0="g_276d310@0" Pin0InfoVect0LinkObjId="g_276d310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52778_0" Pin1InfoVect1LinkObjId="g_25af970_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5069,-240 5069,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-427 4892,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9368@x" ObjectIDZND0="g_2775560@0" Pin0InfoVect0LinkObjId="g_2775560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="SW-52787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-427 4892,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2775370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-444 4880,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_2775560@0" ObjectIDZND1="9368@x" Pin0InfoVect0LinkObjId="g_2775560_0" Pin0InfoVect1LinkObjId="SW-52787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-444 4880,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2778800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-427 4880,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_2775560@0" ObjectIDZND0="9368@1" Pin0InfoVect0LinkObjId="SW-52787_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="g_2775560_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-427 4880,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2778a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-374 4880,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9368@0" ObjectIDZND0="9369@x" ObjectIDZND1="9366@x" Pin0InfoVect0LinkObjId="SW-52788_0" Pin0InfoVect1LinkObjId="SW-52784_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52787_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-374 4880,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277b770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-361 4898,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9368@x" ObjectIDND1="9366@x" ObjectIDZND0="9369@0" Pin0InfoVect0LinkObjId="SW-52788_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52787_0" Pin1InfoVect1LinkObjId="SW-52784_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-361 4898,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277b9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4934,-361 4941,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9369@1" ObjectIDZND0="g_277bc30@0" Pin0InfoVect0LinkObjId="g_277bc30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52788_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4934,-361 4941,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277e790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-361 4880,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9369@x" ObjectIDND1="9368@x" ObjectIDZND0="9366@1" Pin0InfoVect0LinkObjId="SW-52784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52788_0" Pin1InfoVect1LinkObjId="SW-52787_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-361 4880,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-303 4892,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9366@x" ObjectIDND1="9367@x" ObjectIDZND0="g_277ec50@0" Pin0InfoVect0LinkObjId="g_277ec50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="SW-52786_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-303 4892,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_277fa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-318 4880,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9366@0" ObjectIDZND0="g_277ec50@0" ObjectIDZND1="9367@x" Pin0InfoVect0LinkObjId="g_277ec50_0" Pin0InfoVect1LinkObjId="SW-52786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-318 4880,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2782510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-303 4880,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9366@x" ObjectIDND1="g_277ec50@0" ObjectIDZND0="9367@1" Pin0InfoVect0LinkObjId="SW-52786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52784_0" Pin1InfoVect1LinkObjId="g_277ec50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-303 4880,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2785d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-174 4880,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2782770@1" ObjectIDZND0="9370@1" Pin0InfoVect0LinkObjId="SW-52789_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2782770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-174 4880,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2785fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-113 4911,-113 4911,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9370@x" ObjectIDZND0="g_27866f0@0" Pin0InfoVect0LinkObjId="g_27866f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-113 4911,-113 4911,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2786230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-125 4880,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9370@0" ObjectIDZND0="g_27866f0@0" Pin0InfoVect0LinkObjId="g_27866f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52789_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-125 4880,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2786490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-113 4880,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9370@x" ObjectIDND1="g_27866f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52789_0" Pin1InfoVect1LinkObjId="g_27866f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-113 4880,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2787e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-240 4865,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9367@x" ObjectIDND1="g_2782770@0" ObjectIDZND0="g_2788460@0" Pin0InfoVect0LinkObjId="g_2788460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52786_0" Pin1InfoVect1LinkObjId="g_2782770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-240 4865,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2788060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-251 4880,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9367@0" ObjectIDZND0="g_2788460@0" ObjectIDZND1="g_2782770@0" Pin0InfoVect0LinkObjId="g_2788460_0" Pin0InfoVect1LinkObjId="g_2782770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52786_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-251 4880,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2788250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-240 4880,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9367@x" ObjectIDND1="g_2788460@0" ObjectIDZND0="g_2782770@0" Pin0InfoVect0LinkObjId="g_2782770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52786_0" Pin1InfoVect1LinkObjId="g_2788460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-240 4880,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2789520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-427 4701,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9333@x" ObjectIDZND0="g_2789900@0" Pin0InfoVect0LinkObjId="g_2789900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="SW-52731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-427 4701,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2789710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-444 4689,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_2789900@0" ObjectIDZND1="9333@x" Pin0InfoVect0LinkObjId="g_2789900_0" Pin0InfoVect1LinkObjId="SW-52731_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-444 4689,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-427 4689,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_2789900@0" ObjectIDZND0="9333@1" Pin0InfoVect0LinkObjId="SW-52731_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="g_2789900_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-427 4689,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278cec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-374 4689,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9333@0" ObjectIDZND0="9334@x" ObjectIDZND1="9331@x" Pin0InfoVect0LinkObjId="SW-52732_0" Pin0InfoVect1LinkObjId="SW-52728_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52731_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-374 4689,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278fbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-361 4707,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9333@x" ObjectIDND1="9331@x" ObjectIDZND0="9334@0" Pin0InfoVect0LinkObjId="SW-52732_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52731_0" Pin1InfoVect1LinkObjId="SW-52728_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-361 4707,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_278fe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-361 4750,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9334@1" ObjectIDZND0="g_2790090@0" Pin0InfoVect0LinkObjId="g_2790090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52732_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-361 4750,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2792bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-361 4689,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9334@x" ObjectIDND1="9333@x" ObjectIDZND0="9331@1" Pin0InfoVect0LinkObjId="SW-52728_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52732_0" Pin1InfoVect1LinkObjId="SW-52731_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-361 4689,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2792e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-303 4701,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9331@x" ObjectIDND1="9332@x" ObjectIDZND0="g_27930b0@0" Pin0InfoVect0LinkObjId="g_27930b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="SW-52730_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-303 4701,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2793e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-318 4689,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9331@0" ObjectIDZND0="g_27930b0@0" ObjectIDZND1="9332@x" Pin0InfoVect0LinkObjId="g_27930b0_0" Pin0InfoVect1LinkObjId="SW-52730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-318 4689,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2796970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-303 4689,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9331@x" ObjectIDND1="g_27930b0@0" ObjectIDZND0="9332@1" Pin0InfoVect0LinkObjId="SW-52730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52728_0" Pin1InfoVect1LinkObjId="g_27930b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-303 4689,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-174 4689,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2796bd0@1" ObjectIDZND0="9335@1" Pin0InfoVect0LinkObjId="SW-52733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2796bd0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-174 4689,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-113 4720,-113 4720,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9335@x" ObjectIDZND0="g_279ab50@0" Pin0InfoVect0LinkObjId="g_279ab50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-113 4720,-113 4720,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279a690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-125 4689,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9335@0" ObjectIDZND0="g_279ab50@0" Pin0InfoVect0LinkObjId="g_279ab50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52733_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-125 4689,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279a8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-113 4689,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9335@x" ObjectIDND1="g_279ab50@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52733_0" Pin1InfoVect1LinkObjId="g_279ab50_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-113 4689,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279cc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-240 4674,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9332@x" ObjectIDND1="g_2796bd0@0" ObjectIDZND0="g_279d220@0" Pin0InfoVect0LinkObjId="g_279d220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52730_0" Pin1InfoVect1LinkObjId="g_2796bd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-240 4674,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-251 4689,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9332@0" ObjectIDZND0="g_279d220@0" ObjectIDZND1="g_2796bd0@0" Pin0InfoVect0LinkObjId="g_279d220_0" Pin0InfoVect1LinkObjId="g_2796bd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-251 4689,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4689,-240 4689,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9332@x" ObjectIDND1="g_279d220@0" ObjectIDZND0="g_2796bd0@0" Pin0InfoVect0LinkObjId="g_2796bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52730_0" Pin1InfoVect1LinkObjId="g_279d220_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4689,-240 4689,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-427 4512,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9330@0" ObjectIDND1="9338@x" ObjectIDZND0="g_279e830@0" Pin0InfoVect0LinkObjId="g_279e830_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="SW-52739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-427 4512,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_279e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-444 4500,-427 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9330@0" ObjectIDZND0="g_279e830@0" ObjectIDZND1="9338@x" Pin0InfoVect0LinkObjId="g_279e830_0" Pin0InfoVect1LinkObjId="SW-52739_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-444 4500,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a1a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-427 4500,-410 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9330@0" ObjectIDND1="g_279e830@0" ObjectIDZND0="9338@1" Pin0InfoVect0LinkObjId="SW-52739_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="g_279e830_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-427 4500,-410 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a1c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-374 4500,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="9338@0" ObjectIDZND0="9339@x" ObjectIDZND1="9336@x" Pin0InfoVect0LinkObjId="SW-52740_0" Pin0InfoVect1LinkObjId="SW-52736_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52739_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-374 4500,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a49a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-361 4518,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="9338@x" ObjectIDND1="9336@x" ObjectIDZND0="9339@0" Pin0InfoVect0LinkObjId="SW-52740_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52739_0" Pin1InfoVect1LinkObjId="SW-52736_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-361 4518,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a4c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4554,-361 4561,-361 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="9339@1" ObjectIDZND0="g_27a4e60@0" Pin0InfoVect0LinkObjId="g_27a4e60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52740_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4554,-361 4561,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a79c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-361 4500,-345 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="9339@x" ObjectIDND1="9338@x" ObjectIDZND0="9336@1" Pin0InfoVect0LinkObjId="SW-52736_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52740_0" Pin1InfoVect1LinkObjId="SW-52739_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-361 4500,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a7c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-303 4512,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="9336@x" ObjectIDND1="9337@x" ObjectIDZND0="g_27a7e80@0" Pin0InfoVect0LinkObjId="g_27a7e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="SW-52738_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-303 4512,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27a8c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-318 4500,-303 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="9336@0" ObjectIDZND0="g_27a7e80@0" ObjectIDZND1="9337@x" Pin0InfoVect0LinkObjId="g_27a7e80_0" Pin0InfoVect1LinkObjId="SW-52738_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-318 4500,-303 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27ab740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-303 4500,-287 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="9336@x" ObjectIDND1="g_27a7e80@0" ObjectIDZND0="9337@1" Pin0InfoVect0LinkObjId="SW-52738_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52736_0" Pin1InfoVect1LinkObjId="g_27a7e80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-303 4500,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27aefa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-174 4500,-161 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_27ab9a0@1" ObjectIDZND0="9340@1" Pin0InfoVect0LinkObjId="SW-52741_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27ab9a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-174 4500,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27af200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-113 4531,-113 4531,-104 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9340@x" ObjectIDZND0="g_27af920@0" Pin0InfoVect0LinkObjId="g_27af920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-113 4531,-113 4531,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27af460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-125 4500,-113 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="9340@0" ObjectIDZND0="g_27af920@0" Pin0InfoVect0LinkObjId="g_27af920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52741_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-125 4500,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27af6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-113 4500,-42 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="9340@x" ObjectIDND1="g_27af920@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52741_0" Pin1InfoVect1LinkObjId="g_27af920_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-113 4500,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b0d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-240 4485,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9337@x" ObjectIDND1="g_27ab9a0@0" ObjectIDZND0="g_27b12d0@0" Pin0InfoVect0LinkObjId="g_27b12d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52738_0" Pin1InfoVect1LinkObjId="g_27ab9a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-240 4485,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b0ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-251 4500,-240 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="9337@0" ObjectIDZND0="g_27b12d0@0" ObjectIDZND1="g_27ab9a0@0" Pin0InfoVect0LinkObjId="g_27b12d0_0" Pin0InfoVect1LinkObjId="g_27ab9a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-52738_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-251 4500,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b10e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4500,-240 4500,-227 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9337@x" ObjectIDND1="g_27b12d0@0" ObjectIDZND0="g_27ab9a0@0" Pin0InfoVect0LinkObjId="g_27ab9a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-52738_0" Pin1InfoVect1LinkObjId="g_27b12d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4500,-240 4500,-227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c8e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3739,-471 3739,-444 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="9329@0" ObjectIDZND0="9329@0" Pin0InfoVect0LinkObjId="g_26e8a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e8a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3739,-471 3739,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27c9c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4826,-471 4826,-444 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="busSection" ObjectIDND0="9330@0" ObjectIDZND0="9330@0" Pin0InfoVect0LinkObjId="g_2591110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2591110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4826,-471 4826,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d4830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-557 4035,-574 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="9396@x" ObjectIDND1="9395@x" ObjectIDND2="g_25991f0@0" ObjectIDZND0="g_2598570@0" Pin0InfoVect0LinkObjId="g_2598570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-52911_0" Pin1InfoVect1LinkObjId="SW-52908_0" Pin1InfoVect2LinkObjId="g_25991f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-557 4035,-574 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4035,-605 4035,-626 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2598570@1" ObjectIDZND0="g_2599fa0@0" Pin0InfoVect0LinkObjId="g_2599fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2598570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4035,-605 4035,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2647160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3737,-1050 3737,-1082 3756,-1082 " stroke-width="0.5"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_23d2400@0" ObjectIDND1="0@1" ObjectIDND2="9381@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_23d2400_0" Pin1InfoVect1LinkObjId="SW-0_1" Pin1InfoVect2LinkObjId="SW-52817_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3737,-1050 3737,-1082 3756,-1082 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52671" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52671" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52672" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52672" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52669" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3862.000000 -920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52669" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9379"/>
     <cge:Term_Ref ObjectID="13304"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52666" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52666" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52667" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52667" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52664" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4927.000000 -920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52664" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9374"/>
     <cge:Term_Ref ObjectID="13294"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52683" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52683" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52684" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52684" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52680" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3876.000000 -570.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9385"/>
     <cge:Term_Ref ObjectID="13316"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52661" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52661" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52662" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52662" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52659" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4377.000000 -664.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52659" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9371"/>
     <cge:Term_Ref ObjectID="13288"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52695" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52695" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52696" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52696" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52692" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4967.000000 -570.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52692" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9390"/>
     <cge:Term_Ref ObjectID="13326"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52648" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3717.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9356"/>
     <cge:Term_Ref ObjectID="13258"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52645" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52646" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52643" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3893.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52643" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9351"/>
     <cge:Term_Ref ObjectID="13248"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52640" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52641" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52638" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4094.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9346"/>
     <cge:Term_Ref ObjectID="13238"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52635" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52635" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52636" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52633" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4295.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9341"/>
     <cge:Term_Ref ObjectID="13228"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52630" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52631" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52631" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52628" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4557.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9336"/>
     <cge:Term_Ref ObjectID="13218"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-0" prefix="">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4745.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9331"/>
     <cge:Term_Ref ObjectID="13208"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52656" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52656" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52657" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52654" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4947.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9366"/>
     <cge:Term_Ref ObjectID="13278"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="7" appendix="" decimal="0" id="ME-52651" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="7" appendix="" decimal="0" id="ME-52652" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="0" id="ME-52649" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5128.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52649" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9361"/>
     <cge:Term_Ref ObjectID="13268"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52713" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -865.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9328"/>
     <cge:Term_Ref ObjectID="13205"/>
    <cge:TPSR_Ref TObjectID="9328"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52715" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5127.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52715" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9330"/>
     <cge:Term_Ref ObjectID="13207"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-52714" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3611.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="52714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="9329"/>
     <cge:Term_Ref ObjectID="13206"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -1096.000000) translate(0,18)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">城北变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3709.000000 -1148.000000) translate(0,18)">上城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4164.000000 -1030.000000) translate(0,18)">35kVTV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4841.000000 -1119.000000) translate(0,18)">35kV线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4763.000000 -1148.000000) translate(0,18)">35kV金城线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4590.000000 -678.000000) translate(0,18)">10kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3981.000000 -678.000000) translate(0,18)">10kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3636.000000 -36.000000) translate(0,18)">铁路线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3681.000000 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3825.666667 -36.000000) translate(0,18)">石灰坝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3870.666667 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4015.333333 -36.000000) translate(0,18)">城北III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4060.333333 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4205.000000 -36.000000) translate(0,18)">城北II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4250.000000 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 5037.000000 -36.000000) translate(0,18)">大横山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4847.666667 -36.000000) translate(0,18)">北厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4892.666667 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4647.333333 -36.000000) translate(0,18)">喜尧水渣厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.333333 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 4468.000000 -36.000000) translate(0,18)">城北I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -210.000000) translate(0,15)">50%5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" transform="matrix(1.000000 0.000000 0.000000 1.000000 3874.000000 -1035.000000) translate(0,18)">35kV站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3746.000000 -910.000000) translate(0,15)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -957.000000) translate(0,15)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3744.000000 -863.000000) translate(0,15)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3749.000000 -1012.000000) translate(0,15)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4206.000000 -868.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4096.000000 -929.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4800.000000 -910.000000) translate(0,15)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4798.000000 -863.000000) translate(0,15)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4798.000000 -957.000000) translate(0,15)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4803.000000 -1012.000000) translate(0,15)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3582.000000 -844.000000) translate(0,15)">35kVIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4853.000000 -684.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3766.000000 -684.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -753.000000) translate(0,15)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -800.000000) translate(0,15)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4835.000000 -752.000000) translate(0,15)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -800.000000) translate(0,15)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4835.000000 -560.000000) translate(0,15)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -513.000000) translate(0,15)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4833.000000 -610.000000) translate(0,15)">4026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3747.000000 -560.000000) translate(0,15)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -610.000000) translate(0,15)">4016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3745.000000 -513.000000) translate(0,15)">4011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4042.000000 -516.000000) translate(0,15)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3938.000000 -577.000000) translate(0,15)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3547.000000 -494.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -494.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3620.000000 -341.000000) translate(0,15)">451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -401.000000) translate(0,15)">4511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -278.000000) translate(0,15)">4512</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3610.000000 -152.000000) translate(0,15)">4516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3814.000000 -341.000000) translate(0,15)">452</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3804.000000 -401.000000) translate(0,15)">4521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3804.000000 -152.000000) translate(0,15)">4526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3804.000000 -278.000000) translate(0,15)">4522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3872.000000 -390.000000) translate(0,15)">45217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3681.000000 -390.000000) translate(0,15)">45117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4002.000000 -341.000000) translate(0,15)">453</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -278.000000) translate(0,15)">4532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -401.000000) translate(0,15)">4531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3992.000000 -152.000000) translate(0,15)">4536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4060.000000 -390.000000) translate(0,15)">45317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4194.000000 -341.000000) translate(0,15)">454</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -401.000000) translate(0,15)">4541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -152.000000) translate(0,15)">4546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4184.000000 -278.000000) translate(0,15)">4542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4250.000000 -390.000000) translate(0,15)">45417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.000000 -467.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3547.000000 -466.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4303.000000 -566.000000) translate(0,15)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -515.000000) translate(0,15)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4462.000000 -546.000000) translate(0,15)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4670.000000 -516.000000) translate(0,15)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4560.000000 -577.000000) translate(0,15)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4457.000000 -341.000000) translate(0,15)">455</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -401.000000) translate(0,15)">4551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -278.000000) translate(0,15)">4552</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4513.000000 -390.000000) translate(0,15)">45517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4447.000000 -152.000000) translate(0,15)">4556</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4643.000000 -341.000000) translate(0,15)">456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -401.000000) translate(0,15)">4561</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -278.000000) translate(0,15)">4562</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -152.000000) translate(0,15)">4566</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4702.000000 -390.000000) translate(0,15)">45617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4840.000000 -341.000000) translate(0,15)">457</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4893.000000 -390.000000) translate(0,15)">45717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -401.000000) translate(0,15)">4571</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -278.000000) translate(0,15)">4572</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4830.000000 -152.000000) translate(0,15)">4576</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5024.000000 -341.000000) translate(0,15)">458</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5082.000000 -390.000000) translate(0,15)">45817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -152.000000) translate(0,15)">4586</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -401.000000) translate(0,15)">4581</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5014.000000 -278.000000) translate(0,15)">4582</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-52815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9379" ObjectName="SW-CX_CB.CX_CB_351BK"/>
     <cge:Meas_Ref ObjectId="52815"/>
    <cge:TPSR_Ref TObjectID="9379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52799">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -879.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9374" ObjectName="SW-CX_CB.CX_CB_352BK"/>
     <cge:Meas_Ref ObjectId="52799"/>
    <cge:TPSR_Ref TObjectID="9374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52862">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.707493 -721.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9388" ObjectName="SW-CX_CB.CX_CB_302BK"/>
     <cge:Meas_Ref ObjectId="52862"/>
    <cge:TPSR_Ref TObjectID="9388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52868">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.707493 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9390" ObjectName="SW-CX_CB.CX_CB_402BK"/>
     <cge:Meas_Ref ObjectId="52868"/>
    <cge:TPSR_Ref TObjectID="9390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52792">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.759366 -535.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9371" ObjectName="SW-CX_CB.CX_CB_412BK"/>
     <cge:Meas_Ref ObjectId="52792"/>
    <cge:TPSR_Ref TObjectID="9371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52830">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -722.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9383" ObjectName="SW-CX_CB.CX_CB_301BK"/>
     <cge:Meas_Ref ObjectId="52830"/>
    <cge:TPSR_Ref TObjectID="9383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52836">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 -529.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9385" ObjectName="SW-CX_CB.CX_CB_401BK"/>
     <cge:Meas_Ref ObjectId="52836"/>
    <cge:TPSR_Ref TObjectID="9385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3659.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9356" ObjectName="SW-CX_CB.CX_CB_451BK"/>
     <cge:Meas_Ref ObjectId="52768"/>
    <cge:TPSR_Ref TObjectID="9356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.666667 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9351" ObjectName="SW-CX_CB.CX_CB_452BK"/>
     <cge:Meas_Ref ObjectId="52760"/>
    <cge:TPSR_Ref TObjectID="9351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.333333 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9346" ObjectName="SW-CX_CB.CX_CB_453BK"/>
     <cge:Meas_Ref ObjectId="52752"/>
    <cge:TPSR_Ref TObjectID="9346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9341" ObjectName="SW-CX_CB.CX_CB_454BK"/>
     <cge:Meas_Ref ObjectId="52744"/>
    <cge:TPSR_Ref TObjectID="9341"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52776">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5060.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9361" ObjectName="SW-CX_CB.CX_CB_458BK"/>
     <cge:Meas_Ref ObjectId="52776"/>
    <cge:TPSR_Ref TObjectID="9361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4870.666667 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9366" ObjectName="SW-CX_CB.CX_CB_457BK"/>
     <cge:Meas_Ref ObjectId="52784"/>
    <cge:TPSR_Ref TObjectID="9366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52728">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.333333 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9331" ObjectName="SW-CX_CB.CX_CB_456BK"/>
     <cge:Meas_Ref ObjectId="52728"/>
    <cge:TPSR_Ref TObjectID="9331"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-52736">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4491.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9336" ObjectName="SW-CX_CB.CX_CB_455BK"/>
     <cge:Meas_Ref ObjectId="52736"/>
    <cge:TPSR_Ref TObjectID="9336"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3865.000000 -1013.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3865.000000 -1013.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CB.CX_CB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13350"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.707493 -630.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4800.707493 -630.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9400" ObjectName="TF-CX_CB.CX_CB_2T"/>
    <cge:TPSR_Ref TObjectID="9400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_CB.CX_CB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="13346"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -630.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -630.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="9399" ObjectName="TF-CX_CB.CX_CB_1T"/>
    <cge:TPSR_Ref TObjectID="9399"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 920.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3815.000000 890.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3790.000000 905.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 920.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 890.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 905.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4901.000000 570.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4915.000000 540.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4890.000000 555.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 570.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3827.000000 540.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3802.000000 555.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4308.000000 664.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4322.000000 634.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 649.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3667.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3642.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3832.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4043.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4018.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4230.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4219.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4478.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4689.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4664.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4888.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4863.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5061.000000 -20.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5075.000000 -50.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5050.000000 -35.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3537.000000 865.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3515.000000 511.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 511.000000) translate(0,12)">Uab（kV）：</text>
    </g>
   <metadata/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_23d2400">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3670.000000 -1044.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25f1500">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 3842.500000 -1068.500000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_263f8c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4190.000000 -916.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a8c90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -926.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a96a0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4213.000000 -1003.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25aadb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4724.000000 -1044.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_260bbe0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4896.500000 -1068.500000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25dfcd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4653.900576 -564.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e0e30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4690.900576 -574.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25e1b00">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4676.900576 -651.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2598570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 -569.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25991f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -574.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2599fa0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4049.000000 -651.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26ea640">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2603ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26077c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3663.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ae280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3692.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25afde0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3595.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25b1620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.666667 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25ba3d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.666667 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25bdb60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3853.666667 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c1730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.666667 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25c3500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.666667 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25623a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.333333 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256af60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.333333 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256e6f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.333333 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25724b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.333333 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2574520">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3974.333333 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25759f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_257f1a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2582cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2586c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4261.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25885f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2589b10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27697f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5077.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_276d310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5064.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2771290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5093.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25af970">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2775560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.666667 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_277ec50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4887.666667 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2782770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.666667 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27866f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4903.666667 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2788460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4806.666667 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2789900">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.333333 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27930b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4697.333333 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2796bd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4684.333333 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_279ab50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4713.333333 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_279d220">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.333333 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_279e830">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -421.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27a7e80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 -297.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27ab9a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4495.000000 -169.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27af920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27b12d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 -234.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="cx_索引_接线图_客户变110.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-821 5109,-821 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9328" ObjectName="BS-CX_CB.CX_CB_3IM"/>
    <cge:TPSR_Ref TObjectID="9328"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-821 5109,-821 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-471 5132,-471 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9330" ObjectName="BS-CX_CB.CX_CB_9IIM"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-471 5132,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4416,-444 5132,-444 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9330" ObjectName="BS-CX_CB.CX_CB_9IIM"/>
    <cge:TPSR_Ref TObjectID="9330"/></metadata>
   <polyline fill="none" opacity="0" points="4416,-444 5132,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-444 4327,-444 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9329" ObjectName="BS-CX_CB.CX_CB_9IM"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-444 4327,-444 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_CB.CX_CB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3582,-471 4327,-471 " stroke-width="5"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9329" ObjectName="BS-CX_CB.CX_CB_9IM"/>
    <cge:TPSR_Ref TObjectID="9329"/></metadata>
   <polyline fill="none" opacity="0" points="3582,-471 4327,-471 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4"/>
</svg>