<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-63" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3118 -1265 2218 1269">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <polyline arcFlag="1" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <polyline arcFlag="1" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 " stroke-width="0.0972"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape125">
    <ellipse cx="14" cy="17" fillStyle="0" rx="9" ry="7" stroke-width="0.153636"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="20" x2="20" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="8" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.153636" x1="14" x2="14" y1="16" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.153636"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.0625"/>
    <polyline points="58,100 64,100 " stroke-width="1.0625"/>
    <polyline points="64,100 64,93 " stroke-width="1.0625"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape43_0">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="60" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="68" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="76" y2="68"/>
   </symbol>
   <symbol id="transformer2:shape43_1">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape56_0">
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,43 41,43 41,72 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="57" y2="99"/>
    <polyline DF8003:Layer="PUBLIC" points="16,84 22,71 9,71 16,84 16,83 16,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="72" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="39" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="16" y1="54" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="43" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="11" y1="43" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="43" y2="38"/>
   </symbol>
   <symbol id="transformer2:shape56_1">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="14" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="20" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="16" y1="20" y2="14"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d5f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2a19d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d7de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d8d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33d9cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33da8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33db350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33dbe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d6f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33d6f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33df1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33df1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e0f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e0f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_33e1f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e3b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33e5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e5e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e74a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e81a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e8a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e9220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ea300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eac80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eb770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ec130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ed5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ee150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ef180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33efdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33fe200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f1330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33f2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33f3e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1279" width="2228" x="3113" y="-1270"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4798" y1="-1120" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4794" y1="-1086" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4794" x2="4803" y1="-1078" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4803" y1="-1086" y2="-1078"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4794" y1="-1098" y2="-1107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4794" x2="4803" y1="-1107" y2="-1107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4798" x2="4803" y1="-1098" y2="-1107"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4825" x2="4798" y1="-1120" y2="-1120"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4278" x2="4200" y1="-1204" y2="-1204"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4313,-365 4353,-365 4353,-329 " stroke="rgb(0,255,0)" stroke-width="1"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5334" x2="5334" y1="-382" y2="-366"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-44003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.978771 -642.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7378" ObjectName="SW-CX_SJ.CX_SJ_001BK"/>
     <cge:Meas_Ref ObjectId="44003"/>
    <cge:TPSR_Ref TObjectID="7378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.333333 -410.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7354" ObjectName="SW-CX_SJ.CX_SJ_031BK"/>
     <cge:Meas_Ref ObjectId="43949"/>
    <cge:TPSR_Ref TObjectID="7354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43988">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.333333 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7373" ObjectName="SW-CX_SJ.CX_SJ_032BK"/>
     <cge:Meas_Ref ObjectId="43988"/>
    <cge:TPSR_Ref TObjectID="7373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.333333 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7376" ObjectName="SW-CX_SJ.CX_SJ_033BK"/>
     <cge:Meas_Ref ObjectId="43996"/>
    <cge:TPSR_Ref TObjectID="7376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43954">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3799.333333 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7359" ObjectName="SW-CX_SJ.CX_SJ_034BK"/>
     <cge:Meas_Ref ObjectId="43954"/>
    <cge:TPSR_Ref TObjectID="7359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43970">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.260965 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7365" ObjectName="SW-CX_SJ.CX_SJ_036BK"/>
     <cge:Meas_Ref ObjectId="43970"/>
    <cge:TPSR_Ref TObjectID="7365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.451754 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7368" ObjectName="SW-CX_SJ.CX_SJ_037BK"/>
     <cge:Meas_Ref ObjectId="43978"/>
    <cge:TPSR_Ref TObjectID="7368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43962">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.030702 -409.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7362" ObjectName="SW-CX_SJ.CX_SJ_038BK"/>
     <cge:Meas_Ref ObjectId="43962"/>
    <cge:TPSR_Ref TObjectID="7362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44030">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4483.513158 -616.700000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7385" ObjectName="SW-CX_SJ.CX_SJ_012BK"/>
     <cge:Meas_Ref ObjectId="44030"/>
    <cge:TPSR_Ref TObjectID="7385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185038">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.187003 -1055.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28097" ObjectName="SW-CX_SJ.CX_SJ_341BK"/>
     <cge:Meas_Ref ObjectId="185038"/>
    <cge:TPSR_Ref TObjectID="28097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185103">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4191.187003 -1051.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28101" ObjectName="SW-CX_SJ.CX_SJ_342BK"/>
     <cge:Meas_Ref ObjectId="185103"/>
    <cge:TPSR_Ref TObjectID="28101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185149">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.187003 -1061.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28105" ObjectName="SW-CX_SJ.CX_SJ_343BK"/>
     <cge:Meas_Ref ObjectId="185149"/>
    <cge:TPSR_Ref TObjectID="28105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185222">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.478771 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28109" ObjectName="SW-CX_SJ.CX_SJ_302BK"/>
     <cge:Meas_Ref ObjectId="185222"/>
    <cge:TPSR_Ref TObjectID="28109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4732.478771 -642.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28110" ObjectName="SW-CX_SJ.CX_SJ_002BK"/>
     <cge:Meas_Ref ObjectId="185242"/>
    <cge:TPSR_Ref TObjectID="28110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44022">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4079.978771 -832.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28094" ObjectName="SW-CX_SJ.CX_SJ_301BK"/>
     <cge:Meas_Ref ObjectId="44022"/>
    <cge:TPSR_Ref TObjectID="28094"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SJ.CX_SJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3734,-546 4432,-546 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7349" ObjectName="BS-CX_SJ.CX_SJ_9IM"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   <polyline fill="none" opacity="0" points="3734,-546 4432,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SJ.CX_SJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-963 4912,-963 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="7350" ObjectName="BS-CX_SJ.CX_SJ_3IM"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   <polyline fill="none" opacity="0" points="3732,-963 4912,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SJ.CX_SJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4466,-546 5117,-546 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10674" ObjectName="BS-CX_SJ.CX_SJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   <polyline fill="none" opacity="0" points="4466,-546 5117,-546 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_SJ.CX_SJ_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4283.000000 -178.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41581" ObjectName="CB-CX_SJ.CX_SJ_1C"/>
    <cge:TPSR_Ref TObjectID="41581"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SJ.CX_SJ_1Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="28166"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4310.000000 -1136.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4310.000000 -1136.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="20215" ObjectName="TF-CX_SJ.CX_SJ_1Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SJ.CX_SJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="39860"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.954545 -0.000000 0.000000 -0.941176 4706.000000 -727.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.954545 -0.000000 0.000000 -0.941176 4706.000000 -727.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28113" ObjectName="TF-CX_SJ.CX_SJ_2T"/>
    <cge:TPSR_Ref TObjectID="28113"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SJ.CX_SJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="10714"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -712.000000)" xlink:href="#transformer2:shape43_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -712.000000)" xlink:href="#transformer2:shape43_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="7384" ObjectName="TF-CX_SJ.CX_SJ_1T"/>
    <cge:TPSR_Ref TObjectID="7384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_SJ.CX_SJ_2Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="40098"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4976.000000 -441.000000)" xlink:href="#transformer2:shape56_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4976.000000 -441.000000)" xlink:href="#transformer2:shape56_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28249" ObjectName="TF-CX_SJ.CX_SJ_2Zyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ae4c20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4887.493421 -702.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2969330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4879.493421 -736.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aed370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3897.000000 -706.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aeda60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3889.000000 -740.000000)" xlink:href="#lightningRod:shape125"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b14000">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -653.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b14560">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4933.493421 -649.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b14940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b14c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3991.000000 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15020">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4167.000000 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.927632 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b157e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4820.118421 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b15bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5071.697368 -205.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3229.500000 -1116.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="0" id="ME-185135" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4809.000000 -728.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185135" ObjectName="CX_SJ:CX_SJ_2T_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-185131" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4809.000000 -708.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185131" ObjectName="CX_SJ:CX_SJ_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200504" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4030.000000 -693.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200504" ObjectName="CX_SJ:CX_SJ_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200697" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3250.538462 -982.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200697" ObjectName="CX_SJ:CX_SJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-200698" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3250.538462 -939.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="200698" ObjectName="CX_SJ:CX_SJ_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-44028" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4331.000000 -121.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="44028" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7354"/>
     <cge:Term_Ref ObjectID="10652"/>
    <cge:TPSR_Ref TObjectID="7354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43873" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4331.000000 -121.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43873" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7354"/>
     <cge:Term_Ref ObjectID="10652"/>
    <cge:TPSR_Ref TObjectID="7354"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-43927" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -952.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7350"/>
     <cge:Term_Ref ObjectID="10645"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-43929" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -952.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7350"/>
     <cge:Term_Ref ObjectID="10645"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-43931" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -952.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43931" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7350"/>
     <cge:Term_Ref ObjectID="10645"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-43932" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -952.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7350"/>
     <cge:Term_Ref ObjectID="10645"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-43926" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3705.000000 -952.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="7350"/>
     <cge:Term_Ref ObjectID="10645"/>
    <cge:TPSR_Ref TObjectID="7350"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-43884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,12)">43884.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43884" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-43886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,27)">43886.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43886" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-43888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,42)">43888.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43888" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-43889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,57)">43889.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43889" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-43883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,72)">43883.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43883" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-43885" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,87)">43885.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43885" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-43887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -677.000000) translate(0,102)">43887.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43887" ObjectName="CX_SJ.CX_SJ_9IM:F"/>
     <cge:PSR_Ref ObjectID="7349"/>
     <cge:Term_Ref ObjectID="10644"/>
    <cge:TPSR_Ref TObjectID="7349"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-97179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,12)">97179.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97179" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-97180" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,27)">97180.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97180" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-97181" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,42)">97181.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97181" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-97188" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,57)">97188.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97188" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-97185" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,72)">97185.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97185" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-97186" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,87)">97186.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97186" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-97187" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5130.000000 -681.000000) translate(0,102)">97187.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="97187" ObjectName="CX_SJ.CX_SJ_9IIM:F"/>
     <cge:PSR_Ref ObjectID="10674"/>
     <cge:Term_Ref ObjectID="11699"/>
    <cge:TPSR_Ref TObjectID="10674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185048" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -1089.000000) translate(0,12)">185048.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185048" ObjectName="CX_SJ.CX_SJ_341BK:F"/>
     <cge:PSR_Ref ObjectID="28097"/>
     <cge:Term_Ref ObjectID="39832"/>
    <cge:TPSR_Ref TObjectID="28097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185049" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -1089.000000) translate(0,27)">185049.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185049" ObjectName="CX_SJ.CX_SJ_341BK:F"/>
     <cge:PSR_Ref ObjectID="28097"/>
     <cge:Term_Ref ObjectID="39832"/>
    <cge:TPSR_Ref TObjectID="28097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185045" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3837.000000 -1089.000000) translate(0,42)">185045.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185045" ObjectName="CX_SJ.CX_SJ_341BK:F"/>
     <cge:PSR_Ref ObjectID="28097"/>
     <cge:Term_Ref ObjectID="39832"/>
    <cge:TPSR_Ref TObjectID="28097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1101.000000) translate(0,12)">185055.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185055" ObjectName="CX_SJ.CX_SJ_342BK:F"/>
     <cge:PSR_Ref ObjectID="28101"/>
     <cge:Term_Ref ObjectID="39838"/>
    <cge:TPSR_Ref TObjectID="28101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185056" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1101.000000) translate(0,27)">185056.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185056" ObjectName="CX_SJ.CX_SJ_342BK:F"/>
     <cge:PSR_Ref ObjectID="28101"/>
     <cge:Term_Ref ObjectID="39838"/>
    <cge:TPSR_Ref TObjectID="28101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185051" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4107.000000 -1101.000000) translate(0,42)">185051.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185051" ObjectName="CX_SJ.CX_SJ_342BK:F"/>
     <cge:PSR_Ref ObjectID="28101"/>
     <cge:Term_Ref ObjectID="39838"/>
    <cge:TPSR_Ref TObjectID="28101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -1094.000000) translate(0,12)">185073.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185073" ObjectName="CX_SJ.CX_SJ_343BK:F"/>
     <cge:PSR_Ref ObjectID="28105"/>
     <cge:Term_Ref ObjectID="39844"/>
    <cge:TPSR_Ref TObjectID="28105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -1094.000000) translate(0,27)">185076.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185076" ObjectName="CX_SJ.CX_SJ_343BK:F"/>
     <cge:PSR_Ref ObjectID="28105"/>
     <cge:Term_Ref ObjectID="39844"/>
    <cge:TPSR_Ref TObjectID="28105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4550.000000 -1094.000000) translate(0,42)">185068.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185068" ObjectName="CX_SJ.CX_SJ_343BK:F"/>
     <cge:PSR_Ref ObjectID="28105"/>
     <cge:Term_Ref ObjectID="39844"/>
    <cge:TPSR_Ref TObjectID="28105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -885.000000) translate(0,12)">43940.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43940" ObjectName="CX_SJ.CX_SJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="28094"/>
     <cge:Term_Ref ObjectID="39828"/>
    <cge:TPSR_Ref TObjectID="28094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -885.000000) translate(0,27)">43939.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43939" ObjectName="CX_SJ.CX_SJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="28094"/>
     <cge:Term_Ref ObjectID="39828"/>
    <cge:TPSR_Ref TObjectID="28094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -885.000000) translate(0,42)">43933.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43933" ObjectName="CX_SJ.CX_SJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="28094"/>
     <cge:Term_Ref ObjectID="39828"/>
    <cge:TPSR_Ref TObjectID="28094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-43936" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4193.000000 -885.000000) translate(0,57)">43936.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43936" ObjectName="CX_SJ.CX_SJ_301BK:F"/>
     <cge:PSR_Ref ObjectID="28094"/>
     <cge:Term_Ref ObjectID="39828"/>
    <cge:TPSR_Ref TObjectID="28094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -681.000000) translate(0,12)">43882.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43882" ObjectName="CX_SJ.CX_SJ_001BK:F"/>
     <cge:PSR_Ref ObjectID="7378"/>
     <cge:Term_Ref ObjectID="10656"/>
    <cge:TPSR_Ref TObjectID="7378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -681.000000) translate(0,27)">43881.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43881" ObjectName="CX_SJ.CX_SJ_001BK:F"/>
     <cge:PSR_Ref ObjectID="7378"/>
     <cge:Term_Ref ObjectID="10656"/>
    <cge:TPSR_Ref TObjectID="7378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -681.000000) translate(0,42)">43876.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43876" ObjectName="CX_SJ.CX_SJ_001BK:F"/>
     <cge:PSR_Ref ObjectID="7378"/>
     <cge:Term_Ref ObjectID="10656"/>
    <cge:TPSR_Ref TObjectID="7378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-43879" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -681.000000) translate(0,57)">43879.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43879" ObjectName="CX_SJ.CX_SJ_001BK:F"/>
     <cge:PSR_Ref ObjectID="7378"/>
     <cge:Term_Ref ObjectID="10656"/>
    <cge:TPSR_Ref TObjectID="7378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -900.000000) translate(0,12)">185098.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185098" ObjectName="CX_SJ.CX_SJ_302BK:F"/>
     <cge:PSR_Ref ObjectID="28109"/>
     <cge:Term_Ref ObjectID="39850"/>
    <cge:TPSR_Ref TObjectID="28109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -900.000000) translate(0,27)">185101.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185101" ObjectName="CX_SJ.CX_SJ_302BK:F"/>
     <cge:PSR_Ref ObjectID="28109"/>
     <cge:Term_Ref ObjectID="39850"/>
    <cge:TPSR_Ref TObjectID="28109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -900.000000) translate(0,42)">185092.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185092" ObjectName="CX_SJ.CX_SJ_302BK:F"/>
     <cge:PSR_Ref ObjectID="28109"/>
     <cge:Term_Ref ObjectID="39850"/>
    <cge:TPSR_Ref TObjectID="28109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -900.000000) translate(0,57)">185100.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185100" ObjectName="CX_SJ.CX_SJ_302BK:F"/>
     <cge:PSR_Ref ObjectID="28109"/>
     <cge:Term_Ref ObjectID="39850"/>
    <cge:TPSR_Ref TObjectID="28109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-185110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -694.000000) translate(0,12)">185110.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185110" ObjectName="CX_SJ.CX_SJ_002BK:F"/>
     <cge:PSR_Ref ObjectID="28110"/>
     <cge:Term_Ref ObjectID="39852"/>
    <cge:TPSR_Ref TObjectID="28110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-185111" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -694.000000) translate(0,27)">185111.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185111" ObjectName="CX_SJ.CX_SJ_002BK:F"/>
     <cge:PSR_Ref ObjectID="28110"/>
     <cge:Term_Ref ObjectID="39852"/>
    <cge:TPSR_Ref TObjectID="28110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-185102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -694.000000) translate(0,42)">185102.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185102" ObjectName="CX_SJ.CX_SJ_002BK:F"/>
     <cge:PSR_Ref ObjectID="28110"/>
     <cge:Term_Ref ObjectID="39852"/>
    <cge:TPSR_Ref TObjectID="28110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-185113" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4639.000000 -694.000000) translate(0,57)">185113.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="185113" ObjectName="CX_SJ.CX_SJ_002BK:F"/>
     <cge:PSR_Ref ObjectID="28110"/>
     <cge:Term_Ref ObjectID="39852"/>
    <cge:TPSR_Ref TObjectID="28110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -116.000000) translate(0,12)">43925.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43925" ObjectName="CX_SJ.CX_SJ_033BK:F"/>
     <cge:PSR_Ref ObjectID="7376"/>
     <cge:Term_Ref ObjectID="10674"/>
    <cge:TPSR_Ref TObjectID="7376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -116.000000) translate(0,27)">43924.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43924" ObjectName="CX_SJ.CX_SJ_033BK:F"/>
     <cge:PSR_Ref ObjectID="7376"/>
     <cge:Term_Ref ObjectID="10674"/>
    <cge:TPSR_Ref TObjectID="7376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -116.000000) translate(0,42)">43920.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43920" ObjectName="CX_SJ.CX_SJ_033BK:F"/>
     <cge:PSR_Ref ObjectID="7376"/>
     <cge:Term_Ref ObjectID="10674"/>
    <cge:TPSR_Ref TObjectID="7376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -117.000000) translate(0,12)">43919.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43919" ObjectName="CX_SJ.CX_SJ_032BK:F"/>
     <cge:PSR_Ref ObjectID="7373"/>
     <cge:Term_Ref ObjectID="10668"/>
    <cge:TPSR_Ref TObjectID="7373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -117.000000) translate(0,27)">43918.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43918" ObjectName="CX_SJ.CX_SJ_032BK:F"/>
     <cge:PSR_Ref ObjectID="7373"/>
     <cge:Term_Ref ObjectID="10668"/>
    <cge:TPSR_Ref TObjectID="7373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -117.000000) translate(0,42)">43914.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43914" ObjectName="CX_SJ.CX_SJ_032BK:F"/>
     <cge:PSR_Ref ObjectID="7373"/>
     <cge:Term_Ref ObjectID="10668"/>
    <cge:TPSR_Ref TObjectID="7373"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -116.000000) translate(0,12)">43895.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43895" ObjectName="CX_SJ.CX_SJ_034BK:F"/>
     <cge:PSR_Ref ObjectID="7359"/>
     <cge:Term_Ref ObjectID="10680"/>
    <cge:TPSR_Ref TObjectID="7359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43894" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -116.000000) translate(0,27)">43894.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43894" ObjectName="CX_SJ.CX_SJ_034BK:F"/>
     <cge:PSR_Ref ObjectID="7359"/>
     <cge:Term_Ref ObjectID="10680"/>
    <cge:TPSR_Ref TObjectID="7359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3797.000000 -116.000000) translate(0,42)">43890.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43890" ObjectName="CX_SJ.CX_SJ_034BK:F"/>
     <cge:PSR_Ref ObjectID="7359"/>
     <cge:Term_Ref ObjectID="10680"/>
    <cge:TPSR_Ref TObjectID="7359"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -117.000000) translate(0,12)">43907.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43907" ObjectName="CX_SJ.CX_SJ_036BK:F"/>
     <cge:PSR_Ref ObjectID="7365"/>
     <cge:Term_Ref ObjectID="10692"/>
    <cge:TPSR_Ref TObjectID="7365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -117.000000) translate(0,27)">43906.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43906" ObjectName="CX_SJ.CX_SJ_036BK:F"/>
     <cge:PSR_Ref ObjectID="7365"/>
     <cge:Term_Ref ObjectID="10692"/>
    <cge:TPSR_Ref TObjectID="7365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4572.000000 -117.000000) translate(0,42)">43902.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43902" ObjectName="CX_SJ.CX_SJ_036BK:F"/>
     <cge:PSR_Ref ObjectID="7365"/>
     <cge:Term_Ref ObjectID="10692"/>
    <cge:TPSR_Ref TObjectID="7365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -122.000000) translate(0,12)">43913.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43913" ObjectName="CX_SJ.CX_SJ_037BK:F"/>
     <cge:PSR_Ref ObjectID="7368"/>
     <cge:Term_Ref ObjectID="10698"/>
    <cge:TPSR_Ref TObjectID="7368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -122.000000) translate(0,27)">43912.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43912" ObjectName="CX_SJ.CX_SJ_037BK:F"/>
     <cge:PSR_Ref ObjectID="7368"/>
     <cge:Term_Ref ObjectID="10698"/>
    <cge:TPSR_Ref TObjectID="7368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -122.000000) translate(0,42)">43908.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43908" ObjectName="CX_SJ.CX_SJ_037BK:F"/>
     <cge:PSR_Ref ObjectID="7368"/>
     <cge:Term_Ref ObjectID="10698"/>
    <cge:TPSR_Ref TObjectID="7368"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-43901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -120.000000) translate(0,12)">43901.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43901" ObjectName="CX_SJ.CX_SJ_038BK:F"/>
     <cge:PSR_Ref ObjectID="7362"/>
     <cge:Term_Ref ObjectID="10704"/>
    <cge:TPSR_Ref TObjectID="7362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-43900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -120.000000) translate(0,27)">43900.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43900" ObjectName="CX_SJ.CX_SJ_038BK:F"/>
     <cge:PSR_Ref ObjectID="7362"/>
     <cge:Term_Ref ObjectID="10704"/>
    <cge:TPSR_Ref TObjectID="7362"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-43896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5036.000000 -120.000000) translate(0,42)">43896.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="43896" ObjectName="CX_SJ.CX_SJ_038BK:F"/>
     <cge:PSR_Ref ObjectID="7362"/>
     <cge:Term_Ref ObjectID="10704"/>
    <cge:TPSR_Ref TObjectID="7362"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3241" y="-1175"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3241" y="-1175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3193" y="-1192"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3193" y="-1192"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="5050" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="5050" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4796" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4796" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4578" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4578" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4318" y="-439"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4318" y="-439"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4147" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4147" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3971" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3971" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3818" y="-438"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3818" y="-438"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4447" y="-651"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4447" y="-651"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3934" y="-1086"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3934" y="-1086"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4210" y="-1080"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4210" y="-1080"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4657" y="-1090"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4657" y="-1090"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3481" y="-1150"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3481" y="-1150"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3481" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3481" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="75" x="3139" y="-842"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="75" x="3139" y="-842"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4780" y="-794"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4780" y="-794"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="63" x="3988" y="-767"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="63" x="3988" y="-767"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3407,-1177 3404,-1180 3404,-1127 3407,-1130 3407,-1177" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3407,-1177 3404,-1180 3455,-1180 3452,-1177 3407,-1177" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3407,-1130 3404,-1127 3455,-1127 3452,-1130 3407,-1130" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3452,-1177 3455,-1180 3455,-1127 3452,-1130 3452,-1177" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3407" y="-1177"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3407" y="-1177"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="3126" y="-801"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="3126" y="-801"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3241" y="-1175"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3193" y="-1192"/></g>
   <g href="35kV三街变10kV蚂蝗箐线038断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="5050" y="-439"/></g>
   <g href="35kV三街变三街变10kV集镇线037断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4796" y="-439"/></g>
   <g href="35kV三街变三街变10kV黑泥线036断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4578" y="-439"/></g>
   <g href="35kV三街变三街变10kV＃1电容器组031断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4318" y="-439"/></g>
   <g href="35kV三街变三街变10kV秀水塘线032断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4147" y="-438"/></g>
   <g href="35kV三街变三街变10kV中三树线033断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3971" y="-438"/></g>
   <g href="35kV三街变10kV备用线034断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3818" y="-438"/></g>
   <g href="35kV三街变10kV分段012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4447" y="-651"/></g>
   <g href="35kV三街变35kV徐三线341断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3934" y="-1086"/></g>
   <g href="35kV三街变35kV杜三线342断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4210" y="-1080"/></g>
   <g href="35kV三街变35kV三树线343断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4657" y="-1090"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3481" y="-1150"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3481" y="-1185"/></g>
   <g href="35kV三街变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="75" x="3139" y="-842"/></g>
   <g href="35kV三街变2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4780" y="-794"/></g>
   <g href="35kV三街变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="63" x="3988" y="-767"/></g>
   <g href="AVC三街站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3407" y="-1177"/></g>
   <g href="35kV三街变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="3126" y="-801"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3120" y="-1196"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1076"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-596"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_XY" endPointId="0" endStationName="CX_SJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xusan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3926,-1221 3926,-1244 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38080" ObjectName="AC-35kV.LN_xusan"/>
    <cge:TPSR_Ref TObjectID="38080_SS-63"/></metadata>
   <polyline fill="none" opacity="0" points="3926,-1221 3926,-1244 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DuJ" endPointId="0" endStationName="CX_SJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_dusan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4200,-1215 4200,-1238 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38063" ObjectName="AC-35kV.LN_dusan"/>
    <cge:TPSR_Ref TObjectID="38063_SS-63"/></metadata>
   <polyline fill="none" opacity="0" points="4200,-1215 4200,-1238 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SJ" endPointId="0" endStationName="CX_SHJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_sanshu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4647,-1223 4647,-1246 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38081" ObjectName="AC-35kV.LN_sanshu"/>
    <cge:TPSR_Ref TObjectID="38081_SS-63"/></metadata>
   <polyline fill="none" opacity="0" points="4647,-1223 4647,-1246 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b66f70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4388.000000 -315.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="3903" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="4307" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="4136" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="3960" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="3807" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="4391" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7349" cx="4089" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="4836" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="4647" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4570" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4789" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="5041" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4525" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="4089" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="4742" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4742" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4893" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10674" cx="4960" cy="-546" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="4200" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="7350" cx="3926" cy="-963" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_28b4ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -573.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba7430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -1131.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ba7430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4881.000000 -1131.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -781.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3cc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3855.000000 -781.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -781.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3ced0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4861.000000 -781.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3d040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -165.000000) translate(0,15)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3d040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4265.000000 -165.000000) translate(0,33)">总容量:450kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3d1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -194.000000) translate(0,15)">备</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3d1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -194.000000) translate(0,33)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b3d1b0" transform="matrix(1.000000 0.000000 0.000000 1.000000 3776.000000 -194.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8b920" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -194.000000) translate(0,15)"/>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8b920" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -194.000000) translate(0,33)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8b920" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -194.000000) translate(0,51)">树</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8b920" transform="matrix(1.000000 0.000000 0.000000 1.000000 3930.000000 -194.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bd20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4097.000000 -194.000000) translate(0,15)">秀</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bd20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4097.000000 -194.000000) translate(0,33)">水</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bd20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4097.000000 -194.000000) translate(0,51)">塘</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bd20" transform="matrix(1.000000 0.000000 0.000000 1.000000 4097.000000 -194.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bf30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4542.000000 -196.000000) translate(0,15)">黑</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bf30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4542.000000 -196.000000) translate(0,33)">泥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8bf30" transform="matrix(1.000000 0.000000 0.000000 1.000000 4542.000000 -196.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4757.000000 -194.000000) translate(0,15)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4757.000000 -194.000000) translate(0,33)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c170" transform="matrix(1.000000 0.000000 0.000000 1.000000 4757.000000 -194.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c390" transform="matrix(1.000000 0.000000 0.000000 1.000000 5008.000000 -201.000000) translate(0,15)">蚂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c390" transform="matrix(1.000000 0.000000 0.000000 1.000000 5008.000000 -201.000000) translate(0,33)">蟥</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c390" transform="matrix(1.000000 0.000000 0.000000 1.000000 5008.000000 -201.000000) translate(0,51)">箐</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a8c390" transform="matrix(1.000000 0.000000 0.000000 1.000000 5008.000000 -201.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac51d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -988.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac55c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4842.952002 -1021.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4899.493421 -601.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5051.697368 -438.000000) translate(0,12)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5047.697368 -497.000000) translate(0,12)">0382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac64d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.697368 -375.000000) translate(0,12)">0386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4800.118421 -438.000000) translate(0,12)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4796.118421 -497.000000) translate(0,12)">0372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4797.118421 -375.000000) translate(0,12)">0376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b44e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4580.927632 -438.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.927632 -497.000000) translate(0,12)">0362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b452c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4577.927632 -375.000000) translate(0,12)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.013158 -650.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4398.013158 -590.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4532.013158 -589.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.778771 -677.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4095.778771 -609.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4318.000000 -439.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -498.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b464c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -351.000000) translate(0,12)">03167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 -373.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4147.000000 -438.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4143.000000 -497.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b46dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4144.000000 -375.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3971.000000 -438.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3967.000000 -497.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3968.000000 -375.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b476c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3818.000000 -438.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3814.000000 -497.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3815.000000 -375.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b47fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3910.000000 -605.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2b15fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3281.500000 -1164.500000) translate(0,16)">三街变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -586.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -1024.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abe500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3739.000000 -572.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa8f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -1259.000000) translate(0,12)">35kV徐三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9caa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -1253.000000) translate(0,12)">35kV杜三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a339d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4593.000000 -1265.000000) translate(0,12)">35kV三树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a92330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -1144.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a92820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -1084.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a92a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3933.000000 -1028.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a92ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -1024.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a92ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4207.000000 -1140.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a93120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4210.000000 -1080.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a93360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1034.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a935a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4654.000000 -1150.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a937e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -1090.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,27)">S9-2000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,42)">35±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,57)">2000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,72)">Y，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a95760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4129.000000 -798.000000) translate(0,87)">Ud%=6.41</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4ce90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -861.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4096.000000 -931.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4d310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -869.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -930.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4d790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4751.000000 -671.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4d9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4749.000000 -612.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,27)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,57)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,72)">Y，d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a63e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4573.000000 -798.000000) translate(0,87)">Ud%=7.03</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a65180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4401.000000 -654.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a038c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1142.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a05ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3492.000000 -1177.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a09450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3143.000000 -840.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2a0a140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -543.000000) translate(0,12)">1号主变档位不可遥调。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a18bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -191.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a18bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3130.000000 -191.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a1b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -201.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a1b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -201.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a1b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -201.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a1cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3257.000000 -231.000000) translate(0,17)">3811059</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a20490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4274.000000 -1033.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a20dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -794.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3988.000000 -767.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_295c520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4897.000000 -331.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_295f890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3411.000000 -1163.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2960550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -646.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29609b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -661.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2960bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3678.000000 -677.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2960e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3684.000000 -629.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2961070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.000000 -614.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29612b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.000000 -598.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29614f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3670.000000 -583.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2961730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5069.000000 -653.500000) translate(0,12)">Uc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2961970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5069.000000 -668.750000) translate(0,12)">Ub(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2961bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5069.000000 -684.000000) translate(0,12)">Ua(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2961df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -636.250000) translate(0,12)">U0(V):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2962030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -621.000000) translate(0,12)">Uab(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2962270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -605.000000) translate(0,12)">Ubc(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29624b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5061.000000 -590.000000) translate(0,12)">Uca(kV):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_29626f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3128.000000 -800.000000) translate(0,20)">隔刀远控</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6e350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6ea90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6ed10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3905.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6efc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3894.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6f200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3919.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4073.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6f8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4062.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6fb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6ff40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4514.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4744.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b12b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b12fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4980.000000 116.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b13270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4969.000000 101.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b134b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 86.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b138d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 121.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b13bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 106.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3bdf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3633.000000 907.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3bf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 923.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af72c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 936.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2af7470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3618.000000 892.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3c5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3626.000000 954.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.25" x1="4862" x2="4859" y1="1121" y2="1121"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4864" x2="4856" y1="1118" y2="1118"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.251748" x1="4860" x2="4860" y1="1115" y2="1106"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="4854" x2="4866" y1="1116" y2="1116"/>
    <circle DF8003:Layer="PUBLIC" cx="4836" cy="1124" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <circle DF8003:Layer="PUBLIC" cx="4836" cy="1113" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(255,255,0)" stroke-width="0.416667" width="14" x="4853" y="1078"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4860" x2="4860" y1="1063" y2="1100"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4859" x2="4836" y1="1063" y2="1063"/>
    <rect DF8003:Layer="PUBLIC" fill="none" height="27" stroke="rgb(255,255,0)" stroke-width="0.416667" width="14" x="4829" y="1071"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4836" x2="4836" y1="1052" y2="1105"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.0587025" x1="4836" x2="4833" y1="1110" y2="1107"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.173913" x1="4837" x2="4837" y1="1112" y2="1115"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.108974" x1="4837" x2="4840" y1="1111" y2="1108"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.0587025" x1="4836" x2="4833" y1="1125" y2="1122"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.173913" x1="4837" x2="4837" y1="1127" y2="1130"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.108974" x1="4837" x2="4840" y1="1126" y2="1123"/>
    <g DF8003:Layer="PUBLIC">
     <circle DF8003:Layer="PUBLIC" cx="4847" cy="1119" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4849,1119 4851,1121 4845,1121 4847,1118 " stroke="rgb(255,255,0)" stroke-width="1"/>
    </g>
    <g DF8003:Layer="PUBLIC">
     <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.108974" x1="4825" x2="4828" y1="1120" y2="1117"/>
     <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.173913" x1="4825" x2="4825" y1="1121" y2="1124"/>
     <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.0587025" x1="4824" x2="4821" y1="1120" y2="1117"/>
    </g>
    <circle DF8003:Layer="PUBLIC" cx="4827" cy="1119" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <metadata/><polyline fill="none" opacity="0" points="4849,1119 4851,1121 4845,1121 4847,1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5938.000000 -5903.000000)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4845" x2="4845" y1="1137" y2="1144"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.223776" x1="4842" x2="4835" y1="1140" y2="1140"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="4842" x2="4842" y1="1146" y2="1135"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.25" x1="4848" x2="4848" y1="1138" y2="1141"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="4845" x2="4845" y1="1137" y2="1144"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3779.000000 1091.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3768.000000 1076.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a271d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 1061.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a275f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4051.000000 1100.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a278b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 1085.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a27af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4065.000000 1070.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a27f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 1093.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a281d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 1078.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a28410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4508.000000 1063.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a65930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 730.000000) translate(0,15)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a661d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 709.000000) translate(0,15)">油温:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a67810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.000000 716.000000) translate(0,15)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a67a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.000000 695.000000) translate(0,15)">油温:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a68880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a68ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 856.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a68cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4157.000000 841.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a69970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 886.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a69ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 667.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a69f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 652.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6a190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4159.000000 637.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6a3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4139.000000 682.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6a700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4792.000000 883.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6a970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 868.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6abb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.000000 853.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6adf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 898.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6b120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 678.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6b390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.000000 663.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6b5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4600.000000 648.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a02250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 693.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <circle DF8003:Layer="PUBLIC" cx="3864" cy="1209" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <circle DF8003:Layer="PUBLIC" cx="3854" cy="1209" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.416667" width="26" x="3886" y="1202"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3873" x2="3926" y1="1209" y2="1209"/>
   <metadata/><rect fill="white" height="13" opacity="0" stroke="white" transform="" width="26" x="3886" y="1202"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(255,255,0)" stroke-width="1" width="28" x="3883" y="1175"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="3926" x2="3891" y1="1181" y2="1181"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="3873" x2="3873" y1="1179" y2="1186"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="3876" x2="3876" y1="1188" y2="1177"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.223776" x1="3876" x2="3883" y1="1182" y2="1182"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="3871" x2="3871" y1="1181" y2="1183"/>
   <metadata/><rect fill="white" height="14" opacity="0" stroke="white" transform="" width="28" x="3883" y="1175"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <circle DF8003:Layer="PUBLIC" cx="4138" cy="1195" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <circle DF8003:Layer="PUBLIC" cx="4128" cy="1195" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.416667" width="26" x="4160" y="1188"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4147" x2="4200" y1="1195" y2="1195"/>
   <metadata/><rect fill="white" height="13" opacity="0" stroke="white" transform="" width="26" x="4160" y="1188"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(255,255,0)" stroke-width="1" width="28" x="4157" y="1161"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4200" x2="4165" y1="1167" y2="1167"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4147" x2="4147" y1="1165" y2="1172"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="4150" x2="4150" y1="1174" y2="1163"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.223776" x1="4150" x2="4157" y1="1168" y2="1168"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4145" x2="4145" y1="1167" y2="1169"/>
   <metadata/><rect fill="white" height="14" opacity="0" stroke="white" transform="" width="28" x="4157" y="1161"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <circle DF8003:Layer="PUBLIC" cx="4585" cy="1211" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <circle DF8003:Layer="PUBLIC" cx="4575" cy="1211" fill="none" fillStyle="0" r="8.5" stroke="rgb(255,255,0)" stroke-width="1"/>
    <rect DF8003:Layer="PUBLIC" fill="none" height="13" stroke="rgb(255,255,0)" stroke-width="0.416667" width="26" x="4607" y="1204"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4594" x2="4647" y1="1211" y2="1211"/>
   <metadata/><rect fill="white" height="13" opacity="0" stroke="white" transform="" width="26" x="4607" y="1204"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <rect DF8003:Layer="PUBLIC" fill="none" height="14" stroke="rgb(255,255,0)" stroke-width="1" width="28" x="4604" y="1177"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="4647" x2="4612" y1="1183" y2="1183"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4594" x2="4594" y1="1181" y2="1188"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.611465" x1="4597" x2="4597" y1="1190" y2="1179"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.223776" x1="4597" x2="4604" y1="1184" y2="1184"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.39375" x1="4592" x2="4592" y1="1183" y2="1185"/>
   <metadata/><rect fill="white" height="14" opacity="0" stroke="white" transform="" width="28" x="4604" y="1177"/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_SJ.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3952.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34488" ObjectName="EC-CX_SJ.033Ld"/>
    <cge:TPSR_Ref TObjectID="34488"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SJ.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4128.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34489" ObjectName="EC-CX_SJ.032Ld"/>
    <cge:TPSR_Ref TObjectID="34489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SJ.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4562.000000 -131.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34490" ObjectName="EC-CX_SJ.036Ld"/>
    <cge:TPSR_Ref TObjectID="34490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SJ.038Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5033.000000 -134.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34492" ObjectName="EC-CX_SJ.038Ld"/>
    <cge:TPSR_Ref TObjectID="34492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_SJ.037Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -142.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34491" ObjectName="EC-CX_SJ.037Ld"/>
    <cge:TPSR_Ref TObjectID="34491"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37332" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3409.000000 -1084.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5914" ObjectName="DYN-CX_SJ"/>
     <cge:Meas_Ref ObjectId="37332"/>
    </metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-43950">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.826754 -554.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7355" ObjectName="SW-CX_SJ.CX_SJ_0902SW"/>
     <cge:Meas_Ref ObjectId="43950"/>
    <cge:TPSR_Ref TObjectID="7355"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4820.973623 -974.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7379" ObjectName="SW-CX_SJ.CX_SJ_3901SW"/>
     <cge:Meas_Ref ObjectId="44018"/>
    <cge:TPSR_Ref TObjectID="7379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.333333 -558.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7356" ObjectName="SW-CX_SJ.CX_SJ_0901SW"/>
     <cge:Meas_Ref ObjectId="43951"/>
    <cge:TPSR_Ref TObjectID="7356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44002">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.178771 -562.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7377" ObjectName="SW-CX_SJ.CX_SJ_0011SW"/>
     <cge:Meas_Ref ObjectId="44002"/>
    <cge:TPSR_Ref TObjectID="7377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.333333 -451.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7351" ObjectName="SW-CX_SJ.CX_SJ_0311SW"/>
     <cge:Meas_Ref ObjectId="43946"/>
    <cge:TPSR_Ref TObjectID="7351"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4293.333333 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7353" ObjectName="SW-CX_SJ.CX_SJ_0316SW"/>
     <cge:Meas_Ref ObjectId="43948"/>
    <cge:TPSR_Ref TObjectID="7353"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4345.000000 -299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7352" ObjectName="SW-CX_SJ.CX_SJ_03167SW"/>
     <cge:Meas_Ref ObjectId="43947"/>
    <cge:TPSR_Ref TObjectID="7352"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43986">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.333333 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7371" ObjectName="SW-CX_SJ.CX_SJ_0321SW"/>
     <cge:Meas_Ref ObjectId="43986"/>
    <cge:TPSR_Ref TObjectID="7371"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43987">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4122.333333 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7372" ObjectName="SW-CX_SJ.CX_SJ_0326SW"/>
     <cge:Meas_Ref ObjectId="43987"/>
    <cge:TPSR_Ref TObjectID="7372"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43994">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3945.333333 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7374" ObjectName="SW-CX_SJ.CX_SJ_0331SW"/>
     <cge:Meas_Ref ObjectId="43994"/>
    <cge:TPSR_Ref TObjectID="7374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3946.333333 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7375" ObjectName="SW-CX_SJ.CX_SJ_0336SW"/>
     <cge:Meas_Ref ObjectId="43995"/>
    <cge:TPSR_Ref TObjectID="7375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.333333 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7357" ObjectName="SW-CX_SJ.CX_SJ_0341SW"/>
     <cge:Meas_Ref ObjectId="43952"/>
    <cge:TPSR_Ref TObjectID="7357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.333333 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7358" ObjectName="SW-CX_SJ.CX_SJ_0346SW"/>
     <cge:Meas_Ref ObjectId="43953"/>
    <cge:TPSR_Ref TObjectID="7358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4555.260965 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7363" ObjectName="SW-CX_SJ.CX_SJ_0362SW"/>
     <cge:Meas_Ref ObjectId="43968"/>
    <cge:TPSR_Ref TObjectID="7363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43969">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.260965 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7364" ObjectName="SW-CX_SJ.CX_SJ_0366SW"/>
     <cge:Meas_Ref ObjectId="43969"/>
    <cge:TPSR_Ref TObjectID="7364"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43976">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.451754 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7366" ObjectName="SW-CX_SJ.CX_SJ_0372SW"/>
     <cge:Meas_Ref ObjectId="43976"/>
    <cge:TPSR_Ref TObjectID="7366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43977">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4775.451754 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7367" ObjectName="SW-CX_SJ.CX_SJ_0376SW"/>
     <cge:Meas_Ref ObjectId="43977"/>
    <cge:TPSR_Ref TObjectID="7367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43960">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5026.030702 -450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7360" ObjectName="SW-CX_SJ.CX_SJ_0382SW"/>
     <cge:Meas_Ref ObjectId="43960"/>
    <cge:TPSR_Ref TObjectID="7360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43961">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5027.030702 -328.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7361" ObjectName="SW-CX_SJ.CX_SJ_0386SW"/>
     <cge:Meas_Ref ObjectId="43961"/>
    <cge:TPSR_Ref TObjectID="7361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4376.413158 -543.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7369" ObjectName="SW-CX_SJ.CX_SJ_0121SW"/>
     <cge:Meas_Ref ObjectId="43984"/>
    <cge:TPSR_Ref TObjectID="7369"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-43985">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.413158 -542.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="7370" ObjectName="SW-CX_SJ.CX_SJ_0122SW"/>
     <cge:Meas_Ref ObjectId="43985"/>
    <cge:TPSR_Ref TObjectID="7370"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185040">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.187003 -981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28099" ObjectName="SW-CX_SJ.CX_SJ_3411SW"/>
     <cge:Meas_Ref ObjectId="185040"/>
    <cge:TPSR_Ref TObjectID="28099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3911.187003 -1097.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28098" ObjectName="SW-CX_SJ.CX_SJ_3416SW"/>
     <cge:Meas_Ref ObjectId="185041"/>
    <cge:TPSR_Ref TObjectID="28098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185105">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.187003 -977.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28102" ObjectName="SW-CX_SJ.CX_SJ_3421SW"/>
     <cge:Meas_Ref ObjectId="185105"/>
    <cge:TPSR_Ref TObjectID="28102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185109">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.187003 -1093.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28103" ObjectName="SW-CX_SJ.CX_SJ_3426SW"/>
     <cge:Meas_Ref ObjectId="185109"/>
    <cge:TPSR_Ref TObjectID="28103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185151">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.187003 -987.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28106" ObjectName="SW-CX_SJ.CX_SJ_3431SW"/>
     <cge:Meas_Ref ObjectId="185151"/>
    <cge:TPSR_Ref TObjectID="28106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185152">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4632.187003 -1103.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28107" ObjectName="SW-CX_SJ.CX_SJ_3436SW"/>
     <cge:Meas_Ref ObjectId="185152"/>
    <cge:TPSR_Ref TObjectID="28107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185224">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.178771 -883.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28111" ObjectName="SW-CX_SJ.CX_SJ_3021SW"/>
     <cge:Meas_Ref ObjectId="185224"/>
    <cge:TPSR_Ref TObjectID="28111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-185244">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.178771 -565.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28112" ObjectName="SW-CX_SJ.CX_SJ_0021SW"/>
     <cge:Meas_Ref ObjectId="185244"/>
    <cge:TPSR_Ref TObjectID="28112"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-44021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4074.178771 -884.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28095" ObjectName="SW-CX_SJ.CX_SJ_3011SW"/>
     <cge:Meas_Ref ObjectId="44021"/>
    <cge:TPSR_Ref TObjectID="28095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4955.000000 -458.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4273.000000 -1143.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2ba7240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-654 4940,-632 4893,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b14560@0" ObjectIDZND0="g_2ae4c20@0" ObjectIDZND1="7355@x" Pin0InfoVect0LinkObjId="g_2ae4c20_0" Pin0InfoVect1LinkObjId="SW-43950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b14560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-654 4940,-632 4893,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-611 4893,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7355@1" ObjectIDZND0="g_2ae4c20@0" ObjectIDZND1="g_2b14560@0" Pin0InfoVect0LinkObjId="g_2ae4c20_0" Pin0InfoVect1LinkObjId="g_2b14560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-611 4893,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae3600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-632 4893,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b14560@0" ObjectIDND1="7355@x" ObjectIDZND0="g_2ae4c20@0" Pin0InfoVect0LinkObjId="g_2ae4c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b14560_0" Pin1InfoVect1LinkObjId="SW-43950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-632 4893,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296bcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4892,-697 4892,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ae4c20@1" ObjectIDZND0="g_2969330@0" Pin0InfoVect0LinkObjId="g_2969330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4892,-697 4892,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296beb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4893,-546 4893,-576 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10674@0" ObjectIDZND0="7355@0" Pin0InfoVect0LinkObjId="SW-43950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac28a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4893,-546 4893,-576 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aee540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-658 3950,-636 3903,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b14000@0" ObjectIDZND0="7356@x" ObjectIDZND1="g_2aed370@0" Pin0InfoVect0LinkObjId="SW-43951_0" Pin0InfoVect1LinkObjId="g_2aed370_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b14000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-658 3950,-636 3903,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aee730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-620 3903,-636 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="7356@1" ObjectIDZND0="g_2aed370@0" ObjectIDZND1="g_2b14000@0" Pin0InfoVect0LinkObjId="g_2aed370_0" Pin0InfoVect1LinkObjId="g_2b14000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43951_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-620 3903,-636 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aee920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-636 3903,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="7356@x" ObjectIDND1="g_2b14000@0" ObjectIDZND0="g_2aed370@0" Pin0InfoVect0LinkObjId="g_2aed370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43951_0" Pin1InfoVect1LinkObjId="g_2b14000_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-636 3903,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae83b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3902,-701 3902,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2aed370@1" ObjectIDZND0="g_2aeda60@0" Pin0InfoVect0LinkObjId="g_2aeda60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aed370_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3902,-701 3902,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae85a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3903,-546 3903,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7356@0" Pin0InfoVect0LinkObjId="SW-43951_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3903,-546 3903,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae9e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4836,-996 4836,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7379@0" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2abf3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4836,-996 4836,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b04d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-546 4307,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7351@1" Pin0InfoVect0LinkObjId="SW-43946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-546 4307,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b04f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-473 4307,-446 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7351@0" ObjectIDZND0="7354@1" Pin0InfoVect0LinkObjId="SW-43949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-473 4307,-446 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b06d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-418 4308,-387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7354@0" ObjectIDZND0="7353@1" Pin0InfoVect0LinkObjId="SW-43948_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-418 4308,-387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b67720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4372,-325 4393,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="7352@1" ObjectIDZND0="g_2b66f70@0" Pin0InfoVect0LinkObjId="g_2b66f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43947_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4372,-325 4393,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b67910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4336,-325 4308,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="7352@0" ObjectIDZND0="7353@x" ObjectIDZND1="41581@x" Pin0InfoVect0LinkObjId="SW-43948_0" Pin0InfoVect1LinkObjId="CB-CX_SJ.CX_SJ_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4336,-325 4308,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b67b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-350 4308,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="7353@0" ObjectIDZND0="7352@x" ObjectIDZND1="41581@x" Pin0InfoVect0LinkObjId="SW-43947_0" Pin0InfoVect1LinkObjId="CB-CX_SJ.CX_SJ_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-350 4308,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab8b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-546 4136,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7371@1" Pin0InfoVect0LinkObjId="SW-43986_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-546 4136,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab8d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4136,-472 4136,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7371@0" ObjectIDZND0="7373@1" Pin0InfoVect0LinkObjId="SW-43988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43986_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4136,-472 4136,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ababd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-417 4137,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7373@0" ObjectIDZND0="7372@1" Pin0InfoVect0LinkObjId="SW-43987_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-417 4137,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abadc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4174,-263 4174,-285 4137,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b15020@0" ObjectIDZND0="7372@x" ObjectIDZND1="34489@x" Pin0InfoVect0LinkObjId="SW-43987_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b15020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4174,-263 4174,-285 4137,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abafb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-350 4137,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="7372@0" ObjectIDZND0="g_2b15020@0" ObjectIDZND1="34489@x" Pin0InfoVect0LinkObjId="g_2b15020_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.032Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-350 4137,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2abb1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-285 4137,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="7372@x" ObjectIDND1="g_2b15020@0" ObjectIDZND0="34489@0" Pin0InfoVect0LinkObjId="EC-CX_SJ.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43987_0" Pin1InfoVect1LinkObjId="g_2b15020_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-285 4137,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3960,-546 3960,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7374@1" Pin0InfoVect0LinkObjId="SW-43994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3960,-546 3960,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3960,-472 3960,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7374@0" ObjectIDZND0="7376@1" Pin0InfoVect0LinkObjId="SW-43996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3960,-472 3960,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-417 3961,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7376@0" ObjectIDZND0="7375@1" Pin0InfoVect0LinkObjId="SW-43995_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-417 3961,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3998,-263 3998,-285 3961,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b14c40@0" ObjectIDZND0="7375@x" ObjectIDZND1="34488@x" Pin0InfoVect0LinkObjId="SW-43995_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b14c40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3998,-263 3998,-285 3961,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-350 3961,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="7375@0" ObjectIDZND0="g_2b14c40@0" ObjectIDZND1="34488@x" Pin0InfoVect0LinkObjId="g_2b14c40_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.033Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43995_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-350 3961,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2affb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-285 3961,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="7375@x" ObjectIDND1="g_2b14c40@0" ObjectIDZND0="34488@0" Pin0InfoVect0LinkObjId="EC-CX_SJ.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43995_0" Pin1InfoVect1LinkObjId="g_2b14c40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-285 3961,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3807,-546 3807,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7357@1" Pin0InfoVect0LinkObjId="SW-43952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3807,-546 3807,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3807,-472 3807,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7357@0" ObjectIDZND0="7359@1" Pin0InfoVect0LinkObjId="SW-43954_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3807,-472 3807,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-417 3808,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7359@0" ObjectIDZND0="7358@1" Pin0InfoVect0LinkObjId="SW-43953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43954_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-417 3808,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-263 3845,-285 3808,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b14940@0" ObjectIDZND0="7358@x" Pin0InfoVect0LinkObjId="SW-43953_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b14940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3845,-263 3845,-285 3808,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa3050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-350 3808,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="7358@0" ObjectIDZND0="g_2b14940@0" Pin0InfoVect0LinkObjId="g_2b14940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43953_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-350 3808,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa3270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3808,-285 3808,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" ObjectIDND0="7358@x" ObjectIDND1="g_2b14940@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43953_0" Pin1InfoVect1LinkObjId="g_2b14940_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3808,-285 3808,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a14ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-546 4570,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10674@0" ObjectIDZND0="7363@1" Pin0InfoVect0LinkObjId="SW-43968_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac28a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-546 4570,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a150c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4570,-472 4570,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7363@0" ObjectIDZND0="7365@1" Pin0InfoVect0LinkObjId="SW-43970_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43968_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4570,-472 4570,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-417 4571,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7365@0" ObjectIDZND0="7364@1" Pin0InfoVect0LinkObjId="SW-43969_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-417 4571,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4608,-263 4608,-285 4571,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b15400@0" ObjectIDZND0="7364@x" ObjectIDZND1="34490@x" Pin0InfoVect0LinkObjId="SW-43969_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.036Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b15400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4608,-263 4608,-285 4571,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5ba40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-350 4571,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="7364@0" ObjectIDZND0="g_2b15400@0" ObjectIDZND1="34490@x" Pin0InfoVect0LinkObjId="g_2b15400_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.036Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43969_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-350 4571,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5bc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4571,-285 4571,-158 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="7364@x" ObjectIDND1="g_2b15400@0" ObjectIDZND0="34490@0" Pin0InfoVect0LinkObjId="EC-CX_SJ.036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43969_0" Pin1InfoVect1LinkObjId="g_2b15400_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4571,-285 4571,-158 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2bbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-546 4789,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10674@0" ObjectIDZND0="7366@1" Pin0InfoVect0LinkObjId="SW-43976_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac28a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-546 4789,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4789,-472 4789,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7366@0" ObjectIDZND0="7368@1" Pin0InfoVect0LinkObjId="SW-43978_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43976_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4789,-472 4789,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2e280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4790,-417 4790,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7368@0" ObjectIDZND0="7367@1" Pin0InfoVect0LinkObjId="SW-43977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43978_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4790,-417 4790,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2e4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4827,-263 4827,-285 4790,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b157e0@0" ObjectIDZND0="7367@x" ObjectIDZND1="34491@x" Pin0InfoVect0LinkObjId="SW-43977_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.037Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b157e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4827,-263 4827,-285 4790,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2e6c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4790,-350 4790,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="7367@0" ObjectIDZND0="g_2b157e0@0" ObjectIDZND1="34491@x" Pin0InfoVect0LinkObjId="g_2b157e0_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.037Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4790,-350 4790,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2e8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4790,-285 4790,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="7367@x" ObjectIDND1="g_2b157e0@0" ObjectIDZND0="34491@0" Pin0InfoVect0LinkObjId="EC-CX_SJ.037Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43977_0" Pin1InfoVect1LinkObjId="g_2b157e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4790,-285 4790,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a731c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-546 5041,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10674@0" ObjectIDZND0="7360@1" Pin0InfoVect0LinkObjId="SW-43960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac28a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-546 5041,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a733e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-472 5041,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7360@0" ObjectIDZND0="7362@1" Pin0InfoVect0LinkObjId="SW-43962_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-472 5041,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8b0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-417 5042,-386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7362@0" ObjectIDZND0="7361@1" Pin0InfoVect0LinkObjId="SW-43961_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43962_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-417 5042,-386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8b2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5079,-263 5079,-285 5042,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b15bc0@0" ObjectIDZND0="7361@x" ObjectIDZND1="34492@x" Pin0InfoVect0LinkObjId="SW-43961_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.038Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b15bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5079,-263 5079,-285 5042,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8b4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-350 5042,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="7361@0" ObjectIDZND0="g_2b15bc0@0" ObjectIDZND1="34492@x" Pin0InfoVect0LinkObjId="g_2b15bc0_0" Pin0InfoVect1LinkObjId="EC-CX_SJ.038Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43961_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-350 5042,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8b700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5042,-285 5042,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="7361@x" ObjectIDND1="g_2b15bc0@0" ObjectIDZND0="34492@0" Pin0InfoVect0LinkObjId="EC-CX_SJ.038Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43961_0" Pin1InfoVect1LinkObjId="g_2b15bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5042,-285 5042,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8e6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-546 4391,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7369@0" Pin0InfoVect0LinkObjId="SW-43984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-546 4391,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac28a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4525,-564 4525,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="7370@0" ObjectIDZND0="10674@0" Pin0InfoVect0LinkObjId="g_2a94990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43985_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4525,-564 4525,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac4730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4391,-601 4391,-626 4448,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7369@1" ObjectIDZND0="7385@1" Pin0InfoVect0LinkObjId="SW-44030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-43984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4391,-601 4391,-626 4448,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac4950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4475,-626 4525,-626 4525,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="7385@0" ObjectIDZND0="7370@1" Pin0InfoVect0LinkObjId="SW-43985_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4475,-626 4525,-626 4525,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac4b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-546 4089,-584 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="7349@0" ObjectIDZND0="7377@0" Pin0InfoVect0LinkObjId="SW-44002_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-546 4089,-584 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac4d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-620 4089,-647 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="7377@1" ObjectIDZND0="7378@0" Pin0InfoVect0LinkObjId="SW-44003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-620 4089,-647 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac4fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-325 4308,-287 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="7353@x" ObjectIDND1="7352@x" ObjectIDZND0="41581@0" Pin0InfoVect0LinkObjId="CB-CX_SJ.CX_SJ_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-43948_0" Pin1InfoVect1LinkObjId="SW-43947_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-325 4308,-287 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b13e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4088,-679 4088,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="7378@1" ObjectIDZND0="7384@1" Pin0InfoVect0LinkObjId="g_2a920d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4088,-679 4088,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abf3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-942 4089,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28095@1" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2ae9e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44021_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-942 4089,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa8860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1119 3926,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28098@0" ObjectIDZND0="28097@1" Pin0InfoVect0LinkObjId="SW-185038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1119 3926,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa8ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1063 3926,-1039 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28097@0" ObjectIDZND0="28099@1" Pin0InfoVect0LinkObjId="SW-185040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1063 3926,-1039 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa8d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1003 3926,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28099@0" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2ae9e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1003 3926,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9c5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1115 4200,-1086 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28103@0" ObjectIDZND0="28101@1" Pin0InfoVect0LinkObjId="SW-185103_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185109_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1115 4200,-1086 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9c840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1059 4200,-1035 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28101@0" ObjectIDZND0="28102@1" Pin0InfoVect0LinkObjId="SW-185105_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185103_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1059 4200,-1035 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9d3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-999 4200,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28102@0" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2ae9e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185105_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-999 4200,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a33510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-1125 4647,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28107@0" ObjectIDZND0="28105@1" Pin0InfoVect0LinkObjId="SW-185149_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185152_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-1125 4647,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a33770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-1069 4647,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28105@0" ObjectIDZND0="28106@1" Pin0InfoVect0LinkObjId="SW-185151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185149_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-1069 4647,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a341a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-1009 4647,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28106@0" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2ae9e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-1009 4647,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad3c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4836,-1032 4836,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="7379@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4836,-1032 4836,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a91e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-906 4089,-867 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="28095@0" ObjectIDZND0="28094@1" Pin0InfoVect0LinkObjId="SW-44022_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44021_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-906 4089,-867 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a920d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4089,-840 4089,-797 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28094@0" ObjectIDZND0="7384@0" Pin0InfoVect0LinkObjId="g_2b13e10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-44022_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4089,-840 4089,-797 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a93a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-941 4742,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28111@1" ObjectIDZND0="7350@0" Pin0InfoVect0LinkObjId="g_2ae9e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185224_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-941 4742,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a93c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-875 4742,-905 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28109@1" ObjectIDZND0="28111@0" Pin0InfoVect0LinkObjId="SW-185224_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185222_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-875 4742,-905 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a93e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-848 4742,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28109@0" ObjectIDZND0="28113@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185222_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-848 4742,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a93ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-731 4742,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="28113@1" ObjectIDZND0="28110@1" Pin0InfoVect0LinkObjId="SW-185242_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a93e00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-731 4742,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a94760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-650 4742,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="28110@0" ObjectIDZND0="28112@1" Pin0InfoVect0LinkObjId="SW-185244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185242_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-650 4742,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a94990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4742,-587 4742,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="28112@0" ObjectIDZND0="10674@0" Pin0InfoVect0LinkObjId="g_2ac28a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185244_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4742,-587 4742,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a24660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-546 4960,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="10674@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ae4c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac28a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-546 4960,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a24e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4960,-463 4960,-436 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="28249@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4960,-463 4960,-436 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a2b6e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-1204 4278,-1193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2ae4c20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-1204 4278,-1193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a2b940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-1148 4278,-1131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="20215@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae4c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-1148 4278,-1131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a16970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4200,-1151 4200,-1215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="28103@1" ObjectIDZND0="38063@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185109_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4200,-1151 4200,-1215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a16e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3926,-1155 3926,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="28098@1" ObjectIDZND0="38080@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185041_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3926,-1155 3926,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a17c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-1161 4647,-1223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" ObjectIDND0="28107@1" ObjectIDZND0="38081@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-185152_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-1161 4647,-1223 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SJ"/>
</svg>