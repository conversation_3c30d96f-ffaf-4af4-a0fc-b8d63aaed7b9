<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-162" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-264 -1188 2509 1288">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape28">
    <polyline arcFlag="1" points="19,105 17,105 15,104 14,104 12,103 11,102 9,101 8,99 7,97 7,96 6,94 6,92 6,90 7,88 7,87 8,85 9,84 11,82 12,81 14,80 15,80 17,79 19,79 21,79 23,80 24,80 26,81 27,82 29,84 30,85 31,87 31,88 32,90 32,92 " stroke-width="0.0972"/>
    <polyline arcFlag="1" points="36,30 37,30 38,30 38,30 39,31 39,31 40,31 40,32 41,32 41,33 41,34 41,34 42,35 42,36 42,36 41,37 41,38 41,38 41,39 40,39 40,40 39,40 39,40 38,41 38,41 37,41 36,41 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="11" x2="26" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="10" x2="26" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="19" x2="19" y1="19" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="35" x2="35" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="2" x2="2" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.356919" x1="19" x2="36" y1="60" y2="60"/>
    <rect height="23" stroke-width="0.369608" width="12" x="13" y="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44164" x1="19" x2="36" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="2" x2="35" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.423529" x1="19" x2="19" y1="92" y2="26"/>
    <polyline arcFlag="1" points="36,41 37,41 38,41 38,42 39,42 39,42 40,43 40,43 41,44 41,44 41,45 41,45 42,46 42,47 42,47 41,48 41,49 41,49 41,50 40,50 40,51 39,51 39,52 38,52 38,52 37,52 36,52 " stroke-width="1"/>
    <polyline arcFlag="1" points="36,19 37,19 38,19 38,19 39,19 39,20 40,20 40,21 41,21 41,22 41,22 41,23 42,24 42,24 42,25 41,26 41,26 41,27 41,27 40,28 40,28 39,29 39,29 38,29 38,30 37,30 36,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="60" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="19" x2="19" y1="105" y2="116"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="32" x2="19" y1="92" y2="92"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="5" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="3" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="8" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="1" x2="12" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape25_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
   </symbol>
   <symbol id="switch2:shape25_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="36" y1="14" y2="44"/>
    <rect height="29" stroke-width="0.416609" width="9" x="14" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape25-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="7" x2="7" y1="50" y2="14"/>
    <rect height="26" stroke-width="0.416609" width="14" x="0" y="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="10" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="7" x2="7" y1="5" y2="14"/>
   </symbol>
   <symbol id="switch2:shape36_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
   </symbol>
   <symbol id="switch2:shape36_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="17" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="5" y1="39" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-16" x2="-4" y1="31" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-4" x2="3" y1="18" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="3" y1="38" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-9" x2="-16" y1="38" y2="31"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape36-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
   </symbol>
   <symbol id="switch2:shape45_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="25" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape45-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="9" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="34" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="4" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape39_0">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline points="64,96 64,89 " stroke-width="1"/>
    <polyline points="58,96 64,96 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="transformer2:shape39_1">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,96 1,33 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="38" y1="64" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="28" y1="71" y2="64"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="voltageTransformer:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="32" y2="97"/>
    <circle cx="24" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="26" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="6" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="8" y2="11"/>
    <circle cx="37" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="21" y1="15" y2="15"/>
    <circle cx="50" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="37" cy="24" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="73" x2="70" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="67" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="71" x2="71" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="65" x2="77" y1="31" y2="31"/>
    <rect height="27" stroke-width="0.416667" width="14" x="64" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="71" x2="71" y1="78" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="38" y1="78" y2="78"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="2" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="0" x2="12" y1="11" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="51" x2="51" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="48" x2="51" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="51" x2="54" y1="15" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape61">
    <ellipse cx="8" cy="7" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="19" y1="21" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="22" x2="19" y1="17" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="19" y1="20" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="18" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="23" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="6" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="11" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="5" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="10" x2="7" y1="17" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="7" y1="19" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_297ec40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_297fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2980750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2981420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2982680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29832a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2983d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_29847c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1e187c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2987a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29892b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_298a2c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298bf50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298cba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_298da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_298e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_298fb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2990dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2991580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2992fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2993ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2994490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2995930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29964a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_29974c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2998110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_29a6420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_29a6ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299a510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_299b9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1298" width="2519" x="-269" y="-1193"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac18a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 347.000000 886.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac2a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 336.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 361.000000 856.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 620.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac3ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1063.000000 602.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.000000 886.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 871.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31474d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 759.000000 856.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1078.000000 887.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 872.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147ca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 857.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3147fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 701.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3148230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1106.000000 686.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3148470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1131.000000 671.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31487a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1116.000000 535.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3148a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 520.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3148c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1130.000000 505.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3148f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 -47.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31491d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1966.000000 -62.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1991.000000 -77.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.000000 -46.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31499a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.000000 -61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1769.000000 -76.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1551.000000 -46.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1540.000000 -61.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314a3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1565.000000 -76.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314a6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1330.000000 -44.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314a940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1319.000000 -59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314ab80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1344.000000 -74.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314aeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.000000 -55.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314b110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1080.000000 -70.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314b350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 -85.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314b680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 755.000000 -44.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314b8e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 744.000000 -59.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314bb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 769.000000 -74.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314be50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 -41.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314c0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 538.000000 -56.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314c2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -71.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314c620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 865.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314cc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 851.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314d1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 96.000000 837.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314d760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 88.000000 822.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314dad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2083.000000 505.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314dd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2083.000000 491.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314df80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2083.000000 477.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314e1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2075.000000 462.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="540" x2="545" y1="1053" y2="1043"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="546" x2="528" y1="1044" y2="1033"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="522" x2="527" y1="1042" y2="1032"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="536" x2="540" y1="1038" y2="1032"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="548" x2="513" y1="1051" y2="1031"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="522" x2="540" y1="1042" y2="1052"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="540" x2="545" y1="1053" y2="1043"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="541" x2="546" y1="1088" y2="1078"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="547" x2="529" y1="1079" y2="1068"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="523" x2="528" y1="1077" y2="1067"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="537" x2="541" y1="1073" y2="1067"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="549" x2="514" y1="1086" y2="1066"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="523" x2="541" y1="1077" y2="1087"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="541" x2="546" y1="1088" y2="1078"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="921" x2="926" y1="1060" y2="1050"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="927" x2="909" y1="1051" y2="1040"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="903" x2="908" y1="1049" y2="1039"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="917" x2="921" y1="1045" y2="1039"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="929" x2="894" y1="1058" y2="1038"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="903" x2="921" y1="1049" y2="1059"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="921" x2="926" y1="1060" y2="1050"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="922" x2="927" y1="1095" y2="1085"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="928" x2="910" y1="1086" y2="1075"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="904" x2="909" y1="1084" y2="1074"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="918" x2="922" y1="1080" y2="1074"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="930" x2="895" y1="1093" y2="1073"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="904" x2="922" y1="1084" y2="1094"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="922" x2="927" y1="1095" y2="1085"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1245" x2="1250" y1="1061" y2="1051"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1251" x2="1233" y1="1052" y2="1041"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1227" x2="1232" y1="1050" y2="1040"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1241" x2="1245" y1="1046" y2="1040"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1253" x2="1218" y1="1059" y2="1039"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1227" x2="1245" y1="1050" y2="1060"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1245" x2="1250" y1="1061" y2="1051"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1246" x2="1251" y1="1096" y2="1086"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1252" x2="1234" y1="1087" y2="1076"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1228" x2="1233" y1="1085" y2="1075"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1242" x2="1246" y1="1081" y2="1075"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1254" x2="1219" y1="1094" y2="1074"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1228" x2="1246" y1="1085" y2="1095"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1246" x2="1251" y1="1096" y2="1086"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.684211 -0.000000 0.000000 -0.647059 8.631579 -31.647059)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1617" x2="1608" y1="320" y2="306"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1609" x2="1599" y1="306" y2="320"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1600" x2="1617" y1="320" y2="320"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1617" x2="1608" y1="320" y2="306"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.684211 -0.000000 0.000000 0.647059 8.631579 -419.352941)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1617" x2="1608" y1="320" y2="306"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1609" x2="1599" y1="306" y2="320"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1600" x2="1617" y1="320" y2="320"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1617" x2="1608" y1="320" y2="306"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.684211 -0.000000 0.000000 -0.647059 -863.368421 -92.647059)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1617" x2="1608" y1="320" y2="306"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1609" x2="1599" y1="306" y2="320"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1600" x2="1617" y1="320" y2="320"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1617" x2="1608" y1="320" y2="306"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(0.684211 -0.000000 0.000000 0.647059 -863.368421 -480.352941)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1617" x2="1608" y1="320" y2="306"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1609" x2="1599" y1="306" y2="320"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="1600" x2="1617" y1="320" y2="320"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1617" x2="1608" y1="320" y2="306"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b28890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 338.000000 -37.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b28af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 327.000000 -52.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b28d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 352.000000 -67.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b29a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 70.000000 508.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b29cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 70.000000 494.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b29ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 70.000000 480.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2a130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 62.000000 465.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 113.000000 -48.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 138.000000 -63.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 585.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 567.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2f7a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.000000 503.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2fae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 628.000000 488.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2fd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 473.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b30050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 706.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b302b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.000000 691.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b304f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 676.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="102" y="-985"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-109434">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -894.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21259" ObjectName="SW-SB_EJ.SB_EJ_3316SW"/>
     <cge:Meas_Ref ObjectId="109434"/>
    <cge:TPSR_Ref TObjectID="21259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109433">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -777.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21258" ObjectName="SW-SB_EJ.SB_EJ_3311SW"/>
     <cge:Meas_Ref ObjectId="109433"/>
    <cge:TPSR_Ref TObjectID="21258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 -946.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21260" ObjectName="SW-SB_EJ.SB_EJ_33167SW"/>
     <cge:Meas_Ref ObjectId="109435"/>
    <cge:TPSR_Ref TObjectID="21260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21262" ObjectName="SW-SB_EJ.SB_EJ_3326SW"/>
     <cge:Meas_Ref ObjectId="109438"/>
    <cge:TPSR_Ref TObjectID="21262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109437">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 -778.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21261" ObjectName="SW-SB_EJ.SB_EJ_3321SW"/>
     <cge:Meas_Ref ObjectId="109437"/>
    <cge:TPSR_Ref TObjectID="21261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110016">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.000000 -895.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21295" ObjectName="SW-SB_EJ.SB_EJ_3336SW"/>
     <cge:Meas_Ref ObjectId="110016"/>
    <cge:TPSR_Ref TObjectID="21295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.000000 -778.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21293" ObjectName="SW-SB_EJ.SB_EJ_3331SW"/>
     <cge:Meas_Ref ObjectId="110014"/>
    <cge:TPSR_Ref TObjectID="21293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110015">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 -826.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21294" ObjectName="SW-SB_EJ.SB_EJ_33317SW"/>
     <cge:Meas_Ref ObjectId="110015"/>
    <cge:TPSR_Ref TObjectID="21294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110017">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.000000 -883.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21296" ObjectName="SW-SB_EJ.SB_EJ_33360SW"/>
     <cge:Meas_Ref ObjectId="110017"/>
    <cge:TPSR_Ref TObjectID="21296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1591.000000 -676.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21256" ObjectName="SW-SB_EJ.SB_EJ_3901SW"/>
     <cge:Meas_Ref ObjectId="109430"/>
    <cge:TPSR_Ref TObjectID="21256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109431">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1667.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21257" ObjectName="SW-SB_EJ.SB_EJ_39010SW"/>
     <cge:Meas_Ref ObjectId="109431"/>
    <cge:TPSR_Ref TObjectID="21257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 814.000000 -692.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109584">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -713.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21303" ObjectName="SW-SB_EJ.SB_EJ_3011SW"/>
     <cge:Meas_Ref ObjectId="109584"/>
    <cge:TPSR_Ref TObjectID="21303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109595">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -431.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21304" ObjectName="SW-SB_EJ.SB_EJ_0011SW"/>
     <cge:Meas_Ref ObjectId="109595"/>
    <cge:TPSR_Ref TObjectID="21304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1443.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21268" ObjectName="SW-SB_EJ.SB_EJ_0901SW"/>
     <cge:Meas_Ref ObjectId="109467"/>
    <cge:TPSR_Ref TObjectID="21268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109469">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21269" ObjectName="SW-SB_EJ.SB_EJ_0311SW"/>
     <cge:Meas_Ref ObjectId="109469"/>
    <cge:TPSR_Ref TObjectID="21269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1910.000000 -428.000000)" xlink:href="#switch2:shape25_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109470">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -219.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21270" ObjectName="SW-SB_EJ.SB_EJ_0316SW"/>
     <cge:Meas_Ref ObjectId="109470"/>
    <cge:TPSR_Ref TObjectID="21270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109471">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21271" ObjectName="SW-SB_EJ.SB_EJ_0321SW"/>
     <cge:Meas_Ref ObjectId="109471"/>
    <cge:TPSR_Ref TObjectID="21271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21272" ObjectName="SW-SB_EJ.SB_EJ_0326SW"/>
     <cge:Meas_Ref ObjectId="109472"/>
    <cge:TPSR_Ref TObjectID="21272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1569.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21273" ObjectName="SW-SB_EJ.SB_EJ_0331SW"/>
     <cge:Meas_Ref ObjectId="109473"/>
    <cge:TPSR_Ref TObjectID="21273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109474">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1569.000000 -221.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21274" ObjectName="SW-SB_EJ.SB_EJ_0336SW"/>
     <cge:Meas_Ref ObjectId="109474"/>
    <cge:TPSR_Ref TObjectID="21274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 -354.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21275" ObjectName="SW-SB_EJ.SB_EJ_0341SW"/>
     <cge:Meas_Ref ObjectId="109475"/>
    <cge:TPSR_Ref TObjectID="21275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21276" ObjectName="SW-SB_EJ.SB_EJ_0346SW"/>
     <cge:Meas_Ref ObjectId="109476"/>
    <cge:TPSR_Ref TObjectID="21276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21264" ObjectName="SW-SB_EJ.SB_EJ_0351SW"/>
     <cge:Meas_Ref ObjectId="109441"/>
    <cge:TPSR_Ref TObjectID="21264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -243.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21265" ObjectName="SW-SB_EJ.SB_EJ_0353SW"/>
     <cge:Meas_Ref ObjectId="109442"/>
    <cge:TPSR_Ref TObjectID="21265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -164.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21266" ObjectName="SW-SB_EJ.SB_EJ_0356SW"/>
     <cge:Meas_Ref ObjectId="109443"/>
    <cge:TPSR_Ref TObjectID="21266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109477">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -354.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21277" ObjectName="SW-SB_EJ.SB_EJ_0362SW"/>
     <cge:Meas_Ref ObjectId="109477"/>
    <cge:TPSR_Ref TObjectID="21277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109478">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -222.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21278" ObjectName="SW-SB_EJ.SB_EJ_0366SW"/>
     <cge:Meas_Ref ObjectId="109478"/>
    <cge:TPSR_Ref TObjectID="21278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109479">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -357.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21279" ObjectName="SW-SB_EJ.SB_EJ_0372SW"/>
     <cge:Meas_Ref ObjectId="109479"/>
    <cge:TPSR_Ref TObjectID="21279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109480">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -225.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21280" ObjectName="SW-SB_EJ.SB_EJ_0376SW"/>
     <cge:Meas_Ref ObjectId="109480"/>
    <cge:TPSR_Ref TObjectID="21280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 908.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21263" ObjectName="SW-SB_EJ.SB_EJ_33267SW"/>
     <cge:Meas_Ref ObjectId="109439"/>
    <cge:TPSR_Ref TObjectID="21263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-110018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1225.000000 -945.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21297" ObjectName="SW-SB_EJ.SB_EJ_33367SW"/>
     <cge:Meas_Ref ObjectId="110018"/>
    <cge:TPSR_Ref TObjectID="21297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109948">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 -116.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21291" ObjectName="SW-SB_EJ.SB_EJ_03567SW"/>
     <cge:Meas_Ref ObjectId="109948"/>
    <cge:TPSR_Ref TObjectID="21291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1054.000000 -185.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21290" ObjectName="SW-SB_EJ.SB_EJ_03560SW"/>
     <cge:Meas_Ref ObjectId="109947"/>
    <cge:TPSR_Ref TObjectID="21290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2043.000000 -91.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 21.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -712.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41071" ObjectName="SW-SB_EJ.SB_EJ_3021SW"/>
     <cge:Meas_Ref ObjectId="244333"/>
    <cge:TPSR_Ref TObjectID="41071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244304">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.000000 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41069" ObjectName="SW-SB_EJ.SB_EJ_0122SW"/>
     <cge:Meas_Ref ObjectId="244304"/>
    <cge:TPSR_Ref TObjectID="41069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 979.000000 -434.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41068" ObjectName="SW-SB_EJ.SB_EJ_0121SW"/>
     <cge:Meas_Ref ObjectId="244303"/>
    <cge:TPSR_Ref TObjectID="41068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.043478 498.000000 -651.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41072" ObjectName="SW-SB_EJ.SB_EJ_30210SW"/>
     <cge:Meas_Ref ObjectId="244334"/>
    <cge:TPSR_Ref TObjectID="41072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244281">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -365.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41064" ObjectName="SW-SB_EJ.SB_EJ_0392SW"/>
     <cge:Meas_Ref ObjectId="244281"/>
    <cge:TPSR_Ref TObjectID="41064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244282">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -225.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41065" ObjectName="SW-SB_EJ.SB_EJ_0396SW"/>
     <cge:Meas_Ref ObjectId="244282"/>
    <cge:TPSR_Ref TObjectID="41065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244257">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -359.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41061" ObjectName="SW-SB_EJ.SB_EJ_0382SW"/>
     <cge:Meas_Ref ObjectId="244257"/>
    <cge:TPSR_Ref TObjectID="41061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244256">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -227.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41060" ObjectName="SW-SB_EJ.SB_EJ_0386SW"/>
     <cge:Meas_Ref ObjectId="244256"/>
    <cge:TPSR_Ref TObjectID="41060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -431.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41074" ObjectName="SW-SB_EJ.SB_EJ_0022SW"/>
     <cge:Meas_Ref ObjectId="244336"/>
    <cge:TPSR_Ref TObjectID="41074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 160.000000 -196.000000)" xlink:href="#switch2:shape45_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41066" ObjectName="SW-SB_EJ.SB_EJ_03967SW"/>
     <cge:Meas_Ref ObjectId="244283"/>
    <cge:TPSR_Ref TObjectID="41066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1369.000000 -116.000000)" xlink:href="#switch2:shape36_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244278">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 315.000000 -428.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41062" ObjectName="SW-SB_EJ.SB_EJ_0902SW"/>
     <cge:Meas_Ref ObjectId="244278"/>
    <cge:TPSR_Ref TObjectID="41062"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-SB_EJ.SB_EJ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="149,-767 1676,-767 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21254" ObjectName="BS-SB_EJ.SB_EJ_3IM"/>
    <cge:TPSR_Ref TObjectID="21254"/></metadata>
   <polyline fill="none" opacity="0" points="149,-767 1676,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_EJ.SB_EJ_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="971,-417 2129,-417 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="21253" ObjectName="BS-SB_EJ.SB_EJ_9IM"/>
    <cge:TPSR_Ref TObjectID="21253"/></metadata>
   <polyline fill="none" opacity="0" points="971,-417 2129,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-SB_EJ.SB_EJ_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="166,-419 855,-419 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="41078" ObjectName="BS-SB_EJ.SB_EJ_9IIM"/>
    <cge:TPSR_Ref TObjectID="41078"/></metadata>
   <polyline fill="none" opacity="0" points="166,-419 855,-419 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-SB_EJ.SB_EJ_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1090.000000 -33.000000)" xlink:href="#capacitor:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="40863" ObjectName="CB-SB_EJ.SB_EJ_Cb1"/>
    <cge:TPSR_Ref TObjectID="40863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-SB_EJ.SB_EJ_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 212.000000 -67.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41589" ObjectName="CB-SB_EJ.SB_EJ_Cb2"/>
    <cge:TPSR_Ref TObjectID="41589"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-SB_EJ.SB_EJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29763"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -554.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -554.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21300" ObjectName="TF-SB_EJ.SB_EJ_1T"/>
    <cge:TPSR_Ref TObjectID="21300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_EJ.SB_EJ_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29767"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 -559.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 790.000000 -559.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21301" ObjectName="TF-SB_EJ.SB_EJ_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_EJ.SB_EJ_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="29771"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.000000 -500.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1901.000000 -500.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="21302" ObjectName="TF-SB_EJ.SB_EJ_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-SB_EJ.SB_EJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="62247"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 -553.000000)" xlink:href="#transformer2:shape39_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 -553.000000)" xlink:href="#transformer2:shape39_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="41075" ObjectName="TF-SB_EJ.SB_EJ_2T"/>
    <cge:TPSR_Ref TObjectID="41075"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cb9ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1417.000000 -500.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a48040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1982.000000 -98.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c8e530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1830.000000 -98.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cb3cc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.000000 -99.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cbee60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 820.000000 -101.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a50300">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 620.000000 -104.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aaf6b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 429.000000 -1047.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a33e40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.000000 -1049.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296e3d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1135.000000 -1045.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df3820">
    <use class="BV-10KV" transform="matrix(0.600000 -0.000000 0.000000 -0.518519 1448.000000 -514.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c319c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1147.000000 -83.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c59080">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -104.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0e150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 -123.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1fa20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -500.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b24110">
    <use class="BV-10KV" transform="matrix(0.600000 -0.000000 0.000000 -0.518519 320.000000 -514.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2c440">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 382.000000 -76.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2d140">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1790.000000 -68.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b31530">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 603.000000 -545.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -161.000000 -1001.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-247537" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -826.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="247537" ObjectName="SB_EJ:SB_EJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-247538" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -138.000000 -788.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="247538" ObjectName="SB_EJ:SB_EJ_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-247537" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -907.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="247537" ObjectName="SB_EJ:SB_EJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-247537" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -140.000000 -867.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="247537" ObjectName="SB_EJ:SB_EJ_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-244444" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 696.000000 -566.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244444" ObjectName="SB_EJ:SB_EJ_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-244443" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 695.000000 -583.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244443" ObjectName="SB_EJ:SB_EJ_2T_Tp"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109263" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 -886.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109263" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21288"/>
     <cge:Term_Ref ObjectID="29737"/>
    <cge:TPSR_Ref TObjectID="21288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109264" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 -886.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109264" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21288"/>
     <cge:Term_Ref ObjectID="29737"/>
    <cge:TPSR_Ref TObjectID="21288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109253" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 403.000000 -886.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109253" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21288"/>
     <cge:Term_Ref ObjectID="29737"/>
    <cge:TPSR_Ref TObjectID="21288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109277" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -887.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109277" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21281"/>
     <cge:Term_Ref ObjectID="29723"/>
    <cge:TPSR_Ref TObjectID="21281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109278" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -887.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21281"/>
     <cge:Term_Ref ObjectID="29723"/>
    <cge:TPSR_Ref TObjectID="21281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109267" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -887.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109267" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21281"/>
     <cge:Term_Ref ObjectID="29723"/>
    <cge:TPSR_Ref TObjectID="21281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109291" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -887.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21292"/>
     <cge:Term_Ref ObjectID="29745"/>
    <cge:TPSR_Ref TObjectID="21292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109292" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -887.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21292"/>
     <cge:Term_Ref ObjectID="29745"/>
    <cge:TPSR_Ref TObjectID="21292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109281" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -887.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21292"/>
     <cge:Term_Ref ObjectID="29745"/>
    <cge:TPSR_Ref TObjectID="21292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109234" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -700.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109234" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21298"/>
     <cge:Term_Ref ObjectID="29757"/>
    <cge:TPSR_Ref TObjectID="21298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109235" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -700.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109235" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21298"/>
     <cge:Term_Ref ObjectID="29757"/>
    <cge:TPSR_Ref TObjectID="21298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109228" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1173.000000 -700.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109228" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21298"/>
     <cge:Term_Ref ObjectID="29757"/>
    <cge:TPSR_Ref TObjectID="21298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109237" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109237" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21299"/>
     <cge:Term_Ref ObjectID="29759"/>
    <cge:TPSR_Ref TObjectID="21299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109238" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109238" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21299"/>
     <cge:Term_Ref ObjectID="29759"/>
    <cge:TPSR_Ref TObjectID="21299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109231" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1172.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109231" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21299"/>
     <cge:Term_Ref ObjectID="29759"/>
    <cge:TPSR_Ref TObjectID="21299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.000000 47.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21282"/>
     <cge:Term_Ref ObjectID="29725"/>
    <cge:TPSR_Ref TObjectID="21282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.000000 47.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21282"/>
     <cge:Term_Ref ObjectID="29725"/>
    <cge:TPSR_Ref TObjectID="21282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109295" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2037.000000 47.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21282"/>
     <cge:Term_Ref ObjectID="29725"/>
    <cge:TPSR_Ref TObjectID="21282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21283"/>
     <cge:Term_Ref ObjectID="29727"/>
    <cge:TPSR_Ref TObjectID="21283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21283"/>
     <cge:Term_Ref ObjectID="29727"/>
    <cge:TPSR_Ref TObjectID="21283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109309" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1814.000000 46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21283"/>
     <cge:Term_Ref ObjectID="29727"/>
    <cge:TPSR_Ref TObjectID="21283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109333" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21284"/>
     <cge:Term_Ref ObjectID="29729"/>
    <cge:TPSR_Ref TObjectID="21284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109334" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109334" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21284"/>
     <cge:Term_Ref ObjectID="29729"/>
    <cge:TPSR_Ref TObjectID="21284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109323" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 46.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109323" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21284"/>
     <cge:Term_Ref ObjectID="29729"/>
    <cge:TPSR_Ref TObjectID="21284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 44.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109347" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21285"/>
     <cge:Term_Ref ObjectID="29731"/>
    <cge:TPSR_Ref TObjectID="21285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 44.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21285"/>
     <cge:Term_Ref ObjectID="29731"/>
    <cge:TPSR_Ref TObjectID="21285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109337" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1385.000000 44.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109337" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21285"/>
     <cge:Term_Ref ObjectID="29731"/>
    <cge:TPSR_Ref TObjectID="21285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109388" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 55.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21289"/>
     <cge:Term_Ref ObjectID="29739"/>
    <cge:TPSR_Ref TObjectID="21289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109389" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 55.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21289"/>
     <cge:Term_Ref ObjectID="29739"/>
    <cge:TPSR_Ref TObjectID="21289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109379" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 55.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21289"/>
     <cge:Term_Ref ObjectID="29739"/>
    <cge:TPSR_Ref TObjectID="21289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109361" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 44.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109361" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21286"/>
     <cge:Term_Ref ObjectID="29733"/>
    <cge:TPSR_Ref TObjectID="21286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109362" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 44.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109362" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21286"/>
     <cge:Term_Ref ObjectID="29733"/>
    <cge:TPSR_Ref TObjectID="21286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109351" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 811.000000 44.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109351" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21286"/>
     <cge:Term_Ref ObjectID="29733"/>
    <cge:TPSR_Ref TObjectID="21286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-109375" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 41.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109375" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21287"/>
     <cge:Term_Ref ObjectID="29735"/>
    <cge:TPSR_Ref TObjectID="21287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-109376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 41.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109376" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21287"/>
     <cge:Term_Ref ObjectID="29735"/>
    <cge:TPSR_Ref TObjectID="21287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-109365" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 603.000000 41.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109365" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21287"/>
     <cge:Term_Ref ObjectID="29735"/>
    <cge:TPSR_Ref TObjectID="21287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-109220" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -865.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109220" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21254"/>
     <cge:Term_Ref ObjectID="29670"/>
    <cge:TPSR_Ref TObjectID="21254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-109221" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -865.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109221" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21254"/>
     <cge:Term_Ref ObjectID="29670"/>
    <cge:TPSR_Ref TObjectID="21254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-109222" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -865.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109222" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21254"/>
     <cge:Term_Ref ObjectID="29670"/>
    <cge:TPSR_Ref TObjectID="21254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-109223" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 150.000000 -865.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109223" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21254"/>
     <cge:Term_Ref ObjectID="29670"/>
    <cge:TPSR_Ref TObjectID="21254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-109213" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2138.000000 -506.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109213" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21253"/>
     <cge:Term_Ref ObjectID="29669"/>
    <cge:TPSR_Ref TObjectID="21253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-109214" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2138.000000 -506.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109214" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21253"/>
     <cge:Term_Ref ObjectID="29669"/>
    <cge:TPSR_Ref TObjectID="21253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-109215" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2138.000000 -506.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109215" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21253"/>
     <cge:Term_Ref ObjectID="29669"/>
    <cge:TPSR_Ref TObjectID="21253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-109216" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2138.000000 -506.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21253"/>
     <cge:Term_Ref ObjectID="29669"/>
    <cge:TPSR_Ref TObjectID="21253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-109227" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -617.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109227" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21300"/>
     <cge:Term_Ref ObjectID="29764"/>
    <cge:TPSR_Ref TObjectID="21300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-109240" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1131.000000 -617.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="109240" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="21300"/>
     <cge:Term_Ref ObjectID="29764"/>
    <cge:TPSR_Ref TObjectID="21300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-244404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 37.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41059"/>
     <cge:Term_Ref ObjectID="62213"/>
    <cge:TPSR_Ref TObjectID="41059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-244405" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 37.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41059"/>
     <cge:Term_Ref ObjectID="62213"/>
    <cge:TPSR_Ref TObjectID="41059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-244401" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 393.000000 37.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41059"/>
     <cge:Term_Ref ObjectID="62213"/>
    <cge:TPSR_Ref TObjectID="41059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-244451" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 125.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244451" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41078"/>
     <cge:Term_Ref ObjectID="62249"/>
    <cge:TPSR_Ref TObjectID="41078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-244452" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 125.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244452" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41078"/>
     <cge:Term_Ref ObjectID="62249"/>
    <cge:TPSR_Ref TObjectID="41078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-244453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 125.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41078"/>
     <cge:Term_Ref ObjectID="62249"/>
    <cge:TPSR_Ref TObjectID="41078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-244454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 125.000000 -504.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244454" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41078"/>
     <cge:Term_Ref ObjectID="62249"/>
    <cge:TPSR_Ref TObjectID="41078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-244410" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 51.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244410" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41063"/>
     <cge:Term_Ref ObjectID="62221"/>
    <cge:TPSR_Ref TObjectID="41063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-244407" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 184.000000 51.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244407" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41063"/>
     <cge:Term_Ref ObjectID="62221"/>
    <cge:TPSR_Ref TObjectID="41063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-244439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -504.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41073"/>
     <cge:Term_Ref ObjectID="62241"/>
    <cge:TPSR_Ref TObjectID="41073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-244440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -504.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244440" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41073"/>
     <cge:Term_Ref ObjectID="62241"/>
    <cge:TPSR_Ref TObjectID="41073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-244430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.000000 -504.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244430" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41073"/>
     <cge:Term_Ref ObjectID="62241"/>
    <cge:TPSR_Ref TObjectID="41073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-244426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -708.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41070"/>
     <cge:Term_Ref ObjectID="62235"/>
    <cge:TPSR_Ref TObjectID="41070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-244427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -708.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244427" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41070"/>
     <cge:Term_Ref ObjectID="62235"/>
    <cge:TPSR_Ref TObjectID="41070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-244417" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 697.000000 -708.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="244417" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="41070"/>
     <cge:Term_Ref ObjectID="62235"/>
    <cge:TPSR_Ref TObjectID="41070"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="141" x="-151" y="-1060"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-198" y="-1077"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2023" y="-316"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2023" y="-316"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1587" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1587" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1354" y="-319"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1354" y="-319"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1118" y="-325"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1118" y="-325"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="805" y="-319"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="805" y="-319"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="599" y="-322"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="599" y="-322"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="98" x="1885" y="-618"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="98" x="1885" y="-618"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="97" x="792" y="-550"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="97" x="792" y="-550"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="45" x="1267" y="-610"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="45" x="1267" y="-610"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="877" y="-870"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="877" y="-870"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="501" y="-870"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="501" y="-870"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1201" y="-870"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1201" y="-870"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="61" x="-247" y="-669"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="61" x="-247" y="-669"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="91" y="-1092"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="91" y="-1092"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="93" y="-1050"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="93" y="-1050"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="101" y="-986"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="101" y="-986"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="401" y="-324"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="401" y="-324"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1809" y="-318"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1809" y="-318"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="630" y="-628"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="630" y="-628"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="898" y="-475"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="898" y="-475"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="246" y="-341"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="246" y="-341"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="141" x="-151" y="-1060"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-198" y="-1077"/></g>
   <g href="35kV鄂嘉变SB_EJ_031间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2023" y="-316"/></g>
   <g href="35kV鄂嘉变SB_EJ_033间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1587" y="-318"/></g>
   <g href="35kV鄂嘉变SB_EJ_034间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1354" y="-319"/></g>
   <g href="35kV鄂嘉变SB_EJ_035间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1118" y="-325"/></g>
   <g href="35kV鄂嘉变SB_EJ_036间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="805" y="-319"/></g>
   <g href="35kV鄂嘉变SB_EJ_037间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="599" y="-322"/></g>
   <g href="35kV鄂嘉变SB_EJ_Zyb间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="98" x="1885" y="-618"/></g>
   <g href="35kV鄂嘉变SB_EJ_Zyb间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="97" x="792" y="-550"/></g>
   <g href="35kV鄂嘉变1＃主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="45" x="1267" y="-610"/></g>
   <g href="35kV鄂嘉变SB_EJ_332间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="877" y="-870"/></g>
   <g href="35kV鄂嘉变SB_EJ_331间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="501" y="-870"/></g>
   <g href="35kV鄂嘉变SB_EJ_333间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1201" y="-870"/></g>
   <g href="35kV鄂嘉变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="61" x="-247" y="-669"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="91" y="-1092"/></g>
   <g href="cx_配调_配网接线图35_双柏.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="93" y="-1050"/></g>
   <g href="AVC鄂嘉站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="101" y="-986"/></g>
   <g href="35kV鄂嘉变SB_EJ_038间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="401" y="-324"/></g>
   <g href="35kV鄂嘉变SB_EJ_032间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1809" y="-318"/></g>
   <g href="35kV鄂嘉变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="630" y="-628"/></g>
   <g href="35kV鄂嘉变SB_EJ_012间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="898" y="-475"/></g>
   <g href="35kV鄂嘉变SB_EJ_039间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="246" y="-341"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="579" x2="579" y1="-1079" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="596" x2="596" y1="-1079" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="569" x2="569" y1="-1086" y2="-1051"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="549" x2="569" y1="-1086" y2="-1086"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="548" x2="568" y1="-1051" y2="-1051"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="507" x2="507" y1="-1085" y2="-1050"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="487" x2="514" y1="-1058" y2="-1058"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="508" x2="515" y1="-1076" y2="-1076"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="515" x2="515" y1="-1083" y2="-1069"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="515" x2="515" y1="-1064" y2="-1050"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="479" x2="479" y1="-1077" y2="-1042"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="960" x2="960" y1="-1086" y2="-1066"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="977" x2="977" y1="-1086" y2="-1066"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="950" x2="950" y1="-1093" y2="-1058"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="930" x2="950" y1="-1093" y2="-1093"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="929" x2="949" y1="-1058" y2="-1058"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="888" x2="888" y1="-1092" y2="-1057"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="868" x2="895" y1="-1065" y2="-1065"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="889" x2="896" y1="-1083" y2="-1083"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="896" x2="896" y1="-1090" y2="-1076"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="896" x2="896" y1="-1071" y2="-1057"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="860" x2="860" y1="-1084" y2="-1049"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1301" x2="1301" y1="-1087" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1274" x2="1274" y1="-1094" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1254" x2="1274" y1="-1094" y2="-1094"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1253" x2="1273" y1="-1059" y2="-1059"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1212" x2="1212" y1="-1093" y2="-1058"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1192" x2="1219" y1="-1066" y2="-1066"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1213" x2="1220" y1="-1084" y2="-1084"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1220" x2="1220" y1="-1091" y2="-1077"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1220" x2="1220" y1="-1072" y2="-1058"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1184" x2="1184" y1="-1085" y2="-1050"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1284" x2="1284" y1="-1087" y2="-1067"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,238,0)" stroke-width="1" x1="169" x2="169" y1="-211" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="162" x2="177" y1="-261" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="165" x2="173" y1="-264" y2="-264"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="166" x2="172" y1="-267" y2="-267"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="169" x2="169" y1="-237" y2="-261"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="169" x2="238" y1="-67" y2="-67"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="213" x2="221" y1="-194" y2="-194"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="221" x2="216" y1="-194" y2="-186"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="213" x2="216" y1="-194" y2="-187"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="610" x2="610" y1="-623" y2="-604"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="571" x2="610" y1="-623" y2="-623"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2afec70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1562.000000 -552.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df1060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1468.000000 -525.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b0c3c0">
    <use class="BV-0KV" transform="matrix(1.720536 0.000000 0.000000 -1.533333 2024.000000 -38.000000)" xlink:href="#voltageTransformer:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b12490">
    <use class="BV-0KV" transform="matrix(1.720536 0.000000 0.000000 -1.533333 1350.000000 -63.000000)" xlink:href="#voltageTransformer:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b21ae0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 340.000000 -525.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.031Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2009.000000 -8.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34342" ObjectName="EC-SB_EJ.031Ld"/>
    <cge:TPSR_Ref TObjectID="34342"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.032Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1795.000000 -10.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34343" ObjectName="EC-SB_EJ.032Ld"/>
    <cge:TPSR_Ref TObjectID="34343"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.033Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1573.000000 -10.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34344" ObjectName="EC-SB_EJ.033Ld"/>
    <cge:TPSR_Ref TObjectID="34344"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.034Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1340.000000 -11.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34345" ObjectName="EC-SB_EJ.034Ld"/>
    <cge:TPSR_Ref TObjectID="34345"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.036Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 791.000000 -11.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34346" ObjectName="EC-SB_EJ.036Ld"/>
    <cge:TPSR_Ref TObjectID="34346"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.037Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 585.000000 -14.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37569" ObjectName="EC-SB_EJ.037Ld"/>
    <cge:TPSR_Ref TObjectID="37569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-SB_EJ.038Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 387.000000 -21.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="42307" ObjectName="EC-SB_EJ.038Ld"/>
    <cge:TPSR_Ref TObjectID="42307"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2cabee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-767 486,-782 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDZND0="21258@0" Pin0InfoVect0LinkObjId="SW-109433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-767 486,-782 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cce6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-818 486,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21258@1" ObjectIDZND0="21288@0" Pin0InfoVect0LinkObjId="SW-109927_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-818 486,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb9dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-875 486,-899 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21288@1" ObjectIDZND0="21259@0" Pin0InfoVect0LinkObjId="SW-109434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109927_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-875 486,-899 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cadcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,-1011 539,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2cc86f0@0" ObjectIDZND0="21260@1" Pin0InfoVect0LinkObjId="SW-109435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2cc86f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,-1011 539,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cb73c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-767 868,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDZND0="21261@0" Pin0InfoVect0LinkObjId="SW-109437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,-767 868,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d05bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-819 868,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21261@1" ObjectIDZND0="21281@0" Pin0InfoVect0LinkObjId="SW-109655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,-819 868,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c8eab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-876 868,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21281@1" ObjectIDZND0="21262@0" Pin0InfoVect0LinkObjId="SW-109438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,-876 868,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d02820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-767 1192,-783 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDZND0="21293@0" Pin0InfoVect0LinkObjId="SW-110014_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-767 1192,-783 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d02a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-950 1234,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21295@x" ObjectIDND1="g_296e3d0@0" ObjectIDND2="37764@1" ObjectIDZND0="21297@0" Pin0InfoVect0LinkObjId="SW-110018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110016_0" Pin1InfoVect1LinkObjId="g_296e3d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-950 1234,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2cbd510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1676,-693 1676,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21257@0" ObjectIDZND0="g_2cbd770@0" Pin0InfoVect0LinkObjId="g_2cbd770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109431_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1676,-693 1676,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af0520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-767 821,-749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-767 821,-749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2af0780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="821,-697 821,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="21301@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="821,-697 821,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfa830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-767 1237,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDZND0="21303@1" Pin0InfoVect0LinkObjId="SW-109584_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-767 1237,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfaa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-718 1237,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21303@0" ObjectIDZND0="21298@1" Pin0InfoVect0LinkObjId="SW-109583_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109584_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-718 1237,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfacf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-666 1237,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="21298@0" ObjectIDZND0="21300@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109583_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-666 1237,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a35e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-559 1237,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="21300@0" ObjectIDZND0="21299@1" Pin0InfoVect0LinkObjId="SW-109594_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dfacf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-559 1237,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-498 1237,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21299@0" ObjectIDZND0="21304@1" Pin0InfoVect0LinkObjId="SW-109595_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-498 1237,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab9080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1237,-436 1237,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21304@0" ObjectIDZND0="21253@0" Pin0InfoVect0LinkObjId="g_2b14850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1237,-436 1237,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a18f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1917,-417 1917,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21253@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab9080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1917,-417 1917,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a19140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1917,-485 1917,-505 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="21302@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1917,-485 1917,-505 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab08a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-417 2014,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21253@0" ObjectIDZND0="21269@1" Pin0InfoVect0LinkObjId="SW-109469_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab9080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-417 2014,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab0b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-356 2014,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21269@0" ObjectIDZND0="21282@1" Pin0InfoVect0LinkObjId="SW-109698_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-356 2014,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a47de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-295 2014,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21282@0" ObjectIDZND0="21270@1" Pin0InfoVect0LinkObjId="SW-109470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109698_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-295 2014,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-417 1800,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21253@0" ObjectIDZND0="21271@1" Pin0InfoVect0LinkObjId="SW-109471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab9080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-417 1800,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-358 1800,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21271@0" ObjectIDZND0="21283@1" Pin0InfoVect0LinkObjId="SW-109740_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-358 1800,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8e2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-297 1800,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21283@0" ObjectIDZND0="21272@1" Pin0InfoVect0LinkObjId="SW-109472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109740_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-297 1800,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb3800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-358 1578,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21273@0" ObjectIDZND0="21284@1" Pin0InfoVect0LinkObjId="SW-109778_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-358 1578,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cb3a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-297 1578,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21284@0" ObjectIDZND0="21274@1" Pin0InfoVect0LinkObjId="SW-109474_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109778_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-297 1578,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a23e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-226 1578,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="21274@0" ObjectIDZND0="34344@x" ObjectIDZND1="g_2cb3cc0@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.033Ld_0" Pin0InfoVect1LinkObjId="g_2cb3cc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-226 1578,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a240a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-157 1578,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="21274@x" ObjectIDND1="g_2cb3cc0@0" ObjectIDZND0="34344@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.033Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109474_0" Pin1InfoVect1LinkObjId="g_2cb3cc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-157 1578,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df76a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-417 1345,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="21253@0" ObjectIDZND0="21275@1" Pin0InfoVect0LinkObjId="SW-109475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ab9080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-417 1345,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-359 1345,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21275@0" ObjectIDZND0="21285@1" Pin0InfoVect0LinkObjId="SW-109816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-359 1345,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df7b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-298 1345,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21285@0" ObjectIDZND0="21276@1" Pin0InfoVect0LinkObjId="SW-109476_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-298 1345,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-354 1109,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21264@0" ObjectIDZND0="21289@1" Pin0InfoVect0LinkObjId="SW-109946_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-354 1109,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c8b410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-304 1109,-284 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21289@0" ObjectIDZND0="21265@1" Pin0InfoVect0LinkObjId="SW-109442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109946_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-304 1109,-284 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbe740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-419 796,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41078@0" ObjectIDZND0="21277@1" Pin0InfoVect0LinkObjId="SW-109477_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c44300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-419 796,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbe9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-359 796,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21277@0" ObjectIDZND0="21286@1" Pin0InfoVect0LinkObjId="SW-109856_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109477_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-359 796,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2cbec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-298 796,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21286@0" ObjectIDZND0="21278@1" Pin0InfoVect0LinkObjId="SW-109478_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109856_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-298 796,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-419 590,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41078@0" ObjectIDZND0="21279@1" Pin0InfoVect0LinkObjId="SW-109479_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c44300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-419 590,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4fe40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-362 590,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="21279@0" ObjectIDZND0="21287@1" Pin0InfoVect0LinkObjId="SW-109892_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109479_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-362 590,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a500a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-301 590,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="21287@0" ObjectIDZND0="21280@1" Pin0InfoVect0LinkObjId="SW-109480_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109892_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-301 590,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a258a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-951 539,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21259@x" ObjectIDND1="g_2aaf6b0@0" ObjectIDND2="49390@1" ObjectIDZND0="21260@0" Pin0InfoVect0LinkObjId="SW-109435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109434_0" Pin1InfoVect1LinkObjId="g_2aaf6b0_0" Pin1InfoVect2LinkObjId="g_2df6210_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-951 539,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a26370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-935 486,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21259@1" ObjectIDZND0="21260@x" ObjectIDZND1="g_2aaf6b0@0" ObjectIDZND2="49390@1" Pin0InfoVect0LinkObjId="SW-109435_0" Pin0InfoVect1LinkObjId="g_2aaf6b0_0" Pin0InfoVect2LinkObjId="g_2df6210_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="486,-935 486,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b008b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="917,-1008 917,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a32770@0" ObjectIDZND0="21263@1" Pin0InfoVect0LinkObjId="SW-109439_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a32770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="917,-1008 917,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a331a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-949 917,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="21262@x" ObjectIDND1="g_2a33e40@0" ObjectIDND2="37769@1" ObjectIDZND0="21263@0" Pin0InfoVect0LinkObjId="SW-109439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109438_0" Pin1InfoVect1LinkObjId="g_2a33e40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,-949 917,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a33be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-936 868,-949 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21262@1" ObjectIDZND0="21263@x" ObjectIDZND1="g_2a33e40@0" ObjectIDZND2="37769@1" Pin0InfoVect0LinkObjId="SW-109439_0" Pin0InfoVect1LinkObjId="g_2a33e40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109438_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="868,-936 868,-949 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296df10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-1112 818,-1112 818,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="21263@x" ObjectIDND1="21262@x" ObjectIDND2="37769@1" ObjectIDZND0="g_2a33e40@0" Pin0InfoVect0LinkObjId="g_2a33e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109439_0" Pin1InfoVect1LinkObjId="SW-109438_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="868,-1112 818,-1112 818,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296e170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-1112 436,-1112 436,-1105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="21260@x" ObjectIDND1="21259@x" ObjectIDND2="49390@1" ObjectIDZND0="g_2aaf6b0@0" Pin0InfoVect0LinkObjId="g_2aaf6b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109435_0" Pin1InfoVect1LinkObjId="SW-109434_0" Pin1InfoVect2LinkObjId="g_2df6210_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-1112 436,-1112 436,-1105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-1107 1142,-1107 1142,-1103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="21297@x" ObjectIDND1="21295@x" ObjectIDND2="37764@1" ObjectIDZND0="g_296e3d0@0" Pin0InfoVect0LinkObjId="g_296e3d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-110018_0" Pin1InfoVect1LinkObjId="SW-110016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-1107 1142,-1107 1142,-1103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3ea70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1234,-1009 1234,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2a3ecd0@0" ObjectIDZND0="21297@1" Pin0InfoVect0LinkObjId="SW-110018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a3ecd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1234,-1009 1234,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a3fe70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-936 1192,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="21295@1" ObjectIDZND0="21297@x" ObjectIDZND1="g_296e3d0@0" ObjectIDZND2="37764@1" Pin0InfoVect0LinkObjId="SW-110018_0" Pin0InfoVect1LinkObjId="g_296e3d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-936 1192,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a400d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-888 1268,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21292@x" ObjectIDND1="21295@x" ObjectIDZND0="21296@0" Pin0InfoVect0LinkObjId="SW-110017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109976_0" Pin1InfoVect1LinkObjId="SW-110016_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-888 1268,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a40bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-876 1192,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21292@1" ObjectIDZND0="21295@x" ObjectIDZND1="21296@x" Pin0InfoVect0LinkObjId="SW-110016_0" Pin0InfoVect1LinkObjId="SW-110017_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109976_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-876 1192,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a40e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-888 1192,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21292@x" ObjectIDND1="21296@x" ObjectIDZND0="21295@0" Pin0InfoVect0LinkObjId="SW-110016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109976_0" Pin1InfoVect1LinkObjId="SW-110017_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-888 1192,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afc7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-832 1265,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="21293@x" ObjectIDND1="21292@x" ObjectIDZND0="21294@0" Pin0InfoVect0LinkObjId="SW-110015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110014_0" Pin1InfoVect1LinkObjId="SW-109976_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-832 1265,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afd290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-819 1192,-832 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="21293@1" ObjectIDZND0="21292@x" ObjectIDZND1="21294@x" Pin0InfoVect0LinkObjId="SW-109976_0" Pin0InfoVect1LinkObjId="SW-110015_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-819 1192,-832 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afd4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-832 1192,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="21293@x" ObjectIDND1="21294@x" ObjectIDZND0="21292@0" Pin0InfoVect0LinkObjId="SW-109976_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-110014_0" Pin1InfoVect1LinkObjId="SW-110015_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-832 1192,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c75c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-888 1316,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21296@1" ObjectIDZND0="g_2afd750@0" Pin0InfoVect0LinkObjId="g_2afd750_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-888 1316,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c75e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-831 1318,-831 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21294@1" ObjectIDZND0="g_2afe1e0@0" Pin0InfoVect0LinkObjId="g_2afe1e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-110015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-831 1318,-831 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a44380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-731 1675,-731 1676,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21254@0" ObjectIDND1="21256@x" ObjectIDZND0="21257@1" Pin0InfoVect0LinkObjId="SW-109431_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="SW-109430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-731 1675,-731 1676,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a44e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-767 1600,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21254@0" ObjectIDZND0="21257@x" ObjectIDZND1="21256@x" Pin0InfoVect0LinkObjId="SW-109431_0" Pin0InfoVect1LinkObjId="SW-109430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b32cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-767 1600,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a450d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-731 1600,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="21257@x" ObjectIDND1="21254@0" ObjectIDZND0="21256@1" Pin0InfoVect0LinkObjId="SW-109430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109431_0" Pin1InfoVect1LinkObjId="g_2b32cd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-731 1600,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a45330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1600,-681 1600,-649 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="21256@0" ObjectIDZND0="g_2afec70@0" Pin0InfoVect0LinkObjId="g_2afec70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1600,-681 1600,-649 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a45590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-156 1989,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="21270@x" ObjectIDND1="0@x" ObjectIDND2="34342@x" ObjectIDZND0="g_2a48040@0" Pin0InfoVect0LinkObjId="g_2a48040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109470_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-SB_EJ.031Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-156 1989,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a457f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-224 2014,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="load" ObjectIDND0="21270@0" ObjectIDZND0="g_2a48040@0" ObjectIDZND1="0@x" ObjectIDZND2="34342@x" Pin0InfoVect0LinkObjId="g_2a48040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="EC-SB_EJ.031Ld_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-224 2014,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a45a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-156 2014,-29 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2a48040@0" ObjectIDND1="21270@x" ObjectIDND2="0@x" ObjectIDZND0="34342@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.031Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a48040_0" Pin1InfoVect1LinkObjId="SW-109470_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-156 2014,-29 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a45cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-156 1837,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21272@x" ObjectIDND1="g_2b2d140@0" ObjectIDZND0="g_2c8e530@0" Pin0InfoVect0LinkObjId="g_2c8e530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109472_0" Pin1InfoVect1LinkObjId="g_2b2d140_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-156 1837,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a45f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-226 1800,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21272@0" ObjectIDZND0="g_2c8e530@0" ObjectIDZND1="g_2b2d140@0" Pin0InfoVect0LinkObjId="g_2c8e530_0" Pin0InfoVect1LinkObjId="g_2b2d140_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-226 1800,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aaa530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-157 1617,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34344@x" ObjectIDND1="21274@x" ObjectIDZND0="g_2cb3cc0@0" Pin0InfoVect0LinkObjId="g_2cb3cc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-SB_EJ.033Ld_0" Pin1InfoVect1LinkObjId="SW-109474_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-157 1617,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c69d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1183,-121 1183,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21291@0" ObjectIDZND0="g_2c69330@0" Pin0InfoVect0LinkObjId="g_2c69330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109948_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1183,-121 1183,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c69fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-159 827,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21278@x" ObjectIDND1="34346@x" ObjectIDZND0="g_2cbee60@0" Pin0InfoVect0LinkObjId="g_2cbee60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109478_0" Pin1InfoVect1LinkObjId="EC-SB_EJ.036Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-159 827,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6a240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-227 796,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21278@0" ObjectIDZND0="g_2cbee60@0" ObjectIDZND1="34346@x" Pin0InfoVect0LinkObjId="g_2cbee60_0" Pin0InfoVect1LinkObjId="EC-SB_EJ.036Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109478_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="796,-227 796,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6a4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="796,-159 796,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2cbee60@0" ObjectIDND1="21278@x" ObjectIDZND0="34346@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.036Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cbee60_0" Pin1InfoVect1LinkObjId="SW-109478_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="796,-159 796,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6a700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-162 627,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="21280@x" ObjectIDND1="37569@x" ObjectIDZND0="g_2a50300@0" Pin0InfoVect0LinkObjId="g_2a50300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109480_0" Pin1InfoVect1LinkObjId="EC-SB_EJ.037Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-162 627,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-230 590,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="21280@0" ObjectIDZND0="g_2a50300@0" ObjectIDZND1="37569@x" Pin0InfoVect0LinkObjId="g_2a50300_0" Pin0InfoVect1LinkObjId="EC-SB_EJ.037Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109480_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="590,-230 590,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6abc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="590,-162 590,-35 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2a50300@0" ObjectIDND1="21280@x" ObjectIDZND0="37569@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.037Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a50300_0" Pin1InfoVect1LinkObjId="SW-109480_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="590,-162 590,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6ae20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-504 1423,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="21268@x" ObjectIDND1="g_2df3820@0" ObjectIDZND0="g_2cb9ff0@0" Pin0InfoVect0LinkObjId="g_2cb9ff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109467_0" Pin1InfoVect1LinkObjId="g_2df3820_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-504 1423,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-469 1452,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="21268@1" ObjectIDZND0="g_2cb9ff0@0" ObjectIDZND1="g_2df3820@0" Pin0InfoVect0LinkObjId="g_2cb9ff0_0" Pin0InfoVect1LinkObjId="g_2df3820_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109467_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-469 1452,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c6b2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-504 1452,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2cb9ff0@0" ObjectIDND1="21268@x" ObjectIDZND0="g_2df3820@0" Pin0InfoVect0LinkObjId="g_2df3820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2cb9ff0_0" Pin1InfoVect1LinkObjId="SW-109467_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-504 1452,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa56a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-226 1063,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21265@x" ObjectIDND1="21266@x" ObjectIDZND0="21290@1" Pin0InfoVect0LinkObjId="SW-109947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109442_0" Pin1InfoVect1LinkObjId="SW-109443_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-226 1063,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_3144b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1063,-190 1063,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="21290@0" ObjectIDZND0="g_3144d80@0" Pin0InfoVect0LinkObjId="g_3144d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1063,-190 1063,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dec3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-248 1109,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="21265@0" ObjectIDZND0="21290@x" ObjectIDZND1="21266@x" Pin0InfoVect0LinkObjId="SW-109947_0" Pin0InfoVect1LinkObjId="SW-109443_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-248 1109,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dec620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-226 1109,-205 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="21290@x" ObjectIDND1="21265@x" ObjectIDZND0="21266@1" Pin0InfoVect0LinkObjId="SW-109443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109947_0" Pin1InfoVect1LinkObjId="SW-109442_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-226 1109,-205 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2deddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,16 1109,27 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_2ded380@0" Pin0InfoVect0LinkObjId="g_2ded380_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,16 1109,27 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df07d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-20 1109,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" ObjectIDND0="0@1" ObjectIDZND0="40863@1" Pin0InfoVect0LinkObjId="CB-SB_EJ.SB_EJ_Cb1_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-20 1109,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df4120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-539 1452,-578 1491,-578 1491,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2df3820@1" ObjectIDZND0="g_2df1060@0" Pin0InfoVect0LinkObjId="g_2df1060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df3820_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-539 1452,-578 1491,-578 1491,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df5620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-949 868,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21263@x" ObjectIDND1="21262@x" ObjectIDZND0="g_2a33e40@0" ObjectIDZND1="37769@1" Pin0InfoVect0LinkObjId="g_2a33e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109439_0" Pin1InfoVect1LinkObjId="SW-109438_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="868,-949 868,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df5ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-951 486,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="21260@x" ObjectIDND1="21259@x" ObjectIDZND0="g_2aaf6b0@0" ObjectIDZND1="49390@1" Pin0InfoVect0LinkObjId="g_2aaf6b0_0" Pin0InfoVect1LinkObjId="g_2df6210_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-109435_0" Pin1InfoVect1LinkObjId="SW-109434_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="486,-951 486,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df6210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="486,-1112 486,-1128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2aaf6b0@0" ObjectIDND1="21260@x" ObjectIDND2="21259@x" ObjectIDZND0="49390@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2aaf6b0_0" Pin1InfoVect1LinkObjId="SW-109435_0" Pin1InfoVect2LinkObjId="SW-109434_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="486,-1112 486,-1128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2f2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-1107 1192,-950 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_296e3d0@0" ObjectIDND1="37764@1" ObjectIDZND0="21297@x" ObjectIDZND1="21295@x" Pin0InfoVect0LinkObjId="SW-110018_0" Pin0InfoVect1LinkObjId="SW-110016_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_296e3d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-1107 1192,-950 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-1125 868,-1112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37769@1" ObjectIDZND0="g_2a33e40@0" ObjectIDZND1="21263@x" ObjectIDZND2="21262@x" Pin0InfoVect0LinkObjId="g_2a33e40_0" Pin0InfoVect1LinkObjId="SW-109439_0" Pin0InfoVect2LinkObjId="SW-109438_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="868,-1125 868,-1112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c2f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1192,-1122 1192,-1107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="37764@1" ObjectIDZND0="g_296e3d0@0" ObjectIDZND1="21297@x" ObjectIDZND2="21295@x" Pin0InfoVect0LinkObjId="g_296e3d0_0" Pin0InfoVect1LinkObjId="SW-110018_0" Pin0InfoVect2LinkObjId="SW-110016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1192,-1122 1192,-1107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c324c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-169 1109,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="capacitor" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="21266@0" ObjectIDZND0="40863@x" ObjectIDZND1="g_2c319c0@0" ObjectIDZND2="21291@x" Pin0InfoVect0LinkObjId="CB-SB_EJ.SB_EJ_Cb1_0" Pin0InfoVect1LinkObjId="g_2c319c0_0" Pin0InfoVect2LinkObjId="SW-109948_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-169 1109,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c326f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-157 1109,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="21266@x" ObjectIDND1="g_2c319c0@0" ObjectIDND2="21291@x" ObjectIDZND0="40863@0" Pin0InfoVect0LinkObjId="CB-SB_EJ.SB_EJ_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-109443_0" Pin1InfoVect1LinkObjId="g_2c319c0_0" Pin1InfoVect2LinkObjId="SW-109948_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-157 1109,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c32920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1154,-141 1154,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2c319c0@0" ObjectIDZND0="21291@x" ObjectIDZND1="21266@x" ObjectIDZND2="40863@x" Pin0InfoVect0LinkObjId="SW-109948_0" Pin0InfoVect1LinkObjId="SW-109443_0" Pin0InfoVect2LinkObjId="CB-SB_EJ.SB_EJ_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c319c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1154,-141 1154,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c32b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1183,-157 1154,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="21291@1" ObjectIDZND0="g_2c319c0@0" ObjectIDZND1="21266@x" ObjectIDZND2="40863@x" Pin0InfoVect0LinkObjId="g_2c319c0_0" Pin0InfoVect1LinkObjId="SW-109443_0" Pin0InfoVect2LinkObjId="CB-SB_EJ.SB_EJ_Cb1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109948_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1183,-157 1154,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c32de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1154,-157 1109,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="g_2c319c0@0" ObjectIDND1="21291@x" ObjectIDZND0="21266@x" ObjectIDZND1="40863@x" Pin0InfoVect0LinkObjId="SW-109443_0" Pin0InfoVect1LinkObjId="CB-SB_EJ.SB_EJ_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c319c0_0" Pin1InfoVect1LinkObjId="SW-109948_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1154,-157 1109,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c44300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-439 826,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41069@0" ObjectIDZND0="41078@0" Pin0InfoVect0LinkObjId="g_2b24c80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244304_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-439 826,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c44560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-475 826,-491 896,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41069@1" ObjectIDZND0="41067@1" Pin0InfoVect0LinkObjId="SW-244302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="826,-475 826,-491 896,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2c48990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="507,-656 507,-644 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="41072@0" ObjectIDZND0="g_2c478d0@0" Pin0InfoVect0LinkObjId="g_2c478d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244334_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="507,-656 507,-644 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-419 237,-406 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41078@0" ObjectIDZND0="41064@1" Pin0InfoVect0LinkObjId="SW-244281_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c44300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-419 237,-406 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c4ffd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-370 237,-347 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41064@0" ObjectIDZND0="41063@1" Pin0InfoVect0LinkObjId="SW-244280_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244281_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-370 237,-347 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c51370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-266 237,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41065@1" ObjectIDZND0="41063@0" Pin0InfoVect0LinkObjId="SW-244280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244282_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-266 237,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c58960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-419 392,-400 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="41078@0" ObjectIDZND0="41061@1" Pin0InfoVect0LinkObjId="SW-244257_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c44300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-419 392,-400 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c58bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-364 392,-330 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="41061@0" ObjectIDZND0="41059@1" Pin0InfoVect0LinkObjId="SW-244255_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244257_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-364 392,-330 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c58e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-303 392,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41059@0" ObjectIDZND0="41060@1" Pin0InfoVect0LinkObjId="SW-244256_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244255_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-303 392,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c59df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-162 429,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41060@x" ObjectIDND1="g_2b2c440@0" ObjectIDZND0="g_2c59080@0" Pin0InfoVect0LinkObjId="g_2c59080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244256_0" Pin1InfoVect1LinkObjId="g_2b2c440_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-162 429,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c5a050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-232 392,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="41060@0" ObjectIDZND0="g_2c59080@0" ObjectIDZND1="g_2b2c440@0" Pin0InfoVect0LinkObjId="g_2c59080_0" Pin0InfoVect1LinkObjId="g_2b2c440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244256_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="392,-232 392,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b07500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-237 193,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="41066@1" ObjectIDZND0="g_2c65090@0" Pin0InfoVect0LinkObjId="g_2c65090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244283_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="193,-237 193,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0aa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="193,-201 193,-191 237,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="41066@0" ObjectIDZND0="41065@x" ObjectIDZND1="41589@x" Pin0InfoVect0LinkObjId="SW-244282_0" Pin0InfoVect1LinkObjId="CB-SB_EJ.SB_EJ_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244283_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="193,-201 193,-191 237,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-230 237,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="41065@0" ObjectIDZND0="41066@x" ObjectIDZND1="41589@x" Pin0InfoVect0LinkObjId="SW-244283_0" Pin0InfoVect1LinkObjId="CB-SB_EJ.SB_EJ_Cb2_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244282_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="237,-230 237,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="237,-191 237,-176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="41066@x" ObjectIDND1="41065@x" ObjectIDZND0="41589@0" Pin0InfoVect0LinkObjId="CB-SB_EJ.SB_EJ_Cb2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244283_0" Pin1InfoVect1LinkObjId="SW-244282_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="237,-191 237,-176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0bf00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2014,-156 2048,-156 2048,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2a48040@0" ObjectIDND1="21270@x" ObjectIDND2="34342@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a48040_0" Pin1InfoVect1LinkObjId="SW-109470_0" Pin1InfoVect2LinkObjId="EC-SB_EJ.031Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2014,-156 2048,-156 2048,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0c160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2048,-96 2048,-74 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2b0c3c0@0" Pin0InfoVect0LinkObjId="g_2b0c3c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2048,-96 2048,-74 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b12230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1374,-121 1374,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_2b12490@0" Pin0InfoVect0LinkObjId="g_2b12490_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1374,-121 1374,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b14850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1452,-433 1452,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21268@0" ObjectIDZND0="21253@0" Pin0InfoVect0LinkObjId="g_2ab9080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1452,-433 1452,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b193d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1578,-394 1578,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21273@1" ObjectIDZND0="21253@0" Pin0InfoVect0LinkObjId="g_2ab9080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109473_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1578,-394 1578,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1a7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="923,-491 988,-491 988,-475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41067@0" ObjectIDZND0="41068@1" Pin0InfoVect0LinkObjId="SW-244303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="923,-491 988,-491 988,-475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="988,-439 988,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41068@0" ObjectIDZND0="21253@0" Pin0InfoVect0LinkObjId="g_2ab9080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="988,-439 988,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b21180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-504 295,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41062@x" ObjectIDND1="g_2b24110@0" ObjectIDZND0="g_2b1fa20@0" Pin0InfoVect0LinkObjId="g_2b1fa20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244278_0" Pin1InfoVect1LinkObjId="g_2b24110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-504 295,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b21370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-469 324,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="41062@1" ObjectIDZND0="g_2b1fa20@0" ObjectIDZND1="g_2b24110@0" Pin0InfoVect0LinkObjId="g_2b1fa20_0" Pin0InfoVect1LinkObjId="g_2b24110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244278_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="324,-469 324,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b21560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-504 324,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="41062@x" ObjectIDND1="g_2b1fa20@0" ObjectIDZND0="g_2b24110@0" Pin0InfoVect0LinkObjId="g_2b24110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244278_0" Pin1InfoVect1LinkObjId="g_2b1fa20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-504 324,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b24a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-539 324,-578 363,-578 363,-566 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b24110@1" ObjectIDZND0="g_2b21ae0@0" Pin0InfoVect0LinkObjId="g_2b21ae0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b24110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-539 324,-578 363,-578 363,-566 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b24c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="324,-433 324,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41062@0" ObjectIDZND0="41078@0" Pin0InfoVect0LinkObjId="g_2c44300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244278_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="324,-433 324,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b25d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-181 1345,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b0e150@0" ObjectIDZND0="34345@x" ObjectIDZND1="0@x" ObjectIDZND2="21276@x" Pin0InfoVect0LinkObjId="EC-SB_EJ.034Ld_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-109476_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b0e150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-181 1345,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-226 1345,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="21276@0" ObjectIDZND0="g_2b0e150@0" ObjectIDZND1="34345@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b0e150_0" Pin0InfoVect1LinkObjId="EC-SB_EJ.034Ld_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109476_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-226 1345,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-181 1345,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_2b0e150@0" ObjectIDND1="0@x" ObjectIDND2="21276@x" ObjectIDZND0="34345@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.034Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b0e150_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-109476_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-181 1345,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1345,-181 1374,-181 1374,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b0e150@0" ObjectIDND1="34345@x" ObjectIDND2="21276@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b0e150_0" Pin1InfoVect1LinkObjId="EC-SB_EJ.034Ld_0" Pin1InfoVect2LinkObjId="SW-109476_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1345,-181 1374,-181 1374,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2a860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1109,-390 1109,-417 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="21264@1" ObjectIDZND0="21253@0" Pin0InfoVect0LinkObjId="g_2ab9080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-109441_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1109,-390 1109,-417 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-162 392,-120 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c59080@0" ObjectIDND1="41060@x" ObjectIDZND0="g_2b2c440@1" Pin0InfoVect0LinkObjId="g_2b2c440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c59080_0" Pin1InfoVect1LinkObjId="SW-244256_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-162 392,-120 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2cee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="392,-81 392,-42 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b2c440@0" ObjectIDZND0="42307@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.038Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2c440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="392,-81 392,-42 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2dd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-156 1800,-112 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2c8e530@0" ObjectIDND1="21272@x" ObjectIDZND0="g_2b2d140@1" Pin0InfoVect0LinkObjId="g_2b2d140_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2c8e530_0" Pin1InfoVect1LinkObjId="SW-109472_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-156 1800,-112 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2dfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1800,-73 1800,-31 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b2d140@0" ObjectIDZND0="34343@0" Pin0InfoVect0LinkObjId="EC-SB_EJ.032Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2d140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1800,-73 1800,-31 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b32cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-753 571,-767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41071@1" ObjectIDZND0="21254@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244333_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-753 571,-767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b32f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-646 571,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="41075@1" ObjectIDZND0="41070@0" Pin0InfoVect0LinkObjId="SW-244332_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-646 571,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b33d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-436 571,-419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="41074@0" ObjectIDZND0="41078@0" Pin0InfoVect0LinkObjId="g_2c44300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244336_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-436 571,-419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b33f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-558 571,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="41075@0" ObjectIDZND0="41073@1" Pin0InfoVect0LinkObjId="SW-244335_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-558 571,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b341f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-498 571,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="41073@0" ObjectIDZND0="41074@1" Pin0InfoVect0LinkObjId="SW-244336_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244335_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-498 571,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b34ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-692 571,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="41070@1" ObjectIDZND0="41071@x" ObjectIDZND1="41072@x" Pin0InfoVect0LinkObjId="SW-244333_0" Pin0InfoVect1LinkObjId="SW-244334_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-244332_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="571,-692 571,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b34f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-704 571,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41070@x" ObjectIDND1="41072@x" ObjectIDZND0="41071@0" Pin0InfoVect0LinkObjId="SW-244333_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244332_0" Pin1InfoVect1LinkObjId="SW-244334_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-704 571,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b351a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="571,-704 507,-704 507,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="41070@x" ObjectIDND1="41071@x" ObjectIDZND0="41072@1" Pin0InfoVect0LinkObjId="SW-244334_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-244332_0" Pin1InfoVect1LinkObjId="SW-244333_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="571,-704 507,-704 507,-694 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-109189" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 48.000000 -972.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21234" ObjectName="DYN-SB_EJ"/>
     <cge:Meas_Ref ObjectId="109189"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="579" cy="-1069" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="596" cy="-1069" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="977" cy="-1076" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="960" cy="-1076" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1301" cy="-1077" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" cx="1284" cy="-1077" fill="none" fillStyle="0" r="7" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_EJ" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_nayueTej" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="868,-1125 868,-1151 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37769" ObjectName="AC-35kV.LN_nayueTej"/>
    <cge:TPSR_Ref TObjectID="37769_SS-162"/></metadata>
   <polyline fill="none" opacity="0" points="868,-1125 868,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_EJ" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_konglhyj" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1192,-1121 1192,-1147 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37764" ObjectName="AC-35kV.LN_konglhyj"/>
    <cge:TPSR_Ref TObjectID="37764_SS-162"/></metadata>
   <polyline fill="none" opacity="0" points="1192,-1121 1192,-1147 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="SB_DT" endPointId="0" endStationName="SB_EJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_tiane" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="487,-1126 487,-1167 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49390" ObjectName="AC-35kV.LN_tiane"/>
    <cge:TPSR_Ref TObjectID="49390_SS-162"/></metadata>
   <polyline fill="none" opacity="0" points="487,-1126 487,-1167 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="21254" cx="1192" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21254" cx="1237" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1237" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1917" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="2014" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1800" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1345" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1452" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1578" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21254" cx="1600" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="988" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="796" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="590" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="826" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="392" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="237" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="324" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21253" cx="1109" cy="-417" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21254" cx="821" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="21254" cx="571" cy="-767" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="41078" cx="571" cy="-419" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-109927">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -840.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21288" ObjectName="SW-SB_EJ.SB_EJ_331BK"/>
     <cge:Meas_Ref ObjectId="109927"/>
    <cge:TPSR_Ref TObjectID="21288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 859.000000 -841.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21281" ObjectName="SW-SB_EJ.SB_EJ_332BK"/>
     <cge:Meas_Ref ObjectId="109655"/>
    <cge:TPSR_Ref TObjectID="21281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109976">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1183.000000 -841.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21292" ObjectName="SW-SB_EJ.SB_EJ_333BK"/>
     <cge:Meas_Ref ObjectId="109976"/>
    <cge:TPSR_Ref TObjectID="21292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109583">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -658.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21298" ObjectName="SW-SB_EJ.SB_EJ_301BK"/>
     <cge:Meas_Ref ObjectId="109583"/>
    <cge:TPSR_Ref TObjectID="21298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109594">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21299" ObjectName="SW-SB_EJ.SB_EJ_001BK"/>
     <cge:Meas_Ref ObjectId="109594"/>
    <cge:TPSR_Ref TObjectID="21299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109698">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2005.000000 -287.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21282" ObjectName="SW-SB_EJ.SB_EJ_031BK"/>
     <cge:Meas_Ref ObjectId="109698"/>
    <cge:TPSR_Ref TObjectID="21282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109740">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1791.000000 -289.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21283" ObjectName="SW-SB_EJ.SB_EJ_032BK"/>
     <cge:Meas_Ref ObjectId="109740"/>
    <cge:TPSR_Ref TObjectID="21283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109778">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1569.000000 -289.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21284" ObjectName="SW-SB_EJ.SB_EJ_033BK"/>
     <cge:Meas_Ref ObjectId="109778"/>
    <cge:TPSR_Ref TObjectID="21284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1336.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21285" ObjectName="SW-SB_EJ.SB_EJ_034BK"/>
     <cge:Meas_Ref ObjectId="109816"/>
    <cge:TPSR_Ref TObjectID="21285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109946">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -296.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21289" ObjectName="SW-SB_EJ.SB_EJ_035BK"/>
     <cge:Meas_Ref ObjectId="109946"/>
    <cge:TPSR_Ref TObjectID="21289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109856">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 787.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21286" ObjectName="SW-SB_EJ.SB_EJ_036BK"/>
     <cge:Meas_Ref ObjectId="109856"/>
    <cge:TPSR_Ref TObjectID="21286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-109892">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -293.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="21287" ObjectName="SW-SB_EJ.SB_EJ_037BK"/>
     <cge:Meas_Ref ObjectId="109892"/>
    <cge:TPSR_Ref TObjectID="21287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -656.584906)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41070" ObjectName="SW-SB_EJ.SB_EJ_302BK"/>
     <cge:Meas_Ref ObjectId="244332"/>
    <cge:TPSR_Ref TObjectID="41070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 931.915094 -481.500000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41067" ObjectName="SW-SB_EJ.SB_EJ_012BK"/>
     <cge:Meas_Ref ObjectId="244302"/>
    <cge:TPSR_Ref TObjectID="41067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244280">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 228.000000 -312.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41063" ObjectName="SW-SB_EJ.SB_EJ_039BK"/>
     <cge:Meas_Ref ObjectId="244280"/>
    <cge:TPSR_Ref TObjectID="41063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244255">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 383.000000 -295.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41059" ObjectName="SW-SB_EJ.SB_EJ_038BK"/>
     <cge:Meas_Ref ObjectId="244255"/>
    <cge:TPSR_Ref TObjectID="41059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-244335">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -490.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41073" ObjectName="SW-SB_EJ.SB_EJ_002BK"/>
     <cge:Meas_Ref ObjectId="244335"/>
    <cge:TPSR_Ref TObjectID="41073"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b034d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -908.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2cd1d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -252.000000 -470.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2cc5ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -114.000000 -1049.500000) translate(0,16)">鄂嘉变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2d1d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -1187.000000) translate(0,18)">35kV田鄂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c1d8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 152.000000 -799.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_292f960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2137.000000 -407.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2cb6620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 799.000000 -1179.000000) translate(0,18)">35kV纳鱼鄂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_2cb5500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -1180.000000) translate(0,18)">35kV空龙河一级线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af09e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -546.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2caf560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1418.000000 -602.000000) translate(0,15)">10kVⅠ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2adcbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1985.000000 17.000000) translate(0,15)">鱼庄河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a0e290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1773.000000 16.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a0ea10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1545.000000 16.000000) translate(0,15)">城街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2d04fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1323.000000 14.000000) translate(0,15)">老虎山线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a51890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2041.000000 -31.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2dfd5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1123.000000 5.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c791f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 763.000000 14.000000) translate(0,15)">茶叶线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a1a560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 11.000000) translate(0,15)">中学线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2a1b050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -114.000000) translate(0,17)">7813292</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6b540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 501.000000 -870.000000) translate(0,12)">331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6ba30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 -924.000000) translate(0,12)">3316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6bd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.000000 -807.000000) translate(0,12)">3311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6c1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -976.000000) translate(0,12)">33167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c6c400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 877.000000 -870.000000) translate(0,12)">332</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae8ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -808.000000) translate(0,12)">3321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 875.000000 -925.000000) translate(0,12)">3326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.000000 -974.000000) translate(0,12)">33267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -808.000000) translate(0,12)">3331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae97c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1199.000000 -925.000000) translate(0,12)">3336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 -975.000000) translate(0,12)">33367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1265.000000 -914.000000) translate(0,12)">33360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae9e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1266.000000 -857.000000) translate(0,12)">33317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aea0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -687.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aea300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -743.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aea540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -519.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aea780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1244.000000 -461.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aea9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1267.000000 -610.000000) translate(0,12)">1#主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeaee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 -550.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeb170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1885.000000 -618.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeb3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1607.000000 -706.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aeb8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1683.000000 -718.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aebb40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1459.000000 -457.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aebd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2023.000000 -316.000000) translate(0,12)">031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aebfc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2021.000000 -381.000000) translate(0,12)">0311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2021.000000 -249.000000) translate(0,12)">0316</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 -318.000000) translate(0,12)">032</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -383.000000) translate(0,12)">0321</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aec8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 -251.000000) translate(0,12)">0326</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aecb00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 -318.000000) translate(0,12)">033</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aecd40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -383.000000) translate(0,12)">0331</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aecf80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -251.000000) translate(0,12)">0336</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aed1c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.000000 -319.000000) translate(0,12)">034</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abfaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -384.000000) translate(0,12)">0341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abfcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1352.000000 -252.000000) translate(0,12)">0346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2abfef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1118.000000 -325.000000) translate(0,12)">035</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac0130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -379.000000) translate(0,12)">0351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac0370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -273.000000) translate(0,12)">0353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac05b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1116.000000 -194.000000) translate(0,12)">0356</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac07f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 -146.000000) translate(0,12)">03567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 805.000000 -319.000000) translate(0,12)">036</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac0c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -384.000000) translate(0,12)">0362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac0eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 803.000000 -252.000000) translate(0,12)">0366</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac10f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 599.000000 -322.000000) translate(0,12)">037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac1330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -387.000000) translate(0,12)">0372</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac1570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 597.000000 -255.000000) translate(0,12)">0376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31457d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -213.000000) translate(0,12)">03560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_314f6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1201.000000 -870.000000) translate(0,12)">333</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,15)">1号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,33)">SZ11-3150-35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,51)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,87)">标准档电流比：52/173.2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,105)">容量比：3150/3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_3151970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1305.000000 -745.000000) translate(0,123)">Ud%=7.06%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3154bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -246.000000 -669.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_31551d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 102.000000 -1084.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_3155750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 101.000000 -1043.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2de1ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 810.000000 -532.000000) translate(0,15)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2df0a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1904.000000 -640.000000) translate(0,15)">50kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c31180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -77.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c31180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -264.000000 -77.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c313c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -87.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c313c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -87.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2c313c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -137.000000 -87.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c33180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.500000 -974.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3b1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -686.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3b820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 578.000000 -742.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c3ba60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -518.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c447c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -628.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c44df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 898.000000 -475.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c45030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 996.000000 -464.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c45270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 833.000000 -464.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c48360" transform="matrix(1.000000 -0.000000 -0.000000 1.043478 454.000000 -684.391304) translate(0,12)">30210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c501c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 246.000000 -341.000000) translate(0,12)">039</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c50610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -395.000000) translate(0,12)">0392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c50850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.000000 -256.000000) translate(0,12)">0396</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c515a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 174.000000 -186.000000) translate(0,12)">03967</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c51a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 136.000000 -439.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5a2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 401.000000 -324.000000) translate(0,12)">038</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5aae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 -389.000000) translate(0,12)">0382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c5af10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 399.000000 -257.000000) translate(0,12)">0386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2c5b8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 189.000000 -62.000000) translate(0,15)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 580.000000 -519.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c60730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 579.000000 -460.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b14220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1349.000000 -57.000000) translate(0,15)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b1c970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 372.000000 6.000000) translate(0,15)">新树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b207d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 270.000000 -603.000000) translate(0,15)">10kVⅡ段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 331.000000 -457.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,15)">2号主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,33)">SZ11-8000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,51)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,69)">YN,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,87)">标准档电流比：132/439.9</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,105)">容量比：8000/8000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,123)">Ud1-2=10.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,141)">Ud1-3=17.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b27170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 166.000000 -758.000000) translate(0,159)">Ud2-3=6.5%</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2cc86f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 -1006.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2cbd770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1670.000000 -662.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a32770" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 911.000000 -1003.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a3ecd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.000000 -1004.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afd750" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 -882.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afe1e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1314.000000 -825.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c69330" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1177.000000 -92.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3144d80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1057.000000 -156.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ded380" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1103.000000 45.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c478d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.043478 501.000000 -625.869565)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c65090" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 187.000000 -247.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="SB_EJ"/>
</svg>