<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-285" aopId="3940870" id="thSvg" product="E8000V2" version="1.0" viewBox="-12 -1029 1671 1083">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="4" y2="4"/>
    <ellipse cx="14" cy="18" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="22" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="17" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="22" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="9" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="24" x2="24" y1="12" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="24" y1="8" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="24" y1="10" y2="12"/>
    <ellipse cx="8" cy="10" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape213">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="13" y1="6" y2="6"/>
    <rect height="20" stroke-width="1" width="5" x="24" y="6"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="16,26 38,5 46,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="14" y1="3" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.11864"/>
    <polyline points="58,100 64,100 " stroke-width="1.11864"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.11864"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape24_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="14" y1="47" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="77" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="71" y2="71"/>
    <polyline DF8003:Layer="PUBLIC" points="14,84 20,71 7,71 14,84 14,83 14,84 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="56" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="15" y1="19" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="19" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="13" y2="19"/>
   </symbol>
   <symbol id="transformer2:shape24_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,41 40,41 40,70 " stroke-width="1"/>
    <circle cx="16" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="43" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="42" y2="47"/>
   </symbol>
   <symbol id="transformer2:shape41_0">
    <circle cx="16" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="15,84 21,71 8,71 15,84 15,83 15,84 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="32,42 41,42 41,70 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="35" x2="47" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="45" x2="37" y1="75" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="44" x2="41" y1="78" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="15" y1="45" y2="72"/>
    <polyline DF8003:Layer="PUBLIC" points="17,11 21,20 11,20 17,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="17" y2="17"/>
   </symbol>
   <symbol id="transformer2:shape41_1">
    <circle cx="17" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="41" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="21" y1="41" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="12" y1="41" y2="45"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3097b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3098a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3099550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_309a040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_309afd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_309ba10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_309c410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_309ce40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a9de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2a9de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a0000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a0000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a1d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a1d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_30a2d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a49d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30a5660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30a64d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30a6c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a84a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a9100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30a99c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30aa180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ab260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30abbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30ac6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30ad090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30ae6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30af0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_30b0270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30b0f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_30bf310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_30b7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30b8be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_30b30f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1093" width="1681" x="-17" y="-1034"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="268" y="-993"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-271233">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.291667 -697.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43696" ObjectName="SW-YR_YX.YR_YX_3016SW"/>
     <cge:Meas_Ref ObjectId="271233"/>
    <cge:TPSR_Ref TObjectID="43696"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271237">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.291667 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43700" ObjectName="SW-YR_YX.YR_YX_0011SW"/>
     <cge:Meas_Ref ObjectId="271237"/>
    <cge:TPSR_Ref TObjectID="43700"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271476">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43712" ObjectName="SW-YR_YX.YR_YX_0741SW"/>
     <cge:Meas_Ref ObjectId="271476"/>
    <cge:TPSR_Ref TObjectID="43712"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271525">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1547.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43715" ObjectName="SW-YR_YX.YR_YX_0751SW"/>
     <cge:Meas_Ref ObjectId="271525"/>
    <cge:TPSR_Ref TObjectID="43715"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271526">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1547.000000 -151.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43716" ObjectName="SW-YR_YX.YR_YX_0756SW"/>
     <cge:Meas_Ref ObjectId="271526"/>
    <cge:TPSR_Ref TObjectID="43716"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271477">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43713" ObjectName="SW-YR_YX.YR_YX_0746SW"/>
     <cge:Meas_Ref ObjectId="271477"/>
    <cge:TPSR_Ref TObjectID="43713"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271378">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -257.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43706" ObjectName="SW-YR_YX.YR_YX_0721SW"/>
     <cge:Meas_Ref ObjectId="271378"/>
    <cge:TPSR_Ref TObjectID="43706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271379">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43707" ObjectName="SW-YR_YX.YR_YX_0726SW"/>
     <cge:Meas_Ref ObjectId="271379"/>
    <cge:TPSR_Ref TObjectID="43707"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271329">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43703" ObjectName="SW-YR_YX.YR_YX_0711SW"/>
     <cge:Meas_Ref ObjectId="271329"/>
    <cge:TPSR_Ref TObjectID="43703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271330">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -151.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43704" ObjectName="SW-YR_YX.YR_YX_0716SW"/>
     <cge:Meas_Ref ObjectId="271330"/>
    <cge:TPSR_Ref TObjectID="43704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271314">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 580.000000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43701" ObjectName="SW-YR_YX.YR_YX_0901SW"/>
     <cge:Meas_Ref ObjectId="271314"/>
    <cge:TPSR_Ref TObjectID="43701"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271235">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 925.291667 -694.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43698" ObjectName="SW-YR_YX.YR_YX_30117SW"/>
     <cge:Meas_Ref ObjectId="271235"/>
    <cge:TPSR_Ref TObjectID="43698"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271234">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 1.000000 1.000000 0.000000 927.291667 -766.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43697" ObjectName="SW-YR_YX.YR_YX_30167SW"/>
     <cge:Meas_Ref ObjectId="271234"/>
    <cge:TPSR_Ref TObjectID="43697"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271427">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43709" ObjectName="SW-YR_YX.YR_YX_0731SW"/>
     <cge:Meas_Ref ObjectId="271427"/>
    <cge:TPSR_Ref TObjectID="43709"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271428">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43710" ObjectName="SW-YR_YX.YR_YX_0736SW"/>
     <cge:Meas_Ref ObjectId="271428"/>
    <cge:TPSR_Ref TObjectID="43710"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1496.000000 -84.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-316470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.714286 -0.000000 0.000000 -0.630435 896.208333 -602.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48775" ObjectName="SW-YR_YX.XB"/>
     <cge:Meas_Ref ObjectId="316470"/>
    <cge:TPSR_Ref TObjectID="48775"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_YX.YR_YX_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="423,-322 1659,-322 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="39173" ObjectName="BS-YR_YX.YR_YX_9IM"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   <polyline fill="none" opacity="0" points="423,-322 1659,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_YX.XM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="894,-637 907,-637 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48774" ObjectName="BS-YR_YX.XM"/>
    <cge:TPSR_Ref TObjectID="48774"/></metadata>
   <polyline fill="none" opacity="0" points="894,-637 907,-637 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_YX.YR_YX_075Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1547.000000 -55.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43484" ObjectName="EC-YR_YX.YR_YX_075Ld"/>
    <cge:TPSR_Ref TObjectID="43484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YX.YR_YX_074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43482" ObjectName="EC-YR_YX.YR_YX_074Ld"/>
    <cge:TPSR_Ref TObjectID="43482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YX.YR_YX_072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -56.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43481" ObjectName="EC-YR_YX.YR_YX_072Ld"/>
    <cge:TPSR_Ref TObjectID="43481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YX.YR_YX_071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -55.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43717" ObjectName="EC-YR_YX.YR_YX_071Ld"/>
    <cge:TPSR_Ref TObjectID="43717"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_YX.YR_YX_073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -52.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43483" ObjectName="EC-YR_YX.YR_YX_073Ld"/>
    <cge:TPSR_Ref TObjectID="43483"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a02480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 982.291667 -679.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acb0a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 977.291667 -751.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2b35d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-646 902,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="39171@0" ObjectIDZND0="48774@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-646 902,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aeecb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-215 1556,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43714@0" ObjectIDZND0="43716@1" Pin0InfoVect0LinkObjId="SW-271526_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271524_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-215 1556,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab55c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-216 1267,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43711@0" ObjectIDZND0="43713@1" Pin0InfoVect0LinkObjId="SW-271477_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-216 1267,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2d370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-243 1267,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43711@1" ObjectIDZND0="43712@0" Pin0InfoVect0LinkObjId="SW-271476_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271475_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-243 1267,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a2d560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-298 1267,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43712@1" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2b1ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271476_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-298 1267,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1e450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-216 750,-193 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43705@0" ObjectIDZND0="43707@1" Pin0InfoVect0LinkObjId="SW-271379_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271377_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-216 750,-193 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1eb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-243 750,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43705@1" ObjectIDZND0="43706@0" Pin0InfoVect0LinkObjId="SW-271378_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271377_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-243 750,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-298 750,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43706@1" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271378_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-298 750,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1ef30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-354 902,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43700@0" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-354 902,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e5e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-215 522,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43702@0" ObjectIDZND0="43704@1" Pin0InfoVect0LinkObjId="SW-271330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-215 522,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-242 522,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43702@1" ObjectIDZND0="43703@0" Pin0InfoVect0LinkObjId="SW-271329_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-242 522,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29e6720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-297 522,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43703@1" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271329_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-297 522,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a01fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-685 930,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43696@x" ObjectIDND1="39171@x" ObjectIDZND0="43698@0" Pin0InfoVect0LinkObjId="SW-271235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-271233_0" Pin1InfoVect1LinkObjId="SW-234627_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-685 930,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a02220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-685 986,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43698@1" ObjectIDZND0="g_2a02480@0" Pin0InfoVect0LinkObjId="g_2a02480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-685 986,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acabe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-757 932,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_2b1c230@0" ObjectIDND1="g_2acc4e0@0" ObjectIDND2="43718@1" ObjectIDZND0="43697@0" Pin0InfoVect0LinkObjId="SW-271234_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b1c230_0" Pin1InfoVect1LinkObjId="g_2acc4e0_0" Pin1InfoVect2LinkObjId="g_2ad7800_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-757 932,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-757 981,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43697@1" ObjectIDZND0="g_2acb0a0@0" Pin0InfoVect0LinkObjId="g_2acb0a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271234_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="968,-757 981,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acc280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-738 902,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43696@1" ObjectIDZND0="43697@x" ObjectIDZND1="g_2b1c230@0" ObjectIDZND2="g_2acc4e0@0" Pin0InfoVect0LinkObjId="SW-271234_0" Pin0InfoVect1LinkObjId="g_2b1c230_0" Pin0InfoVect2LinkObjId="g_2acc4e0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271233_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="902,-738 902,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acd0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-866 942,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ad8ab0@0" ObjectIDND1="g_2b1c230@0" ObjectIDND2="43697@x" ObjectIDZND0="g_2acc4e0@0" Pin0InfoVect0LinkObjId="g_2acc4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ad8ab0_0" Pin1InfoVect1LinkObjId="g_2b1c230_0" Pin1InfoVect2LinkObjId="SW-271234_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-866 942,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad7800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-866 902,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_2acc4e0@0" ObjectIDND1="g_2ad8ab0@0" ObjectIDND2="g_2b1c230@0" ObjectIDZND0="43718@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2acc4e0_0" Pin1InfoVect1LinkObjId="g_2ad8ab0_0" Pin1InfoVect2LinkObjId="g_2b1c230_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-866 902,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad7e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-852 842,-852 842,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2acc4e0@0" ObjectIDND1="43718@1" ObjectIDND2="g_2b1c230@0" ObjectIDZND0="g_2ad8ab0@0" Pin0InfoVect0LinkObjId="g_2ad8ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2acc4e0_0" Pin1InfoVect1LinkObjId="g_2ad7800_1" Pin1InfoVect2LinkObjId="g_2b1c230_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-852 842,-852 842,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad8850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-852 902,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_2ad8ab0@0" ObjectIDND1="g_2b1c230@0" ObjectIDND2="43697@x" ObjectIDZND0="g_2acc4e0@0" ObjectIDZND1="43718@1" Pin0InfoVect0LinkObjId="g_2acc4e0_0" Pin0InfoVect1LinkObjId="g_2ad7800_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ad8ab0_0" Pin1InfoVect1LinkObjId="g_2b1c230_0" Pin1InfoVect2LinkObjId="SW-271234_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-852 902,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1cd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="903,-789 779,-789 779,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="43697@x" ObjectIDND1="43696@x" ObjectIDND2="g_2acc4e0@0" ObjectIDZND0="g_2b1c230@0" Pin0InfoVect0LinkObjId="g_2b1c230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-271234_0" Pin1InfoVect1LinkObjId="SW-271233_0" Pin1InfoVect2LinkObjId="g_2acc4e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="903,-789 779,-789 779,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1cf90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="779,-739 779,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b1c230@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b1c230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="779,-739 779,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f1ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-212 993,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43708@0" ObjectIDZND0="43710@1" Pin0InfoVect0LinkObjId="SW-271428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-212 993,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-239 993,-258 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43708@1" ObjectIDZND0="43709@0" Pin0InfoVect0LinkObjId="SW-271427_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-239 993,-258 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f25e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-294 993,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43709@1" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-294 993,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afb3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-156 522,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43704@0" ObjectIDZND0="g_2afaaa0@1" Pin0InfoVect0LinkObjId="g_2afaaa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-156 522,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afb5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="522,-93 522,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2afaaa0@0" ObjectIDZND0="43717@0" Pin0InfoVect0LinkObjId="EC-YR_YX.YR_YX_071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afaaa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="522,-93 522,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae7d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-157 750,-145 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43707@0" ObjectIDZND0="g_2afb800@1" Pin0InfoVect0LinkObjId="g_2afb800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271379_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-157 750,-145 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae7f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="750,-95 750,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2afb800@0" ObjectIDZND0="43481@0" Pin0InfoVect0LinkObjId="EC-YR_YX.YR_YX_072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afb800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="750,-95 750,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae8ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-153 993,-141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43710@0" ObjectIDZND0="g_2ae81b0@1" Pin0InfoVect0LinkObjId="g_2ae81b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-153 993,-141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-91 993,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ae81b0@0" ObjectIDZND0="43483@0" Pin0InfoVect0LinkObjId="EC-YR_YX.YR_YX_073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae81b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="993,-91 993,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae9830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-157 1267,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="43713@0" ObjectIDZND0="g_2ae8f10@1" Pin0InfoVect0LinkObjId="g_2ae8f10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271477_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-157 1267,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae9a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1267,-92 1267,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ae8f10@0" ObjectIDZND0="43482@0" Pin0InfoVect0LinkObjId="EC-YR_YX.YR_YX_074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae8f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1267,-92 1267,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a73aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-92 1556,-82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ae9c70@0" ObjectIDZND0="43484@0" Pin0InfoVect0LinkObjId="EC-YR_YX.YR_YX_075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ae9c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-92 1556,-82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a745e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-134 1491,-148 1498,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2a73cc0@1" Pin0InfoVect0LinkObjId="g_2a73cc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-134 1491,-148 1498,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a74800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1538,-148 1557,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2a73cc0@0" ObjectIDZND0="43716@x" ObjectIDZND1="g_2ae9c70@0" Pin0InfoVect0LinkObjId="SW-271526_0" Pin0InfoVect1LinkObjId="g_2ae9c70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a73cc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1538,-148 1557,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c40a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,2 1491,-15 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="transformer2" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1491,2 1491,-15 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c42c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-66 1491,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-66 1491,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a95ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-356 589,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43701@0" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271314_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="589,-356 589,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a68cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-242 1556,-261 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43714@1" ObjectIDZND0="43715@0" Pin0InfoVect0LinkObjId="SW-271525_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271524_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-242 1556,-261 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a68eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-297 1556,-322 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43715@1" ObjectIDZND0="39173@0" Pin0InfoVect0LinkObjId="g_2a2d560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271525_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-297 1556,-322 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad2ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-156 1556,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="43716@0" ObjectIDZND0="g_2a73cc0@0" ObjectIDZND1="g_2ae9c70@0" Pin0InfoVect0LinkObjId="g_2a73cc0_0" Pin0InfoVect1LinkObjId="g_2ae9c70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-156 1556,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-148 1556,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a73cc0@0" ObjectIDND1="43716@x" ObjectIDZND0="g_2ae9c70@1" Pin0InfoVect0LinkObjId="g_2ae9c70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a73cc0_0" Pin1InfoVect1LinkObjId="SW-271526_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-148 1556,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-757 902,-789 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="43697@x" ObjectIDND1="43696@x" ObjectIDZND0="g_2b1c230@0" ObjectIDZND1="g_2acc4e0@0" ObjectIDZND2="43718@1" Pin0InfoVect0LinkObjId="g_2b1c230_0" Pin0InfoVect1LinkObjId="g_2acc4e0_0" Pin0InfoVect2LinkObjId="g_2ad7800_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-271234_0" Pin1InfoVect1LinkObjId="SW-271233_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="902,-757 902,-789 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a282e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-789 902,-852 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="g_2b1c230@0" ObjectIDND1="43697@x" ObjectIDND2="43696@x" ObjectIDZND0="g_2acc4e0@0" ObjectIDZND1="43718@1" ObjectIDZND2="g_2ad8ab0@0" Pin0InfoVect0LinkObjId="g_2acc4e0_0" Pin0InfoVect1LinkObjId="g_2ad7800_1" Pin0InfoVect2LinkObjId="g_2ad8ab0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b1c230_0" Pin1InfoVect1LinkObjId="SW-271234_0" Pin1InfoVect2LinkObjId="SW-271233_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="902,-789 902,-852 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-478 589,-458 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2a28580@0" ObjectIDZND0="g_2a6bfd0@1" Pin0InfoVect0LinkObjId="g_2a6bfd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a28580_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="589,-478 589,-458 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-402 555,-402 555,-420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a6bfd0@0" ObjectIDND1="43701@x" ObjectIDZND0="g_2a6cb30@0" Pin0InfoVect0LinkObjId="g_2a6cb30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a6bfd0_0" Pin1InfoVect1LinkObjId="SW-271314_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="589,-402 555,-402 555,-420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a85240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-414 589,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a6bfd0@0" ObjectIDZND0="g_2a6cb30@0" ObjectIDZND1="43701@x" Pin0InfoVect0LinkObjId="g_2a6cb30_0" Pin0InfoVect1LinkObjId="SW-271314_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a6bfd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="589,-414 589,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a854a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-402 589,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a6cb30@0" ObjectIDND1="g_2a6bfd0@0" ObjectIDZND0="43701@1" Pin0InfoVect0LinkObjId="SW-271314_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a6cb30_0" Pin1InfoVect1LinkObjId="g_2a6bfd0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="589,-402 589,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a860d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-467 902,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="43699@0" ObjectIDZND0="g_2a85700@1" Pin0InfoVect0LinkObjId="g_2a85700_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-467 902,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a86370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-400 902,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a85700@0" ObjectIDZND0="43700@1" Pin0InfoVect0LinkObjId="SW-271237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a85700_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-400 902,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a873c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="934,-504 902,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer2" ObjectIDND0="g_2a86610@0" ObjectIDZND0="43699@x" ObjectIDZND1="39172@x" Pin0InfoVect0LinkObjId="SW-271236_0" Pin0InfoVect1LinkObjId="g_2a881d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a86610_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="934,-504 902,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a87f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-494 902,-504 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer2" ObjectIDND0="43699@1" ObjectIDZND0="g_2a86610@0" ObjectIDZND1="39172@x" Pin0InfoVect0LinkObjId="g_2a86610_0" Pin0InfoVect1LinkObjId="g_2a881d0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-494 902,-504 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a881d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-504 902,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="g_2a86610@0" ObjectIDND1="43699@x" ObjectIDZND0="39172@0" Pin0InfoVect0LinkObjId="g_29be440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a86610_0" Pin1InfoVect1LinkObjId="SW-271236_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-504 902,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29baaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-702 902,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43696@0" ObjectIDZND0="43698@x" ObjectIDZND1="39171@x" Pin0InfoVect0LinkObjId="SW-271235_0" Pin0InfoVect1LinkObjId="SW-234627_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-271233_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-702 902,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29bad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-671 902,-685 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="39171@1" ObjectIDZND0="43698@x" ObjectIDZND1="43696@x" Pin0InfoVect0LinkObjId="SW-271235_0" Pin0InfoVect1LinkObjId="SW-271233_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-234627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="902,-671 902,-685 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29be1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-637 902,-627 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48774@0" ObjectIDZND0="48775@1" Pin0InfoVect0LinkObjId="SW-316470_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b35d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-637 902,-627 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29be440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="902,-605 902,-598 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="48775@0" ObjectIDZND0="39172@1" Pin0InfoVect0LinkObjId="g_2a881d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-316470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="902,-605 902,-598 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="1267" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="750" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="902" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="522" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="993" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="589" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="39173" cx="1556" cy="-322" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48774" cx="902" cy="-637" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48774" cx="902" cy="-637" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-234541" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 349.500000 -896.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39167" ObjectName="DYN-YR_YX"/>
     <cge:Meas_Ref ObjectId="234541"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b16380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -422.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_26502b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 16.000000 -860.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_27a8c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 159.000000 -1001.500000) translate(0,16)">永兴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a6a370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -1009.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a4ccb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 369.000000 -977.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a4e850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.500000 36.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4eb60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 410.000000 -348.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ae39d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.291667 -667.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29b2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.500000 -542.000000) translate(0,15)">主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29b2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.500000 -542.000000) translate(0,33)">SZ9-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29b2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.500000 -542.000000) translate(0,51)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29b2080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 701.500000 -542.000000) translate(0,69)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b2390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -12.000000 -27.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b2500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -37.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b2500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -37.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b2830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -67.000000) translate(0,17)">6881113</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a17410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 810.291667 -578.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ad7a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 521.000000 -546.000000) translate(0,12)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b1d1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 -622.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c6150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 914.291667 -725.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c67c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 943.291667 -784.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afc640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.291667 -711.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afcaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.291667 -482.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afcce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 911.291667 -375.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afcf20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -379.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -234.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 -287.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 538.000000 -182.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 771.000000 -237.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afda60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 767.000000 -285.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afdea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 762.000000 -181.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -233.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 -284.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1007.000000 -176.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -238.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afe9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1278.000000 -288.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afec20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1286.000000 -183.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afee60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1575.000000 -238.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff0a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -285.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aff2e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 -178.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ad2f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 141.000000 -616.000000) translate(0,15)">公用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_2ad3f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -946.000000) translate(0,14)">35kV万兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a1b190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 284.500000 -982.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a88470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -48.000000) translate(0,12)">10kV那软线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ed240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1232.000000 -46.000000) translate(0,12)">10kV灰坝线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29edac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 946.000000 -45.000000) translate(0,12)">10kV干树子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ee660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 480.000000 -48.000000) translate(0,12)">10kV苍蒲地线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ef200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 702.000000 -49.000000) translate(0,12)">10kV永兴街线</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-234627">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.291667 -638.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="39171" ObjectName="SW-YR_YX.YR_YX_301BK"/>
     <cge:Meas_Ref ObjectId="234627"/>
    <cge:TPSR_Ref TObjectID="39171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271236">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 893.291667 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43699" ObjectName="SW-YR_YX.YR_YX_001BK"/>
     <cge:Meas_Ref ObjectId="271236"/>
    <cge:TPSR_Ref TObjectID="43699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271524">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1547.000000 -207.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43714" ObjectName="SW-YR_YX.YR_YX_075BK"/>
     <cge:Meas_Ref ObjectId="271524"/>
    <cge:TPSR_Ref TObjectID="43714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -208.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43711" ObjectName="SW-YR_YX.YR_YX_074BK"/>
     <cge:Meas_Ref ObjectId="271475"/>
    <cge:TPSR_Ref TObjectID="43711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271377">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 741.000000 -208.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43705" ObjectName="SW-YR_YX.YR_YX_072BK"/>
     <cge:Meas_Ref ObjectId="271377"/>
    <cge:TPSR_Ref TObjectID="43705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271328">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 513.000000 -207.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43702" ObjectName="SW-YR_YX.YR_YX_071BK"/>
     <cge:Meas_Ref ObjectId="271328"/>
    <cge:TPSR_Ref TObjectID="43702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-271426">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -204.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43708" ObjectName="SW-YR_YX.YR_YX_073BK"/>
     <cge:Meas_Ref ObjectId="271426"/>
    <cge:TPSR_Ref TObjectID="43708"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_WM" endPointId="0" endStationName="YR_YX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_wanxing" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="902,-924 902,-897 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="43718" ObjectName="AC-35kV.LN_wanxing"/>
    <cge:TPSR_Ref TObjectID="43718_SS-285"/></metadata>
   <polyline fill="none" opacity="0" points="902,-924 902,-897 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2acc4e0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 938.291667 -873.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad8ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 829.291667 -800.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b1c230">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 774.791667 -734.500000)" xlink:href="#lightningRod:shape213"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afaaa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 517.000000 -90.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afb800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 745.000000 -92.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae81b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 988.000000 -88.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae8f10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 1262.000000 -89.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ae9c70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 1551.000000 -89.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a73cc0">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -0.500000 -0.000000 1541.000000 -143.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6bfd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.000000 -409.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6cb30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 548.000000 -416.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a85700">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.633333 897.291667 -397.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a86610">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 930.291667 -511.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-234615" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.291667 -627.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234615" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39171"/>
     <cge:Term_Ref ObjectID="58802"/>
    <cge:TPSR_Ref TObjectID="39171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-234616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.291667 -627.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39171"/>
     <cge:Term_Ref ObjectID="58802"/>
    <cge:TPSR_Ref TObjectID="39171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-234611" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.291667 -627.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234611" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39171"/>
     <cge:Term_Ref ObjectID="58802"/>
    <cge:TPSR_Ref TObjectID="39171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-234614" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.291667 -627.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234614" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39171"/>
     <cge:Term_Ref ObjectID="58802"/>
    <cge:TPSR_Ref TObjectID="39171"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-234639" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-234640" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-234641" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234641" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-234646" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-234642" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-234645" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1586.000000 -437.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="234645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39173"/>
     <cge:Term_Ref ObjectID="58808"/>
    <cge:TPSR_Ref TObjectID="39173"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 491.000000 -13.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43702"/>
     <cge:Term_Ref ObjectID="19946"/>
    <cge:TPSR_Ref TObjectID="43702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271074" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 491.000000 -13.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271074" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43702"/>
     <cge:Term_Ref ObjectID="19946"/>
    <cge:TPSR_Ref TObjectID="43702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271070" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 491.000000 -13.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271070" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43702"/>
     <cge:Term_Ref ObjectID="19946"/>
    <cge:TPSR_Ref TObjectID="43702"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43705"/>
     <cge:Term_Ref ObjectID="19952"/>
    <cge:TPSR_Ref TObjectID="43705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43705"/>
     <cge:Term_Ref ObjectID="19952"/>
    <cge:TPSR_Ref TObjectID="43705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43705"/>
     <cge:Term_Ref ObjectID="19952"/>
    <cge:TPSR_Ref TObjectID="43705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271085" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -18.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271085" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43708"/>
     <cge:Term_Ref ObjectID="19958"/>
    <cge:TPSR_Ref TObjectID="43708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271086" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -18.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271086" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43708"/>
     <cge:Term_Ref ObjectID="19958"/>
    <cge:TPSR_Ref TObjectID="43708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271082" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -18.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271082" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43708"/>
     <cge:Term_Ref ObjectID="19958"/>
    <cge:TPSR_Ref TObjectID="43708"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 -15.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43711"/>
     <cge:Term_Ref ObjectID="19964"/>
    <cge:TPSR_Ref TObjectID="43711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271092" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 -15.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271092" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43711"/>
     <cge:Term_Ref ObjectID="19964"/>
    <cge:TPSR_Ref TObjectID="43711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 -15.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43711"/>
     <cge:Term_Ref ObjectID="19964"/>
    <cge:TPSR_Ref TObjectID="43711"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43714"/>
     <cge:Term_Ref ObjectID="19970"/>
    <cge:TPSR_Ref TObjectID="43714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271098" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271098" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43714"/>
     <cge:Term_Ref ObjectID="19970"/>
    <cge:TPSR_Ref TObjectID="43714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -14.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43714"/>
     <cge:Term_Ref ObjectID="19970"/>
    <cge:TPSR_Ref TObjectID="43714"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-271064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.291667 -448.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43699"/>
     <cge:Term_Ref ObjectID="19940"/>
    <cge:TPSR_Ref TObjectID="43699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-271065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.291667 -448.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43699"/>
     <cge:Term_Ref ObjectID="19940"/>
    <cge:TPSR_Ref TObjectID="43699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-271055" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.291667 -448.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271055" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43699"/>
     <cge:Term_Ref ObjectID="19940"/>
    <cge:TPSR_Ref TObjectID="43699"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-271069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1060.291667 -529.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39172"/>
     <cge:Term_Ref ObjectID="58807"/>
    <cge:TPSR_Ref TObjectID="39172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="0" id="ME-271068" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.291667 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="271068" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="39172"/>
     <cge:Term_Ref ObjectID="58807"/>
    <cge:TPSR_Ref TObjectID="39172"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="358" y="-1017"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="358" y="-983"/></g>
   <g href="35kV永兴变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="811" y="-579"/></g>
   <g href="35kV永兴变10kV苍蒲地线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="543" y="-234"/></g>
   <g href="35kV永兴变10kV永兴街线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="771" y="-237"/></g>
   <g href="35kV永兴变10kV干树子线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1012" y="-233"/></g>
   <g href="35kV永兴变10kV灰坝线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1291" y="-238"/></g>
   <g href="35kV永兴变10kV那软线075间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1575" y="-238"/></g>
   <g href="35kV永兴变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="141" y="-616"/></g>
   <g href="AVC永兴站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="267" y="-994"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab5c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1001.000000 579.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab5ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 997.000000 597.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab60a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 983.000000 627.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ab62c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 612.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b1130" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 423.750000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b1310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 439.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b14f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 393.250000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b16d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1519.000000 378.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b18b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1533.000000 361.000000) translate(0,12)">F(HZ):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29b1a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1527.000000 408.500000) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a96ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.000000 -16.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a96cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 433.000000 14.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a96ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 422.000000 -1.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a988b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 973.000000 420.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a98ad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 959.000000 450.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a98cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 948.000000 435.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a66b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1019.000000 530.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a67390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1018.000000 514.000000) translate(0,12)">温度:</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_YX.YR_YX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="58806"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.893939 -0.000000 0.000000 -0.901961 868.291667 -509.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.893939 -0.000000 0.000000 -0.901961 868.291667 -509.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="39172" ObjectName="TF-YR_YX.YR_YX_1T"/>
    <cge:TPSR_Ref TObjectID="39172"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 794.291667 -734.000000)" xlink:href="#transformer2:shape24_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 794.291667 -734.000000)" xlink:href="#transformer2:shape24_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1475.000000 -71.000000)" xlink:href="#transformer2:shape41_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1475.000000 -71.000000)" xlink:href="#transformer2:shape41_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="119" y="-1012"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="119" y="-1012"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="70" y="-1029"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="70" y="-1029"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="358" y="-1017"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="358" y="-1017"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="358" y="-983"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="358" y="-983"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="811" y="-579"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="811" y="-579"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="543" y="-234"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="543" y="-234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="771" y="-237"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="771" y="-237"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1012" y="-233"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1012" y="-233"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1291" y="-238"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1291" y="-238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1575" y="-238"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1575" y="-238"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="141" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="141" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="267" y="-994"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="267" y="-994"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a28580">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 570.000000 -473.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 107.000000 -953.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_YX"/>
</svg>