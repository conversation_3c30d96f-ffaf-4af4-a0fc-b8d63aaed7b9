<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-218" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-196 -1064 2180 1119">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape11">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.974359" x1="16" x2="92" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="37" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="68" x2="36" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="31" x2="0" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.484375" x1="107" x2="76" y1="37" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="52" x2="52" y1="22" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="92" x2="92" y1="22" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape39">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="49" x2="49" y1="6" y2="9"/>
    <rect height="8" stroke-width="0.75" width="18" x="11" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="24" x2="22" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="22" x2="24" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="4" x2="24" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="29" x2="43" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="43" x2="43" y1="0" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="46" x2="46" y1="4" y2="10"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <circle cx="14" cy="13" fillStyle="0" r="13.5" stroke-width="1"/>
    <circle cx="14" cy="34" fillStyle="0" r="14" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer2:shape15_0">
    <circle cx="15" cy="19" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="10" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="10" x2="15" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape15_1">
    <circle cx="15" cy="41" fillStyle="0" r="15" stroke-width="0.306122"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="11" x2="15" y1="50" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="15" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.306122" x1="15" x2="20" y1="45" y2="50"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
   </symbol>
   <symbol id="transformer2:shape70_0">
    <circle cx="39" cy="32" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,93 64,100 " stroke-width="1.1087"/>
    <polyline points="58,100 64,100 " stroke-width="1.1087"/>
    <polyline DF8003:Layer="PUBLIC" points="39,19 32,34 47,34 39,19 39,19 39,19 "/>
   </symbol>
   <symbol id="transformer2:shape70_1">
    <circle cx="39" cy="70" fillStyle="0" r="27" stroke-width="0.650262"/>
    <polyline points="64,100 1,37 " stroke-width="1.1087"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="39" y1="74" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="50" x2="39" y1="66" y2="75"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="39" x2="27" y1="75" y2="66"/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="voltageTransformer:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="32" y2="97"/>
    <circle cx="24" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="26" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="6" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="8" y2="11"/>
    <circle cx="37" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="23" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="26" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="21" y1="15" y2="15"/>
    <circle cx="50" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <ellipse cx="37" cy="24" fillStyle="0" rx="8.5" ry="8" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="73" x2="70" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="67" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="71" x2="71" y1="30" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="65" x2="77" y1="31" y2="31"/>
    <rect height="27" stroke-width="0.416667" width="14" x="64" y="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="71" x2="71" y1="78" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="38" y1="78" y2="78"/>
    <rect height="27" stroke-width="0.416667" width="14" x="30" y="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="10" x2="2" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="0" x2="12" y1="11" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="37" x2="37" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="34" x2="37" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="37" x2="40" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.231293" x1="51" x2="51" y1="15" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.185034" x1="48" x2="51" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.192744" x1="51" x2="54" y1="15" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e81b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33e8bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33e9520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ea1c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33eb420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ec040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ecaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ed520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33eddf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ee6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ee6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f0590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f0590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_33f15b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f31b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33f3da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33f4b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33f54a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f6ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f72e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f79d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33f83f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f95d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33f9f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33faa40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33eebe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33ef840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33fd5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33fe880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33ff510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_340d630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_340e100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3401b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33fcd00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1129" width="2190" x="-201" y="-1069"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="1975" x2="1984" y1="-394" y2="-394"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-147612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25796" ObjectName="SW-YA_XJC.YA_XJC_3811SW"/>
     <cge:Meas_Ref ObjectId="147612"/>
    <cge:TPSR_Ref TObjectID="25796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147614">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25798" ObjectName="SW-YA_XJC.YA_XJC_3816SW"/>
     <cge:Meas_Ref ObjectId="147614"/>
    <cge:TPSR_Ref TObjectID="25798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147676">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25808" ObjectName="SW-YA_XJC.YA_XJC_3011SW"/>
     <cge:Meas_Ref ObjectId="147676"/>
    <cge:TPSR_Ref TObjectID="25808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147753">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25814" ObjectName="SW-YA_XJC.YA_XJC_0011SW"/>
     <cge:Meas_Ref ObjectId="147753"/>
    <cge:TPSR_Ref TObjectID="25814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25828" ObjectName="SW-YA_XJC.YA_XJC_4836SW"/>
     <cge:Meas_Ref ObjectId="147770"/>
    <cge:TPSR_Ref TObjectID="25828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25826" ObjectName="SW-YA_XJC.YA_XJC_4831SW"/>
     <cge:Meas_Ref ObjectId="147768"/>
    <cge:TPSR_Ref TObjectID="25826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 653.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25797" ObjectName="SW-YA_XJC.YA_XJC_38167SW"/>
     <cge:Meas_Ref ObjectId="147613"/>
    <cge:TPSR_Ref TObjectID="25797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25802" ObjectName="SW-YA_XJC.YA_XJC_3826SW"/>
     <cge:Meas_Ref ObjectId="147635"/>
    <cge:TPSR_Ref TObjectID="25802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25801" ObjectName="SW-YA_XJC.YA_XJC_38267SW"/>
     <cge:Meas_Ref ObjectId="147634"/>
    <cge:TPSR_Ref TObjectID="25801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25800" ObjectName="SW-YA_XJC.YA_XJC_3821SW"/>
     <cge:Meas_Ref ObjectId="147633"/>
    <cge:TPSR_Ref TObjectID="25800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25806" ObjectName="SW-YA_XJC.YA_XJC_3836SW"/>
     <cge:Meas_Ref ObjectId="147656"/>
    <cge:TPSR_Ref TObjectID="25806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1486.000000 -938.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25805" ObjectName="SW-YA_XJC.YA_XJC_38367SW"/>
     <cge:Meas_Ref ObjectId="147655"/>
    <cge:TPSR_Ref TObjectID="25805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -787.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25804" ObjectName="SW-YA_XJC.YA_XJC_3831SW"/>
     <cge:Meas_Ref ObjectId="147654"/>
    <cge:TPSR_Ref TObjectID="25804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25813" ObjectName="SW-YA_XJC.YA_XJC_0016SW"/>
     <cge:Meas_Ref ObjectId="147752"/>
    <cge:TPSR_Ref TObjectID="25813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147754">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 770.000000 -430.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25815" ObjectName="SW-YA_XJC.YA_XJC_00117SW"/>
     <cge:Meas_Ref ObjectId="147754"/>
    <cge:TPSR_Ref TObjectID="25815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147711">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -728.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25810" ObjectName="SW-YA_XJC.YA_XJC_3021SW"/>
     <cge:Meas_Ref ObjectId="147711"/>
    <cge:TPSR_Ref TObjectID="25810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -386.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25817" ObjectName="SW-YA_XJC.YA_XJC_4021SW"/>
     <cge:Meas_Ref ObjectId="147758"/>
    <cge:TPSR_Ref TObjectID="25817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147759">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25818" ObjectName="SW-YA_XJC.YA_XJC_4022SW"/>
     <cge:Meas_Ref ObjectId="147759"/>
    <cge:TPSR_Ref TObjectID="25818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.000000 -430.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25819" ObjectName="SW-YA_XJC.YA_XJC_40217SW"/>
     <cge:Meas_Ref ObjectId="147760"/>
    <cge:TPSR_Ref TObjectID="25819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147745">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25811" ObjectName="SW-YA_XJC.YA_XJC_3901SW"/>
     <cge:Meas_Ref ObjectId="147745"/>
    <cge:TPSR_Ref TObjectID="25811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147866">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 976.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25857" ObjectName="SW-YA_XJC.YA_XJC_4122SW"/>
     <cge:Meas_Ref ObjectId="147866"/>
    <cge:TPSR_Ref TObjectID="25857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147864">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 -389.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25855" ObjectName="SW-YA_XJC.YA_XJC_4121SW"/>
     <cge:Meas_Ref ObjectId="147864"/>
    <cge:TPSR_Ref TObjectID="25855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147865">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1140.000000 -441.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25856" ObjectName="SW-YA_XJC.YA_XJC_41217SW"/>
     <cge:Meas_Ref ObjectId="147865"/>
    <cge:TPSR_Ref TObjectID="25856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147767">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25825" ObjectName="SW-YA_XJC.YA_XJC_4832SW"/>
     <cge:Meas_Ref ObjectId="147767"/>
    <cge:TPSR_Ref TObjectID="25825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147769">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25827" ObjectName="SW-YA_XJC.YA_XJC_48317SW"/>
     <cge:Meas_Ref ObjectId="147769"/>
    <cge:TPSR_Ref TObjectID="25827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147875">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25860" ObjectName="SW-YA_XJC.YA_XJC_0521SW"/>
     <cge:Meas_Ref ObjectId="147875"/>
    <cge:TPSR_Ref TObjectID="25860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147874">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25859" ObjectName="SW-YA_XJC.YA_XJC_0522SW"/>
     <cge:Meas_Ref ObjectId="147874"/>
    <cge:TPSR_Ref TObjectID="25859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147876">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 310.000000 -308.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25861" ObjectName="SW-YA_XJC.YA_XJC_05210SW"/>
     <cge:Meas_Ref ObjectId="147876"/>
    <cge:TPSR_Ref TObjectID="25861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147786">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25833" ObjectName="SW-YA_XJC.YA_XJC_4856SW"/>
     <cge:Meas_Ref ObjectId="147786"/>
    <cge:TPSR_Ref TObjectID="25833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147784">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25831" ObjectName="SW-YA_XJC.YA_XJC_4851SW"/>
     <cge:Meas_Ref ObjectId="147784"/>
    <cge:TPSR_Ref TObjectID="25831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147783">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25830" ObjectName="SW-YA_XJC.YA_XJC_4852SW"/>
     <cge:Meas_Ref ObjectId="147783"/>
    <cge:TPSR_Ref TObjectID="25830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147785">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 635.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25832" ObjectName="SW-YA_XJC.YA_XJC_48517SW"/>
     <cge:Meas_Ref ObjectId="147785"/>
    <cge:TPSR_Ref TObjectID="25832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147802">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25838" ObjectName="SW-YA_XJC.YA_XJC_4876SW"/>
     <cge:Meas_Ref ObjectId="147802"/>
    <cge:TPSR_Ref TObjectID="25838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147800">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25836" ObjectName="SW-YA_XJC.YA_XJC_4871SW"/>
     <cge:Meas_Ref ObjectId="147800"/>
    <cge:TPSR_Ref TObjectID="25836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147799">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25835" ObjectName="SW-YA_XJC.YA_XJC_4872SW"/>
     <cge:Meas_Ref ObjectId="147799"/>
    <cge:TPSR_Ref TObjectID="25835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147801">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 799.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25837" ObjectName="SW-YA_XJC.YA_XJC_48717SW"/>
     <cge:Meas_Ref ObjectId="147801"/>
    <cge:TPSR_Ref TObjectID="25837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147761">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 -316.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25820" ObjectName="SW-YA_XJC.YA_XJC_4901SW"/>
     <cge:Meas_Ref ObjectId="147761"/>
    <cge:TPSR_Ref TObjectID="25820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147762">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 963.000000 -306.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25821" ObjectName="SW-YA_XJC.YA_XJC_49017SW"/>
     <cge:Meas_Ref ObjectId="147762"/>
    <cge:TPSR_Ref TObjectID="25821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147763">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1131.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25822" ObjectName="SW-YA_XJC.YA_XJC_4902SW"/>
     <cge:Meas_Ref ObjectId="147763"/>
    <cge:TPSR_Ref TObjectID="25822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147764">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1195.000000 -305.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25823" ObjectName="SW-YA_XJC.YA_XJC_49027SW"/>
     <cge:Meas_Ref ObjectId="147764"/>
    <cge:TPSR_Ref TObjectID="25823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147818">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25843" ObjectName="SW-YA_XJC.YA_XJC_4826SW"/>
     <cge:Meas_Ref ObjectId="147818"/>
    <cge:TPSR_Ref TObjectID="25843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147816">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25841" ObjectName="SW-YA_XJC.YA_XJC_4821SW"/>
     <cge:Meas_Ref ObjectId="147816"/>
    <cge:TPSR_Ref TObjectID="25841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147815">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25840" ObjectName="SW-YA_XJC.YA_XJC_4822SW"/>
     <cge:Meas_Ref ObjectId="147815"/>
    <cge:TPSR_Ref TObjectID="25840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147817">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1339.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25842" ObjectName="SW-YA_XJC.YA_XJC_48217SW"/>
     <cge:Meas_Ref ObjectId="147817"/>
    <cge:TPSR_Ref TObjectID="25842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25848" ObjectName="SW-YA_XJC.YA_XJC_4846SW"/>
     <cge:Meas_Ref ObjectId="147834"/>
    <cge:TPSR_Ref TObjectID="25848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147832">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25846" ObjectName="SW-YA_XJC.YA_XJC_4841SW"/>
     <cge:Meas_Ref ObjectId="147832"/>
    <cge:TPSR_Ref TObjectID="25846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147831">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25845" ObjectName="SW-YA_XJC.YA_XJC_4842SW"/>
     <cge:Meas_Ref ObjectId="147831"/>
    <cge:TPSR_Ref TObjectID="25845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147833">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1485.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25847" ObjectName="SW-YA_XJC.YA_XJC_48417SW"/>
     <cge:Meas_Ref ObjectId="147833"/>
    <cge:TPSR_Ref TObjectID="25847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147851">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -109.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25853" ObjectName="SW-YA_XJC.YA_XJC_4866SW"/>
     <cge:Meas_Ref ObjectId="147851"/>
    <cge:TPSR_Ref TObjectID="25853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147849">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25851" ObjectName="SW-YA_XJC.YA_XJC_4861SW"/>
     <cge:Meas_Ref ObjectId="147849"/>
    <cge:TPSR_Ref TObjectID="25851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147848">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25850" ObjectName="SW-YA_XJC.YA_XJC_4862SW"/>
     <cge:Meas_Ref ObjectId="147848"/>
    <cge:TPSR_Ref TObjectID="25850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147850">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1680.000000 -308.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25852" ObjectName="SW-YA_XJC.YA_XJC_48617SW"/>
     <cge:Meas_Ref ObjectId="147850"/>
    <cge:TPSR_Ref TObjectID="25852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182953">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27842" ObjectName="SW-YA_XJC.YA_XJC_4886SW"/>
     <cge:Meas_Ref ObjectId="182953"/>
    <cge:TPSR_Ref TObjectID="27842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182949">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 -317.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27839" ObjectName="SW-YA_XJC.YA_XJC_4881SW"/>
     <cge:Meas_Ref ObjectId="182949"/>
    <cge:TPSR_Ref TObjectID="27839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182952">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27841" ObjectName="SW-YA_XJC.YA_XJC_4882SW"/>
     <cge:Meas_Ref ObjectId="182952"/>
    <cge:TPSR_Ref TObjectID="27841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182951">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1872.000000 -307.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27840" ObjectName="SW-YA_XJC.YA_XJC_48817SW"/>
     <cge:Meas_Ref ObjectId="182951"/>
    <cge:TPSR_Ref TObjectID="27840"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="352,-779 1792,-779 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25791" ObjectName="BS-YA_XJC.YA_XJC_3M"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   <polyline fill="none" opacity="0" points="352,-779 1792,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="210,-370 1024,-370 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25792" ObjectName="BS-YA_XJC.YA_XJC_9IM"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   <polyline fill="none" opacity="0" points="210,-370 1024,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_XJC.YA_XJC_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1083,-370 1935,-370 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25793" ObjectName="BS-YA_XJC.YA_XJC_9IIM"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   <polyline fill="none" opacity="0" points="1083,-370 1935,-370 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_XJC.YA_XJC_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 203.000000 -68.000000)" xlink:href="#capacitor:shape11"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41921" ObjectName="CB-YA_XJC.YA_XJC_Cb1"/>
    <cge:TPSR_Ref TObjectID="41921"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_Zyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36536"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -637.000000)" xlink:href="#transformer2:shape15_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -637.000000)" xlink:href="#transformer2:shape15_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25864" ObjectName="TF-YA_XJC.YA_XJC_Zyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 46.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1546.000000 46.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36528"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -599.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 670.000000 -599.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25862" ObjectName="TF-YA_XJC.YA_XJC_1T"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_XJC.YA_XJC_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36532"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1366.000000 -599.000000)" xlink:href="#transformer2:shape70_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.901961 1366.000000 -599.000000)" xlink:href="#transformer2:shape70_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25863" ObjectName="TF-YA_XJC.YA_XJC_2T"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2af7710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-779 636,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25796@0" Pin0InfoVect0LinkObjId="SW-147612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-779 636,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2af7900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-829 636,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25796@1" ObjectIDZND0="25795@0" Pin0InfoVect0LinkObjId="SW-147610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-829 636,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bd3e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-872 636,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25795@1" ObjectIDZND0="25798@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147610_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-872 636,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbae40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-100 422,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2b311b0@0" ObjectIDZND0="34043@x" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b311b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="466,-100 422,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbb030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-66 422,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34043@0" ObjectIDZND0="g_2b311b0@0" ObjectIDZND1="25828@x" Pin0InfoVect0LinkObjId="g_2b311b0_0" Pin0InfoVect1LinkObjId="SW-147770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="422,-66 422,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbb220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-100 422,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b311b0@0" ObjectIDND1="34043@x" ObjectIDZND0="25828@0" Pin0InfoVect0LinkObjId="SW-147770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b311b0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.483Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-100 422,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5a920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="658,-945 636,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25797@0" ObjectIDZND0="25798@x" ObjectIDZND1="g_2b76f90@0" ObjectIDZND2="g_2b5ba40@0" Pin0InfoVect0LinkObjId="SW-147614_0" Pin0InfoVect1LinkObjId="g_2b76f90_0" Pin0InfoVect2LinkObjId="g_2b5ba40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="658,-945 636,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b5b200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-928 636,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25798@1" ObjectIDZND0="25797@x" ObjectIDZND1="g_2b76f90@0" ObjectIDZND2="g_2b5ba40@0" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="g_2b76f90_0" Pin0InfoVect2LinkObjId="g_2b5ba40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147614_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="636,-928 636,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b76bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-945 636,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDZND0="g_2b76f90@0" ObjectIDZND1="g_2b5ba40@0" ObjectIDZND2="38083@1" Pin0InfoVect0LinkObjId="g_2b76f90_0" Pin0InfoVect1LinkObjId="g_2b5ba40_0" Pin0InfoVect2LinkObjId="g_2b76da0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="636,-945 636,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b76da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-992 636,-1011 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_2b76f90@0" ObjectIDZND0="38083@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_2b76f90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-992 636,-1011 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b57590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="593,-992 636,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b76f90@0" ObjectIDZND0="25797@x" ObjectIDZND1="25798@x" ObjectIDZND2="g_2b5ba40@0" Pin0InfoVect0LinkObjId="SW-147613_0" Pin0InfoVect1LinkObjId="SW-147614_0" Pin0InfoVect2LinkObjId="g_2b5ba40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b76f90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="593,-992 636,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b59170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-872 1052,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@1" ObjectIDZND0="25802@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-872 1052,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b346d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1074,-945 1052,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25801@0" ObjectIDZND0="25802@x" ObjectIDZND1="g_2ad5c10@0" ObjectIDZND2="g_2b34ab0@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="g_2ad5c10_0" Pin0InfoVect2LinkObjId="g_2b34ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147634_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1074,-945 1052,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b348c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-928 1052,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25802@1" ObjectIDZND0="25801@x" ObjectIDZND1="g_2ad5c10@0" ObjectIDZND2="g_2b34ab0@0" Pin0InfoVect0LinkObjId="SW-147634_0" Pin0InfoVect1LinkObjId="g_2ad5c10_0" Pin0InfoVect2LinkObjId="g_2b34ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-928 1052,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad5830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-945 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25802@x" ObjectIDND1="25801@x" ObjectIDZND0="g_2ad5c10@0" ObjectIDZND1="g_2b34ab0@0" ObjectIDZND2="34316@1" Pin0InfoVect0LinkObjId="g_2ad5c10_0" Pin0InfoVect1LinkObjId="g_2b34ab0_0" Pin0InfoVect2LinkObjId="g_2ad5a20_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147635_0" Pin1InfoVect1LinkObjId="SW-147634_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-945 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad5a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-992 1052,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25802@x" ObjectIDND1="25801@x" ObjectIDND2="g_2ad5c10@0" ObjectIDZND0="34316@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147635_0" Pin1InfoVect1LinkObjId="SW-147634_0" Pin1InfoVect2LinkObjId="g_2ad5c10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-992 1052,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad6e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1009,-992 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2ad5c10@0" ObjectIDZND0="25802@x" ObjectIDZND1="25801@x" ObjectIDZND2="g_2b34ab0@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="g_2b34ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad5c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1009,-992 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b60a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-779 1052,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25800@0" Pin0InfoVect0LinkObjId="SW-147633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-779 1052,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bae4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-845 1052,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25799@0" ObjectIDZND0="25800@1" Pin0InfoVect0LinkObjId="SW-147633_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-845 1052,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bae710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-992 1052,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2b34ab0@0" ObjectIDZND0="25802@x" ObjectIDZND1="25801@x" ObjectIDZND2="g_2ad5c10@0" Pin0InfoVect0LinkObjId="SW-147635_0" Pin0InfoVect1LinkObjId="SW-147634_0" Pin0InfoVect2LinkObjId="g_2ad5c10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b34ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-992 1052,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bae930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="636,-992 660,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="25797@x" ObjectIDND1="25798@x" ObjectIDND2="g_2b76f90@0" ObjectIDZND0="g_2b5ba40@0" Pin0InfoVect0LinkObjId="g_2b5ba40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147613_0" Pin1InfoVect1LinkObjId="SW-147614_0" Pin1InfoVect2LinkObjId="g_2b76f90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="636,-992 660,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb0ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-872 1469,-892 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@1" ObjectIDZND0="25806@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-872 1469,-892 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9d070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1491,-945 1469,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25805@0" ObjectIDZND0="25806@x" ObjectIDZND1="g_2a9e790@0" ObjectIDZND2="g_2a9d4b0@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="g_2a9e790_0" Pin0InfoVect2LinkObjId="g_2a9d4b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1491,-945 1469,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9d290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-928 1469,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="lightningRod" ObjectIDND0="25806@1" ObjectIDZND0="25805@x" ObjectIDZND1="g_2a9e790@0" ObjectIDZND2="g_2a9d4b0@0" Pin0InfoVect0LinkObjId="SW-147655_0" Pin0InfoVect1LinkObjId="g_2a9e790_0" Pin0InfoVect2LinkObjId="g_2a9d4b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-928 1469,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-945 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25806@x" ObjectIDND1="25805@x" ObjectIDZND0="g_2a9e790@0" ObjectIDZND1="g_2a9d4b0@0" ObjectIDZND2="37772@1" Pin0InfoVect0LinkObjId="g_2a9e790_0" Pin0InfoVect1LinkObjId="g_2a9d4b0_0" Pin0InfoVect2LinkObjId="g_2a9e570_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147656_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-945 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a9e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-992 1469,-1003 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="voltageTransformer" EndDevType0="powerLine" ObjectIDND0="25806@x" ObjectIDND1="25805@x" ObjectIDND2="g_2a9e790@0" ObjectIDZND0="37772@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-147656_0" Pin1InfoVect1LinkObjId="SW-147655_0" Pin1InfoVect2LinkObjId="g_2a9e790_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-992 1469,-1003 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b82570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1426,-992 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a9e790@0" ObjectIDZND0="25806@x" ObjectIDZND1="25805@x" ObjectIDZND2="g_2a9d4b0@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="g_2a9d4b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9e790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1426,-992 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b84920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-779 1469,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25804@0" Pin0InfoVect0LinkObjId="SW-147654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-779 1469,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b84b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1469,-845 1469,-828 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25803@0" ObjectIDZND0="25804@1" Pin0InfoVect0LinkObjId="SW-147654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147652_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1469,-845 1469,-828 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b84d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1493,-992 1469,-992 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2a9d4b0@0" ObjectIDZND0="25806@x" ObjectIDZND1="25805@x" ObjectIDZND2="g_2a9e790@0" Pin0InfoVect0LinkObjId="SW-147656_0" Pin0InfoVect1LinkObjId="SW-147655_0" Pin0InfoVect2LinkObjId="g_2a9e790_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9d4b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1493,-992 1469,-992 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aeac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-779 705,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25808@1" Pin0InfoVect0LinkObjId="SW-147676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-779 705,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aeae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-733 705,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25808@0" ObjectIDZND0="25807@1" Pin0InfoVect0LinkObjId="SW-147673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-733 705,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aeb070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-694 705,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25807@0" ObjectIDZND0="25862@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-694 705,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aeb290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-603 705,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25862@0" ObjectIDZND0="g_2b18f20@0" Pin0InfoVect0LinkObjId="g_2b18f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aeb070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-603 705,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aeb4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-538 705,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b18f20@1" ObjectIDZND0="25813@1" Pin0InfoVect0LinkObjId="SW-147752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b18f20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-538 705,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aeb6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-488 705,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25813@0" ObjectIDZND0="25812@1" Pin0InfoVect0LinkObjId="SW-147749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147752_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-488 705,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-437 775,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25814@x" ObjectIDND1="25812@x" ObjectIDZND0="25815@0" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147753_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-437 775,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-447 705,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25812@0" ObjectIDZND0="25815@x" ObjectIDZND1="25814@x" Pin0InfoVect0LinkObjId="SW-147754_0" Pin0InfoVect1LinkObjId="SW-147753_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="705,-447 705,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b9c6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-437 705,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25815@x" ObjectIDND1="25812@x" ObjectIDZND0="25814@1" Pin0InfoVect0LinkObjId="SW-147753_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147754_0" Pin1InfoVect1LinkObjId="SW-147749_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-437 705,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-779 1401,-769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25810@1" Pin0InfoVect0LinkObjId="SW-147711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-779 1401,-769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7be80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-733 1401,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25810@0" ObjectIDZND0="25809@1" Pin0InfoVect0LinkObjId="SW-147708_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-733 1401,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a7c0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-694 1401,-682 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25809@0" ObjectIDZND0="25863@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147708_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-694 1401,-682 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7c340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-603 1401,-591 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="25863@0" ObjectIDZND0="g_2acf0f0@0" Pin0InfoVect0LinkObjId="g_2acf0f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a7c0e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-603 1401,-591 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7c5a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-488 1401,-474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25818@0" ObjectIDZND0="25816@1" Pin0InfoVect0LinkObjId="SW-147755_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147759_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-488 1401,-474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b471a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-437 1471,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25816@x" ObjectIDND1="25817@x" ObjectIDZND0="25819@0" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="SW-147758_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-437 1471,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b47420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-447 1401,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25816@0" ObjectIDZND0="25819@x" ObjectIDZND1="25817@x" Pin0InfoVect0LinkObjId="SW-147760_0" Pin0InfoVect1LinkObjId="SW-147758_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-447 1401,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b47650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-437 1401,-427 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="25816@x" ObjectIDND1="25819@x" ObjectIDZND0="25817@1" Pin0InfoVect0LinkObjId="SW-147758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147755_0" Pin1InfoVect1LinkObjId="SW-147760_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-437 1401,-427 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b48be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-538 1401,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2acf0f0@1" ObjectIDZND0="25818@1" Pin0InfoVect0LinkObjId="SW-147759_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acf0f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-538 1401,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a715d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-779 848,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25791@0" ObjectIDZND0="g_2b48dd0@1" Pin0InfoVect0LinkObjId="g_2b48dd0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-779 848,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a71810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="848,-710 848,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2b48dd0@0" ObjectIDZND0="25864@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b48dd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="848,-710 848,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-779 1052,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25791@0" ObjectIDZND0="25811@1" Pin0InfoVect0LinkObjId="SW-147745_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-779 1052,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-652 1052,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2a73fa0@0" ObjectIDZND0="g_2a74ab0@0" Pin0InfoVect0LinkObjId="g_2a74ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a73fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-652 1052,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b424f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-710 1099,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25811@x" ObjectIDND1="g_2a73fa0@0" ObjectIDZND0="g_2b40e00@0" Pin0InfoVect0LinkObjId="g_2b40e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="g_2a73fa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-710 1099,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b42fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-721 1052,-710 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25811@0" ObjectIDZND0="g_2a73fa0@0" ObjectIDZND1="g_2b40e00@0" Pin0InfoVect0LinkObjId="g_2a73fa0_0" Pin0InfoVect1LinkObjId="g_2b40e00_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-721 1052,-710 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b43220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1052,-710 1052,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25811@x" ObjectIDND1="g_2b40e00@0" ObjectIDZND0="g_2a73fa0@1" Pin0InfoVect0LinkObjId="g_2a73fa0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147745_0" Pin1InfoVect1LinkObjId="g_2b40e00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1052,-710 1052,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b436a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="705,-391 705,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25814@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_2ab5570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147753_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="705,-391 705,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b43eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1401,-391 1401,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25817@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_2ab57d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1401,-391 1401,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab5570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="985,-394 985,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25857@0" ObjectIDZND0="25792@0" Pin0InfoVect0LinkObjId="g_2b436a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147866_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="985,-394 985,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab57d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-394 1119,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25855@0" ObjectIDZND0="25793@0" Pin0InfoVect0LinkObjId="g_2b43eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147864_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-394 1119,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab5a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="985,-430 985,-457 1039,-457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25857@1" ObjectIDZND0="25854@1" Pin0InfoVect0LinkObjId="SW-147862_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147866_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="985,-430 985,-457 1039,-457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ab89e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1145,-448 1119,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25856@0" ObjectIDZND0="25854@x" ObjectIDZND1="25855@x" Pin0InfoVect0LinkObjId="SW-147862_0" Pin0InfoVect1LinkObjId="SW-147864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1145,-448 1119,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b265c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-457 1119,-457 1119,-448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25854@0" ObjectIDZND0="25856@x" ObjectIDZND1="25855@x" Pin0InfoVect0LinkObjId="SW-147865_0" Pin0InfoVect1LinkObjId="SW-147864_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147862_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-457 1119,-457 1119,-448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-448 1119,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25856@x" ObjectIDND1="25854@x" ObjectIDZND0="25855@1" Pin0InfoVect0LinkObjId="SW-147864_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147865_0" Pin1InfoVect1LinkObjId="SW-147862_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1119,-448 1119,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b29990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-162 422,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b307c0@1" ObjectIDZND0="25828@1" Pin0InfoVect0LinkObjId="SW-147770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b307c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-162 422,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b29bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-370 422,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25792@0" ObjectIDZND0="25826@1" Pin0InfoVect0LinkObjId="SW-147768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b436a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-370 422,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b29e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-228 422,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25825@0" ObjectIDZND0="g_2b307c0@0" Pin0InfoVect0LinkObjId="g_2b307c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147767_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-228 422,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a62d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-228 255,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25859@0" ObjectIDZND0="g_2b03940@0" Pin0InfoVect0LinkObjId="g_2b03940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147874_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-228 255,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6c930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-370 255,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25792@0" ObjectIDZND0="25860@1" Pin0InfoVect0LinkObjId="SW-147875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b436a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-370 255,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="482,-314 422,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25827@0" ObjectIDZND0="25826@x" ObjectIDZND1="25824@x" Pin0InfoVect0LinkObjId="SW-147768_0" Pin0InfoVect1LinkObjId="SW-147765_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147769_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="482,-314 422,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6dc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-322 422,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25826@0" ObjectIDZND0="25827@x" ObjectIDZND1="25824@x" Pin0InfoVect0LinkObjId="SW-147769_0" Pin0InfoVect1LinkObjId="SW-147765_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="422,-322 422,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6deb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-314 422,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25827@x" ObjectIDND1="25826@x" ObjectIDZND0="25824@1" Pin0InfoVect0LinkObjId="SW-147765_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147769_0" Pin1InfoVect1LinkObjId="SW-147768_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-314 422,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="274,-135 255,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_2b6f450@0" ObjectIDZND0="g_2b03940@0" ObjectIDZND1="41921@x" Pin0InfoVect0LinkObjId="g_2b03940_0" Pin0InfoVect1LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6f450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="274,-135 255,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b70640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-162 255,-135 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="g_2b03940@1" ObjectIDZND0="g_2b6f450@0" ObjectIDZND1="41921@x" Pin0InfoVect0LinkObjId="g_2b6f450_0" Pin0InfoVect1LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b03940_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="255,-162 255,-135 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b708a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-135 255,-105 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="capacitor" ObjectIDND0="g_2b6f450@0" ObjectIDND1="g_2b03940@0" ObjectIDZND0="41921@0" Pin0InfoVect0LinkObjId="CB-YA_XJC.YA_XJC_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b6f450_0" Pin1InfoVect1LinkObjId="g_2b03940_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-135 255,-105 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c26750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="624,-100 580,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2c24db0@0" ObjectIDZND0="34044@x" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c24db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="624,-100 580,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c269b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-66 580,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34044@0" ObjectIDZND0="g_2c24db0@0" ObjectIDZND1="25833@x" Pin0InfoVect0LinkObjId="g_2c24db0_0" Pin0InfoVect1LinkObjId="SW-147786_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="580,-66 580,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c26c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-100 580,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34044@x" ObjectIDND1="g_2c24db0@0" ObjectIDZND0="25833@0" Pin0InfoVect0LinkObjId="SW-147786_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.485Ld_0" Pin1InfoVect1LinkObjId="g_2c24db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-100 580,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a5b5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-162 580,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2c24060@1" ObjectIDZND0="25833@1" Pin0InfoVect0LinkObjId="SW-147786_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c24060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-162 580,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a5b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-370 580,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25792@0" ObjectIDZND0="25831@1" Pin0InfoVect0LinkObjId="SW-147784_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b436a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-370 580,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a5baa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-228 580,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25830@0" ObjectIDZND0="g_2c24060@0" Pin0InfoVect0LinkObjId="g_2c24060_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147783_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-228 580,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af8490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="640,-314 580,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25832@0" ObjectIDZND0="25831@x" ObjectIDZND1="25829@x" Pin0InfoVect0LinkObjId="SW-147784_0" Pin0InfoVect1LinkObjId="SW-147781_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147785_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="640,-314 580,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-322 580,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25831@0" ObjectIDZND0="25832@x" ObjectIDZND1="25829@x" Pin0InfoVect0LinkObjId="SW-147785_0" Pin0InfoVect1LinkObjId="SW-147781_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="580,-322 580,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af8950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-314 580,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25831@x" ObjectIDND1="25832@x" ObjectIDZND0="25829@1" Pin0InfoVect0LinkObjId="SW-147781_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147784_0" Pin1InfoVect1LinkObjId="SW-147785_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-314 580,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="788,-100 744,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2afd7f0@0" ObjectIDZND0="34045@x" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afd7f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="788,-100 744,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-66 744,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34045@0" ObjectIDZND0="g_2afd7f0@0" ObjectIDZND1="25838@x" Pin0InfoVect0LinkObjId="g_2afd7f0_0" Pin0InfoVect1LinkObjId="SW-147802_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="744,-66 744,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aff650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-100 744,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34045@x" ObjectIDND1="g_2afd7f0@0" ObjectIDZND0="25838@0" Pin0InfoVect0LinkObjId="SW-147802_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.487Ld_0" Pin1InfoVect1LinkObjId="g_2afd7f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-100 744,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae5660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-162 744,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2afcaa0@1" ObjectIDZND0="25838@1" Pin0InfoVect0LinkObjId="SW-147802_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afcaa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-162 744,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae58c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-370 744,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25792@0" ObjectIDZND0="25836@1" Pin0InfoVect0LinkObjId="SW-147800_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b436a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-370 744,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ae5b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-228 744,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25835@0" ObjectIDZND0="g_2afcaa0@0" Pin0InfoVect0LinkObjId="g_2afcaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147799_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-228 744,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="804,-314 744,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25837@0" ObjectIDZND0="25836@x" ObjectIDZND1="25834@x" Pin0InfoVect0LinkObjId="SW-147800_0" Pin0InfoVect1LinkObjId="SW-147797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147801_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="804,-314 744,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0c7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-322 744,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25836@0" ObjectIDZND0="25837@x" ObjectIDZND1="25834@x" Pin0InfoVect0LinkObjId="SW-147801_0" Pin0InfoVect1LinkObjId="SW-147797_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="744,-322 744,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b0ca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-314 744,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25836@x" ObjectIDND1="25837@x" ObjectIDZND0="25834@1" Pin0InfoVect0LinkObjId="SW-147797_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147800_0" Pin1InfoVect1LinkObjId="SW-147801_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-314 744,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b11b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-370 908,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25792@0" ObjectIDZND0="25820@1" Pin0InfoVect0LinkObjId="SW-147761_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b436a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="908,-370 908,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b14880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-315 315,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="25860@x" ObjectIDND1="25858@x" ObjectIDZND0="25861@0" Pin0InfoVect0LinkObjId="SW-147876_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="SW-147872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-315 315,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a64e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-323 255,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25860@0" ObjectIDZND0="25861@x" ObjectIDZND1="25858@x" Pin0InfoVect0LinkObjId="SW-147876_0" Pin0InfoVect1LinkObjId="SW-147872_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="255,-323 255,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a65090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-315 255,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25861@x" ObjectIDND1="25860@x" ObjectIDZND0="25858@1" Pin0InfoVect0LinkObjId="SW-147872_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147876_0" Pin1InfoVect1LinkObjId="SW-147875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-315 255,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a68560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="968,-313 908,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25821@0" ObjectIDZND0="25820@x" ObjectIDZND1="g_2a652f0@0" Pin0InfoVect0LinkObjId="SW-147761_0" Pin0InfoVect1LinkObjId="g_2a652f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147762_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="968,-313 908,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a69050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-321 908,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25820@0" ObjectIDZND0="25821@x" ObjectIDZND1="g_2a652f0@0" Pin0InfoVect0LinkObjId="SW-147762_0" Pin0InfoVect1LinkObjId="g_2a652f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147761_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="908,-321 908,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a692b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="908,-313 908,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25821@x" ObjectIDND1="25820@x" ObjectIDZND0="g_2a652f0@0" Pin0InfoVect0LinkObjId="g_2a652f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147762_0" Pin1InfoVect1LinkObjId="SW-147761_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="908,-313 908,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-370 1140,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25822@1" Pin0InfoVect0LinkObjId="SW-147763_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-370 1140,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1200,-312 1140,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25823@0" ObjectIDZND0="25822@x" ObjectIDZND1="g_2a6f280@0" Pin0InfoVect0LinkObjId="SW-147763_0" Pin0InfoVect1LinkObjId="g_2a6f280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147764_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1200,-312 1140,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-320 1140,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="25822@0" ObjectIDZND0="25823@x" ObjectIDZND1="g_2a6f280@0" Pin0InfoVect0LinkObjId="SW-147764_0" Pin0InfoVect1LinkObjId="g_2a6f280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147763_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-320 1140,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1140,-312 1140,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="voltageTransformer" ObjectIDND0="25822@x" ObjectIDND1="25823@x" ObjectIDZND0="g_2a6f280@0" Pin0InfoVect0LinkObjId="g_2a6f280_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147763_0" Pin1InfoVect1LinkObjId="SW-147764_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1140,-312 1140,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa8ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1328,-100 1284,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2aa7340@0" ObjectIDZND0="34046@x" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa7340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1328,-100 1284,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa8f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-66 1284,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34046@0" ObjectIDZND0="g_2aa7340@0" ObjectIDZND1="25843@x" Pin0InfoVect0LinkObjId="g_2aa7340_0" Pin0InfoVect1LinkObjId="SW-147818_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-66 1284,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa91a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-100 1284,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34046@x" ObjectIDND1="g_2aa7340@0" ObjectIDZND0="25843@0" Pin0InfoVect0LinkObjId="SW-147818_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.482Ld_0" Pin1InfoVect1LinkObjId="g_2aa7340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-100 1284,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a87390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-162 1284,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2aa65f0@1" ObjectIDZND0="25843@1" Pin0InfoVect0LinkObjId="SW-147818_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa65f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-162 1284,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a875f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-370 1284,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25841@1" Pin0InfoVect0LinkObjId="SW-147816_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-370 1284,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a87850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-228 1284,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25840@0" ObjectIDZND0="g_2aa65f0@0" Pin0InfoVect0LinkObjId="g_2aa65f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147815_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-228 1284,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8a650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1344,-314 1284,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25842@0" ObjectIDZND0="25841@x" ObjectIDZND1="25839@x" Pin0InfoVect0LinkObjId="SW-147816_0" Pin0InfoVect1LinkObjId="SW-147813_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147817_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1344,-314 1284,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8a8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-322 1284,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25841@0" ObjectIDZND0="25842@x" ObjectIDZND1="25839@x" Pin0InfoVect0LinkObjId="SW-147817_0" Pin0InfoVect1LinkObjId="SW-147813_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-322 1284,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a8ab10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-314 1284,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25841@x" ObjectIDND1="25842@x" ObjectIDZND0="25839@1" Pin0InfoVect0LinkObjId="SW-147813_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147816_0" Pin1InfoVect1LinkObjId="SW-147817_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-314 1284,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a908c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-100 1430,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2a8f0c0@0" ObjectIDZND0="34047@x" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8f0c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-100 1430,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a90b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-66 1430,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34047@0" ObjectIDZND0="g_2a8f0c0@0" ObjectIDZND1="25848@x" Pin0InfoVect0LinkObjId="g_2a8f0c0_0" Pin0InfoVect1LinkObjId="SW-147834_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-66 1430,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a90d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-100 1430,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34047@x" ObjectIDND1="g_2a8f0c0@0" ObjectIDZND0="25848@0" Pin0InfoVect0LinkObjId="SW-147834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.484Ld_0" Pin1InfoVect1LinkObjId="g_2a8f0c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-100 1430,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a978f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-162 1430,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a8e470@1" ObjectIDZND0="25848@1" Pin0InfoVect0LinkObjId="SW-147834_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8e470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-162 1430,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a97b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-370 1430,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25846@1" Pin0InfoVect0LinkObjId="SW-147832_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-370 1430,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a97db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-228 1430,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25845@0" ObjectIDZND0="g_2a8e470@0" Pin0InfoVect0LinkObjId="g_2a8e470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147831_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-228 1430,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9adb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1490,-314 1430,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25847@0" ObjectIDZND0="25846@x" ObjectIDZND1="25844@x" Pin0InfoVect0LinkObjId="SW-147832_0" Pin0InfoVect1LinkObjId="SW-147829_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147833_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1490,-314 1430,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9b010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-322 1430,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25846@0" ObjectIDZND0="25847@x" ObjectIDZND1="25844@x" Pin0InfoVect0LinkObjId="SW-147833_0" Pin0InfoVect1LinkObjId="SW-147829_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147832_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-322 1430,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-314 1430,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25846@x" ObjectIDND1="25847@x" ObjectIDZND0="25844@1" Pin0InfoVect0LinkObjId="SW-147829_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147832_0" Pin1InfoVect1LinkObjId="SW-147833_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-314 1430,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d56590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1669,-101 1625,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2d54bf0@0" ObjectIDZND0="34042@x" ObjectIDZND1="25853@x" ObjectIDZND2="g_2bb40d0@0" Pin0InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="g_2bb40d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d54bf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1669,-101 1625,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d567f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-67 1625,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="34042@0" ObjectIDZND0="g_2d54bf0@0" ObjectIDZND1="25853@x" ObjectIDZND2="g_2bb40d0@0" Pin0InfoVect0LinkObjId="g_2d54bf0_0" Pin0InfoVect1LinkObjId="SW-147851_0" Pin0InfoVect2LinkObjId="g_2bb40d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-67 1625,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d56a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-101 1625,-114 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d54bf0@0" ObjectIDND1="34042@x" ObjectIDND2="g_2bb40d0@0" ObjectIDZND0="25853@0" Pin0InfoVect0LinkObjId="SW-147851_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d54bf0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect2LinkObjId="g_2bb40d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-101 1625,-114 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5dd80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-163 1625,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d53ea0@1" ObjectIDZND0="25853@1" Pin0InfoVect0LinkObjId="SW-147851_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d53ea0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-163 1625,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5dfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-370 1625,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="25851@1" Pin0InfoVect0LinkObjId="SW-147849_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-370 1625,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d5e240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-229 1625,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25850@0" ObjectIDZND0="g_2d53ea0@0" Pin0InfoVect0LinkObjId="g_2d53ea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147848_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-229 1625,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d61240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1685,-315 1625,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25852@0" ObjectIDZND0="25851@x" ObjectIDZND1="25849@x" Pin0InfoVect0LinkObjId="SW-147849_0" Pin0InfoVect1LinkObjId="SW-147846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1685,-315 1625,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d614a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-323 1625,-315 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25851@0" ObjectIDZND0="25852@x" ObjectIDZND1="25849@x" Pin0InfoVect0LinkObjId="SW-147850_0" Pin0InfoVect1LinkObjId="SW-147846_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147849_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-323 1625,-315 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d61700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-315 1625,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="25852@x" ObjectIDND1="25851@x" ObjectIDZND0="25849@1" Pin0InfoVect0LinkObjId="SW-147846_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-147850_0" Pin1InfoVect1LinkObjId="SW-147849_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-315 1625,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d62ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-101 1562,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d54bf0@0" ObjectIDND1="34042@x" ObjectIDND2="25853@x" ObjectIDZND0="g_2bb40d0@0" Pin0InfoVect0LinkObjId="g_2bb40d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d54bf0_0" Pin1InfoVect1LinkObjId="EC-YA_XJC.486Ld_0" Pin1InfoVect2LinkObjId="SW-147851_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-101 1562,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2d62f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1561,-47 1561,-56 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2bb40d0@1" Pin0InfoVect0LinkObjId="g_2bb40d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="TF-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1561,-47 1561,-56 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a41810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1793,-272 1817,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a40b50@0" ObjectIDZND0="27838@x" ObjectIDZND1="27841@x" Pin0InfoVect0LinkObjId="SW-182947_0" Pin0InfoVect1LinkObjId="SW-182952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a40b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1793,-272 1817,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a41a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="231,-272 255,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a41cd0@0" ObjectIDZND0="25858@x" ObjectIDZND1="25859@x" Pin0InfoVect0LinkObjId="SW-147872_0" Pin0InfoVect1LinkObjId="SW-147874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a41cd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="231,-272 255,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a43310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-279 255,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25858@0" ObjectIDZND0="g_2a41cd0@0" ObjectIDZND1="25859@x" Pin0InfoVect0LinkObjId="g_2a41cd0_0" Pin0InfoVect1LinkObjId="SW-147874_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147872_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="255,-279 255,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a43570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="255,-272 255,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a41cd0@0" ObjectIDND1="25858@x" ObjectIDZND0="25859@1" Pin0InfoVect0LinkObjId="SW-147874_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a41cd0_0" Pin1InfoVect1LinkObjId="SW-147872_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="255,-272 255,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a44580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="398,-272 422,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a437d0@0" ObjectIDZND0="25824@x" ObjectIDZND1="25825@x" Pin0InfoVect0LinkObjId="SW-147765_0" Pin0InfoVect1LinkObjId="SW-147767_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a437d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="398,-272 422,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a45070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-279 422,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25824@0" ObjectIDZND0="g_2a437d0@0" ObjectIDZND1="25825@x" Pin0InfoVect0LinkObjId="g_2a437d0_0" Pin0InfoVect1LinkObjId="SW-147767_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147765_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="422,-279 422,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a452d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="422,-272 422,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a437d0@0" ObjectIDND1="25824@x" ObjectIDZND0="25825@1" Pin0InfoVect0LinkObjId="SW-147767_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a437d0_0" Pin1InfoVect1LinkObjId="SW-147765_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="422,-272 422,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a462e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="556,-272 580,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a45530@0" ObjectIDZND0="25829@x" ObjectIDZND1="25830@x" Pin0InfoVect0LinkObjId="SW-147781_0" Pin0InfoVect1LinkObjId="SW-147783_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a45530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="556,-272 580,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a46dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-279 580,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25829@0" ObjectIDZND0="g_2a45530@0" ObjectIDZND1="25830@x" Pin0InfoVect0LinkObjId="g_2a45530_0" Pin0InfoVect1LinkObjId="SW-147783_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147781_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="580,-279 580,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a47030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="580,-272 580,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a45530@0" ObjectIDND1="25829@x" ObjectIDZND0="25830@1" Pin0InfoVect0LinkObjId="SW-147783_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a45530_0" Pin1InfoVect1LinkObjId="SW-147781_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="580,-272 580,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a48040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="720,-272 744,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a47290@0" ObjectIDZND0="25834@x" ObjectIDZND1="25835@x" Pin0InfoVect0LinkObjId="SW-147797_0" Pin0InfoVect1LinkObjId="SW-147799_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a47290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="720,-272 744,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a48b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-279 744,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25834@0" ObjectIDZND0="g_2a47290@0" ObjectIDZND1="25835@x" Pin0InfoVect0LinkObjId="g_2a47290_0" Pin0InfoVect1LinkObjId="SW-147799_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147797_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="744,-279 744,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a48d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="744,-272 744,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a47290@0" ObjectIDND1="25834@x" ObjectIDZND0="25835@1" Pin0InfoVect0LinkObjId="SW-147799_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a47290_0" Pin1InfoVect1LinkObjId="SW-147797_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="744,-272 744,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a49da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1260,-272 1284,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a48ff0@0" ObjectIDZND0="25839@x" ObjectIDZND1="25840@x" Pin0InfoVect0LinkObjId="SW-147813_0" Pin0InfoVect1LinkObjId="SW-147815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a48ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1260,-272 1284,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4a890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-279 1284,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25839@0" ObjectIDZND0="g_2a48ff0@0" ObjectIDZND1="25840@x" Pin0InfoVect0LinkObjId="g_2a48ff0_0" Pin0InfoVect1LinkObjId="SW-147815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147813_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-279 1284,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4aaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1284,-272 1284,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a48ff0@0" ObjectIDND1="25839@x" ObjectIDZND0="25840@1" Pin0InfoVect0LinkObjId="SW-147815_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a48ff0_0" Pin1InfoVect1LinkObjId="SW-147813_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1284,-272 1284,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4bb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1406,-272 1430,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a4ad50@0" ObjectIDZND0="25844@x" ObjectIDZND1="25845@x" Pin0InfoVect0LinkObjId="SW-147829_0" Pin0InfoVect1LinkObjId="SW-147831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4ad50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1406,-272 1430,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-279 1430,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25844@0" ObjectIDZND0="g_2a4ad50@0" ObjectIDZND1="25845@x" Pin0InfoVect0LinkObjId="g_2a4ad50_0" Pin0InfoVect1LinkObjId="SW-147831_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147829_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-279 1430,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1430,-272 1430,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a4ad50@0" ObjectIDND1="25844@x" ObjectIDZND0="25845@1" Pin0InfoVect0LinkObjId="SW-147831_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a4ad50_0" Pin1InfoVect1LinkObjId="SW-147829_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1430,-272 1430,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4d860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1601,-272 1625,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_2a4cab0@0" ObjectIDZND0="25849@x" ObjectIDZND1="25850@x" Pin0InfoVect0LinkObjId="SW-147846_0" Pin0InfoVect1LinkObjId="SW-147848_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4cab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1601,-272 1625,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4e350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-280 1625,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25849@0" ObjectIDZND0="g_2a4cab0@0" ObjectIDZND1="25850@x" Pin0InfoVect0LinkObjId="g_2a4cab0_0" Pin0InfoVect1LinkObjId="SW-147848_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-147846_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-280 1625,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1625,-272 1625,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a4cab0@0" ObjectIDND1="25849@x" ObjectIDZND0="25850@1" Pin0InfoVect0LinkObjId="SW-147848_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a4cab0_0" Pin1InfoVect1LinkObjId="SW-147846_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1625,-272 1625,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a07390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-100 1817,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2a059f0@0" ObjectIDZND0="34048@x" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a059f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1861,-100 1817,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a075f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-66 1817,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="34048@0" ObjectIDZND0="g_2a059f0@0" ObjectIDZND1="27842@x" Pin0InfoVect0LinkObjId="g_2a059f0_0" Pin0InfoVect1LinkObjId="SW-182953_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-66 1817,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a07850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-100 1817,-113 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34048@x" ObjectIDND1="g_2a059f0@0" ObjectIDZND0="27842@0" Pin0InfoVect0LinkObjId="SW-182953_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_XJC.488Ld_0" Pin1InfoVect1LinkObjId="g_2a059f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-100 1817,-113 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0eb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-162 1817,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a04ca0@1" ObjectIDZND0="27842@1" Pin0InfoVect0LinkObjId="SW-182953_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a04ca0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-162 1817,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0edf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-370 1817,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25793@0" ObjectIDZND0="27839@1" Pin0InfoVect0LinkObjId="SW-182949_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b43eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-370 1817,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0f050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-228 1817,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="27841@0" ObjectIDZND0="g_2a04ca0@0" Pin0InfoVect0LinkObjId="g_2a04ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182952_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-228 1817,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a12050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1877,-314 1817,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27840@0" ObjectIDZND0="27839@x" ObjectIDZND1="27838@x" Pin0InfoVect0LinkObjId="SW-182949_0" Pin0InfoVect1LinkObjId="SW-182947_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182951_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1877,-314 1817,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a122b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-322 1817,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="27839@0" ObjectIDZND0="27840@x" ObjectIDZND1="27838@x" Pin0InfoVect0LinkObjId="SW-182951_0" Pin0InfoVect1LinkObjId="SW-182947_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182949_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-322 1817,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a12510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-314 1817,-306 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="27839@x" ObjectIDND1="27840@x" ObjectIDZND0="27838@1" Pin0InfoVect0LinkObjId="SW-182947_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-182949_0" Pin1InfoVect1LinkObjId="SW-182951_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-314 1817,-306 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a14af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-279 1817,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="27838@0" ObjectIDZND0="g_2a40b50@0" ObjectIDZND1="27841@x" Pin0InfoVect0LinkObjId="g_2a40b50_0" Pin0InfoVect1LinkObjId="SW-182952_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-182947_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-279 1817,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a14d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,-272 1817,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2a40b50@0" ObjectIDND1="27838@x" ObjectIDZND0="27841@1" Pin0InfoVect0LinkObjId="SW-182952_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a40b50_0" Pin1InfoVect1LinkObjId="SW-182947_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1817,-272 1817,-264 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="636" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1052" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1469" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="705" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1401" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="848" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25791" cx="1052" cy="-779" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="705" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="985" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1119" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="255" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="580" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25792" cx="744" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1140" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25793" cx="1817" cy="-370" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-147474" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98.500000 -959.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25770" ObjectName="DYN-YA_XJC"/>
     <cge:Meas_Ref ObjectId="147474"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2bcf700" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">西教场变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2952070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1004.000000 -1056.000000) translate(0,12)">35kV西大连线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29ea900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1430.000000 -1046.000000) translate(0,12)">35kV姚西线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28923c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 591.000000 -1048.000000) translate(0,12)">35kV黄西太线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,12)">SZ9-10000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bb4940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 551.000000 -650.000000) translate(0,42)">Ud=7.44%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -57.000000) translate(0,12)">1号电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -57.000000) translate(0,27)">共22×3只，1056kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b5a320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 198.000000 -57.000000) translate(0,42)">实投运：870kVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b5a690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b8e4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b485b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,12)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b485b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,27)">35±3×2.5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b485b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -650.000000) translate(0,42)">Ud=7.25%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b10b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 870.000000 -156.400000) translate(0,12)">10kVⅠ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a6bd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1102.000000 -155.400000) translate(0,12)">10kVⅡ母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d63160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1508.000000 -130.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 317.000000 -341.000000) translate(0,12)">05210</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 262.000000 -348.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 262.000000 -253.000000) translate(0,12)">0522</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d648d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 264.000000 -300.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1347.000000 -340.000000) translate(0,12)">48217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -347.000000) translate(0,12)">4821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d64f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -253.000000) translate(0,12)">4822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d651d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1291.000000 -138.000000) translate(0,12)">4826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1293.000000 -300.000000) translate(0,12)">482</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1848.000000 -392.000000) translate(0,12)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 805.000000 -340.000000) translate(0,12)">48717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -347.000000) translate(0,12)">4871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d65f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -253.000000) translate(0,12)">4872</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d661b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 -138.000000) translate(0,12)">4876</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d663f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 753.000000 -300.000000) translate(0,12)">487</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1489.000000 -340.000000) translate(0,12)">48417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -347.000000) translate(0,12)">4841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -253.000000) translate(0,12)">4842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -138.000000) translate(0,12)">4846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d66f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -300.000000) translate(0,12)">484</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 642.000000 -340.000000) translate(0,12)">48517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d673b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -347.000000) translate(0,12)">4851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d675f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -253.000000) translate(0,12)">4852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -138.000000) translate(0,12)">4856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 589.000000 -300.000000) translate(0,12)">485</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 485.000000 -340.000000) translate(0,12)">48317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d67ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -347.000000) translate(0,12)">4831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -253.000000) translate(0,12)">4832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 429.000000 -138.000000) translate(0,12)">4836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d685b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 431.000000 -300.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d687f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1160.000000 -474.000000) translate(0,12)">41217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1126.000000 -419.000000) translate(0,12)">4121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 992.000000 -419.000000) translate(0,12)">4122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d68eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.000000 -481.000000) translate(0,12)">412</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d690f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -339.000000) translate(0,12)">49017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 915.000000 -346.000000) translate(0,12)">4901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1686.000000 -341.000000) translate(0,12)">48617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d697b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1632.000000 -348.000000) translate(0,12)">4861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d699f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1632.000000 -254.000000) translate(0,12)">4862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1632.000000 -139.000000) translate(0,12)">4866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d69e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1634.000000 -301.000000) translate(0,12)">486</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 210.000000 -390.000000) translate(0,12)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1203.000000 -338.000000) translate(0,12)">49027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1147.000000 -345.000000) translate(0,12)">4902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.000000 -631.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6a9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -463.000000) translate(0,12)">00117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6abf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -416.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -513.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -468.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 712.000000 -758.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b4f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 713.000000 -715.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1474.000000 -463.000000) translate(0,12)">40217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6b970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -416.000000) translate(0,12)">4021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6bbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -513.000000) translate(0,12)">4022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6bdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -468.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1408.000000 -758.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1410.000000 -715.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 282.000000 -785.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -818.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6c930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 672.000000 -971.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6cb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.000000 -917.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6cdb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 645.000000 -866.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2d6cff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -746.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2ea50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -817.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2ec90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -971.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2eed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1059.000000 -917.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1061.000000 -866.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -817.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1505.000000 -971.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2f7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1476.000000 -917.000000) translate(0,12)">3836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2fa10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1478.000000 -866.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a12770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.000000 -340.000000) translate(0,12)">48817</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a14f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -347.000000) translate(0,12)">4881</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a15420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -253.000000) translate(0,12)">4882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a15660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1824.000000 -138.000000) translate(0,12)">4886</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a158a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1826.000000 -300.000000) translate(0,12)">488</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a167a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -1001.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2a18b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 216.000000 -1038.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1be30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -143.000000 -625.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1c7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1001.000000 -609.000000) translate(0,12)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1e340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 398.000000 -30.000000) translate(0,12)">麻纺厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.000000 -29.000000) translate(0,12)">龙岗线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a1f910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -32.000000) translate(0,12)">官屯线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a20190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -38.000000) translate(0,12)">城区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a20a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1399.000000 -36.000000) translate(0,12)">观测站线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a21290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1606.000000 -35.000000) translate(0,12)">仁和线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a21b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1787.000000 -39.000000) translate(0,12)">金龟街线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_31a57b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -196.000000 -56.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b251a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b251a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -66.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b78600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -96.000000) translate(0,17)">5712404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31abbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 553.000000 -670.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31ac640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -668.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_31ad3b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 217.000000 -923.000000) translate(0,16)">AVC</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-147610">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 627.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25795" ObjectName="SW-YA_XJC.YA_XJC_381BK"/>
     <cge:Meas_Ref ObjectId="147610"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -686.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25807" ObjectName="SW-YA_XJC.YA_XJC_301BK"/>
     <cge:Meas_Ref ObjectId="147673"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 696.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25812" ObjectName="SW-YA_XJC.YA_XJC_001BK"/>
     <cge:Meas_Ref ObjectId="147749"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147765">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25824" ObjectName="SW-YA_XJC.YA_XJC_061BK"/>
     <cge:Meas_Ref ObjectId="147765"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1043.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25799" ObjectName="SW-YA_XJC.YA_XJC_382BK"/>
     <cge:Meas_Ref ObjectId="147631"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147652">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1460.000000 -837.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25803" ObjectName="SW-YA_XJC.YA_XJC_383BK"/>
     <cge:Meas_Ref ObjectId="147652"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147708">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -686.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25809" ObjectName="SW-YA_XJC.YA_XJC_302BK"/>
     <cge:Meas_Ref ObjectId="147708"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147755">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1392.000000 -439.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25816" ObjectName="SW-YA_XJC.YA_XJC_402BK"/>
     <cge:Meas_Ref ObjectId="147755"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147862">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1030.000000 -447.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25854" ObjectName="SW-YA_XJC.YA_XJC_412BK"/>
     <cge:Meas_Ref ObjectId="147862"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147872">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 246.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25858" ObjectName="SW-YA_XJC.YA_XJC_052BK"/>
     <cge:Meas_Ref ObjectId="147872"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147781">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25829" ObjectName="SW-YA_XJC.YA_XJC_485BK"/>
     <cge:Meas_Ref ObjectId="147781"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147797">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 735.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25834" ObjectName="SW-YA_XJC.YA_XJC_487BK"/>
     <cge:Meas_Ref ObjectId="147797"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147813">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25839" ObjectName="SW-YA_XJC.YA_XJC_482BK"/>
     <cge:Meas_Ref ObjectId="147813"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147829">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25844" ObjectName="SW-YA_XJC.YA_XJC_484BK"/>
     <cge:Meas_Ref ObjectId="147829"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-147846">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -272.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25849" ObjectName="SW-YA_XJC.YA_XJC_486BK"/>
     <cge:Meas_Ref ObjectId="147846"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-182947">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1808.000000 -271.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="27838" ObjectName="SW-YA_XJC.YA_XJC_488BK"/>
     <cge:Meas_Ref ObjectId="182947"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_XJC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xidalianTxjc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1052,-1041 1052,-1013 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34316" ObjectName="AC-35kV.LN_xidalianTxjc"/>
    <cge:TPSR_Ref TObjectID="34316_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="1052,-1041 1052,-1013 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YA" endPointId="0" endStationName="YA_XJC" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_yaoxi" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1469,-1030 1469,-1002 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37772" ObjectName="AC-35kV.LN_yaoxi"/>
    <cge:TPSR_Ref TObjectID="37772_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="1469,-1030 1469,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_XJC" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_huangxitaiXJC" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="636,-1009 636,-1030 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38083" ObjectName="AC-35kV.LN_huangxitaiXJC"/>
    <cge:TPSR_Ref TObjectID="38083_SS-218"/></metadata>
   <polyline fill="none" opacity="0" points="636,-1009 636,-1030 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b307c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b311b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb40d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1566.000000 -106.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5ba40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 655.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b34ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9d4b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1488.000000 -986.000000)" xlink:href="#lightningRod:shape39"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b18f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 700.000000 -533.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acf0f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -533.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b48dd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 -705.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a73fa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1047.000000 -647.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b40e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -656.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b03940">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 250.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6f450">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 322.500000 -121.500000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c24060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2c24db0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 617.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afcaa0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afd7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 781.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa65f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa7340">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1321.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8e470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8f0c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1467.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d53ea0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1620.000000 -158.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d54bf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1662.000000 -47.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a40b50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1736.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a41cd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a437d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 341.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a45530">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 499.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a47290">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a48ff0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1203.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4ad50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1349.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4cab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1544.000000 -265.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a04ca0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1812.000000 -157.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a059f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1854.000000 -46.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147498" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147498" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147499" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147499" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147496" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 -883.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147496" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25795"/>
     <cge:Term_Ref ObjectID="36392"/>
    <cge:TPSR_Ref TObjectID="25795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147503" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147503" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147504" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147504" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147501" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1178.000000 -878.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25799"/>
     <cge:Term_Ref ObjectID="36400"/>
    <cge:TPSR_Ref TObjectID="25799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147508" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1585.000000 -879.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25803"/>
     <cge:Term_Ref ObjectID="36408"/>
    <cge:TPSR_Ref TObjectID="25803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147514" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147514" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147511" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -731.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147511" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25807"/>
     <cge:Term_Ref ObjectID="36416"/>
    <cge:TPSR_Ref TObjectID="25807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147530" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -482.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147530" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147531" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -482.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147527" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 648.000000 -482.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147527" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25812"/>
     <cge:Term_Ref ObjectID="36426"/>
    <cge:TPSR_Ref TObjectID="25812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147539" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147539" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147535" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -484.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25816"/>
     <cge:Term_Ref ObjectID="36434"/>
    <cge:TPSR_Ref TObjectID="25816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147520" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147521" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147521" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147517" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1353.000000 -734.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147517" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25809"/>
     <cge:Term_Ref ObjectID="36420"/>
    <cge:TPSR_Ref TObjectID="25809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147588" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147588" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147589" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147589" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147586" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 247.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147586" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25858"/>
     <cge:Term_Ref ObjectID="36518"/>
    <cge:TPSR_Ref TObjectID="25858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147553" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 413.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25824"/>
     <cge:Term_Ref ObjectID="36450"/>
    <cge:TPSR_Ref TObjectID="25824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 6.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 6.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 6.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25829"/>
     <cge:Term_Ref ObjectID="36460"/>
    <cge:TPSR_Ref TObjectID="25829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147563" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147563" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147564" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 736.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25834"/>
     <cge:Term_Ref ObjectID="36470"/>
    <cge:TPSR_Ref TObjectID="25834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147569" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1275.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25839"/>
     <cge:Term_Ref ObjectID="36480"/>
    <cge:TPSR_Ref TObjectID="25839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147574" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147574" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147571" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1421.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147571" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25844"/>
     <cge:Term_Ref ObjectID="36490"/>
    <cge:TPSR_Ref TObjectID="25844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147578" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147576" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1665.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147576" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25849"/>
     <cge:Term_Ref ObjectID="36500"/>
    <cge:TPSR_Ref TObjectID="25849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147581" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1042.000000 -530.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25854"/>
     <cge:Term_Ref ObjectID="36510"/>
    <cge:TPSR_Ref TObjectID="25854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147523" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -766.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147523" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147524" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -766.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147524" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -766.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147526" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 361.000000 -766.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147526" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25791"/>
     <cge:Term_Ref ObjectID="36387"/>
    <cge:TPSR_Ref TObjectID="25791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147543" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147545" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147545" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 313.000000 -462.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25792"/>
     <cge:Term_Ref ObjectID="36388"/>
    <cge:TPSR_Ref TObjectID="25792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-147533" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -596.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147533" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-147534" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 851.000000 -596.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25862"/>
     <cge:Term_Ref ObjectID="36526"/>
    <cge:TPSR_Ref TObjectID="25862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-147541" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -597.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-147542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1546.000000 -597.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25863"/>
     <cge:Term_Ref ObjectID="36533"/>
    <cge:TPSR_Ref TObjectID="25863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-147547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-147548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-147549" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-147550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1870.000000 -461.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25793"/>
     <cge:Term_Ref ObjectID="36389"/>
    <cge:TPSR_Ref TObjectID="25793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-182933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-182934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-182925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1809.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="182925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="27838"/>
     <cge:Term_Ref ObjectID="39403"/>
    <cge:TPSR_Ref TObjectID="27838"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="205" y="-1008"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="205" y="-1047"/></g>
   <g href="35kV西教场变10kV1号电容器481间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="264" y="-300"/></g>
   <g href="35kV西教场变10kV麻纺厂线483间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="431" y="-300"/></g>
   <g href="35kV西教场变10kV龙岗线485间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="589" y="-300"/></g>
   <g href="35kV西教场变10kV官屯线487间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="753" y="-300"/></g>
   <g href="35kV西教场变10kV城区线482间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1293" y="-300"/></g>
   <g href="35kV西教场变10kV观测站线484间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1439" y="-300"/></g>
   <g href="35kV西教场变10kV仁和线486间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1634" y="-301"/></g>
   <g href="35kV西教场变10kV金龟街线488间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1826" y="-300"/></g>
   <g href="35kV西教场变35kV黄西太线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="645" y="-866"/></g>
   <g href="35kV西教场变35kV西大连线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1061" y="-866"/></g>
   <g href="35kV西教场变35kV姚西线383间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1478" y="-866"/></g>
   <g href="35kV西教场变10kV母联分段412间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="1041" y="-481"/></g>
   <g href="35kV西教场变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="-143" y="-625"/></g>
   <g href="35kV西教场变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="553" y="-670"/></g>
   <g href="35kV西教场变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1246" y="-668"/></g>
   <g href="AVC西教场站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="209" y="-939"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a2fd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 750.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a30790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 765.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a30a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 264.000000 722.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a30c40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 272.000000 736.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a31390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 703.000000 882.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a32640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 867.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a32ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 717.000000 852.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a33550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 581.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a34140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 753.000000 596.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a34a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 446.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a34ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 461.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a34f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 214.000000 418.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 222.000000 432.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 190.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35840" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 179.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a35a80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 204.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a374d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 590.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a37760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 579.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a379a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 604.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a383f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 592.000000 484.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a38680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 469.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a388c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.000000 454.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3cf90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 581.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3d1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1446.000000 596.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3e5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 445.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3e830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 460.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3ea70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1773.000000 417.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3ecb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 431.000000) translate(0,12)">Uc（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a22890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 881.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a22b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 866.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a22dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 851.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a231f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1525.000000 880.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a234b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1514.000000 865.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a236f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1539.000000 850.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a23b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1297.000000 735.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a23dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.000000 720.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1311.000000 705.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1294.000000 485.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a246f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 470.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.000000 455.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a24d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 986.000000 530.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 975.000000 515.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 500.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 357.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 346.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 371.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a25f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 512.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 526.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a268b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a26db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 691.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a3430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1219.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a36f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1208.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a3930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1233.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a3d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1365.000000 -4.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1354.000000 -19.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1379.000000 -34.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1610.000000 -9.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -24.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 -39.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a4f90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1755.000000 -5.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a5250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1744.000000 -20.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_31a5490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1769.000000 -35.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.483Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 417.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34043" ObjectName="EC-YA_XJC.483Ld"/>
    <cge:TPSR_Ref TObjectID="34043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.485Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 575.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34044" ObjectName="EC-YA_XJC.485Ld"/>
    <cge:TPSR_Ref TObjectID="34044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.487Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 739.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34045" ObjectName="EC-YA_XJC.487Ld"/>
    <cge:TPSR_Ref TObjectID="34045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.482Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34046" ObjectName="EC-YA_XJC.482Ld"/>
    <cge:TPSR_Ref TObjectID="34046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.484Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1425.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34047" ObjectName="EC-YA_XJC.484Ld"/>
    <cge:TPSR_Ref TObjectID="34047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.486Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1620.000000 -46.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34042" ObjectName="EC-YA_XJC.486Ld"/>
    <cge:TPSR_Ref TObjectID="34042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_XJC.488Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1812.000000 -45.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34048" ObjectName="EC-YA_XJC.488Ld"/>
    <cge:TPSR_Ref TObjectID="34048"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="205" y="-1008"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="205" y="-1008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="205" y="-1047"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="205" y="-1047"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="264" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="264" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="431" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="431" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="589" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="589" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="753" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="753" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1293" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1293" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1439" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1439" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1634" y="-301"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1634" y="-301"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1826" y="-300"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1826" y="-300"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="645" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="645" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1061" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1061" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1478" y="-866"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1478" y="-866"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="1041" y="-481"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="1041" y="-481"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="-143" y="-625"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="-143" y="-625"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="553" y="-670"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="553" y="-670"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1246" y="-668"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1246" y="-668"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="209,-939 206,-942 206,-887 209,-890 209,-939" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="209,-939 206,-942 265,-942 262,-939 209,-939" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="209,-890 206,-887 265,-887 262,-890 209,-890" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="262,-939 265,-942 265,-887 262,-890 262,-939" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="53" x="209" y="-939"/>
     <rect fill="none" height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="209" y="-939"/>
    </a>
   <metadata/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b76f90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 571.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad5c10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 987.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9e790">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1404.000000 -922.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a74ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1037.000000 -615.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a652f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.000000 -174.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6f280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1102.000000 -173.000000)" xlink:href="#voltageTransformer:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -814.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="YA_XJC:YA_XJC_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-219742" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -774.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="219742" ObjectName="YA_XJC:YA_XJC_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -44.000000 -895.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217887" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -854.000000) translate(0,16)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217887" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_XJC"/>
</svg>