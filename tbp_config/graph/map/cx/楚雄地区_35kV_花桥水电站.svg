<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-50" aopId="3933182" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1201 1755 1202">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="18" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="5" x2="5" y1="13" y2="5"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="1.14"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="1.14"/>
   </symbol>
   <symbol id="lightningRod:shape44">
    <ellipse cx="11" cy="16" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="29" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape113">
    <ellipse cx="12" cy="15" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="18" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="18" y2="14"/>
    <ellipse cx="12" cy="35" rx="11" ry="12" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="17" y1="41" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="7" x2="17" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="7" y1="41" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape130">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="43" x2="43" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="53" x2="33" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="53" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="43" x2="43" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="87" x2="67" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="87" x2="67" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="33" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="20" x2="0" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="77" x2="10" y1="33" y2="33"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape162">
    <ellipse cx="18" cy="67" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="15" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="40" y1="15" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="57" y2="82"/>
    <rect height="27" stroke-width="0.416667" width="14" x="12" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="60" y1="15" y2="53"/>
    <rect height="27" stroke-width="0.416667" width="14" x="53" y="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="18" y1="82" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="30" x2="42" y1="92" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="36" x2="36" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="40" x2="32" y1="94" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="18" y1="75" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="39" x2="36" y1="97" y2="97"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="71" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="69" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="71" y2="69"/>
    <ellipse cx="8" cy="62" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="64" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="66" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="66" y2="64"/>
    <ellipse cx="17" cy="56" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="32" x2="29" y1="61" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="32" x2="28" y1="60" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="29" x2="29" y1="58" y2="64"/>
    <ellipse cx="28" cy="61" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="18" x2="18" y1="54" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="21" x2="18" y1="56" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="15" x2="18" y1="56" y2="54"/>
   </symbol>
   <symbol id="lightningRod:shape163">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <polyline points="34,21 34,9 19,9 " stroke-width="1"/>
    <polyline points="35,33 35,30 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="29" y1="28" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="44" y1="28" y2="28"/>
    <rect height="9" stroke-width="1" width="5" x="32" y="21"/>
    <ellipse cx="8" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="34" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="7" x2="7" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="10" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="4" x2="7" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="10" y1="10" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="9" x2="9" y1="12" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="5" y2="29"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,19 9,31 17,19 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="18" x2="1" y1="33" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675" x1="18" x2="18" y1="27" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.845585" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="28" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="29" y2="29"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="16" x2="-1" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="17" x2="25" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="16" x2="16" y1="28" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-7" x2="-2" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="21" x2="1" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="19" x2="28" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="19" x2="19" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="-8" x2="1" y1="27" y2="27"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape27_0">
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="16,14 22,27 10,27 16,14 16,15 16,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="10" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape27_1">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="15,87 10,75 21,75 15,87 15,86 15,87 "/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198e740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_198f120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_198fb00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19902c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19912d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1991ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1992a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19934c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1993d30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19946d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19964d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_19975e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19991b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1999da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_199ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199b4a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199c520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199cd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199d410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_199de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_199f990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19a0480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a0e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a2e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_19a4100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19a4d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_19b3590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_19b3dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a6f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_19a8560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1212" width="1765" x="3112" y="-1206"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40400">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -736.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6540" ObjectName="SW-CX_HQ.CX_HQ_301BK"/>
     <cge:Meas_Ref ObjectId="40400"/>
    <cge:TPSR_Ref TObjectID="6540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40405">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6545" ObjectName="SW-CX_HQ.CX_HQ_601BK"/>
     <cge:Meas_Ref ObjectId="40405"/>
    <cge:TPSR_Ref TObjectID="6545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40417">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6557" ObjectName="SW-CX_HQ.CX_HQ_621BK"/>
     <cge:Meas_Ref ObjectId="40417"/>
    <cge:TPSR_Ref TObjectID="6557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40421">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4118.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6561" ObjectName="SW-CX_HQ.CX_HQ_622BK"/>
     <cge:Meas_Ref ObjectId="40421"/>
    <cge:TPSR_Ref TObjectID="6561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40425">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6565" ObjectName="SW-CX_HQ.CX_HQ_623BK"/>
     <cge:Meas_Ref ObjectId="40425"/>
    <cge:TPSR_Ref TObjectID="6565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -1.000000 4663.428571 -493.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10970" ObjectName="SW-CX_HQ.CX_HQ_641BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10970"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2394770">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3763.500000 -156.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2388420">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4020.500000 -159.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1fa7070">
    <use class="BV-6KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4276.500000 -166.500000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="4670,-817 4670,-846 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4670,-817 4670,-846 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -620.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 -620.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 -695.000000)" xlink:href="#transformer2:shape27_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3893.000000 -695.000000)" xlink:href="#transformer2:shape27_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.680000 -0.000000 0.000000 -0.633333 4588.000000 -221.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(0.680000 -0.000000 0.000000 -0.633333 4588.000000 -221.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_HQ.P1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 -188.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43417" ObjectName="SM-CX_HQ.P1"/>
    <cge:TPSR_Ref TObjectID="43417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HQ.P2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.000000 -188.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43418" ObjectName="SM-CX_HQ.P2"/>
    <cge:TPSR_Ref TObjectID="43418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_HQ.P3">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4359.000000 -188.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43419" ObjectName="SM-CX_HQ.P3"/>
    <cge:TPSR_Ref TObjectID="43419"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_24932c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-880 4255,-909 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6538@0" ObjectIDZND0="6542@0" Pin0InfoVect0LinkObjId="SW-40402_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_212b300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-880 4255,-909 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_22a3950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-918 4513,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="6544@x" ObjectIDND1="6538@0" ObjectIDZND0="6550@0" Pin0InfoVect0LinkObjId="SW-40410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40404_0" Pin1InfoVect1LinkObjId="g_212b300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-918 4513,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c1e4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-918 4559,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6550@1" ObjectIDZND0="g_2187520@0" Pin0InfoVect0LinkObjId="g_2187520_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-918 4559,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-335 4670,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6554@1" ObjectIDZND0="g_1a127a0@0" Pin0InfoVect0LinkObjId="g_1a127a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40414_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-335 4670,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2720cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1012 4223,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6542@x" ObjectIDND1="0@x" ObjectIDND2="6543@x" ObjectIDZND0="g_2507ca0@0" Pin0InfoVect0LinkObjId="g_2507ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40402_0" Pin1InfoVect1LinkObjId="g_2394770_0" Pin1InfoVect2LinkObjId="SW-40403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1012 4223,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167dd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1057 4255,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2507ca0@0" ObjectIDZND1="6542@x" ObjectIDZND2="6543@x" Pin0InfoVect0LinkObjId="g_2507ca0_0" Pin0InfoVect1LinkObjId="SW-40402_0" Pin0InfoVect2LinkObjId="SW-40403_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1057 4255,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_226e570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-1012 4255,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2507ca0@0" ObjectIDND1="0@x" ObjectIDND2="6543@x" ObjectIDZND0="6542@1" Pin0InfoVect0LinkObjId="SW-40402_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2507ca0_0" Pin1InfoVect1LinkObjId="g_2394770_0" Pin1InfoVect2LinkObjId="SW-40403_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-1012 4255,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfcf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4256,-1012 4274,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2507ca0@0" ObjectIDND1="6542@x" ObjectIDND2="0@x" ObjectIDZND0="6543@0" Pin0InfoVect0LinkObjId="SW-40403_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2507ca0_0" Pin1InfoVect1LinkObjId="SW-40402_0" Pin1InfoVect2LinkObjId="g_2394770_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4256,-1012 4274,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1bfd180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4310,-1012 4320,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6543@1" ObjectIDZND0="g_2187fb0@0" Pin0InfoVect0LinkObjId="g_2187fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40403_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4310,-1012 4320,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2322850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-880 4495,-918 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6538@0" ObjectIDZND0="6544@x" ObjectIDZND1="6550@x" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="SW-40410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_212b300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-880 4495,-918 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2322ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-918 4495,-939 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6538@0" ObjectIDND1="6550@x" ObjectIDZND0="6544@0" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_212b300_0" Pin1InfoVect1LinkObjId="SW-40410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-918 4495,-939 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1741e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-996 4513,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6544@x" ObjectIDND1="g_2378bc0@0" ObjectIDZND0="6549@0" Pin0InfoVect0LinkObjId="SW-40409_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40404_0" Pin1InfoVect1LinkObjId="g_2378bc0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-996 4513,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1742080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4549,-996 4559,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6549@1" ObjectIDZND0="g_210ff40@0" Pin0InfoVect0LinkObjId="g_210ff40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40409_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4549,-996 4559,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d6a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-975 4495,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6544@1" ObjectIDZND0="g_2378bc0@0" ObjectIDZND1="6549@x" Pin0InfoVect0LinkObjId="g_2378bc0_0" Pin0InfoVect1LinkObjId="SW-40409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40404_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-975 4495,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a4fe20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-487 4110,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6546@x" ObjectIDND1="6545@x" ObjectIDZND0="6548@0" Pin0InfoVect0LinkObjId="SW-40408_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40406_0" Pin1InfoVect1LinkObjId="SW-40405_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-487 4110,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a50080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4146,-487 4156,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6548@1" ObjectIDZND0="g_2494020@0" Pin0InfoVect0LinkObjId="g_2494020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40408_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4146,-487 4156,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21addd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-487 4091,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="6545@x" ObjectIDND1="6548@x" ObjectIDZND0="6546@1" Pin0InfoVect0LinkObjId="SW-40406_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40405_0" Pin1InfoVect1LinkObjId="SW-40408_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-487 4091,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_167e020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-501 4091,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="6545@0" ObjectIDZND0="6546@x" ObjectIDZND1="6548@x" Pin0InfoVect0LinkObjId="SW-40406_0" Pin0InfoVect1LinkObjId="SW-40408_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40405_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-501 4091,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_238aef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-413 3870,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6558@1" Pin0InfoVect0LinkObjId="SW-40418_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bea2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-413 3870,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a1c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-360 3870,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6558@0" ObjectIDZND0="6557@x" ObjectIDZND1="6559@x" Pin0InfoVect0LinkObjId="SW-40417_0" Pin0InfoVect1LinkObjId="SW-40419_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-360 3870,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2390340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-343 3870,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6558@x" ObjectIDND1="6559@x" ObjectIDZND0="6557@1" Pin0InfoVect0LinkObjId="SW-40417_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="SW-40419_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-343 3870,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2391420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-298 3870,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="hydroGenerator" ObjectIDND0="6557@0" ObjectIDZND0="g_21cfa20@0" ObjectIDZND1="6560@x" ObjectIDZND2="43417@x" Pin0InfoVect0LinkObjId="g_21cfa20_0" Pin0InfoVect1LinkObjId="SW-40420_0" Pin0InfoVect2LinkObjId="SM-CX_HQ.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-298 3870,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2394510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-288 3870,-246 3870,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="6557@x" ObjectIDND1="g_21cfa20@0" ObjectIDND2="6560@x" ObjectIDZND0="43417@0" Pin0InfoVect0LinkObjId="SM-CX_HQ.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="g_21cfa20_0" Pin1InfoVect2LinkObjId="SW-40420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-288 3870,-246 3870,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_238bbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-190 3870,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_20f4440@0" Pin0InfoVect0LinkObjId="g_20f4440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-190 3870,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23937a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-288 3820,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6557@x" ObjectIDND1="43417@x" ObjectIDZND0="g_21cfa20@0" ObjectIDZND1="6560@x" Pin0InfoVect0LinkObjId="g_21cfa20_0" Pin0InfoVect1LinkObjId="SW-40420_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P1_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-288 3820,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bf0700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-360 4127,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6562@0" ObjectIDZND0="6561@x" ObjectIDZND1="6563@x" Pin0InfoVect0LinkObjId="SW-40421_0" Pin0InfoVect1LinkObjId="SW-40423_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-360 4127,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_167c8d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-343 4127,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6562@x" ObjectIDND1="6563@x" ObjectIDZND0="6561@1" Pin0InfoVect0LinkObjId="SW-40421_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="SW-40423_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-343 4127,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_167cb30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-304 4127,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6561@0" ObjectIDZND0="43418@x" ObjectIDZND1="g_2325b70@0" ObjectIDZND2="6564@x" Pin0InfoVect0LinkObjId="SM-CX_HQ.P2_0" Pin0InfoVect1LinkObjId="g_2325b70_0" Pin0InfoVect2LinkObjId="SW-40424_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40421_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-304 4127,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_167cd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-288 4127,-246 4127,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="hydroGenerator" ObjectIDND0="6561@x" ObjectIDND1="g_2325b70@0" ObjectIDND2="6564@x" ObjectIDZND0="43418@0" Pin0InfoVect0LinkObjId="SM-CX_HQ.P2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40421_0" Pin1InfoVect1LinkObjId="g_2325b70_0" Pin1InfoVect2LinkObjId="SW-40424_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-288 4127,-246 4127,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23bad20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-190 4127,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_1a11c00@0" Pin0InfoVect0LinkObjId="g_1a11c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-190 4127,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23baf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-288 4077,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="43418@x" ObjectIDND1="6561@x" ObjectIDZND0="g_2325b70@0" ObjectIDZND1="6564@x" Pin0InfoVect0LinkObjId="g_2325b70_0" Pin0InfoVect1LinkObjId="SW-40424_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_HQ.P2_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-288 4077,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2241050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-415 4383,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6566@1" Pin0InfoVect0LinkObjId="SW-40426_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bea2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-415 4383,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22412b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-360 4383,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="6566@0" ObjectIDZND0="6565@x" ObjectIDZND1="6567@x" Pin0InfoVect0LinkObjId="SW-40425_0" Pin0InfoVect1LinkObjId="SW-40427_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-360 4383,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fa6bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-343 4383,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="6566@x" ObjectIDND1="6567@x" ObjectIDZND0="6565@1" Pin0InfoVect0LinkObjId="SW-40425_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="SW-40427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-343 4383,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fa6e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-303 4383,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="hydroGenerator" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6565@0" ObjectIDZND0="43419@x" ObjectIDZND1="g_1f6ea90@0" ObjectIDZND2="6568@x" Pin0InfoVect0LinkObjId="SM-CX_HQ.P3_0" Pin0InfoVect1LinkObjId="g_1f6ea90_0" Pin0InfoVect2LinkObjId="SW-40428_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-303 4383,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_228d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4384,-188 4384,-169 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_20cd810@0" Pin0InfoVect0LinkObjId="g_20cd810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4384,-188 4384,-169 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bea2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-435 4091,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6546@0" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_210e880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40406_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-435 4091,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_210e880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-396 4127,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6562@1" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_1bea2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40422_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-396 4127,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20c3930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-413 3823,-435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6551@0" Pin0InfoVect0LinkObjId="SW-40411_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bea2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-413 3823,-435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd75f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3822,-488 3840,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="6551@x" ObjectIDND1="g_21daa00@0" ObjectIDZND0="6552@0" Pin0InfoVect0LinkObjId="SW-40412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40411_0" Pin1InfoVect1LinkObjId="g_21daa00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3822,-488 3840,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1bd7820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3876,-488 3886,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6552@1" ObjectIDZND0="g_2095f50@0" Pin0InfoVect0LinkObjId="g_2095f50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40412_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3876,-488 3886,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_218cf30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-471 3823,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6551@1" ObjectIDZND0="g_21daa00@0" ObjectIDZND1="6552@x" Pin0InfoVect0LinkObjId="g_21daa00_0" Pin0InfoVect1LinkObjId="SW-40412_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40411_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-471 3823,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_218d190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-488 3823,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6551@x" ObjectIDND1="6552@x" ObjectIDZND0="g_21daa00@0" Pin0InfoVect0LinkObjId="g_21daa00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40411_0" Pin1InfoVect1LinkObjId="SW-40412_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-488 3823,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_24967e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-413 4353,-437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="6555@0" Pin0InfoVect0LinkObjId="SW-40415_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bea2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-413 4353,-437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_256fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-490 4370,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6555@x" ObjectIDND1="g_2256640@0" ObjectIDND2="g_218c350@0" ObjectIDZND0="6556@0" Pin0InfoVect0LinkObjId="SW-40416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_2256640_0" Pin1InfoVect2LinkObjId="g_218c350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-490 4370,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2570040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4406,-490 4416,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6556@1" ObjectIDZND0="g_23ba470@0" Pin0InfoVect0LinkObjId="g_23ba470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4406,-490 4416,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25702a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-473 4353,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6555@1" ObjectIDZND0="g_2256640@0" ObjectIDZND1="g_218c350@0" ObjectIDZND2="6556@x" Pin0InfoVect0LinkObjId="g_2256640_0" Pin0InfoVect1LinkObjId="g_218c350_0" Pin0InfoVect2LinkObjId="SW-40416_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-473 4353,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2017f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-237 4383,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" EndDevType0="breaker" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="43419@0" ObjectIDZND0="6565@x" ObjectIDZND1="g_1f6ea90@0" ObjectIDZND2="6568@x" Pin0InfoVect0LinkObjId="SW-40425_0" Pin0InfoVect1LinkObjId="g_1f6ea90_0" Pin0InfoVect2LinkObjId="SW-40428_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_HQ.P3_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-237 4383,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20181d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-288 4333,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6565@x" ObjectIDND1="43419@x" ObjectIDZND0="g_1f6ea90@0" ObjectIDZND1="6568@x" Pin0InfoVect0LinkObjId="g_1f6ea90_0" Pin0InfoVect1LinkObjId="SW-40428_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P3_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-288 4333,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2018430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-490 4320,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6555@x" ObjectIDND1="g_2256640@0" ObjectIDND2="6556@x" ObjectIDZND0="g_218c350@0" Pin0InfoVect0LinkObjId="g_218c350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_2256640_0" Pin1InfoVect2LinkObjId="SW-40416_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-490 4320,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_15184b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-413 4670,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6539@0" ObjectIDZND0="11303@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1bea2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-413 4670,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21d25b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4669,-487 4687,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="11303@x" ObjectIDND1="10970@x" ObjectIDZND0="11304@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4669,-487 4687,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21d2810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-487 4733,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="11304@1" ObjectIDZND0="g_2320480@0" Pin0InfoVect0LinkObjId="g_2320480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-487 4733,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1cd0bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-470 4670,-487 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="11303@1" ObjectIDZND0="10970@x" ObjectIDZND1="11304@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-470 4670,-487 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_166e2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-487 4670,-501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="11303@x" ObjectIDND1="11304@x" ObjectIDZND0="10970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-487 4670,-501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2257db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-528 4670,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10970@1" ObjectIDZND0="11305@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-528 4670,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2258010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-608 4702,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11305@x" ObjectIDND1="g_1c05850@0" ObjectIDZND0="g_1519d10@0" Pin0InfoVect0LinkObjId="g_1519d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1c05850_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-608 4702,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1c055f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-586 4670,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11305@1" ObjectIDZND0="g_1c05850@0" ObjectIDZND1="g_1519d10@0" Pin0InfoVect0LinkObjId="g_1c05850_0" Pin0InfoVect1LinkObjId="g_1519d10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-586 4670,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1178570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-608 4670,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="11305@x" ObjectIDND1="g_1519d10@0" ObjectIDZND0="g_1c05850@0" Pin0InfoVect0LinkObjId="g_1c05850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1519d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-608 4670,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23793a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1089 4495,-1048 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21cdc60@0" ObjectIDZND0="g_2378bc0@1" Pin0InfoVect0LinkObjId="g_2378bc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21cdc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1089 4495,-1048 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24af130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-1017 4495,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2378bc0@0" ObjectIDZND0="6544@x" ObjectIDZND1="6549@x" Pin0InfoVect0LinkObjId="SW-40404_0" Pin0InfoVect1LinkObjId="SW-40409_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2378bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-1017 4495,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_246e1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-595 4091,-625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="6547@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-595 4091,-625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-705 4091,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="6540@0" Pin0InfoVect0LinkObjId="SW-40400_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-705 4091,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_246e690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-771 4091,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6540@1" ObjectIDZND0="6541@0" Pin0InfoVect0LinkObjId="SW-40401_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40400_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-771 4091,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_212b300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-841 4091,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6541@1" ObjectIDZND0="6538@0" Pin0InfoVect0LinkObjId="g_2323860_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40401_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-841 4091,-880 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2256ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-490 4353,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6555@x" ObjectIDND1="g_218c350@0" ObjectIDND2="6556@x" ObjectIDZND0="g_2256640@0" Pin0InfoVect0LinkObjId="g_2256640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40415_0" Pin1InfoVect1LinkObjId="g_218c350_0" Pin1InfoVect2LinkObjId="SW-40416_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-490 4353,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21cee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4353,-540 4353,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2256640@1" ObjectIDZND0="g_23d05c0@0" Pin0InfoVect0LinkObjId="g_23d05c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2256640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4353,-540 4353,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21cf7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-179 3773,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_21cf080@0" ObjectIDZND0="g_2394770@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21cf080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-179 3773,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_23256b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-288 3820,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6557@x" ObjectIDND1="43417@x" ObjectIDND2="6560@x" ObjectIDZND0="g_21cfa20@1" Pin0InfoVect0LinkObjId="g_21cfa20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P1_0" Pin1InfoVect2LinkObjId="SW-40420_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-288 3820,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2325910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-186 3820,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_21cfa20@0" ObjectIDZND0="g_211dc70@1" Pin0InfoVect0LinkObjId="g_211dc70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_21cfa20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-186 3820,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2574e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-288 4077,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="43418@x" ObjectIDND1="6561@x" ObjectIDND2="6564@x" ObjectIDZND0="g_2325b70@1" Pin0InfoVect0LinkObjId="g_2325b70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HQ.P2_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="SW-40424_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-288 4077,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_25750e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-186 4077,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2325b70@0" ObjectIDZND0="g_2392a30@1" Pin0InfoVect0LinkObjId="g_2392a30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2325b70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-186 4077,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20f41e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-183 4030,-154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2575340@0" ObjectIDZND0="g_2388420@0" Pin0InfoVect0LinkObjId="g_2388420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2575340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-183 4030,-154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f6e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-187 4286,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_218dd20@0" ObjectIDZND0="g_1fa7070@0" Pin0InfoVect0LinkObjId="g_1fa7070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_218dd20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-187 4286,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f6f270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-288 4333,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="6565@x" ObjectIDND1="43419@x" ObjectIDND2="6568@x" ObjectIDZND0="g_1f6ea90@1" Pin0InfoVect0LinkObjId="g_1f6ea90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P3_0" Pin1InfoVect2LinkObjId="SW-40428_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-288 4333,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f6f4d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-192 4333,-172 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f6ea90@0" ObjectIDZND0="g_1ab3e00@1" Pin0InfoVect0LinkObjId="g_1ab3e00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f6ea90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-192 4333,-172 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-343 4369,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6566@x" ObjectIDND1="6565@x" ObjectIDZND0="6567@1" Pin0InfoVect0LinkObjId="SW-40427_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40426_0" Pin1InfoVect1LinkObjId="SW-40425_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-343 4369,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_227d2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-343 4319,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6567@0" ObjectIDZND0="g_210f4b0@0" Pin0InfoVect0LinkObjId="g_210f4b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40427_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-343 4319,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2573ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4127,-343 4113,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6562@x" ObjectIDND1="6561@x" ObjectIDZND0="6563@1" Pin0InfoVect0LinkObjId="SW-40423_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40422_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4127,-343 4113,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2573d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-343 4063,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6563@0" ObjectIDZND0="g_1ccf730@0" Pin0InfoVect0LinkObjId="g_1ccf730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40423_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-343 4063,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f8e600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3870,-343 3857,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="6558@x" ObjectIDND1="6557@x" ObjectIDZND0="6559@1" Pin0InfoVect0LinkObjId="SW-40419_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40418_0" Pin1InfoVect1LinkObjId="SW-40417_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3870,-343 3857,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f8e860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-343 3805,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6559@0" ObjectIDZND0="g_1cced00@0" Pin0InfoVect0LinkObjId="g_1cced00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40419_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-343 3805,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_271f070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-293 4605,-274 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_27012b0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2394770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27012b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-293 4605,-274 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_271f260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-224 4605,-182 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-224 4605,-182 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2722240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3820,-288 3773,-288 3773,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6557@x" ObjectIDND1="43417@x" ObjectIDND2="g_21cfa20@0" ObjectIDZND0="6560@1" Pin0InfoVect0LinkObjId="SW-40420_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40417_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P1_0" Pin1InfoVect2LinkObjId="g_21cfa20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3820,-288 3773,-288 3773,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2483a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3773,-236 3773,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6560@0" ObjectIDZND0="g_21cf080@1" Pin0InfoVect0LinkObjId="g_21cf080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40420_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3773,-236 3773,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22a4380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4077,-288 4030,-288 4030,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43418@x" ObjectIDND1="6561@x" ObjectIDND2="g_2325b70@0" ObjectIDZND0="6564@1" Pin0InfoVect0LinkObjId="SW-40424_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SM-CX_HQ.P2_0" Pin1InfoVect1LinkObjId="SW-40421_0" Pin1InfoVect2LinkObjId="g_2325b70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4077,-288 4030,-288 4030,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_22a45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4030,-235 4030,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6564@0" ObjectIDZND0="g_2575340@1" Pin0InfoVect0LinkObjId="g_2575340_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40424_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4030,-235 4030,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_21273c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4333,-288 4286,-288 4286,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="hydroGenerator" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="6565@x" ObjectIDND1="43419@x" ObjectIDND2="g_1f6ea90@0" ObjectIDZND0="6568@1" Pin0InfoVect0LinkObjId="SW-40428_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-40425_0" Pin1InfoVect1LinkObjId="SM-CX_HQ.P3_0" Pin1InfoVect2LinkObjId="g_1f6ea90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4333,-288 4286,-288 4286,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2127620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-236 4286,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6568@0" ObjectIDZND0="g_218dd20@1" Pin0InfoVect0LinkObjId="g_218dd20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-236 4286,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_228bc30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4091,-528 4091,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6545@1" ObjectIDZND0="6547@0" Pin0InfoVect0LinkObjId="SW-40407_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40405_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4091,-528 4091,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_217d530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4624,-336 4605,-336 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6554@0" ObjectIDZND0="g_27012b0@0" ObjectIDZND1="6553@x" Pin0InfoVect0LinkObjId="g_27012b0_0" Pin0InfoVect1LinkObjId="SW-40413_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4624,-336 4605,-336 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_217d740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-336 4605,-324 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6553@x" ObjectIDND1="6554@x" ObjectIDZND0="g_27012b0@1" Pin0InfoVect0LinkObjId="g_27012b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-40413_0" Pin1InfoVect1LinkObjId="SW-40414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-336 4605,-324 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20955a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-336 4605,-359 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_27012b0@0" ObjectIDND1="6554@x" ObjectIDZND0="6553@0" Pin0InfoVect0LinkObjId="SW-40413_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_27012b0_0" Pin1InfoVect1LinkObjId="SW-40414_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-336 4605,-359 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2095800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4605,-394 4605,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6553@1" ObjectIDZND0="6539@0" Pin0InfoVect0LinkObjId="g_1bea2d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-40413_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4605,-394 4605,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_20c6ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4589,-780 4607,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1670460@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1670460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4589,-780 4607,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_20c7130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4643,-780 4669,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@1" ObjectIDZND2="g_1519020@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="g_2394770_1" Pin0InfoVect2LinkObjId="g_1519020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4643,-780 4669,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_166ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-818 4670,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1519020@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_1519020_0" Pin0InfoVect1LinkObjId="g_2394770_0" Pin0InfoVect2LinkObjId="g_2394770_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-818 4670,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1670200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-800 4702,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="g_1519020@0" Pin0InfoVect0LinkObjId="g_1519020_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="g_2394770_0" Pin1InfoVect2LinkObjId="g_2394770_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-800 4702,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2284df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-767 4670,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@1" ObjectIDZND2="g_1519020@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="g_2394770_1" Pin0InfoVect2LinkObjId="g_1519020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-767 4670,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_2285050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-780 4670,-800 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" ObjectIDZND1="g_1519020@0" Pin0InfoVect0LinkObjId="g_2394770_1" Pin0InfoVect1LinkObjId="g_1519020_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2394770_0" Pin1InfoVect1LinkObjId="g_2394770_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-780 4670,-800 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a1afd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-668 4670,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1c05850@1" ObjectIDZND0="g_22852b0@0" Pin0InfoVect0LinkObjId="g_22852b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c05850_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-668 4670,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1a1b230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-715 4670,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_22852b0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_22852b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-715 4670,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2323600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-789 3908,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2394770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-789 3908,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2323860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3908,-850 3908,-880 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="6538@0" Pin0InfoVect0LinkObjId="g_212b300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2394770_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3908,-850 3908,-880 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="3870" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4383" cy="-415" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4091" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4127" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="3823" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4353" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4670" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4091" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4255" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="4495" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6539" cx="4605" cy="-413" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6538" cx="3908" cy="-880" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3247" y="-1179"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3198" y="-1196"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3417" y="-1166"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3417" y="-1166"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3417" y="-1201"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3417" y="-1201"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3247" y="-1179"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3198" y="-1196"/></g>
   <g href="cx_配调_配网接线图35kV电站.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3417" y="-1166"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3417" y="-1201"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-40402">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4240.000000 -887.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6542" ObjectName="SW-CX_HQ.CX_HQ_3116SW"/>
     <cge:Meas_Ref ObjectId="40402"/>
    <cge:TPSR_Ref TObjectID="6542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40404">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4480.000000 -917.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6544" ObjectName="SW-CX_HQ.CX_HQ_3901SW"/>
     <cge:Meas_Ref ObjectId="40404"/>
    <cge:TPSR_Ref TObjectID="6544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40410">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.000000 4522.000000 -892.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6550" ObjectName="SW-CX_HQ.CX_HQ_39010SW"/>
     <cge:Meas_Ref ObjectId="40410"/>
    <cge:TPSR_Ref TObjectID="6550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40414">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4633.000000 -308.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6554" ObjectName="SW-CX_HQ.CX_HQ_69027SW"/>
     <cge:Meas_Ref ObjectId="40414"/>
    <cge:TPSR_Ref TObjectID="6554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40403">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.068182 -0.000000 0.000000 -0.923077 4282.000000 -988.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6543" ObjectName="SW-CX_HQ.CX_HQ_31167SW"/>
     <cge:Meas_Ref ObjectId="40403"/>
    <cge:TPSR_Ref TObjectID="6543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40409">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.000000 4522.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6549" ObjectName="SW-CX_HQ.CX_HQ_39017SW"/>
     <cge:Meas_Ref ObjectId="40409"/>
    <cge:TPSR_Ref TObjectID="6549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40401">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -783.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6541" ObjectName="SW-CX_HQ.CX_HQ_3011SW"/>
     <cge:Meas_Ref ObjectId="40401"/>
    <cge:TPSR_Ref TObjectID="6541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40406">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6546" ObjectName="SW-CX_HQ.CX_HQ_6011SW"/>
     <cge:Meas_Ref ObjectId="40406"/>
    <cge:TPSR_Ref TObjectID="6546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40408">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4119.000000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6548" ObjectName="SW-CX_HQ.CX_HQ_60117SW"/>
     <cge:Meas_Ref ObjectId="40408"/>
    <cge:TPSR_Ref TObjectID="6548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40407">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4076.000000 -537.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6547" ObjectName="SW-CX_HQ.CX_HQ_6016SW"/>
     <cge:Meas_Ref ObjectId="40407"/>
    <cge:TPSR_Ref TObjectID="6547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40418">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3855.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6558" ObjectName="SW-CX_HQ.CX_HQ_6211SW"/>
     <cge:Meas_Ref ObjectId="40418"/>
    <cge:TPSR_Ref TObjectID="6558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40422">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4112.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6562" ObjectName="SW-CX_HQ.CX_HQ_6221SW"/>
     <cge:Meas_Ref ObjectId="40422"/>
    <cge:TPSR_Ref TObjectID="6562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40426">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4368.000000 -338.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6566" ObjectName="SW-CX_HQ.CX_HQ_6231SW"/>
     <cge:Meas_Ref ObjectId="40426"/>
    <cge:TPSR_Ref TObjectID="6566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40411">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3808.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6551" ObjectName="SW-CX_HQ.CX_HQ_6901SW"/>
     <cge:Meas_Ref ObjectId="40411"/>
    <cge:TPSR_Ref TObjectID="6551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40412">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 3849.000000 -460.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6552" ObjectName="SW-CX_HQ.CX_HQ_69017SW"/>
     <cge:Meas_Ref ObjectId="40412"/>
    <cge:TPSR_Ref TObjectID="6552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40415">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4338.000000 -415.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6555" ObjectName="SW-CX_HQ.CX_HQ_6903SW"/>
     <cge:Meas_Ref ObjectId="40415"/>
    <cge:TPSR_Ref TObjectID="6555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40416">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4379.000000 -462.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6556" ObjectName="SW-CX_HQ.CX_HQ_69037SW"/>
     <cge:Meas_Ref ObjectId="40416"/>
    <cge:TPSR_Ref TObjectID="6556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4655.000000 -412.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11303" ObjectName="SW-CX_HQ.CX_HQ_6411SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4696.000000 -459.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11304" ObjectName="SW-CX_HQ.CX_HQ_64117SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4655.000000 -528.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11305" ObjectName="SW-CX_HQ.CX_HQ_6416SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40427">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4342.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6567" ObjectName="SW-CX_HQ.CX_HQ_62317SW"/>
     <cge:Meas_Ref ObjectId="40427"/>
    <cge:TPSR_Ref TObjectID="6567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40423">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 4086.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6563" ObjectName="SW-CX_HQ.CX_HQ_62217SW"/>
     <cge:Meas_Ref ObjectId="40423"/>
    <cge:TPSR_Ref TObjectID="6563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40419">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.045455 -0.000000 0.000000 -1.076923 3829.000000 -315.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6559" ObjectName="SW-CX_HQ.CX_HQ_62117SW"/>
     <cge:Meas_Ref ObjectId="40419"/>
    <cge:TPSR_Ref TObjectID="6559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40420">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3758.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6560" ObjectName="SW-CX_HQ.CX_HQ_6911SW"/>
     <cge:Meas_Ref ObjectId="40420"/>
    <cge:TPSR_Ref TObjectID="6560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40424">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 -212.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6564" ObjectName="SW-CX_HQ.CX_HQ_6921SW"/>
     <cge:Meas_Ref ObjectId="40424"/>
    <cge:TPSR_Ref TObjectID="6564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40428">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.000000 -213.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6568" ObjectName="SW-CX_HQ.CX_HQ_6931SW"/>
     <cge:Meas_Ref ObjectId="40428"/>
    <cge:TPSR_Ref TObjectID="6568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-40413">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -336.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6553" ObjectName="SW-CX_HQ.CX_HQ_6902SW"/>
     <cge:Meas_Ref ObjectId="40413"/>
    <cge:TPSR_Ref TObjectID="6553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4655.000000 -709.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.022727 -0.000000 0.000000 -1.076923 4616.000000 -752.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3903.000000 -800.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4246.000000 -1052.000000)" xlink:href="#load:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_211dc70">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -128.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2392a30">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4064.000000 -131.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ab3e00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4321.000000 -132.000000)" xlink:href="#lightningRod:shape44"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c05850">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4682.000000 -672.000000)" xlink:href="#lightningRod:shape113"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2507ca0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4228.500000 -1004.500000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2378bc0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -1012.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23d05c0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4310.000000 -550.000000)" xlink:href="#lightningRod:shape130"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2256640">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4344.000000 -504.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21cf080">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3764.000000 -174.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21cfa20">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3811.000000 -181.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2325b70">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4068.000000 -181.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2575340">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4021.000000 -178.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20f4440">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3878.000000 -177.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a11c00">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4135.000000 -177.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_20cd810">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4392.000000 -175.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218dd20">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4277.000000 -182.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f6ea90">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -187.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27012b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4596.000000 -288.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_218c350">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4262.000000 -484.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1519020">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4761.000000 -807.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1519d10">
    <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4761.000000 -615.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21daa00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3784.000000 -507.000000)" xlink:href="#lightningRod:shape162"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_21cdc60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4476.000000 -1087.000000)" xlink:href="#lightningRod:shape163"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22852b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4660.000000 -671.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37319" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3414.000000 -1097.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5901" ObjectName="DYN-CX_HQ"/>
     <cge:Meas_Ref ObjectId="37319"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-599"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1079"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1199"/>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a127a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4665.000000 -326.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2494020" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4151.000000 -478.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23ba470" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4411.000000 -481.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2320480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.055556 4728.000000 -478.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2095f50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3881.000000 -479.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1cced00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ccf730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210f4b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -334.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_210ff40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -987.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2187520" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -909.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2187fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -1003.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1670460" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4564.000000 -771.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_117c7a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4263.000000 -1100.000000) translate(0,15)">花桥线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23ce150" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4466.000000 -1142.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13a80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4695.000000 -669.000000) translate(0,12)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13a80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4695.000000 -669.000000) translate(0,27)">400kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a13a80" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4695.000000 -669.000000) translate(0,42)">6/10kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_246b590" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4577.000000 -178.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f6f740" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3797.000000 -123.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23939e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3840.000000 -90.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23939e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3840.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_238bfc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3765.000000 -120.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2392ff0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4056.000000 -126.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23bb1e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4097.000000 -90.000000) translate(0,12)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23bb1e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4097.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2570cb0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4022.000000 -123.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2125010" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4308.000000 -123.000000) translate(0,12)">励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f000" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4353.000000 -90.000000) translate(0,12)">3号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f000" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4353.000000 -90.000000) translate(0,27)">800kW</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_226f4c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4278.000000 -130.000000) translate(0,12)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11787d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4334.000000 -606.000000) translate(0,12)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1178cc0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4678.000000 -845.000000) translate(0,12)">10kV近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f8eac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3915.000000 -898.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3671.000000 -443.000000) translate(0,12)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4262.000000 -934.000000) translate(0,12)">3116</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4273.000000 -1038.000000) translate(0,12)">31167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4502.000000 -964.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2974f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -943.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2975170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4515.000000 -1020.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29753b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -765.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168ec10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -830.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168ee50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -522.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -460.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -584.000000) translate(0,12)">6016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4132.000000 -516.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4360.000000 -462.000000) translate(0,12)">6903</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168f990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -516.000000) translate(0,12)">69037</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_168fbd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3830.000000 -460.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271f600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3838.000000 -514.000000) translate(0,12)">69017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271f840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -319.000000) translate(0,12)">621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271fa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3877.000000 -385.000000) translate(0,12)">6211</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271fcc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3819.000000 -369.000000) translate(0,12)">62117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271ff00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4136.000000 -319.000000) translate(0,12)">622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb19b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -385.000000) translate(0,12)">6221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb1bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4075.000000 -369.000000) translate(0,12)">62217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb1e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4392.000000 -319.000000) translate(0,12)">623</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -385.000000) translate(0,12)">6231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb22b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4331.000000 -369.000000) translate(0,12)">62317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cb24f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -361.000000) translate(0,12)">69027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218bce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -830.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_218c110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4007.000000 -673.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11a4ee0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4224.000000 -1131.000000) translate(0,15)">至110kV舍资变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2144fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3139.000000 -578.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_21452d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3131.000000 -1057.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2572580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3276.000000 -1168.500000) translate(0,16)">花桥水电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482240" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3786.000000 -621.000000) translate(0,12)">6kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4627.000000 -464.000000) translate(0,12)">6411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4697.000000 -513.000000) translate(0,12)">64117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24829a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4634.000000 -521.000000) translate(0,12)">641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2482be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4625.000000 -577.000000) translate(0,12)">6416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2483cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 -261.000000) translate(0,12)">6911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d9a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4037.000000 -260.000000) translate(0,12)">6921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21d9f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -261.000000) translate(0,12)">6931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -685.000000) translate(0,12)">S9-4000/38.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -685.000000) translate(0,27)">38.5±3×5%/6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_21da180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4127.000000 -685.000000) translate(0,42)">Y,d11,Ud＝7.17%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_228b560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4676.000000 -759.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2095a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4612.000000 -384.000000) translate(0,12)">6902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2576d90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3772.000000 -775.000000) translate(0,15)">1号站用变:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2576d90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3772.000000 -775.000000) translate(0,33)">S11-50/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2576d90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3772.000000 -775.000000) translate(0,51)">35±2*2.5%/0.4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_21195f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3428.000000 -1158.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2119a80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3428.000000 -1193.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_20c7390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4604.000000 -773.000000) translate(0,12)">04167</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_HQ.CX_HQ_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3669,-413 4872,-413 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6539" ObjectName="BS-CX_HQ.CX_HQ_6IM"/>
    <cge:TPSR_Ref TObjectID="6539"/></metadata>
   <polyline fill="none" opacity="0" points="3669,-413 4872,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_HQ.CX_HQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3845,-881 4699,-881 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6538" ObjectName="BS-CX_HQ.CX_HQ_3IM"/>
    <cge:TPSR_Ref TObjectID="6538"/></metadata>
   <polyline fill="none" opacity="0" points="3845,-881 4699,-881 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3235.000000 -1120.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_HQ"/>
</svg>