<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-190" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-2 -1035 1959 1106">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape41">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.299051" x1="44" x2="44" y1="67" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="101" y2="109"/>
    <polyline arcFlag="1" points="44,21 45,21 46,21 46,21 47,22 47,22 48,23 48,23 49,24 49,24 49,25 49,26 50,27 50,28 50,28 49,29 49,30 49,31 49,31 48,32 48,32 47,33 47,33 46,34 46,34 45,34 44,34 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,34 45,34 46,34 46,34 47,35 47,35 48,36 48,36 49,37 49,37 49,38 49,39 50,40 50,41 50,41 49,42 49,43 49,44 49,44 48,45 48,45 47,46 47,46 46,47 46,47 45,47 44,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="44,47 45,47 46,47 46,47 47,48 47,48 48,49 48,49 49,50 49,50 49,51 49,52 50,53 50,54 50,54 49,55 49,56 49,57 49,57 48,58 48,58 47,59 47,59 46,60 46,60 45,60 44,60 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="7" x2="44" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="44" y1="13" y2="13"/>
    <rect height="28" stroke-width="0.398039" width="12" x="1" y="32"/>
    <rect height="26" stroke-width="0.398039" width="12" x="20" y="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="7" x2="7" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.275463" x1="44" x2="44" y1="6" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.682641" x1="26" x2="26" y1="21" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="24" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="44" y1="21" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="26" x2="26" y1="29" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="12" x2="26" y1="88" y2="88"/>
    <polyline points="25,101 27,101 29,100 30,100 32,99 33,98 35,97 36,95 37,93 37,92 38,90 38,88 38,86 37,84 37,83 36,81 35,80 33,78 32,77 30,76 29,76 27,75 25,75 23,75 21,76 20,76 18,77 17,78 15,80 14,81 13,83 13,84 12,86 12,88 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.708333" x1="7" x2="7" y1="67" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.468987" x1="7" x2="44" y1="68" y2="68"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,61 9,39 9,30 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape189">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="13" y1="21" y2="11"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="13,64 38,64 38,35 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="13" y1="58" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="32" x2="44" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="41" x2="35" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="39" x2="37" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="13" y1="21" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="53" y2="0"/>
    <circle cx="13" cy="66" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="80" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="84" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="88" y2="84"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="60" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="64" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="68" y2="64"/>
    <ellipse cx="13" cy="82" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape49_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="41" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="13" y1="36" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="switch2:shape49-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="36" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="11" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="15" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="14" x2="14" y1="37" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="38" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="38" x2="47" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="14" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="54" x2="54" y1="38" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="51" y1="39" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="47" x2="47" y1="43" y2="29"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="33" x2="33" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="33"/>
   </symbol>
   <symbol id="voltageTransformer:shape57">
    <circle cx="18" cy="16" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.374294" x1="45" x2="39" y1="30" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="45" x2="39" y1="23" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="39" x2="39" y1="33" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="11" y1="14" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="21" x2="16" y1="19" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="16" x2="16" y1="9" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="12" y1="40" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="22" x2="17" y1="45" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.340267" x1="17" x2="17" y1="34" y2="40"/>
    <ellipse cx="38" cy="28" fillStyle="0" rx="16.5" ry="16" stroke-width="0.340267"/>
    <circle cx="17" cy="37" fillStyle="0" r="16.5" stroke-width="0.340267"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="33" y1="14" y2="14"/>
   </symbol>
   <symbol id="voltageTransformer:shape61">
    <ellipse cx="8" cy="7" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="19" cy="8" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="8" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="18" rx="7.5" ry="7" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="19" x2="19" y1="21" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.103806" x1="22" x2="19" y1="17" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="22" x2="19" y1="20" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="18" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="23" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="20" x2="20" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="6" y1="7" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="11" x2="8" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="8" x2="8" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="5" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="10" x2="7" y1="17" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236902" x1="7" x2="7" y1="19" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2a090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2aaa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b2b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b2bd50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b2cfb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b2dbd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2e630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2ef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2f670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2ff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b2ff70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b30d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b30d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2b318c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b331a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b33d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b34a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b35170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b36830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b37030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b375d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b37da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b38f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b398c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b3a3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b3ad70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b3c030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b3ca20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2b3da80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b3e6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2b4ced0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2b45030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b45cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2b462e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1116" width="1969" x="-7" y="-1040"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="1826" x2="1826" y1="-25" y2="-25"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="1826" x2="1826" y1="-32" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="1893" x2="1893" y1="-122" y2="-18"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,205,0)" stroke-width="1" x1="1826" x2="1893" y1="-18" y2="-18"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-140996">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 -557.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25195" ObjectName="SW-YR_BB.YR_BB_301BK"/>
     <cge:Meas_Ref ObjectId="140996"/>
    <cge:TPSR_Ref TObjectID="25195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141001">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -405.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25197" ObjectName="SW-YR_BB.YR_BB_001BK"/>
     <cge:Meas_Ref ObjectId="141001"/>
    <cge:TPSR_Ref TObjectID="25197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 -557.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25199" ObjectName="SW-YR_BB.YR_BB_302BK"/>
     <cge:Meas_Ref ObjectId="141034"/>
    <cge:TPSR_Ref TObjectID="25199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140978">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -744.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25191" ObjectName="SW-YR_BB.YR_BB_341BK"/>
     <cge:Meas_Ref ObjectId="140978"/>
    <cge:TPSR_Ref TObjectID="25191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141112">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -205.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25211" ObjectName="SW-YR_BB.YR_BB_044BK"/>
     <cge:Meas_Ref ObjectId="141112"/>
    <cge:TPSR_Ref TObjectID="25211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -202.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25217" ObjectName="SW-YR_BB.YR_BB_046BK"/>
     <cge:Meas_Ref ObjectId="141142"/>
    <cge:TPSR_Ref TObjectID="25217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141157">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25220" ObjectName="SW-YR_BB.YR_BB_047BK"/>
     <cge:Meas_Ref ObjectId="141157"/>
    <cge:TPSR_Ref TObjectID="25220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141173">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1816.000000 -218.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25223" ObjectName="SW-YR_BB.YR_BB_041BK"/>
     <cge:Meas_Ref ObjectId="141173"/>
    <cge:TPSR_Ref TObjectID="25223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141082">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -202.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25205" ObjectName="SW-YR_BB.YR_BB_042BK"/>
     <cge:Meas_Ref ObjectId="141082"/>
    <cge:TPSR_Ref TObjectID="25205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141097">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -199.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25208" ObjectName="SW-YR_BB.YR_BB_043BK"/>
     <cge:Meas_Ref ObjectId="141097"/>
    <cge:TPSR_Ref TObjectID="25208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141127">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -202.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25214" ObjectName="SW-YR_BB.YR_BB_045BK"/>
     <cge:Meas_Ref ObjectId="141127"/>
    <cge:TPSR_Ref TObjectID="25214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141039">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -406.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25201" ObjectName="SW-YR_BB.YR_BB_002BK"/>
     <cge:Meas_Ref ObjectId="141039"/>
    <cge:TPSR_Ref TObjectID="25201"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_276ce40">
    <use class="BV-35KV" transform="matrix(0.892857 -0.000000 0.000000 -0.827586 826.000000 -466.000000)" xlink:href="#voltageTransformer:shape57"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23c3dd0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1754.000000 -469.000000)" xlink:href="#voltageTransformer:shape61"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YR_BB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_banbieTlc" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1240,-935 1240,-980 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37845" ObjectName="AC-35kV.LN_banbieTlc"/>
    <cge:TPSR_Ref TObjectID="37845_SS-190"/></metadata>
   <polyline fill="none" opacity="0" points="1240,-935 1240,-980 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -23.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33973" ObjectName="EC-YR_BB.BB_044Ld"/>
    <cge:TPSR_Ref TObjectID="33973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_046Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33971" ObjectName="EC-YR_BB.BB_046Ld"/>
    <cge:TPSR_Ref TObjectID="33971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_042Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33975" ObjectName="EC-YR_BB.BB_042Ld"/>
    <cge:TPSR_Ref TObjectID="33975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -23.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33974" ObjectName="EC-YR_BB.BB_043Ld"/>
    <cge:TPSR_Ref TObjectID="33974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33972" ObjectName="EC-YR_BB.BB_045Ld"/>
    <cge:TPSR_Ref TObjectID="33972"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YR_BB.BB_047Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33970" ObjectName="EC-YR_BB.BB_047Ld"/>
    <cge:TPSR_Ref TObjectID="33970"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_268f1c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1313.000000 -762.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25340e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1784.000000 -143.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_24ee570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-614 1042,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25196@0" ObjectIDZND0="25195@1" Pin0InfoVect0LinkObjId="SW-140996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140998_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-614 1042,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24c8e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-565 1042,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25195@0" ObjectIDZND0="25230@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-565 1042,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_207a4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-463 1041,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25230@1" ObjectIDZND0="25197@1" Pin0InfoVect0LinkObjId="SW-141001_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c8e40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-463 1041,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24e3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1428,-613 1428,-592 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25200@0" ObjectIDZND0="25199@1" Pin0InfoVect0LinkObjId="SW-141034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1428,-613 1428,-592 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d4b550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1428,-565 1428,-545 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25199@0" ObjectIDZND0="25231@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1428,-565 1428,-545 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2385e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-465 1427,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25231@1" ObjectIDZND0="25201@1" Pin0InfoVect0LinkObjId="SW-141039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1d4b550_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-465 1427,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24e41b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-800 1240,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25193@0" ObjectIDZND0="25191@1" Pin0InfoVect0LinkObjId="SW-140978_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-800 1240,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ea6620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-752 1240,-729 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25191@0" ObjectIDZND0="25192@1" Pin0InfoVect0LinkObjId="SW-140979_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140978_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-752 1240,-729 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26bee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1171,-844 1171,-877 1240,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_24a5980@0" ObjectIDZND0="25193@x" ObjectIDZND1="25194@x" ObjectIDZND2="g_2373460@0" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="SW-140981_0" Pin0InfoVect2LinkObjId="g_2373460_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a5980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1171,-844 1171,-877 1240,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2378200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1208,-898 1240,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2373460@0" ObjectIDZND0="25193@x" ObjectIDZND1="25194@x" ObjectIDZND2="g_24a5980@0" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="SW-140981_0" Pin0InfoVect2LinkObjId="g_24a5980_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2373460_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1208,-898 1240,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2713410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-898 1240,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2373460@0" ObjectIDND1="25193@x" ObjectIDND2="25194@x" ObjectIDZND0="37845@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2373460_0" Pin1InfoVect1LinkObjId="SW-140980_0" Pin1InfoVect2LinkObjId="SW-140981_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-898 1240,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2703f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-213 1240,-189 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25211@0" ObjectIDZND0="25213@1" Pin0InfoVect0LinkObjId="SW-141114_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141112_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-213 1240,-189 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2553ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-264 1240,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25212@0" ObjectIDZND0="25211@1" Pin0InfoVect0LinkObjId="SW-141112_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-264 1240,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26de280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-210 840,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25217@0" ObjectIDZND0="25219@1" Pin0InfoVect0LinkObjId="SW-141144_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-210 840,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d1fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-261 840,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25218@0" ObjectIDZND0="25217@1" Pin0InfoVect0LinkObjId="SW-141142_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141143_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-261 840,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2752590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-297 840,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25218@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_272d0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141143_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="840,-297 840,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2739410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-207 643,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25220@0" ObjectIDZND0="25222@1" Pin0InfoVect0LinkObjId="SW-141159_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141157_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="643,-207 643,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2735c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-258 643,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25221@0" ObjectIDZND0="25220@1" Pin0InfoVect0LinkObjId="SW-141157_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141158_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="643,-258 643,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2728130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="609,-108 609,-126 643,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2769500@0" ObjectIDZND0="25222@x" ObjectIDZND1="33970@x" Pin0InfoVect0LinkObjId="SW-141159_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_047Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2769500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="609,-108 609,-126 643,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_272d0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-294 643,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25221@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141158_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="643,-294 643,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_276a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-274 1825,-253 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25224@0" ObjectIDZND0="25223@1" Pin0InfoVect0LinkObjId="SW-141173_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-274 1825,-253 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2783160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-310 1825,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25224@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141174_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-310 1825,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b66f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-210 1624,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25205@0" ObjectIDZND0="25207@1" Pin0InfoVect0LinkObjId="SW-141084_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141082_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-210 1624,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e25b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-261 1624,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25206@0" ObjectIDZND0="25205@1" Pin0InfoVect0LinkObjId="SW-141082_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141083_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-261 1624,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_209c9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1590,-111 1590,-129 1624,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b30730@0" ObjectIDZND0="25207@x" ObjectIDZND1="33975@x" Pin0InfoVect0LinkObjId="SW-141084_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b30730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1590,-111 1590,-129 1624,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250bec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-150 1624,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25207@0" ObjectIDZND0="g_2b30730@0" ObjectIDZND1="33975@x" Pin0InfoVect0LinkObjId="g_2b30730_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_042Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141084_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-150 1624,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f65e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-47 1624,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="33975@0" ObjectIDZND0="25207@x" ObjectIDZND1="g_2b30730@0" Pin0InfoVect0LinkObjId="SW-141084_0" Pin0InfoVect1LinkObjId="g_2b30730_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_042Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-47 1624,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2512170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1624,-297 1624,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25206@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141083_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1624,-297 1624,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23b69f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-207 1427,-183 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25208@0" ObjectIDZND0="25210@1" Pin0InfoVect0LinkObjId="SW-141099_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141097_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-207 1427,-183 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2378900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-258 1427,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25209@0" ObjectIDZND0="25208@1" Pin0InfoVect0LinkObjId="SW-141097_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141098_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-258 1427,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2361980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1393,-108 1393,-126 1427,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2647e10@0" ObjectIDZND0="25210@x" ObjectIDZND1="33974@x" Pin0InfoVect0LinkObjId="SW-141099_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2647e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1393,-108 1393,-126 1427,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2535450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-294 1427,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25209@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141098_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-294 1427,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b378c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-210 1041,-186 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25214@0" ObjectIDZND0="25216@1" Pin0InfoVect0LinkObjId="SW-141129_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141127_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-210 1041,-186 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-261 1041,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25215@0" ObjectIDZND0="25214@1" Pin0InfoVect0LinkObjId="SW-141127_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141128_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-261 1041,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e28f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1007,-111 1007,-129 1041,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2629ef0@0" ObjectIDZND0="25216@x" ObjectIDZND1="33972@x" Pin0InfoVect0LinkObjId="SW-141129_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2629ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1007,-111 1007,-129 1041,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23ca230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-297 1041,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25215@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141128_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-297 1041,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23a04c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-300 1240,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25212@1" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141113_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-300 1240,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26bfc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-650 1042,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25196@1" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="g_26a6470_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-650 1042,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26a6470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1428,-649 1428,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25200@1" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="g_26bfc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141036_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1428,-649 1428,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d3bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-650 842,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25203@1" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="g_26bfc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-650 842,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27dd860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-652 1620,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_262e230@1" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="g_26bfc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262e230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-652 1620,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27377b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1768,-350 1768,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25228@0" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141198_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1768,-350 1768,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27fd4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-693 1240,-672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25192@0" ObjectIDZND0="25188@0" Pin0InfoVect0LinkObjId="g_26bfc00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140979_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-693 1240,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_254b340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1790,-176 1790,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25226@0" ObjectIDZND0="g_25340e0@0" Pin0InfoVect0LinkObjId="g_25340e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1790,-176 1790,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f5bfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="882,-588 882,-600 842,-600 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="25204@0" ObjectIDZND0="25203@x" ObjectIDZND1="g_278db10@0" ObjectIDZND2="g_2705a90@0" Pin0InfoVect0LinkObjId="SW-141075_0" Pin0InfoVect1LinkObjId="g_278db10_0" Pin0InfoVect2LinkObjId="g_2705a90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="882,-588 882,-600 842,-600 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-600 842,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25204@x" ObjectIDND1="g_278db10@0" ObjectIDND2="g_2705a90@0" ObjectIDZND0="25203@0" Pin0InfoVect0LinkObjId="SW-141075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141077_0" Pin1InfoVect1LinkObjId="g_278db10_0" Pin1InfoVect2LinkObjId="g_2705a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-600 842,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27b92c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-413 1041,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25197@0" ObjectIDZND0="25198@1" Pin0InfoVect0LinkObjId="SW-141003_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141001_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-413 1041,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28057f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-414 1427,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25201@0" ObjectIDZND0="25202@1" Pin0InfoVect0LinkObjId="SW-141041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-414 1427,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2758630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-353 1427,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25202@0" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141041_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-353 1427,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2e0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-353 1041,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25198@0" ObjectIDZND0="25189@0" Pin0InfoVect0LinkObjId="g_2752590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141003_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-353 1041,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27940d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-109 1206,-127 1240,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_26a66b0@0" ObjectIDZND0="25213@x" ObjectIDZND1="33973@x" Pin0InfoVect0LinkObjId="SW-141114_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26a66b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-109 1206,-127 1240,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27d57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="802,-107 802,-125 840,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_262a920@0" ObjectIDZND0="25219@x" ObjectIDZND1="33971@x" Pin0InfoVect0LinkObjId="SW-141144_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_262a920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="802,-107 802,-125 840,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26ca970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-147 643,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25222@0" ObjectIDZND0="g_2769500@0" ObjectIDZND1="33970@x" Pin0InfoVect0LinkObjId="g_2769500_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_047Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141159_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="643,-147 643,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2737ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="643,-47 643,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33970@0" ObjectIDZND0="g_2769500@0" ObjectIDZND1="25222@x" Pin0InfoVect0LinkObjId="g_2769500_0" Pin0InfoVect1LinkObjId="SW-141159_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_047Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="643,-47 643,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23a3380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-150 840,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25219@0" ObjectIDZND0="g_262a920@0" ObjectIDZND1="33971@x" Pin0InfoVect0LinkObjId="g_262a920_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_046Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141144_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="840,-150 840,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2386150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="840,-47 840,-125 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33971@0" ObjectIDZND0="g_262a920@0" ObjectIDZND1="25219@x" Pin0InfoVect0LinkObjId="g_262a920_0" Pin0InfoVect1LinkObjId="SW-141144_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_046Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="840,-47 840,-125 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_266e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-150 1041,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25216@0" ObjectIDZND0="g_2629ef0@0" ObjectIDZND1="33972@x" Pin0InfoVect0LinkObjId="g_2629ef0_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_045Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141129_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-150 1041,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e79090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-47 1041,-129 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33972@0" ObjectIDZND0="g_2629ef0@0" ObjectIDZND1="25216@x" Pin0InfoVect0LinkObjId="g_2629ef0_0" Pin0InfoVect1LinkObjId="SW-141129_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_045Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-47 1041,-129 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e2ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-153 1240,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25213@0" ObjectIDZND0="g_26a66b0@0" ObjectIDZND1="33973@x" Pin0InfoVect0LinkObjId="g_26a66b0_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_044Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141114_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-153 1240,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2716940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-50 1240,-127 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33973@0" ObjectIDZND0="g_26a66b0@0" ObjectIDZND1="25213@x" Pin0InfoVect0LinkObjId="g_26a66b0_0" Pin0InfoVect1LinkObjId="SW-141114_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_044Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-50 1240,-127 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26d2350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-147 1427,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25210@0" ObjectIDZND0="g_2647e10@0" ObjectIDZND1="33974@x" Pin0InfoVect0LinkObjId="g_2647e10_0" Pin0InfoVect1LinkObjId="EC-YR_BB.BB_043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141099_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-147 1427,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_26e5900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1427,-51 1427,-126 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="33974@0" ObjectIDZND0="g_2647e10@0" ObjectIDZND1="25210@x" Pin0InfoVect0LinkObjId="g_2647e10_0" Pin0InfoVect1LinkObjId="SW-141099_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YR_BB.BB_043Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1427,-51 1427,-126 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2752fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1790,-212 1825,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="25226@1" ObjectIDZND0="25223@x" ObjectIDZND1="25225@x" Pin0InfoVect0LinkObjId="SW-141173_0" Pin0InfoVect1LinkObjId="SW-141175_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1790,-212 1825,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27bc020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-226 1825,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="25223@0" ObjectIDZND0="25226@x" ObjectIDZND1="25225@x" Pin0InfoVect0LinkObjId="SW-141183_0" Pin0InfoVect1LinkObjId="SW-141175_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141173_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-226 1825,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27af0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-199 1825,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="25225@1" ObjectIDZND0="25226@x" ObjectIDZND1="25223@x" Pin0InfoVect0LinkObjId="SW-141183_0" Pin0InfoVect1LinkObjId="SW-141173_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141175_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-199 1825,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27cc1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-150 1892,-150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="25225@x" ObjectIDND1="41827@x" ObjectIDZND0="25227@0" Pin0InfoVect0LinkObjId="SW-141184_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141175_0" Pin1InfoVect1LinkObjId="CB-YR_BB.YR_BB_1C_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-150 1892,-150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c9b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-163 1825,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="25225@0" ObjectIDZND0="25227@x" ObjectIDZND1="41827@x" Pin0InfoVect0LinkObjId="SW-141184_0" Pin0InfoVect1LinkObjId="CB-YR_BB.YR_BB_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141175_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-163 1825,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_209c450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-151 1825,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="25225@x" ObjectIDND1="25227@x" ObjectIDZND0="41827@0" Pin0InfoVect0LinkObjId="CB-YR_BB.YR_BB_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141175_0" Pin1InfoVect1LinkObjId="SW-141184_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-151 1825,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b33690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-539 842,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_278db10@1" ObjectIDZND0="g_276ce40@0" Pin0InfoVect0LinkObjId="g_276ce40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_278db10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-539 842,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d473f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-600 842,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="25204@x" ObjectIDND1="25203@x" ObjectIDZND0="g_278db10@0" ObjectIDZND1="g_2705a90@0" Pin0InfoVect0LinkObjId="g_278db10_0" Pin0InfoVect1LinkObjId="g_2705a90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141077_0" Pin1InfoVect1LinkObjId="SW-141075_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="842,-600 842,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d65140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="842,-589 842,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="25204@x" ObjectIDND1="25203@x" ObjectIDND2="g_2705a90@0" ObjectIDZND0="g_278db10@0" Pin0InfoVect0LinkObjId="g_278db10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-141077_0" Pin1InfoVect1LinkObjId="SW-141075_0" Pin1InfoVect2LinkObjId="g_2705a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="842,-589 842,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f02f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="795,-571 795,-589 842,-589 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2705a90@0" ObjectIDZND0="25204@x" ObjectIDZND1="25203@x" ObjectIDZND2="g_278db10@0" Pin0InfoVect0LinkObjId="SW-141077_0" Pin0InfoVect1LinkObjId="SW-141075_0" Pin0InfoVect2LinkObjId="g_278db10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2705a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="795,-571 795,-589 842,-589 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2375b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-839 1319,-860 1240,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25194@1" ObjectIDZND0="25193@x" ObjectIDZND1="g_2373460@0" ObjectIDZND2="37845@1" Pin0InfoVect0LinkObjId="SW-140980_0" Pin0InfoVect1LinkObjId="g_2373460_0" Pin0InfoVect2LinkObjId="g_2713410_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-839 1319,-860 1240,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2377ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-836 1240,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="25193@1" ObjectIDZND0="25194@x" ObjectIDZND1="g_2373460@0" ObjectIDZND2="37845@1" Pin0InfoVect0LinkObjId="SW-140981_0" Pin0InfoVect1LinkObjId="g_2373460_0" Pin0InfoVect2LinkObjId="g_2713410_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-140980_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-836 1240,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26bacb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1319,-780 1319,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_268f1c0@0" ObjectIDZND0="25194@0" Pin0InfoVect0LinkObjId="SW-140981_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_268f1c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1319,-780 1319,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_267f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-860 1240,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDZND0="g_2373460@0" ObjectIDZND1="37845@1" ObjectIDZND2="g_24a5980@0" Pin0InfoVect0LinkObjId="g_2373460_0" Pin0InfoVect1LinkObjId="g_2713410_1" Pin0InfoVect2LinkObjId="g_24a5980_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-860 1240,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_23a3b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1240,-877 1240,-898 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="25193@x" ObjectIDND1="25194@x" ObjectIDND2="g_24a5980@0" ObjectIDZND0="g_2373460@0" ObjectIDZND1="37845@1" Pin0InfoVect0LinkObjId="g_2373460_0" Pin0InfoVect1LinkObjId="g_2713410_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-140980_0" Pin1InfoVect1LinkObjId="SW-140981_0" Pin1InfoVect2LinkObjId="g_24a5980_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1240,-877 1240,-898 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2627ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1620,-587 1620,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_26e4940@0" ObjectIDZND0="g_262e230@0" Pin0InfoVect0LinkObjId="g_262e230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26e4940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1620,-587 1620,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27fe3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-328 712,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25189@0" ObjectIDZND0="g_2766c00@0" Pin0InfoVect0LinkObjId="g_2766c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2752590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-328 712,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_251f460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="712,-393 712,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2766c00@1" ObjectIDZND0="g_2510c60@0" Pin0InfoVect0LinkObjId="g_2510c60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2766c00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="712,-393 712,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e291d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1768,-386 1768,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" EndDevType1="lightningRod" ObjectIDND0="25228@1" ObjectIDZND0="g_23c3dd0@0" ObjectIDZND1="g_2752c90@0" Pin0InfoVect0LinkObjId="g_23c3dd0_0" Pin0InfoVect1LinkObjId="g_2752c90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-141198_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1768,-386 1768,-398 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23d3f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1768,-398 1768,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="25228@x" ObjectIDND1="g_2752c90@0" ObjectIDZND0="g_23c3dd0@0" Pin0InfoVect0LinkObjId="g_23c3dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-141198_0" Pin1InfoVect1LinkObjId="g_2752c90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1768,-398 1768,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_27259d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1810,-412 1810,-398 1768,-398 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="voltageTransformer" ObjectIDND0="g_2752c90@0" ObjectIDZND0="25228@x" ObjectIDZND1="g_23c3dd0@0" Pin0InfoVect0LinkObjId="SW-141198_0" Pin0InfoVect1LinkObjId="g_23c3dd0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2752c90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1810,-412 1810,-398 1768,-398 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="840" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="643" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1825" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1624" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1427" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1041" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1240" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="1042" cy="-672" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="1428" cy="-672" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="842" cy="-672" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="1240" cy="-672" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1427" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1041" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25188" cx="1620" cy="-672" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="1768" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25189" cx="712" cy="-328" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130470" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.500000 -921.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23910" ObjectName="DYN-YR_BB"/>
     <cge:Meas_Ref ObjectId="130470"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1e71df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -428.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1d8fed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 26.000000 -866.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_252a7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 169.000000 -1007.500000) translate(0,16)">班别变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.500000 -1004.000000) translate(0,15)">班</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.500000 -1004.000000) translate(0,33)">别</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.500000 -1004.000000) translate(0,51)">T</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.500000 -1004.000000) translate(0,69)">接</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2503500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1256.500000 -1004.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2392340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1635.500000 -698.000000) translate(0,15)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2382eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.500000 -462.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23754a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 643.500000 -531.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_235e6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1878.500000 -354.000000) translate(0,15)">10kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25475d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1731.500000 -490.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27044d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 615.500000 -17.000000) translate(0,15)">莲池线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_274a3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 804.500000 -17.000000) translate(0,15)">羊提江线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_272bf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1006.500000 -17.000000) translate(0,15)">羊旧乍线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27fb080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1214.500000 -17.000000) translate(0,15)">勐莲线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27f3040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1403.500000 -17.000000) translate(0,15)">园区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_276e5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.500000 -17.000000) translate(0,15)">云龙线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_23cf610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1556.500000 -485.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274b0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1051.000000 -586.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2537590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 891.000000 -572.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c9130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -434.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -378.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2531c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1088.000000 -544.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c4470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1437.000000 -586.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c3740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1435.000000 -638.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2392010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1436.000000 -435.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2382b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -378.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_267ff30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1338.000000 -530.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dacd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 655.000000 -228.000000) translate(0,12)">047</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5c250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -283.000000) translate(0,12)">0471</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 650.000000 -172.000000) translate(0,12)">0476</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_270eed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 -231.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23a5bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -286.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23961f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 847.000000 -175.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26cf090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -232.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27cdfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -286.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27efe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -175.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279e510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -234.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_265f850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -289.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26a8230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -178.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ba5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1439.000000 -230.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2631300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -283.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2531f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1434.000000 -172.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23c4760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1636.000000 -231.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236d0d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1631.000000 -286.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1d657c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1631.000000 -175.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e879c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1838.000000 -247.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26a8a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1832.000000 -299.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2678bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1742.000000 -203.000000) translate(0,12)">04160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e7f330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1832.000000 -188.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ac270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1775.000000 -375.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_276f8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -773.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2793df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1331.000000 -825.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b2b600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -718.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_279b6d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1247.000000 -825.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27df6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1048.000000 -636.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 849.000000 -639.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_266e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44.500000 -345.000000) translate(0,15)">35kV341重合闸软压板位置信号不正确</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(213,0,0)" font-family="SimSun" font-size="18" graphid="g_266e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 44.500000 -345.000000) translate(0,33)">，经现场自动化人员核实该装置不具备重合闸软压板功能。</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_26bb620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -1015.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_26f6c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 306.000000 -982.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2654360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 52.000000 -634.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.500000 -522.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.500000 -522.000000) translate(0,27)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.500000 -522.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d0410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1079.500000 -522.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a0550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.500000 -506.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a0550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.500000 -506.000000) translate(0,27)">SZ11-4000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a0550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.500000 -506.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a0550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.500000 -506.000000) translate(0,57)">Yd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_23d24a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2.000000 -34.000000) translate(0,17)">永仁巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_27b5590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 149.000000 -44.500000) translate(0,17)">13638777384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_27b5590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 149.000000 -44.500000) translate(0,38)">13987885824</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_24caee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 147.000000 -74.000000) translate(0,17)">6829073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ea1a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1782.000000 -5.000000) translate(0,15)">1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_272c390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1896.000000 -178.000000) translate(0,12)">04167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2724730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 444.500000 -1003.000000) translate(0,16)">AVC</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="20" stroke="rgb(0,238,0)" stroke-width="1" width="10" x="1763" y="-426"/>
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="428" y="-1014"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YR_BB.YR_BB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="737,-672 1716,-672 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25188" ObjectName="BS-YR_BB.YR_BB_3IM"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   <polyline fill="none" opacity="0" points="737,-672 1716,-672 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YR_BB.YR_BB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="525,-328 1957,-328 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25189" ObjectName="BS-YR_BB.YR_BB_9IM"/>
    <cge:TPSR_Ref TObjectID="25189"/></metadata>
   <polyline fill="none" opacity="0" points="525,-328 1957,-328 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YR_BB.YR_BB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35551"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -458.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.000000 -458.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25230" ObjectName="TF-YR_BB.YR_BB_1T"/>
    <cge:TPSR_Ref TObjectID="25230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YR_BB.YR_BB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="35555"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -460.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1390.000000 -460.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25231" ObjectName="TF-YR_BB.YR_BB_2T"/>
    <cge:TPSR_Ref TObjectID="25231"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 117.000000 -951.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-217882" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 139.000000 -787.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="217882" ObjectName="YR_BB:YR_BB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239631" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -865.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239631" ObjectName="YR_BB:YR_BB_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-239631" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="18" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 137.000000 -825.000000) translate(0,15)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="239631" ObjectName="YR_BB:YR_BB_sumP1"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="129" y="-1018"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="129" y="-1018"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="80" y="-1035"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="80" y="-1035"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1255" y="-773"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1255" y="-773"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1088" y="-545"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1088" y="-545"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="1337" y="-531"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="1337" y="-531"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="655" y="-228"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="655" y="-228"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="853" y="-231"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="853" y="-231"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1055" y="-232"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1055" y="-232"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1254" y="-234"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1254" y="-234"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1439" y="-230"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1439" y="-230"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1636" y="-231"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1636" y="-231"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1838" y="-247"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1838" y="-247"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="294" y="-1023"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="294" y="-1023"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="294" y="-990"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="294" y="-990"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="25" qtmmishow="hidden" width="74" x="50" y="-638"/>
    </a>
   <metadata/><rect fill="white" height="25" opacity="0" stroke="white" transform="" width="74" x="50" y="-638"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="428" y="-1015"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="428" y="-1015"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26767c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.000000 756.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26861c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1299.000000 741.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2365490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1324.000000 726.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_236bbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 602.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2749b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1084.000000 587.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2735310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 572.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2806cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1095.000000 451.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2648000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1084.000000 436.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_253dbc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1109.000000 421.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b384e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 604.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_254d8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 589.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251c730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 574.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26868d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.000000 449.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e1fe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1469.000000 434.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26ce150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1494.000000 419.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f62c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 574.000000 -23.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d2e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.000000 -38.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239e290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 588.000000 -53.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2729580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.000000 710.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_239d570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 537.000000 694.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_274b250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 725.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2729770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 740.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2812e10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 754.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_266d1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 502.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2766220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.000000 517.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f0db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 504.000000) translate(0,12)">油温(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27f3c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1463.000000 519.000000) translate(0,12)">档位(档):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ff020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 389.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 404.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 529.000000 418.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27efb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 521.000000 374.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2748af0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 771.000000 -22.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c5e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -37.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24caa50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 785.000000 -52.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e90d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -21.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_262a5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 960.000000 -36.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f5b850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 985.000000 -51.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23b5f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1171.000000 -19.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2720a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.000000 -34.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_271b790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1185.000000 -49.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f4910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1361.000000 -25.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26f5c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1350.000000 -40.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d2a00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 -55.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26d1ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1553.000000 -22.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26c8230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1542.000000 -37.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2742500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1567.000000 -52.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YR_BB.YR_BB_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1800.000000 -31.000000)" xlink:href="#capacitor:shape41"/>
    <metadata>
     <cge:PSR_Ref ObjectId="41827" ObjectName="CB-YR_BB.YR_BB_1C"/>
    <cge:TPSR_Ref TObjectID="41827"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -755.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25191"/>
     <cge:Term_Ref ObjectID="35463"/>
    <cge:TPSR_Ref TObjectID="25191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -755.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25191"/>
     <cge:Term_Ref ObjectID="35463"/>
    <cge:TPSR_Ref TObjectID="25191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1374.000000 -755.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25191"/>
     <cge:Term_Ref ObjectID="35463"/>
    <cge:TPSR_Ref TObjectID="25191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-140912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -416.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25189"/>
     <cge:Term_Ref ObjectID="35460"/>
    <cge:TPSR_Ref TObjectID="25189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-140913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -416.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25189"/>
     <cge:Term_Ref ObjectID="35460"/>
    <cge:TPSR_Ref TObjectID="25189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-140914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -416.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25189"/>
     <cge:Term_Ref ObjectID="35460"/>
    <cge:TPSR_Ref TObjectID="25189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-140915" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -416.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140915" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25189"/>
     <cge:Term_Ref ObjectID="35460"/>
    <cge:TPSR_Ref TObjectID="25189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-189345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -601.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189345" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25195"/>
     <cge:Term_Ref ObjectID="35471"/>
    <cge:TPSR_Ref TObjectID="25195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-189346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -601.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189346" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25195"/>
     <cge:Term_Ref ObjectID="35471"/>
    <cge:TPSR_Ref TObjectID="25195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-189342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -601.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189342" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25195"/>
     <cge:Term_Ref ObjectID="35471"/>
    <cge:TPSR_Ref TObjectID="25195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140895" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -606.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140895" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25199"/>
     <cge:Term_Ref ObjectID="35487"/>
    <cge:TPSR_Ref TObjectID="25199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -606.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25199"/>
     <cge:Term_Ref ObjectID="35487"/>
    <cge:TPSR_Ref TObjectID="25199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1541.000000 -606.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25199"/>
     <cge:Term_Ref ObjectID="35487"/>
    <cge:TPSR_Ref TObjectID="25199"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.000000 -448.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25197"/>
     <cge:Term_Ref ObjectID="35483"/>
    <cge:TPSR_Ref TObjectID="25197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.000000 -448.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25197"/>
     <cge:Term_Ref ObjectID="35483"/>
    <cge:TPSR_Ref TObjectID="25197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-189348" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1154.000000 -448.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189348" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25197"/>
     <cge:Term_Ref ObjectID="35483"/>
    <cge:TPSR_Ref TObjectID="25197"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -447.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25201"/>
     <cge:Term_Ref ObjectID="35491"/>
    <cge:TPSR_Ref TObjectID="25201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -447.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25201"/>
     <cge:Term_Ref ObjectID="35491"/>
    <cge:TPSR_Ref TObjectID="25201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1543.000000 -447.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25201"/>
     <cge:Term_Ref ObjectID="35491"/>
    <cge:TPSR_Ref TObjectID="25201"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25220"/>
     <cge:Term_Ref ObjectID="35529"/>
    <cge:TPSR_Ref TObjectID="25220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25220"/>
     <cge:Term_Ref ObjectID="35529"/>
    <cge:TPSR_Ref TObjectID="25220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140942" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.000000 26.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140942" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25220"/>
     <cge:Term_Ref ObjectID="35529"/>
    <cge:TPSR_Ref TObjectID="25220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140939" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140939" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25217"/>
     <cge:Term_Ref ObjectID="35523"/>
    <cge:TPSR_Ref TObjectID="25217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25217"/>
     <cge:Term_Ref ObjectID="35523"/>
    <cge:TPSR_Ref TObjectID="25217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140937" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 829.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140937" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25217"/>
     <cge:Term_Ref ObjectID="35523"/>
    <cge:TPSR_Ref TObjectID="25217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25214"/>
     <cge:Term_Ref ObjectID="35517"/>
    <cge:TPSR_Ref TObjectID="25214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25214"/>
     <cge:Term_Ref ObjectID="35517"/>
    <cge:TPSR_Ref TObjectID="25214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1030.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25214"/>
     <cge:Term_Ref ObjectID="35517"/>
    <cge:TPSR_Ref TObjectID="25214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 20.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25211"/>
     <cge:Term_Ref ObjectID="35511"/>
    <cge:TPSR_Ref TObjectID="25211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 20.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25211"/>
     <cge:Term_Ref ObjectID="35511"/>
    <cge:TPSR_Ref TObjectID="25211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1229.000000 20.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25211"/>
     <cge:Term_Ref ObjectID="35511"/>
    <cge:TPSR_Ref TObjectID="25211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 26.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25208"/>
     <cge:Term_Ref ObjectID="35505"/>
    <cge:TPSR_Ref TObjectID="25208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140925" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 26.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140925" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25208"/>
     <cge:Term_Ref ObjectID="35505"/>
    <cge:TPSR_Ref TObjectID="25208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1416.000000 26.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25208"/>
     <cge:Term_Ref ObjectID="35505"/>
    <cge:TPSR_Ref TObjectID="25208"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-140919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 23.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25205"/>
     <cge:Term_Ref ObjectID="35499"/>
    <cge:TPSR_Ref TObjectID="25205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-140920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 23.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25205"/>
     <cge:Term_Ref ObjectID="35499"/>
    <cge:TPSR_Ref TObjectID="25205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-140917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1613.000000 23.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25205"/>
     <cge:Term_Ref ObjectID="35499"/>
    <cge:TPSR_Ref TObjectID="25205"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-140890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -518.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25230"/>
     <cge:Term_Ref ObjectID="35549"/>
    <cge:TPSR_Ref TObjectID="25230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-140891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -518.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25230"/>
     <cge:Term_Ref ObjectID="35549"/>
    <cge:TPSR_Ref TObjectID="25230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-140904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25231"/>
     <cge:Term_Ref ObjectID="35553"/>
    <cge:TPSR_Ref TObjectID="25231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-140905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1530.000000 -519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25231"/>
     <cge:Term_Ref ObjectID="35553"/>
    <cge:TPSR_Ref TObjectID="25231"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-140907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -756.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25188"/>
     <cge:Term_Ref ObjectID="35459"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-140908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -756.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25188"/>
     <cge:Term_Ref ObjectID="35459"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-140909" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -756.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140909" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25188"/>
     <cge:Term_Ref ObjectID="35459"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-140910" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -756.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="140910" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25188"/>
     <cge:Term_Ref ObjectID="35459"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-189341" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.000000 -756.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="189341" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25188"/>
     <cge:Term_Ref ObjectID="35459"/>
    <cge:TPSR_Ref TObjectID="25188"/></metadata>
   </g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-140998">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1033.000000 -609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25196" ObjectName="SW-YR_BB.YR_BB_3011SW"/>
     <cge:Meas_Ref ObjectId="140998"/>
    <cge:TPSR_Ref TObjectID="25196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1419.000000 -608.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25200" ObjectName="SW-YR_BB.YR_BB_3021SW"/>
     <cge:Meas_Ref ObjectId="141036"/>
    <cge:TPSR_Ref TObjectID="25200"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140980">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25193" ObjectName="SW-YR_BB.YR_BB_3416SW"/>
     <cge:Meas_Ref ObjectId="140980"/>
    <cge:TPSR_Ref TObjectID="25193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140979">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -688.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25192" ObjectName="SW-YR_BB.YR_BB_3411SW"/>
     <cge:Meas_Ref ObjectId="140979"/>
    <cge:TPSR_Ref TObjectID="25192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -259.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25212" ObjectName="SW-YR_BB.YR_BB_0441SW"/>
     <cge:Meas_Ref ObjectId="141113"/>
    <cge:TPSR_Ref TObjectID="25212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141114">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1231.000000 -148.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25213" ObjectName="SW-YR_BB.YR_BB_0446SW"/>
     <cge:Meas_Ref ObjectId="141114"/>
    <cge:TPSR_Ref TObjectID="25213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141143">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25218" ObjectName="SW-YR_BB.YR_BB_0461SW"/>
     <cge:Meas_Ref ObjectId="141143"/>
    <cge:TPSR_Ref TObjectID="25218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 831.000000 -145.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25219" ObjectName="SW-YR_BB.YR_BB_0466SW"/>
     <cge:Meas_Ref ObjectId="141144"/>
    <cge:TPSR_Ref TObjectID="25219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141158">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25221" ObjectName="SW-YR_BB.YR_BB_0471SW"/>
     <cge:Meas_Ref ObjectId="141158"/>
    <cge:TPSR_Ref TObjectID="25221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141159">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 634.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25222" ObjectName="SW-YR_BB.YR_BB_0476SW"/>
     <cge:Meas_Ref ObjectId="141159"/>
    <cge:TPSR_Ref TObjectID="25222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141174">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1816.000000 -269.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25224" ObjectName="SW-YR_BB.YR_BB_0411SW"/>
     <cge:Meas_Ref ObjectId="141174"/>
    <cge:TPSR_Ref TObjectID="25224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141175">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1816.000000 -158.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25225" ObjectName="SW-YR_BB.YR_BB_0416SW"/>
     <cge:Meas_Ref ObjectId="141175"/>
    <cge:TPSR_Ref TObjectID="25225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25206" ObjectName="SW-YR_BB.YR_BB_0421SW"/>
     <cge:Meas_Ref ObjectId="141083"/>
    <cge:TPSR_Ref TObjectID="25206"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141084">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -145.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25207" ObjectName="SW-YR_BB.YR_BB_0426SW"/>
     <cge:Meas_Ref ObjectId="141084"/>
    <cge:TPSR_Ref TObjectID="25207"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141098">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -253.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25209" ObjectName="SW-YR_BB.YR_BB_0431SW"/>
     <cge:Meas_Ref ObjectId="141098"/>
    <cge:TPSR_Ref TObjectID="25209"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141099">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -142.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25210" ObjectName="SW-YR_BB.YR_BB_0436SW"/>
     <cge:Meas_Ref ObjectId="141099"/>
    <cge:TPSR_Ref TObjectID="25210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141128">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25215" ObjectName="SW-YR_BB.YR_BB_0451SW"/>
     <cge:Meas_Ref ObjectId="141128"/>
    <cge:TPSR_Ref TObjectID="25215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141129">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -145.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25216" ObjectName="SW-YR_BB.YR_BB_0456SW"/>
     <cge:Meas_Ref ObjectId="141129"/>
    <cge:TPSR_Ref TObjectID="25216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -609.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25203" ObjectName="SW-YR_BB.YR_BB_3901SW"/>
     <cge:Meas_Ref ObjectId="141075"/>
    <cge:TPSR_Ref TObjectID="25203"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -537.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25204" ObjectName="SW-YR_BB.YR_BB_39017SW"/>
     <cge:Meas_Ref ObjectId="141077"/>
    <cge:TPSR_Ref TObjectID="25204"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141198">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1759.000000 -345.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25228" ObjectName="SW-YR_BB.YR_BB_0901SW"/>
     <cge:Meas_Ref ObjectId="141198"/>
    <cge:TPSR_Ref TObjectID="25228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-140981">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1310.000000 -798.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25194" ObjectName="SW-YR_BB.YR_BB_34167SW"/>
     <cge:Meas_Ref ObjectId="140981"/>
    <cge:TPSR_Ref TObjectID="25194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141183">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.000000 -171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25226" ObjectName="SW-YR_BB.YR_BB_04160SW"/>
     <cge:Meas_Ref ObjectId="141183"/>
    <cge:TPSR_Ref TObjectID="25226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141003">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1032.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25198" ObjectName="SW-YR_BB.YR_BB_0011SW"/>
     <cge:Meas_Ref ObjectId="141003"/>
    <cge:TPSR_Ref TObjectID="25198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141041">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1418.000000 -348.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25202" ObjectName="SW-YR_BB.YR_BB_0021SW"/>
     <cge:Meas_Ref ObjectId="141041"/>
    <cge:TPSR_Ref TObjectID="25202"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-141184">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1887.000000 -114.000000)" xlink:href="#switch2:shape49_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25227" ObjectName="SW-YR_BB.YR_BB_04167SW"/>
     <cge:Meas_Ref ObjectId="141184"/>
    <cge:TPSR_Ref TObjectID="25227"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="129" y="-1018"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="80" y="-1035"/></g>
   <g href="35kV班别变35kV班别T接线341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1255" y="-773"/></g>
   <g href="35kV班别变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1088" y="-545"/></g>
   <g href="35kV班别变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="1337" y="-531"/></g>
   <g href="35kV班别变10kV莲池线047间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="655" y="-228"/></g>
   <g href="35kV班别变10kV羊提江线046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="853" y="-231"/></g>
   <g href="35kV班别变10kV羊臼乍线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1055" y="-232"/></g>
   <g href="35kV班别变10kV勐莲线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1254" y="-234"/></g>
   <g href="35kV班别变10kV园区线043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1439" y="-230"/></g>
   <g href="35kV班别变10kV云龙线042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1636" y="-231"/></g>
   <g href="35kV班别变10kV电容器041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1838" y="-247"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="294" y="-1023"/></g>
   <g href="cx_配调_配网接线图35_永仁.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="294" y="-990"/></g>
   <g href="35kV班别变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="25" qtmmishow="hidden" width="74" x="50" y="-638"/></g>
   <g href="AVC班别站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="428" y="-1015"/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2373460">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1151.000000 -891.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26a66b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 -56.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2769500">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.000000 -54.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b30730">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1583.000000 -57.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2647e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1386.000000 -54.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2629ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1000.000000 -60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262a920">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 795.000000 -52.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_278db10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 833.000000 -534.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2705a90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 788.000000 -576.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a5980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.000000 -772.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262e230">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.000000 -602.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26e4940">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.000000 -492.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2766c00">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 707.000000 -343.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2510c60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 699.000000 -508.000000)" xlink:href="#lightningRod:shape189"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2752c90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 1803.000000 -466.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YR_BB"/>
</svg>