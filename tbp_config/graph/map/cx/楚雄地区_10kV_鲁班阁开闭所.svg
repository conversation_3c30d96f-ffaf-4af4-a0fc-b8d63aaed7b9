<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-57" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3059 -1230 2151 1232">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape25">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0182374" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="8" x2="13" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0326126" x1="17" x2="13" y1="29" y2="32"/>
    <ellipse cx="12" cy="32" rx="12.5" ry="12" stroke-width="0.120929"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="9" x2="9" y1="17" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.0433242" x1="16" x2="9" y1="12" y2="7"/>
    <circle cx="12" cy="12" r="12.5" stroke-width="0.120929"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfc730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dfd6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfe0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dfec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dffe10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e00930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e014f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e01fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e029c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e03350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e03350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e05190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e05190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1e05f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e07b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e087d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e09050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e09aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0b150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0b980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0bf10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e0c910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0daf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0e470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e0ef60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e04540" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e11bb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e12550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1e21da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1e13bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e173d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1e111c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1242" width="2161" x="3054" y="-1235"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="1201" stroke="rgb(21,40,56)" stroke-width="1" width="2150" x="3059" y="-1230"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3117" y="-598"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47670">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3717.000000 -638.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8647" ObjectName="SW-CX_LBG.CX_LBG_0901SW"/>
     <cge:Meas_Ref ObjectId="47670"/>
    <cge:TPSR_Ref TObjectID="8647"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47672">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8649" ObjectName="SW-CX_LBG.CX_LBG_09017SW"/>
     <cge:Meas_Ref ObjectId="47672"/>
    <cge:TPSR_Ref TObjectID="8649"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47695">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3864.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8672" ObjectName="SW-CX_LBG.CX_LBG_06417SW"/>
     <cge:Meas_Ref ObjectId="47695"/>
    <cge:TPSR_Ref TObjectID="8672"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47694">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3895.000000 -638.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8671" ObjectName="SW-CX_LBG.CX_LBG_0641SW"/>
     <cge:Meas_Ref ObjectId="47694"/>
    <cge:TPSR_Ref TObjectID="8671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8623" ObjectName="SW-CX_LBG.CX_LBG_0661SW"/>
     <cge:Meas_Ref ObjectId="47642"/>
    <cge:TPSR_Ref TObjectID="8623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3634.000000 -461.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8624" ObjectName="SW-CX_LBG.CX_LBG_0666SW"/>
     <cge:Meas_Ref ObjectId="47643"/>
    <cge:TPSR_Ref TObjectID="8624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-94369">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3679.000000 -438.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="19875" ObjectName="SW-CX_LBG.CX_LBG_06667SW"/>
     <cge:Meas_Ref ObjectId="94369"/>
    <cge:TPSR_Ref TObjectID="19875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8627" ObjectName="SW-CX_LBG.CX_LBG_0651SW"/>
     <cge:Meas_Ref ObjectId="47646"/>
    <cge:TPSR_Ref TObjectID="8627"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47647">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -458.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8628" ObjectName="SW-CX_LBG.CX_LBG_0656SW"/>
     <cge:Meas_Ref ObjectId="47647"/>
    <cge:TPSR_Ref TObjectID="8628"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47648">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3817.000000 -435.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8629" ObjectName="SW-CX_LBG.CX_LBG_06567SW"/>
     <cge:Meas_Ref ObjectId="47648"/>
    <cge:TPSR_Ref TObjectID="8629"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47651">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8632" ObjectName="SW-CX_LBG.CX_LBG_0636SW"/>
     <cge:Meas_Ref ObjectId="47651"/>
    <cge:TPSR_Ref TObjectID="8632"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47652">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8633" ObjectName="SW-CX_LBG.CX_LBG_06367SW"/>
     <cge:Meas_Ref ObjectId="47652"/>
    <cge:TPSR_Ref TObjectID="8633"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47650">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3916.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8631" ObjectName="SW-CX_LBG.CX_LBG_0631SW"/>
     <cge:Meas_Ref ObjectId="47650"/>
    <cge:TPSR_Ref TObjectID="8631"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47655">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -458.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8636" ObjectName="SW-CX_LBG.CX_LBG_0626SW"/>
     <cge:Meas_Ref ObjectId="47655"/>
    <cge:TPSR_Ref TObjectID="8636"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4108.000000 -435.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8637" ObjectName="SW-CX_LBG.CX_LBG_06267SW"/>
     <cge:Meas_Ref ObjectId="47656"/>
    <cge:TPSR_Ref TObjectID="8637"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47654">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4063.000000 -553.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8635" ObjectName="SW-CX_LBG.CX_LBG_0621SW"/>
     <cge:Meas_Ref ObjectId="47654"/>
    <cge:TPSR_Ref TObjectID="8635"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -734.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8640" ObjectName="SW-CX_LBG.CX_LBG_0616SW"/>
     <cge:Meas_Ref ObjectId="47659"/>
    <cge:TPSR_Ref TObjectID="8640"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -639.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8639" ObjectName="SW-CX_LBG.CX_LBG_0611SW"/>
     <cge:Meas_Ref ObjectId="47658"/>
    <cge:TPSR_Ref TObjectID="8639"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4143.000000 -793.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8641" ObjectName="SW-CX_LBG.CX_LBG_06167SW"/>
     <cge:Meas_Ref ObjectId="47660"/>
    <cge:TPSR_Ref TObjectID="8641"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -560.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8643" ObjectName="SW-CX_LBG.CX_LBG_0121SW"/>
     <cge:Meas_Ref ObjectId="47662"/>
    <cge:TPSR_Ref TObjectID="8643"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47663">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -559.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8644" ObjectName="SW-CX_LBG.CX_LBG_0122SW"/>
     <cge:Meas_Ref ObjectId="47663"/>
    <cge:TPSR_Ref TObjectID="8644"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47664">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4180.000000 -493.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8645" ObjectName="SW-CX_LBG.CX_LBG_01217SW"/>
     <cge:Meas_Ref ObjectId="47664"/>
    <cge:TPSR_Ref TObjectID="8645"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47669">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -492.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8646" ObjectName="SW-CX_LBG.CX_LBG_01227SW"/>
     <cge:Meas_Ref ObjectId="47669"/>
    <cge:TPSR_Ref TObjectID="8646"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47676">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -460.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8653" ObjectName="SW-CX_LBG.CX_LBG_0726SW"/>
     <cge:Meas_Ref ObjectId="47676"/>
    <cge:TPSR_Ref TObjectID="8653"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47677">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4451.000000 -437.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8654" ObjectName="SW-CX_LBG.CX_LBG_07267SW"/>
     <cge:Meas_Ref ObjectId="47677"/>
    <cge:TPSR_Ref TObjectID="8654"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47675">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -555.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8652" ObjectName="SW-CX_LBG.CX_LBG_0722SW"/>
     <cge:Meas_Ref ObjectId="47675"/>
    <cge:TPSR_Ref TObjectID="8652"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8657" ObjectName="SW-CX_LBG.CX_LBG_0736SW"/>
     <cge:Meas_Ref ObjectId="47680"/>
    <cge:TPSR_Ref TObjectID="8657"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -433.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8658" ObjectName="SW-CX_LBG.CX_LBG_07367SW"/>
     <cge:Meas_Ref ObjectId="47681"/>
    <cge:TPSR_Ref TObjectID="8658"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -551.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8656" ObjectName="SW-CX_LBG.CX_LBG_0732SW"/>
     <cge:Meas_Ref ObjectId="47679"/>
    <cge:TPSR_Ref TObjectID="8656"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -457.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8661" ObjectName="SW-CX_LBG.CX_LBG_0746SW"/>
     <cge:Meas_Ref ObjectId="47684"/>
    <cge:TPSR_Ref TObjectID="8661"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47685">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4746.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8662" ObjectName="SW-CX_LBG.CX_LBG_07467SW"/>
     <cge:Meas_Ref ObjectId="47685"/>
    <cge:TPSR_Ref TObjectID="8662"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47683">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4701.000000 -552.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8660" ObjectName="SW-CX_LBG.CX_LBG_0742SW"/>
     <cge:Meas_Ref ObjectId="47683"/>
    <cge:TPSR_Ref TObjectID="8660"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47692">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 -459.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8669" ObjectName="SW-CX_LBG.CX_LBG_0766SW"/>
     <cge:Meas_Ref ObjectId="47692"/>
    <cge:TPSR_Ref TObjectID="8669"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47693">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -436.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8670" ObjectName="SW-CX_LBG.CX_LBG_07667SW"/>
     <cge:Meas_Ref ObjectId="47693"/>
    <cge:TPSR_Ref TObjectID="8670"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47691">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4844.000000 -554.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8668" ObjectName="SW-CX_LBG.CX_LBG_0762SW"/>
     <cge:Meas_Ref ObjectId="47691"/>
    <cge:TPSR_Ref TObjectID="8668"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47697">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4356.000000 -690.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8674" ObjectName="SW-CX_LBG.CX_LBG_07127SW"/>
     <cge:Meas_Ref ObjectId="47697"/>
    <cge:TPSR_Ref TObjectID="8674"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47696">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4386.000000 -639.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8673" ObjectName="SW-CX_LBG.CX_LBG_0712SW"/>
     <cge:Meas_Ref ObjectId="47696"/>
    <cge:TPSR_Ref TObjectID="8673"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47671">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 -638.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8648" ObjectName="SW-CX_LBG.CX_LBG_0902SW"/>
     <cge:Meas_Ref ObjectId="47671"/>
    <cge:TPSR_Ref TObjectID="8648"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47673">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4576.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8650" ObjectName="SW-CX_LBG.CX_LBG_09027SW"/>
     <cge:Meas_Ref ObjectId="47673"/>
    <cge:TPSR_Ref TObjectID="8650"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47688">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -734.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8665" ObjectName="SW-CX_LBG.CX_LBG_0756SW"/>
     <cge:Meas_Ref ObjectId="47688"/>
    <cge:TPSR_Ref TObjectID="8665"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47687">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4779.000000 -639.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8664" ObjectName="SW-CX_LBG.CX_LBG_0752SW"/>
     <cge:Meas_Ref ObjectId="47687"/>
    <cge:TPSR_Ref TObjectID="8664"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47689">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4825.000000 -793.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8666" ObjectName="SW-CX_LBG.CX_LBG_07567SW"/>
     <cge:Meas_Ref ObjectId="47689"/>
    <cge:TPSR_Ref TObjectID="8666"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_130fd60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 -707.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1227760" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -707.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_22d9560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3722.000000 -454.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2296b90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -451.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_223eee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4004.000000 -453.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1245180" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4151.000000 -451.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11ae4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4186.000000 -809.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122a6d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 -473.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c0270" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -470.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_119c610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -453.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11e4340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.000000 -449.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_123bb40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4789.000000 -450.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12fa560" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4932.000000 -452.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12cd970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4305.000000 -708.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f35e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -707.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12d17b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 -809.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_12723b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-635 3732,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8647@0" Pin0InfoVect0LinkObjId="SW-47670_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-635 3732,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1216630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-696 3732,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8647@1" ObjectIDZND0="8649@x" ObjectIDZND1="g_176d2b0@0" ObjectIDZND2="g_127aed0@0" Pin0InfoVect0LinkObjId="SW-47672_0" Pin0InfoVect1LinkObjId="g_176d2b0_0" Pin0InfoVect2LinkObjId="g_127aed0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47670_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-696 3732,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13797e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-715 3714,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8647@x" ObjectIDND1="g_176d2b0@0" ObjectIDND2="g_127aed0@0" ObjectIDZND0="8649@1" Pin0InfoVect0LinkObjId="SW-47672_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47670_0" Pin1InfoVect1LinkObjId="g_176d2b0_0" Pin1InfoVect2LinkObjId="g_127aed0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-715 3714,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13795b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3678,-715 3661,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8649@0" ObjectIDZND0="g_130fd60@0" Pin0InfoVect0LinkObjId="g_130fd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47672_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3678,-715 3661,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137a4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3760,-775 3760,-756 3732,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_127aed0@0" ObjectIDZND0="8647@x" ObjectIDZND1="8649@x" ObjectIDZND2="g_176d2b0@0" Pin0InfoVect0LinkObjId="SW-47670_0" Pin0InfoVect1LinkObjId="SW-47672_0" Pin0InfoVect2LinkObjId="g_176d2b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127aed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3760,-775 3760,-756 3732,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1379be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-756 3732,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_176d2b0@0" ObjectIDND1="g_127aed0@0" ObjectIDZND0="8647@x" ObjectIDZND1="8649@x" Pin0InfoVect0LinkObjId="SW-47670_0" Pin0InfoVect1LinkObjId="SW-47672_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_176d2b0_0" Pin1InfoVect1LinkObjId="g_127aed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-756 3732,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1314100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3732,-756 3689,-756 3689,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8647@x" ObjectIDND1="8649@x" ObjectIDND2="g_127aed0@0" ObjectIDZND0="g_176d2b0@0" Pin0InfoVect0LinkObjId="g_176d2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47670_0" Pin1InfoVect1LinkObjId="SW-47672_0" Pin1InfoVect2LinkObjId="g_127aed0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3732,-756 3689,-756 3689,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1313a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-797 3689,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_176d2b0@1" ObjectIDZND0="g_1217370@0" Pin0InfoVect0LinkObjId="g_1217370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_176d2b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3689,-797 3689,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13128c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3909,-715 3891,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="8671@x" ObjectIDND1="8672@x" ObjectIDND2="8671@x" ObjectIDZND0="8672@1" Pin0InfoVect0LinkObjId="SW-47695_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47694_0" Pin1InfoVect1LinkObjId="SW-47695_0" Pin1InfoVect2LinkObjId="SW-47694_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3909,-715 3891,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1312680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-715 3838,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8672@0" ObjectIDZND0="g_1227760@0" Pin0InfoVect0LinkObjId="g_1227760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47695_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-715 3838,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12179c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-635 3910,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8671@0" Pin0InfoVect0LinkObjId="SW-47694_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-635 3910,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1217780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-696 3910,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8671@1" ObjectIDZND0="8672@x" ObjectIDZND1="8672@x" ObjectIDZND2="8671@x" Pin0InfoVect0LinkObjId="SW-47695_0" Pin0InfoVect1LinkObjId="SW-47695_0" Pin0InfoVect2LinkObjId="SW-47694_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47694_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-696 3910,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1217540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-715 3910,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8672@x" ObjectIDND1="8671@x" ObjectIDND2="8672@x" ObjectIDZND0="8672@x" ObjectIDZND1="8671@x" ObjectIDZND2="g_12bd3a0@0" Pin0InfoVect0LinkObjId="SW-47695_0" Pin0InfoVect1LinkObjId="SW-47694_0" Pin0InfoVect2LinkObjId="g_12bd3a0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47695_0" Pin1InfoVect1LinkObjId="SW-47694_0" Pin1InfoVect2LinkObjId="SW-47695_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-715 3910,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137a080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-635 3649,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8623@1" Pin0InfoVect0LinkObjId="SW-47642_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-635 3649,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1379e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-578 3649,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8623@0" ObjectIDZND0="8622@1" Pin0InfoVect0LinkObjId="SW-47641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-578 3649,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1314340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-535 3649,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8622@0" ObjectIDZND0="8624@1" Pin0InfoVect0LinkObjId="SW-47643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-535 3649,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137aba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-483 3649,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8624@0" ObjectIDZND0="19875@x" Pin0InfoVect0LinkObjId="SW-94369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-483 3649,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137a720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-464 3649,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8624@x" ObjectIDND1="19875@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47643_0" Pin1InfoVect1LinkObjId="SW-94369_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-464 3649,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1313c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-464 3670,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8624@x" ObjectIDZND0="19875@0" Pin0InfoVect0LinkObjId="SW-94369_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-464 3670,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1312b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3706,-464 3727,-464 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="19875@1" ObjectIDZND0="g_22d9560@0" Pin0InfoVect0LinkObjId="g_22d9560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-94369_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3706,-464 3727,-464 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_149d8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-635 3787,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8627@1" Pin0InfoVect0LinkObjId="SW-47646_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-635 3787,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-575 3787,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8627@0" ObjectIDZND0="8626@1" Pin0InfoVect0LinkObjId="SW-47645_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-575 3787,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1240cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-532 3787,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8626@0" ObjectIDZND0="8628@1" Pin0InfoVect0LinkObjId="SW-47647_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47645_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-532 3787,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-480 3787,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8628@0" ObjectIDZND0="8629@x" Pin0InfoVect0LinkObjId="SW-47648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-480 3787,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11adfa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-461 3787,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8628@x" ObjectIDND1="8629@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47647_0" Pin1InfoVect1LinkObjId="SW-47648_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-461 3787,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125fd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3787,-461 3808,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8628@x" ObjectIDZND0="8629@0" Pin0InfoVect0LinkObjId="SW-47648_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47647_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3787,-461 3808,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1313750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-461 3865,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8629@1" ObjectIDZND0="g_2296b90@0" Pin0InfoVect0LinkObjId="g_2296b90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47648_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-461 3865,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123aaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-635 3931,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8631@1" Pin0InfoVect0LinkObjId="SW-47650_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-635 3931,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11aea20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-577 3931,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8631@0" ObjectIDZND0="8630@1" Pin0InfoVect0LinkObjId="SW-47649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-577 3931,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c1470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-534 3931,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8630@0" ObjectIDZND0="8632@1" Pin0InfoVect0LinkObjId="SW-47651_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-534 3931,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12dd140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-482 3931,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8632@0" ObjectIDZND0="8633@x" Pin0InfoVect0LinkObjId="SW-47652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-482 3931,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1350100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-463 3931,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8632@x" ObjectIDND1="8633@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47651_0" Pin1InfoVect1LinkObjId="SW-47652_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-463 3931,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1215d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-463 3952,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8632@x" ObjectIDZND0="8633@0" Pin0InfoVect0LinkObjId="SW-47652_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-463 3952,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1242dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3988,-463 4009,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8633@1" ObjectIDZND0="g_223eee0@0" Pin0InfoVect0LinkObjId="g_223eee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47652_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3988,-463 4009,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1227960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-575 4078,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8635@0" ObjectIDZND0="8634@1" Pin0InfoVect0LinkObjId="SW-47653_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47654_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-575 4078,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12678a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-532 4078,-516 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8634@0" ObjectIDZND0="8636@1" Pin0InfoVect0LinkObjId="SW-47655_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47653_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-532 4078,-516 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-480 4078,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8636@0" ObjectIDZND0="8637@x" Pin0InfoVect0LinkObjId="SW-47656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-480 4078,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_130ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-461 4078,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8636@x" ObjectIDND1="8637@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47655_0" Pin1InfoVect1LinkObjId="SW-47656_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-461 4078,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1244f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-461 4099,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8636@x" ObjectIDZND0="8637@0" Pin0InfoVect0LinkObjId="SW-47656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47655_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-461 4099,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12182c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4135,-461 4156,-461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8637@1" ObjectIDZND0="g_1245180@0" Pin0InfoVect0LinkObjId="g_1245180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4135,-461 4156,-461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12725d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4078,-635 4078,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8635@1" Pin0InfoVect0LinkObjId="SW-47654_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4078,-635 4078,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-635 4113,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8639@0" Pin0InfoVect0LinkObjId="SW-47658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-635 4113,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_137b960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-697 4113,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8639@1" ObjectIDZND0="8638@0" Pin0InfoVect0LinkObjId="SW-47657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47658_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-697 4113,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b5720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-740 4113,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8638@1" ObjectIDZND0="8640@0" Pin0InfoVect0LinkObjId="SW-47659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-740 4113,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12432c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-819 4134,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8640@x" ObjectIDZND0="8641@0" Pin0InfoVect0LinkObjId="SW-47660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-819 4134,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11ae2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4170,-819 4191,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8641@1" ObjectIDZND0="g_11ae4e0@0" Pin0InfoVect0LinkObjId="g_11ae4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4170,-819 4191,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c1ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-792 4113,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8640@1" ObjectIDZND0="8641@x" Pin0InfoVect0LinkObjId="SW-47660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-792 4113,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1312d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4113,-819 4113,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8641@x" ObjectIDND1="8640@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47660_0" Pin1InfoVect1LinkObjId="SW-47659_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4113,-819 4113,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1272bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-635 4195,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19873@0" ObjectIDZND0="8643@1" Pin0InfoVect0LinkObjId="SW-47662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-635 4195,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1243820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-636 4307,-617 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8644@1" Pin0InfoVect0LinkObjId="SW-47663_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-636 4307,-617 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b13c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-582 4195,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8643@0" ObjectIDZND0="8642@x" ObjectIDZND1="8645@x" Pin0InfoVect0LinkObjId="SW-47661_0" Pin0InfoVect1LinkObjId="SW-47664_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-582 4195,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1199990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-581 4307,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8644@0" ObjectIDZND0="8642@x" ObjectIDZND1="8646@x" Pin0InfoVect0LinkObjId="SW-47661_0" Pin0InfoVect1LinkObjId="SW-47669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47663_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-581 4307,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_148ca10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-568 4237,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8643@x" ObjectIDND1="8645@x" ObjectIDZND0="8642@1" Pin0InfoVect0LinkObjId="SW-47661_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47662_0" Pin1InfoVect1LinkObjId="SW-47664_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-568 4237,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12249c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4264,-568 4307,-568 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8642@0" ObjectIDZND0="8644@x" ObjectIDZND1="8646@x" Pin0InfoVect0LinkObjId="SW-47663_0" Pin0InfoVect1LinkObjId="SW-47669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47661_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4264,-568 4307,-568 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131dd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-568 4307,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8644@x" ObjectIDND1="8642@x" ObjectIDZND0="8646@1" Pin0InfoVect0LinkObjId="SW-47669_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47663_0" Pin1InfoVect1LinkObjId="SW-47661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-568 4307,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131df90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4307,-514 4307,-496 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8646@0" ObjectIDZND0="g_12c0270@0" Pin0InfoVect0LinkObjId="g_12c0270_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4307,-514 4307,-496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131e1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-568 4195,-551 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8643@x" ObjectIDND1="8642@x" ObjectIDZND0="8645@1" Pin0InfoVect0LinkObjId="SW-47664_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47662_0" Pin1InfoVect1LinkObjId="SW-47661_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-568 4195,-551 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131e3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4195,-515 4195,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8645@0" ObjectIDZND0="g_122a6d0@0" Pin0InfoVect0LinkObjId="g_122a6d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4195,-515 4195,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1240fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-636 4421,-613 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8652@1" Pin0InfoVect0LinkObjId="SW-47675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-636 4421,-613 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e5380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-577 4421,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8652@0" ObjectIDZND0="8651@1" Pin0InfoVect0LinkObjId="SW-47674_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-577 4421,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12733b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-534 4421,-518 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8651@0" ObjectIDZND0="8653@1" Pin0InfoVect0LinkObjId="SW-47676_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-534 4421,-518 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12735d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-482 4421,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8653@0" ObjectIDZND0="8654@x" Pin0InfoVect0LinkObjId="SW-47677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-482 4421,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12737f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-463 4421,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8653@x" ObjectIDND1="8654@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47676_0" Pin1InfoVect1LinkObjId="SW-47677_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-463 4421,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4421,-463 4442,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8653@x" ObjectIDZND0="8654@0" Pin0InfoVect0LinkObjId="SW-47677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47676_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4421,-463 4442,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_119c3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-463 4499,-463 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8654@1" ObjectIDZND0="g_119c610@0" Pin0InfoVect0LinkObjId="g_119c610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-463 4499,-463 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e3260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-636 4569,-609 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8656@1" Pin0InfoVect0LinkObjId="SW-47679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-636 4569,-609 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_122b7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-573 4569,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8656@0" ObjectIDZND0="8655@1" Pin0InfoVect0LinkObjId="SW-47678_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-573 4569,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cab90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-530 4569,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8655@0" ObjectIDZND0="8657@1" Pin0InfoVect0LinkObjId="SW-47680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47678_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-530 4569,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cadb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-478 4569,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="8657@0" ObjectIDZND0="8658@x" ObjectIDZND1="g_1265c50@0" Pin0InfoVect0LinkObjId="SW-47681_0" Pin0InfoVect1LinkObjId="g_1265c50_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-478 4569,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e3f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-459 4590,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8657@x" ObjectIDND1="g_1265c50@0" ObjectIDZND0="8658@0" Pin0InfoVect0LinkObjId="SW-47681_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47680_0" Pin1InfoVect1LinkObjId="g_1265c50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-459 4590,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11e4120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-459 4647,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8658@1" ObjectIDZND0="g_11e4340@0" Pin0InfoVect0LinkObjId="g_11e4340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47681_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-459 4647,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12251c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-574 4716,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8660@0" ObjectIDZND0="8659@1" Pin0InfoVect0LinkObjId="SW-47682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47683_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-574 4716,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121a300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-531 4716,-515 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8659@0" ObjectIDZND0="8661@1" Pin0InfoVect0LinkObjId="SW-47684_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-531 4716,-515 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121a520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-479 4716,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8661@0" ObjectIDZND0="8662@x" Pin0InfoVect0LinkObjId="SW-47685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-479 4716,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-460 4716,-389 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8661@x" ObjectIDND1="8662@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47684_0" Pin1InfoVect1LinkObjId="SW-47685_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-460 4716,-389 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123b700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-460 4737,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8661@x" ObjectIDZND0="8662@0" Pin0InfoVect0LinkObjId="SW-47685_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-460 4737,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_123b920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4773,-460 4794,-460 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8662@1" ObjectIDZND0="g_123bb40@0" Pin0InfoVect0LinkObjId="g_123bb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47685_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4773,-460 4794,-460 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c5080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-636 4716,-610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8660@1" Pin0InfoVect0LinkObjId="SW-47683_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-636 4716,-610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_131ee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-576 4859,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8668@0" ObjectIDZND0="8667@1" Pin0InfoVect0LinkObjId="SW-47690_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47691_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-576 4859,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cc810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-533 4859,-517 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8667@0" ObjectIDZND0="8669@1" Pin0InfoVect0LinkObjId="SW-47692_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-533 4859,-517 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cca30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-481 4859,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8669@0" ObjectIDZND0="8670@x" Pin0InfoVect0LinkObjId="SW-47693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-481 4859,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ccc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-462 4859,-391 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8669@x" ObjectIDND1="8670@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47692_0" Pin1InfoVect1LinkObjId="SW-47693_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-462 4859,-391 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12f01f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-462 4880,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8669@x" ObjectIDZND0="8670@0" Pin0InfoVect0LinkObjId="SW-47693_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47692_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-462 4880,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fa340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-462 4937,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8670@1" ObjectIDZND0="g_12fa560@0" Pin0InfoVect0LinkObjId="g_12fa560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47693_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-462 4937,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1221c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4859,-636 4859,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8668@1" Pin0InfoVect0LinkObjId="SW-47691_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4859,-636 4859,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_126fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-716 4383,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="8673@x" ObjectIDND1="8674@x" ObjectIDND2="8673@x" ObjectIDZND0="8674@1" Pin0InfoVect0LinkObjId="SW-47697_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47696_0" Pin1InfoVect1LinkObjId="SW-47697_0" Pin1InfoVect2LinkObjId="SW-47696_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-716 4383,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12cd750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-716 4330,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8674@0" ObjectIDZND0="g_12cd970@0" Pin0InfoVect0LinkObjId="g_12cd970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47697_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-716 4330,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a1d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-636 4401,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8673@0" Pin0InfoVect0LinkObjId="SW-47696_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-636 4401,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12a1f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-697 4401,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="8673@1" ObjectIDZND0="8674@x" ObjectIDZND1="8674@x" ObjectIDZND2="8673@x" Pin0InfoVect0LinkObjId="SW-47697_0" Pin0InfoVect1LinkObjId="SW-47697_0" Pin0InfoVect2LinkObjId="SW-47696_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47696_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-697 4401,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f2d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-636 4621,-660 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8648@0" Pin0InfoVect0LinkObjId="SW-47671_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-636 4621,-660 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f2f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-696 4621,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="8648@1" ObjectIDZND0="8650@x" ObjectIDZND1="g_128f030@0" ObjectIDZND2="g_127a5b0@0" Pin0InfoVect0LinkObjId="SW-47673_0" Pin0InfoVect1LinkObjId="g_128f030_0" Pin0InfoVect2LinkObjId="g_127a5b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47671_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-696 4621,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f31a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-715 4603,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="8648@x" ObjectIDND1="g_128f030@0" ObjectIDND2="g_127a5b0@0" ObjectIDZND0="8650@1" Pin0InfoVect0LinkObjId="SW-47673_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47671_0" Pin1InfoVect1LinkObjId="g_128f030_0" Pin1InfoVect2LinkObjId="g_127a5b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-715 4603,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f33c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-715 4550,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8650@0" ObjectIDZND0="g_11f35e0@0" Pin0InfoVect0LinkObjId="g_11f35e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47673_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-715 4550,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f3f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4644,-775 4644,-756 4621,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_127a5b0@0" ObjectIDZND0="8648@x" ObjectIDZND1="8650@x" ObjectIDZND2="g_128f030@0" Pin0InfoVect0LinkObjId="SW-47671_0" Pin0InfoVect1LinkObjId="SW-47673_0" Pin0InfoVect2LinkObjId="g_128f030_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_127a5b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4644,-775 4644,-756 4621,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f4130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-756 4621,-715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_128f030@0" ObjectIDND1="g_127a5b0@0" ObjectIDZND0="8648@x" ObjectIDZND1="8650@x" Pin0InfoVect0LinkObjId="SW-47671_0" Pin0InfoVect1LinkObjId="SW-47673_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_128f030_0" Pin1InfoVect1LinkObjId="g_127a5b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-756 4621,-715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f4940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-756 4578,-756 4578,-766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="8648@x" ObjectIDND1="8650@x" ObjectIDND2="g_127a5b0@0" ObjectIDZND0="g_128f030@0" Pin0InfoVect0LinkObjId="g_128f030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47671_0" Pin1InfoVect1LinkObjId="SW-47673_0" Pin1InfoVect2LinkObjId="g_127a5b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-756 4578,-756 4578,-766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f4b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4578,-797 4578,-815 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_128f030@1" ObjectIDZND0="g_11f4d80@0" Pin0InfoVect0LinkObjId="g_11f4d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_128f030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4578,-797 4578,-815 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121b320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-636 4794,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="19874@0" ObjectIDZND0="8664@0" Pin0InfoVect0LinkObjId="SW-47687_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-636 4794,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121b540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-697 4794,-713 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8664@1" ObjectIDZND0="8663@0" Pin0InfoVect0LinkObjId="SW-47686_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47687_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-697 4794,-713 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_121b760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-740 4794,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8663@1" ObjectIDZND0="8665@0" Pin0InfoVect0LinkObjId="SW-47688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47686_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-740 4794,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d1590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4852,-819 4873,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8666@1" ObjectIDZND0="g_12d17b0@0" Pin0InfoVect0LinkObjId="g_12d17b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47689_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4852,-819 4873,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12d20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-819 4794,-900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="8666@x" ObjectIDND1="8665@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47689_0" Pin1InfoVect1LinkObjId="SW-47688_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-819 4794,-900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1201810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4816,-819 4794,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8666@0" ObjectIDZND0="8665@x" Pin0InfoVect0LinkObjId="SW-47688_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4816,-819 4794,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1201a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4794,-819 4794,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8666@x" ObjectIDZND0="8665@1" Pin0InfoVect0LinkObjId="SW-47688_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47689_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4794,-819 4794,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1266840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-402 4569,-438 4590,-438 4590,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8657@x" ObjectIDND1="8658@x" ObjectIDZND0="g_1265c50@0" Pin0InfoVect0LinkObjId="g_1265c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47680_0" Pin1InfoVect1LinkObjId="SW-47681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-402 4569,-438 4590,-438 4590,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1267270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-459 4569,-402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="8657@x" ObjectIDND1="8658@x" ObjectIDZND0="g_1265c50@0" Pin0InfoVect0LinkObjId="g_1265c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47680_0" Pin1InfoVect1LinkObjId="SW-47681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-459 4569,-402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1267490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4569,-402 4569,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" ObjectIDND0="g_1265c50@0" ObjectIDND1="8657@x" ObjectIDND2="8658@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1265c50_0" Pin1InfoVect1LinkObjId="SW-47680_0" Pin1InfoVect2LinkObjId="SW-47681_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4569,-402 4569,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12baeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-717 3910,-740 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8672@x" ObjectIDND1="8671@x" ObjectIDND2="8672@x" ObjectIDZND0="8672@x" ObjectIDZND1="8671@x" ObjectIDZND2="g_12bd3a0@0" Pin0InfoVect0LinkObjId="SW-47695_0" Pin0InfoVect1LinkObjId="SW-47694_0" Pin0InfoVect2LinkObjId="g_12bd3a0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47695_0" Pin1InfoVect1LinkObjId="SW-47694_0" Pin1InfoVect2LinkObjId="SW-47695_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-717 3910,-740 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-716 4401,-734 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8674@x" ObjectIDND1="8673@x" ObjectIDZND0="8674@x" ObjectIDZND1="8673@x" ObjectIDZND2="g_11876c0@0" Pin0InfoVect0LinkObjId="SW-47697_0" Pin0InfoVect1LinkObjId="SW-47696_0" Pin0InfoVect2LinkObjId="g_11876c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47697_0" Pin1InfoVect1LinkObjId="SW-47696_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-716 4401,-734 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-734 4401,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8674@x" ObjectIDND1="8673@x" ObjectIDND2="8674@x" ObjectIDZND0="8674@x" ObjectIDZND1="8673@x" ObjectIDZND2="g_11876c0@0" Pin0InfoVect0LinkObjId="SW-47697_0" Pin0InfoVect1LinkObjId="SW-47696_0" Pin0InfoVect2LinkObjId="g_11876c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47697_0" Pin1InfoVect1LinkObjId="SW-47696_0" Pin1InfoVect2LinkObjId="SW-47697_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-734 4401,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bc320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-734 4401,-741 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="8674@x" ObjectIDND1="8673@x" ObjectIDND2="8674@x" ObjectIDZND0="8674@x" ObjectIDZND1="8673@x" ObjectIDZND2="g_11876c0@0" Pin0InfoVect0LinkObjId="SW-47697_0" Pin0InfoVect1LinkObjId="SW-47696_0" Pin0InfoVect2LinkObjId="g_11876c0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47697_0" Pin1InfoVect1LinkObjId="SW-47696_0" Pin1InfoVect2LinkObjId="SW-47697_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-734 4401,-741 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1187280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-740 3910,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8672@x" ObjectIDND1="8671@x" ObjectIDND2="8672@x" ObjectIDZND0="g_12bd3a0@0" Pin0InfoVect0LinkObjId="g_12bd3a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47695_0" Pin1InfoVect1LinkObjId="SW-47694_0" Pin1InfoVect2LinkObjId="SW-47695_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-740 3910,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11874a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-791 3910,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12bd3a0@1" ObjectIDZND0="g_234e7f0@0" Pin0InfoVect0LinkObjId="g_234e7f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12bd3a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-791 3910,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1236200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-741 4401,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="8674@x" ObjectIDND1="8673@x" ObjectIDND2="8674@x" ObjectIDZND0="g_11876c0@0" Pin0InfoVect0LinkObjId="g_11876c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-47697_0" Pin1InfoVect1LinkObjId="SW-47696_0" Pin1InfoVect2LinkObjId="SW-47697_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-741 4401,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1236420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4401,-789 4401,-804 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_11876c0@1" ObjectIDZND0="g_12a2150@0" Pin0InfoVect0LinkObjId="g_12a2150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11876c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4401,-789 4401,-804 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4307" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4421" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4569" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4716" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4859" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4401" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4621" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19874" cx="4794" cy="-636" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="3732" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="3910" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="3649" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="3787" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="3931" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="4078" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="4113" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="19873" cx="4195" cy="-635" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37326" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3406.000000 -1086.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5908" ObjectName="DYN-CX_LBG"/>
     <cge:Meas_Ref ObjectId="37326"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e5e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -869.000000) translate(0,15)">10kVI段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12d3900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3875.000000 -879.000000) translate(0,15)">10kV1号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e6200" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4050.000000 -926.000000) translate(0,15)">10kV南城II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e65f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4366.000000 -876.000000) translate(0,15)">10kV2号变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e6920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -862.000000) translate(0,15)">10kVII段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e6c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 -926.000000) translate(0,15)">10kV楚城III回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e6e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3605.000000 -375.000000) translate(0,15)">10kV鲁北线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12e71c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3748.000000 -375.000000) translate(0,15)">10kV鲁青线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11fffb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3883.000000 -375.000000) translate(0,15)">10kV南城I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1200340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4042.000000 -375.000000) translate(0,15)">10kV东鲁I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1200520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4367.000000 -375.000000) translate(0,15)">10kV州医院I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1200880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4512.000000 -375.000000) translate(0,15)">10kV东鲁II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1200be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4675.000000 -375.000000) translate(0,15)">10kV鲁尹线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1200d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -375.000000) translate(0,15)">10kV楚城I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1201bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4213.000000 -509.000000) translate(0,15)">10kV母联</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1201ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3676.000000 -741.000000) translate(0,12)">09017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12022b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3920.000000 -684.000000) translate(0,12)">0641</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1202670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -741.000000) translate(0,12)">06417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4125.000000 -733.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -685.000000) translate(0,12)">0611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -780.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1279e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4133.000000 -845.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127a010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4408.000000 -686.000000) translate(0,12)">0712</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127a1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4344.000000 -742.000000) translate(0,12)">07127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127bac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4628.000000 -685.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127be90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4564.000000 -741.000000) translate(0,12)">09027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127c070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4803.000000 -734.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127c250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -686.000000) translate(0,12)">0752</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a3b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4801.000000 -781.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a3e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4814.000000 -845.000000) translate(0,12)">07567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a3ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4868.000000 -554.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a41d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4866.000000 -601.000000) translate(0,12)">0762</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a43b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4877.000000 -488.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4866.000000 -506.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -599.000000) translate(0,12)">0742</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -486.000000) translate(0,12)">07467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -504.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4725.000000 -552.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a4ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4578.000000 -551.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -503.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4590.000000 -480.000000) translate(0,12)">07367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -598.000000) translate(0,12)">0732</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4430.000000 -555.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4428.000000 -507.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4447.000000 -485.000000) translate(0,12)">07267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4428.000000 -602.000000) translate(0,12)">0722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a5fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4238.000000 -592.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a61b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -607.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a6390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -606.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12a6570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -540.000000) translate(0,12)">01217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b18f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -539.000000) translate(0,12)">01227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b1ad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4087.000000 -553.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b1cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -505.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b1e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -483.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4085.000000 -600.000000) translate(0,12)">0621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -555.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -507.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3949.000000 -489.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b27f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -602.000000) translate(0,12)">0631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b29d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -553.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -600.000000) translate(0,12)">0651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -505.000000) translate(0,12)">0656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b2f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -484.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3658.000000 -556.000000) translate(0,12)">066</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -508.000000) translate(0,12)">0666</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3674.000000 -488.000000) translate(0,12)">06667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b36f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3656.000000 -603.000000) translate(0,12)">0661</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1220640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3136.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_12647d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3262.500000 -1165.500000) translate(0,16)">鲁班阁开闭所</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12676b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3584.000000 -658.500000) translate(0,12)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12ba4d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4903.000000 -658.500000) translate(0,12)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12bc540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3742.000000 -685.000000) translate(0,12)">0901</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3640.000000 -527.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8622" ObjectName="SW-CX_LBG.CX_LBG_066BK"/>
     <cge:Meas_Ref ObjectId="47641"/>
    <cge:TPSR_Ref TObjectID="8622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47645">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -524.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8626" ObjectName="SW-CX_LBG.CX_LBG_065BK"/>
     <cge:Meas_Ref ObjectId="47645"/>
    <cge:TPSR_Ref TObjectID="8626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47649">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8630" ObjectName="SW-CX_LBG.CX_LBG_063BK"/>
     <cge:Meas_Ref ObjectId="47649"/>
    <cge:TPSR_Ref TObjectID="8630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47653">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4069.000000 -524.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8634" ObjectName="SW-CX_LBG.CX_LBG_062BK"/>
     <cge:Meas_Ref ObjectId="47653"/>
    <cge:TPSR_Ref TObjectID="8634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4104.000000 -705.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8638" ObjectName="SW-CX_LBG.CX_LBG_061BK"/>
     <cge:Meas_Ref ObjectId="47657"/>
    <cge:TPSR_Ref TObjectID="8638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4228.000000 -558.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8642" ObjectName="SW-CX_LBG.CX_LBG_012BK"/>
     <cge:Meas_Ref ObjectId="47661"/>
    <cge:TPSR_Ref TObjectID="8642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47674">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4412.000000 -526.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8651" ObjectName="SW-CX_LBG.CX_LBG_072BK"/>
     <cge:Meas_Ref ObjectId="47674"/>
    <cge:TPSR_Ref TObjectID="8651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47678">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -522.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8655" ObjectName="SW-CX_LBG.CX_LBG_073BK"/>
     <cge:Meas_Ref ObjectId="47678"/>
    <cge:TPSR_Ref TObjectID="8655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4707.000000 -523.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8659" ObjectName="SW-CX_LBG.CX_LBG_074BK"/>
     <cge:Meas_Ref ObjectId="47682"/>
    <cge:TPSR_Ref TObjectID="8659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47690">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4850.000000 -525.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8667" ObjectName="SW-CX_LBG.CX_LBG_076BK"/>
     <cge:Meas_Ref ObjectId="47690"/>
    <cge:TPSR_Ref TObjectID="8667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47686">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4785.000000 -705.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8663" ObjectName="SW-CX_LBG.CX_LBG_075BK"/>
     <cge:Meas_Ref ObjectId="47686"/>
    <cge:TPSR_Ref TObjectID="8663"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_176d2b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 -761.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1217370">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3676.000000 -839.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_234e7f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3898.000000 -847.000000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12a2150">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4389.000000 -848.000000)" xlink:href="#lightningRod:shape25"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_128f030">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4569.000000 -761.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11f4d80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4565.000000 -839.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127a5b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 -770.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_127aed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3753.000000 -770.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1265c50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4583.000000 -435.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12bd3a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3901.000000 -755.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11876c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4392.000000 -753.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-94305" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-94306" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-94307" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-94311" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-94308" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-94312" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3549.000000 -747.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94312" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19873"/>
     <cge:Term_Ref ObjectID="12140"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47583" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -982.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47583" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8638"/>
     <cge:Term_Ref ObjectID="12142"/>
    <cge:TPSR_Ref TObjectID="8638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47584" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -982.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8638"/>
     <cge:Term_Ref ObjectID="12142"/>
    <cge:TPSR_Ref TObjectID="8638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47579" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4110.000000 -982.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8638"/>
     <cge:Term_Ref ObjectID="12142"/>
    <cge:TPSR_Ref TObjectID="8638"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47627" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4791.000000 -982.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8663"/>
     <cge:Term_Ref ObjectID="12182"/>
    <cge:TPSR_Ref TObjectID="8663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47628" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4791.000000 -982.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8663"/>
     <cge:Term_Ref ObjectID="12182"/>
    <cge:TPSR_Ref TObjectID="8663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47623" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4791.000000 -982.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47623" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8663"/>
     <cge:Term_Ref ObjectID="12182"/>
    <cge:TPSR_Ref TObjectID="8663"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47637" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47637" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8667"/>
     <cge:Term_Ref ObjectID="12214"/>
    <cge:TPSR_Ref TObjectID="8667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47638" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47638" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8667"/>
     <cge:Term_Ref ObjectID="12214"/>
    <cge:TPSR_Ref TObjectID="8667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47633" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4864.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8667"/>
     <cge:Term_Ref ObjectID="12214"/>
    <cge:TPSR_Ref TObjectID="8667"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47616" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47616" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8659"/>
     <cge:Term_Ref ObjectID="12206"/>
    <cge:TPSR_Ref TObjectID="8659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47617" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47617" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8659"/>
     <cge:Term_Ref ObjectID="12206"/>
    <cge:TPSR_Ref TObjectID="8659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47612" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47612" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8659"/>
     <cge:Term_Ref ObjectID="12206"/>
    <cge:TPSR_Ref TObjectID="8659"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47609" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47609" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8655"/>
     <cge:Term_Ref ObjectID="12198"/>
    <cge:TPSR_Ref TObjectID="8655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47610" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47610" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8655"/>
     <cge:Term_Ref ObjectID="12198"/>
    <cge:TPSR_Ref TObjectID="8655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47605" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4565.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8655"/>
     <cge:Term_Ref ObjectID="12198"/>
    <cge:TPSR_Ref TObjectID="8655"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47602" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47602" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8651"/>
     <cge:Term_Ref ObjectID="12190"/>
    <cge:TPSR_Ref TObjectID="8651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47603" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8651"/>
     <cge:Term_Ref ObjectID="12190"/>
    <cge:TPSR_Ref TObjectID="8651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47598" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4410.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47598" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8651"/>
     <cge:Term_Ref ObjectID="12190"/>
    <cge:TPSR_Ref TObjectID="8651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47594" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -445.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47594" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8642"/>
     <cge:Term_Ref ObjectID="12222"/>
    <cge:TPSR_Ref TObjectID="8642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47595" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -445.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47595" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8642"/>
     <cge:Term_Ref ObjectID="12222"/>
    <cge:TPSR_Ref TObjectID="8642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47590" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4253.000000 -445.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47590" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8642"/>
     <cge:Term_Ref ObjectID="12222"/>
    <cge:TPSR_Ref TObjectID="8642"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47572" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8634"/>
     <cge:Term_Ref ObjectID="12150"/>
    <cge:TPSR_Ref TObjectID="8634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47573" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8634"/>
     <cge:Term_Ref ObjectID="12150"/>
    <cge:TPSR_Ref TObjectID="8634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47568" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4089.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47568" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8634"/>
     <cge:Term_Ref ObjectID="12150"/>
    <cge:TPSR_Ref TObjectID="8634"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3923.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8630"/>
     <cge:Term_Ref ObjectID="12158"/>
    <cge:TPSR_Ref TObjectID="8630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3923.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8630"/>
     <cge:Term_Ref ObjectID="12158"/>
    <cge:TPSR_Ref TObjectID="8630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47561" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3923.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8630"/>
     <cge:Term_Ref ObjectID="12158"/>
    <cge:TPSR_Ref TObjectID="8630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47558" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47558" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8626"/>
     <cge:Term_Ref ObjectID="12166"/>
    <cge:TPSR_Ref TObjectID="8626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8626"/>
     <cge:Term_Ref ObjectID="12166"/>
    <cge:TPSR_Ref TObjectID="8626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47554" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3787.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47554" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8626"/>
     <cge:Term_Ref ObjectID="12166"/>
    <cge:TPSR_Ref TObjectID="8626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47550" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47550" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8622"/>
     <cge:Term_Ref ObjectID="12174"/>
    <cge:TPSR_Ref TObjectID="8622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47551" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47551" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8622"/>
     <cge:Term_Ref ObjectID="12174"/>
    <cge:TPSR_Ref TObjectID="8622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47546" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3641.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47546" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8622"/>
     <cge:Term_Ref ObjectID="12174"/>
    <cge:TPSR_Ref TObjectID="8622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-94313" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94313" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-94314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94314" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-94315" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94315" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-94319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-94316" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94316" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-94320" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -761.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="94320" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="19874"/>
     <cge:Term_Ref ObjectID="12141"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3239" y="-1177"/></g>
   <g href="cx_配调_配网接线图开闭所.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3191" y="-1194"/></g>
   <g href="10kV鲁班阁开闭所10kV南城Ⅱ回线061断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4125" y="-733"/></g>
   <g href="10kV鲁班阁开闭所10kV鲁北线066断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3658" y="-556"/></g>
   <g href="10kV鲁班阁开闭所10kV鲁青线065断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3796" y="-553"/></g>
   <g href="10kV鲁班阁开闭所10kV南城Ⅰ回线063断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3940" y="-555"/></g>
   <g href="10kV鲁班阁开闭所10kV东鲁Ⅰ回线062断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4087" y="-553"/></g>
   <g href="10kV鲁班阁开闭所10kV母联012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4238" y="-592"/></g>
   <g href="10kV鲁班阁开闭所10kV州医院Ⅰ回线072断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4430" y="-555"/></g>
   <g href="10kV鲁班阁开闭所10kV东鲁Ⅱ回线073断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4578" y="-551"/></g>
   <g href="10kV鲁班阁开闭所10kV鲁尹线074断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4725" y="-552"/></g>
   <g href="10kV鲁班阁开闭所10kV楚城Ⅰ回线076断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4868" y="-554"/></g>
   <g href="10kV鲁班阁开闭所10kV楚城Ⅲ回线075断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4803" y="-734"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3ab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4053.000000 983.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 968.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b3ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 953.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12b4350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4736.000000 983.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fd170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 968.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fd350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4750.000000 953.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fd710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fd8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3575.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fdad0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fde90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3729.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3743.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3865.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fe9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3879.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fed90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4028.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11fef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ff150" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4042.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ff510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4196.000000 442.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ff6f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4185.000000 427.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ff8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4210.000000 412.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ffc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121ddc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4335.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121dfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4510.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4499.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4524.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121eae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121ecc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4637.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121eea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4662.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4791.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4816.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1236770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3474.500000 707.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1236c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3460.000000 693.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1237010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 722.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1237340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 737.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12376a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3473.000000 675.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1237880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 753.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1237cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.500000 716.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1237ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 702.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12380d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 731.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12382b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 746.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1238490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 684.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1238670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4868.000000 762.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3227.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LBG.CX_LBG_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3608,-635 4225,-635 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19873" ObjectName="BS-CX_LBG.CX_LBG_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="19873"/></metadata>
   <polyline fill="none" opacity="0" points="3608,-635 4225,-635 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LBG.CX_LBG_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4274,-636 5000,-636 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="19874" ObjectName="BS-CX_LBG.CX_LBG_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="19874"/></metadata>
   <polyline fill="none" opacity="0" points="4274,-636 5000,-636 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3239" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3239" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3191" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3191" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4125" y="-733"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4125" y="-733"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3658" y="-556"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3658" y="-556"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3796" y="-553"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3796" y="-553"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3940" y="-555"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3940" y="-555"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4087" y="-553"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4087" y="-553"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4238" y="-592"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4238" y="-592"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4430" y="-555"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4430" y="-555"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4578" y="-551"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4578" y="-551"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4725" y="-552"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4725" y="-552"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4868" y="-554"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4868" y="-554"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4803" y="-734"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4803" y="-734"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LBG"/>
</svg>