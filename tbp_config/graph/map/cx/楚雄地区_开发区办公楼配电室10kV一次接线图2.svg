<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-248" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="97363 -3560 2285 1311">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a632a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a64410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a64ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a66140" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a67330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a67c10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a68700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a69190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a6a750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a6a750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a6bf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a6bf70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a6d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a6d940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a6e8d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a70530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a711c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a72030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a72780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a74000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a74c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a75520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a75ce0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a76dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a77740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a78230" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a78bf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a7a210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a7ac30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a7bdd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a7ca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a8ae70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a83210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1a7dbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1a7ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1321" width="2295" x="97358" y="-3565"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1471a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98480.000000 3319.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1473220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98505.000000 3304.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1473a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98510.000000 3288.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1474630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98491.000000 3334.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a7850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98990.000000 3306.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a7b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99015.000000 3291.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a7d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99020.000000 3275.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a7f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99001.000000 3321.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a82b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98490.000000 3140.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a8520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98515.000000 3125.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a8760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98520.000000 3109.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a89a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98501.000000 3155.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a8cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99000.000000 3136.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a8f40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99025.000000 3121.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a9180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99030.000000 3105.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a93c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99011.000000 3151.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13a96f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98710.000000 3273.000000) translate(0,12)">3U0（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aa7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98718.000000 3289.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aae20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98718.000000 3302.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ab380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98718.000000 3320.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ab600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98710.000000 3256.000000) translate(0,12)">Uab（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aec50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98010.000000 2863.000000) translate(0,12)">Uc（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13aeec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98010.000000 2876.000000) translate(0,12)">Ub（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13af100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98010.000000 2894.000000) translate(0,12)">Ua（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13af340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98002.000000 2830.000000) translate(0,12)">Uab（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13af580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98002.000000 2847.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13af8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99509.000000 2831.000000) translate(0,12)">Uc（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13afb20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99509.000000 2844.000000) translate(0,12)">Ub（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13afd60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99509.000000 2862.000000) translate(0,12)">Ua（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13affa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99501.000000 2798.000000) translate(0,12)">Uab（V）：</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13b01e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99501.000000 2815.000000) translate(0,12)">3U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1389060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98656.000000 2914.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1389270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98670.000000 2867.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1389470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98669.000000 2850.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13896b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98645.000000 2899.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13898f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98670.000000 2884.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="0" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1389c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99005.000000 2921.000000) translate(0,12)">P(kW):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1389e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99019.000000 2874.000000) translate(0,12)">Ib(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a0d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99018.000000 2857.000000) translate(0,12)">Ic(A):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98994.000000 2906.000000) translate(0,12)">Q(kVar):</text>
    <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138a550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 99019.000000 2891.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="0" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97365" y="-3559"/>
   <rect DF8003:Layer="0" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97364" y="-3439"/>
   <rect DF8003:Layer="0" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="97364" y="-2959"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97804" y="-2629"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97863" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97905" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97944" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="97986" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98023" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98065" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98104" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98390" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98348" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98311" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98269" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98229" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98188" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98147" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98674" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98633" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98593" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98552" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="16" x="98514" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98471" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98429" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99626" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99584" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="16" x="99544" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99502" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99465" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99423" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99380" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99341" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99299" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99262" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99220" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99181" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99139" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99097" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99055" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="99016" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="41" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98974" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98937" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98896" y="-2630"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(0,255,0)" stroke-width="1" width="21" x="98797" y="-3128"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="42" x="98547" y="-3386"/>
   <rect DF8003:Layer="0" fill="none" height="13" stroke="rgb(0,255,0)" stroke-width="1" width="42" x="98989" y="-3383"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98602" y="-2934"/>
   <rect DF8003:Layer="0" fill="none" height="42" stroke="rgb(255,0,0)" stroke-width="1" width="15" x="98965" y="-2909"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98067.000000 -2249.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98066.000000 -2329.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="0" id="TF-CX_PDS.CX_PDS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="41026"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98595.000000 -2999.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98595.000000 -2999.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="28795" ObjectName="TF-CX_PDS.CX_PDS_2T"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98960.000000 -2997.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98960.000000 -2997.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_146c1e0">
     <polyline DF8003:Layer="0" fill="none" points="98608,-3234 98608,-3176 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="28786@0" ObjectIDZND0="28789@0" Pin0InfoVect0LinkObjId="SW-188995_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_146cb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98608,-3234 98608,-3176 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146c440">
     <polyline DF8003:Layer="0" fill="none" points="98608,-3084 98608,-3046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28789@1" ObjectIDZND0="28795@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188995_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98608,-3084 98608,-3046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146c6a0">
     <polyline DF8003:Layer="0" fill="none" points="98973,-3362 98973,-3346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="28787@0" Pin0InfoVect0LinkObjId="SW-188956_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98973,-3362 98973,-3346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146c900">
     <polyline DF8003:Layer="0" fill="none" points="98973,-3083 98973,-3044 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="28788@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188981_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98973,-3083 98973,-3044 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146cb60">
     <polyline DF8003:Layer="0" fill="none" points="98973,-3254 98973,-3234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="28787@1" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_146cdc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188956_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98973,-3254 98973,-3234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_146cdc0">
     <polyline DF8003:Layer="0" fill="none" points="98973,-3175 98973,-3234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="28788@0" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_146cb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188981_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98973,-3175 98973,-3234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ae220">
     <polyline DF8003:Layer="0" fill="none" points="98608,-3378 98608,-3346 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="breaker" ObjectIDZND0="28791@0" Pin0InfoVect0LinkObjId="SW-188968_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98608,-3378 98608,-3346 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_13ae410">
     <polyline DF8003:Layer="0" fill="none" points="98608,-3254 98608,-3234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" ObjectIDND0="28791@1" ObjectIDZND0="28786@0" Pin0InfoVect0LinkObjId="g_146cb60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-188968_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="98608,-3254 98608,-3234 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="0" busDevId="28786" cx="98606" cy="-3234" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="98608" cy="-3234" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="98973" cy="-3234" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="0" busDevId="28786" cx="98973" cy="-3234" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="0" freshType="0" id="DYN-188552" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 97716.000000 -3450.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28725" ObjectName="DYN-CX_PDS"/>
     <cge:Meas_Ref ObjectId="188552"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1268600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97383.000000 -3387.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,38)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,59)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,80)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,101)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,122)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,143)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,164)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,185)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,206)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,227)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,248)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,269)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,290)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,311)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,332)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,353)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_149ef40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97384.000000 -2949.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="0" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_f61dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97476.500000 -3525.500000) translate(0,16)">10kV供电办公楼配电室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="74" transform="matrix(0.581873 -0.000000 -0.000000 0.520147 98585.587388 -3509.963931) translate(0,60)">10kV供电局办公楼配电室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98523.622831 -3014.649723) translate(0,44)">2号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99006.198671 -3016.487465) translate(0,44)">1号变</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99230.356838 -3467.031520) translate(0,40)">10kV开发区Ⅱ回线37号杆</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99230.356838 -3467.031520) translate(0,90)">育才路联络线4号杆供电局办公楼Ⅰ回支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98717.364343 -3013.010737) translate(0,44)">10kV母线电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99503.560102 -2776.244155) translate(0,44)">0.4kVI段母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,24)">应</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,54)">急</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,84)">发</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,174)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,204)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,234)">接</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,264)">入</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97783.981641 -2498.856837) translate(0,294)">口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,84)">信</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,114)">息</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,174)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,204)">配</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,264)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,294)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97863.907766 -2489.856837) translate(0,324)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,84)">信</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,114)">息</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,144)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,174)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,204)">配</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,234)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,264)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,294)">回</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97906.509762 -2489.856837) translate(0,324)">路</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98721.123632 -2489.856837) translate(0,24)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98721.123632 -2489.856837) translate(0,54)">补</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98721.123632 -2489.856837) translate(0,84)">偿</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98721.123632 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98721.123632 -2489.856837) translate(0,144)">容</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,24)">#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,54)">风</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,114)">有</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,144)">载</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,174)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,204)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,234)">过</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,264)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98677.725337 -2489.856837) translate(0,294)">箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,24)">#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,54)">直</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,84)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,114)">充</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,144)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,174)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,204)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,234)">高</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,264)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,294)">照</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98633.928892 -2489.856837) translate(0,324)">明</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98597.104690 -2489.856837) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98597.104690 -2489.856837) translate(0,54)">食</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98597.104690 -2489.856837) translate(0,84)">堂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98597.104690 -2489.856837) translate(0,114)">餐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98597.104690 -2489.856837) translate(0,144)">厅</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.289740 -2489.856837) translate(0,24)">4</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.289740 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.289740 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.289740 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98556.289740 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98517.678492 -2489.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98517.678492 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98517.678492 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98517.678492 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98517.678492 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98473.280197 -2489.856837) translate(0,24)">6</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98473.280197 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98473.280197 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98473.280197 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98473.280197 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98431.465247 -2489.856837) translate(0,24)">8</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98431.465247 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98431.465247 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98431.465247 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98431.465247 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.048447 -2489.856837) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.048447 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.048447 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.048447 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98392.048447 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98351.427946 -2489.856837) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98351.427946 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98351.427946 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98351.427946 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98351.427946 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98313.807445 -2489.856837) translate(0,24)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98313.807445 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98313.807445 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98313.807445 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98313.807445 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98272.011000 -2489.856837) translate(0,24)">12</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98272.011000 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98272.011000 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98272.011000 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98272.011000 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98234.186798 -2489.856837) translate(0,24)">9</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98234.186798 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98234.186798 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98234.186798 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98234.186798 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98191.992204 -2489.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98191.992204 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98191.992204 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98191.992204 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98191.992204 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98148.186507 -2489.856837) translate(0,24)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98148.186507 -2489.856837) translate(0,54)">梯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98148.186507 -2489.856837) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98148.186507 -2489.856837) translate(0,114)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98105.584510 -2489.856837) translate(0,24)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98105.584510 -2489.856837) translate(0,54)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98105.584510 -2489.856837) translate(0,84)">室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98105.584510 -2489.856837) translate(0,114)">消</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98105.584510 -2489.856837) translate(0,144)">防</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,24)">夜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,54)">景</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,84)">灯  </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,114)"> </text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,144)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,174)">埋</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98066.167710 -2489.856837) translate(0,204)">灯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98028.556462 -2489.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98028.556462 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98028.556462 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98028.556462 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98028.556462 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97991.139661 -2489.856837) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97991.139661 -2489.856837) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97991.139661 -2489.856837) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97991.139661 -2489.856837) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97991.139661 -2489.856837) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97946.139516 -2489.856837) translate(0,24)">半</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97946.139516 -2489.856837) translate(0,54)">层</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97946.139516 -2489.856837) translate(0,84)">暖</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97946.139516 -2489.856837) translate(0,114)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98859.679657 -2495.433863) translate(0,24)">#2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98859.679657 -2495.433863) translate(0,54)">补</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98859.679657 -2495.433863) translate(0,84)">偿</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98859.679657 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98859.679657 -2495.433863) translate(0,144)">容</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,24)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,54)">风</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,114)">有</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,144)">载</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,174)">调</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,204)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,234)">过</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,264)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99304.014495 -2495.433863) translate(0,294)">箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99546.885688 -2495.433863) translate(0,24)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99546.885688 -2495.433863) translate(0,54)">梯</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99546.885688 -2495.433863) translate(0,84)">机</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99546.885688 -2495.433863) translate(0,114)">房</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99504.672588 -2495.433863) translate(0,24)">地</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99504.672588 -2495.433863) translate(0,54)">下</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99504.672588 -2495.433863) translate(0,84)">室</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99504.672588 -2495.433863) translate(0,114)">消</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99504.672588 -2495.433863) translate(0,144)">防</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99467.459489 -2495.433863) translate(0,24)">7</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99467.459489 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99467.459489 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99467.459489 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99467.459489 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99382.449946 -2495.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99382.449946 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99382.449946 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99382.449946 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99382.449946 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99342.449800 -2495.433863) translate(0,24)">半</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99342.449800 -2495.433863) translate(0,54)">层</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99342.449800 -2495.433863) translate(0,84)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99342.449800 -2495.433863) translate(0,114)">暖</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99342.449800 -2495.433863) translate(0,144)">通</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,84)">楚</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,114)">雄</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,144)">监</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,174)">控</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,204)">中</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,234)">心</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,264)">UPS</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,294)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99223.986446 -2495.433863) translate(0,324)">源</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99140.375052 -2495.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99140.375052 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99140.375052 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99140.375052 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99140.375052 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98978.355964 -2495.433863) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98978.355964 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98978.355964 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98978.355964 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98978.355964 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98938.513257 -2495.433863) translate(0,24)">2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98938.513257 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98938.513257 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98938.513257 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98938.513257 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98897.503859 -2495.433863) translate(0,24)">副</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98897.503859 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98897.503859 -2495.433863) translate(0,84)">餐</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99018.541306 -2495.433863) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99018.541306 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99018.541306 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99018.541306 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99018.541306 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99058.356255 -2495.433863) translate(0,24)">1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99058.356255 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99058.356255 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99058.356255 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99058.356255 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99098.161953 -2495.433863) translate(0,24)">5</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99098.161953 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99098.161953 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99098.161953 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99098.161953 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99182.967795 -2495.433863) translate(0,24)">3</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99182.967795 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99182.967795 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99182.967795 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99182.967795 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,24)">#1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,54)">直</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,84)">流</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,114)">充</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,144)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,174)">10</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,204)">kV</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,234)">高</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,264)">压</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,294)">照</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99265.792143 -2495.433863) translate(0,324)">明</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99425.459344 -2495.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99425.459344 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99425.459344 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99425.459344 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99425.459344 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99586.496936 -2495.433863) translate(0,24)">11</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99586.496936 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99586.496936 -2495.433863) translate(0,84)">强</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99586.496936 -2495.433863) translate(0,114)">电</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99586.496936 -2495.433863) translate(0,144)">井</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99628.080427 -2495.433863) translate(0,24)">副</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99628.080427 -2495.433863) translate(0,54)">楼</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99628.080427 -2495.433863) translate(0,84)">食</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99628.080427 -2495.433863) translate(0,114)">堂</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98011.398149 -3456.795419) translate(0,40)">10kV紫溪大道线6号环网柜</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98011.398149 -3456.795419) translate(0,90)">育才路联络线1号电缆分接箱供电局办公楼Ⅱ回支线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="54" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97992.622394 -2771.658923) translate(0,44)">0.4kVII段母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99135.291488 -3385.353351) translate(0,40)">线路电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98334.316915 -3387.117249) translate(0,40)">线路电压互感器</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_146dbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98684.000000 -3221.000000) translate(0,16)">10kV母线</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_146f2d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98903.000000 -3305.000000) translate(0,16)">   010</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_146f8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98622.000000 -3135.000000) translate(0,16)">  002</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_146fb20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98907.000000 -2893.000000) translate(0,16)"> 410</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_14702b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98544.000000 -2924.000000) translate(0,16)">  420</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13ae600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98905.000000 -3134.000000) translate(0,16)">  001</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13ae920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98778.000000 -2727.000000) translate(0,16)">412</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13b0420" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97936.139516 -2441.856837) translate(0,24)">(14-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13b0f20" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97981.139661 -2430.856837) translate(0,24)">(11-4)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13b1380" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98018.556462 -2430.856837) translate(0,24)">(11-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1383650" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98096.584510 -2427.856837) translate(0,24)">(-1-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1383890" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98138.556462 -2427.856837) translate(0,24)">(13-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1383ad0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98181.556462 -2427.856837) translate(0,24)">(11-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1383d10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98228.056462 -2427.856837) translate(0,24)">(9-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1383f50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98262.056462 -2427.856837) translate(0,24)">(12-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384190" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98303.556462 -2427.856837) translate(0,24)">(10-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13843d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98344.556462 -2427.856837) translate(0,24)">(2-3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384610" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98385.056462 -2427.856837) translate(0,24)">(3-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384850" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98425.056462 -2427.856837) translate(0,24)">(8-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384a90" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98467.056462 -2427.856837) translate(0,24)">(6-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384cd0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98510.556462 -2427.856837) translate(0,24)">(2-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1384f10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98549.556462 -2427.856837) translate(0,24)">(4-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385150" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98588.556462 -2427.856837) translate(0,24)">(-1-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385390" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98890.556462 -2455.856837) translate(0,24)">(1-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13855d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98931.556462 -2428.856837) translate(0,24)">(2-4)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385810" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98971.556462 -2428.856837) translate(0,24)">(2-2)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385a50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99011.556462 -2428.856837) translate(0,24)">(3-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385c90" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99051.556462 -2428.856837) translate(0,24)">(1-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1385ed0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99091.556462 -2428.856837) translate(0,24)">(5-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386110" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99130.556462 -2428.856837) translate(0,24)">(11-7)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386350" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99175.556462 -2428.856837) translate(0,24)">(3-3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386590" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99332.556462 -2428.856837) translate(0,24)">(14-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13867d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99372.556462 -2428.856837) translate(0,24)">(11-3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386a10" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99415.556462 -2428.856837) translate(0,24)">(11-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386c50" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99461.056462 -2428.856837) translate(0,24)">(7-3)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1386e90" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99495.556462 -2428.856837) translate(0,24)">(-1-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_13870d0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99536.556462 -2428.856837) translate(0,24)">(13-1)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1387310" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99621.556462 -2428.856837) translate(0,24)">(1-4)</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="30" graphid="g_1387550" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 99576.556462 -2428.856837) translate(0,24)">(11-5)</text>
   <text DF8003:Layer="0" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_138a790" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 97377.000000 -2893.000000) translate(0,12)">注：801、802以及0.4kV开关位置信号均未采集</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_13b3d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98614.000000 -3304.000000) translate(0,16)">   020</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" graphid="g_13b94e0" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98169.622394 -2344.658923) translate(0,26)">发电机接口</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="32" graphid="g_13ba940" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 98172.622394 -2269.658923) translate(0,26)">充电区低压配电箱</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13bbcb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98070.000000 -2362.000000) translate(0,15)">QF1</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13bca30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98074.000000 -2281.000000) translate(0,15)">QF2</text>
   <text DF8003:Layer="0" fill="rgb(255,255,255)" font-family="SimSun" font-size="46" graphid="g_13bcf90" transform="matrix(0.398149 -0.000000 -0.000000 0.398359 97804.622394 -2279.658923) translate(0,37)">楚雄供电局户外电缆分支箱</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98973" x2="99223" y1="-3427" y2="-3427"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98608" x2="98608" y1="-3438" y2="-3371"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98973" x2="98973" y1="-3427" y2="-3360"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98856" y1="-3184" y2="-3184"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98856" x2="98856" y1="-3184" y2="-3148"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98808" y1="-3018" y2="-3048"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98808" y1="-3060" y2="-3087"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98808" y1="-3129" y2="-3154"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98808" y1="-3166" y2="-3232"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98797" y1="-3166" y2="-3147"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98819" y1="-3166" y2="-3147"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98797" y1="-3154" y2="-3135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98819" y1="-3154" y2="-3135"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98819" y1="-3048" y2="-3067"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98797" y1="-3048" y2="-3067"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98819" y1="-3060" y2="-3079"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98797" y1="-3060" y2="-3079"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98609" y1="-2861" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98972" y1="-2829" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="98734" y1="-2746" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98747" x2="98781" y1="-2746" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98807" x2="98840" y1="-2746" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98853" x2="98833" y1="-2746" y2="-2757"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98853" x2="98833" y1="-2746" y2="-2735"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98841" x2="98821" y1="-2746" y2="-2757"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98841" x2="98821" y1="-2746" y2="-2735"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98734" x2="98753" y1="-2746" y2="-2735"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98734" x2="98753" y1="-2746" y2="-2757"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98746" x2="98765" y1="-2746" y2="-2735"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98746" x2="98765" y1="-2746" y2="-2757"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98608" x2="98619" y1="-3410" y2="-3392"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98608" x2="98597" y1="-3410" y2="-3392"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98619" x2="98597" y1="-3392" y2="-3392"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98973" x2="98984" y1="-3409" y2="-3390"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98973" x2="98962" y1="-3409" y2="-3390"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98984" x2="98962" y1="-3390" y2="-3390"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97811" y1="-2654" y2="-2629"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97811" y1="-2587" y2="-2560"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97801" y1="-2666" y2="-2647"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97823" y1="-2666" y2="-2647"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97801" y1="-2654" y2="-2635"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97823" y1="-2654" y2="-2635"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97823" y1="-2548" y2="-2567"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97801" y1="-2548" y2="-2567"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97823" y1="-2560" y2="-2579"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97801" y1="-2560" y2="-2579"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97811" y1="-2680" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97811" y1="-2803" y2="-2697"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97807" y1="-2698" y2="-2686"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97806" x2="97817" y1="-2680" y2="-2680"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97812" x2="97812" y1="-2548" y2="-2502"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97812" x2="97801" y1="-2501" y2="-2521"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97812" x2="97823" y1="-2501" y2="-2521"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98852" x2="99634" y1="-2746" y2="-2746"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97871" y1="-2802" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97871" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97870" x2="97870" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97860" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97882" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97860" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97882" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97882" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97860" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97882" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97860" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97871" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97860" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97871" x2="97882" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97860" x2="97883" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97913" y1="-2791" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97913" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97912" x2="97912" y1="-2589" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97902" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97924" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97902" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97924" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97924" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97902" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97924" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97902" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97913" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97903" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="97924" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97903" x2="97924" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97952" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97952" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97952" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97941" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97963" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97941" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97963" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97963" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97941" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97963" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97941" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97952" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97942" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97952" x2="97963" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97942" x2="97964" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97994" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97994" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97994" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97983" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="98005" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97983" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="98005" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="98005" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97983" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="98005" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97983" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97994" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="97983" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97994" x2="98004" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97983" x2="98005" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98031" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98031" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98031" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98020" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98042" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98020" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98042" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98042" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98020" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98042" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98020" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98031" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98020" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98031" x2="98041" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98020" x2="98042" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98073" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98073" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98073" y1="-2589" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98062" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98084" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98062" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98084" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98084" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98062" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98084" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98062" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98073" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98063" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98073" x2="98084" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98063" x2="98085" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98112" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98112" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98112" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98101" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98123" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98101" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98123" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98123" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98101" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98123" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98101" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98112" x2="98112" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98113" x2="98102" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98113" x2="98124" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98102" x2="98124" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98154" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98154" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98154" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98143" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98165" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98143" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98165" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98165" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98143" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98165" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98154" x2="98143" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98155" x2="98155" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98155" x2="98144" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98155" x2="98165" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98144" x2="98166" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98196" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98196" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98195" x2="98195" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98185" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98206" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98185" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98206" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98206" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98185" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98206" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98184" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98196" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98185" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98196" x2="98207" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98185" x2="98207" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98238" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98237" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98237" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98227" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98249" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98227" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98249" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98249" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98227" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98249" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98237" x2="98227" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98238" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98227" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98238" x2="98249" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98227" x2="98249" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98277" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98277" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98276" x2="98276" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98266" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98288" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98266" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98288" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98288" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98266" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98288" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98266" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98277" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98266" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98277" x2="98288" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98266" x2="98288" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98319" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98319" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98318" x2="98318" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98308" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98329" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98308" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98330" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98330" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98308" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98329" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98308" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98319" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98308" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98319" x2="98330" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98308" x2="98331" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98356" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98356" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98355" x2="98355" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98345" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98366" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98345" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98366" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98366" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98345" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98366" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98345" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98356" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98345" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98356" x2="98367" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98345" x2="98368" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98398" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98398" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98397" x2="98397" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98387" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98409" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98387" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98409" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98409" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98387" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98409" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98387" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98398" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98387" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98398" x2="98409" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98387" x2="98409" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98437" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98437" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98436" x2="98436" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98426" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98448" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98426" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98448" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98448" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98426" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98448" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98426" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98437" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98426" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98437" x2="98448" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98426" x2="98448" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98479" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98479" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98478" x2="98478" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98468" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98490" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98468" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98490" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98490" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98468" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98490" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98468" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98479" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98468" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98479" x2="98490" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98468" x2="98491" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98522" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98522" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98522" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98511" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98533" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98511" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98533" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98533" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98511" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98533" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98511" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98522" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98512" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98522" x2="98533" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98512" x2="98534" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98559" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98559" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98559" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98548" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98570" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98548" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98570" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98570" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98548" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98570" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98548" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98559" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98549" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98559" x2="98570" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98549" x2="98571" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98601" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98601" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98600" x2="98600" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98590" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98612" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98590" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98612" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98612" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98590" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98612" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98590" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98601" x2="98601" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98602" x2="98591" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98602" x2="98612" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98591" x2="98613" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98640" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98640" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98640" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98629" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98651" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98629" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98651" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98651" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98629" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98651" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98629" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98640" x2="98640" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98641" x2="98630" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98641" x2="98651" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98630" x2="98652" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98682" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98682" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98682" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98671" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98693" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98671" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98693" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98693" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98671" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98693" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98682" x2="98671" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98683" x2="98683" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98683" x2="98672" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98683" x2="98694" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98672" x2="98694" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98727" x2="98727" y1="-2679" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98727" x2="98727" y1="-2745" y2="-2697"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98727" x2="98723" y1="-2697" y2="-2685"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98722" x2="98733" y1="-2679" y2="-2679"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98728" x2="98717" y1="-2501" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98728" x2="98739" y1="-2501" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98717" x2="98739" y1="-2520" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97913" x2="98972" y1="-2791" y2="-2791"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="98609" y1="-2803" y2="-2803"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98903" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98903" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98903" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98892" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98914" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98892" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98914" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98914" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98892" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98914" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98892" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98903" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98892" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98903" x2="98914" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98892" x2="98914" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98945" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98945" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98945" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98935" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98956" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98935" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98956" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98956" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98935" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98956" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98945" x2="98935" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98946" x2="98946" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98946" x2="98935" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98946" x2="98957" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98935" x2="98957" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98982" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98982" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98982" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98972" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98993" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98972" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98993" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98993" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98972" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98993" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98982" x2="98972" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98983" x2="98983" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98983" x2="98972" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98983" x2="98994" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98994" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99025" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99025" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99024" x2="99024" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99013" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99035" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99013" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99035" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99035" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99013" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99035" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99013" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99025" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99014" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99025" x2="99036" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99014" x2="99036" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99064" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99064" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99063" x2="99063" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99052" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99074" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99053" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99074" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99074" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99053" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99074" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99052" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99064" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99053" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99064" x2="99075" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99053" x2="99075" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99105" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99105" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99105" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99095" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99116" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99095" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99116" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99116" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99095" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99116" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99105" x2="99095" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99106" x2="99106" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99106" x2="99095" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99106" x2="99117" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99095" x2="99117" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99147" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99147" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99146" x2="99146" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99136" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99158" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99136" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99158" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99158" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99136" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99157" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99136" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99147" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99136" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99147" x2="99158" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99136" x2="99159" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99189" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99189" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99189" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99178" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99200" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99178" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99200" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99200" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99178" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99200" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99178" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99189" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99179" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99189" x2="99200" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99179" x2="99200" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99228" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99228" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99228" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99217" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99239" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99217" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99239" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99239" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99217" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99239" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99217" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99228" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99218" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99228" x2="99239" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99218" x2="99240" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99270" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99270" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99270" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99259" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99281" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99259" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99281" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99281" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99259" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99281" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99259" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99270" x2="99270" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99271" x2="99259" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99271" x2="99281" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99259" x2="99282" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99307" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99307" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99306" x2="99306" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99296" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99318" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99296" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99318" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99318" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99296" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99318" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99296" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99307" x2="99307" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99308" x2="99296" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99308" x2="99318" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99296" x2="99319" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99349" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99349" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99349" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99338" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99360" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99338" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99360" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99360" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99338" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99360" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99338" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99349" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99339" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99349" x2="99360" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99339" x2="99361" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99388" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99388" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99388" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99377" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99399" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99377" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99399" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99399" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99377" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99399" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99377" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99388" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99378" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99388" x2="99400" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99378" x2="99400" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99430" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99430" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99430" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99419" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99441" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99419" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99441" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99441" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99419" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99441" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99419" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99430" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99419" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99430" x2="99441" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99419" x2="99441" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99473" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99473" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99473" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99462" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99484" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99462" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99484" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99484" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99462" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99484" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99473" x2="99462" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99474" x2="99474" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99474" x2="99463" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99474" x2="99485" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99463" x2="99485" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99510" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99510" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99510" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99500" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99521" y1="-2668" y2="-2649"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99500" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99521" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99521" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99500" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99521" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99510" x2="99500" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99511" x2="99511" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99511" x2="99500" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99511" x2="99522" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99500" x2="99522" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99552" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99552" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99552" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99541" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99563" y1="-2668" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99542" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99563" y1="-2656" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99563" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99541" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99563" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99541" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99552" y1="-2549" y2="-2504"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99541" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99552" x2="99563" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99541" x2="99563" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99591" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99591" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99591" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99581" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99602" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99581" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99602" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99602" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99581" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99602" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99591" x2="99581" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99592" x2="99592" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99592" x2="99581" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99592" x2="99603" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99581" x2="99603" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99634" y1="-2746" y2="-2666"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99634" y1="-2655" y2="-2630"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99633" x2="99633" y1="-2588" y2="-2562"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99623" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99644" y1="-2667" y2="-2648"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99623" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99644" y1="-2655" y2="-2636"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99644" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99623" y1="-2549" y2="-2568"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99633" x2="99644" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99633" x2="99623" y1="-2561" y2="-2580"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99634" y1="-2549" y2="-2503"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99623" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99634" x2="99645" y1="-2503" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="99623" x2="99645" y1="-2522" y2="-2522"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98866" x2="98866" y1="-2679" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98866" x2="98866" y1="-2745" y2="-2697"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98866" x2="98861" y1="-2697" y2="-2685"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98860" x2="98872" y1="-2679" y2="-2679"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98866" x2="98856" y1="-2501" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98866" x2="98878" y1="-2501" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98856" x2="98878" y1="-2520" y2="-2520"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98808" x2="98808" y1="-3130" y2="-3086"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98480" x2="98480" y1="-3367" y2="-3395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98513" x2="98513" y1="-3367" y2="-3395"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99104" x2="99104" y1="-3390" y2="-3362"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="99071" x2="99071" y1="-3390" y2="-3362"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98219" x2="98608" y1="-3438" y2="-3438"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98534" x2="98608" y1="-3380" y2="-3380"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="98973" x2="99050" y1="-3377" y2="-3377"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98609" y1="-2959" y2="-2934"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98609" y1="-2892" y2="-2865"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98599" y1="-2971" y2="-2952"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98621" y1="-2971" y2="-2952"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98599" y1="-2959" y2="-2940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98621" y1="-2959" y2="-2940"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98621" y1="-2853" y2="-2872"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98599" y1="-2853" y2="-2872"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98621" y1="-2865" y2="-2884"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98599" y1="-2865" y2="-2884"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98609" x2="98609" y1="-3006" y2="-2971"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98972" y1="-2934" y2="-2909"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98972" y1="-2867" y2="-2840"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98962" y1="-2946" y2="-2927"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98984" y1="-2946" y2="-2927"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98962" y1="-2934" y2="-2915"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98984" y1="-2934" y2="-2915"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98984" y1="-2828" y2="-2847"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98962" y1="-2828" y2="-2847"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98984" y1="-2840" y2="-2859"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98962" y1="-2840" y2="-2859"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98972" x2="98972" y1="-3004" y2="-2946"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="97811" x2="97811" y1="-2503" y2="-2298"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98016" x2="97811" y1="-2299" y2="-2299"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98016" x2="98016" y1="-2335" y2="-2253"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98076" x2="98016" y1="-2334" y2="-2334"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98076" x2="98016" y1="-2254" y2="-2254"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98162" x2="98102" y1="-2334" y2="-2334"/>
   <line DF8003:Layer="0" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="98162" x2="98102" y1="-2254" y2="-2254"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="0" id="BS-CX_PDS.CX_PDS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="0" fill="none" points="98599,-3234 98982,-3234 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="28786" ObjectName="BS-CX_PDS.CX_PDS_9IM"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   <polyline fill="none" opacity="0" points="98599,-3234 98982,-3234 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="0" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 97474.500000 -3479.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188939" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99591.000000 -2863.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188939" ObjectName="CX_PDS:CX_PDS_GG_Ua1_43"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188940" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99591.000000 -2846.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188940" ObjectName="CX_PDS:CX_PDS_GG_Ub1_44"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188941" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99591.000000 -2830.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188941" ObjectName="CX_PDS:CX_PDS_GG_Uc1_45"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188945" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99591.000000 -2814.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188945" ObjectName="CX_PDS:CX_PDS_GG_U01_49"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188942" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99591.000000 -2799.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188942" ObjectName="CX_PDS:CX_PDS_GG_Uab1_46"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188947" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98078.000000 -2877.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188947" ObjectName="CX_PDS:CX_PDS_GG_Ub2_51"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188948" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98078.000000 -2861.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188948" ObjectName="CX_PDS:CX_PDS_GG_Uc2_52"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188952" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98078.000000 -2845.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188952" ObjectName="CX_PDS:CX_PDS_GG_U02_56"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188949" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98078.000000 -2830.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188949" ObjectName="CX_PDS:CX_PDS_GG_Uab2_53"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="0" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-188946" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98078.000000 -2894.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188946" ObjectName="CX_PDS:CX_PDS_GG_Ua2_50"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="0" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="230" x="97462" y="-3538"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="230" x="97462" y="-3538"/></g>
   <g DF8003:Layer="0" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="97403" y="-3555"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="97403" y="-3555"/></g>
  </g><g id="Circle_Layer">
   <ellipse DF8003:Layer="0" cx="98513" cy="-3381" fill="none" rx="20.5" ry="21" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <circle DF8003:Layer="0" cx="98479" cy="-3380" fill="none" r="21.5" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <ellipse DF8003:Layer="0" cx="99070" cy="-3376" fill="none" rx="20.5" ry="21" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
   <circle DF8003:Layer="0" cx="99104" cy="-3376" fill="none" r="22" stroke="rgb(0,255,0)" stroke-width="0.398149"/>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="0" id="g_143ca10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98849.000000 -3094.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="230" x="97462" y="-3538"/></g>
   <g href="cx_配调_配网接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="97403" y="-3555"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="0" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98772.000000 -2736.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188968">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98598.000000 -3247.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28791" ObjectName="SW-CX_PDS.CX_PDS_Z463BK"/>
     <cge:Meas_Ref ObjectId="188968"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188995">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98598.000000 -3077.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28789" ObjectName="SW-CX_PDS.CX_PDS_412BK"/>
     <cge:Meas_Ref ObjectId="188995"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188956">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98963.000000 -3247.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28787" ObjectName="SW-CX_PDS.CX_PDS_Z462BK"/>
     <cge:Meas_Ref ObjectId="188956"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" id="SW-188981">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 98963.000000 -3076.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="28788" ObjectName="SW-CX_PDS.CX_PDS_411BK"/>
     <cge:Meas_Ref ObjectId="188981"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188905" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98546.000000 -3333.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188905" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188906" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98546.000000 -3333.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188906" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98546.000000 -3333.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188907" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98546.000000 -3333.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188907" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28791"/>
     <cge:Term_Ref ObjectID="41014"/>
    <cge:TPSR_Ref TObjectID="28791"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99057.000000 -3320.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188900" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99057.000000 -3320.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188900" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99057.000000 -3320.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188901" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99057.000000 -3320.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188901" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28787"/>
     <cge:Term_Ref ObjectID="41006"/>
    <cge:TPSR_Ref TObjectID="28787"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188917" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98557.000000 -3154.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188917" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188918" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98557.000000 -3154.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188918" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98557.000000 -3154.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188919" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98557.000000 -3154.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188919" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28789"/>
     <cge:Term_Ref ObjectID="41010"/>
    <cge:TPSR_Ref TObjectID="28789"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188911" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99066.000000 -3152.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188911" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188912" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99066.000000 -3152.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188912" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188908" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99066.000000 -3152.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188908" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-188913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99066.000000 -3152.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28788"/>
     <cge:Term_Ref ObjectID="41008"/>
    <cge:TPSR_Ref TObjectID="28788"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-188932" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98799.000000 -3317.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188932" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-188933" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98799.000000 -3317.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188933" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-188934" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98799.000000 -3317.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188934" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-188938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98799.000000 -3317.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-188935" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98799.000000 -3317.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188935" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28786"/>
     <cge:Term_Ref ObjectID="41005"/>
    <cge:TPSR_Ref TObjectID="28786"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188929" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98717.000000 -2922.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188929" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188930" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98717.000000 -2922.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188930" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188926" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98717.000000 -2922.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188926" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188927" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98717.000000 -2922.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188927" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188928" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 98717.000000 -2922.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188928" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28793"/>
     <cge:Term_Ref ObjectID="41018"/>
    <cge:TPSR_Ref TObjectID="28793"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-188923" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99065.000000 -2920.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188923" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-188924" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99065.000000 -2920.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188924" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-188920" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99065.000000 -2920.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188920" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ib" PreSymbol="0" appendix="" decimal="2" id="ME-188921" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99065.000000 -2920.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188921" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
   <g DF8003:Layer="0" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-188922" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 99065.000000 -2920.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="188922" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="28792"/>
     <cge:Term_Ref ObjectID="41016"/>
    <cge:TPSR_Ref TObjectID="28792"/></metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="0:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer10="4:0.000000 0.000000" layer11="GDXT:0.000000 0.000000" layer12="Defpoints:0.000000 0.000000" layer13="主干线:0.000000 0.000000" layer14="设备（实线）:0.000000 0.000000" layer15="标注线层:0.000000 0.000000" layer16="0:0.000000 0.000000" layer17="$AUDIT-BAD-LAYER:0.000000 0.000000" layer18="图层2:0.000000 0.000000" layer19="SX:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer20="CSX:0.000000 0.000000" layer21="文字:0.000000 0.000000" layer22="本期工程:0.000000 0.000000" layer23="4:0.000000 0.000000" layer24="GDXT:0.000000 0.000000" layer25="Defpoints:0.000000 0.000000" layer26="主干线:0.000000 0.000000" layer27="设备（实线）:0.000000 0.000000" layer28="标注线层:0.000000 0.000000" layer29="PUBLIC:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layer4="$AUDIT-BAD-LAYER:0.000000 0.000000" layer5="图层2:0.000000 0.000000" layer6="SX:0.000000 0.000000" layer7="CSX:0.000000 0.000000" layer8="文字:0.000000 0.000000" layer9="本期工程:0.000000 0.000000" layerN="30" moveAndZoomFlag="1" stationName="CX_PDS"/>
</svg>