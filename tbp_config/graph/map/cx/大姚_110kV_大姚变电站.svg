<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-26" aopId="3934726" id="thSvg" product="E8000V2" version="1.0" viewBox="3077 -1220 2205 1277">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="15" y2="4"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape14">
    <polyline arcFlag="1" points="27,100 25,100 23,99 22,99 20,98 19,97 17,96 16,94 15,92 15,91 14,89 14,87 14,85 15,83 15,82 16,80 17,79 19,77 20,76 22,75 23,75 25,74 27,74 29,74 31,75 32,75 34,76 35,77 37,79 38,80 39,82 39,83 40,85 40,87 " stroke-width="0.0972"/>
    <rect height="23" stroke-width="0.945274" width="11" x="41" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="47" x2="45" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.945274" x1="49" x2="47" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07143" x1="47" x2="47" y1="54" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="47" x2="47" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="54" x2="40" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.428414" x1="50" x2="44" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.309343" x1="49" x2="45" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="40" x2="28" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="27" x2="27" y1="99" y2="107"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="12" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="11" y1="54" y2="47"/>
    <polyline arcFlag="1" points="11,15 10,15 9,15 9,15 8,15 8,16 7,16 7,17 6,17 6,18 6,18 6,19 5,20 5,20 5,21 6,22 6,22 6,23 6,23 7,24 7,24 8,25 8,25 9,25 9,26 10,26 11,26 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,25 10,25 9,25 9,25 8,26 8,26 7,26 7,27 6,27 6,28 6,29 6,29 5,30 5,31 5,31 6,32 6,33 6,33 6,34 7,34 7,35 8,35 8,35 9,36 9,36 10,36 11,36 " stroke-width="1"/>
    <polyline arcFlag="1" points="11,36 10,36 9,36 9,37 8,37 8,37 7,38 7,38 6,39 6,39 6,40 6,40 5,41 5,42 5,42 6,43 6,44 6,44 6,45 7,45 7,46 8,46 8,47 9,47 9,47 10,47 11,47 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="28" x2="28" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="45" x2="12" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="28" x2="13" y1="8" y2="8"/>
    <rect height="23" stroke-width="0.398039" width="12" x="22" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="46" x2="12" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="46" x2="46" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="11" x2="11" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="28" x2="28" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="38" x2="18" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="2" x2="2" y1="45" y2="16"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="0" x2="12" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="13" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="20" y2="20"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="13" x2="4" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape27">
    <polyline points="22,19 34,19 34,6 32,7 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="37" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="33" x2="36" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="30" x2="39" y1="6" y2="6"/>
    <ellipse cx="8" cy="14" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="18" cy="19" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="25" rx="7.5" ry="7" stroke-width="0.726474"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="6" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="9" x2="9" y1="26" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="24" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="18" y1="20" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="18" y1="17" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="17" y1="19" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="12" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="12" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape70">
    <circle cx="43" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="29" x2="32" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="27" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="31" x2="31" y1="8" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="37" x2="25" y1="8" y2="8"/>
    <polyline arcFlag="1" points="57,22 58,22 58,22 58,22 59,23 59,23 60,24 60,24 60,25 61,25 61,26 61,27 61,27 61,28 61,28 61,29 61,30 60,30 60,31 60,31 59,32 59,32 58,33 58,33 58,33 57,33 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="98" y2="113"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="45" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="56" x2="56" y1="22" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.429293" x1="57" x2="57" y1="48" y2="43"/>
    <polyline arcFlag="1" points="57,32 58,32 58,32 58,32 59,33 59,33 60,34 60,34 60,35 61,35 61,36 61,37 61,37 61,38 61,38 61,39 61,40 60,40 60,41 60,41 59,42 59,42 58,43 58,43 58,43 57,43 " stroke-width="0.0290179"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="98" y2="57"/>
    <rect height="29" stroke-width="0.416667" width="14" x="28" y="62"/>
    <circle cx="43" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="35" y1="98" y2="98"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="97" y2="54"/>
    <rect height="29" stroke-width="0.416667" width="14" x="0" y="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="55" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="51" x2="57" y1="48" y2="48"/>
    <circle cx="34" cy="47" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="34" cy="36" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape103">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="23" x2="22" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.84362" x1="1" x2="22" y1="46" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="22" x2="15" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="10" x2="10" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675211" x1="11" x2="11" y1="5" y2="16"/>
    <polyline arcFlag="1" points="11,28 11,28 11,28 11,28 12,28 12,28 12,27 12,27 13,27 13,27 13,26 13,26 13,26 13,25 13,25 13,24 13,24 13,24 13,23 12,23 12,23 12,23 12,22 11,22 11,22 11,22 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="10,34 11,34 11,34 11,34 11,34 12,34 12,34 12,33 12,33 12,33 12,32 13,32 13,32 13,31 13,31 13,31 12,30 12,30 12,30 12,29 12,29 12,29 11,29 11,28 11,28 11,28 " stroke-width="0.0340106"/>
    <polyline arcFlag="1" points="11,22 11,22 11,22 11,22 12,22 12,21 12,21 12,21 13,21 13,20 13,20 13,20 13,19 13,19 13,19 13,18 13,18 13,17 13,17 12,17 12,17 12,16 12,16 11,16 11,16 11,16 " stroke-width="0.0340106"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="9" x2="12" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="8" x2="13" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="4" x2="17" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape36">
    <rect height="19" stroke-width="0.75" width="8" x="4" y="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.278409" x1="6" x2="9" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="5" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="1" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.351519" x1="8" x2="8" y1="21" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="7" x2="7" y1="46" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="6" x2="7" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="8" x2="9" y1="27" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape141">
    <polyline DF8003:Layer="PUBLIC" points="18,1 18,16 30,8 18,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="4" x2="84" y1="9" y2="9"/>
    <polyline DF8003:Layer="PUBLIC" points="72,1 72,16 60,8 72,1 "/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer:shape16_0">
    <ellipse cx="70" cy="46" fillStyle="0" rx="26.5" ry="26" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="0" x2="71" y1="29" y2="100"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="73" y1="47" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="87" x2="80" y1="54" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="80" x2="80" y1="38" y2="47"/>
   </symbol>
   <symbol id="transformer:shape16_1">
    <ellipse cx="41" cy="61" fillStyle="0" rx="26" ry="26.5" stroke-width="0.540424"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="34" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="49" x2="41" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="41" x2="41" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer:shape16-2">
    <circle cx="41" cy="30" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="31" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="40" x2="49" y1="32" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="31" x2="49" y1="16" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="voltageTransformer:shape6">
    <circle cx="18" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="11" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="18" cy="7" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape9">
    <circle cx="14" cy="19" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="20" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="14" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape14">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="3" x2="7" y1="3" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="6,19 26,19 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="2" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="6" y2="4"/>
    <circle cx="30" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="39" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="30" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="37" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="40" x2="40" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="42" x2="39" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="28" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="21" x2="24" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="21" x2="18" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="18" x2="24" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="31" x2="31" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="33" x2="30" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.37768" x1="0" x2="12" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251787" x1="6" x2="6" y1="19" y2="9"/>
   </symbol>
   <symbol id="voltageTransformer:shape22">
    <polyline points="41,18 8,18 8,43 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="5" x2="11" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.789474" x1="0" x2="15" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.618687" x1="6" x2="10" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="38" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.17171" x1="22" x2="31" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21568" x1="27" x2="22" y1="20" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.21447" x1="27" x2="31" y1="20" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="42" x2="42" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="15" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="61" x2="57" y1="22" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.32675" x1="57" x2="57" y1="26" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="52" x2="56" y1="22" y2="26"/>
    <ellipse cx="27" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="42" cy="35" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <ellipse cx="55" cy="26" fillStyle="0" rx="9.5" ry="11" stroke-width="0.695459"/>
    <ellipse cx="41" cy="20" fillStyle="0" rx="10" ry="11" stroke-width="0.695459"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="37" x2="42" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3267" x1="47" x2="42" y1="35" y2="38"/>
   </symbol>
   <symbol id="voltageTransformer:shape36">
    <ellipse cx="19" cy="20" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="8" cy="19" rx="7.5" ry="7" stroke-width="0.726474"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="6.5" stroke-width="0.726474"/>
    <polyline points="19,9 35,9 35,22 " stroke-width="0.8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="32" x2="38" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.125" x1="34" x2="36" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.05263" x1="31" x2="39" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="8" x2="8" y1="8" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="9" x2="11" y1="18" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="7" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="5" x2="11" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="19" x2="19" y1="21" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="19" y1="19" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="16" x2="19" y1="19" y2="21"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31101f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3110b90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3111560" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3112200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3113460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3114080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3114ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31157f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3116090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="55" stroke="rgb(255,0,0)" stroke-width="9.28571" width="98" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3116980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3116980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 52.000000) translate(0,35)">二种工作</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3118930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3118930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3119bd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311b7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311cc70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_311d550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311ec60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_311f430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3120250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3120a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3121a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31223a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3122e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3117810" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3125aa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">测试</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3126420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3135df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3127ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_312b290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31253b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1287" width="2215" x="3072" y="-1225"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e09890" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3615.000000 1159.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0a9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 1144.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0b7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3629.000000 1129.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0bec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3847.000000 1159.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3836.000000 1144.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0c3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3861.000000 1129.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0c7f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4017.000000 845.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0cab0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4006.000000 830.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0ccf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 815.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0d110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 1159.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0d3d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4242.000000 1144.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0d610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4267.000000 1129.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0da30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4536.000000 1159.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0dcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 1144.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0df30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4550.000000 1129.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0e350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3898.000000 783.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0e610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 768.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0e850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3912.000000 753.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0ec70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 792.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0ef30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4225.000000 777.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0f170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4250.000000 762.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0f590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3701.000000 501.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0f850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 486.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0fa90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3715.000000 471.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e0feb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.000000 489.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e10170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 474.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e103b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4257.000000 459.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e107d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3988.000000 265.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e10a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 250.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e10cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4002.000000 235.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e110f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4103.000000 347.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e113b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4092.000000 332.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e115f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 317.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e11a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4516.000000 616.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e11cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 601.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e11f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4530.000000 586.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e12330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4026.000000 617.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e125f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4015.000000 602.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e12830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 587.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e12c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3680.000000 46.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e12f50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3705.000000 31.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e13370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3788.000000 14.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e13670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3813.000000 -1.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e13a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -24.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e13d90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 -39.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e141b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4459.000000 -24.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e144b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4484.000000 -39.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f681c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 827.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f68d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 812.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f69320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3450.000000 797.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f69570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 781.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f697b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3444.000000 841.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f699f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3452.000000 765.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6a6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 943.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6a960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 928.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6aba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 913.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6ade0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4360.000000 897.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6b020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4368.000000 957.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6b260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4376.000000 881.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6b590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 478.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6b810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 463.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6ba50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4609.000000 448.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6bc90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 432.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6bed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4603.000000 492.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6c110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4611.000000 416.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6c440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 502.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6c6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 487.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6c900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3474.000000 472.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6cb40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3460.000000 456.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6cd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3468.000000 516.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6cfc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 440.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6d2f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 1120.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6d570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 1105.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6d7b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4817.000000 1090.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6d9f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 1074.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6dc30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4811.000000 1134.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6de70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.000000 1058.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 168.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 153.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4801.000000 138.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6e8a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 122.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6eae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4795.000000 182.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6ed20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4803.000000 106.000000) translate(0,12)">F(Hz):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f6f050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 573.000000) translate(0,12)">绕组温度(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f702a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 603.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f70b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3888.000000 588.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f71210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4178.000000 690.000000) translate(0,12)">绕组温度(℃):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f71440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 720.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f71680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 705.000000) translate(0,12)">油温(℃):</text>
   <metadata/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-26562">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4038.000000 -917.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4271" ObjectName="SW-CX_DY.CX_DY_112BK"/>
     <cge:Meas_Ref ObjectId="26562"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26183">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4216" ObjectName="SW-CX_DY.CX_DY_151BK"/>
     <cge:Meas_Ref ObjectId="26183"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26202">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4222" ObjectName="SW-CX_DY.CX_DY_152BK"/>
     <cge:Meas_Ref ObjectId="26202"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26255">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 -927.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4238" ObjectName="SW-CX_DY.CX_DY_154BK"/>
     <cge:Meas_Ref ObjectId="26255"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26286">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -737.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4244" ObjectName="SW-CX_DY.CX_DY_101BK"/>
     <cge:Meas_Ref ObjectId="26286"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26422">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -738.239382)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4257" ObjectName="SW-CX_DY.CX_DY_102BK"/>
     <cge:Meas_Ref ObjectId="26422"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -302.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4293" ObjectName="SW-CX_DY.CX_DY_053BK"/>
     <cge:Meas_Ref ObjectId="26641"/>
    <cge:TPSR_Ref TObjectID="4293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -302.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4282" ObjectName="SW-CX_DY.CX_DY_052BK"/>
     <cge:Meas_Ref ObjectId="26608"/>
    <cge:TPSR_Ref TObjectID="4282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26570">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4276" ObjectName="SW-CX_DY.CX_DY_051BK"/>
     <cge:Meas_Ref ObjectId="26570"/>
    <cge:TPSR_Ref TObjectID="4276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -84.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26301">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -459.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4254" ObjectName="SW-CX_DY.CX_DY_001BK"/>
     <cge:Meas_Ref ObjectId="26301"/>
    <cge:TPSR_Ref TObjectID="4254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26623">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -310.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4286" ObjectName="SW-CX_DY.CX_DY_062BK"/>
     <cge:Meas_Ref ObjectId="26623"/>
    <cge:TPSR_Ref TObjectID="4286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -455.239382)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4268" ObjectName="SW-CX_DY.CX_DY_002BK"/>
     <cge:Meas_Ref ObjectId="26441"/>
    <cge:TPSR_Ref TObjectID="4268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26435">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -616.239382)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4264" ObjectName="SW-CX_DY.CX_DY_302BK"/>
     <cge:Meas_Ref ObjectId="26435"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 -620.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4250" ObjectName="SW-CX_DY.CX_DY_301BK"/>
     <cge:Meas_Ref ObjectId="26296"/>
    <cge:TPSR_Ref TObjectID="4250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4942.000000 -362.345005)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10381" ObjectName="SW-CX_DY.CX_DY_346BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10381"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4951.000000 -993.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10384" ObjectName="SW-CX_DY.CX_DY_343BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10384"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4951.000000 -795.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10383" ObjectName="SW-CX_DY.CX_DY_341BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10383"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4944.000000 -617.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10382" ObjectName="SW-CX_DY.CX_DY_342BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10382"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4946.000000 -243.746531)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10380" ObjectName="SW-CX_DY.CX_DY_345BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10380"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4624.000000 -520.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10399" ObjectName="SW-CX_DY.CX_DY_344BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10399"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26589">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -304.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4279" ObjectName="SW-CX_DY.CX_DY_061BK"/>
     <cge:Meas_Ref ObjectId="26589"/>
    <cge:TPSR_Ref TObjectID="4279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26656">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -311.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4297" ObjectName="SW-CX_DY.CX_DY_063BK"/>
     <cge:Meas_Ref ObjectId="26656"/>
    <cge:TPSR_Ref TObjectID="4297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26236">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -928.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4232" ObjectName="SW-CX_DY.CX_DY_153BK"/>
     <cge:Meas_Ref ObjectId="26236"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DY.CX_DY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6272"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 3759.000000 -581.050757)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="6274"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 3759.000000 -581.050757)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="6276"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 3759.000000 -581.050757)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4313" ObjectName="TF-CX_DY.CX_DY_1T"/>
    <cge:TPSR_Ref TObjectID="4313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DY.CX_DY_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="6265"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 4306.000000 -577.050757)" xlink:href="#transformer:shape16_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="6267"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 4306.000000 -577.050757)" xlink:href="#transformer:shape16_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="6269"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.019802 -0.000000 0.000000 -1.049320 4306.000000 -577.050757)" xlink:href="#transformer:shape16-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="4312" ObjectName="TF-CX_DY.CX_DY_2T"/>
    <cge:TPSR_Ref TObjectID="4312"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3536,-855 4033,-855 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4212" ObjectName="BS-CX_DY.CX_DY_1IM"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   <polyline fill="none" opacity="0" points="3536,-855 4033,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3522,-404 4042,-404 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4210" ObjectName="BS-CX_DY.CX_DY_9IM"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   <polyline fill="none" opacity="0" points="3522,-404 4042,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-404 4601,-404 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4211" ObjectName="BS-CX_DY.CX_DY_9IIM"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   <polyline fill="none" opacity="0" points="4155,-404 4601,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4095,-855 4727,-855 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4213" ObjectName="BS-CX_DY.CX_DY_1IIM"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   <polyline fill="none" opacity="0" points="4095,-855 4727,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3948,-36 4038,-36 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3948,-36 4038,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4164,-35 4257,-35 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4164,-35 4257,-35 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-200 4835,-432 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4215" ObjectName="BS-CX_DY.CX_DY_3IIM"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   <polyline fill="none" opacity="0" points="4835,-200 4835,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DY.CX_DY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-471 4835,-1015 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="4214" ObjectName="BS-CX_DY.CX_DY_3IM"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   <polyline fill="none" opacity="0" points="4835,-471 4835,-1015 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_DY.CX_DY_Cb3">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3721.000000 -82.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15565" ObjectName="CB-CX_DY.CX_DY_Cb3"/>
    <cge:TPSR_Ref TObjectID="15565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DY.CX_DY_Cb1">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3830.000000 -82.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15563" ObjectName="CB-CX_DY.CX_DY_Cb1"/>
    <cge:TPSR_Ref TObjectID="15563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DY.CX_DY_Cb2">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4297.000000 -35.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15564" ObjectName="CB-CX_DY.CX_DY_Cb2"/>
    <cge:TPSR_Ref TObjectID="15564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_DY.CX_DY_Cb4">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4415.000000 -36.000000)" xlink:href="#capacitor:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15566" ObjectName="CB-CX_DY.CX_DY_Cb4"/>
    <cge:TPSR_Ref TObjectID="15566"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DY.CX_DY_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17063"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 3643.000000 -147.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.897959 3643.000000 -147.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12183" ObjectName="TF-CX_DY.CX_DY_1TZyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_296ba30">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3701.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ac810">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3730.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29cbeb0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3931.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29ccab0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3960.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c7000">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 3630.500000 -708.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2860d00">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4619.500000 -1102.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29032c0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4648.000000 -1065.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29775f0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4338.500000 -1103.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2978540">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4367.000000 -1066.000000)" xlink:href="#lightningRod:shape27"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291c3f0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3749.000000 -587.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291cfa0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.000000 -590.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29574d0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4292.000000 -588.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2958080">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -600.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2887fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -299.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2878cb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4126.000000 -431.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2853d70">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4558.500000 -715.739382)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2854a10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4619.000000 -713.239382)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2855030">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4548.000000 -679.239382)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283c7a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4061.500000 -717.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_283d440">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4054.000000 -681.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28408c0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4505.000000 -506.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28441e0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4554.500000 -477.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a208a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5176.000000 -553.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2a8a0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5165.500000 -966.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a2b2e0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5222.000000 -969.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28bfef0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5104.500000 -768.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c06d0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5150.000000 -771.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c7160">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5036.500000 -566.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28c7940">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5084.000000 -569.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ca710">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5218.500000 -580.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28caef0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5264.000000 -583.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28cbc30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5116.000000 -934.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b22b80">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5160.500000 -216.246531)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b235c0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5217.000000 -218.746531)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b29170">
    <use class="BV-35KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4750.000000 -924.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b29f20">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4742.500000 -969.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2d6e0">
    <use class="BV-110KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4691.500000 -707.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dca6d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3651.000000 -235.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dcd4f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -201.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dcf450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4212.000000 -187.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ddafe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 -235.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df0ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4437.000000 -236.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df52b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 -174.000000)" xlink:href="#lightningRod:shape70"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df91c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4022.000000 -495.000000)" xlink:href="#lightningRod:shape103"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e43090">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4281.500000 -576.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e47150">
    <use class="BV-10KV" transform="matrix(1.466667 -0.000000 0.000000 -1.060000 3902.000000 -138.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4aa30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3560.000000 -227.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4b510">
    <use class="BV-10KV" transform="matrix(1.466667 -0.000000 0.000000 -1.060000 3594.000000 -246.000000)" xlink:href="#lightningRod:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4fb50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5059.000000 -740.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e514e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5120.000000 -181.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e529b0">
    <use class="BV-35KV" transform="matrix(0.943820 -0.000000 0.000000 -0.764706 5099.000000 -226.000000)" xlink:href="#lightningRod:shape141"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26102" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -588.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26102" ObjectName="CX_DY:CX_DY_1T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -573.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-26111" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -705.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26111" ObjectName="CX_DY:CX_DY_2T_Tmp"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -689.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3188.500000 -1139.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62641" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3228.538462 -1007.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62641" ObjectName="CX_DY:CX_DY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79712" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3227.538462 -962.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79712" ObjectName="CX_DY:CX_DY_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26064" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -1159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26064" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26065" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -1159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26065" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26061" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3675.000000 -1159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26061" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4216"/>
     <cge:Term_Ref ObjectID="6072"/>
    <cge:TPSR_Ref TObjectID="4216"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4075.000000 -845.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26095" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4075.000000 -845.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26095" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26091" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4075.000000 -845.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26091" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4271"/>
     <cge:Term_Ref ObjectID="6182"/>
    <cge:TPSR_Ref TObjectID="4271"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26072" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -1159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26072" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26073" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -1159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26073" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26069" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3905.000000 -1159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26069" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4222"/>
     <cge:Term_Ref ObjectID="6084"/>
    <cge:TPSR_Ref TObjectID="4222"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26087" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -1159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26087" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26088" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -1159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26088" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26084" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4597.000000 -1159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26084" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4238"/>
     <cge:Term_Ref ObjectID="6116"/>
    <cge:TPSR_Ref TObjectID="4238"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26079" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4315.000000 -1159.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26079" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26080" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4315.000000 -1159.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26080" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26076" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4315.000000 -1159.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26076" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4232"/>
     <cge:Term_Ref ObjectID="6104"/>
    <cge:TPSR_Ref TObjectID="4232"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26100" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -783.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26100" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26101" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -783.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26101" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26097" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3954.000000 -783.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26097" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4244"/>
     <cge:Term_Ref ObjectID="6128"/>
    <cge:TPSR_Ref TObjectID="4244"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26109" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -793.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26109" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26110" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -793.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26110" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -793.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4257"/>
     <cge:Term_Ref ObjectID="6154"/>
    <cge:TPSR_Ref TObjectID="4257"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -270.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4276"/>
     <cge:Term_Ref ObjectID="6192"/>
    <cge:TPSR_Ref TObjectID="4276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -270.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4276"/>
     <cge:Term_Ref ObjectID="6192"/>
    <cge:TPSR_Ref TObjectID="4276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4054.000000 -270.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4276"/>
     <cge:Term_Ref ObjectID="6192"/>
    <cge:TPSR_Ref TObjectID="4276"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -501.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62466" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4254"/>
     <cge:Term_Ref ObjectID="6148"/>
    <cge:TPSR_Ref TObjectID="4254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -501.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4254"/>
     <cge:Term_Ref ObjectID="6148"/>
    <cge:TPSR_Ref TObjectID="4254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3758.000000 -501.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4254"/>
     <cge:Term_Ref ObjectID="6148"/>
    <cge:TPSR_Ref TObjectID="4254"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62478" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -490.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4268"/>
     <cge:Term_Ref ObjectID="6176"/>
    <cge:TPSR_Ref TObjectID="4268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -490.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4268"/>
     <cge:Term_Ref ObjectID="6176"/>
    <cge:TPSR_Ref TObjectID="4268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4299.000000 -490.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4268"/>
     <cge:Term_Ref ObjectID="6176"/>
    <cge:TPSR_Ref TObjectID="4268"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -616.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62472" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62473" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -616.239382) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62473" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62469" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4576.000000 -616.239382) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62469" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4264"/>
     <cge:Term_Ref ObjectID="6168"/>
    <cge:TPSR_Ref TObjectID="4264"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-62460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -617.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4250"/>
     <cge:Term_Ref ObjectID="6140"/>
    <cge:TPSR_Ref TObjectID="4250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-62461" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -617.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62461" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4250"/>
     <cge:Term_Ref ObjectID="6140"/>
    <cge:TPSR_Ref TObjectID="4250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-62457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4088.000000 -617.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4250"/>
     <cge:Term_Ref ObjectID="6140"/>
    <cge:TPSR_Ref TObjectID="4250"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-26123" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -347.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26123" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4279"/>
     <cge:Term_Ref ObjectID="6198"/>
    <cge:TPSR_Ref TObjectID="4279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26124" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -347.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26124" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4279"/>
     <cge:Term_Ref ObjectID="6198"/>
    <cge:TPSR_Ref TObjectID="4279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26120" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4163.000000 -347.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26120" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4279"/>
     <cge:Term_Ref ObjectID="6198"/>
    <cge:TPSR_Ref TObjectID="4279"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4286"/>
     <cge:Term_Ref ObjectID="6212"/>
    <cge:TPSR_Ref TObjectID="4286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26128" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4264.000000 24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26128" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4286"/>
     <cge:Term_Ref ObjectID="6212"/>
    <cge:TPSR_Ref TObjectID="4286"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26133" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4535.000000 24.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26133" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4297"/>
     <cge:Term_Ref ObjectID="6234"/>
    <cge:TPSR_Ref TObjectID="4297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26132" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4535.000000 24.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26132" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4297"/>
     <cge:Term_Ref ObjectID="6234"/>
    <cge:TPSR_Ref TObjectID="4297"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26131" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.000000 -46.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26131" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4293"/>
     <cge:Term_Ref ObjectID="6226"/>
    <cge:TPSR_Ref TObjectID="4293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3760.000000 -46.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4293"/>
     <cge:Term_Ref ObjectID="6226"/>
    <cge:TPSR_Ref TObjectID="4293"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-26127" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -14.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26127" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4282"/>
     <cge:Term_Ref ObjectID="6204"/>
    <cge:TPSR_Ref TObjectID="4282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-26126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3869.000000 -14.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4282"/>
     <cge:Term_Ref ObjectID="6204"/>
    <cge:TPSR_Ref TObjectID="4282"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-26104" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -604.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26104" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4312"/>
     <cge:Term_Ref ObjectID="6268"/>
    <cge:TPSR_Ref TObjectID="4312"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-26113" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4278.000000 -721.239382) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26113" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4313"/>
     <cge:Term_Ref ObjectID="6275"/>
    <cge:TPSR_Ref TObjectID="4313"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26159" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26159" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26160" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26160" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26161" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26161" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26158" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4432.000000 -958.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26158" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4213"/>
     <cge:Term_Ref ObjectID="6069"/>
    <cge:TPSR_Ref TObjectID="4213"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26151" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26151" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26152" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26152" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26155" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26155" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3505.000000 -840.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4212"/>
     <cge:Term_Ref ObjectID="6068"/>
    <cge:TPSR_Ref TObjectID="4212"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26167" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26167" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26168" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26168" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26169" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26169" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26170" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26170" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26171" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26171" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4871.000000 -1133.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4214"/>
     <cge:Term_Ref ObjectID="6070"/>
    <cge:TPSR_Ref TObjectID="4214"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26135" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26135" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26136" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26136" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26137" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26137" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26139" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26139" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26134" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3528.000000 -515.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26134" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4210"/>
     <cge:Term_Ref ObjectID="6066"/>
    <cge:TPSR_Ref TObjectID="4210"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26143" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26143" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26144" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26144" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26145" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26145" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26146" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26146" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26147" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26147" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4661.000000 -492.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4211"/>
     <cge:Term_Ref ObjectID="6067"/>
    <cge:TPSR_Ref TObjectID="4211"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-26175" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26175" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-26176" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26176" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-26177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-26178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-26179" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26179" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-26174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -182.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="26174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="4215"/>
     <cge:Term_Ref ObjectID="6071"/>
    <cge:TPSR_Ref TObjectID="4215"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3200" y="-1198"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3200" y="-1198"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3152" y="-1215"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3152" y="-1215"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3374,-1207 3371,-1210 3371,-1148 3374,-1151 3374,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3374,-1207 3371,-1210 3430,-1210 3427,-1207 3374,-1207" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3374,-1151 3371,-1148 3430,-1148 3427,-1151 3374,-1151" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3427,-1207 3430,-1210 3430,-1148 3427,-1151 3427,-1207" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="56" stroke="rgb(255,255,255)" width="53" x="3374" y="-1207"/>
     <rect fill="none" height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3374" y="-1207"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="22" x="4481" y="-1149"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="22" x="4481" y="-1149"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3976" y="-339"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3976" y="-339"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3866" y="-331"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3866" y="-331"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3757" y="-331"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3757" y="-331"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4226" y="-333"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4226" y="-333"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4337" y="-341"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4337" y="-341"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4451" y="-340"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4451" y="-340"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="68" x="3852" y="-669"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="68" x="3852" y="-669"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="62" x="4409" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="62" x="4409" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3603" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3603" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="3833" y="-956"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="3833" y="-956"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="24" x="4047" y="-949"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="24" x="4047" y="-949"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4241" y="-957"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4241" y="-957"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4522" y="-956"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4522" y="-956"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="39" qtmmishow="hidden" width="145" x="3151" y="-790"/>
    </a>
   <metadata/><rect fill="white" height="39" opacity="0" stroke="white" transform="" width="145" x="3151" y="-790"/></g>
  </g><g id="MotifButton_Layer">
   <g href="dy_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3200" y="-1198"/></g>
   <g href="dy_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3152" y="-1215"/></g>
   <g href="AVC大姚站.svg" style="fill-opacity:0"><rect height="56" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="3374" y="-1207"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="22" x="4481" y="-1149"/></g>
   <g href="110kV大姚变10kV中心站Ⅰ回051断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3976" y="-339"/></g>
   <g href="110kV大姚变10kV1号电容器052断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3866" y="-331"/></g>
   <g href="110kV大姚变10kV3号电容器053断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3757" y="-331"/></g>
   <g href="110kV大姚变10kV中心站Ⅱ回061断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4226" y="-333"/></g>
   <g href="110kV大姚变10kV2号电容器062断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4337" y="-341"/></g>
   <g href="110kV大姚变10kV4号电容器063断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4451" y="-340"/></g>
   <g href="110kV大姚变110kV1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="68" x="3852" y="-669"/></g>
   <g href="110kV大姚变110kV2号主变间隔接线图.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="62" x="4409" y="-613"/></g>
   <g href="110kV大姚变110kV元大线151断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3603" y="-957"/></g>
   <g href="110kV大姚变110kV姚大线152断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="3833" y="-956"/></g>
   <g href="110kV大姚变110kV分段112断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="24" x="4047" y="-949"/></g>
   <g href="110kV大姚变110kV渔大线153断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4241" y="-957"/></g>
   <g href="110kV大姚变110kV大六线154断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4522" y="-956"/></g>
   <g href="110kV大姚变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="39" qtmmishow="hidden" width="145" x="3151" y="-790"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3079" y="-1219"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3078" y="-1099"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3116" y="-637"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2844e80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -476.000000)" xlink:href="#voltageTransformer:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e44770">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4082.000000 -741.000000)" xlink:href="#voltageTransformer:shape9"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e48230">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -192.000000)" xlink:href="#voltageTransformer:shape14"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4cd10">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4677.500000 -918.500000)" xlink:href="#voltageTransformer:shape22"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e55850">
    <use class="BV-110KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 3561.000000 -712.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e57d10">
    <use class="BV-110KV" transform="matrix(1.250000 -0.000000 0.000000 1.928571 4622.000000 -707.000000)" xlink:href="#voltageTransformer:shape36"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.LD_343">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5282.000000 -974.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15405" ObjectName="EC-CX_DY.LD_343"/>
    <cge:TPSR_Ref TObjectID="15405"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.LD_341">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5282.000000 -776.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15406" ObjectName="EC-CX_DY.LD_341"/>
    <cge:TPSR_Ref TObjectID="15406"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.LD_342">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5275.000000 -598.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15407" ObjectName="EC-CX_DY.LD_342"/>
    <cge:TPSR_Ref TObjectID="15407"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.LD_346">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5267.000000 -343.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15408" ObjectName="EC-CX_DY.LD_346"/>
    <cge:TPSR_Ref TObjectID="15408"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.LD_345">
    <use class="BKBV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5268.000000 -224.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15409" ObjectName="EC-CX_DY.LD_345"/>
    <cge:TPSR_Ref TObjectID="15409"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.CX_DY_LD061">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -112.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15936" ObjectName="EC-CX_DY.CX_DY_LD061"/>
    <cge:TPSR_Ref TObjectID="15936"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DY.CX_DY_LD051">
    <use class="BKBV-10KV" transform="matrix(0.944444 -0.000000 0.000000 -0.812500 3959.000000 -168.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15935" ObjectName="EC-CX_DY.CX_DY_LD051"/>
    <cge:TPSR_Ref TObjectID="15935"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_29e47d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-855 4005,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4272@0" Pin0InfoVect0LinkObjId="SW-26566_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-855 4005,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2902610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-855 4120,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4274@0" Pin0InfoVect0LinkObjId="SW-26568_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-855 4120,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2902800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-903 4005,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4272@1" ObjectIDZND0="4271@x" ObjectIDZND1="4273@x" Pin0InfoVect0LinkObjId="SW-26562_0" Pin0InfoVect1LinkObjId="SW-26567_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26566_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-903 4005,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_299baf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-909 4120,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4274@1" ObjectIDZND0="4271@x" ObjectIDZND1="4275@x" Pin0InfoVect0LinkObjId="SW-26562_0" Pin0InfoVect1LinkObjId="SW-26569_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26568_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-909 4120,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d5e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-927 4047,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4273@x" ObjectIDND1="4272@x" ObjectIDZND0="4271@1" Pin0InfoVect0LinkObjId="SW-26562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26567_0" Pin1InfoVect1LinkObjId="SW-26566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-927 4047,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d5ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4074,-927 4120,-927 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4271@0" ObjectIDZND0="4274@x" ObjectIDZND1="4275@x" Pin0InfoVect0LinkObjId="SW-26568_0" Pin0InfoVect1LinkObjId="SW-26569_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4074,-927 4120,-927 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d61e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-927 4005,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4271@x" ObjectIDND1="4272@x" ObjectIDZND0="4273@0" Pin0InfoVect0LinkObjId="SW-26567_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="SW-26566_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-927 4005,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d63d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-983 4005,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4273@1" ObjectIDZND0="g_29d69a0@0" Pin0InfoVect0LinkObjId="g_29d69a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26567_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-983 4005,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-927 4120,-947 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4271@x" ObjectIDND1="4274@x" ObjectIDZND0="4275@0" Pin0InfoVect0LinkObjId="SW-26569_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26562_0" Pin1InfoVect1LinkObjId="SW-26568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-927 4120,-947 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d67b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-983 4120,-994 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4275@1" ObjectIDZND0="g_295d5d0@0" Pin0InfoVect0LinkObjId="g_295d5d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26569_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-983 4120,-994 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b3550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-905 3595,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4217@1" ObjectIDZND0="4216@x" ObjectIDZND1="4219@x" Pin0InfoVect0LinkObjId="SW-26183_0" Pin0InfoVect1LinkObjId="SW-26187_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26185_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-905 3595,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b3740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-921 3595,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4219@x" ObjectIDND1="4217@x" ObjectIDZND0="4216@0" Pin0InfoVect0LinkObjId="SW-26183_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26187_0" Pin1InfoVect1LinkObjId="SW-26185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-921 3595,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b3930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-962 3595,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4216@1" ObjectIDZND0="4218@x" ObjectIDZND1="4220@x" Pin0InfoVect0LinkObjId="SW-26186_0" Pin0InfoVect1LinkObjId="SW-26188_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26183_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-962 3595,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29b3b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-975 3595,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4220@x" ObjectIDZND0="4218@0" Pin0InfoVect0LinkObjId="SW-26186_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26188_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-975 3595,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-975 3612,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4218@x" ObjectIDZND0="4220@0" Pin0InfoVect0LinkObjId="SW-26188_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-975 3612,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-975 3662,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4220@1" ObjectIDZND0="g_296a610@0" Pin0InfoVect0LinkObjId="g_296a610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26188_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-975 3662,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296a420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3649,-1040 3662,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4221@1" ObjectIDZND0="g_296ac40@0" Pin0InfoVect0LinkObjId="g_296ac40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26189_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3649,-1040 3662,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296b270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-1025 3595,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4218@1" ObjectIDZND0="g_28ac810@0" ObjectIDZND1="g_296ba30@0" ObjectIDZND2="11550@1" Pin0InfoVect0LinkObjId="g_28ac810_0" Pin0InfoVect1LinkObjId="g_296ba30_0" Pin0InfoVect2LinkObjId="g_2e538f0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26186_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-1025 3595,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296b460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-1040 3613,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_28ac810@0" ObjectIDND1="g_296ba30@0" ObjectIDND2="11550@1" ObjectIDZND0="4221@0" Pin0InfoVect0LinkObjId="SW-26189_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28ac810_0" Pin1InfoVect1LinkObjId="g_296ba30_0" Pin1InfoVect2LinkObjId="g_2e538f0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-1040 3613,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296b650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-1108 3708,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28ac810@0" ObjectIDND1="4218@x" ObjectIDND2="4221@x" ObjectIDZND0="g_296ba30@0" Pin0InfoVect0LinkObjId="g_296ba30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28ac810_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="SW-26189_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3708,-1108 3708,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_296b840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3708,-1108 3738,-1108 3738,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_296ba30@0" ObjectIDND1="4218@x" ObjectIDND2="4221@x" ObjectIDZND0="g_28ac810@0" Pin0InfoVect0LinkObjId="g_28ac810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_296ba30_0" Pin1InfoVect1LinkObjId="SW-26186_0" Pin1InfoVect2LinkObjId="SW-26189_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3708,-1108 3738,-1108 3738,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ac620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-855 3595,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4217@0" Pin0InfoVect0LinkObjId="SW-26185_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-855 3595,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2950ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-905 3825,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4223@1" ObjectIDZND0="4222@x" ObjectIDZND1="4224@x" Pin0InfoVect0LinkObjId="SW-26202_0" Pin0InfoVect1LinkObjId="SW-26206_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26205_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-905 3825,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2950cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-921 3825,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4224@x" ObjectIDND1="4223@x" ObjectIDZND0="4222@0" Pin0InfoVect0LinkObjId="SW-26202_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26206_0" Pin1InfoVect1LinkObjId="SW-26205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-921 3825,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2950eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-962 3825,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4222@1" ObjectIDZND0="4225@x" ObjectIDZND1="4226@x" Pin0InfoVect0LinkObjId="SW-26207_0" Pin0InfoVect1LinkObjId="SW-26208_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26202_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-962 3825,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29510a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-975 3825,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4226@x" ObjectIDZND0="4225@0" Pin0InfoVect0LinkObjId="SW-26207_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26208_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-975 3825,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_294b0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-921 3842,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4223@x" ObjectIDZND0="4224@0" Pin0InfoVect0LinkObjId="SW-26206_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26205_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-921 3842,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_294b2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-921 3892,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4224@1" ObjectIDZND0="g_294ba60@0" Pin0InfoVect0LinkObjId="g_294ba60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26206_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-921 3892,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_294b490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-975 3842,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4222@x" ObjectIDND1="4225@x" ObjectIDZND0="4226@0" Pin0InfoVect0LinkObjId="SW-26208_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26202_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-975 3842,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_294b680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3878,-975 3892,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4226@1" ObjectIDZND0="g_294c210@0" Pin0InfoVect0LinkObjId="g_294c210_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26208_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3878,-975 3892,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_294b870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-1040 3892,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4227@1" ObjectIDZND0="g_294c9c0@0" Pin0InfoVect0LinkObjId="g_294c9c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26209_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-1040 3892,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29cb6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-1025 3825,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4225@1" ObjectIDZND0="4227@x" ObjectIDZND1="g_29ccab0@0" ObjectIDZND2="g_29cbeb0@0" Pin0InfoVect0LinkObjId="SW-26209_0" Pin0InfoVect1LinkObjId="g_29ccab0_0" Pin0InfoVect2LinkObjId="g_29cbeb0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26207_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-1025 3825,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29cb8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-1040 3843,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="4225@x" ObjectIDND1="g_29ccab0@0" ObjectIDND2="g_29cbeb0@0" ObjectIDZND0="4227@0" Pin0InfoVect0LinkObjId="SW-26209_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="g_29ccab0_0" Pin1InfoVect2LinkObjId="g_29cbeb0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-1040 3843,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29cbad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3938,-1108 3938,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29ccab0@0" ObjectIDND1="4225@x" ObjectIDND2="4227@x" ObjectIDZND0="g_29cbeb0@0" Pin0InfoVect0LinkObjId="g_29cbeb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29ccab0_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="SW-26209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3938,-1108 3938,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29cbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3938,-1108 3968,-1108 3968,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29cbeb0@0" ObjectIDND1="4225@x" ObjectIDND2="4227@x" ObjectIDZND0="g_29ccab0@0" Pin0InfoVect0LinkObjId="g_29ccab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29cbeb0_0" Pin1InfoVect1LinkObjId="SW-26207_0" Pin1InfoVect2LinkObjId="SW-26209_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3938,-1108 3968,-1108 3968,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29cc8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-855 3825,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4223@0" Pin0InfoVect0LinkObjId="SW-26205_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-855 3825,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2921a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-921 3612,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4216@x" ObjectIDND1="4217@x" ObjectIDZND0="4219@0" Pin0InfoVect0LinkObjId="SW-26187_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26183_0" Pin1InfoVect1LinkObjId="SW-26185_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-921 3612,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2921c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3648,-921 3662,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4219@1" ObjectIDZND0="g_2921e40@0" Pin0InfoVect0LinkObjId="g_2921e40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26187_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3648,-921 3662,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2922770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-745 3627,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_29c7000@0" ObjectIDND1="g_2e55850@0" ObjectIDND2="4228@x" ObjectIDZND0="4230@0" Pin0InfoVect0LinkObjId="SW-26212_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c7000_0" Pin1InfoVect1LinkObjId="g_2e55850_0" Pin1InfoVect2LinkObjId="SW-26210_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-745 3627,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c4a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-745 3677,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4230@1" ObjectIDZND0="g_29c4ca0@0" Pin0InfoVect0LinkObjId="g_29c4ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26212_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-745 3677,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c55d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3610,-819 3627,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDND1="4228@x" ObjectIDZND0="4229@0" Pin0InfoVect0LinkObjId="SW-26211_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26210_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3610,-819 3627,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c57f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3663,-819 3677,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4229@1" ObjectIDZND0="g_29c5a10@0" Pin0InfoVect0LinkObjId="g_29c5a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26211_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3663,-819 3677,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c6340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-760 3609,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4228@0" ObjectIDZND0="g_29c7000@0" ObjectIDZND1="g_2e55850@0" ObjectIDZND2="4230@x" Pin0InfoVect0LinkObjId="g_29c7000_0" Pin0InfoVect1LinkObjId="g_2e55850_0" Pin0InfoVect2LinkObjId="SW-26212_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-760 3609,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c6560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-855 3609,-819 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4212@0" ObjectIDZND0="4228@x" ObjectIDZND1="4229@x" Pin0InfoVect0LinkObjId="SW-26210_0" Pin0InfoVect1LinkObjId="SW-26211_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-855 3609,-819 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c6780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-819 3609,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDND1="4229@x" ObjectIDZND0="4228@1" Pin0InfoVect0LinkObjId="SW-26210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26211_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-819 3609,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c69a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-721 3585,-721 3585,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_29c7000@0" ObjectIDND1="4228@x" ObjectIDND2="4230@x" ObjectIDZND0="g_2e55850@0" Pin0InfoVect0LinkObjId="g_2e55850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29c7000_0" Pin1InfoVect1LinkObjId="SW-26210_0" Pin1InfoVect2LinkObjId="SW-26212_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-721 3585,-721 3585,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c6bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3637,-704 3637,-721 3609,-721 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_29c7000@0" ObjectIDZND0="g_2e55850@0" ObjectIDZND1="4228@x" ObjectIDZND2="4230@x" Pin0InfoVect0LinkObjId="g_2e55850_0" Pin0InfoVect1LinkObjId="SW-26210_0" Pin0InfoVect2LinkObjId="SW-26212_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c7000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3637,-704 3637,-721 3609,-721 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29c6de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-721 3609,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_29c7000@0" ObjectIDND1="g_2e55850@0" ObjectIDZND0="4228@x" ObjectIDZND1="4230@x" Pin0InfoVect0LinkObjId="SW-26210_0" Pin0InfoVect1LinkObjId="SW-26212_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29c7000_0" Pin1InfoVect1LinkObjId="g_2e55850_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-721 3609,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ffad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-904 4513,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4239@1" ObjectIDZND0="4238@x" ObjectIDZND1="4241@x" Pin0InfoVect0LinkObjId="SW-26255_0" Pin0InfoVect1LinkObjId="SW-26259_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26257_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-904 4513,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d7920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-921 4513,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4241@x" ObjectIDND1="4239@x" ObjectIDZND0="4238@0" Pin0InfoVect0LinkObjId="SW-26255_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26259_0" Pin1InfoVect1LinkObjId="SW-26257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-921 4513,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d7b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-962 4513,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4238@1" ObjectIDZND0="4240@x" ObjectIDZND1="4242@x" Pin0InfoVect0LinkObjId="SW-26258_0" Pin0InfoVect1LinkObjId="SW-26260_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26255_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-962 4513,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29d7d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-975 4513,-989 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4242@x" ObjectIDZND0="4240@0" Pin0InfoVect0LinkObjId="SW-26258_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26260_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-975 4513,-989 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-921 4530,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4239@x" ObjectIDZND0="4241@0" Pin0InfoVect0LinkObjId="SW-26259_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26257_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-921 4530,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f2e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-921 4580,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4241@1" ObjectIDZND0="g_285e530@0" Pin0InfoVect0LinkObjId="g_285e530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26259_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-921 4580,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285de10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-975 4530,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4238@x" ObjectIDND1="4240@x" ObjectIDZND0="4242@0" Pin0InfoVect0LinkObjId="SW-26260_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26255_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-975 4530,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285e070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4566,-975 4580,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4242@1" ObjectIDZND0="g_285ef20@0" Pin0InfoVect0LinkObjId="g_285ef20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26260_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4566,-975 4580,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_285e2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4567,-1040 4580,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4243@1" ObjectIDZND0="g_285f950@0" Pin0InfoVect0LinkObjId="g_285f950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26261_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4567,-1040 4580,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2860380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-1025 4513,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4240@1" ObjectIDZND0="11715@1" ObjectIDZND1="g_29032c0@0" ObjectIDZND2="g_2860d00@0" Pin0InfoVect0LinkObjId="g_2e54ed0_1" Pin0InfoVect1LinkObjId="g_29032c0_0" Pin0InfoVect2LinkObjId="g_2860d00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26258_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-1025 4513,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28605e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-1040 4531,-1040 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="11715@1" ObjectIDND1="g_29032c0@0" ObjectIDND2="g_2860d00@0" ObjectIDZND0="4243@0" Pin0InfoVect0LinkObjId="SW-26261_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2860380_1" Pin1InfoVect1LinkObjId="g_29032c0_0" Pin1InfoVect2LinkObjId="g_2860d00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-1040 4531,-1040 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2860840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-1108 4626,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29032c0@0" ObjectIDND1="4240@x" ObjectIDND2="4243@x" ObjectIDZND0="g_2860d00@0" Pin0InfoVect0LinkObjId="g_2860d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29032c0_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="SW-26261_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-1108 4626,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2860aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4626,-1108 4656,-1108 4656,-1096 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2860d00@0" ObjectIDND1="4240@x" ObjectIDND2="4243@x" ObjectIDZND0="g_29032c0@0" Pin0InfoVect0LinkObjId="g_29032c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2860d00_0" Pin1InfoVect1LinkObjId="SW-26258_0" Pin1InfoVect2LinkObjId="SW-26261_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4626,-1108 4656,-1108 4656,-1096 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2903060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-855 4513,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4239@0" Pin0InfoVect0LinkObjId="SW-26257_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-855 4513,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29119f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-905 4232,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4233@1" ObjectIDZND0="4232@x" ObjectIDZND1="4235@x" Pin0InfoVect0LinkObjId="SW-26236_0" Pin0InfoVect1LinkObjId="SW-26239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26237_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-905 4232,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2911c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-922 4232,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4235@x" ObjectIDND1="4233@x" ObjectIDZND0="4232@0" Pin0InfoVect0LinkObjId="SW-26236_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26239_0" Pin1InfoVect1LinkObjId="SW-26237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-922 4232,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2911eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-963 4232,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4232@1" ObjectIDZND0="4234@x" ObjectIDZND1="4236@x" Pin0InfoVect0LinkObjId="SW-26238_0" Pin0InfoVect1LinkObjId="SW-26240_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26236_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-963 4232,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2912110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-976 4232,-990 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4236@x" ObjectIDZND0="4234@0" Pin0InfoVect0LinkObjId="SW-26238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26240_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-976 4232,-990 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2945f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-922 4249,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4233@x" ObjectIDZND0="4235@0" Pin0InfoVect0LinkObjId="SW-26239_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26237_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-922 4249,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29461d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-922 4299,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4235@1" ObjectIDZND0="g_2946b50@0" Pin0InfoVect0LinkObjId="g_2946b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26239_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-922 4299,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2946430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-976 4249,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4232@x" ObjectIDND1="4234@x" ObjectIDZND0="4236@0" Pin0InfoVect0LinkObjId="SW-26240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26236_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-976 4249,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2946690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4285,-976 4299,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4236@1" ObjectIDZND0="g_2947580@0" Pin0InfoVect0LinkObjId="g_2947580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4285,-976 4299,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29468f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-1041 4299,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4237@1" ObjectIDZND0="g_2947fb0@0" Pin0InfoVect0LinkObjId="g_2947fb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-1041 4299,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2976c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-1026 4232,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4234@1" ObjectIDZND0="11716@1" ObjectIDZND1="g_2978540@0" ObjectIDZND2="g_29775f0@0" Pin0InfoVect0LinkObjId="g_2e54510_1" Pin0InfoVect1LinkObjId="g_2978540_0" Pin0InfoVect2LinkObjId="g_29775f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-1026 4232,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2976ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-1041 4250,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="11716@1" ObjectIDND1="g_2978540@0" ObjectIDND2="g_29775f0@0" ObjectIDZND0="4237@0" Pin0InfoVect0LinkObjId="SW-26241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2976c70_1" Pin1InfoVect1LinkObjId="g_2978540_0" Pin1InfoVect2LinkObjId="g_29775f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-1041 4250,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2977130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1109 4345,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2978540@0" ObjectIDND1="4234@x" ObjectIDND2="4237@x" ObjectIDZND0="g_29775f0@0" Pin0InfoVect0LinkObjId="g_29775f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2978540_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="SW-26241_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1109 4345,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2977390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-1109 4375,-1109 4375,-1097 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29775f0@0" ObjectIDND1="4234@x" ObjectIDND2="4237@x" ObjectIDZND0="g_2978540@0" Pin0InfoVect0LinkObjId="g_2978540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29775f0_0" Pin1InfoVect1LinkObjId="SW-26238_0" Pin1InfoVect2LinkObjId="SW-26241_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4345,-1109 4375,-1109 4375,-1097 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29782e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-855 4232,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDZND0="4233@0" Pin0InfoVect0LinkObjId="SW-26237_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-855 4232,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2961120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-855 3802,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4212@0" ObjectIDZND0="4245@1" Pin0InfoVect0LinkObjId="SW-26288_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-855 3802,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2847330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-671 3802,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4313@1" ObjectIDZND0="4247@0" Pin0InfoVect0LinkObjId="SW-26290_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-671 3802,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2847590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-787 3819,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4245@x" ObjectIDZND0="4246@0" Pin0InfoVect0LinkObjId="SW-26289_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26288_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-787 3819,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28477f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-787 3869,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4246@1" ObjectIDZND0="g_2847a50@0" Pin0InfoVect0LinkObjId="g_2847a50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26289_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-787 3869,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_284a740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-772 3802,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4244@1" ObjectIDZND0="4245@x" ObjectIDZND1="4246@x" Pin0InfoVect0LinkObjId="SW-26288_0" Pin0InfoVect1LinkObjId="SW-26289_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-772 3802,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_284a9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-787 3802,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4246@x" ObjectIDZND0="4245@0" Pin0InfoVect0LinkObjId="SW-26288_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26289_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-787 3802,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_284ac00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-735 3819,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4244@x" ObjectIDND1="4247@x" ObjectIDZND0="4248@0" Pin0InfoVect0LinkObjId="SW-26291_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26286_0" Pin1InfoVect1LinkObjId="SW-26290_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-735 3819,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_284ae60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3855,-735 3869,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4248@1" ObjectIDZND0="g_284b0c0@0" Pin0InfoVect0LinkObjId="g_284b0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26291_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3855,-735 3869,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_293d3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-722 3802,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4247@1" ObjectIDZND0="4244@x" ObjectIDZND1="4248@x" Pin0InfoVect0LinkObjId="SW-26286_0" Pin0InfoVect1LinkObjId="SW-26291_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26290_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-722 3802,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_293d640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3802,-735 3802,-745 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4247@x" ObjectIDND1="4248@x" ObjectIDZND0="4244@0" Pin0InfoVect0LinkObjId="SW-26286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26290_0" Pin1InfoVect1LinkObjId="SW-26291_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3802,-735 3802,-745 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_288eed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-784 4366,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4258@x" ObjectIDZND0="4259@0" Pin0InfoVect0LinkObjId="SW-26427_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26426_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-784 4366,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_288f130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-784 4416,-784 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4259@1" ObjectIDZND0="g_288f390@0" Pin0InfoVect0LinkObjId="g_288f390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26427_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-784 4416,-784 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2972fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-736 4366,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4260@x" ObjectIDZND0="4261@0" Pin0InfoVect0LinkObjId="SW-26429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-736 4366,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2973220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-736 4416,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4261@1" ObjectIDZND0="g_2973480@0" Pin0InfoVect0LinkObjId="g_2973480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-736 4416,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2887d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-404 3656,-390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4210@0" ObjectIDZND0="4311@1" Pin0InfoVect0LinkObjId="SW-26682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29298b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-404 3656,-390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28887a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-354 3656,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4311@0" ObjectIDZND0="g_2887fe0@1" Pin0InfoVect0LinkObjId="g_2887fe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-354 3656,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_288af90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-404 3569,-388 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4210@0" ObjectIDZND0="4310@1" Pin0InfoVect0LinkObjId="SW-26681_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29298b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-404 3569,-388 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2907050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4039,-88 4025,-88 4025,-36 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4039,-88 4025,-88 4025,-36 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29072b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4182,-35 4182,-88 4173,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4182,-35 4182,-88 4173,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2907510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4137,-88 4119,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4137,-88 4119,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2907770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-88 4075,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-88 4075,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_290b9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-357 3748,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4294@0" ObjectIDZND0="4293@1" Pin0InfoVect0LinkObjId="SW-26641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26642_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-357 3748,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29298b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-393 3748,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4294@1" ObjectIDZND0="4210@0" Pin0InfoVect0LinkObjId="g_28e2be0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-393 3748,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2929b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-310 3748,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4293@0" ObjectIDZND0="4295@1" Pin0InfoVect0LinkObjId="SW-26643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-310 3748,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292c030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-217 3765,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15565@x" ObjectIDND1="4295@x" ObjectIDZND0="4296@0" Pin0InfoVect0LinkObjId="SW-26644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb3_0" Pin1InfoVect1LinkObjId="SW-26643_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-217 3765,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292c290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-217 3813,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4296@1" ObjectIDZND0="g_292c4f0@0" Pin0InfoVect0LinkObjId="g_292c4f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-217 3813,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e0430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-357 3857,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4283@0" ObjectIDZND0="4282@1" Pin0InfoVect0LinkObjId="SW-26608_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26609_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-357 3857,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e2be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-393 3857,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4283@1" ObjectIDZND0="4210@0" Pin0InfoVect0LinkObjId="g_29298b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26609_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-393 3857,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28e2e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-310 3857,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4282@0" ObjectIDZND0="4284@1" Pin0InfoVect0LinkObjId="SW-26610_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-310 3857,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-217 3874,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15563@x" ObjectIDND1="4284@x" ObjectIDZND0="4285@0" Pin0InfoVect0LinkObjId="SW-26611_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb1_0" Pin1InfoVect1LinkObjId="SW-26610_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-217 3874,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3910,-217 3922,-217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4285@1" ObjectIDZND0="g_2933010@0" Pin0InfoVect0LinkObjId="g_2933010_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3910,-217 3922,-217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28d8280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-393 3967,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4277@1" ObjectIDZND0="4210@0" Pin0InfoVect0LinkObjId="g_29298b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26571_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-393 3967,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2891e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-345 3967,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4276@1" ObjectIDZND0="4277@0" Pin0InfoVect0LinkObjId="SW-26571_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-345 3967,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2894640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-348 4563,-286 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="4308@0" ObjectIDZND0="g_2df52b0@0" Pin0InfoVect0LinkObjId="g_2df52b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-348 4563,-286 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28948a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4563,-404 4563,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4211@0" ObjectIDZND0="4308@1" Pin0InfoVect0LinkObjId="SW-26679_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_286ccd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4563,-404 4563,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2897090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-588 3801,-554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4313@2" ObjectIDZND0="4256@1" Pin0InfoVect0LinkObjId="SW-26303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-588 3801,-554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287abc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-518 3801,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4256@0" ObjectIDZND0="4254@1" Pin0InfoVect0LinkObjId="SW-26301_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-518 3801,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287d370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-467 3801,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4254@0" ObjectIDZND0="4255@1" Pin0InfoVect0LinkObjId="SW-26302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26301_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-467 3801,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_287d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3801,-418 3801,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4255@0" ObjectIDZND0="4210@0" Pin0InfoVect0LinkObjId="g_29298b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3801,-418 3801,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2883a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-225 4341,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2ddafe0@0" ObjectIDND1="4289@x" ObjectIDZND0="4290@0" Pin0InfoVect0LinkObjId="SW-26627_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ddafe0_0" Pin1InfoVect1LinkObjId="SW-26626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-225 4341,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2883c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-225 4389,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4290@1" ObjectIDZND0="g_2883ee0@0" Pin0InfoVect0LinkObjId="g_2883ee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-225 4389,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_28631a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-307 4341,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4286@x" ObjectIDND1="g_2ddafe0@0" ObjectIDZND0="4288@0" Pin0InfoVect0LinkObjId="SW-26625_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26623_0" Pin1InfoVect1LinkObjId="g_2ddafe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-307 4341,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2863400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-307 4389,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4288@1" ObjectIDZND0="g_2863660@0" Pin0InfoVect0LinkObjId="g_2863660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26625_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-307 4389,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2868910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-161 4341,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15564@x" ObjectIDND1="4289@x" ObjectIDZND0="4291@0" Pin0InfoVect0LinkObjId="SW-26628_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb2_0" Pin1InfoVect1LinkObjId="SW-26626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-161 4341,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2868b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4377,-161 4389,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4291@1" ObjectIDZND0="g_2868dd0@0" Pin0InfoVect0LinkObjId="g_2868dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26628_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4377,-161 4389,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_286bde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,-38 4325,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="4292@1" Pin0InfoVect0LinkObjId="SW-26629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,-38 4325,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_286c040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4325,12 4325,38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4292@0" ObjectIDZND0="g_286c2a0@0" Pin0InfoVect0LinkObjId="g_286c2a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26629_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4325,12 4325,38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_286ccd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-393 4324,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4287@1" ObjectIDZND0="4211@0" Pin0InfoVect0LinkObjId="g_2873a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26624_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-393 4324,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2873810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-463 4347,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4268@0" ObjectIDZND0="4269@1" Pin0InfoVect0LinkObjId="SW-26442_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26441_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-463 4347,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2873a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-415 4347,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4269@0" ObjectIDZND0="4211@0" Pin0InfoVect0LinkObjId="g_286ccd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26442_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-415 4347,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2873cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-507 4347,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4270@0" ObjectIDZND0="4268@1" Pin0InfoVect0LinkObjId="SW-26441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-507 4347,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2878590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3841,-604 3841,-498 3922,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="4313@x" ObjectIDZND0="4252@0" Pin0InfoVect0LinkObjId="SW-26299_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3841,-604 3841,-498 3922,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28787f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-498 4389,-498 4389,-599 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="4267@1" ObjectIDZND0="4312@x" Pin0InfoVect0LinkObjId="g_2f54b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26440_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-498 4389,-498 4389,-599 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2878a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-498 4133,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2df91c0@0" ObjectIDND1="4252@x" ObjectIDND2="4267@x" ObjectIDZND0="g_2878cb0@0" Pin0InfoVect0LinkObjId="g_2878cb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2df91c0_0" Pin1InfoVect1LinkObjId="SW-26299_0" Pin1InfoVect2LinkObjId="SW-26440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-498 4133,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28799a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4033,-498 4033,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2878cb0@0" ObjectIDND1="4267@x" ObjectIDND2="4252@x" ObjectIDZND0="g_2df91c0@0" Pin0InfoVect0LinkObjId="g_2df91c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2878cb0_0" Pin1InfoVect1LinkObjId="SW-26440_0" Pin1InfoVect2LinkObjId="SW-26299_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4033,-498 4033,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2851320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4596,-626 4619,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4264@0" ObjectIDZND0="4265@0" Pin0InfoVect0LinkObjId="SW-26438_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26435_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4596,-626 4619,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2851580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4542,-626 4569,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4266@1" ObjectIDZND0="4264@1" Pin0InfoVect0LinkObjId="SW-26435_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26439_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4542,-626 4569,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2853b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-686 4552,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2853d70@0" ObjectIDND1="4307@x" ObjectIDZND0="g_2855030@0" Pin0InfoVect0LinkObjId="g_2855030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2853d70_0" Pin1InfoVect1LinkObjId="SW-26678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-686 4552,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2854550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4504,-686 4504,-725 4522,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2855030@0" ObjectIDND1="4307@x" ObjectIDZND0="g_2853d70@1" Pin0InfoVect0LinkObjId="g_2853d70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2855030_0" Pin1InfoVect1LinkObjId="SW-26678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4504,-686 4504,-725 4522,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28547b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4553,-725 4583,-725 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2853d70@0" ObjectIDZND0="g_2854a10@0" Pin0InfoVect0LinkObjId="g_2854a10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2853d70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4553,-725 4583,-725 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2839890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4092,-630 4111,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4250@0" ObjectIDZND0="4251@0" Pin0InfoVect0LinkObjId="SW-26298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4092,-630 4111,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2839af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4052,-630 4065,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4253@1" ObjectIDZND0="4250@1" Pin0InfoVect0LinkObjId="SW-26296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26300_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4052,-630 4065,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-630 3934,-687 3952,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4313@x" ObjectIDND1="4253@x" ObjectIDZND0="4309@0" Pin0InfoVect0LinkObjId="SW-26680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-630 3934,-687 3952,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283c2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3988,-687 4007,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4309@1" ObjectIDZND0="g_283d440@0" ObjectIDZND1="g_283c7a0@0" Pin0InfoVect0LinkObjId="g_283d440_0" Pin0InfoVect1LinkObjId="g_283c7a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3988,-687 4007,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-687 4058,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_283c7a0@0" ObjectIDND1="4309@x" ObjectIDZND0="g_283d440@0" Pin0InfoVect0LinkObjId="g_283d440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_283c7a0_0" Pin1InfoVect1LinkObjId="SW-26680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-687 4058,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283cf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4007,-687 4007,-726 4025,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_283d440@0" ObjectIDND1="4309@x" ObjectIDZND0="g_283c7a0@1" Pin0InfoVect0LinkObjId="g_283c7a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_283d440_0" Pin1InfoVect1LinkObjId="SW-26680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4007,-687 4007,-726 4025,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283d1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4056,-726 4087,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_283c7a0@0" ObjectIDZND0="g_2e44770@0" Pin0InfoVect0LinkObjId="g_2e44770_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_283c7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4056,-726 4087,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283e110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-630 3934,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4313@0" ObjectIDZND0="4253@x" ObjectIDZND1="4309@x" Pin0InfoVect0LinkObjId="SW-26300_0" Pin0InfoVect1LinkObjId="SW-26680_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-630 3934,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_283e370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3934,-630 4016,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4313@x" ObjectIDND1="4309@x" ObjectIDZND0="4253@0" Pin0InfoVect0LinkObjId="SW-26300_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-26680_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3934,-630 4016,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2841570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4485,-486 4494,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="10400@1" ObjectIDZND0="g_28408c0@0" ObjectIDZND1="g_28441e0@0" ObjectIDZND2="10376@x" Pin0InfoVect0LinkObjId="g_28408c0_0" Pin0InfoVect1LinkObjId="g_28441e0_0" Pin0InfoVect2LinkObjId="SW-56516_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4485,-486 4494,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28417d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-486 4494,-512 4509,-512 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28441e0@0" ObjectIDND1="10400@x" ObjectIDND2="10376@x" ObjectIDZND0="g_28408c0@0" Pin0InfoVect0LinkObjId="g_28408c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28441e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-56516_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-486 4494,-512 4509,-512 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2843d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4494,-486 4494,-451 4508,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_28408c0@0" ObjectIDND1="g_28441e0@0" ObjectIDND2="10400@x" ObjectIDZND0="10376@0" Pin0InfoVect0LinkObjId="SW-56516_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28408c0_0" Pin1InfoVect1LinkObjId="g_28441e0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4494,-486 4494,-451 4508,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2843f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4544,-451 4556,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10376@1" ObjectIDZND0="g_2b3c5c0@0" Pin0InfoVect0LinkObjId="g_2b3c5c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-56516_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4544,-451 4556,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28449c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4580,-486 4549,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2844e80@0" ObjectIDZND0="g_28441e0@0" Pin0InfoVect0LinkObjId="g_28441e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2844e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4580,-486 4549,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2844c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4518,-486 4494,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_28441e0@1" ObjectIDZND0="g_28408c0@0" ObjectIDZND1="10400@x" ObjectIDZND2="10376@x" Pin0InfoVect0LinkObjId="g_28408c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-56516_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28441e0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4518,-486 4494,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1ff20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-352 4879,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4215@0" ObjectIDZND0="10387@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-352 4879,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a20180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-352 4951,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10387@1" ObjectIDZND0="10381@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4915,-352 4951,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a203e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-352 5001,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10381@0" ObjectIDZND0="10388@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-352 5001,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a20640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5037,-352 5240,-352 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" ObjectIDND0="10388@1" ObjectIDZND0="15408@0" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_346_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5037,-352 5240,-352 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a21550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-417 4788,-417 4788,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4215@0" ObjectIDZND0="10379@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-417 4788,-417 4788,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a217b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-469 4788,-486 4837,-486 4835,-485 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10379@1" ObjectIDZND0="4214@0" Pin0InfoVect0LinkObjId="g_28cc8e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-469 4788,-486 4837,-486 4835,-485 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a279d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-983 4864,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4214@0" ObjectIDZND0="10393@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a217b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-983 4864,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a27c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-983 5010,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10384@0" ObjectIDZND0="10394@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-983 5010,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a27e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-983 5060,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="switch" ObjectIDND0="10394@1" ObjectIDZND0="15405@x" ObjectIDZND1="10395@x" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_343_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-983 5060,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a280f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-983 5255,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="10395@x" ObjectIDND1="10394@x" ObjectIDZND0="15405@0" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-983 5255,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a28350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-983 5060,-957 5074,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15405@x" ObjectIDND1="10394@x" ObjectIDZND0="10395@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_DY.LD_343_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-983 5060,-957 5074,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a2b080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5160,-957 5186,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2a2a8a0@0" ObjectIDZND0="g_2a2b2e0@0" Pin0InfoVect0LinkObjId="g_2a2b2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a2a8a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5160,-957 5186,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bc150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-785 4897,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4214@0" ObjectIDZND0="10391@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a217b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-785 4897,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bc3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-785 4960,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10391@1" ObjectIDZND0="10383@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-785 4960,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bc610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-785 5010,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10383@0" ObjectIDZND0="10392@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-785 5010,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bc870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5046,-785 5060,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="10392@1" ObjectIDZND0="15406@x" ObjectIDZND1="g_28bfef0@0" ObjectIDZND2="g_2e4fb50@0" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_341_0" Pin0InfoVect1LinkObjId="g_28bfef0_0" Pin0InfoVect2LinkObjId="g_2e4fb50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5046,-785 5060,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bcad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-785 5255,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="load" ObjectIDND0="g_28bfef0@0" ObjectIDND1="g_2e4fb50@0" ObjectIDND2="10392@x" ObjectIDZND0="15406@0" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28bfef0_0" Pin1InfoVect1LinkObjId="g_2e4fb50_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-785 5255,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bf020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-785 5060,-811 5074,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="15406@x" ObjectIDND1="g_28bfef0@0" ObjectIDND2="g_2e4fb50@0" ObjectIDZND0="10396@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_DY.LD_341_0" Pin1InfoVect1LinkObjId="g_28bfef0_0" Pin1InfoVect2LinkObjId="g_2e4fb50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-785 5060,-811 5074,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28bf280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5110,-811 5122,-811 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10396@1" ObjectIDZND0="g_28bf4e0@0" Pin0InfoVect0LinkObjId="g_28bf4e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5110,-811 5122,-811 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c6ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-608 4890,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4214@0" ObjectIDZND0="10389@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a217b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-608 4890,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c6f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-607 4953,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10389@1" ObjectIDZND0="10382@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-607 4953,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c7f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5048,-557 5031,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28c7940@0" ObjectIDZND0="g_28c7160@0" Pin0InfoVect0LinkObjId="g_28c7160_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c7940_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5048,-557 5031,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28c81c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-557 4989,-557 4989,-608 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="g_28c7160@1" ObjectIDZND0="10382@x" ObjectIDZND1="10390@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c7160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-557 4989,-557 4989,-608 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5228,-571 5213,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28caef0@0" ObjectIDZND0="g_28ca710@0" Pin0InfoVect0LinkObjId="g_28ca710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28caef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5228,-571 5213,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5182,-571 5172,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_28ca710@1" ObjectIDZND0="10397@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28ca710_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5182,-571 5172,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cb9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5114,-759 5099,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_28c06d0@0" ObjectIDZND0="g_28bfef0@0" Pin0InfoVect0LinkObjId="g_28bfef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28c06d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5114,-759 5099,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28cc8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4809,-960 4835,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10377@1" ObjectIDZND0="4214@0" Pin0InfoVect0LinkObjId="g_2a217b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4809,-960 4835,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d2af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4982,-233 5005,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10380@0" ObjectIDZND0="10386@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4982,-233 5005,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_28d2d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-233 5055,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="10386@1" ObjectIDZND0="g_2e529b0@0" ObjectIDZND1="11889@x" ObjectIDZND2="10398@x" Pin0InfoVect0LinkObjId="g_2e529b0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-233 5055,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b23360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5155,-207 5181,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b22b80@0" ObjectIDZND0="g_2b235c0@0" Pin0InfoVect0LinkObjId="g_2b235c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b22b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5155,-207 5181,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b284c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4757,-1010 4757,-1027 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10378@1" ObjectIDZND0="g_2b28720@0" Pin0InfoVect0LinkObjId="g_2b28720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4757,-1010 4757,-1027 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b2a7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-960 4706,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2e4cd10@0" ObjectIDZND0="g_2b29f20@1" Pin0InfoVect0LinkObjId="g_2b29f20_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4cd10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-960 4706,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2aa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-744 4688,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b2d6e0@0" ObjectIDND1="g_2e57d10@0" ObjectIDND2="4304@x" ObjectIDZND0="4306@0" Pin0InfoVect0LinkObjId="SW-26677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b2d6e0_0" Pin1InfoVect1LinkObjId="g_2e57d10_0" Pin1InfoVect2LinkObjId="SW-26675_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-744 4688,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2ac60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-744 4738,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4306@1" ObjectIDZND0="g_2b2aec0@0" Pin0InfoVect0LinkObjId="g_2b2aec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-744 4738,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2b950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4671,-818 4688,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDND1="4304@x" ObjectIDZND0="4305@0" Pin0InfoVect0LinkObjId="SW-26676_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="SW-26675_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4671,-818 4688,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2bbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4724,-818 4738,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4305@1" ObjectIDZND0="g_2b2be10@0" Pin0InfoVect0LinkObjId="g_2b2be10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26676_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4724,-818 4738,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2c8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-759 4670,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" EndDevType2="switch" ObjectIDND0="4304@0" ObjectIDZND0="g_2b2d6e0@0" ObjectIDZND1="g_2e57d10@0" ObjectIDZND2="4306@x" Pin0InfoVect0LinkObjId="g_2b2d6e0_0" Pin0InfoVect1LinkObjId="g_2e57d10_0" Pin0InfoVect2LinkObjId="SW-26677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26675_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-759 4670,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2cb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-855 4670,-818 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4213@0" ObjectIDZND0="4304@x" ObjectIDZND1="4305@x" Pin0InfoVect0LinkObjId="SW-26675_0" Pin0InfoVect1LinkObjId="SW-26676_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-855 4670,-818 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-818 4670,-795 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4213@0" ObjectIDND1="4305@x" ObjectIDZND0="4304@1" Pin0InfoVect0LinkObjId="SW-26675_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e07830_0" Pin1InfoVect1LinkObjId="SW-26676_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-818 4670,-795 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-720 4646,-720 4646,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_2b2d6e0@0" ObjectIDND1="4304@x" ObjectIDND2="4306@x" ObjectIDZND0="g_2e57d10@0" Pin0InfoVect0LinkObjId="g_2e57d10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b2d6e0_0" Pin1InfoVect1LinkObjId="SW-26675_0" Pin1InfoVect2LinkObjId="SW-26677_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-720 4646,-720 4646,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2d220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4698,-703 4698,-720 4670,-720 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b2d6e0@0" ObjectIDZND0="g_2e57d10@0" ObjectIDZND1="4304@x" ObjectIDZND2="4306@x" Pin0InfoVect0LinkObjId="g_2e57d10_0" Pin0InfoVect1LinkObjId="SW-26675_0" Pin0InfoVect2LinkObjId="SW-26677_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b2d6e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4698,-703 4698,-720 4670,-720 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b2d480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4670,-720 4670,-744 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="voltageTransformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b2d6e0@0" ObjectIDND1="g_2e57d10@0" ObjectIDZND0="4304@x" ObjectIDZND1="4306@x" Pin0InfoVect0LinkObjId="SW-26675_0" Pin0InfoVect1LinkObjId="SW-26677_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b2d6e0_0" Pin1InfoVect1LinkObjId="g_2e57d10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4670,-720 4670,-744 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3bea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4711,-530 4835,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10374@1" ObjectIDZND0="4214@0" Pin0InfoVect0LinkObjId="g_2a217b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4711,-530 4835,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3c100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4618,-530 4633,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10375@1" ObjectIDZND0="10399@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4618,-530 4633,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3c360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4660,-530 4675,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10399@0" ObjectIDZND0="10374@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4660,-530 4675,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2dc9930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3699,-595 3699,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_291b9c0@0" ObjectIDZND0="4249@0" Pin0InfoVect0LinkObjId="SW-26295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291b9c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3699,-595 3699,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcb140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-230 3656,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="12183@0" ObjectIDZND0="g_2dca6d0@1" Pin0InfoVect0LinkObjId="g_2dca6d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-230 3656,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcb370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3656,-293 3656,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2dca6d0@0" ObjectIDZND0="g_2887fe0@0" Pin0InfoVect0LinkObjId="g_2887fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dca6d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3656,-293 3656,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcc270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-217 3748,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15565@x" ObjectIDND1="4296@x" ObjectIDZND0="4295@0" Pin0InfoVect0LinkObjId="SW-26643_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb3_0" Pin1InfoVect1LinkObjId="SW-26644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-217 3748,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcc460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-217 3857,-247 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15563@x" ObjectIDND1="4285@x" ObjectIDZND0="4284@0" Pin0InfoVect0LinkObjId="SW-26610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb1_0" Pin1InfoVect1LinkObjId="SW-26611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-217 3857,-247 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcc650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3857,-217 3857,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="4284@x" ObjectIDND1="4285@x" ObjectIDZND0="15563@0" Pin0InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26610_0" Pin1InfoVect1LinkObjId="SW-26611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3857,-217 3857,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcc840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3748,-217 3748,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="capacitor" ObjectIDND0="4295@x" ObjectIDND1="4296@x" ObjectIDZND0="15565@0" Pin0InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb3_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26643_0" Pin1InfoVect1LinkObjId="SW-26644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3748,-217 3748,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dcddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-306 3967,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4278@1" ObjectIDZND0="4276@0" Pin0InfoVect0LinkObjId="SW-26570_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-306 3967,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dce020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-36 3967,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-36 3967,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dce280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-81 3967,-92 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-81 3967,-92 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dce4e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-119 3967,-131 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-119 3967,-131 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dce740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-259 3967,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2dcd4f0@0" ObjectIDZND0="4278@0" Pin0InfoVect0LinkObjId="SW-26572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dcd4f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-259 3967,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd6cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-339 4217,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4279@1" ObjectIDZND0="4280@0" Pin0InfoVect0LinkObjId="SW-26590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26589_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-339 4217,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd6f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-299 4217,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="4281@1" ObjectIDZND0="4279@0" Pin0InfoVect0LinkObjId="SW-26589_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26591_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-299 4217,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd7170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-245 4217,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2dcf450@0" ObjectIDZND0="4281@0" Pin0InfoVect0LinkObjId="SW-26591_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dcf450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-245 4217,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dd9bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-392 4217,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4280@1" ObjectIDZND0="4211@0" Pin0InfoVect0LinkObjId="g_286ccd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26590_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-392 4217,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2dda8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-35 4217,-71 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-35 4217,-71 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddaad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4217,-139 4217,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" ObjectIDND0="15936@0" ObjectIDZND0="g_2dcf450@1" Pin0InfoVect0LinkObjId="g_2dcf450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DY.CX_DY_LD061_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4217,-139 4217,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddb970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-345 4324,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4286@1" ObjectIDZND0="4287@0" Pin0InfoVect0LinkObjId="SW-26624_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26623_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-345 4324,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-307 4324,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2ddafe0@0" ObjectIDND1="4288@x" ObjectIDZND0="4286@0" Pin0InfoVect0LinkObjId="SW-26623_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ddafe0_0" Pin1InfoVect1LinkObjId="SW-26625_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-307 4324,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-143 4324,-161 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="15564@0" ObjectIDZND0="4289@x" ObjectIDZND1="4291@x" Pin0InfoVect0LinkObjId="SW-26626_0" Pin0InfoVect1LinkObjId="SW-26628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-143 4324,-161 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddc090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-161 4324,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15564@x" ObjectIDND1="4291@x" ObjectIDZND0="4289@0" Pin0InfoVect0LinkObjId="SW-26626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb2_0" Pin1InfoVect1LinkObjId="SW-26628_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-161 4324,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddc2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-209 4324,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4289@1" ObjectIDZND0="g_2ddafe0@0" ObjectIDZND1="4290@x" Pin0InfoVect0LinkObjId="g_2ddafe0_0" Pin0InfoVect1LinkObjId="SW-26627_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-209 4324,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddc550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-307 4324,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4286@x" ObjectIDND1="4288@x" ObjectIDZND0="g_2ddafe0@0" Pin0InfoVect0LinkObjId="g_2ddafe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26623_0" Pin1InfoVect1LinkObjId="SW-26625_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-307 4324,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ddc7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4324,-240 4324,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2ddafe0@1" ObjectIDZND0="4289@x" ObjectIDZND1="4290@x" Pin0InfoVect0LinkObjId="SW-26626_0" Pin0InfoVect1LinkObjId="SW-26627_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ddafe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4324,-240 4324,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de3790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-226 4459,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2df0ef0@0" ObjectIDND1="4300@x" ObjectIDZND0="4302@0" Pin0InfoVect0LinkObjId="SW-26661_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2df0ef0_0" Pin1InfoVect1LinkObjId="SW-26659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-226 4459,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de39f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-226 4507,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4302@1" ObjectIDZND0="g_2de3c50@0" Pin0InfoVect0LinkObjId="g_2de3c50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26661_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-226 4507,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de6c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-308 4459,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="4297@x" ObjectIDND1="g_2df0ef0@0" ObjectIDZND0="4299@0" Pin0InfoVect0LinkObjId="SW-26658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26656_0" Pin1InfoVect1LinkObjId="g_2df0ef0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-308 4459,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2de6e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-308 4507,-308 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4299@1" ObjectIDZND0="g_2de70d0@0" Pin0InfoVect0LinkObjId="g_2de70d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26658_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-308 4507,-308 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2dec890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-162 4459,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15566@x" ObjectIDND1="4300@x" ObjectIDZND0="4301@0" Pin0InfoVect0LinkObjId="SW-26660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb4_0" Pin1InfoVect1LinkObjId="SW-26659_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-162 4459,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2decaf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4495,-162 4507,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4301@1" ObjectIDZND0="g_2decd50@0" Pin0InfoVect0LinkObjId="g_2decd50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4495,-162 4507,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2deffe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,-37 4448,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="4303@1" Pin0InfoVect0LinkObjId="SW-26662_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,-37 4448,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df0240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4448,13 4449,40 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4303@0" ObjectIDZND0="g_2df04a0@0" Pin0InfoVect0LinkObjId="g_2df04a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26662_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4448,13 4449,40 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df1c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-346 4442,-358 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="4297@1" ObjectIDZND0="4298@0" Pin0InfoVect0LinkObjId="SW-26657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26656_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-346 4442,-358 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df1ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-308 4442,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="g_2df0ef0@0" ObjectIDND1="4299@x" ObjectIDZND0="4297@0" Pin0InfoVect0LinkObjId="SW-26656_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2df0ef0_0" Pin1InfoVect1LinkObjId="SW-26658_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-308 4442,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df2100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-144 4442,-162 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="15566@0" ObjectIDZND0="4300@x" ObjectIDZND1="4301@x" Pin0InfoVect0LinkObjId="SW-26659_0" Pin0InfoVect1LinkObjId="SW-26660_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb4_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-144 4442,-162 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df2360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-162 4442,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="15566@x" ObjectIDND1="4301@x" ObjectIDZND0="4300@0" Pin0InfoVect0LinkObjId="SW-26659_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="CB-CX_DY.CX_DY_Cb4_0" Pin1InfoVect1LinkObjId="SW-26660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-162 4442,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df25c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-210 4442,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4300@1" ObjectIDZND0="g_2df0ef0@0" ObjectIDZND1="4302@x" Pin0InfoVect0LinkObjId="g_2df0ef0_0" Pin0InfoVect1LinkObjId="SW-26661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26659_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-210 4442,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df2820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-308 4442,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4297@x" ObjectIDND1="4299@x" ObjectIDZND0="g_2df0ef0@0" Pin0InfoVect0LinkObjId="g_2df0ef0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26656_0" Pin1InfoVect1LinkObjId="SW-26658_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-308 4442,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df2a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-241 4442,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2df0ef0@1" ObjectIDZND0="4300@x" ObjectIDZND1="4302@x" Pin0InfoVect0LinkObjId="SW-26659_0" Pin0InfoVect1LinkObjId="SW-26661_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2df0ef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-241 4442,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2df2ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4442,-394 4442,-404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4298@1" ObjectIDZND0="4211@0" Pin0InfoVect0LinkObjId="g_286ccd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4442,-394 4442,-404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2df8fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-498 4205,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2878cb0@0" ObjectIDND1="g_2df91c0@0" ObjectIDND2="4252@x" ObjectIDZND0="4267@0" Pin0InfoVect0LinkObjId="SW-26440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2878cb0_0" Pin1InfoVect1LinkObjId="g_2df91c0_0" Pin1InfoVect2LinkObjId="SW-26299_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-498 4205,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfa600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3958,-498 4033,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="4252@1" ObjectIDZND0="g_2df91c0@0" ObjectIDZND1="g_2878cb0@0" ObjectIDZND2="4267@x" Pin0InfoVect0LinkObjId="g_2df91c0_0" Pin0InfoVect1LinkObjId="g_2878cb0_0" Pin0InfoVect2LinkObjId="SW-26440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26299_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3958,-498 4033,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dfa860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4133,-498 4033,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2878cb0@0" ObjectIDND1="4267@x" ObjectIDZND0="g_2df91c0@0" ObjectIDZND1="4252@x" Pin0InfoVect0LinkObjId="g_2df91c0_0" Pin0InfoVect1LinkObjId="SW-26299_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2878cb0_0" Pin1InfoVect1LinkObjId="SW-26440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4133,-498 4033,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2dfb9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-675 4366,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4312@x" ObjectIDND1="4260@x" ObjectIDZND0="4262@0" Pin0InfoVect0LinkObjId="SW-26430_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28787f0_0" Pin1InfoVect1LinkObjId="SW-26428_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-675 4366,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2dfbbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4402,-675 4416,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="4262@1" ObjectIDZND0="g_2dfbdd0@0" Pin0InfoVect0LinkObjId="g_2dfbdd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4402,-675 4416,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2dff9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-600 4241,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2956aa0@0" ObjectIDZND0="4263@0" Pin0InfoVect0LinkObjId="SW-26434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2956aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-600 4241,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e069a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-652 3802,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" ObjectIDND0="g_291c3f0@0" ObjectIDND1="g_291cfa0@0" ObjectIDND2="4249@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_291c3f0_0" Pin1InfoVect1LinkObjId="g_291cfa0_0" Pin1InfoVect2LinkObjId="SW-26295_0" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-652 3802,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e06b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-644 3727,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_291cfa0@0" ObjectIDZND0="g_291c3f0@0" ObjectIDZND1="4249@x" Pin0InfoVect0LinkObjId="g_291c3f0_0" Pin0InfoVect1LinkObjId="SW-26295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291cfa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-644 3727,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e06d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-652 3699,-652 3699,-642 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_291cfa0@0" ObjectIDND1="g_291c3f0@0" ObjectIDZND0="4249@1" Pin0InfoVect0LinkObjId="SW-26295_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_291cfa0_0" Pin1InfoVect1LinkObjId="g_291c3f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-652 3699,-652 3699,-642 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e06f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-644 3755,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_291c3f0@0" ObjectIDZND0="g_291cfa0@0" ObjectIDZND1="4249@x" Pin0InfoVect0LinkObjId="g_291cfa0_0" Pin0InfoVect1LinkObjId="SW-26295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291c3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-644 3755,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e071a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3755,-652 3726,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_291c3f0@0" ObjectIDZND0="g_291cfa0@0" ObjectIDZND1="4249@x" Pin0InfoVect0LinkObjId="g_291cfa0_0" Pin0InfoVect1LinkObjId="SW-26295_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_291c3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3755,-652 3726,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e073d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4437,-626 4437,-686 4452,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4312@x" ObjectIDND1="4266@x" ObjectIDZND0="4307@0" Pin0InfoVect0LinkObjId="SW-26678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28787f0_0" Pin1InfoVect1LinkObjId="SW-26439_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4437,-626 4437,-686 4452,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e07600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4488,-686 4504,-686 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4307@1" ObjectIDZND0="g_2853d70@0" ObjectIDZND1="g_2855030@0" Pin0InfoVect0LinkObjId="g_2853d70_0" Pin0InfoVect1LinkObjId="g_2855030_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4488,-686 4504,-686 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e07830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-832 4348,-855 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="4258@1" ObjectIDZND0="4213@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26426_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-832 4348,-855 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e07a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-784 4348,-796 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4257@x" ObjectIDND1="4259@x" ObjectIDZND0="4258@0" Pin0InfoVect0LinkObjId="SW-26426_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26422_0" Pin1InfoVect1LinkObjId="SW-26427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-784 4348,-796 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e07c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-784 4348,-773 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4258@x" ObjectIDND1="4259@x" ObjectIDZND0="4257@1" Pin0InfoVect0LinkObjId="SW-26422_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26426_0" Pin1InfoVect1LinkObjId="SW-26427_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-784 4348,-773 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e07ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-736 4348,-746 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="4260@x" ObjectIDND1="4261@x" ObjectIDZND0="4257@0" Pin0InfoVect0LinkObjId="SW-26422_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26428_0" Pin1InfoVect1LinkObjId="SW-26429_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-736 4348,-746 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e08120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-724 4348,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="4260@1" ObjectIDZND0="4257@x" ObjectIDZND1="4261@x" Pin0InfoVect0LinkObjId="SW-26422_0" Pin0InfoVect1LinkObjId="SW-26429_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26428_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-724 4348,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-233 4892,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4215@0" ObjectIDZND0="10385@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-233 4892,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1be60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4928,-233 4955,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10385@1" ObjectIDZND0="10380@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4928,-233 4955,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1c050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-233 5055,-259 5069,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e529b0@0" ObjectIDND1="10386@x" ObjectIDND2="11889@x" ObjectIDZND0="10398@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e529b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-233 5055,-259 5069,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1c280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5105,-259 5117,-259 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10398@1" ObjectIDZND0="g_28d5360@0" Pin0InfoVect0LinkObjId="g_28d5360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5105,-259 5117,-259 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e1c4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-233 5055,-207 5072,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2e529b0@0" ObjectIDND1="10386@x" ObjectIDND2="10398@x" ObjectIDZND0="11889@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e529b0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-233 5055,-207 5072,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3afc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4835,-387 4736,-387 4736,-626 4655,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="4215@0" ObjectIDZND0="4265@1" Pin0InfoVect0LinkObjId="SW-26438_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4835,-387 4736,-387 4736,-626 4655,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4404,-626 4437,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4312@0" ObjectIDZND0="4266@x" ObjectIDZND1="4307@x" Pin0InfoVect0LinkObjId="SW-26439_0" Pin0InfoVect1LinkObjId="SW-26678_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28787f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4404,-626 4437,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3d5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4437,-626 4506,-626 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4312@x" ObjectIDND1="4307@x" ObjectIDZND0="4266@0" Pin0InfoVect0LinkObjId="SW-26439_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28787f0_0" Pin1InfoVect1LinkObjId="SW-26678_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4437,-626 4506,-626 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3e630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-531 4431,-486 4449,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="4251@x" ObjectIDND1="10375@x" ObjectIDZND0="10400@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26298_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-531 4431,-486 4449,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3e820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4772,-960 4757,-960 4757,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="10377@0" ObjectIDZND0="10378@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4772,-960 4757,-960 4757,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3ea10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-930 4757,-930 4757,-960 4737,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b29170@0" ObjectIDZND0="g_2b29f20@0" Pin0InfoVect0LinkObjId="g_2b29f20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b29170_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-930 4757,-930 4757,-960 4737,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e3ec00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4241,-647 4241,-654 4271,-654 4271,-651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="4263@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4241,-647 4241,-654 4271,-654 4271,-651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e43b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-569 4335,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="4312@x" ObjectIDND1="4270@x" ObjectIDZND0="g_2e43090@0" Pin0InfoVect0LinkObjId="g_2e43090_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28787f0_0" Pin1InfoVect1LinkObjId="SW-26443_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-569 4335,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e43dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-582 4347,-569 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="4312@2" ObjectIDZND0="g_2e43090@0" ObjectIDZND1="4270@x" Pin0InfoVect0LinkObjId="g_2e43090_0" Pin0InfoVect1LinkObjId="SW-26443_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28787f0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-582 4347,-569 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e44020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-569 4347,-543 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2e43090@0" ObjectIDND1="4312@x" ObjectIDZND0="4270@1" Pin0InfoVect0LinkObjId="SW-26443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e43090_0" Pin1InfoVect1LinkObjId="g_28787f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-569 4347,-543 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4b2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-232 3569,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2e4aa30@0" ObjectIDZND0="g_2e48230@0" Pin0InfoVect0LinkObjId="g_2e48230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4aa30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-232 3569,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4c5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3604,-293 3604,-310 3569,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2e4b510@0" ObjectIDZND0="g_2e4aa30@0" ObjectIDZND1="4310@x" Pin0InfoVect0LinkObjId="g_2e4aa30_0" Pin0InfoVect1LinkObjId="SW-26681_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4b510_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3604,-293 3604,-310 3569,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4c850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-352 3569,-310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4310@0" ObjectIDZND0="g_2e4b510@0" ObjectIDZND1="g_2e4aa30@0" Pin0InfoVect0LinkObjId="g_2e4b510_0" Pin0InfoVect1LinkObjId="g_2e4aa30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26681_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-352 3569,-310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2e4cab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3569,-310 3569,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e4b510@0" ObjectIDND1="4310@x" ObjectIDZND0="g_2e4aa30@1" Pin0InfoVect0LinkObjId="g_2e4aa30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e4b510_0" Pin1InfoVect1LinkObjId="SW-26681_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3569,-310 3569,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4f1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-927 5120,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_28cbc30@0" ObjectIDZND0="g_2a2a8a0@0" ObjectIDZND1="10395@x" Pin0InfoVect0LinkObjId="g_2a2a8a0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28cbc30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-927 5120,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4f430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5110,-957 5120,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10395@1" ObjectIDZND0="g_28cbc30@0" ObjectIDZND1="g_2a2a8a0@0" Pin0InfoVect0LinkObjId="g_28cbc30_0" Pin0InfoVect1LinkObjId="g_2a2a8a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5110,-957 5120,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4f690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5120,-957 5129,-957 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_28cbc30@0" ObjectIDND1="10395@x" ObjectIDZND0="g_2a2a8a0@1" Pin0InfoVect0LinkObjId="g_2a2a8a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28cbc30_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5120,-957 5129,-957 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e4f8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-607 5129,-571 5136,-571 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="15407@x" ObjectIDND1="0@x" ObjectIDND2="10390@x" ObjectIDZND0="10397@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_DY.LD_342_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-607 5129,-571 5136,-571 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e50900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-759 5060,-733 5063,-733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_28bfef0@0" ObjectIDND1="15406@x" ObjectIDND2="10392@x" ObjectIDZND0="g_2e4fb50@0" Pin0InfoVect0LinkObjId="g_2e4fb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_28bfef0_0" Pin1InfoVect1LinkObjId="EC-CX_DY.LD_341_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-759 5060,-733 5063,-733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e50b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5068,-759 5060,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="g_28bfef0@1" ObjectIDZND0="g_2e4fb50@0" ObjectIDZND1="15406@x" ObjectIDZND2="10392@x" Pin0InfoVect0LinkObjId="g_2e4fb50_0" Pin0InfoVect1LinkObjId="EC-CX_DY.LD_341_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28bfef0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5068,-759 5060,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e50dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5060,-759 5060,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_28bfef0@0" ObjectIDND1="g_2e4fb50@0" ObjectIDZND0="15406@x" ObjectIDZND1="10392@x" ObjectIDZND2="10396@x" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_341_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_28bfef0_0" Pin1InfoVect1LinkObjId="g_2e4fb50_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5060,-759 5060,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e51020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4980,-607 4989,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="10382@0" ObjectIDZND0="g_28c7160@0" ObjectIDZND1="10390@x" Pin0InfoVect0LinkObjId="g_28c7160_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4980,-607 4989,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e51280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4989,-607 5003,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="10382@x" ObjectIDND1="g_28c7160@0" ObjectIDZND0="10390@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_28c7160_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4989,-607 5003,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e52290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5124,-174 5116,-174 5116,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2e514e0@0" ObjectIDZND0="g_2b22b80@0" ObjectIDZND1="11889@x" Pin0InfoVect0LinkObjId="g_2b22b80_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e514e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5124,-174 5116,-174 5116,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e524f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5108,-207 5116,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="11889@1" ObjectIDZND0="g_2e514e0@0" ObjectIDZND1="g_2b22b80@0" Pin0InfoVect0LinkObjId="g_2e514e0_0" Pin0InfoVect1LinkObjId="g_2b22b80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5108,-207 5116,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e52750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5116,-207 5124,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e514e0@0" ObjectIDND1="11889@x" ObjectIDZND0="g_2b22b80@1" Pin0InfoVect0LinkObjId="g_2b22b80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e514e0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5116,-207 5124,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e53430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5055,-233 5103,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="10386@x" ObjectIDND1="11889@x" ObjectIDND2="10398@x" ObjectIDZND0="g_2e529b0@1" Pin0InfoVect0LinkObjId="g_2e529b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5055,-233 5103,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e53690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5178,-233 5241,-233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2e529b0@0" ObjectIDZND0="15409@0" Pin0InfoVect0LinkObjId="EC-CX_DY.LD_345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e529b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5178,-233 5241,-233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e538f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-1108 3595,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDND2="g_28ac810@0" ObjectIDZND0="11550@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="g_28ac810_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-1108 3595,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e53b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-1040 3595,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDZND0="g_28ac810@0" ObjectIDZND1="g_296ba30@0" ObjectIDZND2="11550@1" Pin0InfoVect0LinkObjId="g_28ac810_0" Pin0InfoVect1LinkObjId="g_296ba30_0" Pin0InfoVect2LinkObjId="g_2e538f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-1040 3595,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e53db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3595,-1108 3708,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4218@x" ObjectIDND1="4221@x" ObjectIDND2="11550@1" ObjectIDZND0="g_28ac810@0" ObjectIDZND1="g_296ba30@0" Pin0InfoVect0LinkObjId="g_28ac810_0" Pin0InfoVect1LinkObjId="g_296ba30_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26186_0" Pin1InfoVect1LinkObjId="SW-26189_0" Pin1InfoVect2LinkObjId="g_2e538f0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3595,-1108 3708,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e54510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4231,-1108 4231,-1141 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDND2="g_2978540@0" ObjectIDZND0="11716@1" Pin0InfoVect0LinkObjId="g_2976c70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="g_2978540_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4231,-1108 4231,-1141 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e54770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-1041 4232,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDZND0="11716@1" ObjectIDZND1="g_2978540@0" ObjectIDZND2="g_29775f0@0" Pin0InfoVect0LinkObjId="g_2976c70_1" Pin0InfoVect1LinkObjId="g_2978540_0" Pin0InfoVect2LinkObjId="g_29775f0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-1041 4232,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e549d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4232,-1109 4345,-1109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4234@x" ObjectIDND1="4237@x" ObjectIDND2="11716@1" ObjectIDZND0="g_2978540@0" ObjectIDZND1="g_29775f0@0" Pin0InfoVect0LinkObjId="g_2978540_0" Pin0InfoVect1LinkObjId="g_29775f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26238_0" Pin1InfoVect1LinkObjId="SW-26241_0" Pin1InfoVect2LinkObjId="g_2976c70_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4232,-1109 4345,-1109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e54ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-1109 4513,-1142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDND2="g_29032c0@0" ObjectIDZND0="11715@1" Pin0InfoVect0LinkObjId="g_2860380_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="g_29032c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-1109 4513,-1142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e55130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-1040 4513,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDZND0="11715@1" ObjectIDZND1="g_29032c0@0" ObjectIDZND2="g_2860d00@0" Pin0InfoVect0LinkObjId="g_2860380_1" Pin0InfoVect1LinkObjId="g_29032c0_0" Pin0InfoVect2LinkObjId="g_2860d00_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-1040 4513,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e55390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4513,-1108 4626,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4240@x" ObjectIDND1="4243@x" ObjectIDND2="11715@1" ObjectIDZND0="g_29032c0@0" ObjectIDZND1="g_2860d00@0" Pin0InfoVect0LinkObjId="g_29032c0_0" Pin0InfoVect1LinkObjId="g_2860d00_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26258_0" Pin1InfoVect1LinkObjId="SW-26261_0" Pin1InfoVect2LinkObjId="g_2860380_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4513,-1108 4626,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e555f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5171,-571 5171,-546 5180,-546 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2a208a0@0" Pin0InfoVect0LinkObjId="g_2a208a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5171,-571 5171,-546 5180,-546 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e5a1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-654 4298,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2958080@0" ObjectIDZND0="g_29574d0@0" Pin0InfoVect0LinkObjId="g_29574d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2958080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-654 4298,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2e5a430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-654 4298,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2958080@0" ObjectIDZND0="g_29574d0@0" Pin0InfoVect0LinkObjId="g_29574d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2958080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-654 4298,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f54110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4298,-654 4344,-654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_2958080@0" ObjectIDND1="g_29574d0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2958080_0" Pin1InfoVect1LinkObjId="g_29574d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4298,-654 4344,-654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f54b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-688 4348,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="4260@0" ObjectIDZND0="4312@x" ObjectIDZND1="4262@x" Pin0InfoVect0LinkObjId="g_28787f0_0" Pin0InfoVect1LinkObjId="SW-26430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-26428_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-688 4348,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f54d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4348,-669 4348,-675 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="4312@1" ObjectIDZND0="4260@x" ObjectIDZND1="4262@x" Pin0InfoVect0LinkObjId="SW-26428_0" Pin0InfoVect1LinkObjId="SW-26430_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_28787f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4348,-669 4348,-675 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f57a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3912,-185 3912,-197 3967,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2e47150@0" ObjectIDZND0="g_2dcd4f0@0" ObjectIDZND1="15935@x" Pin0InfoVect0LinkObjId="g_2dcd4f0_0" Pin0InfoVect1LinkObjId="EC-CX_DY.CX_DY_LD051_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e47150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3912,-185 3912,-197 3967,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f58550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-206 3967,-197 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2dcd4f0@1" ObjectIDZND0="g_2e47150@0" ObjectIDZND1="15935@x" Pin0InfoVect0LinkObjId="g_2e47150_0" Pin0InfoVect1LinkObjId="EC-CX_DY.CX_DY_LD051_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2dcd4f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-206 3967,-197 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2f587b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-197 3967,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2e47150@0" ObjectIDND1="g_2dcd4f0@0" ObjectIDZND0="15935@0" Pin0InfoVect0LinkObjId="EC-CX_DY.CX_DY_LD051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e47150_0" Pin1InfoVect1LinkObjId="g_2dcd4f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-197 3967,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f592a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4582,-530 4431,-530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10375@0" ObjectIDZND0="4251@x" ObjectIDZND1="10400@x" Pin0InfoVect0LinkObjId="SW-26298_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4582,-530 4431,-530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f59500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-530 4431,-555 4199,-555 4199,-630 4147,-630 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10400@x" ObjectIDND1="10375@x" ObjectIDZND0="4251@1" Pin0InfoVect0LinkObjId="SW-26298_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-530 4431,-555 4199,-555 4199,-630 4147,-630 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f670e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-1108 3825,-1140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDND2="g_29ccab0@0" ObjectIDZND0="9192@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="g_29ccab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-1108 3825,-1140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f67bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-1040 3825,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDZND0="g_29ccab0@0" ObjectIDZND1="g_29cbeb0@0" ObjectIDZND2="9192@1" Pin0InfoVect0LinkObjId="g_29ccab0_0" Pin0InfoVect1LinkObjId="g_29cbeb0_0" Pin0InfoVect2LinkObjId="g_2f670e0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-1040 3825,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2f67e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3825,-1108 3938,-1108 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="4225@x" ObjectIDND1="4227@x" ObjectIDND2="9192@1" ObjectIDZND0="g_29ccab0@0" ObjectIDZND1="g_29cbeb0@0" Pin0InfoVect0LinkObjId="g_29ccab0_0" Pin0InfoVect1LinkObjId="g_29cbeb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-26207_0" Pin1InfoVect1LinkObjId="SW-26209_0" Pin1InfoVect2LinkObjId="g_2f670e0_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3825,-1108 3938,-1108 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f72820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4900,-983 4960,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10393@1" ObjectIDZND0="10384@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4900,-983 4960,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f74eb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5101,-605 5101,-631 5115,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="10390@x" ObjectIDND1="10397@x" ObjectIDND2="15407@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="EC-CX_DY.LD_342_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5101,-605 5101,-631 5115,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2f75110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5151,-631 5163,-631 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2f75370@0" Pin0InfoVect0LinkObjId="g_2f75370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5151,-631 5163,-631 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f762f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5039,-607 5101,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="load" ObjectIDND0="10390@1" ObjectIDZND0="0@x" ObjectIDZND1="10397@x" ObjectIDZND2="15407@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="EC-CX_DY.LD_342_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5039,-607 5101,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f76c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5248,-607 5129,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="15407@0" ObjectIDZND0="10397@x" ObjectIDZND1="0@x" ObjectIDZND2="10390@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DY.LD_342_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5248,-607 5129,-607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2f76e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5129,-607 5101,-607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10397@x" ObjectIDND1="15407@x" ObjectIDZND0="0@x" ObjectIDZND1="10390@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="EC-CX_DY.LD_342_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5129,-607 5101,-607 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-26" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3383.000000 -1107.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="26" ObjectName="DYN-CX_DY"/>
     <cge:Meas_Ref ObjectId="26"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="4492" cy="-1138" fill="rgb(0,0,0)" fillStyle="1" r="10" stroke="rgb(255,255,255)" stroke-width="1.75238"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YM" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuanda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3595,-1139 3595,-1185 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11550" ObjectName="AC-110kV.yuanda_line"/>
    <cge:TPSR_Ref TObjectID="11550_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="3595,-1139 3595,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YPJ" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yuda_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4231,-1139 4231,-1185 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11716" ObjectName="AC-110kV.yuda_line"/>
    <cge:TPSR_Ref TObjectID="11716_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="4231,-1139 4231,-1185 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DY" endPointId="0" endStationName="PAS_XN" flowDrawDirect="1" flowShape="0" id="AC-110kV.daliu_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4513,-1140 4513,-1186 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11715" ObjectName="AC-110kV.daliu_line"/>
    <cge:TPSR_Ref TObjectID="11715_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="4513,-1140 4513,-1186 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="CX_DY" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaodaTdayao_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3825,-1138 3825,-1183 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9192" ObjectName="AC-110kV.yaodaTdayao_line"/>
    <cge:TPSR_Ref TObjectID="9192_SS-26"/></metadata>
   <polyline fill="none" opacity="0" points="3825,-1138 3825,-1183 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3609" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4670" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3825" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="4005" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4120" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3595" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4513" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4232" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4212" cx="3802" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4213" cx="4348" cy="-855" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3656" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3569" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3748" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3857" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3967" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="3967" cy="-36" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4211" cx="4563" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4210" cx="3801" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4211" cx="4324" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4211" cx="4347" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4217" cy="-35" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4211" cx="4217" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4211" cx="4442" cy="-404" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4025" cy="-36" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="4182" cy="-35" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-785" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4215" cx="4835" cy="-233" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-608" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-960" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-530" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-485" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4215" cx="4835" cy="-417" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4215" cx="4835" cy="-387" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4215" cx="4835" cy="-352" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="4214" cx="4835" cy="-983" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-26566">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -865.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4272" ObjectName="SW-CX_DY.CX_DY_1121SW"/>
     <cge:Meas_Ref ObjectId="26566"/>
    <cge:TPSR_Ref TObjectID="4272"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26568">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -868.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4274" ObjectName="SW-CX_DY.CX_DY_1122SW"/>
     <cge:Meas_Ref ObjectId="26568"/>
    <cge:TPSR_Ref TObjectID="4274"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26569">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4275" ObjectName="SW-CX_DY.CX_DY_11227SW"/>
     <cge:Meas_Ref ObjectId="26569"/>
    <cge:TPSR_Ref TObjectID="4275"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26567">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 -942.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4273" ObjectName="SW-CX_DY.CX_DY_11217SW"/>
     <cge:Meas_Ref ObjectId="26567"/>
    <cge:TPSR_Ref TObjectID="4273"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26185">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4217" ObjectName="SW-CX_DY.CX_DY_1511SW"/>
     <cge:Meas_Ref ObjectId="26185"/>
    <cge:TPSR_Ref TObjectID="4217"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26186">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3586.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4218" ObjectName="SW-CX_DY.CX_DY_1516SW"/>
     <cge:Meas_Ref ObjectId="26186"/>
    <cge:TPSR_Ref TObjectID="4218"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26189">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4221" ObjectName="SW-CX_DY.CX_DY_15167SW"/>
     <cge:Meas_Ref ObjectId="26189"/>
    <cge:TPSR_Ref TObjectID="4221"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26188">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4220" ObjectName="SW-CX_DY.CX_DY_15160SW"/>
     <cge:Meas_Ref ObjectId="26188"/>
    <cge:TPSR_Ref TObjectID="4220"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26205">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4223" ObjectName="SW-CX_DY.CX_DY_1521SW"/>
     <cge:Meas_Ref ObjectId="26205"/>
    <cge:TPSR_Ref TObjectID="4223"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26207">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3816.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4225" ObjectName="SW-CX_DY.CX_DY_1526SW"/>
     <cge:Meas_Ref ObjectId="26207"/>
    <cge:TPSR_Ref TObjectID="4225"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26209">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4227" ObjectName="SW-CX_DY.CX_DY_15267SW"/>
     <cge:Meas_Ref ObjectId="26209"/>
    <cge:TPSR_Ref TObjectID="4227"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26206">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4224" ObjectName="SW-CX_DY.CX_DY_15217SW"/>
     <cge:Meas_Ref ObjectId="26206"/>
    <cge:TPSR_Ref TObjectID="4224"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26208">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3837.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4226" ObjectName="SW-CX_DY.CX_DY_15260SW"/>
     <cge:Meas_Ref ObjectId="26208"/>
    <cge:TPSR_Ref TObjectID="4226"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26187">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3607.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4219" ObjectName="SW-CX_DY.CX_DY_15117SW"/>
     <cge:Meas_Ref ObjectId="26187"/>
    <cge:TPSR_Ref TObjectID="4219"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26212">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 -740.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4230" ObjectName="SW-CX_DY.CX_DY_19017SW"/>
     <cge:Meas_Ref ObjectId="26212"/>
    <cge:TPSR_Ref TObjectID="4230"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26211">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3622.000000 -814.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4229" ObjectName="SW-CX_DY.CX_DY_19010SW"/>
     <cge:Meas_Ref ObjectId="26211"/>
    <cge:TPSR_Ref TObjectID="4229"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26210">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3600.000000 -755.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4228" ObjectName="SW-CX_DY.CX_DY_1901SW"/>
     <cge:Meas_Ref ObjectId="26210"/>
    <cge:TPSR_Ref TObjectID="4228"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26257">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4239" ObjectName="SW-CX_DY.CX_DY_1542SW"/>
     <cge:Meas_Ref ObjectId="26257"/>
    <cge:TPSR_Ref TObjectID="4239"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26258">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4504.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4240" ObjectName="SW-CX_DY.CX_DY_1546SW"/>
     <cge:Meas_Ref ObjectId="26258"/>
    <cge:TPSR_Ref TObjectID="4240"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26261">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4526.000000 -1035.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4243" ObjectName="SW-CX_DY.CX_DY_15467SW"/>
     <cge:Meas_Ref ObjectId="26261"/>
    <cge:TPSR_Ref TObjectID="4243"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26259">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -916.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4241" ObjectName="SW-CX_DY.CX_DY_15427SW"/>
     <cge:Meas_Ref ObjectId="26259"/>
    <cge:TPSR_Ref TObjectID="4241"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26260">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4525.000000 -970.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4242" ObjectName="SW-CX_DY.CX_DY_15460SW"/>
     <cge:Meas_Ref ObjectId="26260"/>
    <cge:TPSR_Ref TObjectID="4242"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26237">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -864.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4233" ObjectName="SW-CX_DY.CX_DY_1532SW"/>
     <cge:Meas_Ref ObjectId="26237"/>
    <cge:TPSR_Ref TObjectID="4233"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26238">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -985.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4234" ObjectName="SW-CX_DY.CX_DY_1536SW"/>
     <cge:Meas_Ref ObjectId="26238"/>
    <cge:TPSR_Ref TObjectID="4234"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26241">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4245.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4237" ObjectName="SW-CX_DY.CX_DY_15367SW"/>
     <cge:Meas_Ref ObjectId="26241"/>
    <cge:TPSR_Ref TObjectID="4237"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26239">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -917.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4235" ObjectName="SW-CX_DY.CX_DY_15327SW"/>
     <cge:Meas_Ref ObjectId="26239"/>
    <cge:TPSR_Ref TObjectID="4235"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26240">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -971.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4236" ObjectName="SW-CX_DY.CX_DY_15360SW"/>
     <cge:Meas_Ref ObjectId="26240"/>
    <cge:TPSR_Ref TObjectID="4236"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26295">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3690.000000 -601.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4249" ObjectName="SW-CX_DY.CX_DY_1010SW"/>
     <cge:Meas_Ref ObjectId="26295"/>
    <cge:TPSR_Ref TObjectID="4249"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26288">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -804.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4245" ObjectName="SW-CX_DY.CX_DY_1011SW"/>
     <cge:Meas_Ref ObjectId="26288"/>
    <cge:TPSR_Ref TObjectID="4245"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26290">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3793.000000 -681.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4247" ObjectName="SW-CX_DY.CX_DY_1016SW"/>
     <cge:Meas_Ref ObjectId="26290"/>
    <cge:TPSR_Ref TObjectID="4247"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26289">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4246" ObjectName="SW-CX_DY.CX_DY_10117SW"/>
     <cge:Meas_Ref ObjectId="26289"/>
    <cge:TPSR_Ref TObjectID="4246"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26291">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3814.000000 -730.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4248" ObjectName="SW-CX_DY.CX_DY_10160SW"/>
     <cge:Meas_Ref ObjectId="26291"/>
    <cge:TPSR_Ref TObjectID="4248"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26434">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4232.000000 -606.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4263" ObjectName="SW-CX_DY.CX_DY_1020SW"/>
     <cge:Meas_Ref ObjectId="26434"/>
    <cge:TPSR_Ref TObjectID="4263"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26426">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -791.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4258" ObjectName="SW-CX_DY.CX_DY_1022SW"/>
     <cge:Meas_Ref ObjectId="26426"/>
    <cge:TPSR_Ref TObjectID="4258"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26428">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4339.000000 -683.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4260" ObjectName="SW-CX_DY.CX_DY_1026SW"/>
     <cge:Meas_Ref ObjectId="26428"/>
    <cge:TPSR_Ref TObjectID="4260"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26427">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -779.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4259" ObjectName="SW-CX_DY.CX_DY_10217SW"/>
     <cge:Meas_Ref ObjectId="26427"/>
    <cge:TPSR_Ref TObjectID="4259"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26429">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -731.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4261" ObjectName="SW-CX_DY.CX_DY_10260SW"/>
     <cge:Meas_Ref ObjectId="26429"/>
    <cge:TPSR_Ref TObjectID="4261"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -349.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4311" ObjectName="SW-CX_DY.CX_DY_0541SW"/>
     <cge:Meas_Ref ObjectId="26682"/>
    <cge:TPSR_Ref TObjectID="4311"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26681">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3560.000000 -347.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4310" ObjectName="SW-CX_DY.CX_DY_0901SW"/>
     <cge:Meas_Ref ObjectId="26681"/>
    <cge:TPSR_Ref TObjectID="4310"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4034.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4132.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4294" ObjectName="SW-CX_DY.CX_DY_0531SW"/>
     <cge:Meas_Ref ObjectId="26642"/>
    <cge:TPSR_Ref TObjectID="4294"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3739.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4295" ObjectName="SW-CX_DY.CX_DY_0536SW"/>
     <cge:Meas_Ref ObjectId="26643"/>
    <cge:TPSR_Ref TObjectID="4295"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4296" ObjectName="SW-CX_DY.CX_DY_05367SW"/>
     <cge:Meas_Ref ObjectId="26644"/>
    <cge:TPSR_Ref TObjectID="4296"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26609">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4283" ObjectName="SW-CX_DY.CX_DY_0521SW"/>
     <cge:Meas_Ref ObjectId="26609"/>
    <cge:TPSR_Ref TObjectID="4283"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26610">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -242.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4284" ObjectName="SW-CX_DY.CX_DY_0526SW"/>
     <cge:Meas_Ref ObjectId="26610"/>
    <cge:TPSR_Ref TObjectID="4284"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26611">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3869.000000 -212.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4285" ObjectName="SW-CX_DY.CX_DY_05267SW"/>
     <cge:Meas_Ref ObjectId="26611"/>
    <cge:TPSR_Ref TObjectID="4285"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26571">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4277" ObjectName="SW-CX_DY.CX_DY_0511SW"/>
     <cge:Meas_Ref ObjectId="26571"/>
    <cge:TPSR_Ref TObjectID="4277"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26572">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -265.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4278" ObjectName="SW-CX_DY.CX_DY_0516SW"/>
     <cge:Meas_Ref ObjectId="26572"/>
    <cge:TPSR_Ref TObjectID="4278"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -126.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -40.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26679">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4554.000000 -343.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4308" ObjectName="SW-CX_DY.CX_DY_0902SW"/>
     <cge:Meas_Ref ObjectId="26679"/>
    <cge:TPSR_Ref TObjectID="4308"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26303">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -513.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4256" ObjectName="SW-CX_DY.CX_DY_0016SW"/>
     <cge:Meas_Ref ObjectId="26303"/>
    <cge:TPSR_Ref TObjectID="4256"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26302">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3792.000000 -413.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4255" ObjectName="SW-CX_DY.CX_DY_0011SW"/>
     <cge:Meas_Ref ObjectId="26302"/>
    <cge:TPSR_Ref TObjectID="4255"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26624">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -352.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4287" ObjectName="SW-CX_DY.CX_DY_0622SW"/>
     <cge:Meas_Ref ObjectId="26624"/>
    <cge:TPSR_Ref TObjectID="4287"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26627">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -220.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4290" ObjectName="SW-CX_DY.CX_DY_06260SW"/>
     <cge:Meas_Ref ObjectId="26627"/>
    <cge:TPSR_Ref TObjectID="4290"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26625">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -302.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4288" ObjectName="SW-CX_DY.CX_DY_06227SW"/>
     <cge:Meas_Ref ObjectId="26625"/>
    <cge:TPSR_Ref TObjectID="4288"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26626">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4289" ObjectName="SW-CX_DY.CX_DY_0626SW"/>
     <cge:Meas_Ref ObjectId="26626"/>
    <cge:TPSR_Ref TObjectID="4289"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26628">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4336.000000 -156.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4291" ObjectName="SW-CX_DY.CX_DY_06267SW"/>
     <cge:Meas_Ref ObjectId="26628"/>
    <cge:TPSR_Ref TObjectID="4291"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26629">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4316.000000 17.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4292" ObjectName="SW-CX_DY.CX_DY_06200SW"/>
     <cge:Meas_Ref ObjectId="26629"/>
    <cge:TPSR_Ref TObjectID="4292"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -502.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4270" ObjectName="SW-CX_DY.CX_DY_0026SW"/>
     <cge:Meas_Ref ObjectId="26443"/>
    <cge:TPSR_Ref TObjectID="4270"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26442">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4338.000000 -410.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4269" ObjectName="SW-CX_DY.CX_DY_0022SW"/>
     <cge:Meas_Ref ObjectId="26442"/>
    <cge:TPSR_Ref TObjectID="4269"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 -493.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4252" ObjectName="SW-CX_DY.CX_DY_3010SW"/>
     <cge:Meas_Ref ObjectId="26299"/>
    <cge:TPSR_Ref TObjectID="4252"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26440">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4200.000000 -493.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4267" ObjectName="SW-CX_DY.CX_DY_3020SW"/>
     <cge:Meas_Ref ObjectId="26440"/>
    <cge:TPSR_Ref TObjectID="4267"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26439">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4501.000000 -621.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4266" ObjectName="SW-CX_DY.CX_DY_3026SW"/>
     <cge:Meas_Ref ObjectId="26439"/>
    <cge:TPSR_Ref TObjectID="4266"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26438">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4614.000000 -621.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4265" ObjectName="SW-CX_DY.CX_DY_3022SW"/>
     <cge:Meas_Ref ObjectId="26438"/>
    <cge:TPSR_Ref TObjectID="4265"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26678">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4447.000000 -681.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4307" ObjectName="SW-CX_DY.CX_DY_3902SW"/>
     <cge:Meas_Ref ObjectId="26678"/>
    <cge:TPSR_Ref TObjectID="4307"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4011.000000 -625.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4253" ObjectName="SW-CX_DY.CX_DY_3016SW"/>
     <cge:Meas_Ref ObjectId="26300"/>
    <cge:TPSR_Ref TObjectID="4253"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4106.000000 -625.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4251" ObjectName="SW-CX_DY.CX_DY_3011SW"/>
     <cge:Meas_Ref ObjectId="26298"/>
    <cge:TPSR_Ref TObjectID="4251"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26680">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -682.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4309" ObjectName="SW-CX_DY.CX_DY_3901SW"/>
     <cge:Meas_Ref ObjectId="26680"/>
    <cge:TPSR_Ref TObjectID="4309"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 -481.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10400" ObjectName="SW-CX_DY.CX_DY_3449SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10400"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-56516">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4503.000000 -446.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10376" ObjectName="SW-CX_DY.CX_DY_34497SW"/>
     <cge:Meas_Ref ObjectId="56516"/>
    <cge:TPSR_Ref TObjectID="10376"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4892.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10391" ObjectName="SW-CX_DY.CX_DY_3411SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10391"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10392" ObjectName="SW-CX_DY.CX_DY_3416SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10392"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 4887.000000 -229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10385" ObjectName="SW-CX_DY.CX_DY_3451SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10385"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5000.000000 -229.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10386" ObjectName="SW-CX_DY.CX_DY_3456SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10386"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 -952.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10395" ObjectName="SW-CX_DY.CX_DY_3439SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10395"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4874.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10387" ObjectName="SW-CX_DY.CX_DY_3461SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10387"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4996.000000 -347.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10388" ObjectName="SW-CX_DY.CX_DY_3466SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10388"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5069.000000 -806.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10396" ObjectName="SW-CX_DY.CX_DY_34167SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10396"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4885.000000 -602.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10389" ObjectName="SW-CX_DY.CX_DY_3421SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10389"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4998.000000 -602.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10390" ObjectName="SW-CX_DY.CX_DY_3426SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10390"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5131.000000 -566.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10397" ObjectName="SW-CX_DY.CX_DY_3429SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10397"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10393" ObjectName="SW-CX_DY.CX_DY_3431SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10393"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5005.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10394" ObjectName="SW-CX_DY.CX_DY_3436SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10394"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5067.000000 -203.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11889" ObjectName="SW-CX_DY.CX_DY_3459SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.928571 5064.000000 -255.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10398" ObjectName="SW-CX_DY.CX_DY_34567SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10398"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4767.000000 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10377" ObjectName="SW-CX_DY.CX_DYI_3901SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10377"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4748.000000 -969.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10378" ObjectName="SW-CX_DY.CX_DYI_39017SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10378"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26677">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -739.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4306" ObjectName="SW-CX_DY.CX_DY_19027SW"/>
     <cge:Meas_Ref ObjectId="26677"/>
    <cge:TPSR_Ref TObjectID="4306"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26676">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 -813.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4305" ObjectName="SW-CX_DY.CX_DY_19020SW"/>
     <cge:Meas_Ref ObjectId="26676"/>
    <cge:TPSR_Ref TObjectID="4305"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26675">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4661.000000 -754.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4304" ObjectName="SW-CX_DY.CX_DY_1902SW"/>
     <cge:Meas_Ref ObjectId="26675"/>
    <cge:TPSR_Ref TObjectID="4304"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4670.000000 -525.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10374" ObjectName="SW-CX_DY.CX_DY_3441SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10374"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4577.000000 -525.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10375" ObjectName="SW-CX_DY.CX_DY_3446SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10375"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26591">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -258.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4281" ObjectName="SW-CX_DY.CX_DY_0616SW"/>
     <cge:Meas_Ref ObjectId="26591"/>
    <cge:TPSR_Ref TObjectID="4281"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -66.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26590">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4208.000000 -351.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4280" ObjectName="SW-CX_DY.CX_DY_0612SW"/>
     <cge:Meas_Ref ObjectId="26590"/>
    <cge:TPSR_Ref TObjectID="4280"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26657">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -353.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4298" ObjectName="SW-CX_DY.CX_DY_0632SW"/>
     <cge:Meas_Ref ObjectId="26657"/>
    <cge:TPSR_Ref TObjectID="4298"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26661">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -221.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4302" ObjectName="SW-CX_DY.CX_DY_06360SW"/>
     <cge:Meas_Ref ObjectId="26661"/>
    <cge:TPSR_Ref TObjectID="4302"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26658">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -303.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4299" ObjectName="SW-CX_DY.CX_DY_06327SW"/>
     <cge:Meas_Ref ObjectId="26658"/>
    <cge:TPSR_Ref TObjectID="4299"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26659">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4433.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4300" ObjectName="SW-CX_DY.CX_DY_0636SW"/>
     <cge:Meas_Ref ObjectId="26659"/>
    <cge:TPSR_Ref TObjectID="4300"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26660">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4454.000000 -157.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4301" ObjectName="SW-CX_DY.CX_DY_06367SW"/>
     <cge:Meas_Ref ObjectId="26660"/>
    <cge:TPSR_Ref TObjectID="4301"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26662">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 18.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4303" ObjectName="SW-CX_DY.CX_DY_06300SW"/>
     <cge:Meas_Ref ObjectId="26662"/>
    <cge:TPSR_Ref TObjectID="4303"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-26430">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -670.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="4262" ObjectName="SW-CX_DY.CX_DY_10267SW"/>
     <cge:Meas_Ref ObjectId="26430"/>
    <cge:TPSR_Ref TObjectID="4262"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.142857 -0.000000 0.000000 -0.891304 4778.000000 -433.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10379" ObjectName="SW-CX_DY.CX_DY_3121SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="10379"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5110.000000 -626.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3d050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3603.000000 -957.000000) translate(0,12)">151</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3598.000000 -893.000000) translate(0,12)">1511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b45df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -940.000000) translate(0,12)">15117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b48480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -998.000000) translate(0,12)">15160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b48c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3612.000000 -1063.000000) translate(0,12)">15167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3599.000000 -1015.000000) translate(0,12)">1516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b492b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3833.000000 -956.000000) translate(0,12)">152</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b495f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3828.000000 -1014.000000) translate(0,12)">1526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -891.000000) translate(0,12)">1521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3841.000000 -1066.000000) translate(0,12)">15267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b49ed0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3840.000000 -998.000000) translate(0,12)">15260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4a110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3841.000000 -940.000000) translate(0,12)">15217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4a350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3490.000000 -879.000000) translate(0,12)">110kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4c140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4684.000000 -875.000000) translate(0,12)">110kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4c590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4047.000000 -949.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4c7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4010.000000 -895.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4ca10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -897.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4cc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -972.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc5450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -971.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc5660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -957.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b48a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -894.000000) translate(0,12)">1532</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4a780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -942.000000) translate(0,12)">15327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4a9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4247.000000 -997.000000) translate(0,12)">15360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4ac00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4235.000000 -1015.000000) translate(0,12)">1536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4ae40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4248.000000 -1062.000000) translate(0,12)">15367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4b080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4522.000000 -956.000000) translate(0,12)">154</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b4b3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -893.000000) translate(0,12)">1542</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc71b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4517.000000 -1014.000000) translate(0,12)">1546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc73f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -1062.000000) translate(0,12)">15467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc7630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.000000 -997.000000) translate(0,12)">15460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc7870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4530.500000 -941.000000) translate(0,12)">15427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc7ab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4677.000000 -784.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc8030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4686.000000 -844.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc82b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4686.000000 -770.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc84f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3611.000000 -785.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc8730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3625.000000 -841.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc8970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3625.000000 -767.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc8bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3810.000000 -767.000000) translate(0,12)">101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc8df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -834.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc9030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3824.000000 -810.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc9270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3823.500000 -754.000000) translate(0,12)">10160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc94b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3805.000000 -709.000000) translate(0,12)">1016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc96f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3661.000000 -632.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc9b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3811.000000 -485.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dc9dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -441.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dca010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3808.000000 -543.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dca250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3576.000000 -377.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dca490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3663.000000 -379.000000) translate(0,12)">0541</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcb5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3757.000000 -331.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcbbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -382.000000) translate(0,12)">0531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcbdf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3766.000000 -243.000000) translate(0,12)">05367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcc030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3755.000000 -272.000000) translate(0,12)">0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcca70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3866.000000 -331.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcce30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -382.000000) translate(0,12)">0521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcd070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3872.000000 -243.000000) translate(0,12)">05267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcd2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3864.000000 -272.000000) translate(0,12)">0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dce9a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3976.000000 -339.000000) translate(0,12)">051</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcefd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3974.000000 -382.000000) translate(0,12)">0511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dcf210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3974.000000 -295.000000) translate(0,12)">0516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dd9e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4226.000000 -333.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dda460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -381.000000) translate(0,12)">0612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dda6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -288.000000) translate(0,12)">0616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ddacc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4337.000000 -341.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df2f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4331.000000 -198.000000) translate(0,12)">0626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df3570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -154.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df37b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -251.000000) translate(0,12)">06260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df39f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4338.000000 -302.000000) translate(0,12)">06227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df3c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4331.000000 -382.000000) translate(0,12)">0622</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df3e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4332.000000 -14.000000) translate(0,12)">06200</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df40b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -340.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df42f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -383.000000) translate(0,12)">0632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df4530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -302.000000) translate(0,12)">06327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df4770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -252.000000) translate(0,12)">06360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df49b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4397.000000 -13.000000) translate(0,12)">06300</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df4bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4456.000000 -154.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df4e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4449.000000 -199.000000) translate(0,12)">0636</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df5070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -373.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df7560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3531.000000 -423.000000) translate(0,12)">10kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df7b90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4575.000000 -399.000000) translate(0,12)">10kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df7dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4849.000000 -1030.000000) translate(0,12)">35kVI母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4852.000000 -205.000000) translate(0,12)">35kVII母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4066.000000 -650.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4113.000000 -652.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df86d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4018.000000 -652.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -710.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8b50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3924.000000 -524.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df8d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4206.000000 -524.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfaac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4357.000000 -768.239382) translate(0,12)">102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfb0f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4350.000000 -822.239382) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfb330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4353.000000 -716.239382) translate(0,12)">1026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfb570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.500000 -804.239382) translate(0,12)">10217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfb7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -755.239382) translate(0,12)">10260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dfea90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4368.000000 -697.239382) translate(0,12)">10267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dff0c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4358.000000 -480.239382) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dff300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -437.239382) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dff540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4355.000000 -542.239382) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dff780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4209.000000 -639.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dffbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4570.000000 -646.239382) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2dffe60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4620.000000 -647.239382) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e000a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4507.000000 -648.239382) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e002e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4774.000000 -982.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e00520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4450.000000 -712.239382) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e156f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -1122.000000) translate(0,15)">元</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e156f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -1122.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e156f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3567.000000 -1122.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -1120.000000) translate(0,15)">姚</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -1120.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3794.000000 -1120.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -1117.000000) translate(0,15)">渔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -1117.000000) translate(0,33)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4202.000000 -1117.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -1117.000000) translate(0,15)">大</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -1117.000000) translate(0,33)">六</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e16d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4484.000000 -1117.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e172c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -730.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e172c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -730.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e172c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -730.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e172c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -730.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e172c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4716.000000 -730.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e18320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4495.000000 -758.000000) translate(0,15)">2号变35kV侧TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e19dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -1006.000000) translate(0,15)">中天线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1a5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -810.000000) translate(0,15)">中北II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5200.000000 -635.000000) translate(0,15)">中仓线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1b3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5008.000000 -541.000000) translate(0,15)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5179.000000 -380.000000) translate(0,15)">中北I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1cad0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5168.000000 -256.000000) translate(0,15)">六中杨石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1d350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4735.000000 -379.000000) translate(0,15)">大中II回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1d600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4742.000000 -551.000000) translate(0,15)">大中I回线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1d840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4008.000000 -758.000000) translate(0,15)">1号变35kV侧TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -730.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -730.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -730.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -730.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3659.000000 -730.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1dfd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3992.000000 -433.000000) translate(0,15)">1号消弧线圈</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -252.000000) translate(0,15)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -252.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -252.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -252.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1e8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3518.000000 -252.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -268.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -268.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -268.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -268.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1ee10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3629.000000 -268.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,15)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1f050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -160.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e1fd60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3898.000000 -156.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,33)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,69)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e201d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3980.000000 -211.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3914.000000 -29.000000) translate(0,15)">中心站10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -29.000000) translate(0,15)">中心站10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,15)">中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,33)">心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,69)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,87)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e20e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4227.000000 -211.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -124.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,15)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,51)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,69)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,87)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -124.000000) translate(0,105)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -268.000000) translate(0,15)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -268.000000) translate(0,33)">段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -268.000000) translate(0,51)">母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -268.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2e21800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -268.000000) translate(0,87)">TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e21d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3097.000000 -1047.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2e341a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3098.000000 -609.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2e36060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3240.500000 -1187.500000) translate(0,16)">大姚变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e36cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3925.000000 -76.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3934.000000 -121.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3926.000000 -169.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e375d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -93.000000) translate(0,12)">0501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -112.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4044.000000 -114.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e37c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4139.000000 -114.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3a090" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4795.000000 -459.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3a6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4865.000000 -1008.000000) translate(0,12)">3431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3a900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -1006.000000) translate(0,12)">343</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3ab40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 -1007.000000) translate(0,12)">3436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3ad80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -951.000000) translate(0,12)">3439</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3b1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -810.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3b470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4961.000000 -808.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3b6b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5011.000000 -809.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3b8f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5075.000000 -835.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3bb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4890.000000 -632.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3bd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -630.000000) translate(0,12)">342</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3bfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5003.000000 -631.000000) translate(0,12)">3426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3c1f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4877.000000 -379.000000) translate(0,12)">3461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3c430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4952.000000 -382.000000) translate(0,12)">346</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3c670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5002.000000 -383.000000) translate(0,12)">3466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3c8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4892.000000 -227.000000) translate(0,12)">3451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3caf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4955.000000 -225.000000) translate(0,12)">345</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3cd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5005.000000 -226.000000) translate(0,12)">3456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3cf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5068.000000 -283.000000) translate(0,12)">34567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3d1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5133.000000 -563.000000) translate(0,12)">3429</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3d7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4710.000000 -999.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4635.000000 -550.239382) translate(0,12)">344</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3dd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4676.000000 -551.239382) translate(0,12)">3441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3df70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4586.000000 -552.239382) translate(0,12)">3446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3e1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4453.000000 -507.239382) translate(0,12)">3449</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e3e3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -475.239382) translate(0,12)">34497</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e40c50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -957.000000) translate(0,12)">153</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e44280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5082.000000 -230.000000) translate(0,12)">3459</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e44e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4696.000000 -918.000000) translate(0,12)">35kVI母TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2e8aa80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3382.000000 -1190.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2f75e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5116.000000 -655.000000) translate(0,12)">34267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f78c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -666.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2f79730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4409.000000 -613.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="36" graphid="g_2f7a360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3155.000000 -787.000000) translate(0,29)">公用信号</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29d69a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3999.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295d5d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -989.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296a610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_296ac40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294ba60" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294c210" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_294c9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3887.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2921e40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3657.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c4ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3672.000000 -751.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c5a10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3672.000000 -825.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285e530" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -915.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285ef20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -969.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_285f950" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4575.000000 -1034.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2946b50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -916.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2947580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -970.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2947fb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4294.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291b9c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3693.000000 -600.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2847a50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3864.000000 -793.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_284b0c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3864.000000 -741.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2956aa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4235.000000 -605.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_288f390" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4411.000000 -790.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2973480" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4411.000000 -742.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_292c4f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -211.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2933010" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3917.000000 -211.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2883ee0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -219.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2863660" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -301.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2868dd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -155.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_286c2a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4319.000000 55.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28bf4e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5117.000000 -817.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28d5360" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5112.000000 -264.746531)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b28720" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4751.000000 -1044.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2aec0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4733.000000 -750.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b2be10" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4733.000000 -824.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b3c5c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4551.000000 -445.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2de3c50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -220.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2de70d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -302.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2decd50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4502.000000 -156.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2df04a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4443.000000 57.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2dfbdd0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4411.000000 -681.239382)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2f75370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5158.000000 -637.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DY"/>
</svg>