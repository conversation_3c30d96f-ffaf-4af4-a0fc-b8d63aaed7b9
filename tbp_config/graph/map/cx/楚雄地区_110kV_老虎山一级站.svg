<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-77" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="18 -1905 1376 1177">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="hydroGenerator:shape3">
    <polyline arcFlag="1" points="25,25 25,26 25,27 25,28 24,29 24,30 23,31 23,31 22,32 21,32 20,33 19,33 18,33 17,34 16,33 15,33 14,33 13,32 13,32 12,31 11,31 11,30 10,29 10,28 10,27 9,26 10,25 " stroke-width="0.06"/>
    <circle cx="24" cy="24" fillStyle="0" r="24" stroke-width="0.5"/>
    <polyline points="40,25 41,24 40,24 40,23 40,22 39,21 39,20 38,19 37,19 37,18 36,18 35,17 34,17 33,17 32,17 31,17 30,18 29,18 28,19 27,19 27,20 26,21 26,22 25,23 25,24 25,24 25,25 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape116">
    <circle cx="8" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="14" cy="15" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="18" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="7" x2="15" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="15" x2="15" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="49" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="14" x2="16" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="15" x2="15" y1="22" y2="31"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="transformer:shape0_0">
    <circle cx="26" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="20" x2="20" y1="32" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="35" x2="20" y1="23" y2="32"/>
   </symbol>
   <symbol id="transformer:shape0_1">
    <circle cx="26" cy="61" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="25" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="25" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="25" x2="18" y1="66" y2="73"/>
   </symbol>
   <symbol id="transformer:shape0-2">
    <circle cx="56" cy="45" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="62" y1="37" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="70" x2="62" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="62" x2="55" y1="46" y2="53"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape12_0">
    <circle cx="16" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="15" y1="52" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="43" x2="40" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="26" y2="26"/>
    <polyline DF8003:Layer="PUBLIC" points="15,14 21,27 9,27 15,14 15,15 15,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="80" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="81" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="82" y2="87"/>
   </symbol>
   <symbol id="transformer2:shape12_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,57 40,57 40,28 " stroke-width="1"/>
    <circle cx="16" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="56" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="57" y2="62"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dbb5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dbc790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbd180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbde20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbf050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dbfcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc0820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1dc1340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17381e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_17381e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc4640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc63d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1dc73f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dc8ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dc9be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dca9a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dcb2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcc990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcd580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcde20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dce5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dcf6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd0040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd0b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd14f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd2950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd3430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1dd4460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1dd5120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1de3860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1dd6990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd7f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1dd94a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1187" width="1386" x="13" y="-1910"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47229">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 526.000000 -1011.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8491" ObjectName="SW-CX_LHSY.CX_LHSY_601BK"/>
     <cge:Meas_Ref ObjectId="47229"/>
    <cge:TPSR_Ref TObjectID="8491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47233">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 -1016.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8495" ObjectName="SW-CX_LHSY.CX_LHSY_602BK"/>
     <cge:Meas_Ref ObjectId="47233"/>
    <cge:TPSR_Ref TObjectID="8495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 572.000000 -1658.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47239">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 -1247.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8501" ObjectName="SW-CX_LHSY.CX_LHSY_612BK"/>
     <cge:Meas_Ref ObjectId="47239"/>
    <cge:TPSR_Ref TObjectID="8501"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 767.000000 -1478.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47240">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 734.000000 -1659.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8502" ObjectName="SW-CX_LHSY.CX_LHSY_463BK"/>
     <cge:Meas_Ref ObjectId="47240"/>
    <cge:TPSR_Ref TObjectID="8502"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47244">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 465.000000 -1466.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8506" ObjectName="SW-CX_LHSY.CX_LHSY_462BK"/>
     <cge:Meas_Ref ObjectId="47244"/>
    <cge:TPSR_Ref TObjectID="8506"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47238">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 943.000000 -1017.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8500" ObjectName="SW-CX_LHSY.CX_LHSY_611BK"/>
     <cge:Meas_Ref ObjectId="47238"/>
    <cge:TPSR_Ref TObjectID="8500"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 972.000000 -1617.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1051.000000 -1618.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47246">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1215.000000 -1570.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8508" ObjectName="SW-CX_LHSY.CX_LHSY_181BK"/>
     <cge:Meas_Ref ObjectId="47246"/>
    <cge:TPSR_Ref TObjectID="8508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1036.000000 -1426.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="22113" ObjectName="SW-CX_LHSY.CX_LHSY_301BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="22113"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_LHSY.CX_LHSY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="12021"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1249.000000 -1385.000000)" xlink:href="#transformer:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="12023"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1249.000000 -1385.000000)" xlink:href="#transformer:shape0_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="12025"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1249.000000 -1385.000000)" xlink:href="#transformer:shape0-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="8514" ObjectName="TF-CX_LHSY.CX_LHSY_1T"/>
    <cge:TPSR_Ref TObjectID="8514"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LHSY" endPointId="0" endStationName="PAS_T1" flowDrawDirect="1" flowShape="0" id="AC-110kV.laohushan12T1_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1224,-1807 1224,-1849 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11877" ObjectName="AC-110kV.laohushan12T1_line"/>
    <cge:TPSR_Ref TObjectID="11877_SS-77"/></metadata>
   <polyline fill="none" opacity="0" points="1224,-1807 1224,-1849 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1d99500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 525.000000 -830.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d71940" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.000000 -1059.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d97d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 488.000000 -848.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e2f4c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 630.000000 -842.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e1f130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1174.000000 -833.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d7c820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 -1059.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d75780" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1138.000000 -851.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1dab0b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1280.000000 -847.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d78c70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 666.000000 -1302.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d79700" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 667.000000 -1225.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e69e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 668.000000 -1435.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f534e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 -984.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f56890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -986.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f5e850" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 -984.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f77b00" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.000000 -1325.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f7e2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.000000 -1547.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef1d40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1284.000000 -1618.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ef2a30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1288.000000 -1694.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f21820" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 519.000000 -1409.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1e0ddf0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 452.000000 -852.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d70d00">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 561.000000 -906.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e30f20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 596.000000 -828.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1db32d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1102.000000 -855.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e37ad0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1211.000000 -909.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e32670">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1246.000000 -837.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1e66aa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 633.000000 -1414.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f52d80">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 802.000000 -977.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f65b20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 953.000000 -1382.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f78a10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1166.000000 -1695.000000)" xlink:href="#lightningRod:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f01420">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 -883.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f01eb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 599.000000 -877.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f029f0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 780.000000 -1020.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f034d0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 807.000000 -1017.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f05d80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1107.000000 -885.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f06ac0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1249.000000 -879.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f07800">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1039.000000 -1267.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f085b0">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -1197.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f09360">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.000000 -1391.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0a110">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 958.000000 -1423.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0ae50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 -1464.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f0b930">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -1390.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f32d20">
    <use class="BV-6KV" transform="matrix(0.000000 -0.777778 -0.595238 -0.000000 567.500000 -964.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f33890">
    <use class="BV-6KV" transform="matrix(0.000000 -0.777778 -0.595238 -0.000000 1218.500000 -967.500000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f35c00">
    <use class="BV-110KV" transform="matrix(0.000000 -0.923077 -0.796875 -0.000000 1288.601563 -1742.538462)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1f36d60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 613.000000 -1442.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 135.000000 -1823.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78579" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 150.538462 -1719.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78579" ObjectName="CX_LHSY:CX_LHSY_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-79730" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 149.538462 -1679.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="79730" ObjectName="CX_LHSY:CX_LHSY_sumQ"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47216" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -1621.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47216" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8508"/>
     <cge:Term_Ref ObjectID="12008"/>
    <cge:TPSR_Ref TObjectID="8508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47217" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -1621.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47217" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8508"/>
     <cge:Term_Ref ObjectID="12008"/>
    <cge:TPSR_Ref TObjectID="8508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47218" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -1621.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47218" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8508"/>
     <cge:Term_Ref ObjectID="12008"/>
    <cge:TPSR_Ref TObjectID="8508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="U" PreSymbol="0" appendix="" decimal="2" id="ME-47219" prefix="U " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -1621.000000) translate(0,57)">U  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47219" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="8508"/>
     <cge:Term_Ref ObjectID="12008"/>
    <cge:TPSR_Ref TObjectID="8508"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47215" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 377.000000 -1612.000000) translate(0,12)">Uab  47215.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47215" ObjectName="CX_LHSY.CX_LHSY_9IM:F"/>
     <cge:PSR_Ref ObjectID="8490"/>
     <cge:Term_Ref ObjectID="11973"/>
    <cge:TPSR_Ref TObjectID="8490"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-47210" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -1193.000000) translate(0,12)">Uab  47210.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47210" ObjectName="CX_LHSY.CX_LHSY_6IM:F"/>
     <cge:PSR_Ref ObjectID="8489"/>
     <cge:Term_Ref ObjectID="11972"/>
    <cge:TPSR_Ref TObjectID="8489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-47211" prefix="Hz " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 352.000000 -1193.000000) translate(0,27)">Hz  47211.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47211" ObjectName="CX_LHSY.CX_LHSY_6IM:F"/>
     <cge:PSR_Ref ObjectID="8489"/>
     <cge:Term_Ref ObjectID="11972"/>
    <cge:TPSR_Ref TObjectID="8489"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47206" prefix="P " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 -788.000000) translate(0,12)">P  47206.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47206" ObjectName="CX_LHSY.CX_LHSY_602BK:F"/>
     <cge:PSR_Ref ObjectID="8495"/>
     <cge:Term_Ref ObjectID="11982"/>
    <cge:TPSR_Ref TObjectID="8495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47207" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 -788.000000) translate(0,27)">Q  47207.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47207" ObjectName="CX_LHSY.CX_LHSY_602BK:F"/>
     <cge:PSR_Ref ObjectID="8495"/>
     <cge:Term_Ref ObjectID="11982"/>
    <cge:TPSR_Ref TObjectID="8495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47205" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1152.000000 -788.000000) translate(0,42)">Ia   47205.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47205" ObjectName="CX_LHSY.CX_LHSY_602BK:F"/>
     <cge:PSR_Ref ObjectID="8495"/>
     <cge:Term_Ref ObjectID="11982"/>
    <cge:TPSR_Ref TObjectID="8495"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-47201" prefix="P " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -777.000000) translate(0,12)">P  47201.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47201" ObjectName="CX_LHSY.CX_LHSY_601BK:F"/>
     <cge:PSR_Ref ObjectID="8491"/>
     <cge:Term_Ref ObjectID="11974"/>
    <cge:TPSR_Ref TObjectID="8491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-47202" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -777.000000) translate(0,27)">Q  47202.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47202" ObjectName="CX_LHSY.CX_LHSY_601BK:F"/>
     <cge:PSR_Ref ObjectID="8491"/>
     <cge:Term_Ref ObjectID="11974"/>
    <cge:TPSR_Ref TObjectID="8491"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-47200" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 502.000000 -777.000000) translate(0,42)">Ia   47200.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="47200" ObjectName="CX_LHSY.CX_LHSY_601BK:F"/>
     <cge:PSR_Ref ObjectID="8491"/>
     <cge:Term_Ref ObjectID="11974"/>
    <cge:TPSR_Ref TObjectID="8491"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="165" x="147" y="-1882"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="165" x="147" y="-1882"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="98" y="-1899"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="98" y="-1899"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="165" x="147" y="-1882"/></g>
   <g href="cx_索引_接线图_地调直调_水电.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="98" y="-1899"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-47231">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 559.000000 -1043.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8493" ObjectName="SW-CX_LHSY.CX_LHSY_60117SW"/>
     <cge:Meas_Ref ObjectId="47231"/>
    <cge:TPSR_Ref TObjectID="8493"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 451.000000 -918.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.000000 -913.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 483.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 625.000000 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47235">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1043.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8497" ObjectName="SW-CX_LHSY.CX_LHSY_60217SW"/>
     <cge:Meas_Ref ObjectId="47235"/>
    <cge:TPSR_Ref TObjectID="8497"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1100.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.000000 -918.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1133.000000 -866.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47232">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1275.000000 -862.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8494" ObjectName="SW-CX_LHSY.CX_LHSY_62027SW"/>
     <cge:Meas_Ref ObjectId="47232"/>
    <cge:TPSR_Ref TObjectID="8494"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -1745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -1690.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 566.000000 -1589.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 -1159.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 725.000000 -1207.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 722.000000 -1284.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 -1514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.000000 -1407.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47241">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -1588.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8503" ObjectName="SW-CX_LHSY.CX_LHSY_4631SW"/>
     <cge:Meas_Ref ObjectId="47241"/>
    <cge:TPSR_Ref TObjectID="8503"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47242">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -1689.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8504" ObjectName="SW-CX_LHSY.CX_LHSY_4632SW"/>
     <cge:Meas_Ref ObjectId="47242"/>
    <cge:TPSR_Ref TObjectID="8504"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47243">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 728.000000 -1744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8505" ObjectName="SW-CX_LHSY.CX_LHSY_4633SW"/>
     <cge:Meas_Ref ObjectId="47243"/>
    <cge:TPSR_Ref TObjectID="8505"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -1507.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.000000 -1509.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -1450.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47236">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 801.000000 -1060.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8498" ObjectName="SW-CX_LHSY.CX_LHSY_6100SW"/>
     <cge:Meas_Ref ObjectId="47236"/>
    <cge:TPSR_Ref TObjectID="8498"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47237">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 832.000000 -1001.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8499" ObjectName="SW-CX_LHSY.CX_LHSY_61007SW"/>
     <cge:Meas_Ref ObjectId="47237"/>
    <cge:TPSR_Ref TObjectID="8499"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 -1058.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.000000 -999.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47245">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1159.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8507" ObjectName="SW-CX_LHSY.CX_LHSY_6311SW"/>
     <cge:Meas_Ref ObjectId="47245"/>
    <cge:TPSR_Ref TObjectID="8507"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 952.000000 -1477.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -1665.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1050.000000 -1664.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 971.000000 -1582.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1050.000000 -1583.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47249">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1250.000000 -1531.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8511" ObjectName="SW-CX_LHSY.CX_LHSY_18117SW"/>
     <cge:Meas_Ref ObjectId="47249"/>
    <cge:TPSR_Ref TObjectID="8511"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47250">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1251.000000 -1602.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8512" ObjectName="SW-CX_LHSY.CX_LHSY_18160SW"/>
     <cge:Meas_Ref ObjectId="47250"/>
    <cge:TPSR_Ref TObjectID="8512"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47251">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1252.000000 -1678.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8513" ObjectName="SW-CX_LHSY.CX_LHSY_18167SW"/>
     <cge:Meas_Ref ObjectId="47251"/>
    <cge:TPSR_Ref TObjectID="8513"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47248">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1632.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8510" ObjectName="SW-CX_LHSY.CX_LHSY_1816SW"/>
     <cge:Meas_Ref ObjectId="47248"/>
    <cge:TPSR_Ref TObjectID="8510"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47230">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 520.000000 -1058.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8492" ObjectName="SW-CX_LHSY.CX_LHSY_6011SW"/>
     <cge:Meas_Ref ObjectId="47230"/>
    <cge:TPSR_Ref TObjectID="8492"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47234">
    <use class="BV-6KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1169.000000 -1059.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8496" ObjectName="SW-CX_LHSY.CX_LHSY_6021SW"/>
     <cge:Meas_Ref ObjectId="47234"/>
    <cge:TPSR_Ref TObjectID="8496"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 502.000000 -1428.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-47247">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1209.000000 -1470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8509" ObjectName="SW-CX_LHSY.CX_LHSY_1811SW"/>
     <cge:Meas_Ref ObjectId="47247"/>
    <cge:TPSR_Ref TObjectID="8509"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -1326.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 751.000000 -1326.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -1344.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 459.000000 -1344.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 -895.000000)" xlink:href="#transformer2:shape12_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.000000 -895.000000)" xlink:href="#transformer2:shape12_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-6KV" id="g_1e5a590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-1069 550,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8491@x" ObjectIDND1="8492@x" ObjectIDZND0="8493@0" Pin0InfoVect0LinkObjId="SW-47231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47229_0" Pin1InfoVect1LinkObjId="SW-47230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-1069 550,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1da5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="586,-1069 598,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8493@1" ObjectIDZND0="g_1d71940@0" Pin0InfoVect0LinkObjId="g_1d71940_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="586,-1069 598,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1fe8650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-971 535,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="hydroGenerator" ObjectIDND0="g_1f32d20@0" ObjectIDND1="8491@x" ObjectIDZND0="15723@0" Pin0InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f32d20_0" Pin1InfoVect1LinkObjId="SW-47229_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-971 535,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d71620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-872 535,-856 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1d99500@0" Pin0InfoVect0LinkObjId="g_1d99500_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-872 535,-856 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e0a220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-940 466,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f01420@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f01420_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="466,-940 466,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e104f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-930 498,-930 498,-921 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f01420@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f01420_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="466,-930 498,-930 498,-921 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e32ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="498,-885 498,-874 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d97d50@0" Pin0InfoVect0LinkObjId="g_1d97d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="498,-885 498,-874 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e13750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="640,-879 640,-868 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1e2f4c0@0" Pin0InfoVect0LinkObjId="g_1e2f4c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="640,-879 640,-868 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d7b1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-924 640,-924 640,-915 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f01eb0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f01eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-924 640,-924 640,-915 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d7b3e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-924 608,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f01eb0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f01eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-924 608,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e29170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-1069 1200,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8495@x" ObjectIDND1="8496@x" ObjectIDZND0="8497@0" Pin0InfoVect0LinkObjId="SW-47235_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47233_0" Pin1InfoVect1LinkObjId="SW-47234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-1069 1200,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d7c5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1236,-1069 1248,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8497@1" ObjectIDZND0="g_1d7c820@0" Pin0InfoVect0LinkObjId="g_1d7c820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47235_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1236,-1069 1248,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e2c370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-974 1184,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="hydroGenerator" ObjectIDND0="g_1f33890@0" ObjectIDND1="8495@x" ObjectIDZND0="15724@0" Pin0InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN2_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f33890_0" Pin1InfoVect1LinkObjId="SW-47233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-974 1184,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e2c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-875 1184,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1e1f130@0" Pin0InfoVect0LinkObjId="g_1e1f130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-875 1184,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e0a7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-943 1116,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_1f05d80@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_1f05d80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-943 1116,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e25c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-933 1148,-933 1148,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f05d80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f05d80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-933 1148,-933 1148,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d75520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-888 1148,-877 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d75780@0" Pin0InfoVect0LinkObjId="g_1d75780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-888 1148,-877 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1daae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1290,-884 1290,-873 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8494@0" ObjectIDZND0="g_1dab0b0@0" Pin0InfoVect0LinkObjId="g_1dab0b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1290,-884 1290,-873 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1daba00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-929 1290,-929 1290,-920 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_1f06ac0@0" ObjectIDZND0="8494@1" Pin0InfoVect0LinkObjId="SW-47232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f06ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-929 1290,-929 1290,-920 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e45a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-929 1258,-940 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="8494@x" ObjectIDND1="g_1f06ac0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47232_0" Pin1InfoVect1LinkObjId="g_1f06ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-929 1258,-940 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1e2e3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1181 776,-1154 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="8489@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1181 776,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4c0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1233 752,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="8501@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-47239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1233 752,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d4c320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="716,-1233 693,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d79700@0" Pin0InfoVect0LinkObjId="g_1d79700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="716,-1233 693,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d787b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1310 749,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="8501@x" ObjectIDND1="0@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47239_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1310 749,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1d78a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="713,-1310 692,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1d78c70@0" Pin0InfoVect0LinkObjId="g_1d78c70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="713,-1310 692,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4d160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1233 776,-1217 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="8501@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-47239_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1233 776,-1217 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4d3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1310 776,-1282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="8501@1" Pin0InfoVect0LinkObjId="SW-47239_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1310 776,-1282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4d620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1255 776,-1233 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8501@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47239_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1255 776,-1233 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1d4d880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1331 776,-1310 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="8501@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-47239_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1331 776,-1310 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e340e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1429 776,-1411 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1429 776,-1411 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e26500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1486 776,-1465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1486 776,-1465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e26760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1513 776,-1536 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1513 776,-1536 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e269c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="776,-1572 776,-1590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="8490@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="776,-1572 776,-1590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e26c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1590 581,-1611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8490@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e269c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1590 581,-1611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e26e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1647 581,-1666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1647 581,-1666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e270e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1693 581,-1712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1693 581,-1712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e27340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="581,-1748 581,-1767 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="581,-1748 581,-1767 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eec750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1590 743,-1610 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8490@0" ObjectIDZND0="8503@0" Pin0InfoVect0LinkObjId="SW-47241_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e269c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1590 743,-1610 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eec9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1646 743,-1667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="8503@1" ObjectIDZND0="8502@0" Pin0InfoVect0LinkObjId="SW-47240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47241_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1646 743,-1667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eecc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1694 743,-1711 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="8502@1" ObjectIDZND0="8504@0" Pin0InfoVect0LinkObjId="SW-47242_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1694 743,-1711 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eece70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1747 743,-1766 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8504@1" ObjectIDZND0="8505@0" Pin0InfoVect0LinkObjId="SW-47243_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47242_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1747 743,-1766 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1eed0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="743,-1802 743,-1829 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="8505@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47243_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="743,-1802 743,-1829 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e63d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-1590 474,-1565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8490@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e269c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-1590 474,-1565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1e66840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-1590 647,-1567 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8490@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e269c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-1590 647,-1567 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1e69bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="678,-1472 678,-1461 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@0" ObjectIDZND0="g_1e69e30@0" Pin0InfoVect0LinkObjId="g_1e69e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="678,-1472 678,-1461 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f52b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-1154 816,-1118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8489@0" ObjectIDZND0="8498@1" Pin0InfoVect0LinkObjId="SW-47236_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-1154 816,-1118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f56630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="847,-1023 847,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8499@0" ObjectIDZND0="g_1f56890@0" Pin0InfoVect0LinkObjId="g_1f56890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47237_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="847,-1023 847,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5bb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-1155 952,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8489@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-1155 952,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5bda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-988 952,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="8500@0" Pin0InfoVect0LinkObjId="SW-47238_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-988 952,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5f2a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-1052 952,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="8500@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47238_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="952,-1052 952,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5f500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="952,-1068 952,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="8500@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47238_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="952,-1068 952,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f5f760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,-1010 1006,-1021 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1f5e850@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f5e850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-1010 1006,-1021 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f5f9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1006,-1057 1006,-1068 953,-1068 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="8500@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-47238_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1006,-1057 1006,-1068 953,-1068 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f62490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1154 1224,-1181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8489@0" ObjectIDZND0="8507@0" Pin0InfoVect0LinkObjId="SW-47245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1154 1224,-1181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f626f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1273 1258,-1273 1258,-1255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="8507@x" ObjectIDND1="8514@x" ObjectIDZND0="g_1f085b0@0" Pin0InfoVect0LinkObjId="g_1f085b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47245_0" Pin1InfoVect1LinkObjId="g_1f62bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1273 1258,-1273 1258,-1255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f62950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1217 1224,-1273 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="8507@1" ObjectIDZND0="g_1f085b0@0" ObjectIDZND1="8514@x" Pin0InfoVect0LinkObjId="g_1f085b0_0" Pin0InfoVect1LinkObjId="g_1f62bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1217 1224,-1273 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f62bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1273 1224,-1301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="g_1f085b0@0" ObjectIDND1="8507@x" ObjectIDZND0="8514@1" Pin0InfoVect0LinkObjId="g_1f502f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f085b0_0" Pin1InfoVect1LinkObjId="SW-47245_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1273 1224,-1301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f62e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-1557 1046,-1525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="22113@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-1557 1046,-1525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f658c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-1557 967,-1535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,-1557 967,-1535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f66400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-1480 1001,-1480 1001,-1449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_1f0a110@0" ObjectIDZND0="g_1f09360@0" Pin0InfoVect0LinkObjId="g_1f09360_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_1f0a110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,-1480 1001,-1480 1001,-1449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f66660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-1499 967,-1480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1f09360@0" ObjectIDZND1="g_1f0a110@0" Pin0InfoVect0LinkObjId="g_1f09360_0" Pin0InfoVect1LinkObjId="g_1f0a110_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,-1499 967,-1480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f765a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1557 981,-1588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1557 981,-1588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f76800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1606 981,-1625 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1606 981,-1625 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f76a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1652 981,-1672 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1652 981,-1672 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f76cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="981,-1689 981,-1754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="981,-1689 981,-1754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f76f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-1589 1060,-1557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-1589 1060,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f77180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-1626 1060,-1607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-1626 1060,-1607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f773e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-1756 1060,-1688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-1756 1060,-1688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f77640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1060,-1671 1060,-1653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1060,-1671 1060,-1653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f778a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1225,-1361 1274,-1361 1274,-1351 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" ObjectIDZND0="g_1f77b00@0" Pin0InfoVect0LinkObjId="g_1f77b00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-1361 1274,-1361 1274,-1351 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f78550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1466 1244,-1466 1244,-1448 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="8509@x" ObjectIDND1="8514@x" ObjectIDZND0="g_1f0b930@0" Pin0InfoVect0LinkObjId="g_1f0b930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47247_0" Pin1InfoVect1LinkObjId="g_1f62bb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1466 1244,-1466 1244,-1448 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f787b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1380 1224,-1466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="8514@0" ObjectIDZND0="g_1f0b930@0" ObjectIDZND1="8509@x" Pin0InfoVect0LinkObjId="g_1f0b930_0" Pin0InfoVect1LinkObjId="SW-47247_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f62bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1380 1224,-1466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f7ddf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1225,-1557 1241,-1557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="8508@x" ObjectIDND1="48332@0" ObjectIDZND0="8511@0" Pin0InfoVect0LinkObjId="SW-47249_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47246_0" Pin1InfoVect1LinkObjId="g_1f7ed40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1225,-1557 1241,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f7e050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-1557 1288,-1557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8511@1" ObjectIDZND0="g_1f7e2b0@0" Pin0InfoVect0LinkObjId="g_1f7e2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47249_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-1557 1288,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f7ed40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1578 1224,-1557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="8508@0" ObjectIDZND0="48332@0" ObjectIDZND1="8511@x" Pin0InfoVect0LinkObjId="g_1f867f0_0" Pin0InfoVect1LinkObjId="SW-47249_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1578 1224,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ef1ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1278,-1628 1289,-1628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8512@1" ObjectIDZND0="g_1ef1d40@0" Pin0InfoVect0LinkObjId="g_1ef1d40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1278,-1628 1289,-1628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1ef27d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1279,-1704 1293,-1704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="8513@1" ObjectIDZND0="g_1ef2a30@0" Pin0InfoVect0LinkObjId="g_1ef2a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47251_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-1704 1293,-1704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f019f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-876 466,-888 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e0ddf0@0" ObjectIDZND0="g_1f01420@0" Pin0InfoVect0LinkObjId="g_1f01420_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e0ddf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="466,-876 466,-888 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f01c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-919 466,-930 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f01420@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f01420_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="466,-919 466,-930 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f02530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-864 608,-882 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e30f20@0" ObjectIDZND0="g_1f01eb0@0" Pin0InfoVect0LinkObjId="g_1f01eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e30f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="608,-864 608,-882 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f02790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="608,-913 608,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f01eb0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f01eb0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="608,-913 608,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f03270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="789,-1010 789,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_1f534e0@0" ObjectIDZND0="g_1f029f0@0" Pin0InfoVect0LinkObjId="g_1f029f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f534e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="789,-1010 789,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f03d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-1001 816,-1022 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f52d80@0" ObjectIDZND0="g_1f034d0@0" Pin0InfoVect0LinkObjId="g_1f034d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f52d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-1001 816,-1022 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f06600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-879 1116,-890 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1db32d0@0" ObjectIDZND0="g_1f05d80@0" Pin0InfoVect0LinkObjId="g_1f05d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1db32d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-879 1116,-890 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f06860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1116,-921 1116,-933 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f05d80@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f05d80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1116,-921 1116,-933 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f07340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-873 1258,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e32670@0" ObjectIDZND0="g_1f06ac0@0" Pin0InfoVect0LinkObjId="g_1f06ac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e32670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-873 1258,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f075a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1258,-915 1258,-929 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1f06ac0@1" ObjectIDZND0="8494@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-47232_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f06ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1258,-915 1258,-929 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0a990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-1406 967,-1428 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f65b20@0" ObjectIDZND0="g_1f0a110@0" Pin0InfoVect0LinkObjId="g_1f0a110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f65b20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="967,-1406 967,-1428 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0abf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="967,-1459 967,-1480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1f0a110@1" ObjectIDZND0="g_1f09360@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1f09360_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f0a110_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="967,-1459 967,-1480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f0b6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-1438 647,-1469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1e66aa0@0" ObjectIDZND0="g_1f0ae50@0" Pin0InfoVect0LinkObjId="g_1f0ae50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e66aa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-1438 647,-1469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f0f410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-1069 535,-1046 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8493@x" ObjectIDND1="8492@x" ObjectIDZND0="8491@1" Pin0InfoVect0LinkObjId="SW-47229_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47231_0" Pin1InfoVect1LinkObjId="SW-47230_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-1069 535,-1046 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f0f600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-1154 535,-1116 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8489@0" ObjectIDZND0="8492@1" Pin0InfoVect0LinkObjId="SW-47230_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-1154 535,-1116 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f0f7f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-1080 535,-1071 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8492@0" ObjectIDZND0="8493@x" ObjectIDZND1="8491@x" Pin0InfoVect0LinkObjId="SW-47231_0" Pin0InfoVect1LinkObjId="SW-47229_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47230_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="535,-1080 535,-1071 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f12910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-1069 1184,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8497@x" ObjectIDND1="8496@x" ObjectIDZND0="8495@1" Pin0InfoVect0LinkObjId="SW-47233_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47235_0" Pin1InfoVect1LinkObjId="SW-47234_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-1069 1184,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f15420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-1154 1184,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="8489@0" ObjectIDZND0="8496@1" Pin0InfoVect0LinkObjId="SW-47234_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1e2e3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-1154 1184,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f15680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-1081 1184,-1069 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="8496@0" ObjectIDZND0="8497@x" ObjectIDZND1="8495@x" Pin0InfoVect0LinkObjId="SW-47235_0" Pin0InfoVect1LinkObjId="SW-47233_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47234_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-1081 1184,-1069 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f1eef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-1529 474,-1501 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="8506@1" Pin0InfoVect0LinkObjId="SW-47244_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-1529 474,-1501 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f215c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="529,-1454 529,-1435 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_1f21820@0" Pin0InfoVect0LinkObjId="g_1f21820_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="529,-1454 529,-1435 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f28ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1492 1224,-1466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="8509@0" ObjectIDZND0="g_1f0b930@0" ObjectIDZND1="8514@x" Pin0InfoVect0LinkObjId="g_1f0b930_0" Pin0InfoVect1LinkObjId="g_1f62bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47247_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1492 1224,-1466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f333d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-971 545,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="15723@x" ObjectIDND1="8491@x" ObjectIDZND0="g_1f32d20@0" Pin0InfoVect0LinkObjId="g_1f32d20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN1_0" Pin1InfoVect1LinkObjId="SW-47229_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-971 545,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f33630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="564,-971 573,-971 573,-942 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f32d20@1" ObjectIDZND0="g_1d70d00@0" Pin0InfoVect0LinkObjId="g_1d70d00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f32d20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="564,-971 573,-971 573,-942 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f34120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-974 1196,-974 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="15724@x" ObjectIDND1="8495@x" ObjectIDZND0="g_1f33890@0" Pin0InfoVect0LinkObjId="g_1f33890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN2_0" Pin1InfoVect1LinkObjId="SW-47233_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-974 1196,-974 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f34380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1215,-974 1223,-974 1223,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f33890@1" ObjectIDZND0="g_1e37ad0@0" Pin0InfoVect0LinkObjId="g_1e37ad0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f33890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1215,-974 1223,-974 1223,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f3f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-971 535,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="15723@x" ObjectIDND1="g_1f32d20@0" ObjectIDZND0="8491@0" Pin0InfoVect0LinkObjId="SW-47229_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN1_0" Pin1InfoVect1LinkObjId="g_1f32d20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="535,-971 535,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f3fa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="466,-976 466,-989 608,-989 608,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="466,-976 466,-989 608,-989 608,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f3fc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1184,-974 1184,-1024 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="15724@x" ObjectIDND1="g_1f33890@0" ObjectIDZND0="8495@0" Pin0InfoVect0LinkObjId="SW-47233_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SM-CX_LHSY.CX_LHS_GN2_0" Pin1InfoVect1LinkObjId="g_1f33890_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1184,-974 1184,-1024 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f3fee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1115,-981 1115,-994 1258,-994 1258,-976 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1115,-981 1115,-994 1258,-994 1258,-976 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f40150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="647,-1531 647,-1517 678,-1517 678,-1508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="647,-1531 647,-1517 678,-1517 678,-1508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f403c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="816,-1082 816,-1068 847,-1068 847,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="8498@0" ObjectIDZND0="8499@1" Pin0InfoVect0LinkObjId="SW-47237_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47236_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="816,-1082 816,-1068 847,-1068 847,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-6KV" id="g_1f40630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="789,-1056 789,-1068 816,-1068 816,-1053 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f029f0@1" ObjectIDZND0="g_1f034d0@1" Pin0InfoVect0LinkObjId="g_1f034d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f029f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="789,-1056 789,-1068 816,-1068 816,-1053 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f408a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="620,-1500 620,-1517 647,-1517 647,-1500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1f36d60@0" ObjectIDZND0="g_1f0ae50@1" Pin0InfoVect0LinkObjId="g_1f0ae50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f36d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="620,-1500 620,-1517 647,-1517 647,-1500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1f40b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="493,-1816 581,-1816 581,-1803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="493,-1816 581,-1816 581,-1803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f43bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1242,-1749 1224,-1749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1f35c00@0" ObjectIDZND0="g_1f78a10@0" ObjectIDZND1="8513@x" ObjectIDZND2="8510@x" Pin0InfoVect0LinkObjId="g_1f78a10_0" Pin0InfoVect1LinkObjId="SW-47251_0" Pin0InfoVect2LinkObjId="SW-47248_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f35c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1242,-1749 1224,-1749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f446e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1749 1224,-1808 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_1f35c00@0" ObjectIDND1="g_1f78a10@0" ObjectIDND2="8513@x" ObjectIDZND0="11877@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f35c00_0" Pin1InfoVect1LinkObjId="g_1f78a10_0" Pin1InfoVect2LinkObjId="SW-47251_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1749 1224,-1808 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f44940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1180,-1719 1180,-1735 1224,-1735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_1f78a10@0" ObjectIDZND0="g_1f35c00@0" ObjectIDZND1="11877@1" ObjectIDZND2="8513@x" Pin0InfoVect0LinkObjId="g_1f35c00_0" Pin0InfoVect1LinkObjId="g_1f446e0_1" Pin0InfoVect2LinkObjId="SW-47251_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f78a10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1180,-1719 1180,-1735 1224,-1735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f45430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1735 1224,-1749 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1f78a10@0" ObjectIDND1="8513@x" ObjectIDND2="8510@x" ObjectIDZND0="g_1f35c00@0" ObjectIDZND1="11877@1" Pin0InfoVect0LinkObjId="g_1f35c00_0" Pin0InfoVect1LinkObjId="g_1f446e0_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1f78a10_0" Pin1InfoVect1LinkObjId="SW-47251_0" Pin1InfoVect2LinkObjId="SW-47248_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1735 1224,-1749 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f45690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1243,-1704 1224,-1704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8513@0" ObjectIDZND0="g_1f78a10@0" ObjectIDZND1="g_1f35c00@0" ObjectIDZND2="11877@1" Pin0InfoVect0LinkObjId="g_1f78a10_0" Pin0InfoVect1LinkObjId="g_1f35c00_0" Pin0InfoVect2LinkObjId="g_1f446e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47251_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1243,-1704 1224,-1704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f468c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-1454 474,-1437 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="0@x" ObjectIDND1="8506@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-47244_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-1454 474,-1437 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f47230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="493,-1454 474,-1454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="8506@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-47244_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="493,-1454 474,-1454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1f47420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="474,-1454 474,-1474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="8506@0" Pin0InfoVect0LinkObjId="SW-47244_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="474,-1454 474,-1474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f4bd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1704 1224,-1735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8513@x" ObjectIDND1="8510@x" ObjectIDZND0="g_1f78a10@0" ObjectIDZND1="g_1f35c00@0" ObjectIDZND2="11877@1" Pin0InfoVect0LinkObjId="g_1f78a10_0" Pin0InfoVect1LinkObjId="g_1f35c00_0" Pin0InfoVect2LinkObjId="g_1f446e0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47251_0" Pin1InfoVect1LinkObjId="SW-47248_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1704 1224,-1735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f4bf70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1690 1224,-1704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="8510@1" ObjectIDZND0="g_1f78a10@0" ObjectIDZND1="g_1f35c00@0" ObjectIDZND2="11877@1" Pin0InfoVect0LinkObjId="g_1f78a10_0" Pin0InfoVect1LinkObjId="g_1f35c00_0" Pin0InfoVect2LinkObjId="g_1f446e0_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47248_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1690 1224,-1704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f4c1d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1628 1224,-1653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="8512@x" ObjectIDND1="8508@x" ObjectIDZND0="8510@0" Pin0InfoVect0LinkObjId="SW-47248_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47250_0" Pin1InfoVect1LinkObjId="SW-47246_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1628 1224,-1653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f4ccc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1242,-1628 1224,-1628 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="8512@0" ObjectIDZND0="8508@x" ObjectIDZND1="8510@x" Pin0InfoVect0LinkObjId="SW-47246_0" Pin0InfoVect1LinkObjId="SW-47248_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-47250_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1242,-1628 1224,-1628 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f4cf20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1628 1224,-1605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="8512@x" ObjectIDND1="8510@x" ObjectIDZND0="8508@1" Pin0InfoVect0LinkObjId="SW-47246_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47250_0" Pin1InfoVect1LinkObjId="SW-47248_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1628 1224,-1605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f4f980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1169,-1339 1046,-1339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="8514@2" ObjectIDZND0="g_1f07800@0" ObjectIDZND1="22113@x" Pin0InfoVect0LinkObjId="g_1f07800_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f62bb0_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1169,-1339 1046,-1339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f502f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-1325 1046,-1339 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="breaker" ObjectIDND0="g_1f07800@0" ObjectIDZND0="8514@x" ObjectIDZND1="22113@x" Pin0InfoVect0LinkObjId="g_1f62bb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f07800_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-1325 1046,-1339 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1f504e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-1339 1046,-1434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="8514@x" ObjectIDND1="g_1f07800@0" ObjectIDZND0="22113@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1f62bb0_0" Pin1InfoVect1LinkObjId="g_1f07800_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-1339 1046,-1434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f867f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1557 1224,-1540 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="8508@x" ObjectIDND1="8511@x" ObjectIDZND0="48332@0" Pin0InfoVect0LinkObjId="g_1f7ed40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-47246_0" Pin1InfoVect1LinkObjId="SW-47249_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1557 1224,-1540 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_1f86a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1224,-1540 1224,-1529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="48332@0" ObjectIDZND0="8509@1" Pin0InfoVect0LinkObjId="SW-47247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1f7ed40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1224,-1540 1224,-1529 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-47037" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 335.000000 -1791.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="8391" ObjectName="DYN-CX_LHSY"/>
     <cge:Meas_Ref ObjectId="47037"/>
    </metadata>
   </g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="19" y="-1904"/>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_LHSY.CX_LHS_GN1">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.000000 -872.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15723" ObjectName="SM-CX_LHSY.CX_LHS_GN1"/>
    <cge:TPSR_Ref TObjectID="15723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_LHSY.CX_LHS_GN2">
    <use class="BV-6KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1160.000000 -875.000000)" xlink:href="#hydroGenerator:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="15724" ObjectName="SM-CX_LHSY.CX_LHS_GN2"/>
    <cge:TPSR_Ref TObjectID="15724"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1e2ada0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 493.750000 -807.000000) translate(0,15)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1da6430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.375000 -889.000000) translate(0,15)">1号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef8820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 447.875000 -850.000000) translate(0,12)">1YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef9580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.875000 -832.000000) translate(0,12)">1YH3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef98b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 800.875000 -973.000000) translate(0,12)">3YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ef9dd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1098.875000 -855.000000) translate(0,12)">2YH1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efa410" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.875000 -829.000000) translate(0,12)">2YH3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.875000 -893.000000) translate(0,15)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.875000 -893.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.875000 -893.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.875000 -893.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efa690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 563.875000 -893.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efad70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1133.000000 -823.000000) translate(0,15)">2号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.875000 -895.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.875000 -895.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.875000 -895.000000) translate(0,51)">励</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.875000 -895.000000) translate(0,69)">磁</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efaf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1215.875000 -895.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efb1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 458.375000 -1333.000000) translate(0,15)">2号厂用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efb3e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 495.375000 -1838.000000) translate(0,15)">10kV坝区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,15)">空</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,33)">龙</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,51)">河</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,69)">零</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,87)">级</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efc480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 711.000000 -1839.000000) translate(0,105)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efd7b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 964.000000 -1797.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efdcf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1040.000000 -1797.000000) translate(0,15)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1efdf70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1164.875000 -1696.000000) translate(0,12)">4YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1efe570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 900.375000 -1582.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f00a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.875000 -1382.000000) translate(0,12)">5YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="36" graphid="g_1f0c6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.875000 -764.000000) translate(0,29)">老虎山一级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0ddb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -1495.000000) translate(0,12)">462</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0e690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -1689.000000) translate(0,12)">463</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0e8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -1636.000000) translate(0,12)">4631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0eb10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.000000 -1737.000000) translate(0,12)">4632</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0ed50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 747.500000 -1792.000000) translate(0,12)">4633</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0ef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -1276.000000) translate(0,12)">612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0f1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 -1040.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0f9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 556.000000 -1092.000000) translate(0,12)">60117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f0fde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -1048.000000) translate(0,12)">61007</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f10330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1196.000000 -1043.000000) translate(0,12)">602</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f158e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1192.000000 -1110.000000) translate(0,12)">6021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f15f10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1211.000000 -1093.000000) translate(0,12)">60217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f16150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1143.000000 -1402.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 364.000000 -1148.000000) translate(0,12)">6kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f16970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -1612.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f16bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 40.000000 -1283.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1f18e60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 32.000000 -1762.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1f1d100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 181.000000 -1871.500000) translate(0,16)">老虎山一级站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1e7d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 632.875000 -1410.000000) translate(0,12)">6YH</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f1ecb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 477.000000 -1551.000000) translate(0,12)">4621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f22270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 504.000000 -1477.000000) translate(0,12)">4700</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f228a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 652.000000 -1558.000000) translate(0,12)">4100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f22ae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 681.000000 -1497.000000) translate(0,12)">41007</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f22d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 469.000000 -965.000000) translate(0,12)">6101</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f22f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 487.000000 -945.000000) translate(0,12)">61017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f231a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 612.000000 -965.000000) translate(0,12)">6102</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f233e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 647.000000 -901.000000) translate(0,12)">61027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f23620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 543.000000 -1112.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f23860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.000000 -1107.000000) translate(0,12)">6100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f23aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 961.000000 -1105.000000) translate(0,12)">6111</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f23ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 963.000000 -1042.000000) translate(0,12)">611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f23f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -1049.000000) translate(0,12)">61117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f24160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1121.000000 -967.000000) translate(0,12)">6201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f243a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1134.000000 -949.000000) translate(0,12)">62017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f245e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -967.000000) translate(0,12)">6202</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f24820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1274.000000 -949.000000) translate(0,12)">62027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f24a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 592.000000 -1687.000000) translate(0,12)">461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f24ca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -1634.000000) translate(0,12)">4611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f24ee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 588.000000 -1737.000000) translate(0,12)">4612</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 587.500000 -1795.000000) translate(0,12)">4613</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 787.000000 -1509.000000) translate(0,12)">422</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f255a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 -1557.000000) translate(0,12)">4222</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f257e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 783.000000 -1459.000000) translate(0,12)">4221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 785.000000 -1206.000000) translate(0,12)">6121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25c60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -1336.000000) translate(0,12)">61227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f25ea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 719.000000 -1265.000000) translate(0,12)">61217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f260e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 990.000000 -1646.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f26320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1070.000000 -1646.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f26560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 971.000000 -1525.000000) translate(0,12)">3601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f267a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1055.000000 -1481.500000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f29100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1228.000000 -1198.000000) translate(0,12)">6311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2c9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -1517.000000) translate(0,12)">1811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -1654.000000) translate(0,12)">18160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2ce40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -1683.000000) translate(0,12)">1816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2d080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1233.000000 -1599.000000) translate(0,12)">181</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2d2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1240.000000 -1583.000000) translate(0,12)">18117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2d500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 -1730.000000) translate(0,12)">18167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -827.000000) translate(0,12)">1、2号发电机参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -827.000000) translate(0,27)">SF6300-10/2600</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -827.000000) translate(0,42)">6300kW   6.3kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f2dc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 817.000000 -827.000000) translate(0,57)">CoS∮=0.8 721A</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -1306.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -1306.000000) translate(0,27)">SFS8-20000/121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -1306.000000) translate(0,42)">20000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -1306.000000) translate(0,57)">YN,yn0d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 -1392.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f32af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 613.000000 -1392.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f345e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 557.875000 -908.000000) translate(0,12)">1YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f34bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1210.875000 -909.000000) translate(0,12)">2YH2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f34e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 742.875000 -1049.000000) translate(0,12)">1TPB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f35500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1266.875000 -1223.000000) translate(0,12)">1PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f359c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.875000 -1426.000000) translate(0,12)">3PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f36540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.875000 -1742.000000) translate(0,12)">4PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1f36b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 549.875000 -1441.000000) translate(0,12)">6PB</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1f458f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -1828.000000) translate(0,15)">一二级联线</text>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_LHSY.CX_LHSY_6IM">
    <g class="BV-6KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="355,-1154 1394,-1154 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8489" ObjectName="BS-CX_LHSY.CX_LHSY_6IM"/>
    <cge:TPSR_Ref TObjectID="8489"/></metadata>
   <polyline fill="none" opacity="0" points="355,-1154 1394,-1154 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LHSY.CX_LHSY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-1590 828,-1590 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="8490" ObjectName="BS-CX_LHSY.CX_LHSY_9IM"/>
    <cge:TPSR_Ref TObjectID="8490"/></metadata>
   <polyline fill="none" opacity="0" points="409,-1590 828,-1590 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-1557 1086,-1557 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="926,-1557 1086,-1557 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_LHSY.XM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1213,-1540 1235,-1540 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="48332" ObjectName="BS-CX_LHSY.XM"/>
    <cge:TPSR_Ref TObjectID="48332"/></metadata>
   <polyline fill="none" opacity="0" points="1213,-1540 1235,-1540 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="967" cy="-1557" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="981" cy="-1557" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1060" cy="-1557" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8490" cx="743" cy="-1590" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8489" cx="1184" cy="-1154" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8489" cx="952" cy="-1154" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8489" cx="816" cy="-1154" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="8489" cx="535" cy="-1154" fill="rgb(139,139,0)" r="4" stroke="rgb(139,139,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1046" cy="-1557" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48332" cx="1224" cy="-1540" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="48332" cx="1224" cy="-1540" fill="rgb(170,85,127)" r="4" stroke="rgb(170,85,127)" stroke-width="1"/>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_LHSY"/>
</svg>