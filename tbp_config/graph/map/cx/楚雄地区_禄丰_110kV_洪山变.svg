<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-24" aopId="256" id="thSvg" viewBox="3013 -1263 2178 1261">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape18">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="52" x2="52" y1="45" y2="16"/>
    <polyline fill="none" points="25,101 23,101 21,100 20,100 18,99 17,98 15,97 14,95 13,93 13,92 12,90 12,88 12,86 13,84 13,83 14,81 15,80 17,78 18,77 20,76 21,76 23,75 25,75 27,75 29,76 30,76 32,77 33,78 35,80 36,81 37,83 37,84 38,86 38,88 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="38" x2="26" y1="88" y2="88"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="25" x2="25" y1="100" y2="108"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="15" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="54" y2="47"/>
    <polyline fill="none" points="43,15 44,15 45,15 45,15 46,15 46,16 47,16 47,17 48,17 48,18 48,18 48,19 49,20 49,20 49,21 48,22 48,22 48,23 48,23 47,24 47,24 46,25 46,25 45,25 45,26 44,26 43,26 "/>
    <polyline fill="none" points="43,25 44,25 45,25 45,25 46,26 46,26 47,26 47,27 48,27 48,28 48,29 48,29 49,30 49,31 49,31 48,32 48,33 48,33 48,34 47,34 47,35 46,35 46,35 45,36 45,36 44,36 43,36 "/>
    <polyline fill="none" points="43,36 44,36 45,36 45,37 46,37 46,37 47,38 47,38 48,39 48,39 48,40 48,40 49,41 49,42 49,42 48,43 48,44 48,44 48,45 47,45 47,46 46,46 46,47 45,47 45,47 44,47 43,47 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.42985" x1="26" x2="26" y1="88" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="9" x2="42" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="26" x2="41" y1="8" y2="8"/>
    <rect height="24" stroke-width="0.398039" width="12" x="1" y="23"/>
    <rect height="23" stroke-width="0.398039" width="12" x="20" y="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="8" x2="42" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="0.607143" x1="7" x2="7" y1="54" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="8" x2="8" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="43" x2="43" y1="2" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72286" x1="26" x2="26" y1="14" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="16" x2="36" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="16" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="16" y2="24"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="12" x2="0" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="5" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="3" x2="9" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape54">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="58" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="6" x2="6" y1="8" y2="37"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape59">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="23" y2="23"/>
    <circle cx="9" cy="9" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="20" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <polyline fill="none" points="31,61 9,39 9,30 "/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
   </symbol>
   <symbol id="lightningRod:shape103">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="23" x2="22" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.84362" x1="1" x2="22" y1="46" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.786486" x1="22" x2="15" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="10" x2="10" y1="35" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.675211" x1="11" x2="11" y1="5" y2="16"/>
    <polyline fill="none" points="11,28 11,28 11,28 11,28 12,28 12,28 12,27 12,27 13,27 13,27 13,26 13,26 13,26 13,25 13,25 13,24 13,24 13,24 13,23 12,23 12,23 12,23 12,22 11,22 11,22 11,22 "/>
    <polyline fill="none" points="10,34 11,34 11,34 11,34 11,34 12,34 12,34 12,33 12,33 12,33 12,32 13,32 13,32 13,31 13,31 13,31 12,30 12,30 12,30 12,29 12,29 12,29 11,29 11,28 11,28 11,28 "/>
    <polyline fill="none" points="11,22 11,22 11,22 11,22 12,22 12,21 12,21 12,21 13,21 13,20 13,20 13,20 13,19 13,19 13,19 13,18 13,18 13,17 13,17 12,17 12,17 12,16 12,16 11,16 11,16 11,16 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.532624" x1="9" x2="12" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="8" x2="13" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.565049" x1="4" x2="17" y1="46" y2="46"/>
   </symbol>
   <symbol id="lightningRod:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="26" y1="40" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="43" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="42" x2="42" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="35" x2="35" y1="48" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="34" x2="16" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="16" x2="16" y1="48" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="9" x2="9" y1="48" y2="31"/>
    <circle cx="30" cy="8" r="8.5" stroke-width="1"/>
    <circle cx="26" cy="15" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="0" x2="9" y1="40" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape148">
    <circle cx="28" cy="40" r="6.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="8" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="46" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="29" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="29" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="28" y1="32" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="41" y1="40" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="38" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="40" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="35" y1="31" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="40" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="31" y1="40" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="28" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="21" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="2" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="11" y1="55" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="25" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="1" x2="14" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="1" x2="14" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="6" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="1" x2="14" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="1" x2="14" y1="25" y2="25"/>
    <circle cx="37" cy="29" r="6.5" stroke-width="1"/>
    <circle cx="37" cy="40" r="6.5" stroke-width="1"/>
    <circle cx="27" cy="29" r="6.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape149">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="6" x2="13" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="13" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="6" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="7" x2="10" y1="10" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="12" y1="7" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.183673" x1="10" x2="10" y1="4" y2="7"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="48"/>
    <polyline fill="none" points="31,61 9,39 9,30 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="13" y1="63" y2="63"/>
    <circle cx="9" cy="20" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="9" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline fill="none" points="5,36 0,46 10,46 5,36 "/>
    <polyline fill="none" points="5,24 0,14 10,14 5,24 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
   </symbol>
   <symbol id="load:shape12">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="6" x2="6" y1="25" y2="1"/>
    <polyline fill="none" points="1,8 6,2 11,8 "/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="transformer:shape4_0">
    <circle cx="68" cy="45" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="60" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="60" x2="58" y1="90" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="60" y1="90" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="60" y1="56" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="67" y1="46" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="82" x2="74" y1="53" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="74" x2="74" y1="37" y2="46"/>
   </symbol>
   <symbol id="transformer:shape4_1">
    <circle cx="38" cy="61" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="30" y1="66" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="37" y1="73" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="37" x2="37" y1="57" y2="66"/>
   </symbol>
   <symbol id="transformer:shape4-2">
    <circle cx="38" cy="29" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="47" x2="32" y1="23" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="32" x2="32" y1="32" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape20_0">
    <circle cx="20" cy="16" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="72" y1="39" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="78" x2="78" y1="43" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="75" x2="75" y1="44" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="73" x2="73" y1="34" y2="46"/>
    <polyline DF8003:Layer="PUBLIC" points="84,14 71,20 71,7 84,14 83,14 84,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="56" x2="98" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="13" y1="19" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="11" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="19" y1="15" y2="11"/>
   </symbol>
   <symbol id="transformer2:shape20_1">
    <polyline fill="none" points="41,15 41,40 70,40 "/>
    <circle cx="42" cy="16" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="47" y1="15" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="47" y1="15" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="36" y1="15" y2="15"/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline fill="none" points="24,36 8,36 8,26 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="Tag:shape0">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">限</text>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">开关检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">引</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">穿</text>
   </symbol>
   <symbol id="Tag:shape9">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注1</text>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注2</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">禁止操作</text>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    
   </symbol>
   <symbol id="Tag:shape25">
    
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="78" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.246377 -0.000000 0.000000 -1.035714 2.739130 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="25" stroke="rgb(255,0,0)" stroke-width="4.14286" width="32" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(0.512795 -0.000000 0.000000 -1.035714 2.846957 19.678571) translate(0,12)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape28">
    
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(154,205,50);fill:none}
.BKBV-38KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1271" width="2188" x="3008" y="-1268"/>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-191 4556,-191 4545,-208 4534,-191 " stroke="rgb(255,255,255)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="3618,-210 3640,-210 3629,-227 3618,-210 " stroke="rgb(255,255,255)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-24066">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -1001.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3797" ObjectName="SW-CX_HS.CX_HS_172BK"/>
     <cge:Meas_Ref ObjectId="24066"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23875">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -994.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3781" ObjectName="SW-CX_HS.CX_HS_171BK"/>
     <cge:Meas_Ref ObjectId="23875"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24174">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -841.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3813" ObjectName="SW-CX_HS.CX_HS_112BK"/>
     <cge:Meas_Ref ObjectId="24174"/>
    <cge:TPSR_Ref TObjectID="3813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24975">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -431.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3872" ObjectName="SW-CX_HS.CX_HS_073BK"/>
     <cge:Meas_Ref ObjectId="24975"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24887">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -430.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3860" ObjectName="SW-CX_HS.CX_HS_074BK"/>
     <cge:Meas_Ref ObjectId="24887"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23914">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -597.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3794" ObjectName="SW-CX_HS.CX_HS_001BK"/>
     <cge:Meas_Ref ObjectId="23914"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24283">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3821" ObjectName="SW-CX_HS.CX_HS_075BK"/>
     <cge:Meas_Ref ObjectId="24283"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24382">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3831" ObjectName="SW-CX_HS.CX_HS_077BK"/>
     <cge:Meas_Ref ObjectId="24382"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24245">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3994.000000 -602.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3818" ObjectName="SW-CX_HS.CX_HS_012BK"/>
     <cge:Meas_Ref ObjectId="24245"/>
    <cge:TPSR_Ref TObjectID="3818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -597.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3810" ObjectName="SW-CX_HS.CX_HS_002BK"/>
     <cge:Meas_Ref ObjectId="24104"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24320">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -433.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3824" ObjectName="SW-CX_HS.CX_HS_084BK"/>
     <cge:Meas_Ref ObjectId="24320"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24906">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -434.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3863" ObjectName="SW-CX_HS.CX_HS_085BK"/>
     <cge:Meas_Ref ObjectId="24906"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24441">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3839" ObjectName="SW-CX_HS.CX_HS_083BK"/>
     <cge:Meas_Ref ObjectId="24441"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24412">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3835" ObjectName="SW-CX_HS.CX_HS_082BK"/>
     <cge:Meas_Ref ObjectId="24412"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24352">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -432.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3827" ObjectName="SW-CX_HS.CX_HS_076BK"/>
     <cge:Meas_Ref ObjectId="24352"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4860.000000 -165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3763.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4235.000000 -78.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4788.000000 -165.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5007.000000 -166.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -1133.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -1089.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24954">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4831.000000 -1041.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3869" ObjectName="SW-CX_HS.CX_HS_373BK"/>
     <cge:Meas_Ref ObjectId="24954"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -909.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3846" ObjectName="SW-CX_HS.CX_HS_374BK"/>
     <cge:Meas_Ref ObjectId="24486"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24579">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -856.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3849" ObjectName="SW-CX_HS.CX_HS_375BK"/>
     <cge:Meas_Ref ObjectId="24579"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -777.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3852" ObjectName="SW-CX_HS.CX_HS_376BK"/>
     <cge:Meas_Ref ObjectId="24613"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24647">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4828.000000 -687.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3855" ObjectName="SW-CX_HS.CX_HS_381BK"/>
     <cge:Meas_Ref ObjectId="24647"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24929">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -567.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3866" ObjectName="SW-CX_HS.CX_HS_382BK"/>
     <cge:Meas_Ref ObjectId="24929"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -516.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4830.000000 -472.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -429.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.918919 -0.000000 0.000000 -1.000000 4639.000000 -941.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3790" ObjectName="SW-CX_HS.CX_HS_301BK"/>
     <cge:Meas_Ref ObjectId="23900"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4659.000000 -723.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3843" ObjectName="SW-CX_HS.CX_HS_312BK"/>
     <cge:Meas_Ref ObjectId="24469"/>
    <cge:TPSR_Ref TObjectID="3843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24091">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4630.000000 -613.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3806" ObjectName="SW-CX_HS.CX_HS_302BK"/>
     <cge:Meas_Ref ObjectId="24091"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24994">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4612.000000 -443.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3873" ObjectName="SW-CX_HS.CX_HS_086BK"/>
     <cge:Meas_Ref ObjectId="24994"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5537"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="5539"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="5541"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3562.000000 -713.000000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3874" ObjectName="TF-CX_HS.CX_HS_1T"/>
    <cge:TPSR_Ref TObjectID="3874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="5544"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="5546"/>
     </metadata>
     <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="5548"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 -713.000000)" xlink:href="#transformer:shape4-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="3875" ObjectName="TF-CX_HS.CX_HS_2T"/>
    <cge:TPSR_Ref TObjectID="3875"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-539 3992,-539 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3767" ObjectName="BS-CX_HS.CX_HS_9IM"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   <polyline fill="none" opacity="0" points="3517,-539 3992,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_9IIM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4051,-539 4654,-539 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3768" ObjectName="BS-CX_HS.CX_HS_9IIM"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   <polyline fill="none" opacity="0" points="4051,-539 4654,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4769,-122 4887,-122 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4769,-122 4887,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4915,-122 5033,-122 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4915,-122 5033,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1165 4743,-763 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3765" ObjectName="BS-CX_HS.CX_HS_3IM"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   <polyline fill="none" opacity="0" points="4743,-1165 4743,-763 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-725 4743,-412 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3766" ObjectName="BS-CX_HS.CX_HS_3IIM"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   <polyline fill="none" opacity="0" points="4743,-725 4743,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_1IM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3515,-922 3826,-922 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3763" ObjectName="BS-CX_HS.CX_HS_1IM"/>
    <cge:TPSR_Ref TObjectID="3763"/></metadata>
   <polyline fill="none" opacity="0" points="3515,-922 3826,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="AC-CX_HS.CX_HS_1IIM">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3898,-925 4259,-925 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="3764" ObjectName="BS-CX_HS.CX_HS_1IIM"/>
    <cge:TPSR_Ref TObjectID="3764"/></metadata>
   <polyline fill="none" opacity="0" points="3898,-925 4259,-925 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_1C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3824.000000 -169.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11722" ObjectName="CB-CX_HS.CX_HS_1C"/>
    <cge:TPSR_Ref TObjectID="11722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_2C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3928.000000 -167.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11723" ObjectName="CB-CX_HS.CX_HS_2C"/>
    <cge:TPSR_Ref TObjectID="11723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_3C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 -168.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11724" ObjectName="CB-CX_HS.CX_HS_3C"/>
    <cge:TPSR_Ref TObjectID="11724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_HS.CX_HS_4C">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4253.000000 -180.000000)" xlink:href="#capacitor:shape18"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11725" ObjectName="CB-CX_HS.CX_HS_4C"/>
    <cge:TPSR_Ref TObjectID="11725"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_374_1TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17013"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4990.000000 -904.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4990.000000 -904.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12146" ObjectName="TF-CX_HS.CX_HS_374_1TZyb"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_HS.CX_HS_2TZyb">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="17017"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4069.000000 -417.000000)" xlink:href="#transformer2:shape20_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4069.000000 -417.000000)" xlink:href="#transformer2:shape20_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="12145" ObjectName="TF-CX_HS.CX_HS_2TZyb"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29210d0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3558.000000 -846.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2921d80">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3497.000000 -851.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2930510">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3670.000000 -379.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29311c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3687.000000 -428.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29399e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4490.000000 -691.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_293f450">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3872.000000 -780.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2946da0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3753.000000 -248.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2954ef0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -432.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2963eb0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 4392.000000 -378.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2964ba0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4409.000000 -427.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a6d930">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 4381.500000 -64.500000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a81e50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4803.000000 -323.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8b330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5024.000000 -330.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a92de0">
    <use class="BV-110KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4326.000000 -845.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a93b50">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4373.000000 -850.000000)" xlink:href="#lightningRod:shape54"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a96680">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3543.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a973d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3608.000000 -368.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a98120">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3775.000000 -372.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a98e70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3843.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a99bc0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9a910">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4156.000000 -370.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9b660">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9c3b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4616.000000 -372.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9d100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4560.000000 -373.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a9de50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -371.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2abfac0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5089.000000 -858.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac5b80">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5089.000000 -780.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ace950">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 5087.000000 -690.000000)" xlink:href="#lightningRod:shape59"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2acfc90">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4858.500000 -636.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2afbb90">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4582.000000 -1024.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b09290">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -720.000000)" xlink:href="#lightningRod:shape103"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b17c80">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 -1083.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b18fd0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4037.000000 -1098.000000)" xlink:href="#lightningRod:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b229a0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3524.000000 -243.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b59b30">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3552.000000 -1040.000000)" xlink:href="#lightningRod:shape148"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5c8a0">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -1047.000000)" xlink:href="#lightningRod:shape148"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5fdc0">
    <use class="BV-10KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 3591.000000 -245.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b66550">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4481.000000 -2.000000)" xlink:href="#lightningRod:shape149"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6c880">
    <use class="BV-35KV" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 4855.500000 -993.500000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb7430">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.500000 -861.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bb7c00">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5002.500000 -572.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbcfc0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4983.500000 -692.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbdea0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4989.500000 -782.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbed80">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4988.500000 -914.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bbfc60">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4992.500000 -1046.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc3460">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5001.500000 -434.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc4340">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5001.500000 -477.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc5220">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4999.500000 -521.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc6100">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5009.500000 -1094.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc6fe0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5004.500000 -1138.500000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcdf90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4527.000000 -253.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcea50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -241.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bcf7c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4452.000000 -246.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd02d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.000000 -686.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd1040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4227.000000 -687.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd48b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3854.000000 -292.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd5620">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3957.000000 -292.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd6390">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4165.000000 -293.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bd7100">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4282.000000 -294.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be1390">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3731.000000 -1115.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be1e10">
    <use class="BV-110KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4121.000000 -1130.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be2b80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -1063.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be38f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -877.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be4660">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5004.000000 -799.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be53d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5002.000000 -709.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be6140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5022.000000 -551.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be6eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4849.000000 -604.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be7c20">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4846.000000 -970.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be8990">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -52.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2be9700">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3954.000000 -53.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23490" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4134.000000 -754.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23490" ObjectName="CX_HS:CX_HS_2T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23491" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4134.000000 -740.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23491" ObjectName="CX_HS:CX_HS_2T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 0.000000 0.000000 2.335135 3124.500000 -1126.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23452" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3752.000000 -784.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23452" ObjectName="CX_HS:CX_HS_1T_Tmp2"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-23451" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3753.000000 -800.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23451" ObjectName="CX_HS:CX_HS_1T_Tmp1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-62624" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 0.000000 0.000000 1.395515 3164.538462 -992.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="62624" ObjectName="CX_HS:CX_HS_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23495" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3858.000000 -926.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23495" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3813"/>
     <cge:Term_Ref ObjectID="5414"/>
    <cge:TPSR_Ref TObjectID="3813"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23522" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4019.000000 -657.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23522" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3818"/>
     <cge:Term_Ref ObjectID="5424"/>
    <cge:TPSR_Ref TObjectID="3818"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23534" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -225.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23534" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23535" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -225.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23535" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23531" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4483.000000 -225.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23531" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3824"/>
     <cge:Term_Ref ObjectID="5436"/>
    <cge:TPSR_Ref TObjectID="3824"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-23478" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4155.000000 -768.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23478" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3875"/>
     <cge:Term_Ref ObjectID="5543"/>
    <cge:TPSR_Ref TObjectID="3875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23398" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -623.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23398" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23399" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -623.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23399" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23400" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -623.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23400" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23404" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -623.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23404" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23406" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3520.000000 -623.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23406" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3767"/>
     <cge:Term_Ref ObjectID="5324"/>
    <cge:TPSR_Ref TObjectID="3767"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23384" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -1261.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23384" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23385" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -1261.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23385" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23386" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -1261.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23386" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23390" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -1261.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23390" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23392" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4761.000000 -1261.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23392" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3765"/>
     <cge:Term_Ref ObjectID="5322"/>
    <cge:TPSR_Ref TObjectID="3765"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23561" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4625.000000 -753.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23561" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3843"/>
     <cge:Term_Ref ObjectID="5474"/>
    <cge:TPSR_Ref TObjectID="3843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23401" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4496.000000 -617.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23401" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23402" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4496.000000 -617.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23402" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23403" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4496.000000 -617.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23403" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23405" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4496.000000 -617.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23405" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23408" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4496.000000 -617.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23408" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3768"/>
     <cge:Term_Ref ObjectID="5325"/>
    <cge:TPSR_Ref TObjectID="3768"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-23387" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -411.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23387" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-23388" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -411.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23388" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-23389" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -411.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23389" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uo" PreSymbol="0" appendix="" decimal="2" id="ME-23391" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -411.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23391" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-23394" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4875.000000 -411.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23394" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3766"/>
     <cge:Term_Ref ObjectID="5323"/>
    <cge:TPSR_Ref TObjectID="3766"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23519" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -1204.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23519" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23520" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -1204.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23520" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23513" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3807.000000 -1204.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23513" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3781"/>
     <cge:Term_Ref ObjectID="5350"/>
    <cge:TPSR_Ref TObjectID="3781"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23507" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4224.000000 -1191.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23507" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23508" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4224.000000 -1191.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23508" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23501" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4224.000000 -1191.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23501" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3797"/>
     <cge:Term_Ref ObjectID="5382"/>
    <cge:TPSR_Ref TObjectID="3797"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23447" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -642.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23447" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23448" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -642.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23448" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23443" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3700.000000 -642.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3794"/>
     <cge:Term_Ref ObjectID="5376"/>
    <cge:TPSR_Ref TObjectID="3794"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23486" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -645.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23486" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23487" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -645.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23487" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23482" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4328.000000 -645.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23482" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3810"/>
     <cge:Term_Ref ObjectID="5408"/>
    <cge:TPSR_Ref TObjectID="3810"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23651" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3545.000000 -178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23651" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23652" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3545.000000 -178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23652" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23648" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3545.000000 -178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23648" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3872"/>
     <cge:Term_Ref ObjectID="5532"/>
    <cge:TPSR_Ref TObjectID="3872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23627" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -178.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23627" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23628" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -178.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23628" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23624" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3618.000000 -178.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23624" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3860"/>
     <cge:Term_Ref ObjectID="5508"/>
    <cge:TPSR_Ref TObjectID="3860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23528" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -228.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23529" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -228.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23525" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3755.000000 -228.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3821"/>
     <cge:Term_Ref ObjectID="5430"/>
    <cge:TPSR_Ref TObjectID="3821"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23541" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3855.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23541" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3827"/>
     <cge:Term_Ref ObjectID="5442"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23537" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3855.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23537" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3827"/>
     <cge:Term_Ref ObjectID="5442"/>
    <cge:TPSR_Ref TObjectID="3827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23547" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3934.000000 -167.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3831"/>
     <cge:Term_Ref ObjectID="5450"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23543" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3934.000000 -167.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23543" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3831"/>
     <cge:Term_Ref ObjectID="5450"/>
    <cge:TPSR_Ref TObjectID="3831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23553" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4133.000000 -168.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23553" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3835"/>
     <cge:Term_Ref ObjectID="5458"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23549" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4133.000000 -168.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23549" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3835"/>
     <cge:Term_Ref ObjectID="5458"/>
    <cge:TPSR_Ref TObjectID="3835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23559" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -166.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3839"/>
     <cge:Term_Ref ObjectID="5466"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23555" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4252.000000 -166.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23555" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3839"/>
     <cge:Term_Ref ObjectID="5466"/>
    <cge:TPSR_Ref TObjectID="3839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23633" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4549.000000 -187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23633" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23634" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4549.000000 -187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23634" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23630" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4549.000000 -187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23630" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3863"/>
     <cge:Term_Ref ObjectID="5514"/>
    <cge:TPSR_Ref TObjectID="3863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23645" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -1063.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23645" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23646" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -1063.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23646" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23642" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -1063.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23642" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3869"/>
     <cge:Term_Ref ObjectID="5526"/>
    <cge:TPSR_Ref TObjectID="3869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23566" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -952.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23567" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -952.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23567" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23564" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -952.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23564" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3846"/>
     <cge:Term_Ref ObjectID="5480"/>
    <cge:TPSR_Ref TObjectID="3846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23572" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -887.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23572" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23573" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -887.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23573" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23569" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -887.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23569" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3849"/>
     <cge:Term_Ref ObjectID="5486"/>
    <cge:TPSR_Ref TObjectID="3849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23578" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -806.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23578" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23579" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -806.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23579" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23575" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -806.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23575" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3852"/>
     <cge:Term_Ref ObjectID="5492"/>
    <cge:TPSR_Ref TObjectID="3852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23584" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -713.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23584" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23585" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -713.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23585" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23581" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -713.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23581" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3855"/>
     <cge:Term_Ref ObjectID="5498"/>
    <cge:TPSR_Ref TObjectID="3855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23639" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -598.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23639" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23640" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -598.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23640" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23636" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5144.000000 -598.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23636" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3866"/>
     <cge:Term_Ref ObjectID="5520"/>
    <cge:TPSR_Ref TObjectID="3866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23435" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4660.000000 -939.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23436" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4660.000000 -939.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23436" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23431" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4660.000000 -939.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3790"/>
     <cge:Term_Ref ObjectID="5368"/>
    <cge:TPSR_Ref TObjectID="3790"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23474" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4662.000000 -610.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23475" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4662.000000 -610.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23470" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4662.000000 -610.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3806"/>
     <cge:Term_Ref ObjectID="5400"/>
    <cge:TPSR_Ref TObjectID="3806"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-23439" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3775.000000 -816.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3874"/>
     <cge:Term_Ref ObjectID="5538"/>
    <cge:TPSR_Ref TObjectID="3874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-23657" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4653.000000 -485.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23657" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-23658" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4653.000000 -485.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23658" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-23654" prefix="">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4653.000000 -485.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="23654" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="3873"/>
     <cge:Term_Ref ObjectID="5534"/>
    <cge:TPSR_Ref TObjectID="3873"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259" style="fill-opacity:0">
    <a>
     
     <rect height="41" qtmmishow="hidden" width="139" x="3136" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3136" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124" style="fill-opacity:0">
    <a>
     
     <rect height="69" qtmmishow="hidden" width="77" x="3088" y="-1202"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3088" y="-1202"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <polygon fill="rgb(255,255,255)" points="3298,-1194 3295,-1197 3295,-1134 3298,-1137 3298,-1194" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3298,-1194 3295,-1197 3358,-1197 3355,-1194 3298,-1194" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3298,-1137 3295,-1134 3358,-1134 3355,-1137 3298,-1137" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3355,-1194 3358,-1197 3358,-1134 3355,-1137 3355,-1194" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="57" stroke="rgb(255,255,255)" width="57" x="3298" y="-1194"/>
     <rect height="57" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="3298" y="-1194"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <rect height="19" qtmmishow="hidden" width="24" x="3617" y="-228"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="3617" y="-228"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" style="fill-opacity:0">
    <a>
     <rect height="19" qtmmishow="hidden" width="24" x="4533" y="-209"/>
    </a>
   <metadata/><rect fill="white" height="19" opacity="0" stroke="white" transform="" width="24" x="4533" y="-209"/></g>
  </g><g id="MotifButton_Layer">
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3136" y="-1185"/></g>
   <g href="lf_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3088" y="-1202"/></g>
   <g href="AVC洪山站.svg" style="fill-opacity:0"><rect height="57" qtmmishow="hidden" stroke="rgb(0,0,0)" width="57" x="3298" y="-1194"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="3617" y="-228"/></g>
   <g href="cx_地调_重要用电用户表.svg" style="fill-opacity:0"><rect height="19" qtmmishow="hidden" width="24" x="4533" y="-209"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3019" y="-1194"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(0,0,0)" stroke-width="1" width="360" x="3014" y="-606"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b60870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3673.000000 -296.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b632e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4395.000000 -320.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6a0c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4965.500000 -979.500000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6fc40">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 4991.500000 -622.500000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_382Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5084.000000 -571.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12151" ObjectName="EC-CX_HS.CX_HS_382Ld"/>
    <cge:TPSR_Ref TObjectID="12151"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_375Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5105.000000 -860.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12148" ObjectName="EC-CX_HS.CX_HS_375Ld"/>
    <cge:TPSR_Ref TObjectID="12148"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_381Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5105.000000 -691.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12150" ObjectName="EC-CX_HS.CX_HS_381Ld"/>
    <cge:TPSR_Ref TObjectID="12150"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_376Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5103.000000 -781.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12149" ObjectName="EC-CX_HS.CX_HS_376Ld"/>
    <cge:TPSR_Ref TObjectID="12149"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_373Ld">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5107.000000 -1045.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12147" ObjectName="EC-CX_HS.CX_HS_373Ld"/>
    <cge:TPSR_Ref TObjectID="12147"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_084Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4486.000000 -259.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12152" ObjectName="EC-CX_HS.CX_HS_084Ld"/>
    <cge:TPSR_Ref TObjectID="12152"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_HS.CX_HS_075Ld">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3774.000000 -257.000000)" xlink:href="#load:shape12"/>
    <metadata>
     <cge:PSR_Ref ObjectId="12153" ObjectName="EC-CX_HS.CX_HS_075Ld"/>
    <cge:TPSR_Ref TObjectID="12153"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-110KV" id="g_28d9f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-798 3600,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3874@1" ObjectIDZND0="3787@x" ObjectIDZND1="3788@x" Pin0InfoVect0LinkObjId="SW-23887_0" Pin0InfoVect1LinkObjId="SW-23888_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd2950_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-798 3600,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28db5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-825 3617,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDND1="3787@x" ObjectIDZND0="3788@0" Pin0InfoVect0LinkObjId="SW-23888_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd2950_0" Pin1InfoVect1LinkObjId="SW-23887_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-825 3617,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28db7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3653,-825 3670,-825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3788@1" ObjectIDZND0="g_28db9d0@0" Pin0InfoVect0LinkObjId="g_28db9d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23888_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3653,-825 3670,-825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28dd6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-825 3600,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDND1="3788@x" ObjectIDZND0="3787@0" Pin0InfoVect0LinkObjId="SW-23887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd2950_0" Pin1InfoVect1LinkObjId="SW-23888_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-825 3600,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28df490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-953 3630,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3772@1" ObjectIDZND0="g_28df680@0" Pin0InfoVect0LinkObjId="g_28df680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23713_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-953 3630,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e3810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3612,-1031 3629,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3771@1" ObjectIDZND0="g_28e3a00@0" Pin0InfoVect0LinkObjId="g_28e3a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23712_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3612,-1031 3629,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e41b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-1031 3560,-1047 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3771@x" ObjectIDND1="3770@x" ObjectIDZND0="g_2b59b30@0" Pin0InfoVect0LinkObjId="g_2b59b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="SW-23711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-1031 3560,-1047 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e43a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3576,-1031 3560,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="3771@0" ObjectIDZND0="3770@x" ObjectIDZND1="g_2b59b30@0" Pin0InfoVect0LinkObjId="SW-23711_0" Pin0InfoVect1LinkObjId="g_2b59b30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3576,-1031 3560,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e4590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-1031 3560,-1012 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="3771@x" ObjectIDND1="g_2b59b30@0" ObjectIDZND0="3770@1" Pin0InfoVect0LinkObjId="SW-23711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23712_0" Pin1InfoVect1LinkObjId="g_2b59b30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-1031 3560,-1012 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e8160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-988 3733,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3785@x" ObjectIDND1="3781@x" ObjectIDZND0="3786@0" Pin0InfoVect0LinkObjId="SW-23886_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23885_0" Pin1InfoVect1LinkObjId="SW-23875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-988 3733,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e8350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-988 3786,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3786@1" ObjectIDZND0="g_28e8540@0" Pin0InfoVect0LinkObjId="g_28e8540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23886_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-988 3786,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28e8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-988 3721,-978 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3786@x" ObjectIDND1="3781@x" ObjectIDZND0="3785@1" Pin0InfoVect0LinkObjId="SW-23885_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23886_0" Pin1InfoVect1LinkObjId="SW-23875_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-988 3721,-978 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ea5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1002 3721,-988 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3781@0" ObjectIDZND0="3786@x" ObjectIDZND1="3785@x" Pin0InfoVect0LinkObjId="SW-23886_0" Pin0InfoVect1LinkObjId="SW-23885_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23875_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1002 3721,-988 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ec5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1041 3733,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3781@x" ObjectIDND1="3782@x" ObjectIDZND0="3784@0" Pin0InfoVect0LinkObjId="SW-23884_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23875_0" Pin1InfoVect1LinkObjId="SW-23882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1041 3733,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ec7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1041 3786,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3784@1" ObjectIDZND0="g_28ec9b0@0" Pin0InfoVect0LinkObjId="g_28ec9b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23884_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1041 3786,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ed250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1041 3721,-1029 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3784@x" ObjectIDND1="3782@x" ObjectIDZND0="3781@1" Pin0InfoVect0LinkObjId="SW-23875_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23884_0" Pin1InfoVect1LinkObjId="SW-23882_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1041 3721,-1029 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ef6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1055 3721,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3782@0" ObjectIDZND0="3784@x" ObjectIDZND1="3781@x" Pin0InfoVect0LinkObjId="SW-23884_0" Pin0InfoVect1LinkObjId="SW-23875_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23882_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1055 3721,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1101 3733,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3782@x" ObjectIDND1="g_2be1390@0" ObjectIDND2="g_2b17c80@0" ObjectIDZND0="3783@0" Pin0InfoVect0LinkObjId="SW-23883_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23882_0" Pin1InfoVect1LinkObjId="g_2be1390_0" Pin1InfoVect2LinkObjId="g_2b17c80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1101 3733,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f1fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3769,-1101 3786,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3783@1" ObjectIDZND0="g_28f21d0@0" Pin0InfoVect0LinkObjId="g_28f21d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23883_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3769,-1101 3786,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f2b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1101 3721,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3783@x" ObjectIDND1="g_2be1390@0" ObjectIDND2="g_2b17c80@0" ObjectIDZND0="3782@1" Pin0InfoVect0LinkObjId="SW-23882_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23883_0" Pin1InfoVect1LinkObjId="g_2be1390_0" Pin1InfoVect2LinkObjId="g_2b17c80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1101 3721,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f2d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1123 3735,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="3783@x" ObjectIDND1="3782@x" ObjectIDND2="g_2b17c80@0" ObjectIDZND0="g_2be1390@0" Pin0InfoVect0LinkObjId="g_2be1390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-23883_0" Pin1InfoVect1LinkObjId="SW-23882_0" Pin1InfoVect2LinkObjId="g_2b17c80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1123 3735,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f2f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1123 3721,-1101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2be1390@0" ObjectIDND1="g_2b17c80@0" ObjectIDND2="9182@1" ObjectIDZND0="3783@x" ObjectIDZND1="3782@x" Pin0InfoVect0LinkObjId="SW-23883_0" Pin0InfoVect1LinkObjId="SW-23882_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be1390_0" Pin1InfoVect1LinkObjId="g_2b17c80_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1123 3721,-1101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f3160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1143 3721,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="9182@1" ObjectIDZND0="g_2be1390@0" ObjectIDZND1="3783@x" ObjectIDZND2="3782@x" Pin0InfoVect0LinkObjId="g_2be1390_0" Pin0InfoVect1LinkObjId="SW-23883_0" Pin0InfoVect2LinkObjId="SW-23882_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-1143 3721,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-847 4242,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3875@x" ObjectIDND1="3803@x" ObjectIDZND0="3804@0" Pin0InfoVect0LinkObjId="SW-24079_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bb1660_0" Pin1InfoVect1LinkObjId="SW-24078_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-847 4242,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f8b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4278,-847 4295,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3804@1" ObjectIDZND0="g_28f8da0@0" Pin0InfoVect0LinkObjId="g_28f8da0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24079_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4278,-847 4295,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28f96d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4125,-1138 4107,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2be1e10@0" ObjectIDZND0="g_2b18fd0@0" ObjectIDZND1="3799@x" ObjectIDZND2="3798@x" Pin0InfoVect0LinkObjId="g_2b18fd0_0" Pin0InfoVect1LinkObjId="SW-24074_0" Pin0InfoVect2LinkObjId="SW-24073_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be1e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4125,-1138 4107,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fa2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1138 4107,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2be1e10@0" ObjectIDND1="g_2b18fd0@0" ObjectIDND2="3799@x" ObjectIDZND0="11433@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be1e10_0" Pin1InfoVect1LinkObjId="g_2b18fd0_0" Pin1InfoVect2LinkObjId="SW-24074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1138 4107,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fa510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4108,-1138 4091,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2be1e10@0" ObjectIDND1="3799@x" ObjectIDND2="3798@x" ObjectIDZND0="g_2b18fd0@0" Pin0InfoVect0LinkObjId="g_2b18fd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be1e10_0" Pin1InfoVect1LinkObjId="SW-24074_0" Pin1InfoVect2LinkObjId="SW-24073_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4108,-1138 4091,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fcbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1111 4119,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="3798@x" ObjectIDND1="g_2be1e10@0" ObjectIDND2="g_2b18fd0@0" ObjectIDZND0="3799@0" Pin0InfoVect0LinkObjId="SW-24074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24073_0" Pin1InfoVect1LinkObjId="g_2be1e10_0" Pin1InfoVect2LinkObjId="g_2b18fd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1111 4119,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fcdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1111 4172,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3799@1" ObjectIDZND0="g_28fcff0@0" Pin0InfoVect0LinkObjId="g_28fcff0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-1111 4172,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28ffda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1051 4119,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3797@x" ObjectIDND1="3798@x" ObjectIDZND0="3800@0" Pin0InfoVect0LinkObjId="SW-24075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24066_0" Pin1InfoVect1LinkObjId="SW-24073_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1051 4119,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_28fffc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-1051 4172,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3800@1" ObjectIDZND0="g_2900220@0" Pin0InfoVect0LinkObjId="g_2900220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24075_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-1051 4172,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29031d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-998 4119,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3801@x" ObjectIDND1="3797@x" ObjectIDZND0="3802@0" Pin0InfoVect0LinkObjId="SW-24077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24076_0" Pin1InfoVect1LinkObjId="SW-24066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-998 4119,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2903450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4155,-998 4172,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3802@1" ObjectIDZND0="g_2903680@0" Pin0InfoVect0LinkObjId="g_2903680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4155,-998 4172,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2904830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1098 4107,-1111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="3798@1" ObjectIDZND0="3799@x" ObjectIDZND1="g_2be1e10@0" ObjectIDZND2="g_2b18fd0@0" Pin0InfoVect0LinkObjId="SW-24074_0" Pin0InfoVect1LinkObjId="g_2be1e10_0" Pin0InfoVect2LinkObjId="g_2b18fd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1098 4107,-1111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2904a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1111 4107,-1138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="3799@x" ObjectIDND1="3798@x" ObjectIDZND0="g_2be1e10@0" ObjectIDZND1="g_2b18fd0@0" ObjectIDZND2="11433@1" Pin0InfoVect0LinkObjId="g_2be1e10_0" Pin0InfoVect1LinkObjId="g_2b18fd0_0" Pin0InfoVect2LinkObjId="g_28fa2f0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24074_0" Pin1InfoVect1LinkObjId="SW-24073_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1111 4107,-1138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29054a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1036 4107,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3797@1" ObjectIDZND0="3800@x" ObjectIDZND1="3798@x" Pin0InfoVect0LinkObjId="SW-24075_0" Pin0InfoVect1LinkObjId="SW-24073_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1036 4107,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2905700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1051 4107,-1062 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="3800@x" ObjectIDND1="3797@x" ObjectIDZND0="3798@0" Pin0InfoVect0LinkObjId="SW-24073_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24075_0" Pin1InfoVect1LinkObjId="SW-24066_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-1051 4107,-1062 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2906110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-985 4107,-998 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3801@1" ObjectIDZND0="3802@x" ObjectIDZND1="3797@x" Pin0InfoVect0LinkObjId="SW-24077_0" Pin0InfoVect1LinkObjId="SW-24066_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24076_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-985 4107,-998 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2906370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-998 4107,-1009 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3802@x" ObjectIDND1="3801@x" ObjectIDZND0="3797@0" Pin0InfoVect0LinkObjId="SW-24066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24077_0" Pin1InfoVect1LinkObjId="SW-24076_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-998 4107,-1009 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2908b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-960 4022,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3775@1" ObjectIDZND0="g_2908de0@0" Pin0InfoVect0LinkObjId="g_2908de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-960 4022,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_290e4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-1038 4020,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3774@1" ObjectIDZND0="g_290e710@0" Pin0InfoVect0LinkObjId="g_290e710_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23717_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-1038 4020,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_290f100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-1038 3951,-1052 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="3774@x" ObjectIDND1="3773@x" ObjectIDZND0="g_2b5c8a0@0" Pin0InfoVect0LinkObjId="g_2b5c8a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23717_0" Pin1InfoVect1LinkObjId="SW-23716_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-1038 3951,-1052 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_290f360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-1038 3951,-1038 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3774@0" ObjectIDZND0="g_2b5c8a0@0" ObjectIDZND1="3773@x" Pin0InfoVect0LinkObjId="g_2b5c8a0_0" Pin0InfoVect1LinkObjId="SW-23716_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23717_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-1038 3951,-1038 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_290f5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-1038 3951,-1019 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_2b5c8a0@0" ObjectIDND1="3774@x" ObjectIDZND0="3773@1" Pin0InfoVect0LinkObjId="SW-23716_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b5c8a0_0" Pin1InfoVect1LinkObjId="SW-23717_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-1038 3951,-1019 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2916400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3805,-851 3852,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="3814@x" ObjectIDND1="3815@x" ObjectIDZND0="3813@1" Pin0InfoVect0LinkObjId="SW-24174_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24180_0" Pin1InfoVect1LinkObjId="SW-24181_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3805,-851 3852,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291b1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-800 3806,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3815@0" ObjectIDZND0="g_291b6a0@0" Pin0InfoVect0LinkObjId="g_291b6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24181_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-800 3806,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291b440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-802 3927,-788 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3817@0" ObjectIDZND0="g_291c0d0@0" Pin0InfoVect0LinkObjId="g_291c0d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24183_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-802 3927,-788 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291cac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-779 3551,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2921d80@0" ObjectIDND1="3789@x" ObjectIDND2="3874@x" ObjectIDZND0="g_29210d0@0" Pin0InfoVect0LinkObjId="g_29210d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2921d80_0" Pin1InfoVect1LinkObjId="SW-23897_0" Pin1InfoVect2LinkObjId="g_2bd2950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-779 3551,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291cd20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-779 3551,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3874@x" ObjectIDZND0="g_29210d0@0" ObjectIDZND1="g_2921d80@0" ObjectIDZND2="3789@x" Pin0InfoVect0LinkObjId="g_29210d0_0" Pin0InfoVect1LinkObjId="g_2921d80_0" Pin0InfoVect2LinkObjId="SW-23897_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd2950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-779 3551,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291d760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3551,-779 3518,-779 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_29210d0@0" ObjectIDND1="3874@x" ObjectIDZND0="g_2921d80@0" ObjectIDZND1="3789@x" Pin0InfoVect0LinkObjId="g_2921d80_0" Pin0InfoVect1LinkObjId="SW-23897_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29210d0_0" Pin1InfoVect1LinkObjId="g_2bd2950_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3551,-779 3518,-779 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_291d9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-779 3491,-779 3491,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_29210d0@0" ObjectIDND1="3874@x" ObjectIDND2="3789@x" ObjectIDZND0="g_2921d80@0" Pin0InfoVect0LinkObjId="g_2921d80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29210d0_0" Pin1InfoVect1LinkObjId="g_2bd2950_0" Pin1InfoVect2LinkObjId="SW-23897_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-779 3491,-779 3491,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_29201e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-779 3518,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_2921d80@0" ObjectIDND1="g_29210d0@0" ObjectIDND2="3874@x" ObjectIDZND0="3789@0" Pin0InfoVect0LinkObjId="SW-23897_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2921d80_0" Pin1InfoVect1LinkObjId="g_29210d0_0" Pin1InfoVect2LinkObjId="g_2bd2950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-779 3518,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2920440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3518,-830 3518,-845 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3789@1" ObjectIDZND0="g_29206a0@0" Pin0InfoVect0LinkObjId="g_29206a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23897_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3518,-830 3518,-845 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2924ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-539 3548,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3822@1" Pin0InfoVect0LinkObjId="SW-24977_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-539 3548,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2926dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-486 3548,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3822@0" ObjectIDZND0="3872@1" Pin0InfoVect0LinkObjId="SW-24975_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24977_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-486 3548,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292bbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-539 3613,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3861@1" Pin0InfoVect0LinkObjId="SW-24889_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-539 3613,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_292dab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-485 3613,-465 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3861@0" ObjectIDZND0="3860@1" Pin0InfoVect0LinkObjId="SW-24887_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24889_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-485 3613,-465 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29302b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-449 3663,-449 3663,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_29311c0@0" ObjectIDND1="3778@x" ObjectIDZND0="g_2930510@0" Pin0InfoVect0LinkObjId="g_2930510_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29311c0_0" Pin1InfoVect1LinkObjId="SW-23734_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-449 3663,-449 3663,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2931980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-449 3696,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2930510@0" ObjectIDND1="3778@x" ObjectIDZND0="g_29311c0@0" Pin0InfoVect0LinkObjId="g_29311c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2930510_0" Pin1InfoVect1LinkObjId="SW-23734_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-449 3696,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2931be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-392 3696,-337 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_29311c0@1" ObjectIDZND0="g_2b60870@0" Pin0InfoVect0LinkObjId="g_2b60870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29311c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-392 3696,-337 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-539 3696,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3778@1" Pin0InfoVect0LinkObjId="SW-23734_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-539 3696,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2932880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3696,-484 3696,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="3778@0" ObjectIDZND0="g_2930510@0" ObjectIDZND1="g_29311c0@0" Pin0InfoVect0LinkObjId="g_2930510_0" Pin0InfoVect1LinkObjId="g_29311c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23734_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3696,-484 3696,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29392c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-552 3599,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3795@0" ObjectIDZND0="3767@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23916_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-552 3599,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2939520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-588 3599,-605 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3795@1" ObjectIDZND0="3794@0" Pin0InfoVect0LinkObjId="SW-23914_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23916_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-588 3599,-605 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2939780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-632 3599,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3794@1" ObjectIDZND0="3796@0" Pin0InfoVect0LinkObjId="SW-23917_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23914_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-632 3599,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29426a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-539 3780,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="10453@1" Pin0InfoVect0LinkObjId="SW-24285_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-539 3780,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29445a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-487 3780,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10453@0" ObjectIDZND0="3821@1" Pin0InfoVect0LinkObjId="SW-24283_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24285_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-487 3780,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2947a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-539 3952,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3832@1" Pin0InfoVect0LinkObjId="SW-24384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-539 3952,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2949950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-487 3952,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3832@0" ObjectIDZND0="3831@1" Pin0InfoVect0LinkObjId="SW-24382_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24384_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-487 3952,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2950dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-316 3969,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3833@x" ObjectIDND1="g_2bd5620@0" ObjectIDND2="11723@x" ObjectIDZND0="3834@0" Pin0InfoVect0LinkObjId="SW-24386_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="g_2bd5620_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-316 3969,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2951000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4005,-316 4022,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3834@1" ObjectIDZND0="g_2951230@0" Pin0InfoVect0LinkObjId="g_2951230_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24386_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4005,-316 4022,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2951c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-329 3952,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3833@0" ObjectIDZND0="3834@x" ObjectIDZND1="g_2bd5620@0" ObjectIDZND2="11723@x" Pin0InfoVect0LinkObjId="SW-24386_0" Pin0InfoVect1LinkObjId="g_2bd5620_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-329 3952,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2954c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-539 4084,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3780@1" Pin0InfoVect0LinkObjId="SW-23744_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-539 4084,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29556d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-485 4084,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3780@0" ObjectIDZND0="g_2954ef0@1" Pin0InfoVect0LinkObjId="g_2954ef0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23744_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-485 4084,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2955930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-437 4084,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2954ef0@0" ObjectIDZND0="12145@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2954ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-437 4084,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2958160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3970,-539 3970,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3820@0" Pin0InfoVect0LinkObjId="SW-24247_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3970,-539 3970,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295a9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-562 4069,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3819@0" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_2963790_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24246_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-562 4069,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295c5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4069,-598 4069,-612 4030,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3819@1" ObjectIDZND0="3818@0" Pin0InfoVect0LinkObjId="SW-24245_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24246_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4069,-598 4069,-612 4030,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_295c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4003,-612 3970,-612 3970,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3818@1" ObjectIDZND0="3820@1" Pin0InfoVect0LinkObjId="SW-24247_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24245_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4003,-612 3970,-612 3970,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2963530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-605 4224,-588 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3810@0" ObjectIDZND0="3811@1" Pin0InfoVect0LinkObjId="SW-24106_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24104_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-605 4224,-588 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2963790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-552 4224,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3811@0" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_295a9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24106_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-552 4224,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29639f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-645 4224,-632 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3812@0" ObjectIDZND0="3810@1" Pin0InfoVect0LinkObjId="SW-24104_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-645 4224,-632 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2963c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4385,-448 4385,-432 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2964ba0@0" ObjectIDND1="3779@x" ObjectIDZND0="g_2963eb0@0" Pin0InfoVect0LinkObjId="g_2963eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2964ba0_0" Pin1InfoVect1LinkObjId="SW-23737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4385,-448 4385,-432 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2965380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4418,-422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2963eb0@0" ObjectIDND1="3779@x" ObjectIDZND0="g_2964ba0@0" Pin0InfoVect0LinkObjId="g_2964ba0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2963eb0_0" Pin1InfoVect1LinkObjId="SW-23737_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4418,-422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29655e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-391 4418,-361 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2964ba0@1" ObjectIDZND0="g_2b632e0@0" Pin0InfoVect0LinkObjId="g_2b632e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2964ba0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-391 4418,-361 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2968730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-539 4492,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3825@1" Pin0InfoVect0LinkObjId="SW-24322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-539 4492,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296a7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-488 4492,-468 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3825@0" ObjectIDZND0="3824@1" Pin0InfoVect0LinkObjId="SW-24320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-488 4492,-468 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-539 4565,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3864@1" Pin0InfoVect0LinkObjId="SW-24908_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-539 4565,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2971850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-489 4565,-469 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3864@0" ObjectIDZND0="3863@1" Pin0InfoVect0LinkObjId="SW-24906_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24908_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-489 4565,-469 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2974170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-539 4277,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3840@1" Pin0InfoVect0LinkObjId="SW-24443_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-539 4277,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2976210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-487 4277,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3840@0" ObjectIDZND0="3839@1" Pin0InfoVect0LinkObjId="SW-24441_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24443_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-487 4277,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a41db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-316 4294,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3841@x" ObjectIDND1="g_2bd7100@0" ObjectIDND2="11725@x" ObjectIDZND0="3842@0" Pin0InfoVect0LinkObjId="SW-24445_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="g_2bd7100_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-316 4294,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a41fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4331,-316 4348,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3842@1" ObjectIDZND0="g_2a42190@0" Pin0InfoVect0LinkObjId="g_2a42190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24445_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4331,-316 4348,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a427c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-329 4277,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3841@0" ObjectIDZND0="3842@x" ObjectIDZND1="g_2bd7100@0" ObjectIDZND2="11725@x" Pin0InfoVect0LinkObjId="SW-24445_0" Pin0InfoVect1LinkObjId="g_2bd7100_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-329 4277,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a430a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-539 4161,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="3836@1" Pin0InfoVect0LinkObjId="SW-24414_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-539 4161,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a44b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-487 4161,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3836@0" ObjectIDZND0="3835@1" Pin0InfoVect0LinkObjId="SW-24412_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24414_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-487 4161,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-316 4178,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3837@x" ObjectIDND1="g_2bd6390@0" ObjectIDND2="11724@x" ObjectIDZND0="3838@0" Pin0InfoVect0LinkObjId="SW-24416_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="g_2bd6390_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-316 4178,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4cc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4215,-316 4232,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3838@1" ObjectIDZND0="g_2a4cec0@0" Pin0InfoVect0LinkObjId="g_2a4cec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24416_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4215,-316 4232,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-329 4161,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3837@0" ObjectIDZND0="3838@x" ObjectIDZND1="g_2bd6390@0" ObjectIDZND2="11724@x" Pin0InfoVect0LinkObjId="SW-24416_0" Pin0InfoVect1LinkObjId="g_2bd6390_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-329 4161,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4e440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-539 3848,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3767@0" ObjectIDZND0="3828@1" Pin0InfoVect0LinkObjId="SW-24354_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29392c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-539 3848,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a508d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-487 3848,-467 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3828@0" ObjectIDZND0="3827@1" Pin0InfoVect0LinkObjId="SW-24352_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24354_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-487 3848,-467 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a58740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-316 3865,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="capacitor" EndDevType0="switch" ObjectIDND0="3829@x" ObjectIDND1="g_2bd48b0@0" ObjectIDND2="11722@x" ObjectIDZND0="3830@0" Pin0InfoVect0LinkObjId="SW-24356_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="g_2bd48b0_0" Pin1InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-316 3865,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a589a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3901,-316 3918,-316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3830@1" ObjectIDZND0="g_2a58c00@0" Pin0InfoVect0LinkObjId="g_2a58c00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24356_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3901,-316 3918,-316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a59690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-329 3848,-313 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="3829@0" ObjectIDZND0="3830@x" ObjectIDZND1="g_2bd48b0@0" ObjectIDZND2="11722@x" Pin0InfoVect0LinkObjId="SW-24356_0" Pin0InfoVect1LinkObjId="g_2bd48b0_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-329 3848,-313 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a622b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-200 4869,-214 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-200 4869,-214 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a62510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-122 4869,-142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-122 4869,-142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a62770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-160 4869,-173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-160 4869,-173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a671d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3747,-88 3772,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3747,-88 3772,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a67430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3676,-60 3691,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2b229a0@0" ObjectIDND2="10452@x" ObjectIDZND0="g_2be8990@0" Pin0InfoVect0LinkObjId="g_2be8990_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b229a0_0" Pin1InfoVect2LinkObjId="SW-24978_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3676,-60 3691,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a67f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3711,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2be8990@0" ObjectIDND1="g_2b229a0@0" ObjectIDND2="10452@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be8990_0" Pin1InfoVect1LinkObjId="g_2b229a0_0" Pin1InfoVect2LinkObjId="SW-24978_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3711,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a68180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-88 3943,-61 3958,-61 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_2be9700@0" Pin0InfoVect0LinkObjId="g_2be9700_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-88 3943,-61 3958,-61 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a6d470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3799,-88 3943,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_2be9700@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2be9700_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3799,-88 3943,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a6d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4450,-88 4450,-57 4435,-57 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b66550@0" ObjectIDND1="g_2bcea50@0" ObjectIDND2="10455@x" ObjectIDZND0="g_2a6d930@0" Pin0InfoVect0LinkObjId="g_2a6d930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b66550_0" Pin1InfoVect1LinkObjId="g_2bcea50_0" Pin1InfoVect2LinkObjId="SW-24997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4450,-88 4450,-57 4435,-57 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7b780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-173 4797,-160 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-173 4797,-160 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7b9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-142 4797,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-142 4797,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7bc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-214 4797,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-214 4797,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a7e6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4780,-290 4780,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2a7e950@0" Pin0InfoVect0LinkObjId="g_2a7e950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4780,-290 4780,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7fc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4534,-307 4534,-320 4565,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2bcdf90@0" ObjectIDZND0="g_2a81e50@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2a81e50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcdf90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4534,-307 4534,-320 4565,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a7fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-320 4565,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2a81e50@0" ObjectIDND1="0@x" ObjectIDND2="0@x" ObjectIDZND0="3865@0" Pin0InfoVect0LinkObjId="SW-24909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a81e50_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-320 4565,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a809c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-231 4797,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2a81e50@0" ObjectIDZND1="3865@x" ObjectIDZND2="g_2bcdf90@0" Pin0InfoVect0LinkObjId="g_2a81e50_0" Pin0InfoVect1LinkObjId="SW-24909_0" Pin0InfoVect2LinkObjId="g_2bcdf90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-231 4797,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a80c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4797,-245 4780,-245 4780,-254 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2a81e50@0" ObjectIDND2="3865@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a81e50_0" Pin1InfoVect2LinkObjId="SW-24909_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4797,-245 4780,-245 4780,-254 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a80e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-260 4810,-260 4810,-269 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="3865@x" ObjectIDND1="g_2bcdf90@0" ObjectIDND2="0@x" ObjectIDZND0="g_2a81e50@0" Pin0InfoVect0LinkObjId="g_2a81e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-24909_0" Pin1InfoVect1LinkObjId="g_2bcdf90_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-260 4810,-260 4810,-269 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a81970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-320 4565,-198 4719,-198 4719,-340 4798,-340 4798,-260 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="3865@x" ObjectIDND1="g_2bcdf90@0" ObjectIDZND0="g_2a81e50@0" ObjectIDZND1="0@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2a81e50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24909_0" Pin1InfoVect1LinkObjId="g_2bcdf90_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-320 4565,-198 4719,-198 4719,-340 4798,-340 4798,-260 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a81bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4798,-260 4798,-245 4797,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2a81e50@0" ObjectIDND1="3865@x" ObjectIDND2="g_2bcdf90@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a81e50_0" Pin1InfoVect1LinkObjId="SW-24909_0" Pin1InfoVect2LinkObjId="g_2bcdf90_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4798,-260 4798,-245 4797,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a8ac10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-122 5016,-143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-122 5016,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a8ae70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-161 5016,-174 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-161 5016,-174 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a8b0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-201 5016,-215 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-201 5016,-215 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a8e8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-297 5000,-307 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="0@1" ObjectIDZND0="g_2a8eb50@0" Pin0InfoVect0LinkObjId="g_2a8eb50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-297 5000,-307 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a8f5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-778 4319,-791 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="transformer" EndDevType0="lightningRod" ObjectIDND0="g_2a93b50@0" ObjectIDND1="3805@x" ObjectIDND2="3875@x" ObjectIDZND0="g_2a92de0@0" Pin0InfoVect0LinkObjId="g_2a92de0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a93b50_0" Pin1InfoVect1LinkObjId="SW-24088_0" Pin1InfoVect2LinkObjId="g_2bb1660_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-778 4319,-791 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a920f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-829 4352,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="3805@1" ObjectIDZND0="g_2a92350@0" Pin0InfoVect0LinkObjId="g_2a92350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-829 4352,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a94fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-798 4225,-847 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3875@1" ObjectIDZND0="3803@x" ObjectIDZND1="3804@x" Pin0InfoVect0LinkObjId="SW-24078_0" Pin0InfoVect1LinkObjId="SW-24079_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb1660_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-798 4225,-847 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a95210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-847 4225,-862 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3875@x" ObjectIDND1="3804@x" ObjectIDZND0="3803@0" Pin0InfoVect0LinkObjId="SW-24078_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bb1660_0" Pin1InfoVect1LinkObjId="SW-24079_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-847 4225,-862 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a95d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-793 4352,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="3805@0" ObjectIDZND0="g_2a93b50@0" ObjectIDZND1="g_2a92de0@0" ObjectIDZND2="3875@x" Pin0InfoVect0LinkObjId="g_2a93b50_0" Pin0InfoVect1LinkObjId="g_2a92de0_0" Pin0InfoVect2LinkObjId="g_2bb1660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24088_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-793 4352,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a95f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4352,-778 4379,-778 4379,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2a92de0@0" ObjectIDND1="3875@x" ObjectIDND2="3805@x" ObjectIDZND0="g_2a93b50@0" Pin0InfoVect0LinkObjId="g_2a93b50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a92de0_0" Pin1InfoVect1LinkObjId="g_2bb1660_0" Pin1InfoVect2LinkObjId="SW-24088_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4352,-778 4379,-778 4379,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a961c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-778 4319,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="3875@x" ObjectIDZND0="g_2a92de0@0" ObjectIDZND1="g_2a93b50@0" ObjectIDZND2="3805@x" Pin0InfoVect0LinkObjId="g_2a92de0_0" Pin0InfoVect1LinkObjId="g_2a93b50_0" Pin0InfoVect2LinkObjId="SW-24088_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb1660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-778 4319,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2a96420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4319,-778 4352,-778 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2a92de0@0" ObjectIDND1="3875@x" ObjectIDZND0="g_2a93b50@0" ObjectIDZND1="3805@x" Pin0InfoVect0LinkObjId="g_2a93b50_0" Pin0InfoVect1LinkObjId="SW-24088_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a92de0_0" Pin1InfoVect1LinkObjId="g_2bb1660_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4319,-778 4352,-778 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9eba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-363 3613,-373 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3862@1" ObjectIDZND0="g_2a973d0@1" Pin0InfoVect0LinkObjId="g_2a973d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24890_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-363 3613,-373 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-426 3613,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a973d0@0" ObjectIDZND0="3860@0" Pin0InfoVect0LinkObjId="SW-24887_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a973d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-426 3613,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-429 3548,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a96680@0" ObjectIDZND0="3872@0" Pin0InfoVect0LinkObjId="SW-24975_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a96680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-429 3548,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9f2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-365 3952,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3833@1" ObjectIDZND0="g_2a99bc0@1" Pin0InfoVect0LinkObjId="g_2a99bc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-365 3952,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9f520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-429 3952,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a99bc0@0" ObjectIDZND0="3831@0" Pin0InfoVect0LinkObjId="SW-24382_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a99bc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-429 3952,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9f780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-365 3848,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3829@1" ObjectIDZND0="g_2a98e70@1" Pin0InfoVect0LinkObjId="g_2a98e70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24355_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-365 3848,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9f9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-429 3848,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a98e70@0" ObjectIDZND0="3827@0" Pin0InfoVect0LinkObjId="SW-24352_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a98e70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-429 3848,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9fc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-365 3780,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3823@1" ObjectIDZND0="g_2a98120@1" Pin0InfoVect0LinkObjId="g_2a98120_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24286_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-365 3780,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a9fea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-430 3780,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a98120@0" ObjectIDZND0="3821@0" Pin0InfoVect0LinkObjId="SW-24283_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a98120_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-430 3780,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-365 4161,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3837@1" ObjectIDZND0="g_2a9a910@1" Pin0InfoVect0LinkObjId="g_2a9a910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24415_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-365 4161,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-428 4161,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a9a910@0" ObjectIDZND0="3835@0" Pin0InfoVect0LinkObjId="SW-24412_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9a910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-428 4161,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa05c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-365 4277,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3841@1" ObjectIDZND0="g_2a9b660@1" Pin0InfoVect0LinkObjId="g_2a9b660_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24444_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-365 4277,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-429 4277,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a9b660@0" ObjectIDZND0="3839@0" Pin0InfoVect0LinkObjId="SW-24441_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9b660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-429 4277,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-366 4492,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3826@1" ObjectIDZND0="g_2a9de50@1" Pin0InfoVect0LinkObjId="g_2a9de50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-366 4492,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-429 4492,-441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a9de50@0" ObjectIDZND0="3824@0" Pin0InfoVect0LinkObjId="SW-24320_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9de50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-429 4492,-441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa0f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-367 4565,-378 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3865@1" ObjectIDZND0="g_2a9d100@1" Pin0InfoVect0LinkObjId="g_2a9d100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-367 4565,-378 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa11a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4565,-431 4565,-442 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2a9d100@0" ObjectIDZND0="3863@0" Pin0InfoVect0LinkObjId="SW-24906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9d100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4565,-431 4565,-442 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa8240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1143 4787,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac9970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-1143 4787,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2aaa250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4823,-1143 4841,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4823,-1143 4841,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ab1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4823,-1099 4841,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4823,-1099 4841,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab1960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-1099 4787,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac9970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-1099 4787,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ab6480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4884,-1051 4867,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3871@0" ObjectIDZND0="3869@0" Pin0InfoVect0LinkObjId="SW-24954_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24957_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4884,-1051 4867,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abafa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-919 4866,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3848@0" ObjectIDZND0="3846@0" Pin0InfoVect0LinkObjId="SW-24486_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24489_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-919 4866,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac0ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-866 5006,-848 5017,-848 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="g_2be38f0@0" ObjectIDND1="g_2bb7430@0" ObjectIDND2="12148@x" ObjectIDZND0="g_2abfac0@0" Pin0InfoVect0LinkObjId="g_2abfac0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be38f0_0" Pin1InfoVect1LinkObjId="g_2bb7430_0" Pin1InfoVect2LinkObjId="EC-CX_HS.CX_HS_375Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-866 5006,-848 5017,-848 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac0e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-866 5006,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="12148@0" ObjectIDZND0="g_2abfac0@0" ObjectIDZND1="g_2be38f0@0" ObjectIDZND2="g_2bb7430@0" Pin0InfoVect0LinkObjId="g_2abfac0_0" Pin0InfoVect1LinkObjId="g_2be38f0_0" Pin0InfoVect2LinkObjId="g_2bb7430_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_375Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-866 5006,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac5920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-787 4866,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3854@0" ObjectIDZND0="3852@0" Pin0InfoVect0LinkObjId="SW-24613_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-787 4866,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac6c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-787 5006,-770 5017,-770 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="12149@x" ObjectIDND1="g_2be4660@0" ObjectIDND2="g_2bbdea0@0" ObjectIDZND0="g_2ac5b80@0" Pin0InfoVect0LinkObjId="g_2ac5b80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_376Ld_0" Pin1InfoVect1LinkObjId="g_2be4660_0" Pin1InfoVect2LinkObjId="g_2bbdea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-787 5006,-770 5017,-770 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac9970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4722,-951 4743,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3791@1" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2b91930_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23902_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4722,-951 4743,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ace6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4878,-697 4864,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3857@0" ObjectIDZND0="3855@0" Pin0InfoVect0LinkObjId="SW-24647_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24650_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4878,-697 4864,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2acfa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-697 5004,-680 5015,-680 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="12150@x" ObjectIDND1="g_2be53d0@0" ObjectIDND2="g_2bbcfc0@0" ObjectIDZND0="g_2ace950@0" Pin0InfoVect0LinkObjId="g_2ace950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_381Ld_0" Pin1InfoVect1LinkObjId="g_2be53d0_0" Pin1InfoVect2LinkObjId="g_2bbcfc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-697 5004,-680 5015,-680 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad0510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-645 4863,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2be6eb0@0" ObjectIDND1="3777@x" ObjectIDZND0="g_2acfc90@0" Pin0InfoVect0LinkObjId="g_2acfc90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be6eb0_0" Pin1InfoVect1LinkObjId="SW-23724_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-645 4863,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad0770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4894,-645 4949,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2acfc90@1" ObjectIDZND0="g_2b6fc40@0" Pin0InfoVect0LinkObjId="g_2b6fc40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2acfc90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4894,-645 4949,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad5290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-577 4866,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3868@0" ObjectIDZND0="3866@0" Pin0InfoVect0LinkObjId="SW-24929_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24932_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-577 4866,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ad54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-577 5014,-559 5026,-559 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="g_2bb7c00@0" ObjectIDND1="12151@x" ObjectIDZND0="g_2be6140@0" Pin0InfoVect0LinkObjId="g_2be6140_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bb7c00_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-577 5014,-559 5026,-559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ad9f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-526 4866,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-526 4866,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ade9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-482 4866,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-482 4866,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ae3410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4882,-439 4868,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4882,-439 4868,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae6120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4743,-798 4723,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3765@0" ObjectIDZND0="3845@1" Pin0InfoVect0LinkObjId="SW-24471_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac9970_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4743,-798 4723,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae8e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4723,-692 4743,-692 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3844@1" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2aebb40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4723,-692 4743,-692 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aebb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4716,-623 4743,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3807@1" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24093_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4716,-623 4743,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afb930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-951 4686,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3790@0" ObjectIDZND0="3791@0" Pin0InfoVect0LinkObjId="SW-23902_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4672,-951 4686,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b033f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4666,-623 4680,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3806@0" ObjectIDZND0="3807@0" Pin0InfoVect0LinkObjId="SW-24093_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4666,-623 4680,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b03650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-623 4639,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3808@1" ObjectIDZND0="3806@1" Pin0InfoVect0LinkObjId="SW-24091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24094_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-623 4639,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0a940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-623 4497,-637 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer" EndDevType0="lightningRod" ObjectIDND0="3808@x" ObjectIDND1="3875@x" ObjectIDZND0="g_29399e0@0" Pin0InfoVect0LinkObjId="g_29399e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24094_0" Pin1InfoVect1LinkObjId="g_2bb1660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-623 4497,-637 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-623 4585,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_29399e0@0" ObjectIDND1="3875@x" ObjectIDZND0="3808@0" Pin0InfoVect0LinkObjId="SW-24094_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29399e0_0" Pin1InfoVect1LinkObjId="g_2bb1660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-623 4585,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4280,-758 4407,-758 4407,-623 4499,-623 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3875@0" ObjectIDZND0="g_29399e0@0" ObjectIDZND1="3808@x" Pin0InfoVect0LinkObjId="g_29399e0_0" Pin0InfoVect1LinkObjId="SW-24094_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb1660_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4280,-758 4407,-758 4407,-623 4499,-623 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0b900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-951 4626,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3790@1" ObjectIDZND0="3792@1" Pin0InfoVect0LinkObjId="SW-23903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-951 4626,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0d080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-1143 4888,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-1143 4888,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b0d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4868,-1099 4889,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4868,-1099 4889,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b14ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-692 4668,-692 4668,-731 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3844@0" ObjectIDZND0="3843@0" Pin0InfoVect0LinkObjId="SW-24469_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-692 4668,-692 4668,-731 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b150d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4668,-758 4668,-798 4687,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3843@1" ObjectIDZND0="3845@0" Pin0InfoVect0LinkObjId="SW-24471_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4668,-758 4668,-798 4687,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2b17a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3701,-1123 3721,-1123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b17c80@0" ObjectIDZND0="g_2be1390@0" ObjectIDZND1="3783@x" ObjectIDZND2="3782@x" Pin0InfoVect0LinkObjId="g_2be1390_0" Pin0InfoVect1LinkObjId="SW-23883_0" Pin0InfoVect2LinkObjId="SW-23882_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b17c80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3701,-1123 3721,-1123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2c950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4271,-88 4322,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4271,-88 4322,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b2f610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4358,-88 4450,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2a6d930@0" ObjectIDZND1="g_2b66550@0" ObjectIDZND2="g_2bcea50@0" Pin0InfoVect0LinkObjId="g_2a6d930_0" Pin0InfoVect1LinkObjId="g_2b66550_0" Pin0InfoVect2LinkObjId="g_2bcea50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4358,-88 4450,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2f800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3943,-88 4019,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_2be9700@0" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be9700_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3943,-88 4019,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2f9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4244,-88 4132,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4244,-88 4132,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b2fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4105,-88 4055,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4105,-88 4055,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3d070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-970 4575,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_2afbb90@0" ObjectIDZND0="3792@x" ObjectIDZND1="3874@x" Pin0InfoVect0LinkObjId="SW-23903_0" Pin0InfoVect1LinkObjId="g_2bd2950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afbb90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-970 4575,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b3d260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4575,-951 4590,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2afbb90@0" ObjectIDND1="3874@x" ObjectIDZND0="3792@0" Pin0InfoVect0LinkObjId="SW-23903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2afbb90_0" Pin1InfoVect1LinkObjId="g_2bd2950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4575,-951 4590,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3d450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-377 4621,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2a9c3b0@1" ObjectIDZND0="10455@1" Pin0InfoVect0LinkObjId="SW-24997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a9c3b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-377 4621,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3676,-88 3548,-88 3548,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2be8990@0" ObjectIDND1="0@x" ObjectIDZND0="g_2b229a0@0" ObjectIDZND1="10452@x" Pin0InfoVect0LinkObjId="g_2b229a0_0" Pin0InfoVect1LinkObjId="SW-24978_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be8990_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3676,-88 3548,-88 3548,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3dd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-364 3548,-376 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="10452@1" ObjectIDZND0="g_2a96680@1" Pin0InfoVect0LinkObjId="g_2a96680_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24978_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-364 3548,-376 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b442f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4869,-231 4869,-250 4933,-250 4933,-229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4869,-231 4869,-250 4933,-250 4933,-229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b44560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-212 4933,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-212 4933,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b447c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-141 4933,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-141 4933,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b682a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4587,-299 4587,-319 4621,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2bcea50@0" ObjectIDZND0="g_2b66550@0" ObjectIDZND1="g_2a6d930@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b66550_0" Pin0InfoVect1LinkObjId="g_2a6d930_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bcea50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4587,-299 4587,-319 4621,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b68500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-319 4621,-331 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b66550@0" ObjectIDND1="g_2a6d930@0" ObjectIDND2="0@x" ObjectIDZND0="10455@0" Pin0InfoVect0LinkObjId="SW-24997_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b66550_0" Pin1InfoVect1LinkObjId="g_2a6d930_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-319 4621,-331 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b68760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-74 4491,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b66550@0" ObjectIDZND0="g_2bcea50@0" ObjectIDZND1="10455@x" ObjectIDZND2="g_2a6d930@0" Pin0InfoVect0LinkObjId="g_2bcea50_0" Pin0InfoVect1LinkObjId="SW-24997_0" Pin0InfoVect2LinkObjId="g_2a6d930_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b66550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-74 4491,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b69250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-319 4621,-88 4491,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2bcea50@0" ObjectIDND1="10455@x" ObjectIDZND0="g_2b66550@0" ObjectIDZND1="g_2a6d930@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2b66550_0" Pin0InfoVect1LinkObjId="g_2a6d930_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bcea50_0" Pin1InfoVect1LinkObjId="SW-24997_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-319 4621,-88 4491,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b694b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4491,-88 4450,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b66550@0" ObjectIDND1="g_2bcea50@0" ObjectIDND2="10455@x" ObjectIDZND0="g_2a6d930@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2a6d930_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b66550_0" Pin1InfoVect1LinkObjId="g_2bcea50_0" Pin1InfoVect2LinkObjId="SW-24997_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4491,-88 4450,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b69710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5000,-261 5000,-252 5016,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2a8b330@0" ObjectIDZND2="g_2b5fdc0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2a8b330_0" Pin0InfoVect2LinkObjId="g_2b5fdc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5000,-261 5000,-252 5016,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b69970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5016,-252 5016,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2a8b330@0" ObjectIDND2="g_2b5fdc0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2a8b330_0" Pin1InfoVect2LinkObjId="g_2b5fdc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5016,-252 5016,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b6d030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4850,-978 4839,-978 4839,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2be7c20@0" ObjectIDZND0="g_2b6c880@0" ObjectIDZND1="3776@x" Pin0InfoVect0LinkObjId="g_2b6c880_0" Pin0InfoVect1LinkObjId="SW-23721_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be7c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4850,-978 4839,-978 4839,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b6da50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-1002 4860,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2be7c20@0" ObjectIDND1="3776@x" ObjectIDZND0="g_2b6c880@0" Pin0InfoVect0LinkObjId="g_2b6c880_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be7c20_0" Pin1InfoVect1LinkObjId="SW-23721_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-1002 4860,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b6dcb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-1002 4923,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2b6c880@1" ObjectIDZND0="g_2b6a0c0@0" Pin0InfoVect0LinkObjId="g_2b6a0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6c880_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-1002 4923,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8b240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-448 4418,-486 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2963eb0@0" ObjectIDND1="g_2964ba0@0" ObjectIDZND0="3779@0" Pin0InfoVect0LinkObjId="SW-23737_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2963eb0_0" Pin1InfoVect1LinkObjId="g_2964ba0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-448 4418,-486 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8b4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4418,-522 4418,-539 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3779@1" ObjectIDZND0="3768@0" Pin0InfoVect0LinkObjId="g_295a9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23737_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4418,-522 4418,-539 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b916d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4840,-1051 4822,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3869@1" ObjectIDZND0="3870@1" Pin0InfoVect0LinkObjId="SW-24956_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24954_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4840,-1051 4822,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b91930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-1051 4743,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3870@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2ac9970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24956_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-1051 4743,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b94640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-1002 4821,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2be7c20@0" ObjectIDND1="g_2b6c880@0" ObjectIDZND0="3776@1" Pin0InfoVect0LinkObjId="SW-23721_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be7c20_0" Pin1InfoVect1LinkObjId="g_2b6c880_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-1002 4821,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b948a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-1002 4743,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3776@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2ac9970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23721_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-1002 4743,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b975b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-919 4823,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3846@1" ObjectIDZND0="3847@1" Pin0InfoVect0LinkObjId="SW-24488_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-919 4823,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b97810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-919 4743,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3847@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2ac9970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24488_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-919 4743,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b97a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-866 4866,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="3851@0" ObjectIDZND0="3849@0" Pin0InfoVect0LinkObjId="SW-24579_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24582_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-866 4866,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9ad50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-866 4823,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3849@1" ObjectIDZND0="3850@1" Pin0InfoVect0LinkObjId="SW-24581_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24579_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-866 4823,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9afb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-866 4743,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3850@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2ac9970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24581_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-866 4743,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9dcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-787 4823,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3852@1" ObjectIDZND0="3853@1" Pin0InfoVect0LinkObjId="SW-24615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24613_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-787 4823,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b9df20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4787,-787 4743,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3853@0" ObjectIDZND0="3765@0" Pin0InfoVect0LinkObjId="g_2ac9970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4787,-787 4743,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba0c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-697 4821,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3855@1" ObjectIDZND0="3856@1" Pin0InfoVect0LinkObjId="SW-24649_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24647_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-697 4821,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba0e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-697 4743,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3856@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24649_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-697 4743,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba4170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-645 4743,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3777@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23724_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-645 4743,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ba6e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4841,-439 4821,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4841,-439 4821,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba7080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4785,-439 4743,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4785,-439 4743,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ba9d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-482 4822,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-482 4822,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ba9f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-482 4743,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-482 4743,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bacc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-526 4822,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-526 4822,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bacea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4786,-526 4743,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4786,-526 4743,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bafbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4839,-577 4824,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="3866@1" ObjectIDZND0="3867@1" Pin0InfoVect0LinkObjId="SW-24931_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24929_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4839,-577 4824,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bafe10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4788,-577 4743,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3867@0" ObjectIDZND0="3766@0" Pin0InfoVect0LinkObjId="g_2ae8e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24931_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4788,-577 4743,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb1660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3950,-712 4262,-712 4262,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" ObjectIDND0="3809@1" ObjectIDZND0="3875@x" Pin0InfoVect0LinkObjId="g_2bd3900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24101_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3950,-712 4262,-712 4262,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bb8cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5083,-697 5004,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="12150@0" ObjectIDZND0="g_2ace950@0" ObjectIDZND1="g_2be53d0@0" ObjectIDZND2="g_2bbcfc0@0" Pin0InfoVect0LinkObjId="g_2ace950_0" Pin0InfoVect1LinkObjId="g_2be53d0_0" Pin0InfoVect2LinkObjId="g_2bbcfc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_381Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5083,-697 5004,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bba570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5080,-787 5006,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="12149@0" ObjectIDZND0="g_2ac5b80@0" ObjectIDZND1="g_2be4660@0" ObjectIDZND2="g_2bbdea0@0" Pin0InfoVect0LinkObjId="g_2ac5b80_0" Pin0InfoVect1LinkObjId="g_2be4660_0" Pin0InfoVect2LinkObjId="g_2bbdea0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_376Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5080,-787 5006,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbb060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-697 4993,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2ace950@0" ObjectIDND1="12150@x" ObjectIDZND0="g_2be53d0@0" ObjectIDZND1="g_2bbcfc0@0" Pin0InfoVect0LinkObjId="g_2be53d0_0" Pin0InfoVect1LinkObjId="g_2bbcfc0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ace950_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_381Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-697 4993,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbb2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4993,-697 4993,-717 5006,-717 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ace950@0" ObjectIDND1="12150@x" ObjectIDND2="g_2bbcfc0@0" ObjectIDZND0="g_2be53d0@0" Pin0InfoVect0LinkObjId="g_2be53d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ace950_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_381Ld_0" Pin1InfoVect2LinkObjId="g_2bbcfc0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4993,-697 4993,-717 5006,-717 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbbdb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-787 4995,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2ac5b80@0" ObjectIDND1="12149@x" ObjectIDZND0="g_2be4660@0" ObjectIDZND1="g_2bbdea0@0" Pin0InfoVect0LinkObjId="g_2be4660_0" Pin0InfoVect1LinkObjId="g_2bbdea0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac5b80_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_376Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-787 4995,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbc010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-787 4995,-807 5008,-807 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2ac5b80@0" ObjectIDND1="12149@x" ObjectIDND2="g_2bbdea0@0" ObjectIDZND0="g_2be4660@0" Pin0InfoVect0LinkObjId="g_2be4660_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ac5b80_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_376Ld_0" Pin1InfoVect2LinkObjId="g_2bbdea0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-787 4995,-807 5008,-807 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbcb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5010,-1071 4997,-1071 4997,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2be2b80@0" ObjectIDZND0="g_2bbfc60@0" ObjectIDZND1="12147@x" Pin0InfoVect0LinkObjId="g_2bbfc60_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be2b80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5010,-1071 4997,-1071 4997,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbcd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-1051 5082,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2bbfc60@0" ObjectIDND1="g_2be2b80@0" ObjectIDZND0="12147@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bbfc60_0" Pin1InfoVect1LinkObjId="g_2be2b80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-1051 5082,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbd9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4914,-697 4925,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3857@1" ObjectIDZND0="g_2bbcfc0@1" Pin0InfoVect0LinkObjId="g_2bbcfc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4914,-697 4925,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbdc40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4978,-697 4993,-697 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_2bbcfc0@0" ObjectIDZND0="g_2ace950@0" ObjectIDZND1="12150@x" ObjectIDZND2="g_2be53d0@0" Pin0InfoVect0LinkObjId="g_2ace950_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_381Ld_0" Pin0InfoVect2LinkObjId="g_2be53d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbcfc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4978,-697 4993,-697 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbe8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-787 4931,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3854@1" ObjectIDZND0="g_2bbdea0@1" Pin0InfoVect0LinkObjId="g_2bbdea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-787 4931,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbeb20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-787 4996,-787 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_2bbdea0@0" ObjectIDZND0="g_2ac5b80@0" ObjectIDZND1="12149@x" ObjectIDZND2="g_2be4660@0" Pin0InfoVect0LinkObjId="g_2ac5b80_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_376Ld_0" Pin0InfoVect2LinkObjId="g_2be4660_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbdea0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-787 4996,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbf7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-919 4930,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3848@1" ObjectIDZND0="g_2bbed80@1" Pin0InfoVect0LinkObjId="g_2bbed80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24489_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-919 4930,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bbfa00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4983,-919 4992,-919 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2bbed80@0" ObjectIDZND0="12146@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbed80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4983,-919 4992,-919 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc0680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4918,-1051 4934,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3871@1" ObjectIDZND0="g_2bbfc60@1" Pin0InfoVect0LinkObjId="g_2bbfc60_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24957_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4918,-1051 4934,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc08e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4987,-1051 4997,-1051 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="g_2bbfc60@0" ObjectIDZND0="12147@x" ObjectIDZND1="g_2be2b80@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_373Ld_0" Pin0InfoVect1LinkObjId="g_2be2b80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bbfc60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4987,-1051 4997,-1051 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc13d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5006,-866 4995,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_2abfac0@0" ObjectIDND1="12148@x" ObjectIDZND0="g_2be38f0@0" ObjectIDZND1="g_2bb7430@0" Pin0InfoVect0LinkObjId="g_2be38f0_0" Pin0InfoVect1LinkObjId="g_2bb7430_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2abfac0_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_375Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5006,-866 4995,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc1630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4995,-866 4995,-885 5008,-885 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2abfac0@0" ObjectIDND1="12148@x" ObjectIDND2="g_2bb7430@0" ObjectIDZND0="g_2be38f0@0" Pin0InfoVect0LinkObjId="g_2be38f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2abfac0_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_375Ld_0" Pin1InfoVect2LinkObjId="g_2bb7430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4995,-866 4995,-885 5008,-885 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc1890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-866 4931,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3851@1" ObjectIDZND0="g_2bb7430@1" Pin0InfoVect0LinkObjId="g_2bb7430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24582_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-866 4931,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc1af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-866 4996,-866 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="lightningRod" ObjectIDND0="g_2bb7430@0" ObjectIDZND0="g_2abfac0@0" ObjectIDZND1="12148@x" ObjectIDZND2="g_2be38f0@0" Pin0InfoVect0LinkObjId="g_2abfac0_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_375Ld_0" Pin0InfoVect2LinkObjId="g_2be38f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb7430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-866 4996,-866 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc24b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4916,-577 4942,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="3868@1" ObjectIDZND0="g_2bb7c00@1" Pin0InfoVect0LinkObjId="g_2bb7c00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24932_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4916,-577 4942,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc2fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4997,-577 5014,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_2bb7c00@0" ObjectIDZND0="g_2be6140@0" ObjectIDZND1="12151@x" Pin0InfoVect0LinkObjId="g_2be6140_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb7c00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4997,-577 5014,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bc3200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-577 5059,-577 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2be6140@0" ObjectIDND1="g_2bb7c00@0" ObjectIDZND0="12151@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_382Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2be6140_0" Pin1InfoVect1LinkObjId="g_2bb7c00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-577 5059,-577 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc3e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-439 4996,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2bc3460@0" Pin0InfoVect0LinkObjId="g_2bc3460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-439 4996,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc40e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-439 4918,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bc3460@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc3460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-439 4918,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc4d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5041,-482 4996,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2bc4340@0" Pin0InfoVect0LinkObjId="g_2bc4340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5041,-482 4996,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc4fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4943,-482 4916,-482 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bc4340@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc4340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4943,-482 4916,-482 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc5c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5025,-526 4994,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_2bc5220@0" Pin0InfoVect0LinkObjId="g_2bc5220_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5025,-526 4994,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc5ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-526 4916,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2bc5220@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc5220_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-526 4916,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc6b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4925,-1099 4951,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2bc6100@1" Pin0InfoVect0LinkObjId="g_2bc6100_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4925,-1099 4951,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc6d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5004,-1099 5030,-1099 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2bc6100@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc6100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="5004,-1099 5030,-1099 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc7a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4924,-1143 4946,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2bc6fe0@1" Pin0InfoVect0LinkObjId="g_2bc6fe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4924,-1143 4946,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2bc7c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4999,-1143 5030,-1143 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2bc6fe0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc6fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4999,-1143 5030,-1143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5031,-276 5031,-260 5016,-260 5016,-251 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2a8b330@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2b5fdc0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2b5fdc0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a8b330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="5031,-276 5031,-260 5016,-260 5016,-251 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd2950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3609,-694 3599,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2bd02d0@0" ObjectIDZND0="3874@x" ObjectIDZND1="3796@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="SW-23917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd02d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3609,-694 3599,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd3440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-718 3599,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3874@2" ObjectIDZND0="g_2bd02d0@0" ObjectIDZND1="3796@x" Pin0InfoVect0LinkObjId="g_2bd02d0_0" Pin0InfoVect1LinkObjId="SW-23917_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd2950_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-718 3599,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd36a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3599,-694 3599,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2bd02d0@0" ObjectIDND1="3874@x" ObjectIDZND0="3796@1" Pin0InfoVect0LinkObjId="SW-23917_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd02d0_0" Pin1InfoVect1LinkObjId="g_2bd2950_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3599,-694 3599,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd3900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4231,-695 4224,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="switch" ObjectIDND0="g_2bd1040@0" ObjectIDZND0="3875@x" ObjectIDZND1="3812@x" Pin0InfoVect0LinkObjId="g_2bb1660_0" Pin0InfoVect1LinkObjId="SW-24107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd1040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4231,-695 4224,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd43f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-718 4224,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3875@2" ObjectIDZND0="g_2bd1040@0" ObjectIDZND1="3812@x" Pin0InfoVect0LinkObjId="g_2bd1040_0" Pin0InfoVect1LinkObjId="SW-24107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bb1660_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-718 4224,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd4650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4224,-695 4224,-681 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="switch" ObjectIDND0="g_2bd1040@0" ObjectIDND1="3875@x" ObjectIDZND0="3812@1" Pin0InfoVect0LinkObjId="SW-24107_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bd1040_0" Pin1InfoVect1LinkObjId="g_2bb1660_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4224,-695 4224,-681 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd7e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3858,-300 3848,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2bd48b0@0" ObjectIDZND0="3829@x" ObjectIDZND1="3830@x" ObjectIDZND2="11722@x" Pin0InfoVect0LinkObjId="SW-24355_0" Pin0InfoVect1LinkObjId="SW-24356_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd48b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3858,-300 3848,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd8960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-313 3848,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3829@x" ObjectIDND1="3830@x" ObjectIDZND0="g_2bd48b0@0" ObjectIDZND1="11722@x" Pin0InfoVect0LinkObjId="g_2bd48b0_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24355_0" Pin1InfoVect1LinkObjId="SW-24356_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-313 3848,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd8bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3848,-300 3848,-281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2bd48b0@0" ObjectIDND1="3829@x" ObjectIDND2="3830@x" ObjectIDZND0="11722@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_1C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bd48b0_0" Pin1InfoVect1LinkObjId="SW-24355_0" Pin1InfoVect2LinkObjId="SW-24356_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3848,-300 3848,-281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd8e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3961,-300 3952,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2bd5620@0" ObjectIDZND0="3833@x" ObjectIDZND1="3834@x" ObjectIDZND2="11723@x" Pin0InfoVect0LinkObjId="SW-24385_0" Pin0InfoVect1LinkObjId="SW-24386_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd5620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3961,-300 3952,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd9910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-313 3952,-300 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3833@x" ObjectIDND1="3834@x" ObjectIDZND0="g_2bd5620@0" ObjectIDZND1="11723@x" Pin0InfoVect0LinkObjId="g_2bd5620_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24385_0" Pin1InfoVect1LinkObjId="SW-24386_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-313 3952,-300 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd9b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3952,-300 3952,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2bd5620@0" ObjectIDND1="3833@x" ObjectIDND2="3834@x" ObjectIDZND0="11723@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_2C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bd5620_0" Pin1InfoVect1LinkObjId="SW-24385_0" Pin1InfoVect2LinkObjId="SW-24386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3952,-300 3952,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bd9dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4169,-301 4161,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2bd6390@0" ObjectIDZND0="3837@x" ObjectIDZND1="3838@x" ObjectIDZND2="11724@x" Pin0InfoVect0LinkObjId="SW-24415_0" Pin0InfoVect1LinkObjId="SW-24416_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd6390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4169,-301 4161,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bda8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-313 4161,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3837@x" ObjectIDND1="3838@x" ObjectIDZND0="g_2bd6390@0" ObjectIDZND1="11724@x" Pin0InfoVect0LinkObjId="g_2bd6390_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24415_0" Pin1InfoVect1LinkObjId="SW-24416_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-313 4161,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdab20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4161,-301 4161,-280 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2bd6390@0" ObjectIDND1="3837@x" ObjectIDND2="3838@x" ObjectIDZND0="11724@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_3C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bd6390_0" Pin1InfoVect1LinkObjId="SW-24415_0" Pin1InfoVect2LinkObjId="SW-24416_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4161,-301 4161,-280 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4286,-302 4277,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2bd7100@0" ObjectIDZND0="3841@x" ObjectIDZND1="3842@x" ObjectIDZND2="11725@x" Pin0InfoVect0LinkObjId="SW-24444_0" Pin0InfoVect1LinkObjId="SW-24445_0" Pin0InfoVect2LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd7100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4286,-302 4277,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdb870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-313 4277,-302 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="capacitor" ObjectIDND0="3841@x" ObjectIDND1="3842@x" ObjectIDZND0="g_2bd7100@0" ObjectIDZND1="11725@x" Pin0InfoVect0LinkObjId="g_2bd7100_0" Pin0InfoVect1LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24444_0" Pin1InfoVect1LinkObjId="SW-24445_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-313 4277,-302 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdbad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4277,-302 4277,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="capacitor" ObjectIDND0="g_2bd7100@0" ObjectIDND1="3841@x" ObjectIDND2="3842@x" ObjectIDZND0="11725@0" Pin0InfoVect0LinkObjId="CB-CX_HS.CX_HS_4C_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bd7100_0" Pin1InfoVect1LinkObjId="SW-24444_0" Pin1InfoVect2LinkObjId="SW-24445_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4277,-302 4277,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bddd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-284 4492,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="12152@0" ObjectIDZND0="3826@x" ObjectIDZND1="g_2bcf7c0@0" Pin0InfoVect0LinkObjId="SW-24323_0" Pin0InfoVect1LinkObjId="g_2bcf7c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_084Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-284 4492,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bde750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-330 4492,-320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="3826@0" ObjectIDZND0="g_2bcf7c0@0" ObjectIDZND1="12152@x" Pin0InfoVect0LinkObjId="g_2bcf7c0_0" Pin0InfoVect1LinkObjId="EC-CX_HS.CX_HS_084Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-330 4492,-320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bde9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4492,-320 4459,-320 4459,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" ObjectIDND0="3826@x" ObjectIDND1="12152@x" ObjectIDZND0="g_2bcf7c0@0" Pin0InfoVect0LinkObjId="g_2bcf7c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24323_0" Pin1InfoVect1LinkObjId="EC-CX_HS.CX_HS_084Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4492,-320 4459,-320 4459,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdf370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-318 3780,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2946da0@0" ObjectIDND1="3823@x" ObjectIDZND0="12153@0" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_075Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2946da0_0" Pin1InfoVect1LinkObjId="SW-24286_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-318 3780,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdfe60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3746,-302 3746,-318 3780,-318 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2946da0@0" ObjectIDZND0="12153@x" ObjectIDZND1="3823@x" Pin0InfoVect0LinkObjId="EC-CX_HS.CX_HS_075Ld_0" Pin0InfoVect1LinkObjId="SW-24286_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2946da0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3746,-302 3746,-318 3780,-318 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be00c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3780,-318 3780,-329 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="12153@x" ObjectIDND1="g_2946da0@0" ObjectIDZND0="3823@0" Pin0InfoVect0LinkObjId="SW-24286_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_HS.CX_HS_075Ld_0" Pin1InfoVect1LinkObjId="g_2946da0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3780,-318 3780,-329 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2beb240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4853,-612 4837,-612 4837,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2be6eb0@0" ObjectIDZND0="g_2acfc90@0" ObjectIDZND1="3777@x" Pin0InfoVect0LinkObjId="g_2acfc90_0" Pin0InfoVect1LinkObjId="SW-23724_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2be6eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4853,-612 4837,-612 4837,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2beb4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4837,-645 4821,-645 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2acfc90@0" ObjectIDND1="g_2be6eb0@0" ObjectIDZND0="3777@1" Pin0InfoVect0LinkObjId="SW-23724_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2acfc90_0" Pin1InfoVect1LinkObjId="g_2be6eb0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-645 4821,-645 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2beb920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3600,-880 3600,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3787@1" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_2bef200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23887_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3600,-880 3600,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bec3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-949 4107,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3801@0" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_2bec610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4107,-949 4107,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bec610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4225,-898 4225,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3803@1" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_2bec3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24078_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4225,-898 4225,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bec870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-904 3927,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3816@1" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_2bec3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-904 3927,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2becad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-960 3951,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3773@x" ObjectIDND1="3775@x" ObjectIDZND0="3764@0" Pin0InfoVect0LinkObjId="g_2bec3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23716_0" Pin1InfoVect1LinkObjId="SW-23718_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-960 3951,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bed5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-983 3951,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3773@0" ObjectIDZND0="3764@0" ObjectIDZND1="3775@x" Pin0InfoVect0LinkObjId="g_2bec3b0_0" Pin0InfoVect1LinkObjId="SW-23718_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23716_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-983 3951,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bed820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3951,-960 3969,-960 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3764@0" ObjectIDND1="3773@x" ObjectIDZND0="3775@0" Pin0InfoVect0LinkObjId="SW-23718_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bec3b0_0" Pin1InfoVect1LinkObjId="SW-23716_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3951,-960 3969,-960 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bef200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-942 3721,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3785@0" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_2beb920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23885_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3721,-942 3721,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bef460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-953 3560,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="3770@x" ObjectIDND1="3772@x" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_2beb920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-23711_0" Pin1InfoVect1LinkObjId="SW-23713_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-953 3560,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2beff50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-976 3560,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="3770@0" ObjectIDZND0="3763@0" ObjectIDZND1="3772@x" Pin0InfoVect0LinkObjId="g_2beb920_0" Pin0InfoVect1LinkObjId="SW-23713_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-23711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-976 3560,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf01b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3560,-953 3577,-953 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3763@0" ObjectIDND1="3770@x" ObjectIDZND0="3772@0" Pin0InfoVect0LinkObjId="SW-23713_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2beb920_0" Pin1InfoVect1LinkObjId="SW-23711_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3560,-953 3577,-953 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf0410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-902 3806,-922 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="3814@1" ObjectIDZND0="3763@0" Pin0InfoVect0LinkObjId="g_2beb920_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-902 3806,-922 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-866 3806,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3814@0" ObjectIDZND0="3813@x" ObjectIDZND1="3815@x" Pin0InfoVect0LinkObjId="SW-24174_0" Pin0InfoVect1LinkObjId="SW-24181_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-866 3806,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3806,-836 3806,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="3815@1" ObjectIDZND0="3814@x" ObjectIDZND1="3813@x" Pin0InfoVect0LinkObjId="SW-24180_0" Pin0InfoVect1LinkObjId="SW-24174_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3806,-836 3806,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf2f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-851 3927,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="3813@0" ObjectIDZND0="3816@x" ObjectIDZND1="3817@x" Pin0InfoVect0LinkObjId="SW-24182_0" Pin0InfoVect1LinkObjId="SW-24183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24174_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-851 3927,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf3a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-868 3927,-851 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="3816@0" ObjectIDZND0="3813@x" ObjectIDZND1="3817@x" Pin0InfoVect0LinkObjId="SW-24174_0" Pin0InfoVect1LinkObjId="SW-24183_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24182_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-868 3927,-851 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-110KV" id="g_2bf3c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3927,-851 3927,-838 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="3813@x" ObjectIDND1="3816@x" ObjectIDZND0="3817@1" Pin0InfoVect0LinkObjId="SW-24183_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24174_0" Pin1InfoVect1LinkObjId="SW-24182_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3927,-851 3927,-838 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf7540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3517,-297 3517,-312 3548,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b229a0@0" ObjectIDZND0="g_2be8990@0" ObjectIDZND1="0@x" ObjectIDZND2="10452@x" Pin0InfoVect0LinkObjId="g_2be8990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-24978_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b229a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3517,-297 3517,-312 3548,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3548,-312 3548,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2be8990@0" ObjectIDND1="0@x" ObjectIDND2="g_2b229a0@0" ObjectIDZND0="10452@0" Pin0InfoVect0LinkObjId="SW-24978_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2be8990_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2b229a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3548,-312 3548,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfac30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-539 4621,-526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="3768@0" ObjectIDZND0="10454@1" Pin0InfoVect0LinkObjId="SW-24996_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295a9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-539 4621,-526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-495 4621,-478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10454@0" ObjectIDZND0="3873@1" Pin0InfoVect0LinkObjId="SW-24994_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24996_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-495 4621,-478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bfd2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4621,-451 4621,-430 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="3873@0" ObjectIDZND0="g_2a9c3b0@0" Pin0InfoVect0LinkObjId="g_2a9c3b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24994_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4621,-451 4621,-430 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bfd540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3636,-759 3636,-712 3783,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="3874@x" ObjectIDZND0="3793@0" Pin0InfoVect0LinkObjId="SW-23910_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd2950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3636,-759 3636,-712 3783,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bfd7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-725 3844,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b09290@0" ObjectIDZND0="3793@x" ObjectIDZND1="3809@x" ObjectIDZND2="g_293f450@0" Pin0InfoVect0LinkObjId="SW-23910_0" Pin0InfoVect1LinkObjId="SW-24101_0" Pin0InfoVect2LinkObjId="g_293f450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b09290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-725 3844,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bfe290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3844,-712 3819,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b09290@0" ObjectIDND1="3809@x" ObjectIDND2="g_293f450@0" ObjectIDZND0="3793@1" Pin0InfoVect0LinkObjId="SW-23910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b09290_0" Pin1InfoVect1LinkObjId="SW-24101_0" Pin1InfoVect2LinkObjId="g_293f450_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3844,-712 3819,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bfe4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-726 3879,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_293f450@0" ObjectIDZND0="3809@x" ObjectIDZND1="g_2b09290@0" ObjectIDZND2="3793@x" Pin0InfoVect0LinkObjId="SW-24101_0" Pin0InfoVect1LinkObjId="g_2b09290_0" Pin0InfoVect2LinkObjId="SW-23910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_293f450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-726 3879,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bfefe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3914,-712 3879,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="3809@0" ObjectIDZND0="g_2b09290@0" ObjectIDZND1="3793@x" ObjectIDZND2="g_293f450@0" Pin0InfoVect0LinkObjId="g_2b09290_0" Pin0InfoVect1LinkObjId="SW-23910_0" Pin0InfoVect2LinkObjId="g_293f450_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-24101_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3914,-712 3879,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bff240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-712 3844,-712 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3809@x" ObjectIDND1="g_293f450@0" ObjectIDZND0="g_2b09290@0" ObjectIDZND1="3793@x" Pin0InfoVect0LinkObjId="g_2b09290_0" Pin0InfoVect1LinkObjId="SW-23910_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-24101_0" Pin1InfoVect1LinkObjId="g_293f450_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-712 3844,-712 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2bff4a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3654,-759 3678,-759 3678,-707 4497,-707 4497,-951 4575,-951 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="3874@0" ObjectIDZND0="g_2afbb90@0" ObjectIDZND1="3792@x" Pin0InfoVect0LinkObjId="g_2afbb90_0" Pin0InfoVect1LinkObjId="SW-23903_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bd2950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3654,-759 3678,-759 3678,-707 4497,-707 4497,-951 4575,-951 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-314 3613,-123 4725,-123 4725,-75 5083,-75 5083,-356 5016,-356 5016,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b5fdc0@0" ObjectIDND1="3862@x" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2a8b330@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2a8b330_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b5fdc0_0" Pin1InfoVect1LinkObjId="SW-24890_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-314 3613,-123 4725,-123 4725,-75 5083,-75 5083,-356 5016,-356 5016,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0cc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3584,-299 3584,-314 3613,-314 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_2b5fdc0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" ObjectIDZND2="g_2a8b330@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2a8b330_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5fdc0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3584,-299 3584,-314 3613,-314 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2c0ce70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3613,-314 3613,-327 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDND2="g_2a8b330@0" ObjectIDZND0="3862@0" Pin0InfoVect0LinkObjId="SW-24890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_2a8b330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3613,-314 3613,-327 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" id="DYN-24" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3311.000000 -1094.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="24" ObjectName="DYN-CX_HS"/>
     <cge:Meas_Ref ObjectId="24"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3756.000000 1204.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3745.000000 1189.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3770.000000 1174.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4173.000000 1192.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4162.000000 1177.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4187.000000 1162.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3647.000000 642.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 627.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3661.000000 612.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 659.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 644.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 629.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5141.000000 1117.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5130.000000 1102.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5155.000000 1087.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 939.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 924.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 909.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4606.000000 612.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 597.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4620.000000 582.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3488.000000 180.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 165.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3502.000000 150.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3702.000000 228.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3691.000000 213.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3716.000000 198.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4430.000000 225.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4419.000000 210.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4444.000000 195.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3796.000000 167.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3821.000000 152.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 813.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 798.000000) translate(0,12)">油温1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3653.000000 783.000000) translate(0,12)">油温2（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 770.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 755.000000) translate(0,12)">油温1（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 740.000000) translate(0,12)">油温2（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 610.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 596.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3469.500000 581.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3455.000000 565.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3463.000000 625.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 603.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 589.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4441.500000 574.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4427.000000 558.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 618.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 397.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 383.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4819.500000 368.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4805.000000 352.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 412.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1247.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1233.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4705.500000 1218.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 1202.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4699.000000 1262.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_LF" endPointId="0" endStationName="CX_HS" flowDrawDirect="1" flowShape="0" id="AC-110kV.luhong_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3721,-1143 3721,-1190 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="9182" ObjectName="AC-110kV.luhong_line"/>
    <cge:TPSR_Ref TObjectID="9182_SS-24"/></metadata>
   <polyline fill="none" opacity="0" points="3721,-1143 3721,-1190 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_YZ" endPointId="0" endStationName="CX_HS" flowDrawDirect="1" flowShape="0" id="AC-110kV.yaohong_line" runFlow="0">
    <g class="BV-110KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4107,-1151 4107,-1205 " stroke-width="1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11433" ObjectName="AC-110kV.yaohong_line"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4107,-1151 4107,-1205 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer"/><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-24078">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4216.000000 -857.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3803" ObjectName="SW-CX_HS.CX_HS_1022SW"/>
     <cge:Meas_Ref ObjectId="24078"/>
    <cge:TPSR_Ref TObjectID="3803"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24076">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3801" ObjectName="SW-CX_HS.CX_HS_1722SW"/>
     <cge:Meas_Ref ObjectId="24076"/>
    <cge:TPSR_Ref TObjectID="3801"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24073">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4098.000000 -1057.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3798" ObjectName="SW-CX_HS.CX_HS_1726SW"/>
     <cge:Meas_Ref ObjectId="24073"/>
    <cge:TPSR_Ref TObjectID="3798"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23888">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3612.000000 -820.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3788" ObjectName="SW-CX_HS.CX_HS_10117SW"/>
     <cge:Meas_Ref ObjectId="23888"/>
    <cge:TPSR_Ref TObjectID="3788"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23887">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3591.000000 -839.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3787" ObjectName="SW-CX_HS.CX_HS_1011SW"/>
     <cge:Meas_Ref ObjectId="23887"/>
    <cge:TPSR_Ref TObjectID="3787"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23713">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3572.000000 -948.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3772" ObjectName="SW-CX_HS.CX_HS_19010SW"/>
     <cge:Meas_Ref ObjectId="23713"/>
    <cge:TPSR_Ref TObjectID="3772"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23711">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3551.000000 -971.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3770" ObjectName="SW-CX_HS.CX_HS_1901SW"/>
     <cge:Meas_Ref ObjectId="23711"/>
    <cge:TPSR_Ref TObjectID="3770"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23712">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3571.000000 -1026.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3771" ObjectName="SW-CX_HS.CX_HS_19017SW"/>
     <cge:Meas_Ref ObjectId="23712"/>
    <cge:TPSR_Ref TObjectID="3771"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23885">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -937.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3785" ObjectName="SW-CX_HS.CX_HS_1711SW"/>
     <cge:Meas_Ref ObjectId="23885"/>
    <cge:TPSR_Ref TObjectID="3785"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23886">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -983.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3786" ObjectName="SW-CX_HS.CX_HS_17117SW"/>
     <cge:Meas_Ref ObjectId="23886"/>
    <cge:TPSR_Ref TObjectID="3786"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23884">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3784" ObjectName="SW-CX_HS.CX_HS_17160SW"/>
     <cge:Meas_Ref ObjectId="23884"/>
    <cge:TPSR_Ref TObjectID="3784"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23882">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3782" ObjectName="SW-CX_HS.CX_HS_1716SW"/>
     <cge:Meas_Ref ObjectId="23882"/>
    <cge:TPSR_Ref TObjectID="3782"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23883">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3728.000000 -1096.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3783" ObjectName="SW-CX_HS.CX_HS_17167SW"/>
     <cge:Meas_Ref ObjectId="23883"/>
    <cge:TPSR_Ref TObjectID="3783"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24079">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4237.000000 -842.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3804" ObjectName="SW-CX_HS.CX_HS_10227SW"/>
     <cge:Meas_Ref ObjectId="24079"/>
    <cge:TPSR_Ref TObjectID="3804"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24074">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1106.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3799" ObjectName="SW-CX_HS.CX_HS_17267SW"/>
     <cge:Meas_Ref ObjectId="24074"/>
    <cge:TPSR_Ref TObjectID="3799"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24075">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3800" ObjectName="SW-CX_HS.CX_HS_17260SW"/>
     <cge:Meas_Ref ObjectId="24075"/>
    <cge:TPSR_Ref TObjectID="3800"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24077">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4114.000000 -993.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3802" ObjectName="SW-CX_HS.CX_HS_17227SW"/>
     <cge:Meas_Ref ObjectId="24077"/>
    <cge:TPSR_Ref TObjectID="3802"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23718">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -955.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3775" ObjectName="SW-CX_HS.CX_HS_19020SW"/>
     <cge:Meas_Ref ObjectId="23718"/>
    <cge:TPSR_Ref TObjectID="3775"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23716">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -978.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3773" ObjectName="SW-CX_HS.CX_HS_1902SW"/>
     <cge:Meas_Ref ObjectId="23716"/>
    <cge:TPSR_Ref TObjectID="3773"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23717">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3962.000000 -1033.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3774" ObjectName="SW-CX_HS.CX_HS_19027SW"/>
     <cge:Meas_Ref ObjectId="23717"/>
    <cge:TPSR_Ref TObjectID="3774"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24180">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 -861.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3814" ObjectName="SW-CX_HS.CX_HS_1121SW"/>
     <cge:Meas_Ref ObjectId="24180"/>
    <cge:TPSR_Ref TObjectID="3814"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24182">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -863.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3816" ObjectName="SW-CX_HS.CX_HS_1122SW"/>
     <cge:Meas_Ref ObjectId="24182"/>
    <cge:TPSR_Ref TObjectID="3816"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24183">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3918.000000 -797.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3817" ObjectName="SW-CX_HS.CX_HS_11227SW"/>
     <cge:Meas_Ref ObjectId="24183"/>
    <cge:TPSR_Ref TObjectID="3817"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24181">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3797.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3815" ObjectName="SW-CX_HS.CX_HS_11217SW"/>
     <cge:Meas_Ref ObjectId="24181"/>
    <cge:TPSR_Ref TObjectID="3815"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23897">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3509.000000 -789.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3789" ObjectName="SW-CX_HS.CX_HS_1010SW"/>
     <cge:Meas_Ref ObjectId="23897"/>
    <cge:TPSR_Ref TObjectID="3789"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24977">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3822" ObjectName="SW-CX_HS.CX_HS_0731SW"/>
     <cge:Meas_Ref ObjectId="24977"/>
    <cge:TPSR_Ref TObjectID="3822"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24978">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3539.000000 -323.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10452" ObjectName="SW-CX_HS.CX_HS_0736SW"/>
     <cge:Meas_Ref ObjectId="24978"/>
    <cge:TPSR_Ref TObjectID="10452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24889">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3861" ObjectName="SW-CX_HS.CX_HS_0741SW"/>
     <cge:Meas_Ref ObjectId="24889"/>
    <cge:TPSR_Ref TObjectID="3861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24890">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3604.000000 -322.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3862" ObjectName="SW-CX_HS.CX_HS_0746SW"/>
     <cge:Meas_Ref ObjectId="24890"/>
    <cge:TPSR_Ref TObjectID="3862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23917">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -640.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3796" ObjectName="SW-CX_HS.CX_HS_0016SW"/>
     <cge:Meas_Ref ObjectId="23917"/>
    <cge:TPSR_Ref TObjectID="3796"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23916">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 -547.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3795" ObjectName="SW-CX_HS.CX_HS_0011SW"/>
     <cge:Meas_Ref ObjectId="23916"/>
    <cge:TPSR_Ref TObjectID="3795"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.000000 -707.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3793" ObjectName="SW-CX_HS.CX_HS_3010SW"/>
     <cge:Meas_Ref ObjectId="23910"/>
    <cge:TPSR_Ref TObjectID="3793"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24101">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3909.000000 -707.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3809" ObjectName="SW-CX_HS.CX_HS_3020SW"/>
     <cge:Meas_Ref ObjectId="24101"/>
    <cge:TPSR_Ref TObjectID="3809"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24285">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10453" ObjectName="SW-CX_HS.CX_HS_0751SW"/>
     <cge:Meas_Ref ObjectId="24285"/>
    <cge:TPSR_Ref TObjectID="10453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24286">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3771.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3823" ObjectName="SW-CX_HS.CX_HS_0756SW"/>
     <cge:Meas_Ref ObjectId="24286"/>
    <cge:TPSR_Ref TObjectID="3823"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24385">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3833" ObjectName="SW-CX_HS.CX_HS_0776SW"/>
     <cge:Meas_Ref ObjectId="24385"/>
    <cge:TPSR_Ref TObjectID="3833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24384">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3943.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3832" ObjectName="SW-CX_HS.CX_HS_0771SW"/>
     <cge:Meas_Ref ObjectId="24384"/>
    <cge:TPSR_Ref TObjectID="3832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24386">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3964.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3834" ObjectName="SW-CX_HS.CX_HS_07767SW"/>
     <cge:Meas_Ref ObjectId="24386"/>
    <cge:TPSR_Ref TObjectID="3834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23744">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -480.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3780" ObjectName="SW-CX_HS.CX_HS_0812SW"/>
     <cge:Meas_Ref ObjectId="23744"/>
    <cge:TPSR_Ref TObjectID="3780"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24247">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3961.000000 -556.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3820" ObjectName="SW-CX_HS.CX_HS_0121SW"/>
     <cge:Meas_Ref ObjectId="24247"/>
    <cge:TPSR_Ref TObjectID="3820"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24246">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4060.000000 -557.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3819" ObjectName="SW-CX_HS.CX_HS_0122SW"/>
     <cge:Meas_Ref ObjectId="24246"/>
    <cge:TPSR_Ref TObjectID="3819"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -640.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3812" ObjectName="SW-CX_HS.CX_HS_0026SW"/>
     <cge:Meas_Ref ObjectId="24107"/>
    <cge:TPSR_Ref TObjectID="3812"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4215.000000 -547.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3811" ObjectName="SW-CX_HS.CX_HS_0022SW"/>
     <cge:Meas_Ref ObjectId="24106"/>
    <cge:TPSR_Ref TObjectID="3811"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24322">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -483.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3825" ObjectName="SW-CX_HS.CX_HS_0842SW"/>
     <cge:Meas_Ref ObjectId="24322"/>
    <cge:TPSR_Ref TObjectID="3825"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24323">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4483.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3826" ObjectName="SW-CX_HS.CX_HS_0846SW"/>
     <cge:Meas_Ref ObjectId="24323"/>
    <cge:TPSR_Ref TObjectID="3826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24908">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -484.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3864" ObjectName="SW-CX_HS.CX_HS_0852SW"/>
     <cge:Meas_Ref ObjectId="24908"/>
    <cge:TPSR_Ref TObjectID="3864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24909">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4556.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3865" ObjectName="SW-CX_HS.CX_HS_0856SW"/>
     <cge:Meas_Ref ObjectId="24909"/>
    <cge:TPSR_Ref TObjectID="3865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24444">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3841" ObjectName="SW-CX_HS.CX_HS_0836SW"/>
     <cge:Meas_Ref ObjectId="24444"/>
    <cge:TPSR_Ref TObjectID="3841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24443">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4268.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3840" ObjectName="SW-CX_HS.CX_HS_0832SW"/>
     <cge:Meas_Ref ObjectId="24443"/>
    <cge:TPSR_Ref TObjectID="3840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24445">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4290.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3842" ObjectName="SW-CX_HS.CX_HS_08367SW"/>
     <cge:Meas_Ref ObjectId="24445"/>
    <cge:TPSR_Ref TObjectID="3842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24415">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3837" ObjectName="SW-CX_HS.CX_HS_0826SW"/>
     <cge:Meas_Ref ObjectId="24415"/>
    <cge:TPSR_Ref TObjectID="3837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24414">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4152.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3836" ObjectName="SW-CX_HS.CX_HS_0822SW"/>
     <cge:Meas_Ref ObjectId="24414"/>
    <cge:TPSR_Ref TObjectID="3836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24416">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4174.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3838" ObjectName="SW-CX_HS.CX_HS_08267SW"/>
     <cge:Meas_Ref ObjectId="24416"/>
    <cge:TPSR_Ref TObjectID="3838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24355">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -324.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3829" ObjectName="SW-CX_HS.CX_HS_0766SW"/>
     <cge:Meas_Ref ObjectId="24355"/>
    <cge:TPSR_Ref TObjectID="3829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24354">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3839.000000 -482.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3828" ObjectName="SW-CX_HS.CX_HS_0761SW"/>
     <cge:Meas_Ref ObjectId="24354"/>
    <cge:TPSR_Ref TObjectID="3828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24356">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3860.000000 -311.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3830" ObjectName="SW-CX_HS.CX_HS_07667SW"/>
     <cge:Meas_Ref ObjectId="24356"/>
    <cge:TPSR_Ref TObjectID="3830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4859.000000 -207.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3706.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4317.000000 -83.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -207.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4787.000000 -136.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4771.000000 -249.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -208.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5006.000000 -137.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4991.000000 -256.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24088">
    <use class="BV-110KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4343.000000 -788.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3805" ObjectName="SW-CX_HS.CX_HS_1020SW"/>
     <cge:Meas_Ref ObjectId="24088"/>
    <cge:TPSR_Ref TObjectID="3805"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4883.000000 -1138.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -1138.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -1094.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -1094.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24957">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3871" ObjectName="SW-CX_HS.CX_HS_3736SW"/>
     <cge:Meas_Ref ObjectId="24957"/>
    <cge:TPSR_Ref TObjectID="3871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24489">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -914.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3848" ObjectName="SW-CX_HS.CX_HS_3746SW"/>
     <cge:Meas_Ref ObjectId="24489"/>
    <cge:TPSR_Ref TObjectID="3848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24582">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -861.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3851" ObjectName="SW-CX_HS.CX_HS_3756SW"/>
     <cge:Meas_Ref ObjectId="24582"/>
    <cge:TPSR_Ref TObjectID="3851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3854" ObjectName="SW-CX_HS.CX_HS_3766SW"/>
     <cge:Meas_Ref ObjectId="24616"/>
    <cge:TPSR_Ref TObjectID="3854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23902">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4681.000000 -946.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3791" ObjectName="SW-CX_HS.CX_HS_3011SW"/>
     <cge:Meas_Ref ObjectId="23902"/>
    <cge:TPSR_Ref TObjectID="3791"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4873.000000 -692.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3857" ObjectName="SW-CX_HS.CX_HS_3816SW"/>
     <cge:Meas_Ref ObjectId="24650"/>
    <cge:TPSR_Ref TObjectID="3857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24932">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -572.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3868" ObjectName="SW-CX_HS.CX_HS_3826SW"/>
     <cge:Meas_Ref ObjectId="24932"/>
    <cge:TPSR_Ref TObjectID="3868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4875.000000 -477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 -793.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3845" ObjectName="SW-CX_HS.CX_HS_3121SW"/>
     <cge:Meas_Ref ObjectId="24471"/>
    <cge:TPSR_Ref TObjectID="3845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4682.000000 -687.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3844" ObjectName="SW-CX_HS.CX_HS_3122SW"/>
     <cge:Meas_Ref ObjectId="24470"/>
    <cge:TPSR_Ref TObjectID="3844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24093">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4675.000000 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3807" ObjectName="SW-CX_HS.CX_HS_3022SW"/>
     <cge:Meas_Ref ObjectId="24093"/>
    <cge:TPSR_Ref TObjectID="3807"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23903">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4585.000000 -946.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3792" ObjectName="SW-CX_HS.CX_HS_3016SW"/>
     <cge:Meas_Ref ObjectId="23903"/>
    <cge:TPSR_Ref TObjectID="3792"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24094">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4580.000000 -618.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3808" ObjectName="SW-CX_HS.CX_HS_3026SW"/>
     <cge:Meas_Ref ObjectId="24094"/>
    <cge:TPSR_Ref TObjectID="3808"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -205.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -135.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23737">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4409.000000 -481.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3779" ObjectName="SW-CX_HS.CX_HS_0902SW"/>
     <cge:Meas_Ref ObjectId="23737"/>
    <cge:TPSR_Ref TObjectID="3779"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23734">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3687.000000 -479.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3778" ObjectName="SW-CX_HS.CX_HS_0901SW"/>
     <cge:Meas_Ref ObjectId="23734"/>
    <cge:TPSR_Ref TObjectID="3778"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24956">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -1046.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3870" ObjectName="SW-CX_HS.CX_HS_3731SW"/>
     <cge:Meas_Ref ObjectId="24956"/>
    <cge:TPSR_Ref TObjectID="3870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23721">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -997.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3776" ObjectName="SW-CX_HS.CX_HS_3901SW"/>
     <cge:Meas_Ref ObjectId="23721"/>
    <cge:TPSR_Ref TObjectID="3776"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24488">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -914.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3847" ObjectName="SW-CX_HS.CX_HS_3741SW"/>
     <cge:Meas_Ref ObjectId="24488"/>
    <cge:TPSR_Ref TObjectID="3847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24581">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -861.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3850" ObjectName="SW-CX_HS.CX_HS_3751SW"/>
     <cge:Meas_Ref ObjectId="24581"/>
    <cge:TPSR_Ref TObjectID="3850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24615">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4782.000000 -782.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3853" ObjectName="SW-CX_HS.CX_HS_3761SW"/>
     <cge:Meas_Ref ObjectId="24615"/>
    <cge:TPSR_Ref TObjectID="3853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -692.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3856" ObjectName="SW-CX_HS.CX_HS_3812SW"/>
     <cge:Meas_Ref ObjectId="24649"/>
    <cge:TPSR_Ref TObjectID="3856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-23724">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -640.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3777" ObjectName="SW-CX_HS.CX_HS_3902SW"/>
     <cge:Meas_Ref ObjectId="23724"/>
    <cge:TPSR_Ref TObjectID="3777"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4780.000000 -434.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -477.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4781.000000 -521.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24931">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4783.000000 -572.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="3867" ObjectName="SW-CX_HS.CX_HS_3822SW"/>
     <cge:Meas_Ref ObjectId="24931"/>
    <cge:TPSR_Ref TObjectID="3867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24997">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -0.978261 4612.000000 -326.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10455" ObjectName="SW-CX_HS.CX_HS_0866SW"/>
     <cge:Meas_Ref ObjectId="24997"/>
    <cge:TPSR_Ref TObjectID="10455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-24996">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.928571 -0.000000 0.000000 -0.869565 4613.000000 -491.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10454" ObjectName="SW-CX_HS.CX_HS_0862SW"/>
     <cge:Meas_Ref ObjectId="24996"/>
    <cge:TPSR_Ref TObjectID="10454"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4842.000000 -1075.000000) translate(0,12)">373</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5055.000000 -1047.000000) translate(0,15)">光明化工线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4964.000000 -1011.000000) translate(0,15)">35kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4837.000000 -948.000000) translate(0,12)">374</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4878.000000 -949.000000) translate(0,12)">3746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5065.000000 -938.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5092.500000 -886.000000) translate(0,15)">洪清线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5092.500000 -808.000000) translate(0,15)">洪土线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5083.000000 -718.000000) translate(0,15)">金洪T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5056.500000 -599.000000) translate(0,15)">江达磷化线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4986.000000 -651.000000) translate(0,15)">35kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5042.000000 -532.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5042.000000 -486.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5042.000000 -441.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5045.000000 -1152.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 5045.000000 -1108.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4839.000000 -892.000000) translate(0,12)">375</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4881.000000 -894.000000) translate(0,12)">3756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4839.000000 -813.000000) translate(0,12)">376</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4880.000000 -815.000000) translate(0,12)">3766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4837.000000 -723.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4883.000000 -723.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4839.000000 -603.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4880.000000 -603.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4701.000000 -1183.000000) translate(0,15)">35kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4700.000000 -412.000000) translate(0,15)">35kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3439.000000 -534.000000) translate(0,15)">10kVIM段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4624.000000 -563.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4645.000000 -977.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4687.000000 -979.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4591.000000 -979.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4639.000000 -649.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4680.000000 -651.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4585.000000 -651.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4678.000000 -753.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4688.000000 -826.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4688.000000 -720.000000) translate(0,12)">3122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3787.000000 -735.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3919.000000 -734.000000) translate(0,12)">3020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3608.000000 -627.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3606.000000 -578.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3606.000000 -671.000000) translate(0,12)">0016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4003.000000 -638.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -587.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4076.000000 -588.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4233.000000 -627.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4231.000000 -578.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4231.000000 -671.000000) translate(0,12)">0026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3677.000000 -1188.000000) translate(0,15)">禄</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3677.000000 -1188.000000) translate(0,33)">洪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3677.000000 -1188.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -1203.000000) translate(0,15)">腰</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -1203.000000) translate(0,33)">洪</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4064.000000 -1203.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3730.000000 -1024.000000) translate(0,12)">171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3728.000000 -968.000000) translate(0,12)">1711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -1008.000000) translate(0,12)">17117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -1065.000000) translate(0,12)">17160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -1098.000000) translate(0,12)">17167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3731.000000 -1081.000000) translate(0,12)">1716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3607.000000 -869.000000) translate(0,12)">1011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3616.000000 -850.000000) translate(0,12)">10117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3502.000000 -878.000000) translate(0,12)">1010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3574.000000 -1057.000000) translate(0,12)">19017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3580.000000 -976.000000) translate(0,12)">19010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3567.000000 -1001.000000) translate(0,12)">1901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3854.000000 -875.000000) translate(0,12)">112</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3813.000000 -891.000000) translate(0,12)">1121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3813.000000 -825.000000) translate(0,12)">11217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3934.000000 -893.000000) translate(0,12)">1122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3934.000000 -827.000000) translate(0,12)">11227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3958.000000 -1008.000000) translate(0,12)">1902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3967.000000 -986.000000) translate(0,12)">19020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3965.000000 -1064.000000) translate(0,12)">19027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -1034.000000) translate(0,12)">172</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4118.000000 -974.000000) translate(0,12)">1722</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4118.000000 -1090.000000) translate(0,12)">1726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4121.000000 -1018.000000) translate(0,12)">17227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -1075.000000) translate(0,12)">17260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4117.000000 -1106.000000) translate(0,12)">17267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4236.000000 -887.000000) translate(0,12)">1022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4243.000000 -870.000000) translate(0,12)">10227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4339.000000 -876.000000) translate(0,12)">1020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3554.000000 -460.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3619.000000 -460.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,15)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,33)">业</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,51)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,69)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,87)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3558.000000 -357.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -322.000000) translate(0,15)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -322.000000) translate(0,33)">材</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -322.000000) translate(0,51)">I</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -322.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3623.000000 -322.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3617.000000 -511.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3617.000000 -353.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3707.000000 -510.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3789.000000 -461.000000) translate(0,12)">075</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3787.000000 -354.000000) translate(0,12)">0756</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 -291.000000) translate(0,15)">10kVI段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3661.000000 -291.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3734.000000 -245.000000) translate(0,15)">土官线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3857.000000 -461.000000) translate(0,12)">076</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3855.000000 -512.000000) translate(0,12)">0761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3855.000000 -354.000000) translate(0,12)">0766</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3873.000000 -341.000000) translate(0,12)">07667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3961.000000 -461.000000) translate(0,12)">077</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3959.000000 -512.000000) translate(0,12)">0771</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3959.000000 -354.000000) translate(0,12)">0776</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3978.000000 -340.000000) translate(0,12)">07767</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4091.000000 -510.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4170.000000 -461.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -512.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4168.000000 -354.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4186.000000 -340.000000) translate(0,12)">08267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4286.000000 -461.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4284.000000 -512.000000) translate(0,12)">0832</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4284.000000 -354.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4301.000000 -341.000000) translate(0,12)">08367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4501.000000 -462.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4499.000000 -513.000000) translate(0,12)">0842</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4499.000000 -355.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4574.000000 -463.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4572.000000 -514.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4631.000000 -463.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4379.000000 -319.000000) translate(0,15)">10kVII段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4379.000000 -319.000000) translate(0,33)">母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4441.000000 -251.000000) translate(0,15)">指挥营线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4545.000000 -301.000000) translate(0,15)">钛</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4545.000000 -301.000000) translate(0,33)">材</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4545.000000 -301.000000) translate(0,51)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4545.000000 -301.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4545.000000 -301.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,15)">工</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,33)">业</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,51)">园</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,69)">区</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,87)">II</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,105)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4633.000000 -326.000000) translate(0,123)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4779.000000 -102.000000) translate(0,15)">10kVII段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4923.000000 -102.000000) translate(0,15)">10kVI段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="34" transform="matrix(1.000000 0.000000 0.000000 1.000000 4798.000000 -64.000000) translate(0,28)">10kV云钛1号配电室</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3522.000000 -768.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4254.000000 -799.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -441.000000) translate(0,15)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -441.000000) translate(0,33)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -441.000000) translate(0,51)">站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -441.000000) translate(0,69)">用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4045.000000 -441.000000) translate(0,87)">变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4572.000000 -356.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3033.000000 -1034.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" transform="matrix(1.000000 0.000000 0.000000 1.000000 3034.000000 -596.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3176.500000 -1174.500000) translate(0,16)">洪山变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3501.000000 -355.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -356.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3490.000000 -1122.000000) translate(0,15)">110kVI段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 4504.000000 -29.000000) translate(0,15)">工业园区1号公用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4885.000000 -1073.000000) translate(0,12)">3736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3980.000000 -658.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4583.000000 -754.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3825.000000 -913.000000) translate(0,12)">Ia(A):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3885.000000 -1129.000000) translate(0,15)">110kVII段母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3787.000000 -512.000000) translate(0,12)">0751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3548.000000 -511.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4632.000000 -514.000000) translate(0,12)">0862</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4425.000000 -511.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4788.000000 -1077.000000) translate(0,12)">3731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4789.000000 -945.000000) translate(0,12)">3741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4787.000000 -1028.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4789.000000 -892.000000) translate(0,12)">3751</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4789.000000 -813.000000) translate(0,12)">3761</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4787.000000 -723.000000) translate(0,12)">3812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4786.000000 -671.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4789.000000 -603.000000) translate(0,12)">3822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,15)">1号、2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,33)">SFSZ11-50000/110GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,51)">50MVA,100/100/100</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,69)">YN,yn0,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,87)">110±8×1.25%/38.5±2×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,105)">Ud1-2=10.5%,Ud1-3=17.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" transform="matrix(1.000000 0.000000 0.000000 1.000000 3281.000000 -787.000000) translate(0,123)">Ud2-3=6.5%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,42)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3977.000000 -282.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,42)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4185.000000 -278.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,42)">4</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4300.000000 -276.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,12)">10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,27)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,42)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,57)">号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,72)">电</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,87)">容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,102)">器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 3869.000000 -284.000000) translate(0,117)">组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4629.000000 -356.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" transform="matrix(1.000000 0.000000 0.000000 1.000000 3310.000000 -1175.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4728.000000 -280.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4944.000000 -286.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4807.000000 -194.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4881.000000 -193.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 4938.000000 -194.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" transform="matrix(1.000000 0.000000 0.000000 1.000000 5029.000000 -192.000000) translate(0,12)">061</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_28db9d0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3665.000000 -819.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28df680">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3625.000000 -947.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e3a00">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3624.000000 -1025.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28e8540">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3781.000000 -982.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28ec9b0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3781.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f21d0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3781.000000 -1095.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28f8da0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4290.000000 -841.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_28fcff0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4167.000000 -1105.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2900220">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4167.000000 -1045.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2903680">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4167.000000 -992.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2908de0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4017.000000 -954.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_290e710">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4015.000000 -1032.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291b6a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3800.000000 -768.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_291c0d0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3921.000000 -771.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29206a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3512.000000 -840.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2951230">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4017.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a42190">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4343.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4cec0">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 4227.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a58c00">
    <use class="BV-0KV" transform="matrix(1.047619 -0.000000 0.000000 -1.000000 3913.000000 -310.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a7e950">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 -295.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a8eb50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4994.000000 -302.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a92350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4346.000000 -839.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" stationName="CX_HS"/>
</svg>