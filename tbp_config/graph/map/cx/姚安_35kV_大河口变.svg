<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-329" aopId="4063750" id="thSvg" product="E8000V2" version="1.0" viewBox="-197 -1064 1811 1335">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="29" x2="29" y1="25" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="51" x2="51" y1="43" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="55" x2="55" y1="34" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="58" x2="58" y1="35" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="35" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="35" x2="35" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="42" x2="42" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="16" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="16" x2="16" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="23" x2="23" y1="31" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="7" y2="4"/>
    <circle cx="41" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="17" cy="17" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="26" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="20" y1="9" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="26" y1="9" y2="5"/>
    <circle cx="35" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="42" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="42" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="30" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="30" y1="18" y2="15"/>
    <circle cx="29" cy="17" r="7.5" stroke-width="1"/>
    <circle cx="23" cy="7" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="20" x2="17" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="17" y1="18" y2="15"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape178">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="131" y2="123"/>
    <rect height="4" stroke-width="1" width="19" x="11" y="107"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="9,120 31,98 31,86 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="28" y1="122" y2="122"/>
    <circle cx="30" cy="71" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="27" x2="31" y1="71" y2="77"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="27" y1="71" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="77" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="32" y1="43" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="18" y2="18"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,49 6,49 6,20 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,6 26,19 39,19 32,6 32,7 32,6 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="34" y2="0"/>
    <circle cx="30" cy="49" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="29" y1="49" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="49" y2="54"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape132">
    <rect height="16" stroke-width="1" width="31" x="5" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="36" y1="9" y2="9"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape8_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="18" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="9" x2="0" y1="18" y2="43"/>
   </symbol>
   <symbol id="switch2:shape8-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="2" x2="16" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.313725" x1="8" x2="10" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="44" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="9" x2="9" y1="51" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="7" x2="10" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="6" x2="11" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape26_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="10" stroke-width="0.416609" width="28" x="23" y="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="23" x2="49" y1="30" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="48" x2="18" y1="5" y2="34"/>
    <rect height="9" stroke-width="0.416609" width="29" x="21" y="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
   </symbol>
   <symbol id="switch2:shape26-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="12" x2="48" y1="5" y2="5"/>
    <rect height="14" stroke-width="0.416609" width="26" x="19" y="-2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="13" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="13" x2="13" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="57" x2="48" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape27_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape27-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape7_0">
    <circle cx="36" cy="30" r="26" stroke-width="1"/>
    <polyline points="35,44 22,20 48,20 35,44 " stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape7_1">
    <circle cx="36" cy="71" r="26" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="49" x2="35" y1="64" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="22" y1="72" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="88" y2="72"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="68" x2="63" y1="86" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="59" x2="68" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="68" y1="58" y2="87"/>
   </symbol>
   <symbol id="voltageTransformer:shape92">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.992459" x1="12" x2="13" y1="34" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="16" y2="19"/>
    <circle cx="23" cy="16" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="23" cy="30" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="15" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="27" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="31" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="24" y1="31" y2="27"/>
    <circle cx="9" cy="16" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="9" cy="30" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="12" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.960801" x1="6" x2="13" y1="29" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.989747" x1="7" x2="6" y1="34" y2="30"/>
    <polyline points="32,31 43,31 43,27 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="48" y1="24" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="43" x2="43" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="39" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="40" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="41" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="47" x2="38" y1="6" y2="6"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_331cba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31d9730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31da110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_31dae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32d0620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_32d0f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_32d16c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32d1ef0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32d3290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_32d3290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3122500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3122500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3123eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3123eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3124cf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31265a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d7f4e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d80110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d807e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d81d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d831d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_312da90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_312e1a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_312f0c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_312f980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_31303b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3130cb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3131730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3307470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33082a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3308db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_34126f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3413090" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_330b080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_31e2200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1345" width="1821" x="-202" y="-1069"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -1.000000 -0.000000)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29d45e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1198.000000 664.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b390f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1187.000000 649.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bc53b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1212.000000 634.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c14350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1199.000000 378.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2c144c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1188.000000 363.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2be6cc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1213.000000 348.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf9df0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.000000 -44.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfa0e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.000000 -59.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfa290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 581.000000 -74.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 858.000000 -138.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 847.000000 -153.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6c920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 872.000000 -168.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6cd40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -140.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.000000 -155.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6d240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1038.000000 -170.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b54fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1247.000000 -140.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b55270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1236.000000 -155.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b554b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1261.000000 -170.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b76180" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 335.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b76be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 415.000000 305.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b772c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 349.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 320.000000) translate(0,12)">3U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b77740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 423.000000 364.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b50c70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.000000 -135.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b50ec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 652.000000 -150.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b510d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.000000 -165.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 521.000000) translate(0,12)">档位(档):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b52cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1179.000000 505.000000) translate(0,12)">温度(℃):</text>
   <metadata/></g>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="583,-12 578,-14 567,-2 561,-6 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="580,0 580,-2 572,-7 571,-6 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-306520">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -661.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47543" ObjectName="SW-YA_DHK.YA_DHK_3016SW"/>
     <cge:Meas_Ref ObjectId="306520"/>
    <cge:TPSR_Ref TObjectID="47543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -358.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47548" ObjectName="SW-YA_DHK.YA_DHK_001XC1"/>
     <cge:Meas_Ref ObjectId="306563"/>
    <cge:TPSR_Ref TObjectID="47548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -280.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47547" ObjectName="SW-YA_DHK.YA_DHK_001XC"/>
     <cge:Meas_Ref ObjectId="306563"/>
    <cge:TPSR_Ref TObjectID="47547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -200.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47550" ObjectName="SW-YA_DHK.YA_DHK_062XC"/>
     <cge:Meas_Ref ObjectId="306606"/>
    <cge:TPSR_Ref TObjectID="47550"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306606">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 711.000000 -114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47551" ObjectName="SW-YA_DHK.YA_DHK_062XC1"/>
     <cge:Meas_Ref ObjectId="306606"/>
    <cge:TPSR_Ref TObjectID="47551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306608">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.000000 -40.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47552" ObjectName="SW-YA_DHK.YA_DHK_06267SW"/>
     <cge:Meas_Ref ObjectId="306608"/>
    <cge:TPSR_Ref TObjectID="47552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306526">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1038.000000 -639.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47544" ObjectName="SW-YA_DHK.YA_DHK_30160SW"/>
     <cge:Meas_Ref ObjectId="306526"/>
    <cge:TPSR_Ref TObjectID="47544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306527">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1143.000000 -719.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47545" ObjectName="SW-YA_DHK.YA_DHK_30167SW"/>
     <cge:Meas_Ref ObjectId="306527"/>
    <cge:TPSR_Ref TObjectID="47545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 -200.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47554" ObjectName="SW-YA_DHK.YA_DHK_063XC"/>
     <cge:Meas_Ref ObjectId="306644"/>
    <cge:TPSR_Ref TObjectID="47554"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306644">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 899.000000 -114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47555" ObjectName="SW-YA_DHK.YA_DHK_063XC1"/>
     <cge:Meas_Ref ObjectId="306644"/>
    <cge:TPSR_Ref TObjectID="47555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306646">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 948.000000 -40.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47556" ObjectName="SW-YA_DHK.YA_DHK_06367SW"/>
     <cge:Meas_Ref ObjectId="306646"/>
    <cge:TPSR_Ref TObjectID="47556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -197.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47558" ObjectName="SW-YA_DHK.YA_DHK_064XC"/>
     <cge:Meas_Ref ObjectId="306682"/>
    <cge:TPSR_Ref TObjectID="47558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306682">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1068.000000 -111.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47559" ObjectName="SW-YA_DHK.YA_DHK_064XC1"/>
     <cge:Meas_Ref ObjectId="306682"/>
    <cge:TPSR_Ref TObjectID="47559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306684">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1117.000000 -37.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47560" ObjectName="SW-YA_DHK.YA_DHK_06467SW"/>
     <cge:Meas_Ref ObjectId="306684"/>
    <cge:TPSR_Ref TObjectID="47560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 -200.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47562" ObjectName="SW-YA_DHK.YA_DHK_065XC"/>
     <cge:Meas_Ref ObjectId="306720"/>
    <cge:TPSR_Ref TObjectID="47562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306720">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1278.000000 -114.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47563" ObjectName="SW-YA_DHK.YA_DHK_065XC1"/>
     <cge:Meas_Ref ObjectId="306720"/>
    <cge:TPSR_Ref TObjectID="47563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306722">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1327.000000 -40.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47564" ObjectName="SW-YA_DHK.YA_DHK_06567SW"/>
     <cge:Meas_Ref ObjectId="306722"/>
    <cge:TPSR_Ref TObjectID="47564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1329.000000 54.000000)" xlink:href="#switch2:shape26_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -201.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47566" ObjectName="SW-YA_DHK.YA_DHK_061XC"/>
     <cge:Meas_Ref ObjectId="306758"/>
    <cge:TPSR_Ref TObjectID="47566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306758">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 562.000000 -115.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47567" ObjectName="SW-YA_DHK.YA_DHK_061XC1"/>
     <cge:Meas_Ref ObjectId="306758"/>
    <cge:TPSR_Ref TObjectID="47567"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306760">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 611.000000 -41.000000)" xlink:href="#switch2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47568" ObjectName="SW-YA_DHK.YA_DHK_06167SW"/>
     <cge:Meas_Ref ObjectId="306760"/>
    <cge:TPSR_Ref TObjectID="47568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-315142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 530.000000 43.000000)" xlink:href="#switch2:shape27_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48651" ObjectName="SW-YA_DHK.YA_DHK_0030SW"/>
     <cge:Meas_Ref ObjectId="315142"/>
    <cge:TPSR_Ref TObjectID="48651"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306787">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 442.000000 -161.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47570" ObjectName="SW-YA_DHK.YA_DHK_0901XC1"/>
     <cge:Meas_Ref ObjectId="306787"/>
    <cge:TPSR_Ref TObjectID="47570"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b71ed0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 428.000000 -7.000000)" xlink:href="#voltageTransformer:shape92"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YA_LC" endPointId="0" endStationName="YA_DHK" flowDrawDirect="1" flowShape="0" id="AC-35kV.lianhe_line" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1127,-961 1127,-1022 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47613" ObjectName="AC-35kV.lianhe_line"/>
    <cge:TPSR_Ref TObjectID="47613_SS-329"/></metadata>
   <polyline fill="none" opacity="0" points="1127,-961 1127,-1022 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_DHK.YA_DHK_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="45208"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -482.000000)" xlink:href="#transformer2:shape7_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1092.000000 -482.000000)" xlink:href="#transformer2:shape7_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47571" ObjectName="TF-YA_DHK.YA_DHK_1T"/>
    <cge:TPSR_Ref TObjectID="47571"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2c27570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.371429 -22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bba060">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 677.371429 -40.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bfa5f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1123.371429 -404.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b59f80">
    <use class="BV-10KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1042.000000 -400.628571)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b80980">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1025.000000 -678.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ba9a50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.371429 -869.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af4a50">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.371429 -22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b5c880">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 865.371429 -40.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b16f20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.371429 -19.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b8a400">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1034.371429 -37.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b62430">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.371429 -22.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ad3160">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1244.371429 -40.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b578d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 567.371429 -23.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2bc8ec0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 528.371429 -41.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b67c30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.000000 123.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b6f8f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 402.371429 -86.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b70620">
    <use class="BV-10KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 461.500000 -92.500000)" xlink:href="#lightningRod:shape132"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b024c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.371429 -417.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b06ec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1449.000000 223.000000)" xlink:href="#lightningRod:shape178"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b196b0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1468.500000 54.128571)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -69.000000 -994.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-306425" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -892.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306425" ObjectName="YA_DHK:YA_DHK_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-306426" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -848.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306426" ObjectName="YA_DHK:YA_DHK_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-306425" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -801.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306425" ObjectName="YA_DHK:YA_DHK_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-306426" ratioFlag="0">
    <text fill="rgb(0,238,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -42.000000 -763.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306426" ObjectName="YA_DHK:YA_DHK_301BK_Q"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-306474" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -363.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306474" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47540"/>
     <cge:Term_Ref ObjectID="45145"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-306475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -363.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306475" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47540"/>
     <cge:Term_Ref ObjectID="45145"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-306476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -363.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47540"/>
     <cge:Term_Ref ObjectID="45145"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-306480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -363.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47540"/>
     <cge:Term_Ref ObjectID="45145"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-306477" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 511.000000 -363.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306477" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47540"/>
     <cge:Term_Ref ObjectID="45145"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306425" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1252.000000 -665.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306425" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47542"/>
     <cge:Term_Ref ObjectID="45148"/>
    <cge:TPSR_Ref TObjectID="47542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306426" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1252.000000 -665.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306426" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47542"/>
     <cge:Term_Ref ObjectID="45148"/>
    <cge:TPSR_Ref TObjectID="47542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1252.000000 -665.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306422" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47542"/>
     <cge:Term_Ref ObjectID="45148"/>
    <cge:TPSR_Ref TObjectID="47542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306434" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -378.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306434" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47546"/>
     <cge:Term_Ref ObjectID="45156"/>
    <cge:TPSR_Ref TObjectID="47546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306435" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -378.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306435" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47546"/>
     <cge:Term_Ref ObjectID="45156"/>
    <cge:TPSR_Ref TObjectID="47546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -378.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47546"/>
     <cge:Term_Ref ObjectID="45156"/>
    <cge:TPSR_Ref TObjectID="47546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306470" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 622.000000 45.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306470" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47565"/>
     <cge:Term_Ref ObjectID="45194"/>
    <cge:TPSR_Ref TObjectID="47565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306471" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 622.000000 45.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306471" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47565"/>
     <cge:Term_Ref ObjectID="45194"/>
    <cge:TPSR_Ref TObjectID="47565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 622.000000 45.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306467" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47565"/>
     <cge:Term_Ref ObjectID="45194"/>
    <cge:TPSR_Ref TObjectID="47565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306442" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 137.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306442" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47549"/>
     <cge:Term_Ref ObjectID="45162"/>
    <cge:TPSR_Ref TObjectID="47549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306443" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 137.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306443" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47549"/>
     <cge:Term_Ref ObjectID="45162"/>
    <cge:TPSR_Ref TObjectID="47549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 720.000000 137.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306439" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47549"/>
     <cge:Term_Ref ObjectID="45162"/>
    <cge:TPSR_Ref TObjectID="47549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 139.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306449" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47553"/>
     <cge:Term_Ref ObjectID="45170"/>
    <cge:TPSR_Ref TObjectID="47553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306450" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 139.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306450" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47553"/>
     <cge:Term_Ref ObjectID="45170"/>
    <cge:TPSR_Ref TObjectID="47553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306446" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 912.000000 139.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306446" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47553"/>
     <cge:Term_Ref ObjectID="45170"/>
    <cge:TPSR_Ref TObjectID="47553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306456" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 143.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306456" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47557"/>
     <cge:Term_Ref ObjectID="45178"/>
    <cge:TPSR_Ref TObjectID="47557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 143.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306457" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47557"/>
     <cge:Term_Ref ObjectID="45178"/>
    <cge:TPSR_Ref TObjectID="47557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306453" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1081.000000 143.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306453" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47557"/>
     <cge:Term_Ref ObjectID="45178"/>
    <cge:TPSR_Ref TObjectID="47557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-306463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 142.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306463" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47561"/>
     <cge:Term_Ref ObjectID="45186"/>
    <cge:TPSR_Ref TObjectID="47561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-306464" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 142.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306464" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47561"/>
     <cge:Term_Ref ObjectID="45186"/>
    <cge:TPSR_Ref TObjectID="47561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-306460" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1306.000000 142.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306460" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47561"/>
     <cge:Term_Ref ObjectID="45186"/>
    <cge:TPSR_Ref TObjectID="47561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-306437" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -519.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306437" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47571"/>
     <cge:Term_Ref ObjectID="45206"/>
    <cge:TPSR_Ref TObjectID="47571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-306438" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -519.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="306438" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47571"/>
     <cge:Term_Ref ObjectID="45206"/>
    <cge:TPSR_Ref TObjectID="47571"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="176" y="-1024"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="176" y="-1024"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="176" y="-1063"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="176" y="-1063"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="111,-1052 108,-1055 108,-1000 111,-1003 111,-1052" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="111,-1052 108,-1055 167,-1055 164,-1052 111,-1052" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="111,-1003 108,-1000 167,-1000 164,-1003 111,-1003" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="164,-1052 167,-1055 167,-1000 164,-1003 164,-1052" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="49" stroke="rgb(255,255,255)" width="53" x="111" y="-1052"/>
     <rect fill="none" height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="111" y="-1052"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1168" y="-560"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1168" y="-560"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="582" y="-178"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="582" y="-178"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="731" y="-177"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="731" y="-177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="919" y="-177"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="919" y="-177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1095" y="-175"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1095" y="-175"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1298" y="-177"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1298" y="-177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="69" x="-101" y="-670"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="69" x="-101" y="-670"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="176" y="-1024"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="176" y="-1063"/></g>
   <g href="AVC大河口站.svg" style="fill-opacity:0"><rect height="49" qtmmishow="hidden" stroke="rgb(0,0,0)" width="53" x="111" y="-1052"/></g>
   <g href="35kV大河口变35kV1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1168" y="-560"/></g>
   <g href="35kV大河口变10kV接地变061间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="582" y="-178"/></g>
   <g href="35kV大河口变10kV麂子线062间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="731" y="-177"/></g>
   <g href="35kV大河口变10kV水洼线063间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="919" y="-177"/></g>
   <g href="35kV大河口变10kV葡萄线064间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1095" y="-175"/></g>
   <g href="35kV大河口变10kV涟水线065间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1298" y="-177"/></g>
   <g href="35kV大河口变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="69" x="-101" y="-670"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1275" x2="1275" y1="35" y2="57"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1299" x2="1299" y1="35" y2="57"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1275" x2="1299" y1="57" y2="57"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1275" x2="1299" y1="35" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1299" x2="1276" y1="35" y2="57"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1299" x2="1299" y1="56" y2="56"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="1299" x2="1276" y1="58" y2="35"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="552" x2="546" y1="187" y2="197"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,72,216)" stroke-width="1" x1="559" x2="552" y1="187" y2="198"/>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2ba74f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 -0.000000 -1.000000 1156.000000 -819.000000)" xlink:href="#currentTransformer:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_DHK.062Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 716.371429 100.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47609" ObjectName="EC-YA_DHK.062Ld"/>
    <cge:TPSR_Ref TObjectID="47609"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DHK.063Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 904.371429 100.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47610" ObjectName="EC-YA_DHK.063Ld"/>
    <cge:TPSR_Ref TObjectID="47610"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DHK.064Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1073.371429 103.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47611" ObjectName="EC-YA_DHK.064Ld"/>
    <cge:TPSR_Ref TObjectID="47611"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_DHK.065Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1283.371429 100.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47612" ObjectName="EC-YA_DHK.065Ld"/>
    <cge:TPSR_Ref TObjectID="47612"/></metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_2bc0270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-321 1128,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47546@0" ObjectIDZND0="47547@1" Pin0InfoVect0LinkObjId="SW-306563_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306562_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-321 1128,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2be7910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-365 1128,-348 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47548@1" ObjectIDZND0="47546@1" Pin0InfoVect0LinkObjId="SW-306562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306563_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-365 1128,-348 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7d680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-224 721,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47550@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2a4f250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306606_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-224 721,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b7d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-183 721,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47549@1" ObjectIDZND0="47550@1" Pin0InfoVect0LinkObjId="SW-306606_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306604_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-183 721,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bb9e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-136 721,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47551@1" ObjectIDZND0="47549@0" Pin0InfoVect0LinkObjId="SW-306604_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-136 721,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bba9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="684,-94 721,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2bba060@0" ObjectIDZND0="g_2c27570@0" ObjectIDZND1="47551@x" ObjectIDZND2="47552@x" Pin0InfoVect0LinkObjId="g_2c27570_0" Pin0InfoVect1LinkObjId="SW-306606_0" Pin0InfoVect2LinkObjId="SW-306608_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bba060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="684,-94 721,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bbabe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-80 721,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2c27570@0" ObjectIDZND0="g_2bba060@0" ObjectIDZND1="47551@x" ObjectIDZND2="47552@x" Pin0InfoVect0LinkObjId="g_2bba060_0" Pin0InfoVect1LinkObjId="SW-306606_0" Pin0InfoVect2LinkObjId="SW-306608_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c27570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="721,-80 721,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf8980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-94 721,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2bba060@0" ObjectIDND1="g_2c27570@0" ObjectIDZND0="47551@0" ObjectIDZND1="47552@x" Pin0InfoVect0LinkObjId="SW-306606_0" Pin0InfoVect1LinkObjId="SW-306608_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2bba060_0" Pin1InfoVect1LinkObjId="g_2c27570_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="721,-94 721,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf8b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-103 721,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2bba060@0" ObjectIDND1="g_2c27570@0" ObjectIDND2="47552@x" ObjectIDZND0="47551@0" Pin0InfoVect0LinkObjId="SW-306606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2bba060_0" Pin1InfoVect1LinkObjId="g_2c27570_0" Pin1InfoVect2LinkObjId="SW-306608_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-103 721,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bf8d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="769,-91 769,-103 721,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47552@0" ObjectIDZND0="g_2bba060@0" ObjectIDZND1="g_2c27570@0" ObjectIDZND2="47551@0" Pin0InfoVect0LinkObjId="g_2bba060_0" Pin0InfoVect1LinkObjId="g_2c27570_0" Pin0InfoVect2LinkObjId="SW-306606_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="769,-91 769,-103 721,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4f250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-287 1128,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47547@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-287 1128,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4f8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="721,-27 721,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2c27570@1" ObjectIDZND0="47609@0" Pin0InfoVect0LinkObjId="EC-YA_DHK.062Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2c27570_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="721,-27 721,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4fad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-224 909,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47554@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306644_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-224 909,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a4fcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-183 909,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47553@1" ObjectIDZND0="47554@1" Pin0InfoVect0LinkObjId="SW-306644_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306642_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-183 909,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b468b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-136 909,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47555@1" ObjectIDZND0="47553@0" Pin0InfoVect0LinkObjId="SW-306642_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-136 909,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5d3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="872,-94 909,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b5c880@0" ObjectIDZND0="g_2af4a50@0" ObjectIDZND1="47555@x" ObjectIDZND2="47556@x" Pin0InfoVect0LinkObjId="g_2af4a50_0" Pin0InfoVect1LinkObjId="SW-306644_0" Pin0InfoVect2LinkObjId="SW-306646_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b5c880_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="872,-94 909,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b5d610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-80 909,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2af4a50@0" ObjectIDZND0="g_2b5c880@0" ObjectIDZND1="47555@x" ObjectIDZND2="47556@x" Pin0InfoVect0LinkObjId="g_2b5c880_0" Pin0InfoVect1LinkObjId="SW-306644_0" Pin0InfoVect2LinkObjId="SW-306646_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af4a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="909,-80 909,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdc240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-94 909,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2af4a50@0" ObjectIDND1="g_2b5c880@0" ObjectIDZND0="47555@x" ObjectIDZND1="47556@0" Pin0InfoVect0LinkObjId="SW-306644_0" Pin0InfoVect1LinkObjId="SW-306646_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af4a50_0" Pin1InfoVect1LinkObjId="g_2b5c880_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="909,-94 909,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdc430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-103 909,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2af4a50@0" ObjectIDND1="g_2b5c880@0" ObjectIDND2="47556@0" ObjectIDZND0="47555@0" Pin0InfoVect0LinkObjId="SW-306644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2af4a50_0" Pin1InfoVect1LinkObjId="g_2b5c880_0" Pin1InfoVect2LinkObjId="SW-306646_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-103 909,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdc620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="957,-91 957,-103 909,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47556@0" ObjectIDZND0="47555@x" ObjectIDZND1="g_2af4a50@0" ObjectIDZND2="g_2b5c880@0" Pin0InfoVect0LinkObjId="SW-306644_0" Pin0InfoVect1LinkObjId="g_2af4a50_0" Pin0InfoVect2LinkObjId="g_2b5c880_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="957,-91 957,-103 909,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bdcac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="909,-27 909,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2af4a50@1" ObjectIDZND0="47610@0" Pin0InfoVect0LinkObjId="EC-YA_DHK.063Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af4a50_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="909,-27 909,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bde070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-221 1078,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47558@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306682_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-221 1078,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bde290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-180 1078,-204 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47557@1" ObjectIDZND0="47558@1" Pin0InfoVect0LinkObjId="SW-306682_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306680_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-180 1078,-204 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b8a1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-133 1078,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47559@1" ObjectIDZND0="47557@0" Pin0InfoVect0LinkObjId="SW-306680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306682_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-133 1078,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b1fed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1041,-91 1078,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b8a400@0" ObjectIDZND0="g_2b16f20@0" ObjectIDZND1="47559@x" ObjectIDZND2="47560@x" Pin0InfoVect0LinkObjId="g_2b16f20_0" Pin0InfoVect1LinkObjId="SW-306682_0" Pin0InfoVect2LinkObjId="SW-306684_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b8a400_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1041,-91 1078,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b200f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-77 1078,-91 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b16f20@0" ObjectIDZND0="g_2b8a400@0" ObjectIDZND1="47559@x" ObjectIDZND2="47560@x" Pin0InfoVect0LinkObjId="g_2b8a400_0" Pin0InfoVect1LinkObjId="SW-306682_0" Pin0InfoVect2LinkObjId="SW-306684_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b16f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-77 1078,-91 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b222a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-91 1078,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b16f20@0" ObjectIDND1="g_2b8a400@0" ObjectIDZND0="47559@x" ObjectIDZND1="47560@x" Pin0InfoVect0LinkObjId="SW-306682_0" Pin0InfoVect1LinkObjId="SW-306684_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b16f20_0" Pin1InfoVect1LinkObjId="g_2b8a400_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-91 1078,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b22490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-100 1078,-118 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b16f20@0" ObjectIDND1="g_2b8a400@0" ObjectIDND2="47560@x" ObjectIDZND0="47559@0" Pin0InfoVect0LinkObjId="SW-306682_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b16f20_0" Pin1InfoVect1LinkObjId="g_2b8a400_0" Pin1InfoVect2LinkObjId="SW-306684_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-100 1078,-118 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b22680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1126,-88 1126,-100 1078,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47560@0" ObjectIDZND0="47559@x" ObjectIDZND1="g_2b16f20@0" ObjectIDZND2="g_2b8a400@0" Pin0InfoVect0LinkObjId="SW-306682_0" Pin0InfoVect1LinkObjId="g_2b16f20_0" Pin0InfoVect2LinkObjId="g_2b8a400_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306684_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1126,-88 1126,-100 1078,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b22c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1078,-24 1078,82 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2b16f20@1" ObjectIDZND0="47611@0" Pin0InfoVect0LinkObjId="EC-YA_DHK.064Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b16f20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1078,-24 1078,82 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b61330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-224 1288,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47562@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-224 1288,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b61550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-183 1288,-207 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47561@1" ObjectIDZND0="47562@1" Pin0InfoVect0LinkObjId="SW-306720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306718_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-183 1288,-207 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad2f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-136 1288,-156 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47563@1" ObjectIDZND0="47561@0" Pin0InfoVect0LinkObjId="SW-306718_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-136 1288,-156 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad3e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1251,-94 1288,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ad3160@0" ObjectIDZND0="g_2b62430@0" ObjectIDZND1="47563@x" ObjectIDZND2="47564@x" Pin0InfoVect0LinkObjId="g_2b62430_0" Pin0InfoVect1LinkObjId="SW-306720_0" Pin0InfoVect2LinkObjId="SW-306722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad3160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1251,-94 1288,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad40f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-80 1288,-94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b62430@0" ObjectIDZND0="g_2ad3160@0" ObjectIDZND1="47563@x" ObjectIDZND2="47564@x" Pin0InfoVect0LinkObjId="g_2ad3160_0" Pin0InfoVect1LinkObjId="SW-306720_0" Pin0InfoVect2LinkObjId="SW-306722_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b62430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-80 1288,-94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b859a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-94 1288,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b62430@0" ObjectIDND1="g_2ad3160@0" ObjectIDZND0="47563@x" ObjectIDZND1="47564@x" Pin0InfoVect0LinkObjId="SW-306720_0" Pin0InfoVect1LinkObjId="SW-306722_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b62430_0" Pin1InfoVect1LinkObjId="g_2ad3160_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-94 1288,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b85b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-103 1288,-121 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b62430@0" ObjectIDND1="g_2ad3160@0" ObjectIDND2="47564@x" ObjectIDZND0="47563@0" Pin0InfoVect0LinkObjId="SW-306720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b62430_0" Pin1InfoVect1LinkObjId="g_2ad3160_0" Pin1InfoVect2LinkObjId="SW-306722_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-103 1288,-121 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b85d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1336,-91 1336,-103 1288,-103 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47564@0" ObjectIDZND0="47563@x" ObjectIDZND1="g_2b62430@0" ObjectIDZND2="g_2ad3160@0" Pin0InfoVect0LinkObjId="SW-306720_0" Pin0InfoVect1LinkObjId="g_2b62430_0" Pin0InfoVect2LinkObjId="g_2ad3160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306722_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1336,-91 1336,-103 1288,-103 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b69d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,48 1334,48 1334,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_2b62430@0" ObjectIDND1="47612@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b62430_0" Pin1InfoVect1LinkObjId="EC-YA_DHK.065Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,48 1334,48 1334,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,-27 1288,48 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_2b62430@1" ObjectIDZND0="0@x" ObjectIDZND1="47612@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="EC-YA_DHK.065Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b62430_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1288,-27 1288,48 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6aa90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1288,48 1288,79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="0@x" ObjectIDND1="g_2b62430@0" ObjectIDZND0="47612@0" Pin0InfoVect0LinkObjId="EC-YA_DHK.065Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2b62430_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1288,48 1288,79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b556f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-225 572,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47566@0" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306758_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-225 572,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b558e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-184 572,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47565@1" ObjectIDZND0="47566@1" Pin0InfoVect0LinkObjId="SW-306758_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306756_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-184 572,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc8c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-137 572,-157 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47567@1" ObjectIDZND0="47565@0" Pin0InfoVect0LinkObjId="SW-306756_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306758_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-137 572,-157 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="535,-95 572,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2bc8ec0@0" ObjectIDZND0="g_2b578d0@0" ObjectIDZND1="47567@x" ObjectIDZND2="47568@x" Pin0InfoVect0LinkObjId="g_2b578d0_0" Pin0InfoVect1LinkObjId="SW-306758_0" Pin0InfoVect2LinkObjId="SW-306760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2bc8ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="535,-95 572,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2bc9e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-81 572,-95 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2b578d0@0" ObjectIDZND0="g_2bc8ec0@0" ObjectIDZND1="47567@x" ObjectIDZND2="47568@x" Pin0InfoVect0LinkObjId="g_2bc8ec0_0" Pin0InfoVect1LinkObjId="SW-306758_0" Pin0InfoVect2LinkObjId="SW-306760_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b578d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="572,-81 572,-95 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3e640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-95 572,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2b578d0@0" ObjectIDND1="g_2bc8ec0@0" ObjectIDZND0="47567@x" ObjectIDZND1="47568@x" Pin0InfoVect0LinkObjId="SW-306758_0" Pin0InfoVect1LinkObjId="SW-306760_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b578d0_0" Pin1InfoVect1LinkObjId="g_2bc8ec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="572,-95 572,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3e8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="572,-104 572,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2b578d0@0" ObjectIDND1="g_2bc8ec0@0" ObjectIDND2="47568@x" ObjectIDZND0="47567@0" Pin0InfoVect0LinkObjId="SW-306758_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b578d0_0" Pin1InfoVect1LinkObjId="g_2bc8ec0_0" Pin1InfoVect2LinkObjId="SW-306760_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="572,-104 572,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b3eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="620,-92 620,-104 572,-104 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47568@0" ObjectIDZND0="47567@x" ObjectIDZND1="g_2b578d0@0" ObjectIDZND2="g_2bc8ec0@0" Pin0InfoVect0LinkObjId="SW-306758_0" Pin0InfoVect1LinkObjId="g_2b578d0_0" Pin0InfoVect2LinkObjId="g_2bc8ec0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306760_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="620,-92 620,-104 572,-104 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b67a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,2 539,-6 572,-6 572,-32 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48651@0" ObjectIDZND0="g_2b578d0@1" Pin0InfoVect0LinkObjId="g_2b578d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315142_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,2 539,-6 572,-6 572,-32 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b68630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,38 539,84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="48651@1" ObjectIDZND0="g_2b67c30@0" Pin0InfoVect0LinkObjId="g_2b67c30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-315142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,38 539,84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a058f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,192 564,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="earth" ObjectIDND0="g_2b67c30@0" ObjectIDND1="g_2b68890@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b67c30_0" Pin1InfoVect1LinkObjId="g_2b68890_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="539,192 564,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a063c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,118 539,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2b67c30@1" ObjectIDZND0="g_2b68890@0" Pin0InfoVect0LinkObjId="g_2b68890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b67c30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,118 539,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a06620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="539,192 539,222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2b67c30@0" ObjectIDZND0="g_2b68890@0" Pin0InfoVect0LinkObjId="g_2b68890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b67c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="539,192 539,222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b6f0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-235 452,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47570@1" ObjectIDZND0="47540@0" Pin0InfoVect0LinkObjId="g_2b7d680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306787_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="452,-235 452,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b70f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-140 452,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b6f8f0@0" ObjectIDZND0="g_2b70620@0" ObjectIDZND1="47570@x" Pin0InfoVect0LinkObjId="g_2b70620_0" Pin0InfoVect1LinkObjId="SW-306787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b6f8f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="409,-140 452,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b71a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-128 452,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2b70620@1" ObjectIDZND0="g_2b6f8f0@0" ObjectIDZND1="47570@x" Pin0InfoVect0LinkObjId="g_2b6f8f0_0" Pin0InfoVect1LinkObjId="SW-306787_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b70620_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="452,-128 452,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b71c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-140 452,-166 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2b6f8f0@0" ObjectIDND1="g_2b70620@0" ObjectIDZND0="47570@0" Pin0InfoVect0LinkObjId="SW-306787_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b6f8f0_0" Pin1InfoVect1LinkObjId="g_2b70620_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="452,-140 452,-166 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a615b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-47 452,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="lightningRod" ObjectIDND0="g_2b71ed0@0" ObjectIDZND0="g_2b70620@0" Pin0InfoVect0LinkObjId="g_2b70620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b71ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="452,-47 452,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b03150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1092,-471 1128,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="g_2b024c0@0" ObjectIDZND0="47571@x" ObjectIDZND1="g_2bfa5f0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="g_2bfa5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b024c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1092,-471 1128,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b03c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-486 1128,-471 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47571@0" ObjectIDZND0="g_2b024c0@0" ObjectIDZND1="g_2bfa5f0@0" Pin0InfoVect0LinkObjId="g_2b024c0_0" Pin0InfoVect1LinkObjId="g_2bfa5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b03150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-486 1128,-471 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b03e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-471 1128,-462 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="g_2b024c0@0" ObjectIDND1="47571@x" ObjectIDZND0="g_2bfa5f0@0" Pin0InfoVect0LinkObjId="g_2bfa5f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b024c0_0" Pin1InfoVect1LinkObjId="g_2b03150_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-471 1128,-462 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b040d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1096,-394 1128,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2b59f80@0" ObjectIDZND0="47548@x" ObjectIDZND1="g_2bfa5f0@0" Pin0InfoVect0LinkObjId="SW-306563_0" Pin0InfoVect1LinkObjId="g_2bfa5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b59f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1096,-394 1128,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b04bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-382 1128,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47548@0" ObjectIDZND0="g_2b59f80@0" ObjectIDZND1="g_2bfa5f0@0" Pin0InfoVect0LinkObjId="g_2b59f80_0" Pin0InfoVect1LinkObjId="g_2bfa5f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-382 1128,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b04e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1128,-394 1128,-409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2b59f80@0" ObjectIDND1="47548@x" ObjectIDZND0="g_2bfa5f0@1" Pin0InfoVect0LinkObjId="g_2bfa5f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b59f80_0" Pin1InfoVect1LinkObjId="SW-306563_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1128,-394 1128,-409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1386,49 1410,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2b196b0@0" Pin0InfoVect0LinkObjId="g_2b196b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1386,49 1410,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2b1a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1480,92 1480,49 1463,49 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2b06ec0@0" ObjectIDZND0="g_2b196b0@1" Pin0InfoVect0LinkObjId="g_2b196b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b06ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1480,92 1480,49 1463,49 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1a830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-579 1127,-597 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="47571@1" ObjectIDZND0="47542@0" Pin0InfoVect0LinkObjId="SW-306512_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b03150_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-579 1127,-597 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1b890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1089,-646 1127,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="busSection" ObjectIDND0="47544@0" ObjectIDZND0="47542@x" ObjectIDZND1="47616@0" Pin0InfoVect0LinkObjId="SW-306512_0" Pin0InfoVect1LinkObjId="g_338a2e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306526_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1089,-646 1127,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1ce50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1056,-809 1127,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="currentTransformer" ObjectIDND0="g_2b80980@0" ObjectIDZND0="47545@x" ObjectIDZND1="47543@x" ObjectIDZND2="g_2ba74f0@0" Pin0InfoVect0LinkObjId="SW-306527_0" Pin0InfoVect1LinkObjId="SW-306520_0" Pin0InfoVect2LinkObjId="g_2ba74f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2b80980_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1056,-809 1127,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1d940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1148,-726 1127,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="currentTransformer" ObjectIDND0="47545@0" ObjectIDZND0="47543@x" ObjectIDZND1="g_2b80980@0" ObjectIDZND2="g_2ba74f0@0" Pin0InfoVect0LinkObjId="SW-306520_0" Pin0InfoVect1LinkObjId="g_2b80980_0" Pin0InfoVect2LinkObjId="g_2ba74f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306527_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1148,-726 1127,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b1e430">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-702 1127,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="currentTransformer" ObjectIDND0="47543@1" ObjectIDZND0="47545@x" ObjectIDZND1="g_2b80980@0" ObjectIDZND2="g_2ba74f0@0" Pin0InfoVect0LinkObjId="SW-306527_0" Pin0InfoVect1LinkObjId="g_2b80980_0" Pin0InfoVect2LinkObjId="g_2ba74f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306520_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-702 1127,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a185f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-726 1127,-809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="currentTransformer" EndDevType2="powerLine" ObjectIDND0="47545@x" ObjectIDND1="47543@x" ObjectIDZND0="g_2b80980@0" ObjectIDZND1="g_2ba74f0@0" ObjectIDZND2="47613@1" Pin0InfoVect0LinkObjId="g_2b80980_0" Pin0InfoVect1LinkObjId="g_2ba74f0_0" Pin0InfoVect2LinkObjId="g_2a1a090_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306527_0" Pin1InfoVect1LinkObjId="SW-306520_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-726 1127,-809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a18850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1161,-857 1127,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2ba74f0@0" ObjectIDZND0="g_2b80980@0" ObjectIDZND1="47545@x" ObjectIDZND2="47543@x" Pin0InfoVect0LinkObjId="g_2b80980_0" Pin0InfoVect1LinkObjId="SW-306527_0" Pin0InfoVect2LinkObjId="SW-306520_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba74f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1161,-857 1127,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a19340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-809 1127,-857 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="currentTransformer" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="g_2b80980@0" ObjectIDND1="47545@x" ObjectIDND2="47543@x" ObjectIDZND0="g_2ba74f0@0" ObjectIDZND1="47613@1" ObjectIDZND2="g_2ba9a50@0" Pin0InfoVect0LinkObjId="g_2ba74f0_0" Pin0InfoVect1LinkObjId="g_2a1a090_1" Pin0InfoVect2LinkObjId="g_2ba9a50_0" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2b80980_0" Pin1InfoVect1LinkObjId="SW-306527_0" Pin1InfoVect2LinkObjId="SW-306520_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-809 1127,-857 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a195a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1089,-923 1128,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="currentTransformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2ba9a50@0" ObjectIDZND0="g_2ba74f0@0" ObjectIDZND1="g_2b80980@0" ObjectIDZND2="47545@x" Pin0InfoVect0LinkObjId="g_2ba74f0_0" Pin0InfoVect1LinkObjId="g_2b80980_0" Pin0InfoVect2LinkObjId="SW-306527_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ba9a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1089,-923 1128,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1a090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-857 1127,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" EndDevType1="lightningRod" ObjectIDND0="g_2ba74f0@0" ObjectIDND1="g_2b80980@0" ObjectIDND2="47545@x" ObjectIDZND0="47613@1" ObjectIDZND1="g_2ba9a50@0" Pin0InfoVect0LinkObjId="g_2a1a2f0_1" Pin0InfoVect1LinkObjId="g_2ba9a50_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba74f0_0" Pin1InfoVect1LinkObjId="g_2b80980_0" Pin1InfoVect2LinkObjId="SW-306527_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-857 1127,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a1a2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-923 1127,-963 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="currentTransformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2ba74f0@0" ObjectIDND1="g_2b80980@0" ObjectIDND2="47545@x" ObjectIDZND0="47613@1" Pin0InfoVect0LinkObjId="g_2a1a090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2ba74f0_0" Pin1InfoVect1LinkObjId="g_2b80980_0" Pin1InfoVect2LinkObjId="SW-306527_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-923 1127,-963 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_338a2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-666 1127,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47543@0" ObjectIDZND0="47616@0" Pin0InfoVect0LinkObjId="g_3396f90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-666 1127,-656 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3396da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-624 1127,-646 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="47542@1" ObjectIDZND0="47544@x" ObjectIDZND1="47616@0" Pin0InfoVect0LinkObjId="SW-306526_0" Pin0InfoVect1LinkObjId="g_338a2e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-306512_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-624 1127,-646 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3396f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1127,-646 1127,-656 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="busSection" ObjectIDND0="47544@x" ObjectIDND1="47542@x" ObjectIDZND0="47616@0" Pin0InfoVect0LinkObjId="g_338a2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-306526_0" Pin1InfoVect1LinkObjId="SW-306512_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1127,-646 1127,-656 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-305431" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.000000 -913.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47299" ObjectName="DYN-YA_DHK"/>
     <cge:Meas_Ref ObjectId="305431"/>
    </metadata>
   </g>
  </g><g id="CircleFilled_Layer">
   <circle DF8003:Layer="PUBLIC" cx="573" cy="-8" fill="none" fillStyle="0" r="13" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_DHK.YA_DHK_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="409,-265 1416,-265 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47540" ObjectName="BS-YA_DHK.YA_DHK_9IM"/>
    <cge:TPSR_Ref TObjectID="47540"/></metadata>
   <polyline fill="none" opacity="0" points="409,-265 1416,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_DHK.YA_DHK_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1119,-656 1135,-656 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47616" ObjectName="BS-YA_DHK.YA_DHK_3IM"/>
    <cge:TPSR_Ref TObjectID="47616"/></metadata>
   <polyline fill="none" opacity="0" points="1119,-656 1135,-656 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="1128" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="721" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="909" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="1078" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="1288" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="572" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47540" cx="452" cy="-265" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47616" cx="1127" cy="-656" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47616" cx="1127" cy="-656" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-306512">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1118.000000 -589.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47542" ObjectName="SW-YA_DHK.YA_DHK_301BK"/>
     <cge:Meas_Ref ObjectId="306512"/>
    <cge:TPSR_Ref TObjectID="47542"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1119.000000 -313.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47546" ObjectName="SW-YA_DHK.YA_DHK_001BK"/>
     <cge:Meas_Ref ObjectId="306562"/>
    <cge:TPSR_Ref TObjectID="47546"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306604">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 712.371429 -148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47549" ObjectName="SW-YA_DHK.YA_DHK_062BK"/>
     <cge:Meas_Ref ObjectId="306604"/>
    <cge:TPSR_Ref TObjectID="47549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306642">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 900.371429 -148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47553" ObjectName="SW-YA_DHK.YA_DHK_063BK"/>
     <cge:Meas_Ref ObjectId="306642"/>
    <cge:TPSR_Ref TObjectID="47553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306680">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1069.371429 -145.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47557" ObjectName="SW-YA_DHK.YA_DHK_064BK"/>
     <cge:Meas_Ref ObjectId="306680"/>
    <cge:TPSR_Ref TObjectID="47557"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306718">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1279.371429 -148.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47561" ObjectName="SW-YA_DHK.YA_DHK_065BK"/>
     <cge:Meas_Ref ObjectId="306718"/>
    <cge:TPSR_Ref TObjectID="47561"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-306756">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 563.371429 -149.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47565" ObjectName="SW-YA_DHK.YA_DHK_061BK"/>
     <cge:Meas_Ref ObjectId="306756"/>
    <cge:TPSR_Ref TObjectID="47565"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2c4da80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">大河口变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,12)">主变参数</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,27)">SZ11-3150/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,57)">3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,72)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2954e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 929.000000 -584.000000) translate(0,87)">Uk=7.17%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2c2eb70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0d8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -175.000000 -892.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_264db90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 368.000000 -274.000000) translate(0,12)">IM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2872050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 -341.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_285b380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -691.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28743b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1138.000000 -618.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2be6e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -1017.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2c13b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 187.000000 -1054.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b122e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -197.000000 -55.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b12450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -65.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2b12450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -43.000000 -65.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_2be7c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 119.000000 -1036.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bf8650" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 671.000000 109.000000) translate(0,12)">10kV麂子线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bfa440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 779.000000 -75.000000) translate(0,12)">06267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4eaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -761.000000) translate(0,12)">35kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4eaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 979.000000 -761.000000) translate(0,27)">站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4ef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -855.000000) translate(0,12)">35kV连河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a4ef90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1231.000000 -855.000000) translate(0,27)">线路电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdbd30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 879.000000 112.000000) translate(0,12)">10kV水洼线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2bdc810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 967.000000 -75.000000) translate(0,12)">06367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b21c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1039.000000 112.000000) translate(0,12)">10kV葡萄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b22870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1136.000000 -72.000000) translate(0,12)">06467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b850d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1238.000000 109.000000) translate(0,12)">10kV涟水线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b85f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1346.000000 -75.000000) translate(0,12)">06567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b6acf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1190.000000 48.000000) translate(0,12)">（站外N1塔）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b3ed60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 630.000000 -76.000000) translate(0,12)">06167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b40c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 13.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b67550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 546.000000 13.000000) translate(0,12)">0030</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 415.000000 10.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 415.000000 10.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a61fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 499.000000 256.000000) translate(0,12)">10kV1号接地变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1345.000000 66.000000) translate(0,12)">A0R1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 148.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a62db0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1516.000000 148.000000) translate(0,27)">（站内设备）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b73840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1151.000000 -752.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b73d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1050.000000 -633.000000) translate(0,12)">30160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b73f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1168.000000 -560.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b741b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 464.000000 -208.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b746c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 582.000000 -178.000000) translate(0,12)">061</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b74940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 731.000000 -177.000000) translate(0,12)">062</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b74b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 919.000000 -177.000000) translate(0,12)">063</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b74dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1096.000000 -174.000000) translate(0,12)">064</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b75000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1298.000000 -177.000000) translate(0,12)">065</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b753f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1139.000000 -998.000000) translate(0,12)">35kV连河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b05080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -93.000000 -666.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2b191c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 109.000000) translate(0,12)">A0R2</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b68890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 533.000000 240.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_DHK"/>
</svg>