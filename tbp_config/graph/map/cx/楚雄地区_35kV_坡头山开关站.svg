<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-259" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="17 -1132 1753 1038">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="5" x2="5" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="2" x2="2" y1="11" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="26" x2="9" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="18" y2="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="generator:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="60" x2="21" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="15" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="6" x2="6" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="4" x2="4" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="6" y2="9"/>
    <rect height="13" stroke-width="0.424575" width="29" x="15" y="1"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="lightningRod:shape177">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_1a115e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape48_0">
    <ellipse cx="25" cy="29" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="25" y1="32" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="24" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="25" y1="16" y2="24"/>
   </symbol>
   <symbol id="transformer2:shape48_1">
    <circle cx="25" cy="61" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="33" y1="59" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="33" y1="75" y2="59"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="16" y1="75" y2="59"/>
   </symbol>
   <symbol id="voltageTransformer:shape80">
    <ellipse cx="8" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <rect height="24" stroke-width="0.379884" width="14" x="1" y="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="67" y2="23"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
   </symbol>
   <symbol id="voltageTransformer:shape116">
    <ellipse cx="8" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="20" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="10" x2="5" y1="22" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="9" x2="10" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="6" x2="5" y1="18" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="23" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="17" x2="20" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="20" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="11" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="8" x2="8" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="5" x2="8" y1="6" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="33" y1="40" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="36" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="35" y1="42" y2="42"/>
    <polyline points="20,8 37,8 37,19 " stroke-width="0.6"/>
    <ellipse cx="19" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="8" cy="9" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="19" cy="19" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <rect height="13" stroke-width="1" width="8" x="33" y="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="32" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="42" x2="31" y1="21" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="27" y2="30"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a491c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a49e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a91b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a92710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a93880" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a94390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a94dd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a95710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_136d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a98470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1a9b190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9ce00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9da70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9ef70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa6dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa7e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa8f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa0b20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1660" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa2c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa3670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aa4810" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa5430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1b67610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1b68050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b62fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1b645c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="1048" width="1763" x="12" y="-1137"/>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a21250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 872.000000 835.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1502a10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 861.000000 820.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a8d450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 886.000000 805.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 539.000000 676.400000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4d630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 734.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c757b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 705.866667) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c759f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 549.000000 719.933333) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c75c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 554.000000 662.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c76690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 545.000000 691.466667) translate(0,12)">3U0(V):</text>
   <metadata/></g>
  </g><g id="PolygonFilled_Layer">
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1597,-432 1580,-432 1589,-422 1597,-431 1597,-432 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1596,-394 1582,-394 1589,-405 1596,-395 1596,-394 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="710,-375 693,-375 702,-365 710,-374 710,-375 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="974,-376 957,-376 966,-366 974,-375 974,-376 " stroke="rgb(255,255,0)"/>
   <polyline DF8003:Layer="PUBLIC" fill="none" points="1595,-293 1583,-293 1589,-286 1595,-293 1595,-293 " stroke="rgb(255,255,0)"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196899">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 835.000000 -720.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30177" ObjectName="SW-CX_PTS.CX_PTS_351BK"/>
     <cge:Meas_Ref ObjectId="196899"/>
    <cge:TPSR_Ref TObjectID="30177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196902">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 693.000000 -506.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30181" ObjectName="SW-CX_PTS.CX_PTS_352BK"/>
     <cge:Meas_Ref ObjectId="196902"/>
    <cge:TPSR_Ref TObjectID="30181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196905">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 957.000000 -506.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30185" ObjectName="SW-CX_PTS.CX_PTS_353BK"/>
     <cge:Meas_Ref ObjectId="196905"/>
    <cge:TPSR_Ref TObjectID="30185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196908">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -523.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30189" ObjectName="SW-CX_PTS.CX_PTS_354BK"/>
     <cge:Meas_Ref ObjectId="196908"/>
    <cge:TPSR_Ref TObjectID="30189"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a056c0">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 761.000000 -944.000000)" xlink:href="#voltageTransformer:shape80"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c9a2e0">
    <use class="BV-35KV" transform="matrix(1.666667 -0.000000 0.000000 -1.847826 1264.176329 -799.000000)" xlink:href="#voltageTransformer:shape116"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_PTS.CX_PTS_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1769,-616 528,-616 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="30175" ObjectName="BS-CX_PTS.CX_PTS_3IM"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   <polyline fill="none" opacity="0" points="1769,-616 528,-616 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.620000 -0.000000 0.000000 -0.677778 1232.000000 -187.000000)" xlink:href="#transformer2:shape48_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.620000 -0.000000 0.000000 -0.677778 1232.000000 -187.000000)" xlink:href="#transformer2:shape48_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1dbcc70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1268.000000 -744.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a04780">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.000000 -928.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1aba630">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1316.000000 -744.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1d56dd0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 680.500000 -319.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_135aab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1497.000000 -445.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c768d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -994.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c99100">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 944.500000 -320.500000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a08170">
    <use class="BV-35KV" transform="matrix(0.000000 -0.694444 -0.763636 -0.000000 1223.254545 -413.375000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c50200">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -378.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a11350">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1230.000000 -145.000000)" xlink:href="#lightningRod:shape177"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 108.000000 -1056.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196871" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 131.000000 -919.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196871" ObjectName="CX_PTS:CX_PTS_351BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196872" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 130.000000 -881.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196872" ObjectName="CX_PTS:CX_PTS_351BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196895" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -246.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196895" ObjectName="CX_PTS:CX_PTS_355BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196896" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -233.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196896" ObjectName="CX_PTS:CX_PTS_355BK_Cos"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-196894" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1577.000000 -220.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196894" ObjectName="CX_PTS:CX_PTS_355BK_P"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="1277" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="966" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="844" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="702" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="1247" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="30175" cx="1589" cy="-616" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-196188" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 300.000000 -1024.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29873" ObjectName="DYN-CX_PTS"/>
     <cge:Meas_Ref ObjectId="196188"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1dbcfb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1548.000000 -267.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae01a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1243.000000 -913.000000) translate(0,12)"> 35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ae01a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1243.000000 -913.000000) translate(0,27)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1ab8f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 545.000000 -640.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1c76440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -295.000000) translate(0,15)">1号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1358210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -525.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1a21670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 17.000000 -963.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1359a20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 151.000000 -1104.500000) translate(0,16)">坡头山开关站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a08d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -274.000000) translate(0,15)">1-8号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cc5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -895.000000) translate(0,18)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cc5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -895.000000) translate(0,40)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cc5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -895.000000) translate(0,62)">坡</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cc5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -895.000000) translate(0,84)">物</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_1cc5160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 793.000000 -895.000000) translate(0,106)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a06400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 802.000000 -1125.000000) translate(0,15)">至35kV物茂</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a06400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 802.000000 -1125.000000) translate(0,33)">变35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1d414c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -295.000000) translate(0,15)">2号集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a03af0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 924.000000 -275.000000) translate(0,15)">9-16号方阵</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a10e50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1167.000000 -136.000000) translate(0,15)">1号动态无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1cc5bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1267.000000 -222.000000) translate(0,12)">3000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1cc5f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1222.500000 -112.000000) translate(0,15)">3MVar</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -749.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ca2e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -898.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c4f440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1289.000000 -677.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1e4cc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 714.000000 -535.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1acb4b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 977.000000 -535.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1ace900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 -552.000000) translate(0,12)">354</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1c55080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 -536.000000) translate(0,12)">3551</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1d30bf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -348.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1d335e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -348.000000) translate(0,12)">3546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1acaac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 854.000000 -898.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a87cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -437.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a87f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 987.000000 -440.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a88170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1261.000000 -503.000000) translate(0,12)">35460</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(240,255,255)" font-family="SimSun" font-size="15" graphid="g_1a89120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1257.000000 -297.000000) translate(0,12)">35467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a895a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -167.000000) translate(0,15)">15758500229</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1a897f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 129.000000 -138.000000) translate(0,15)">周春飞：15974609552</text>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.3207" x1="1590" x2="1596" y1="-368" y2="-358"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.3207" x1="1590" x2="1584" y1="-368" y2="-358"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.3207" x1="1596" x2="1584" y1="-358" y2="-358"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1589" x2="1593" y1="-343" y2="-347"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1589" x2="1589" y1="-343" y2="-339"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1589" x2="1585" y1="-343" y2="-347"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1589" x2="1589" y1="-331" y2="-269"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1591" x2="1591" y1="-315" y2="-315"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1589" x2="1619" y1="-315" y2="-332"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1614" x2="1623" y1="-319" y2="-319"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1616" x2="1621" y1="-317" y2="-317"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="1" x1="1618" x2="1620" y1="-315" y2="-315"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,0)" stroke-width="0.649727" x1="1589" x2="1589" y1="-549" y2="-517"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-196911">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1267.000000 -654.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30193" ObjectName="SW-CX_PTS.CX_PTS_3901SW"/>
     <cge:Meas_Ref ObjectId="196911"/>
    <cge:TPSR_Ref TObjectID="30193"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -765.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30178" ObjectName="SW-CX_PTS.CX_PTS_351XC"/>
     <cge:Meas_Ref ObjectId="196900"/>
    <cge:TPSR_Ref TObjectID="30178"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196900">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -688.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30179" ObjectName="SW-CX_PTS.CX_PTS_351XC1"/>
     <cge:Meas_Ref ObjectId="196900"/>
    <cge:TPSR_Ref TObjectID="30179"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196903">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30183" ObjectName="SW-CX_PTS.CX_PTS_352XC1"/>
     <cge:Meas_Ref ObjectId="196903"/>
    <cge:TPSR_Ref TObjectID="30183"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196903">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 692.000000 -551.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30182" ObjectName="SW-CX_PTS.CX_PTS_352XC"/>
     <cge:Meas_Ref ObjectId="196903"/>
    <cge:TPSR_Ref TObjectID="30182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196906">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 -474.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30187" ObjectName="SW-CX_PTS.CX_PTS_353XC1"/>
     <cge:Meas_Ref ObjectId="196906"/>
    <cge:TPSR_Ref TObjectID="30187"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196906">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 956.000000 -551.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30186" ObjectName="SW-CX_PTS.CX_PTS_353XC"/>
     <cge:Meas_Ref ObjectId="196906"/>
    <cge:TPSR_Ref TObjectID="30186"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -568.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30190" ObjectName="SW-CX_PTS.CX_PTS_354XC"/>
     <cge:Meas_Ref ObjectId="196909"/>
    <cge:TPSR_Ref TObjectID="30190"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196909">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1237.000000 -491.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30191" ObjectName="SW-CX_PTS.CX_PTS_354XC1"/>
     <cge:Meas_Ref ObjectId="196909"/>
    <cge:TPSR_Ref TObjectID="30191"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1579.000000 -553.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30194" ObjectName="SW-CX_PTS.CX_PTS_3551XC"/>
     <cge:Meas_Ref ObjectId="196912"/>
    <cge:TPSR_Ref TObjectID="30194"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196912">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1579.000000 -481.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30198" ObjectName="SW-CX_PTS.CX_PTS_3551XC1"/>
     <cge:Meas_Ref ObjectId="196912"/>
    <cge:TPSR_Ref TObjectID="30198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196913">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1238.000000 -318.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30195" ObjectName="SW-CX_PTS.CX_PTS_3546SW"/>
     <cge:Meas_Ref ObjectId="196913"/>
    <cge:TPSR_Ref TObjectID="30195"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196904">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.000000 -408.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30184" ObjectName="SW-CX_PTS.CX_PTS_35267SW"/>
     <cge:Meas_Ref ObjectId="196904"/>
    <cge:TPSR_Ref TObjectID="30184"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196907">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 984.000000 -409.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30188" ObjectName="SW-CX_PTS.CX_PTS_35367SW"/>
     <cge:Meas_Ref ObjectId="196907"/>
    <cge:TPSR_Ref TObjectID="30188"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196910">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1258.000000 -472.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30192" ObjectName="SW-CX_PTS.CX_PTS_35460SW"/>
     <cge:Meas_Ref ObjectId="196910"/>
    <cge:TPSR_Ref TObjectID="30192"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196914">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1254.000000 -266.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30196" ObjectName="SW-CX_PTS.CX_PTS_35467SW"/>
     <cge:Meas_Ref ObjectId="196914"/>
    <cge:TPSR_Ref TObjectID="30196"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-196901">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 851.000000 -867.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="30180" ObjectName="SW-CX_PTS.CX_PTS_35167SW"/>
     <cge:Meas_Ref ObjectId="196901"/>
    <cge:TPSR_Ref TObjectID="30180"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="YM_WM" endPointId="0" endStationName="CX_PTS" flowDrawDirect="1" flowShape="0" id="AC-35kV.LINE_powu" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="844,-1054 844,-1079 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37445" ObjectName="AC-35kV.LINE_powu"/>
    <cge:TPSR_Ref TObjectID="37445_SS-259"/></metadata>
   <polyline fill="none" opacity="0" points="844,-1054 844,-1079 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1a07650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="779,-413 761,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1abaa00@0" ObjectIDZND0="30184@1" Pin0InfoVect0LinkObjId="SW-196904_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1abaa00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="779,-413 761,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1adfa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-780 1277,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_1dbcc70@1" ObjectIDZND0="g_1c9a2e0@0" Pin0InfoVect0LinkObjId="g_1c9a2e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbcc70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-780 1277,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1aba3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-616 1277,-661 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30175@0" ObjectIDZND0="30193@0" Pin0InfoVect0LinkObjId="SW-196911_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acad90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-616 1277,-661 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca3c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1322,-748 1322,-728 1277,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1aba630@0" ObjectIDZND0="g_1dbcc70@0" ObjectIDZND1="30193@x" Pin0InfoVect0LinkObjId="g_1dbcc70_0" Pin0InfoVect1LinkObjId="SW-196911_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1aba630_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1322,-748 1322,-728 1277,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca3ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-749 1277,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1dbcc70@0" ObjectIDZND0="g_1aba630@0" ObjectIDZND1="30193@x" Pin0InfoVect0LinkObjId="g_1aba630_0" Pin0InfoVect1LinkObjId="SW-196911_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1dbcc70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-749 1277,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d56b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1277,-728 1277,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1dbcc70@0" ObjectIDND1="g_1aba630@0" ObjectIDZND0="30193@1" Pin0InfoVect0LinkObjId="SW-196911_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1dbcc70_0" Pin1InfoVect1LinkObjId="g_1aba630_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1277,-728 1277,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_135a780">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1315,-477 1303,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1a03d30@0" ObjectIDZND0="30192@1" Pin0InfoVect0LinkObjId="SW-196910_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a03d30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1315,-477 1303,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a1dc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1556,-453 1589,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_135aab0@0" ObjectIDZND0="30198@x" Pin0InfoVect0LinkObjId="SW-196912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135aab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1556,-453 1589,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a05460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-1033 844,-1054 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_1c768d0@1" ObjectIDZND0="37445@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c768d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="844,-1033 844,-1054 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca4690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-413 672,-413 672,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="generator" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="30183@x" ObjectIDND1="43357@x" ObjectIDND2="30184@x" ObjectIDZND0="g_1d56dd0@0" Pin0InfoVect0LinkObjId="g_1d56dd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196903_0" Pin1InfoVect1LinkObjId="SM-CX_PTS.P1_0" Pin1InfoVect2LinkObjId="SW-196904_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-413 672,-413 672,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c98ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1043,-414 1025,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1ca48f0@0" ObjectIDZND0="30188@1" Pin0InfoVect0LinkObjId="SW-196907_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1ca48f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1043,-414 1025,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e237b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-414 936,-414 936,-380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="lightningRod" ObjectIDND0="30187@x" ObjectIDND1="30188@x" ObjectIDND2="43358@x" ObjectIDZND0="g_1c99100@0" Pin0InfoVect0LinkObjId="g_1c99100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196906_0" Pin1InfoVect1LinkObjId="SW-196907_0" Pin1InfoVect2LinkObjId="SM-CX_PTS.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-414 936,-414 936,-380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4ffa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-477 1217,-477 1217,-454 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1c50200@0" ObjectIDND1="30191@x" ObjectIDND2="30192@x" ObjectIDZND0="g_1a08170@0" Pin0InfoVect0LinkObjId="g_1a08170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c50200_0" Pin1InfoVect1LinkObjId="SW-196909_0" Pin1InfoVect2LinkObjId="SW-196910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-477 1217,-477 1217,-454 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1cc59d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-188 1247,-175 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_1a11350@0" Pin0InfoVect0LinkObjId="g_1a11350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a056c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-188 1247,-175 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ab9bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-374 1589,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" ObjectIDZND0="g_135aab0@0" ObjectIDZND1="30198@x" Pin0InfoVect0LinkObjId="g_135aab0_0" Pin0InfoVect1LinkObjId="SW-196912_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-374 1589,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1ab6fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1603,-362 1619,-362 1619,-319 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1603,-362 1619,-362 1619,-319 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4d9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-616 844,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30175@0" ObjectIDZND0="30179@0" Pin0InfoVect0LinkObjId="SW-196900_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acad90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="844,-616 844,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4dc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-712 844,-728 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30179@1" ObjectIDZND0="30177@0" Pin0InfoVect0LinkObjId="SW-196899_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196900_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="844,-712 844,-728 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4de80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-755 844,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30177@1" ObjectIDZND0="30178@1" Pin0InfoVect0LinkObjId="SW-196900_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196899_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="844,-755 844,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc49e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-789 844,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="30178@0" ObjectIDZND0="g_1c768d0@0" ObjectIDZND1="g_1a04780@0" ObjectIDZND2="g_1a056c0@0" Pin0InfoVect0LinkObjId="g_1c768d0_0" Pin0InfoVect1LinkObjId="g_1a04780_0" Pin0InfoVect2LinkObjId="g_1a056c0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196900_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="844,-789 844,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1cc4c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-872 844,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="30178@x" ObjectIDND1="30180@x" ObjectIDZND0="g_1c768d0@0" ObjectIDZND1="g_1a04780@0" ObjectIDZND2="g_1a056c0@0" Pin0InfoVect0LinkObjId="g_1c768d0_0" Pin0InfoVect1LinkObjId="g_1a04780_0" Pin0InfoVect2LinkObjId="g_1a056c0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196900_0" Pin1InfoVect1LinkObjId="SW-196901_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="844,-872 844,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca29d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="901,-872 892,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1c4e6c0@0" ObjectIDZND0="30180@1" Pin0InfoVect0LinkObjId="SW-196901_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c4e6c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="901,-872 892,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ca2c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="856,-872 844,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="30180@0" ObjectIDZND0="30178@x" ObjectIDZND1="g_1c768d0@0" ObjectIDZND2="g_1a04780@0" Pin0InfoVect0LinkObjId="SW-196900_0" Pin0InfoVect1LinkObjId="g_1c768d0_0" Pin0InfoVect2LinkObjId="g_1a04780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196901_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="856,-872 844,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-999 844,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1c768d0@0" ObjectIDZND0="30178@x" ObjectIDZND1="30180@x" ObjectIDZND2="g_1a04780@0" Pin0InfoVect0LinkObjId="SW-196900_0" Pin0InfoVect1LinkObjId="SW-196901_0" Pin0InfoVect2LinkObjId="g_1a04780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c768d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="844,-999 844,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4f040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="844,-936 875,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="30178@x" ObjectIDND1="30180@x" ObjectIDND2="g_1c768d0@0" ObjectIDZND0="g_1a04780@0" Pin0InfoVect0LinkObjId="g_1a04780_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-196900_0" Pin1InfoVect1LinkObjId="SW-196901_0" Pin1InfoVect2LinkObjId="g_1c768d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="844,-936 875,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c4f230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="827,-936 844,-936 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1a056c0@0" ObjectIDZND0="30178@x" ObjectIDZND1="30180@x" ObjectIDZND2="g_1c768d0@0" Pin0InfoVect0LinkObjId="SW-196900_0" Pin0InfoVect1LinkObjId="SW-196901_0" Pin0InfoVect2LinkObjId="g_1c768d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a056c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="827,-936 844,-936 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_166d2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-616 702,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="30175@0" ObjectIDZND0="30182@0" Pin0InfoVect0LinkObjId="SW-196903_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acad90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-616 702,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4c740">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-558 702,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30182@1" ObjectIDZND0="30181@1" Pin0InfoVect0LinkObjId="SW-196902_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196903_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-558 702,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1e4c9a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-514 702,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30181@0" ObjectIDZND0="30183@1" Pin0InfoVect0LinkObjId="SW-196903_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196902_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-514 702,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acad90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-575 966,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30186@0" ObjectIDZND0="30175@0" Pin0InfoVect0LinkObjId="g_1ace6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196906_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-575 966,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acaff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-541 966,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30185@1" ObjectIDZND0="30186@1" Pin0InfoVect0LinkObjId="SW-196906_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196905_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-541 966,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acb250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-498 966,-514 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30187@1" ObjectIDZND0="30185@0" Pin0InfoVect0LinkObjId="SW-196905_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196906_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-498 966,-514 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acc450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-329 966,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="generator" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="43358@0" ObjectIDZND0="g_1c99100@0" ObjectIDZND1="30187@x" ObjectIDZND2="30188@x" Pin0InfoVect0LinkObjId="g_1c99100_0" Pin0InfoVect1LinkObjId="SW-196906_0" Pin0InfoVect2LinkObjId="SW-196907_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SM-CX_PTS.P2_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="966,-329 966,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acc640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="966,-414 966,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="generator" EndDevType0="switch" ObjectIDND0="g_1c99100@0" ObjectIDND1="30188@x" ObjectIDND2="43358@x" ObjectIDZND0="30187@0" Pin0InfoVect0LinkObjId="SW-196906_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1c99100_0" Pin1InfoVect1LinkObjId="SW-196907_0" Pin1InfoVect2LinkObjId="SM-CX_PTS.P2_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="966,-414 966,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acd290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-481 702,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="generator" EndDevType2="switch" ObjectIDND0="30183@0" ObjectIDZND0="g_1d56dd0@0" ObjectIDZND1="43357@x" ObjectIDZND2="30184@x" Pin0InfoVect0LinkObjId="g_1d56dd0_0" Pin0InfoVect1LinkObjId="SM-CX_PTS.P1_0" Pin0InfoVect2LinkObjId="SW-196904_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196903_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="702,-481 702,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acd4f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="702,-413 702,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="generator" ObjectIDND0="g_1d56dd0@0" ObjectIDND1="30183@x" ObjectIDND2="30184@x" ObjectIDZND0="43357@0" Pin0InfoVect0LinkObjId="SM-CX_PTS.P1_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1d56dd0_0" Pin1InfoVect1LinkObjId="SW-196903_0" Pin1InfoVect2LinkObjId="SW-196904_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="702,-413 702,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acd750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="725,-413 702,-413 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="30184@0" ObjectIDZND0="g_1d56dd0@0" ObjectIDZND1="30183@x" ObjectIDZND2="43357@x" Pin0InfoVect0LinkObjId="g_1d56dd0_0" Pin0InfoVect1LinkObjId="SW-196903_0" Pin0InfoVect2LinkObjId="SM-CX_PTS.P1_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196904_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="725,-413 702,-413 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1acd9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="989,-414 966,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="generator" ObjectIDND0="30188@0" ObjectIDZND0="g_1c99100@0" ObjectIDZND1="30187@x" ObjectIDZND2="43358@x" Pin0InfoVect0LinkObjId="g_1c99100_0" Pin0InfoVect1LinkObjId="SW-196906_0" Pin0InfoVect2LinkObjId="SM-CX_PTS.P2_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196907_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="989,-414 966,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ace1e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-515 1247,-531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="30191@1" ObjectIDZND0="30189@0" Pin0InfoVect0LinkObjId="SW-196908_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196909_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-515 1247,-531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ace440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-558 1247,-575 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="30189@1" ObjectIDZND0="30190@1" Pin0InfoVect0LinkObjId="SW-196909_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196908_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-558 1247,-575 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1ace6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-592 1247,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30190@0" ObjectIDZND0="30175@0" Pin0InfoVect0LinkObjId="g_1acad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196909_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-592 1247,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d7840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1304,-271 1295,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1acedb0@0" ObjectIDZND0="30196@1" Pin0InfoVect0LinkObjId="SW-196914_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1acedb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1304,-271 1295,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d84f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-422 1247,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1c50200@1" ObjectIDZND0="g_1a08170@0" ObjectIDZND1="30191@x" ObjectIDZND2="30192@x" Pin0InfoVect0LinkObjId="g_1a08170_0" Pin0InfoVect1LinkObjId="SW-196909_0" Pin0InfoVect2LinkObjId="SW-196910_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1c50200_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-422 1247,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d8750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-477 1247,-498 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1a08170@0" ObjectIDND1="g_1c50200@0" ObjectIDND2="30192@x" ObjectIDZND0="30191@0" Pin0InfoVect0LinkObjId="SW-196909_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1a08170_0" Pin1InfoVect1LinkObjId="g_1c50200_0" Pin1InfoVect2LinkObjId="SW-196910_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-477 1247,-498 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d89b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1263,-477 1247,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="30192@0" ObjectIDZND0="g_1a08170@0" ObjectIDZND1="g_1c50200@0" ObjectIDZND2="30191@x" Pin0InfoVect0LinkObjId="g_1a08170_0" Pin0InfoVect1LinkObjId="g_1c50200_0" Pin0InfoVect2LinkObjId="SW-196909_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196910_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1263,-477 1247,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c54960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-453 1589,-488 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_135aab0@0" ObjectIDZND0="30198@0" Pin0InfoVect0LinkObjId="SW-196912_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_135aab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-453 1589,-488 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c54bc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-505 1589,-560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="30198@1" ObjectIDZND0="30194@1" Pin0InfoVect0LinkObjId="SW-196912_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196912_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-505 1589,-560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1c54e20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1589,-577 1589,-616 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="30194@0" ObjectIDZND0="30175@0" Pin0InfoVect0LinkObjId="g_1acad90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196912_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1589,-577 1589,-616 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1d30e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-359 1247,-384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="30195@1" ObjectIDZND0="g_1c50200@0" Pin0InfoVect0LinkObjId="g_1c50200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196913_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-359 1247,-384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a883b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-271 1247,-244 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="30196@x" ObjectIDND1="30195@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_1a056c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196914_0" Pin1InfoVect1LinkObjId="SW-196913_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-271 1247,-244 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a88d20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1259,-271 1247,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="30196@0" ObjectIDZND0="30195@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-196913_0" Pin0InfoVect1LinkObjId="g_1a056c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-196914_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1259,-271 1247,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a88f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1247,-271 1247,-323 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="30196@x" ObjectIDND1="0@x" ObjectIDZND0="30195@0" Pin0InfoVect0LinkObjId="SW-196913_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-196914_0" Pin1InfoVect1LinkObjId="g_1a056c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1247,-271 1247,-323 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196871" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -835.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196871" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30177"/>
     <cge:Term_Ref ObjectID="42875"/>
    <cge:TPSR_Ref TObjectID="30177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196872" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -835.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196872" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30177"/>
     <cge:Term_Ref ObjectID="42875"/>
    <cge:TPSR_Ref TObjectID="30177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196870" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 928.000000 -835.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196870" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30177"/>
     <cge:Term_Ref ObjectID="42875"/>
    <cge:TPSR_Ref TObjectID="30177"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196883" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -249.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196883" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30181"/>
     <cge:Term_Ref ObjectID="42883"/>
    <cge:TPSR_Ref TObjectID="30181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196884" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -249.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196884" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30181"/>
     <cge:Term_Ref ObjectID="42883"/>
    <cge:TPSR_Ref TObjectID="30181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196882" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 688.000000 -249.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196882" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30181"/>
     <cge:Term_Ref ObjectID="42883"/>
    <cge:TPSR_Ref TObjectID="30181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196887" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -249.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196887" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30185"/>
     <cge:Term_Ref ObjectID="42891"/>
    <cge:TPSR_Ref TObjectID="30185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196888" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -249.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196888" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30185"/>
     <cge:Term_Ref ObjectID="42891"/>
    <cge:TPSR_Ref TObjectID="30185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196886" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -249.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196886" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30185"/>
     <cge:Term_Ref ObjectID="42891"/>
    <cge:TPSR_Ref TObjectID="30185"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-196891" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1189.000000 -236.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196891" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30189"/>
     <cge:Term_Ref ObjectID="42899"/>
    <cge:TPSR_Ref TObjectID="30189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-196892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1189.000000 -236.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30189"/>
     <cge:Term_Ref ObjectID="42899"/>
    <cge:TPSR_Ref TObjectID="30189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-196890" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1189.000000 -236.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196890" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30189"/>
     <cge:Term_Ref ObjectID="42899"/>
    <cge:TPSR_Ref TObjectID="30189"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-196874" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196874" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-196875" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196875" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-196876" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196876" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-196880" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196880" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-196877" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196877" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-196881" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 -735.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="196881" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="30175"/>
     <cge:Term_Ref ObjectID="42872"/>
    <cge:TPSR_Ref TObjectID="30175"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="172" x="120" y="-1115"/></g>
   <g href="cx_索引_接线图_地调直调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="71" y="-1132"/></g>
  </g><g id="CircleFilled_Layer">
   <ellipse DF8003:Layer="PUBLIC" cx="1589" cy="-361" fill="none" fillStyle="0" rx="13.5" ry="12.5" stroke="rgb(255,255,0)" stroke-width="1"/>
   <ellipse DF8003:Layer="PUBLIC" cx="1589" cy="-343" fill="none" fillStyle="0" rx="13.5" ry="12.5" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Generator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-CX_PTS.P1">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 697.000000 -307.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43357" ObjectName="SM-CX_PTS.P1"/>
    <cge:TPSR_Ref TObjectID="43357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SM-CX_PTS.P2">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.000000 -308.000000)" xlink:href="#generator:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43358" ObjectName="SM-CX_PTS.P2"/>
    <cge:TPSR_Ref TObjectID="43358"/></metadata>
   </g>
  </g><g id="ConnectPoint_Layer"/><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="0.416609" width="14" x="1582" y="-543"/>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="172" x="120" y="-1115"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="172" x="120" y="-1115"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="71" y="-1132"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="71" y="-1132"/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1abaa00" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 805.000000 -405.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a03d30" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1341.000000 -469.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1ca48f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1069.000000 -406.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1c4e6c0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 897.000000 -866.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1acedb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1300.000000 -265.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_PTS"/>
</svg>