<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-219" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="-198 -1064 2082 1122">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="capacitor:shape46">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="83" y2="89"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="85" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="92" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="54" x2="54" y1="87" y2="85"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="53" y1="86" y2="94"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="36" y1="86" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="80" x2="54" y1="86" y2="86"/>
    <polyline arcFlag="1" points="104,106 102,106 100,105 99,105 97,104 96,103 94,102 93,100 92,98 92,97 91,95 91,93 91,91 92,89 92,88 93,86 94,85 96,83 97,82 99,81 100,81 102,80 104,80 106,80 108,81 109,81 111,82 112,83 114,85 115,86 116,88 116,89 117,91 117,93 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.664222" x1="80" x2="80" y1="86" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.783099" x1="80" x2="105" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="105" x2="152" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="159" x2="159" y1="78" y2="68"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.152575" x1="154" x2="159" y1="73" y2="73"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="163" x2="163" y1="70" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="166" x2="166" y1="72" y2="74"/>
    <rect height="13" stroke-width="1" width="26" x="128" y="67"/>
    <polyline arcFlag="1" points="138,47 137,47 136,47 136,48 135,48 135,48 134,49 134,49 133,50 133,50 133,51 133,51 132,52 132,53 132,53 133,54 133,55 133,55 133,56 134,56 134,57 135,57 135,58 136,58 136,58 137,58 138,58 " stroke-width="1"/>
    <polyline arcFlag="1" points="138,36 137,36 136,36 136,36 135,37 135,37 134,37 134,38 133,38 133,39 133,40 133,40 132,41 132,42 132,42 133,43 133,44 133,44 133,45 134,45 134,46 135,46 135,46 136,47 136,47 137,47 138,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="138,25 137,25 136,25 136,25 135,25 135,26 134,26 134,27 133,27 133,28 133,28 133,29 132,30 132,30 132,31 133,32 133,32 133,33 133,33 134,34 134,34 135,35 135,35 136,35 136,36 137,36 138,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289172" x1="105" x2="105" y1="38" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="97" x2="112" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.395152" x1="105" x2="122" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.370253" x1="130" x2="130" y1="56" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="97" x2="112" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="121" x2="121" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.236111" x1="88" x2="88" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.338133" x1="105" x2="122" y1="64" y2="64"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.368819" x1="88" x2="121" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375142" x1="105" x2="105" y1="93" y2="46"/>
    <polyline arcFlag="1" points="122,47 123,47 124,47 124,48 125,48 125,48 126,49 126,49 127,50 127,50 127,51 127,51 128,52 128,53 128,53 127,54 127,55 127,55 127,56 126,56 126,57 125,57 125,58 124,58 124,58 123,58 122,58 " stroke-width="1"/>
    <polyline arcFlag="1" points="122,36 123,36 124,36 124,36 125,37 125,37 126,37 126,38 127,38 127,39 127,40 127,40 128,41 128,42 128,42 127,43 127,44 127,44 127,45 126,45 126,46 125,46 125,46 124,47 124,47 123,47 122,47 " stroke-width="1"/>
    <polyline arcFlag="1" points="122,25 123,25 124,25 124,25 125,25 125,26 126,26 126,27 127,27 127,28 127,28 127,29 128,30 128,30 128,31 127,32 127,32 127,33 127,33 126,34 126,34 125,35 125,35 124,35 124,36 123,36 122,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="122" x2="122" y1="64" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="122" x2="122" y1="25" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="104" x2="104" y1="106" y2="114"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="117" x2="105" y1="93" y2="93"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="45" x2="45" y1="90" y2="120"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape7">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="20" y2="23"/>
    <polyline DF8003:Layer="PUBLIC" points="19,44 10,32 1,44 19,44 " stroke-width="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="29" y2="26"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="26" x2="14" y1="30" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="26" y1="38" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="14" y1="26" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="7" y1="38" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="27" y1="17" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="5" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="34" y1="10" y2="40"/>
    <rect height="29" stroke-width="0.416609" width="9" x="12" y="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.649727" x1="5" x2="5" y1="46" y2="10"/>
    <rect height="26" stroke-width="0.416609" width="14" x="-2" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="45" y2="54"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.75" x1="2" x2="8" y1="45" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.939539" x1="5" x2="5" y1="1" y2="10"/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape22_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1"/>
    <polyline points="58,100 64,100 " stroke-width="1"/>
    <polyline points="64,100 64,93 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="30" y1="71" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="45" x2="38" y1="79" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="62" y2="71"/>
   </symbol>
   <symbol id="transformer2:shape22_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape56">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="1" y1="58" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="20" y1="58" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="20" y1="38" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="13" y1="51" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="22" y1="59" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="37" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="61" y2="61"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="22" y1="70" y2="61"/>
    <ellipse cx="21" cy="8" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <ellipse cx="21" cy="16" fillStyle="0" rx="7.5" ry="7" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="25" y1="18" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape106">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="6" y2="16"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="35" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="6" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="1" x2="1" y1="19" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="1" y1="28" y2="19"/>
    <ellipse cx="25" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="34" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="37" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="37" y1="24" y2="22"/>
    <ellipse cx="25" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="34" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="40" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="34" y1="33" y2="33"/>
    <ellipse cx="36" cy="22" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="36" cy="34" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="7" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="25" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="25" y1="24" y2="22"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2280f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2282070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2282a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2283700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2284930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22855d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2285de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_22867d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2035a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2035a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2289ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2289ba0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_228b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_228b5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_228c5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_228e260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_228eeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_228fd90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2290990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2291e30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2292630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2292d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22934b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2294590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2294f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2295a60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2296420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22979c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2298480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_22994b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2299fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_22a8590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_229b440" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_229c100" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_229d620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1132" width="2092" x="-203" y="-1069"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="1779" x2="1779" y1="-56" y2="-85"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="1" x1="1779" x2="1819" y1="-85" y2="-85"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148037">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -711.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25888" ObjectName="SW-YA_MX.YA_MX_3011SW"/>
     <cge:Meas_Ref ObjectId="148037"/>
    <cge:TPSR_Ref TObjectID="25888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148080">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -398.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25892" ObjectName="SW-YA_MX.YA_MX_0011SW"/>
     <cge:Meas_Ref ObjectId="148080"/>
    <cge:TPSR_Ref TObjectID="25892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148014">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -795.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25884" ObjectName="SW-YA_MX.YA_MX_3411SW"/>
     <cge:Meas_Ref ObjectId="148014"/>
    <cge:TPSR_Ref TObjectID="25884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148015">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -898.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25885" ObjectName="SW-YA_MX.YA_MX_3416SW"/>
     <cge:Meas_Ref ObjectId="148015"/>
    <cge:TPSR_Ref TObjectID="25885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 497.000000 -715.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25889" ObjectName="SW-YA_MX.YA_MX_3901SW"/>
     <cge:Meas_Ref ObjectId="148074"/>
    <cge:TPSR_Ref TObjectID="25889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148087">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.500000 -292.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25895" ObjectName="SW-YA_MX.YA_MX_0411SW"/>
     <cge:Meas_Ref ObjectId="148087"/>
    <cge:TPSR_Ref TObjectID="25895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148088">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 532.500000 -92.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25896" ObjectName="SW-YA_MX.YA_MX_0416SW"/>
     <cge:Meas_Ref ObjectId="148088"/>
    <cge:TPSR_Ref TObjectID="25896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148106">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.800000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25898" ObjectName="SW-YA_MX.YA_MX_0421SW"/>
     <cge:Meas_Ref ObjectId="148106"/>
    <cge:TPSR_Ref TObjectID="25898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148107">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.800000 -86.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25899" ObjectName="SW-YA_MX.YA_MX_0426SW"/>
     <cge:Meas_Ref ObjectId="148107"/>
    <cge:TPSR_Ref TObjectID="25899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148125">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.100000 -289.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25901" ObjectName="SW-YA_MX.YA_MX_0431SW"/>
     <cge:Meas_Ref ObjectId="148125"/>
    <cge:TPSR_Ref TObjectID="25901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148126">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.100000 -89.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25902" ObjectName="SW-YA_MX.YA_MX_0436SW"/>
     <cge:Meas_Ref ObjectId="148126"/>
    <cge:TPSR_Ref TObjectID="25902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148144">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.400000 -291.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25904" ObjectName="SW-YA_MX.YA_MX_0441SW"/>
     <cge:Meas_Ref ObjectId="148144"/>
    <cge:TPSR_Ref TObjectID="25904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148145">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.400000 -91.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25905" ObjectName="SW-YA_MX.YA_MX_0446SW"/>
     <cge:Meas_Ref ObjectId="148145"/>
    <cge:TPSR_Ref TObjectID="25905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148163">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.700000 -294.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25907" ObjectName="SW-YA_MX.YA_MX_0451SW"/>
     <cge:Meas_Ref ObjectId="148163"/>
    <cge:TPSR_Ref TObjectID="25907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148164">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.700000 -94.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25908" ObjectName="SW-YA_MX.YA_MX_0456SW"/>
     <cge:Meas_Ref ObjectId="148164"/>
    <cge:TPSR_Ref TObjectID="25908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148083">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1639.400000 -287.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25893" ObjectName="SW-YA_MX.YA_MX_0901SW"/>
     <cge:Meas_Ref ObjectId="148083"/>
    <cge:TPSR_Ref TObjectID="25893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 447.000000 -701.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25890" ObjectName="SW-YA_MX.YA_MX_39017SW"/>
     <cge:Meas_Ref ObjectId="148076"/>
    <cge:TPSR_Ref TObjectID="25890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 501.000000 -640.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1643.000000 -210.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148181">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.700000 -286.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25910" ObjectName="SW-YA_MX.YA_MX_0461SW"/>
     <cge:Meas_Ref ObjectId="148181"/>
    <cge:TPSR_Ref TObjectID="25910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-204984">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.700000 -176.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38138" ObjectName="SW-YA_MX.YA_MX_0463SW"/>
     <cge:Meas_Ref ObjectId="204984"/>
    <cge:TPSR_Ref TObjectID="38138"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148182">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.700000 -65.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25911" ObjectName="SW-YA_MX.YA_MX_0466SW"/>
     <cge:Meas_Ref ObjectId="148182"/>
    <cge:TPSR_Ref TObjectID="25911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-204985">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1761.000000 -49.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38139" ObjectName="SW-YA_MX.YA_MX_04667SW"/>
     <cge:Meas_Ref ObjectId="204985"/>
    <cge:TPSR_Ref TObjectID="38139"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148016">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 950.000000 -923.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25886" ObjectName="SW-YA_MX.YA_MX_34167SW"/>
     <cge:Meas_Ref ObjectId="148016"/>
    <cge:TPSR_Ref TObjectID="25886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1633.000000 -702.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-YA_MX.YA_MX_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="325,-781 1765,-781 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25880" ObjectName="BS-YA_MX.YA_MX_3M"/>
    <cge:TPSR_Ref TObjectID="25880"/></metadata>
   <polyline fill="none" opacity="0" points="325,-781 1765,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-YA_MX.YA_MX_9M">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="276,-363 1872,-363 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="25881" ObjectName="BS-YA_MX.YA_MX_9M"/>
    <cge:TPSR_Ref TObjectID="25881"/></metadata>
   <polyline fill="none" opacity="0" points="276,-363 1872,-363 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-YA_MX.YA_MX_Cb1">
    <use class="BV-10KV" transform="matrix(0.766467 -0.000000 0.000000 -0.750000 1745.000000 35.000000)" xlink:href="#capacitor:shape46"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38140" ObjectName="CB-YA_MX.YA_MX_Cb1"/>
    <cge:TPSR_Ref TObjectID="38140"/></metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-YA_MX.YA_MX_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36598"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.000000 -580.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1607.000000 -580.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25912" ObjectName="TF-YA_MX.YA_MX_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_MX.YA_MX_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36602"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 -169.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 316.000000 -169.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25913" ObjectName="TF-YA_MX.YA_MX_Zyb2"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-YA_MX.YA_MX_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="36606"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.148000 -0.000000 0.000000 -1.000000 982.000000 -524.000000)" xlink:href="#transformer2:shape22_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.148000 -0.000000 0.000000 -1.000000 982.000000 -524.000000)" xlink:href="#transformer2:shape22_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="25914" ObjectName="TF-YA_MX.YA_MX_1T"/>
    <cge:TPSR_Ref TObjectID="25914"/></metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_19f5a70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 582.500000 -12.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a38040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 818.800000 -18.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a1d470">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.100000 -19.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19e7280">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1285.400000 -19.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19bd770">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1509.700000 -25.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19be8d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 352.000000 -346.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a10e00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 866.000000 -968.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19accd0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 535.000000 -639.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1a009f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1681.000000 -214.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_192ab80">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.000000 -117.000000)" xlink:href="#lightningRod:shape7"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_192b960">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1774.700000 -105.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -83.000000 -982.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147946" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -38.000000 -811.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147946" ObjectName="YA_MX:YA_MX_301BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147947" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -38.000000 -768.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147947" ObjectName="YA_MX:YA_MX_301BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147946" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -850.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147946" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147946" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -39.000000 -892.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147946" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147962" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -492.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147962" ObjectName="YA_MX:YA_MX_001BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147963" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -474.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147963" ObjectName="YA_MX:YA_MX_001BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147964" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -455.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147964" ObjectName="YA_MX:YA_MX_001BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147965" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -436.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147965" ObjectName="YA_MX:YA_MX_001BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147957" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -877.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147957" ObjectName="YA_MX:YA_MX_301BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147958" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -859.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147958" ObjectName="YA_MX:YA_MX_301BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147959" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -840.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147959" ObjectName="YA_MX:YA_MX_301BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147960" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -821.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147960" ObjectName="YA_MX:YA_MX_301BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-229152" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 -804.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229152" ObjectName="YA_MX:YA_MX_301BK_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-229151" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -386.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="229151" ObjectName="YA_MX:YA_MX_001BK_F"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147966" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -420.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147966" ObjectName="YA_MX:YA_MX_001BK_Ubc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-147967" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -403.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147967" ObjectName="YA_MX:YA_MX_001BK_Uca"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-147940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -882.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25883"/>
     <cge:Term_Ref ObjectID="36538"/>
    <cge:TPSR_Ref TObjectID="25883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147941" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -882.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147941" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25883"/>
     <cge:Term_Ref ObjectID="36538"/>
    <cge:TPSR_Ref TObjectID="25883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="1" id="ME-147938" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1148.000000 -882.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147938" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25883"/>
     <cge:Term_Ref ObjectID="36538"/>
    <cge:TPSR_Ref TObjectID="25883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 8.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25894"/>
     <cge:Term_Ref ObjectID="36560"/>
    <cge:TPSR_Ref TObjectID="25894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 8.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25894"/>
     <cge:Term_Ref ObjectID="36560"/>
    <cge:TPSR_Ref TObjectID="25894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 533.000000 8.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25894"/>
     <cge:Term_Ref ObjectID="36560"/>
    <cge:TPSR_Ref TObjectID="25894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147975" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 10.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147975" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25897"/>
     <cge:Term_Ref ObjectID="36566"/>
    <cge:TPSR_Ref TObjectID="25897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147976" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 10.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147976" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25897"/>
     <cge:Term_Ref ObjectID="36566"/>
    <cge:TPSR_Ref TObjectID="25897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 786.000000 10.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25897"/>
     <cge:Term_Ref ObjectID="36566"/>
    <cge:TPSR_Ref TObjectID="25897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147980" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 7.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147980" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25900"/>
     <cge:Term_Ref ObjectID="36572"/>
    <cge:TPSR_Ref TObjectID="25900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 7.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25900"/>
     <cge:Term_Ref ObjectID="36572"/>
    <cge:TPSR_Ref TObjectID="25900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1005.000000 7.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25900"/>
     <cge:Term_Ref ObjectID="36572"/>
    <cge:TPSR_Ref TObjectID="25900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147985" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 5.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147985" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25903"/>
     <cge:Term_Ref ObjectID="36578"/>
    <cge:TPSR_Ref TObjectID="25903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147986" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 5.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147986" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25903"/>
     <cge:Term_Ref ObjectID="36578"/>
    <cge:TPSR_Ref TObjectID="25903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147983" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1241.000000 5.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147983" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25903"/>
     <cge:Term_Ref ObjectID="36578"/>
    <cge:TPSR_Ref TObjectID="25903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147990" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 3.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147990" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25906"/>
     <cge:Term_Ref ObjectID="36584"/>
    <cge:TPSR_Ref TObjectID="25906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147991" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 3.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147991" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25906"/>
     <cge:Term_Ref ObjectID="36584"/>
    <cge:TPSR_Ref TObjectID="25906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147988" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 3.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147988" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25906"/>
     <cge:Term_Ref ObjectID="36584"/>
    <cge:TPSR_Ref TObjectID="25906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-147955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -591.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25914"/>
     <cge:Term_Ref ObjectID="36604"/>
    <cge:TPSR_Ref TObjectID="25914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-147956" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1254.000000 -591.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147956" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25914"/>
     <cge:Term_Ref ObjectID="36604"/>
    <cge:TPSR_Ref TObjectID="25914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.000000 -511.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25891"/>
     <cge:Term_Ref ObjectID="36554"/>
    <cge:TPSR_Ref TObjectID="25891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.000000 -511.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25891"/>
     <cge:Term_Ref ObjectID="36554"/>
    <cge:TPSR_Ref TObjectID="25891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147949" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1161.000000 -511.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147949" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25891"/>
     <cge:Term_Ref ObjectID="36554"/>
    <cge:TPSR_Ref TObjectID="25891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147995" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 2.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147995" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25909"/>
     <cge:Term_Ref ObjectID="36590"/>
    <cge:TPSR_Ref TObjectID="25909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147996" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 2.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147996" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25909"/>
     <cge:Term_Ref ObjectID="36590"/>
    <cge:TPSR_Ref TObjectID="25909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147993" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 2.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147993" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25909"/>
     <cge:Term_Ref ObjectID="36590"/>
    <cge:TPSR_Ref TObjectID="25909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-147946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -687.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25887"/>
     <cge:Term_Ref ObjectID="36546"/>
    <cge:TPSR_Ref TObjectID="25887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-147947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -687.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25887"/>
     <cge:Term_Ref ObjectID="36546"/>
    <cge:TPSR_Ref TObjectID="25887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-147943" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1157.000000 -687.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="147943" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="25887"/>
     <cge:Term_Ref ObjectID="36546"/>
    <cge:TPSR_Ref TObjectID="25887"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="191" y="-983"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="191" y="-983"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="-54" y="-1044"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-102" y="-1064"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1034" y="-874"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1034" y="-874"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="550" y="-264"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="550" y="-264"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="792" y="-258"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="792" y="-258"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1012" y="-261"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1012" y="-261"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1258" y="-263"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1258" y="-263"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1481" y="-266"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1481" y="-266"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="191" y="-1022"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="191" y="-1022"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="114,-1051 111,-1054 111,-1001 114,-1004 114,-1051" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="114,-1051 111,-1054 162,-1054 159,-1051 114,-1051" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="114,-1004 111,-1001 162,-1001 159,-1004 114,-1004" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="159,-1051 162,-1054 162,-1001 159,-1004 159,-1051" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="114" y="-1051"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="114" y="-1051"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="1833" y="-258"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="1833" y="-258"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="21" qtmmishow="hidden" width="73" x="-105" y="-692"/>
    </a>
   <metadata/><rect fill="white" height="21" opacity="0" stroke="white" transform="" width="73" x="-105" y="-692"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="1066" y="-590"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="1066" y="-590"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_配调_配网接线图35_姚安.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="191" y="-983"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="-54" y="-1044"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-102" y="-1064"/></g>
   <g href="35kV弥兴变YA_MX_341间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1034" y="-874"/></g>
   <g href="35kV弥兴变10kV大苴线041间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="550" y="-264"/></g>
   <g href="35kV弥兴变10kV备用一042间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="792" y="-258"/></g>
   <g href="35kV弥兴变10kV备用二043间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1012" y="-261"/></g>
   <g href="35kV弥兴变10kV红梅线044间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1258" y="-263"/></g>
   <g href="35kV弥兴变10kV弥兴线045间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1481" y="-266"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="191" y="-1022"/></g>
   <g href="AVC弥兴站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="114" y="-1051"/></g>
   <g href="35kV弥兴变10kV1号电容器组046间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="1833" y="-258"/></g>
   <g href="35kV弥兴变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="21" qtmmishow="hidden" width="73" x="-105" y="-692"/></g>
   <g href="35kV弥兴变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="1066" y="-590"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-148034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -643.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25887" ObjectName="SW-YA_MX.YA_MX_301BK"/>
     <cge:Meas_Ref ObjectId="148034"/>
    <cge:TPSR_Ref TObjectID="25887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148077">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -465.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25891" ObjectName="SW-YA_MX.YA_MX_001BK"/>
     <cge:Meas_Ref ObjectId="148077"/>
    <cge:TPSR_Ref TObjectID="25891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148012">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1016.000000 -845.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25883" ObjectName="SW-YA_MX.YA_MX_341BK"/>
     <cge:Meas_Ref ObjectId="148012"/>
    <cge:TPSR_Ref TObjectID="25883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148085">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 531.500000 -235.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25894" ObjectName="SW-YA_MX.YA_MX_041BK"/>
     <cge:Meas_Ref ObjectId="148085"/>
    <cge:TPSR_Ref TObjectID="25894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148104">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 773.800000 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25897" ObjectName="SW-YA_MX.YA_MX_042BK"/>
     <cge:Meas_Ref ObjectId="148104"/>
    <cge:TPSR_Ref TObjectID="25897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148123">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 994.100000 -232.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25900" ObjectName="SW-YA_MX.YA_MX_043BK"/>
     <cge:Meas_Ref ObjectId="148123"/>
    <cge:TPSR_Ref TObjectID="25900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148142">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1239.400000 -234.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25903" ObjectName="SW-YA_MX.YA_MX_044BK"/>
     <cge:Meas_Ref ObjectId="148142"/>
    <cge:TPSR_Ref TObjectID="25903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148161">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1462.700000 -237.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25906" ObjectName="SW-YA_MX.YA_MX_045BK"/>
     <cge:Meas_Ref ObjectId="148161"/>
    <cge:TPSR_Ref TObjectID="25906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-148180">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1814.700000 -229.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25909" ObjectName="SW-YA_MX.YA_MX_046BK"/>
     <cge:Meas_Ref ObjectId="148180"/>
    <cge:TPSR_Ref TObjectID="25909"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="YA_MX" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_lmixingT" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="1025,-1032 1025,-996 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="37783" ObjectName="AC-35kV.LN_lmixingT"/>
    <cge:TPSR_Ref TObjectID="37783_SS-219"/></metadata>
   <polyline fill="none" opacity="0" points="1025,-1032 1025,-996 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a7e810" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 411.000000 -700.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19271e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1739.000000 -48.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19dd430" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 953.000000 -894.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="347" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="541" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25880" cx="1025" cy="-781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25880" cx="1025" cy="-781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25880" cx="506" cy="-781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25880" cx="1638" cy="-781" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1248" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1472" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1824" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1648" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1025" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="783" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="25881" cx="1003" cy="-363" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1a85470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -16.000000 -1033.500000) translate(0,16)">弥兴变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_17d8d10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 978.000000 -1050.000000) translate(0,12)">35kV弥兴T接线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -621.000000) translate(0,12)">SZ11-8000/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -621.000000) translate(0,27)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -621.000000) translate(0,42)">8000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -621.000000) translate(0,57)">YN,d11 0NAN</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_172ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 821.000000 -621.000000) translate(0,72)">Ud%=7.14%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_19e6940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -457.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1949480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -154.000000 -895.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1926450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1130.000000 -915.333333) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a41110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 476.000000 -578.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a33870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 518.500000 -14.400000) translate(0,12)">大苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a35cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 759.800000 -7.400000) translate(0,12)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1940190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 980.100000 -17.400000) translate(0,12)">小苴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19b52b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1225.400000 -13.400000) translate(0,12)">红梅线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ec7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1448.700000 -16.400000) translate(0,12)">弥兴线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19be500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1621.000000 -154.600000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2eff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 204.000000 -375.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2f4e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 -258.000000) translate(0,12)">042</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2f720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1012.000000 -261.000000) translate(0,12)">043</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2f960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -316.000000) translate(0,12)">0421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2fba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -319.000000) translate(0,12)">0431</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a2fde0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -116.000000) translate(0,12)">0426</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1010.000000 -119.000000) translate(0,12)">0436</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 550.000000 -264.000000) translate(0,12)">041</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a304a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -322.000000) translate(0,12)">0411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a306e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 548.000000 -122.000000) translate(0,12)">0416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1258.000000 -263.000000) translate(0,12)">044</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -321.000000) translate(0,12)">0441</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1255.000000 -121.000000) translate(0,12)">0446</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a30fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1481.000000 -266.000000) translate(0,12)">045</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a31220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -324.000000) translate(0,12)">0451</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a31460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -124.000000) translate(0,12)">0456</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c10a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1655.000000 -317.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193a3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1666.000000 -652.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193a5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1031.000000 -428.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193a9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1033.000000 -494.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193ac30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -741.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193ae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -672.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 300.000000 -160.000000) translate(0,12)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b2f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 248.000000 -789.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1034.000000 -874.000000) translate(0,12)">341</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -825.000000) translate(0,12)">3411</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193b9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1032.000000 -928.000000) translate(0,12)">3416</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193bbf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 513.000000 -745.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1a12c10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 202.000000 -976.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1a13420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 202.000000 -1013.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19ac7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 450.000000 -732.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1833.000000 -258.000000) translate(0,12)">046</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19d0b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1831.000000 -316.000000) translate(0,12)">0461</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c3a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1834.000000 -94.000000) translate(0,12)">0466</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c3f30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1831.000000 -206.000000) translate(0,12)">0463</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19c4170" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1772.000000 43.000000) translate(0,12)">10kV1号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1927c70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1730.000000 -81.000000) translate(0,12)">04667</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1928e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 367.000000 -325.000000) translate(0,12)">0491</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1929370" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 118.000000 -1037.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19de340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 966.000000 -953.000000) translate(0,12)">34167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19de970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1671.000000 -740.000000) translate(0,12)">3421</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19debb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1670.000000 -632.000000) translate(0,12)">S11-50/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19dee00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 294.000000 -142.000000) translate(0,12)">S11-M-50/10</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_19baae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1659.000000 -719.000000) translate(0,12)">（现场核实无采集点号）</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1890d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -494.000000) translate(0,12)">Ua:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1890f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -475.000000) translate(0,12)">Ub:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18914a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 281.000000 -456.000000) translate(0,12)">Uc:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1891a60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -437.000000) translate(0,12)">Uab:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1892da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -879.000000) translate(0,12)">Ua:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1892fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -860.000000) translate(0,12)">Ub:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1893220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 323.000000 -841.000000) translate(0,12)">Uc:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1893460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 315.000000 -822.000000) translate(0,12)">Uab:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1894a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 332.000000 -805.000000) translate(0,12)">F:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1895480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 290.000000 -387.000000) translate(0,12)">F:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_18956c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -102.000000 -688.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_195e230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -198.000000 -62.000000) translate(0,17)">姚安巡维中心：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1925f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -35.000000 -72.500000) translate(0,17)">18787878958</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1925f40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -35.000000 -72.500000) translate(0,38)">18787878954</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_1a09d50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -35.000000 -101.000000) translate(0,17)">5861717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1962de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1066.000000 -590.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1963ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -421.000000) translate(0,12)">Ubc:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1964210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 273.000000 -404.000000) translate(0,12)">Uca:</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193c010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1094.000000 882.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193c350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1083.000000 867.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193c5c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1108.000000 852.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193c820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 577.000000) translate(0,12)">油温（℃）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_193ca60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1155.000000 592.000000) translate(0,12)">档位（档）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a12280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 468.000000 -7.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a12790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 457.000000 -22.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1a129d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 482.000000 -37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1893790" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1085.000000 496.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18939f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1110.000000 481.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1893c30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 511.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1893f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1080.000000 672.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_18941c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1105.000000 657.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1894400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1091.000000 687.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1896860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 731.000000 -10.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1896d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.000000 -25.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1896fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 745.000000 -40.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195bfa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 951.000000 -7.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c250" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 940.000000 -22.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.000000 -37.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195c8b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1186.000000 -6.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195cb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1175.000000 -21.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195cdb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1200.000000 -36.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195d1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1407.000000 -3.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195d490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1396.000000 -18.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195d6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1421.000000 -33.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195daf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1660.000000 -1.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195ddb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1649.000000 -16.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_195dff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1674.000000 -31.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-YA_MX.041Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 536.500000 -29.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34116" ObjectName="EC-YA_MX.041Ld"/>
    <cge:TPSR_Ref TObjectID="34116"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 777.800000 -23.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_MX.043Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 998.100000 -26.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="44671" ObjectName="EC-YA_MX.043Ld"/>
    <cge:TPSR_Ref TObjectID="44671"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_MX.044Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1243.400000 -28.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34117" ObjectName="EC-YA_MX.044Ld"/>
    <cge:TPSR_Ref TObjectID="34117"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-YA_MX.045Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1466.700000 -31.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34118" ObjectName="EC-YA_MX.045Ld"/>
    <cge:TPSR_Ref TObjectID="34118"/></metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-147907" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 119.500000 -959.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="25865" ObjectName="DYN-YA_MX"/>
     <cge:Meas_Ref ObjectId="147907"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1a0a870">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -892.000000)" xlink:href="#voltageTransformer:shape56"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1970ca0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 481.000000 -582.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_19aa590">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.000000 -157.000000)" xlink:href="#voltageTransformer:shape106"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1a5acb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-781 1025,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25880@0" ObjectIDZND0="25888@1" Pin0InfoVect0LinkObjId="SW-148037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a40f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-781 1025,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a40f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-800 1025,-781 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25884@0" ObjectIDZND0="25880@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148014_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-800 1025,-781 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e4f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-836 1025,-853 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25884@1" ObjectIDZND0="25883@0" Pin0InfoVect0LinkObjId="SW-148012_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148014_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-836 1025,-853 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19e5170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-880 1025,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25883@1" ObjectIDZND0="25885@0" Pin0InfoVect0LinkObjId="SW-148015_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148012_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-880 1025,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0a490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-939 1025,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="25885@1" ObjectIDZND0="g_1a10e00@0" ObjectIDZND1="25886@x" ObjectIDZND2="g_1a0a870@0" Pin0InfoVect0LinkObjId="g_1a10e00_0" Pin0InfoVect1LinkObjId="SW-148016_0" Pin0InfoVect2LinkObjId="g_1a0a870_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148015_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-939 1025,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a0a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-975 1025,-996 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="25885@x" ObjectIDND1="g_1a10e00@0" ObjectIDND2="25886@x" ObjectIDZND0="37783@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148015_0" Pin1InfoVect1LinkObjId="g_1a10e00_0" Pin1InfoVect2LinkObjId="SW-148016_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-975 1025,-996 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a0b7b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-403 1025,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25892@0" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a33a60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-403 1025,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-781 506,-756 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25880@0" ObjectIDZND0="25889@1" Pin0InfoVect0LinkObjId="SW-148074_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a40f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="506,-781 506,-756 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-720 506,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="25889@0" ObjectIDZND0="0@x" ObjectIDZND1="25890@x" ObjectIDZND2="g_19accd0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-148076_0" Pin0InfoVect2LinkObjId="g_19accd0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148074_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="506,-720 506,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f3f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-706 506,-690 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25889@x" ObjectIDND1="25890@x" ObjectIDND2="g_19accd0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148074_0" Pin1InfoVect1LinkObjId="SW-148076_0" Pin1InfoVect2LinkObjId="g_19accd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="506,-706 506,-690 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19f5690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1638,-781 1638,-752 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25880@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a40f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1638,-781 1638,-752 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19f5880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1638,-707 1638,-673 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@0" ObjectIDZND0="25912@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1638,-707 1638,-673 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dfae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-270 541,-297 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25894@1" ObjectIDZND0="25895@0" Pin0InfoVect0LinkObjId="SW-148087_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148085_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-270 541,-297 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19dfcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-135 541,-243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25896@1" ObjectIDZND0="25894@0" Pin0InfoVect0LinkObjId="SW-148085_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148088_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-135 541,-243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e07a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-50 541,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34116@0" ObjectIDZND0="25896@x" ObjectIDZND1="g_19f5a70@0" Pin0InfoVect0LinkObjId="SW-148088_0" Pin0InfoVect1LinkObjId="g_19f5a70_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_MX.041Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="541,-50 541,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e0990">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-84 541,-97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34116@x" ObjectIDND1="g_19f5a70@0" ObjectIDZND0="25896@0" Pin0InfoVect0LinkObjId="SW-148088_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_MX.041Ld_0" Pin1InfoVect1LinkObjId="g_19f5a70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-84 541,-97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a33a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="541,-333 541,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25895@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148087_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="541,-333 541,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1907090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-264 783,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25897@1" ObjectIDZND0="25898@0" Pin0InfoVect0LinkObjId="SW-148106_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148104_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-264 783,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1907280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-129 783,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25899@1" ObjectIDZND0="25897@0" Pin0InfoVect0LinkObjId="SW-148104_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148107_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-129 783,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a35ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-327 783,-364 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25898@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148106_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-327 783,-364 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a1dfe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-267 1003,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25900@1" ObjectIDZND0="25901@0" Pin0InfoVect0LinkObjId="SW-148125_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148123_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-267 1003,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a1e200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-132 1003,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25902@1" ObjectIDZND0="25900@0" Pin0InfoVect0LinkObjId="SW-148123_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148126_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-132 1003,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19aee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-269 1248,-296 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25903@1" ObjectIDZND0="25904@0" Pin0InfoVect0LinkObjId="SW-148144_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148142_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-269 1248,-296 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19af0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-134 1248,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25905@1" ObjectIDZND0="25903@0" Pin0InfoVect0LinkObjId="SW-148142_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148145_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-134 1248,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19afd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-49 1248,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34117@0" ObjectIDZND0="25905@x" ObjectIDZND1="g_19e7280@0" Pin0InfoVect0LinkObjId="SW-148145_0" Pin0InfoVect1LinkObjId="g_19e7280_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_MX.044Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-49 1248,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19aff80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-83 1248,-96 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34117@x" ObjectIDND1="g_19e7280@0" ObjectIDZND0="25905@0" Pin0InfoVect0LinkObjId="SW-148145_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_MX.044Ld_0" Pin1InfoVect1LinkObjId="g_19e7280_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-83 1248,-96 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19b5680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1248,-332 1248,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25904@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148144_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1248,-332 1248,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e7dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-137 1472,-245 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25908@1" ObjectIDZND0="25906@0" Pin0InfoVect0LinkObjId="SW-148161_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148164_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-137 1472,-245 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e8a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-52 1472,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="34118@0" ObjectIDZND0="25908@x" ObjectIDZND1="g_19bd770@0" Pin0InfoVect0LinkObjId="SW-148164_0" Pin0InfoVect1LinkObjId="g_19bd770_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-YA_MX.045Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-52 1472,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19e8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-86 1472,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34118@x" ObjectIDND1="g_19bd770@0" ObjectIDZND0="25908@0" Pin0InfoVect0LinkObjId="SW-148164_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-YA_MX.045Ld_0" Pin1InfoVect1LinkObjId="g_19bd770_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-86 1472,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ecbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-335 1472,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25907@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148163_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-335 1472,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19be2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1472,-272 1472,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25906@1" ObjectIDZND0="25907@0" Pin0InfoVect0LinkObjId="SW-148163_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148161_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1472,-272 1472,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19bf130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="347,-363 347,-341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="25881@0" ObjectIDZND0="g_19be8d0@0" Pin0InfoVect0LinkObjId="g_19be8d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="347,-363 347,-341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19bf350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="347,-296 347,-262 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_19be8d0@1" ObjectIDZND0="25913@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19be8d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="347,-296 347,-262 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a2ed90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-363 1648,-328 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="25881@0" ObjectIDZND0="25893@1" Pin0InfoVect0LinkObjId="SW-148083_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0b7b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-363 1648,-328 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a11b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-975 959,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="25885@x" ObjectIDND1="g_1a0a870@0" ObjectIDND2="37783@1" ObjectIDZND0="g_1a10e00@0" ObjectIDZND1="25886@x" Pin0InfoVect0LinkObjId="g_1a10e00_0" Pin0InfoVect1LinkObjId="SW-148016_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148015_0" Pin1InfoVect1LinkObjId="g_1a0a870_0" Pin1InfoVect2LinkObjId="g_1a0a680_1" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-975 959,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a11de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-975 923,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="voltageTransformer" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="25885@x" ObjectIDND1="g_1a0a870@0" ObjectIDND2="37783@1" ObjectIDZND0="g_1a10e00@0" Pin0InfoVect0LinkObjId="g_1a10e00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148015_0" Pin1InfoVect1LinkObjId="g_1a0a870_0" Pin1InfoVect2LinkObjId="g_1a0a680_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="959,-975 923,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1a7e620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1118,-962 1118,-975 1025,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_1a0a870@0" ObjectIDZND0="25885@x" ObjectIDZND1="g_1a10e00@0" ObjectIDZND2="25886@x" Pin0InfoVect0LinkObjId="SW-148015_0" Pin0InfoVect1LinkObjId="g_1a10e00_0" Pin0InfoVect2LinkObjId="SW-148016_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a0a870_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1118,-962 1118,-975 1025,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ac320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-706 488,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="25889@x" ObjectIDND1="0@x" ObjectIDND2="g_19accd0@0" ObjectIDZND0="25890@1" Pin0InfoVect0LinkObjId="SW-148076_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148074_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="g_19accd0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="506,-706 488,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19ac580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="452,-706 429,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="25890@0" ObjectIDZND0="g_1a7e810@0" Pin0InfoVect0LinkObjId="g_1a7e810_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="452,-706 429,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19a6a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-645 506,-624 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_1970ca0@0" Pin0InfoVect0LinkObjId="g_1970ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="506,-645 506,-624 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19a6cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="506,-706 542,-706 542,-693 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="25889@x" ObjectIDND1="0@x" ObjectIDND2="25890@x" ObjectIDZND0="g_19accd0@0" Pin0InfoVect0LinkObjId="g_19accd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-148074_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-148076_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="506,-706 542,-706 542,-693 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19a6f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="589,-66 589,-84 541,-84 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_19f5a70@0" ObjectIDZND0="34116@x" ObjectIDZND1="25896@x" Pin0InfoVect0LinkObjId="EC-YA_MX.041Ld_0" Pin0InfoVect1LinkObjId="SW-148088_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19f5a70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="589,-66 589,-84 541,-84 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_19aa330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-215 1648,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="0@0" ObjectIDZND0="g_19aa590@0" Pin0InfoVect0LinkObjId="g_19aa590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-215 1648,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19ffa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1688,-268 1688,-279 1648,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1a009f0@0" ObjectIDZND0="0@x" ObjectIDZND1="25893@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-148083_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a009f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1688,-268 1688,-279 1648,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a00530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-260 1648,-279 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="g_1a009f0@0" ObjectIDZND1="25893@x" Pin0InfoVect0LinkObjId="g_1a009f0_0" Pin0InfoVect1LinkObjId="SW-148083_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-260 1648,-279 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1a00790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1648,-279 1648,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="g_1a009f0@0" ObjectIDND1="0@x" ObjectIDZND0="25893@0" Pin0InfoVect0LinkObjId="SW-148083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a009f0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1648,-279 1648,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1981bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1517,-79 1517,-86 1472,-86 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_19bd770@0" ObjectIDZND0="34118@x" ObjectIDZND1="25908@x" Pin0InfoVect0LinkObjId="EC-YA_MX.045Ld_0" Pin0InfoVect1LinkObjId="SW-148164_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19bd770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1517,-79 1517,-86 1472,-86 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1981e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1292,-73 1292,-83 1248,-83 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_19e7280@0" ObjectIDZND0="34117@x" ObjectIDZND1="25905@x" Pin0InfoVect0LinkObjId="EC-YA_MX.044Ld_0" Pin0InfoVect1LinkObjId="SW-148145_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19e7280_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1292,-73 1292,-83 1248,-83 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1982070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="826,-72 826,-81 783,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1a38040@0" ObjectIDZND0="0@x" ObjectIDZND1="25899@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-148107_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a38040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="826,-72 826,-81 783,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d0170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-327 1824,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25910@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148181_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-327 1824,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d03d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-264 1824,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25909@1" ObjectIDZND0="25910@0" Pin0InfoVect0LinkObjId="SW-148181_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148180_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-264 1824,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19d3e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-217 1824,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="38138@1" ObjectIDZND0="25909@0" Pin0InfoVect0LinkObjId="SW-148180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-204984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-217 1824,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1926f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1766,-54 1757,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="38139@0" ObjectIDZND0="g_19271e0@0" Pin0InfoVect0LinkObjId="g_19271e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-204985_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1766,-54 1757,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19282a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1802,-54 1825,-54 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" ObjectIDND0="38139@1" ObjectIDZND0="25911@x" ObjectIDZND1="38140@x" Pin0InfoVect0LinkObjId="SW-148182_0" Pin0InfoVect1LinkObjId="CB-YA_MX.YA_MX_Cb1_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-204985_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1802,-54 1825,-54 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1928c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1825,-54 1825,-51 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="38140@0" ObjectIDZND0="38139@x" ObjectIDZND1="25911@x" Pin0InfoVect0LinkObjId="SW-204985_0" Pin0InfoVect1LinkObjId="SW-148182_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-YA_MX.YA_MX_Cb1_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1825,-54 1825,-51 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1929e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-716 1025,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="25888@0" ObjectIDZND0="25887@1" Pin0InfoVect0LinkObjId="SW-148034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-716 1025,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192a040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-473 1025,-438 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="25891@0" ObjectIDZND0="25892@1" Pin0InfoVect0LinkObjId="SW-148080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148077_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-473 1025,-438 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_192a230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-651 1025,-621 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="25887@0" ObjectIDZND0="25914@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-651 1025,-621 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192a460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1025,-529 1025,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="25914@1" ObjectIDZND0="25891@1" Pin0InfoVect0LinkObjId="SW-148077_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_192a230_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1025,-529 1025,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192a950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-54 1824,-69 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="capacitor" EndDevType0="switch" ObjectIDND0="38139@x" ObjectIDND1="38140@x" ObjectIDZND0="25911@0" Pin0InfoVect0LinkObjId="SW-148182_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-204985_0" Pin1InfoVect1LinkObjId="CB-YA_MX.YA_MX_Cb1_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-54 1824,-69 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_192b700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-171 1782,-171 1782,-159 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="38138@x" ObjectIDND1="g_192ab80@0" ObjectIDZND0="g_192b960@0" Pin0InfoVect0LinkObjId="g_192b960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-204984_0" Pin1InfoVect1LinkObjId="g_192ab80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-171 1782,-171 1782,-159 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19da510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-171 1824,-181 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_192b960@0" ObjectIDND1="g_192ab80@0" ObjectIDZND0="38138@0" Pin0InfoVect0LinkObjId="SW-204984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_192b960_0" Pin1InfoVect1LinkObjId="g_192ab80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-171 1824,-181 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19da770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-106 1824,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="25911@1" ObjectIDZND0="g_192ab80@0" Pin0InfoVect0LinkObjId="g_192ab80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148182_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-106 1824,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_19da9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1824,-161 1824,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_192ab80@1" ObjectIDZND0="g_192b960@0" ObjectIDZND1="38138@x" Pin0InfoVect0LinkObjId="g_192b960_0" Pin0InfoVect1LinkObjId="SW-204984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_192ab80_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1824,-161 1824,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19dde80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-912 959,-928 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_19dd430@0" ObjectIDZND0="25886@0" Pin0InfoVect0LinkObjId="SW-148016_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_19dd430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="959,-912 959,-928 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_19de0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="959,-964 959,-975 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="voltageTransformer" EndDevType2="powerLine" ObjectIDND0="25886@1" ObjectIDZND0="25885@x" ObjectIDZND1="g_1a0a870@0" ObjectIDZND2="37783@1" Pin0InfoVect0LinkObjId="SW-148015_0" Pin0InfoVect1LinkObjId="g_1a0a870_0" Pin0InfoVect2LinkObjId="g_1a0a680_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148016_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="959,-964 959,-975 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_198cda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1047,-73 1047,-80 1003,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="load" ObjectIDND0="g_1a1d470@0" ObjectIDZND0="25902@x" ObjectIDZND1="44671@x" Pin0InfoVect0LinkObjId="SW-148126_0" Pin0InfoVect1LinkObjId="EC-YA_MX.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1a1d470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1047,-73 1047,-80 1003,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205b500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-330 1003,-363 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="25901@1" ObjectIDZND0="25881@0" Pin0InfoVect0LinkObjId="g_1a0b7b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148125_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-330 1003,-363 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_205c000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-94 1003,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25902@0" ObjectIDZND0="g_1a1d470@0" ObjectIDZND1="44671@x" Pin0InfoVect0LinkObjId="g_1a1d470_0" Pin0InfoVect1LinkObjId="EC-YA_MX.043Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148126_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-94 1003,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20677e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1003,-80 1003,-47 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1a1d470@0" ObjectIDND1="25902@x" ObjectIDZND0="44671@0" Pin0InfoVect0LinkObjId="EC-YA_MX.043Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a1d470_0" Pin1InfoVect1LinkObjId="SW-148126_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1003,-80 1003,-47 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20bd630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-91 783,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="25899@0" ObjectIDZND0="g_1a38040@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_1a38040_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-148107_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="783,-91 783,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_20bd820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-81 783,-44 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1a38040@0" ObjectIDND1="25899@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1a38040_0" Pin1InfoVect1LinkObjId="SW-148107_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-81 783,-44 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="YA_MX"/>
</svg>