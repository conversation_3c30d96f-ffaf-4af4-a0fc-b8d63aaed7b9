<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-196" aopId="3965442" id="thSvg" product="E8000V2" version="1.0" viewBox="1350 -1653 1961 1336">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="8" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="7" x2="7" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape170">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="16,30 7,30 " stroke-width="1"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <polyline points="15,10 6,10 " stroke-width="1"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="transformer2:shape42_0">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="73" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="79" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="81" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape42_1">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.396825" x1="31" x2="31" y1="49" y2="55"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="31" x2="35" y1="55" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="25" x2="31" y1="57" y2="55"/>
   </symbol>
   <symbol id="transformer2:shape13_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="30" x2="38" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="38" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape13_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="46" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="46" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="38" x2="29" y1="34" y2="18"/>
   </symbol>
   <symbol id="voltageTransformer:shape79">
    <circle cx="18" cy="24" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="34" y1="13" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="13" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="11" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="7" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="7" y1="23" y2="25"/>
    <circle cx="18" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="8" y1="13" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="15" y2="9"/>
    <polyline points="40,23 28,32 28,36 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="37" y2="46"/>
    <rect height="14" stroke-width="1" width="8" x="30" y="23"/>
    <circle cx="7" cy="24" r="7.5" stroke-width="1"/>
    <circle cx="7" cy="12" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="37" y1="48" y2="48"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="50" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="30" x2="39" y1="46" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="19" x2="19" y1="25" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="19" y1="23" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="34" y1="23" y2="13"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c58e90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c59ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c5a2d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c5ab40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c5bb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1c5c700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5cec0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1c5d860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1668d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1668d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5fc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1c5fc40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d28430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d28430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1d293d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d2b060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d2bcb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d2cb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d2d470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d2ec30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d2f930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d301f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d309b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d31a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d32410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d32f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d338c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d34d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d358e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1d36910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d37550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1d45d20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1d38e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d3a430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1d3b960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1346" width="1971" x="1345" y="-1658"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" fillStyle="1" height="42" stroke="rgb(255,255,255)" stroke-width="1" width="72" x="1779" y="-1394"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-130627">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2478.187294 -1101.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23953" ObjectName="SW-NH_XY.NH_XY_30117SW"/>
     <cge:Meas_Ref ObjectId="130627"/>
    <cge:TPSR_Ref TObjectID="23953"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130626">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2451.187294 -1112.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23952" ObjectName="SW-NH_XY.NH_XY_3011SW"/>
     <cge:Meas_Ref ObjectId="130626"/>
    <cge:TPSR_Ref TObjectID="23952"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130643">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2451.000000 -764.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23957" ObjectName="SW-NH_XY.NH_XY_0011SW"/>
     <cge:Meas_Ref ObjectId="130643"/>
    <cge:TPSR_Ref TObjectID="23957"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130638">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3175.000000 -657.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23955" ObjectName="SW-NH_XY.NH_XY_0901SW"/>
     <cge:Meas_Ref ObjectId="130638"/>
    <cge:TPSR_Ref TObjectID="23955"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130633">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2900.403465 -1050.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23954" ObjectName="SW-NH_XY.NH_XY_3901SW"/>
     <cge:Meas_Ref ObjectId="130633"/>
    <cge:TPSR_Ref TObjectID="23954"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130570">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2860.456271 -1186.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23944" ObjectName="SW-NH_XY.NH_XY_3621SW"/>
     <cge:Meas_Ref ObjectId="130570"/>
    <cge:TPSR_Ref TObjectID="23944"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130571">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2860.456271 -1315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23945" ObjectName="SW-NH_XY.NH_XY_3626SW"/>
     <cge:Meas_Ref ObjectId="130571"/>
    <cge:TPSR_Ref TObjectID="23945"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130598">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2127.975248 -1180.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23948" ObjectName="SW-NH_XY.NH_XY_3611SW"/>
     <cge:Meas_Ref ObjectId="130598"/>
    <cge:TPSR_Ref TObjectID="23948"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2127.975248 -1309.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23949" ObjectName="SW-NH_XY.NH_XY_3616SW"/>
     <cge:Meas_Ref ObjectId="130599"/>
    <cge:TPSR_Ref TObjectID="23949"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270191">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -1355.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43626" ObjectName="SW-NH_XY.NH_XY_36167SW"/>
     <cge:Meas_Ref ObjectId="270191"/>
    <cge:TPSR_Ref TObjectID="43626"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130713">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1940.000000 -659.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23959" ObjectName="SW-NH_XY.NH_XY_0711SW"/>
     <cge:Meas_Ref ObjectId="130713"/>
    <cge:TPSR_Ref TObjectID="23959"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130714">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1940.000000 -517.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23960" ObjectName="SW-NH_XY.NH_XY_0716SW"/>
     <cge:Meas_Ref ObjectId="130714"/>
    <cge:TPSR_Ref TObjectID="23960"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130732">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2191.666667 -660.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23962" ObjectName="SW-NH_XY.NH_XY_0721SW"/>
     <cge:Meas_Ref ObjectId="130732"/>
    <cge:TPSR_Ref TObjectID="23962"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130751">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2481.333333 -662.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23965" ObjectName="SW-NH_XY.NH_XY_0731SW"/>
     <cge:Meas_Ref ObjectId="130751"/>
    <cge:TPSR_Ref TObjectID="23965"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130752">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2481.333333 -520.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23966" ObjectName="SW-NH_XY.NH_XY_0736SW"/>
     <cge:Meas_Ref ObjectId="130752"/>
    <cge:TPSR_Ref TObjectID="23966"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130770">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2732.000000 -658.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23968" ObjectName="SW-NH_XY.NH_XY_0741SW"/>
     <cge:Meas_Ref ObjectId="130770"/>
    <cge:TPSR_Ref TObjectID="23968"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130771">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2732.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23969" ObjectName="SW-NH_XY.NH_XY_0746SW"/>
     <cge:Meas_Ref ObjectId="130771"/>
    <cge:TPSR_Ref TObjectID="23969"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270264">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2841.000000 -984.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43624" ObjectName="SW-NH_XY.NH_XY_39017SW"/>
     <cge:Meas_Ref ObjectId="270264"/>
    <cge:TPSR_Ref TObjectID="43624"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130733">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2192.000000 -516.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23963" ObjectName="SW-NH_XY.NH_XY_0726SW"/>
     <cge:Meas_Ref ObjectId="130733"/>
    <cge:TPSR_Ref TObjectID="23963"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270189">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2881.000000 -1364.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43623" ObjectName="SW-NH_XY.NH_XY_36267SW"/>
     <cge:Meas_Ref ObjectId="270189"/>
    <cge:TPSR_Ref TObjectID="43623"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2145.000000 -1226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23950" ObjectName="SW-NH_XY.NH_XY_36117SW"/>
     <cge:Meas_Ref ObjectId="130600"/>
    <cge:TPSR_Ref TObjectID="23950"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270192">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.000000 -1293.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43625" ObjectName="SW-NH_XY.NH_XY_36160SW"/>
     <cge:Meas_Ref ObjectId="270192"/>
    <cge:TPSR_Ref TObjectID="43625"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-270190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2881.000000 -1299.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43622" ObjectName="SW-NH_XY.NH_XY_36260SW"/>
     <cge:Meas_Ref ObjectId="270190"/>
    <cge:TPSR_Ref TObjectID="43622"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130572">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2882.263158 -1238.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23946" ObjectName="SW-NH_XY.NH_XY_36217SW"/>
     <cge:Meas_Ref ObjectId="130572"/>
    <cge:TPSR_Ref TObjectID="23946"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-NH_XY.NH_XY_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1739,-735 3249,-735 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23979" ObjectName="BS-NH_XY.NH_XY_9IM"/>
    <cge:TPSR_Ref TObjectID="23979"/></metadata>
   <polyline fill="none" opacity="0" points="1739,-735 3249,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NH_XY.NH_XY_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,-1168 3082,-1168 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23978" ObjectName="BS-NH_XY.NH_XY_3IM"/>
    <cge:TPSR_Ref TObjectID="23978"/></metadata>
   <polyline fill="none" opacity="0" points="1871,-1168 3082,-1168 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-NH_XY.071Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1940.000000 -407.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33934" ObjectName="EC-NH_XY.071Ld"/>
    <cge:TPSR_Ref TObjectID="33934"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_XY.073Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2481.333333 -412.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33939" ObjectName="EC-NH_XY.073Ld"/>
    <cge:TPSR_Ref TObjectID="33939"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_XY.074Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2732.000000 -413.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33940" ObjectName="EC-NH_XY.074Ld"/>
    <cge:TPSR_Ref TObjectID="33940"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-NH_XY.072Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2192.000000 -406.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="33937" ObjectName="EC-NH_XY.072Ld"/>
    <cge:TPSR_Ref TObjectID="33937"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_165f500" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2537.975717 -1100.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16a6350" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2940.000000 -1363.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a14e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2205.000000 -1354.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16bc130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2844.000000 -955.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15d50d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2204.000000 -1225.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1663e90" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2205.000000 -1292.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_167c4a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2940.000000 -1298.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1603eb0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2939.736842 -1237.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-10KV" id="g_1574ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3184,-638 3184,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_1362f50@0" ObjectIDND1="g_165e390@0" ObjectIDZND0="23955@0" Pin0InfoVect0LinkObjId="SW-130638_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1362f50_0" Pin1InfoVect1LinkObjId="g_165e390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3184,-638 3184,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a58d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3185,-570 3185,-542 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_165e390@0" ObjectIDZND0="g_1644030@0" Pin0InfoVect0LinkObjId="g_1644030_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3185,-570 3185,-542 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_168ebd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2542,-1106 2519,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_165f500@0" ObjectIDZND0="23953@1" Pin0InfoVect0LinkObjId="SW-130627_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165f500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2542,-1106 2519,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_164c570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2483,-1106 2460,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23953@0" ObjectIDZND0="23952@x" ObjectIDZND1="23951@x" Pin0InfoVect0LinkObjId="SW-130626_0" Pin0InfoVect1LinkObjId="SW-130624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130627_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2483,-1106 2460,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16c6240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-1056 2460,-1002 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="23951@0" ObjectIDZND0="23971@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130624_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-1056 2460,-1002 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16bfa10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-922 2460,-865 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="23971@1" ObjectIDZND0="23956@1" Pin0InfoVect0LinkObjId="SW-130641_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16c6240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-922 2460,-865 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165dfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-838 2460,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23956@0" ObjectIDZND0="23957@1" Pin0InfoVect0LinkObjId="SW-130643_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-838 2460,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_165e1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-769 2460,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23957@0" ObjectIDZND0="23979@0" Pin0InfoVect0LinkObjId="g_15e8110_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130643_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-769 2460,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15e8110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3184,-698 3184,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23955@1" ObjectIDZND0="23979@0" Pin0InfoVect0LinkObjId="g_165e1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130638_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3184,-698 3184,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15e8db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-990 2909,-962 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_15f4000@1" ObjectIDZND0="g_15efab0@0" Pin0InfoVect0LinkObjId="g_15efab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f4000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-990 2909,-962 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15f3e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-1041 2909,-1059 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_15f4000@0" ObjectIDND1="g_15e85e0@0" ObjectIDND2="43624@x" ObjectIDZND0="23954@0" Pin0InfoVect0LinkObjId="SW-130633_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15f4000_0" Pin1InfoVect1LinkObjId="g_15e85e0_0" Pin1InfoVect2LinkObjId="SW-270264_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-1041 2909,-1059 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f5570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3229,-613 3229,-638 3184,-638 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_1362f50@0" ObjectIDZND0="23955@x" ObjectIDZND1="g_165e390@0" Pin0InfoVect0LinkObjId="SW-130638_0" Pin0InfoVect1LinkObjId="g_165e390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1362f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3229,-613 3229,-638 3184,-638 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a5b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3184,-638 3184,-614 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="23955@x" ObjectIDND1="g_1362f50@0" ObjectIDZND0="g_165e390@1" Pin0InfoVect0LinkObjId="g_165e390_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130638_0" Pin1InfoVect1LinkObjId="g_1362f50_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3184,-638 3184,-614 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a5d80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-1022 2909,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_15f4000@0" ObjectIDZND0="23954@x" ObjectIDZND1="g_15e85e0@0" ObjectIDZND2="43624@x" Pin0InfoVect0LinkObjId="SW-130633_0" Pin0InfoVect1LinkObjId="g_15e85e0_0" Pin0InfoVect2LinkObjId="SW-270264_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f4000_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-1022 2909,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a5f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-1041 2958,-1041 2958,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="23954@x" ObjectIDND1="g_15f4000@0" ObjectIDND2="43624@x" ObjectIDZND0="g_15e85e0@0" Pin0InfoVect0LinkObjId="g_15e85e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130633_0" Pin1InfoVect1LinkObjId="g_15f4000_0" Pin1InfoVect2LinkObjId="SW-270264_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-1041 2958,-1041 2958,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16a6160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2944,-1369 2922,-1369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_16a6350@0" ObjectIDZND0="43623@1" Pin0InfoVect0LinkObjId="SW-270189_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2944,-1369 2922,-1369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_160be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1185 2137,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23948@0" ObjectIDZND0="23978@0" Pin0InfoVect0LinkObjId="g_15da480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1185 2137,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15a12f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2209,-1360 2187,-1360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15a14e0@0" ObjectIDZND0="43626@1" Pin0InfoVect0LinkObjId="SW-270191_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15a14e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2209,-1360 2187,-1360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162c540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-1117 2460,-1106 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23952@0" ObjectIDZND0="23953@x" ObjectIDZND1="23951@x" Pin0InfoVect0LinkObjId="SW-130627_0" Pin0InfoVect1LinkObjId="SW-130624_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130626_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-1117 2460,-1106 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_162c760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-1106 2460,-1083 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23953@x" ObjectIDND1="23952@x" ObjectIDZND0="23951@1" Pin0InfoVect0LinkObjId="SW-130624_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130627_0" Pin1InfoVect1LinkObjId="SW-130626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-1106 2460,-1083 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1620310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1949,-735 1949,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23979@0" ObjectIDZND0="23959@1" Pin0InfoVect0LinkObjId="SW-130713_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1949,-735 1949,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1622220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1949,-664 1949,-619 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23959@0" ObjectIDZND0="23958@1" Pin0InfoVect0LinkObjId="SW-130711_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130713_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1949,-664 1949,-619 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16225b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1949,-592 1949,-558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23958@0" ObjectIDZND0="23960@1" Pin0InfoVect0LinkObjId="SW-130714_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130711_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1949,-592 1949,-558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1623380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1949,-491 1949,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_16227d0@0" ObjectIDND1="23960@x" ObjectIDZND0="33934@0" Pin0InfoVect0LinkObjId="EC-NH_XY.071Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16227d0_0" Pin1InfoVect1LinkObjId="SW-130714_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1949,-491 1949,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16235a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1982,-491 1949,-491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_16227d0@0" ObjectIDZND0="33934@x" ObjectIDZND1="23960@x" Pin0InfoVect0LinkObjId="EC-NH_XY.071Ld_0" Pin0InfoVect1LinkObjId="SW-130714_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16227d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1982,-491 1949,-491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16237c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1949,-491 1949,-522 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33934@x" ObjectIDND1="g_16227d0@0" ObjectIDZND0="23960@0" Pin0InfoVect0LinkObjId="SW-130714_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_XY.071Ld_0" Pin1InfoVect1LinkObjId="g_16227d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1949,-491 1949,-522 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15da9b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-735 2201,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23979@0" ObjectIDZND0="23962@1" Pin0InfoVect0LinkObjId="SW-130732_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-735 2201,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15dabd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-665 2201,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23962@0" ObjectIDZND0="23961@1" Pin0InfoVect0LinkObjId="SW-130730_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130732_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-665 2201,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a1b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2490,-735 2490,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23979@0" ObjectIDZND0="23965@1" Pin0InfoVect0LinkObjId="SW-130751_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2490,-735 2490,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16a3a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2490,-667 2490,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23965@0" ObjectIDZND0="23964@1" Pin0InfoVect0LinkObjId="SW-130749_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130751_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2490,-667 2490,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1651cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2490,-595 2490,-561 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23964@0" ObjectIDZND0="23966@1" Pin0InfoVect0LinkObjId="SW-130752_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130749_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2490,-595 2490,-561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1653080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2490,-494 2490,-439 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1651ed0@0" ObjectIDND1="23966@x" ObjectIDZND0="33939@0" Pin0InfoVect0LinkObjId="EC-NH_XY.073Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1651ed0_0" Pin1InfoVect1LinkObjId="SW-130752_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2490,-494 2490,-439 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16532a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2523,-494 2490,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1651ed0@0" ObjectIDZND0="33939@x" ObjectIDZND1="23966@x" Pin0InfoVect0LinkObjId="EC-NH_XY.073Ld_0" Pin0InfoVect1LinkObjId="SW-130752_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1651ed0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2523,-494 2490,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16534c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2490,-494 2490,-525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33939@x" ObjectIDND1="g_1651ed0@0" ObjectIDZND0="23966@0" Pin0InfoVect0LinkObjId="SW-130752_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_XY.073Ld_0" Pin1InfoVect1LinkObjId="g_1651ed0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2490,-494 2490,-525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16ce9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2741,-735 2741,-699 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23979@0" ObjectIDZND0="23968@1" Pin0InfoVect0LinkObjId="SW-130770_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2741,-735 2741,-699 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16d0840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2741,-663 2741,-618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="23968@0" ObjectIDZND0="23967@1" Pin0InfoVect0LinkObjId="SW-130768_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130770_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2741,-663 2741,-618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f0e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2741,-591 2741,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23967@0" ObjectIDZND0="23969@1" Pin0InfoVect0LinkObjId="SW-130771_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130768_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2741,-591 2741,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f2220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2741,-490 2741,-440 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_15f1070@0" ObjectIDND1="23969@x" ObjectIDZND0="33940@0" Pin0InfoVect0LinkObjId="EC-NH_XY.074Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15f1070_0" Pin1InfoVect1LinkObjId="SW-130771_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2741,-490 2741,-440 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f2440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2774,-490 2741,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_15f1070@0" ObjectIDZND0="33940@x" ObjectIDZND1="23969@x" Pin0InfoVect0LinkObjId="EC-NH_XY.074Ld_0" Pin0InfoVect1LinkObjId="SW-130771_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15f1070_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2774,-490 2741,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15f2660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2741,-490 2741,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33940@x" ObjectIDND1="g_15f1070@0" ObjectIDZND0="23969@0" Pin0InfoVect0LinkObjId="SW-130771_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_XY.074Ld_0" Pin1InfoVect1LinkObjId="g_15f1070_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2741,-490 2741,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1591180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-1168 2909,-1091 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23978@0" ObjectIDZND0="23954@1" Pin0InfoVect0LinkObjId="SW-130633_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160be00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-1168 2909,-1091 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16bbc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2909,-1041 2850,-1041 2850,-1025 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="23954@x" ObjectIDND1="g_15f4000@0" ObjectIDND2="g_15e85e0@0" ObjectIDZND0="43624@0" Pin0InfoVect0LinkObjId="SW-270264_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-130633_0" Pin1InfoVect1LinkObjId="g_15f4000_0" Pin1InfoVect2LinkObjId="g_15e85e0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2909,-1041 2850,-1041 2850,-1025 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16bbed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2850,-989 2850,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43624@1" ObjectIDZND0="g_16bc130@0" Pin0InfoVect0LinkObjId="g_16bc130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270264_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2850,-989 2850,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16072d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-591 2201,-557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="23961@0" ObjectIDZND0="23963@1" Pin0InfoVect0LinkObjId="SW-130733_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130730_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-591 2201,-557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_16082a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-490 2201,-433 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_1607530@0" ObjectIDND1="23963@x" ObjectIDZND0="33937@0" Pin0InfoVect0LinkObjId="EC-NH_XY.072Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1607530_0" Pin1InfoVect1LinkObjId="SW-130733_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-490 2201,-433 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1608500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2234,-490 2201,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_1607530@0" ObjectIDZND0="33937@x" ObjectIDZND1="23963@x" Pin0InfoVect0LinkObjId="EC-NH_XY.072Ld_0" Pin0InfoVect1LinkObjId="SW-130733_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1607530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2234,-490 2201,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1608760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2201,-490 2201,-521 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="33937@x" ObjectIDND1="g_1607530@0" ObjectIDZND0="23963@0" Pin0InfoVect0LinkObjId="SW-130733_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-NH_XY.072Ld_0" Pin1InfoVect1LinkObjId="g_1607530_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2201,-490 2201,-521 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1518400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3008,-599 3008,-622 2964,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_15b3590@0" ObjectIDZND0="g_15178d0@0" ObjectIDZND1="g_1519950@0" Pin0InfoVect0LinkObjId="g_15178d0_0" Pin0InfoVect1LinkObjId="g_1519950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15b3590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3008,-599 3008,-622 2964,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1518630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2964,-735 2964,-677 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="lightningRod" ObjectIDND0="23979@0" ObjectIDZND0="g_15178d0@1" Pin0InfoVect0LinkObjId="g_15178d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165e1a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2964,-735 2964,-677 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_15196f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2964,-632 2964,-622 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="g_15178d0@0" ObjectIDZND0="g_15b3590@0" ObjectIDZND1="g_1519950@0" Pin0InfoVect0LinkObjId="g_15b3590_0" Pin0InfoVect1LinkObjId="g_1519950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15178d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2964,-632 2964,-622 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2964,-622 2964,-606 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15b3590@0" ObjectIDND1="g_15178d0@0" ObjectIDZND0="g_1519950@1" Pin0InfoVect0LinkObjId="g_1519950_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_15b3590_0" Pin1InfoVect1LinkObjId="g_15178d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2964,-622 2964,-606 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_151a5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2964,-555 2964,-524 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_1519950@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_165f500_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1519950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2964,-555 2964,-524 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15817d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2886,-1369 2869,-1369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43623@0" ObjectIDZND0="23945@x" ObjectIDZND1="g_16813c0@0" ObjectIDZND2="g_1667d80@0" Pin0InfoVect0LinkObjId="SW-130571_0" Pin0InfoVect1LinkObjId="g_16813c0_0" Pin0InfoVect2LinkObjId="g_1667d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270189_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2886,-1369 2869,-1369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1582140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1356 2869,-1369 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="23945@1" ObjectIDZND0="43623@x" ObjectIDZND1="g_16813c0@0" ObjectIDZND2="g_1667d80@0" Pin0InfoVect0LinkObjId="SW-270189_0" Pin0InfoVect1LinkObjId="g_16813c0_0" Pin0InfoVect2LinkObjId="g_1667d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130571_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1356 2869,-1369 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d4e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2208,-1231 2186,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_15d50d0@0" ObjectIDZND0="23950@1" Pin0InfoVect0LinkObjId="SW-130600_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15d50d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2208,-1231 2186,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d5b60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2150,-1231 2136,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="23950@0" ObjectIDZND0="23947@x" ObjectIDZND1="23948@x" Pin0InfoVect0LinkObjId="SW-130596_0" Pin0InfoVect1LinkObjId="SW-130598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2150,-1231 2136,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1661340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1256 2137,-1231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23947@0" ObjectIDZND0="23950@x" ObjectIDZND1="23948@x" Pin0InfoVect0LinkObjId="SW-130600_0" Pin0InfoVect1LinkObjId="SW-130598_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1256 2137,-1231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16615a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1231 2137,-1221 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23950@x" ObjectIDND1="23947@x" ObjectIDZND0="23948@1" Pin0InfoVect0LinkObjId="SW-130598_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130600_0" Pin1InfoVect1LinkObjId="SW-130596_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1231 2137,-1221 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1663c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2209,-1298 2187,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1663e90@0" ObjectIDZND0="43625@1" Pin0InfoVect0LinkObjId="SW-270192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1663e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2209,-1298 2187,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1664920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-1298 2137,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43625@0" ObjectIDZND0="23949@x" ObjectIDZND1="23947@x" Pin0InfoVect0LinkObjId="SW-130599_0" Pin0InfoVect1LinkObjId="SW-130596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-1298 2137,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1685590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1314 2137,-1298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23949@0" ObjectIDZND0="43625@x" ObjectIDZND1="23947@x" Pin0InfoVect0LinkObjId="SW-270192_0" Pin0InfoVect1LinkObjId="SW-130596_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1314 2137,-1298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16857f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1298 2137,-1283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43625@x" ObjectIDND1="23949@x" ObjectIDZND0="23947@1" Pin0InfoVect0LinkObjId="SW-130596_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270192_0" Pin1InfoVect1LinkObjId="SW-130599_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1298 2137,-1283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1686e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2151,-1360 2137,-1360 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43626@0" ObjectIDZND0="23949@x" ObjectIDZND1="g_1685a50@0" ObjectIDZND2="g_15a1c90@0" Pin0InfoVect0LinkObjId="SW-130599_0" Pin0InfoVect1LinkObjId="g_1685a50_0" Pin0InfoVect2LinkObjId="g_15a1c90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270191_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2151,-1360 2137,-1360 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16870f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1360 2137,-1350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="43626@x" ObjectIDND1="g_1685a50@0" ObjectIDND2="g_15a1c90@0" ObjectIDZND0="23949@1" Pin0InfoVect0LinkObjId="SW-130599_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-270191_0" Pin1InfoVect1LinkObjId="g_1685a50_0" Pin1InfoVect2LinkObjId="g_15a1c90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1360 2137,-1350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1687350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1457 2214,-1457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_1685a50@0" ObjectIDND1="43626@x" ObjectIDND2="23949@x" ObjectIDZND0="g_15a1c90@0" Pin0InfoVect0LinkObjId="g_15a1c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1685a50_0" Pin1InfoVect1LinkObjId="SW-270191_0" Pin1InfoVect2LinkObjId="SW-130599_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1457 2214,-1457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1687e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1457 2137,-1497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_15a1c90@0" ObjectIDND1="g_1685a50@0" ObjectIDND2="43626@x" ObjectIDZND0="38080@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_15a1c90_0" Pin1InfoVect1LinkObjId="g_1685a50_0" Pin1InfoVect2LinkObjId="SW-270191_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1457 2137,-1497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167a290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-1427 2058,-1441 2137,-1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_1685a50@0" ObjectIDZND0="43626@x" ObjectIDZND1="23949@x" ObjectIDZND2="g_15a1c90@0" Pin0InfoVect0LinkObjId="SW-270191_0" Pin0InfoVect1LinkObjId="SW-130599_0" Pin0InfoVect2LinkObjId="g_15a1c90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1685a50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2058,-1427 2058,-1441 2137,-1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167ad80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1360 2137,-1441 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="43626@x" ObjectIDND1="23949@x" ObjectIDZND0="g_1685a50@0" ObjectIDZND1="g_15a1c90@0" ObjectIDZND2="38080@1" Pin0InfoVect0LinkObjId="g_1685a50_0" Pin0InfoVect1LinkObjId="g_15a1c90_0" Pin0InfoVect2LinkObjId="g_1687e40_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270191_0" Pin1InfoVect1LinkObjId="SW-130599_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1360 2137,-1441 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167afe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2137,-1441 2137,-1457 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="g_1685a50@0" ObjectIDND1="43626@x" ObjectIDND2="23949@x" ObjectIDZND0="g_15a1c90@0" ObjectIDZND1="38080@1" Pin0InfoVect0LinkObjId="g_15a1c90_0" Pin0InfoVect1LinkObjId="g_1687e40_1" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1685a50_0" Pin1InfoVect1LinkObjId="SW-270191_0" Pin1InfoVect2LinkObjId="SW-130599_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2137,-1441 2137,-1457 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167b240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2058,-1364 2058,-1382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_16880a0@0" ObjectIDZND0="g_1685a50@1" Pin0InfoVect0LinkObjId="g_1685a50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16880a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2058,-1364 2058,-1382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167c2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2944,-1304 2922,-1304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_167c4a0@0" ObjectIDZND0="43622@1" Pin0InfoVect0LinkObjId="SW-270190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_167c4a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2944,-1304 2922,-1304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_167cd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2886,-1304 2869,-1304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="43622@0" ObjectIDZND0="23945@x" ObjectIDZND1="23943@x" Pin0InfoVect0LinkObjId="SW-130571_0" Pin0InfoVect1LinkObjId="SW-130568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-270190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2886,-1304 2869,-1304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1603790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1320 2869,-1304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23945@0" ObjectIDZND0="43622@x" ObjectIDZND1="23943@x" Pin0InfoVect0LinkObjId="SW-270190_0" Pin0InfoVect1LinkObjId="SW-130568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130571_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1320 2869,-1304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16039f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1304 2869,-1289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="43622@x" ObjectIDND1="23945@x" ObjectIDZND0="23943@1" Pin0InfoVect0LinkObjId="SW-130568_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270190_0" Pin1InfoVect1LinkObjId="SW-130571_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1304 2869,-1289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1603c50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2944,-1243 2923,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1603eb0@0" ObjectIDZND0="23946@1" Pin0InfoVect0LinkObjId="SW-130572_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1603eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2944,-1243 2923,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1604940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2887,-1243 2869,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23946@0" ObjectIDZND0="23944@x" ObjectIDZND1="23943@x" Pin0InfoVect0LinkObjId="SW-130570_0" Pin0InfoVect1LinkObjId="SW-130568_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130572_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2887,-1243 2869,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_16678c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1262 2869,-1243 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23943@0" ObjectIDZND0="23946@x" ObjectIDZND1="23944@x" Pin0InfoVect0LinkObjId="SW-130572_0" Pin0InfoVect1LinkObjId="SW-130570_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130568_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1262 2869,-1243 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1667b20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1243 2869,-1227 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23946@x" ObjectIDND1="23943@x" ObjectIDZND0="23944@1" Pin0InfoVect0LinkObjId="SW-130570_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-130572_0" Pin1InfoVect1LinkObjId="SW-130568_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1243 2869,-1227 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15eda80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2791,-1517 2791,-1531 2869,-1531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1667d80@0" ObjectIDZND0="g_16813c0@0" ObjectIDZND1="43623@x" ObjectIDZND2="23945@x" Pin0InfoVect0LinkObjId="g_16813c0_0" Pin0InfoVect1LinkObjId="SW-270189_0" Pin0InfoVect2LinkObjId="SW-130571_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1667d80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2791,-1517 2791,-1531 2869,-1531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15edce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2791,-1454 2791,-1472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_15eceb0@0" ObjectIDZND0="g_1667d80@1" Pin0InfoVect0LinkObjId="g_1667d80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_15eceb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2791,-1454 2791,-1472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15d7b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2460,-1168 2460,-1151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23978@0" ObjectIDZND0="23952@1" Pin0InfoVect0LinkObjId="SW-130626_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_160be00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2460,-1168 2460,-1151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15da480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1192 2869,-1168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="23944@0" ObjectIDZND0="23978@0" Pin0InfoVect0LinkObjId="g_160be00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-130570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1192 2869,-1168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15bc390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2731,-1377 2731,-1409 2869,-1409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_16813c0@1" ObjectIDZND0="43623@x" ObjectIDZND1="23945@x" ObjectIDZND2="g_1667d80@0" Pin0InfoVect0LinkObjId="SW-270189_0" Pin0InfoVect1LinkObjId="SW-130571_0" Pin0InfoVect2LinkObjId="g_1667d80_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16813c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2731,-1377 2731,-1409 2869,-1409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15bcd90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1369 2869,-1409 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="43623@x" ObjectIDND1="23945@x" ObjectIDZND0="g_16813c0@0" ObjectIDZND1="g_1667d80@0" ObjectIDZND2="g_16a6a80@0" Pin0InfoVect0LinkObjId="g_16813c0_0" Pin0InfoVect1LinkObjId="g_1667d80_0" Pin0InfoVect2LinkObjId="g_16a6a80_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-270189_0" Pin1InfoVect1LinkObjId="SW-130571_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1369 2869,-1409 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_15bd9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2731,-1291 2731,-1333 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_16813c0@0" Pin0InfoVect0LinkObjId="g_16813c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_165f500_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2731,-1291 2731,-1333 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_169c310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1409 2869,-1535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="g_16813c0@0" ObjectIDND1="43623@x" ObjectIDND2="23945@x" ObjectIDZND0="g_1667d80@0" ObjectIDZND1="g_16a6a80@0" ObjectIDZND2="38077@1" Pin0InfoVect0LinkObjId="g_1667d80_0" Pin0InfoVect1LinkObjId="g_16a6a80_0" Pin0InfoVect2LinkObjId="g_1635180_1" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16813c0_0" Pin1InfoVect1LinkObjId="SW-270189_0" Pin1InfoVect2LinkObjId="SW-130571_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1409 2869,-1535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1635180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1550 2869,-1570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="g_16a6a80@0" ObjectIDND1="g_1667d80@0" ObjectIDND2="g_16813c0@0" ObjectIDZND0="38077@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_16a6a80_0" Pin1InfoVect1LinkObjId="g_1667d80_0" Pin1InfoVect2LinkObjId="g_16813c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1550 2869,-1570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1635c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2921,-1550 2869,-1550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_16a6a80@0" ObjectIDZND0="g_1667d80@0" ObjectIDZND1="g_16813c0@0" ObjectIDZND2="43623@x" Pin0InfoVect0LinkObjId="g_1667d80_0" Pin0InfoVect1LinkObjId="g_16813c0_0" Pin0InfoVect2LinkObjId="SW-270189_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_16a6a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2921,-1550 2869,-1550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1635e70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2869,-1550 2869,-1531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_16a6a80@0" ObjectIDND1="38077@1" ObjectIDZND0="g_1667d80@0" ObjectIDZND1="g_16813c0@0" ObjectIDZND2="43623@x" Pin0InfoVect0LinkObjId="g_1667d80_0" Pin0InfoVect1LinkObjId="g_16813c0_0" Pin0InfoVect2LinkObjId="SW-270189_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_16a6a80_0" Pin1InfoVect1LinkObjId="g_1635180_1" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2869,-1550 2869,-1531 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="2460" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="1949" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23978" cx="2909" cy="-1170" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="3184" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="2201" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="2490" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="2741" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23979" cx="2964" cy="-735" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23978" cx="2137" cy="-1168" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23978" cx="2461" cy="-1168" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23978" cx="2869" cy="-1168" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-130482" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1616.000000 -1290.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23920" ObjectName="DYN-NH_XY"/>
     <cge:Meas_Ref ObjectId="130482"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_b9cb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1872.000000 -770.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1618180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1357.000000 -1235.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_16d29c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -797.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15983b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1982.000000 -1191.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f3b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2872.000000 -885.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f3b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2872.000000 -885.000000) translate(0,33)">电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15f45b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2623.000000 -1281.000000) translate(0,15)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_15f48e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1504.000000 -1375.000000) translate(0,16)">徐营变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15f4be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3194.000000 -689.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1850.000000 -1273.000000) translate(0,12)">F(Hz):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e6f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1958.000000 -613.000000) translate(0,12)">071</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e71d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.000000 -689.000000) translate(0,12)">0711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e73d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1956.000000 -547.000000) translate(0,12)">0716</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e75d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2208.000000 -690.000000) translate(0,12)">0721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e77d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2499.000000 -616.000000) translate(0,12)">073</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e7aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2497.000000 -692.000000) translate(0,12)">0731</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e7ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2497.000000 -550.000000) translate(0,12)">0736</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e7f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2750.000000 -612.000000) translate(0,12)">074</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fdfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2748.000000 -688.000000) translate(0,12)">0741</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fe1e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2748.000000 -546.000000) translate(0,12)">0746</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fe420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -1075.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fe660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2466.000000 -1142.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fe8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2468.000000 -857.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15feae0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2466.000000 -791.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_15feee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -1361.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_16c6970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1630.000000 -1398.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1600720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2101.000000 -1279.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_15eab80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1360.000000 -1008.000000) translate(0,20)">公用间隔</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_165eef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2313.000000 -940.000000) translate(0,15)">SZ9-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_165eef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2313.000000 -940.000000) translate(0,33)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15ec7e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1898.000000 -398.000000) translate(0,15)">上外河线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_15ecab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2457.000000 -400.000000) translate(0,15)">梅子树线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_165fe90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2729.000000 -406.000000) translate(0,14)">喇石线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_166aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1350.000000 -407.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_166aa20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1350.000000 -407.000000) translate(0,38)">心变运一班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_1625f90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -389.500000) translate(0,16)">13908784302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_158fc50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1479.000000 -441.000000) translate(0,16)">7312631</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1590740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2317.000000 -979.000000) translate(0,12)">35kV1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1642ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2210.000000 -612.000000) translate(0,12)">072</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1643310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2208.000000 -546.000000) translate(0,12)">0726</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1643cd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -489.000000) translate(0,15)">10kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1581080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2938.000000 -416.000000) translate(0,15)">2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_167b4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2033.000000 -1320.000000) translate(0,12)">线路TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15edf40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2092.000000 -1339.000000) translate(0,12)">3616</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ee430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2160.000000 -1386.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ee670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2160.000000 -1324.000000) translate(0,12)">36160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ee8b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2091.000000 -1210.000000) translate(0,12)">3611</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eeaf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2160.000000 -1257.000000) translate(0,12)">36117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eed30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2827.000000 -1218.000000) translate(0,12)">3621</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eef70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2815.000000 -1348.000000) translate(0,12)">3626</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ef1b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -1395.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ef3f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2895.000000 -1330.000000) translate(0,12)">36260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ef630" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2896.000000 -1269.000000) translate(0,12)">36217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ef870" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2789.000000 -1016.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15bbaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2827.000000 -1287.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169b0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2109.000000 -1571.000000) translate(0,12)">35kV徐三线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169c750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2844.000000 -1649.000000) translate(0,12)">35kV徐营线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="17" graphid="g_169cec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2180.000000 -389.000000) translate(0,14)">备用一</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_169d9b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1795.500000 -1383.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_169e7f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2916.000000 -1080.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1632430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2491.000000 -1128.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16366c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2728.000000 -1461.000000) translate(0,12)">线路TV</text>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-130624">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2450.788423 -1048.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23951" ObjectName="SW-NH_XY.NH_XY_301BK"/>
     <cge:Meas_Ref ObjectId="130624"/>
    <cge:TPSR_Ref TObjectID="23951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130641">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2450.788423 -830.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23956" ObjectName="SW-NH_XY.NH_XY_001BK"/>
     <cge:Meas_Ref ObjectId="130641"/>
    <cge:TPSR_Ref TObjectID="23956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130596">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2127.975248 -1248.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23947" ObjectName="SW-NH_XY.NH_XY_361BK"/>
     <cge:Meas_Ref ObjectId="130596"/>
    <cge:TPSR_Ref TObjectID="23947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130711">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1939.670659 -584.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23958" ObjectName="SW-NH_XY.NH_XY_071BK"/>
     <cge:Meas_Ref ObjectId="130711"/>
    <cge:TPSR_Ref TObjectID="23958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130749">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2481.003992 -587.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23964" ObjectName="SW-NH_XY.NH_XY_073BK"/>
     <cge:Meas_Ref ObjectId="130749"/>
    <cge:TPSR_Ref TObjectID="23964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130768">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2731.670659 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23967" ObjectName="SW-NH_XY.NH_XY_074BK"/>
     <cge:Meas_Ref ObjectId="130768"/>
    <cge:TPSR_Ref TObjectID="23967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130730">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2191.670659 -583.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23961" ObjectName="SW-NH_XY.NH_XY_072BK"/>
     <cge:Meas_Ref ObjectId="130730"/>
    <cge:TPSR_Ref TObjectID="23961"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-130568">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2860.000000 -1254.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23943" ObjectName="SW-NH_XY.NH_XY_362BK"/>
     <cge:Meas_Ref ObjectId="130568"/>
    <cge:TPSR_Ref TObjectID="23943"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_XY" endPointId="0" endStationName="CX_SJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xusan" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2137,-1548 2137,-1493 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38080" ObjectName="AC-35kV.LN_xusan"/>
    <cge:TPSR_Ref TObjectID="38080_SS-196"/></metadata>
   <polyline fill="none" opacity="0" points="2137,-1548 2137,-1493 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_NH" endPointId="0" endStationName="NH_XY" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_xuying" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="2869,-1624 2869,-1569 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38077" ObjectName="AC-35kV.LN_xuying"/>
    <cge:TPSR_Ref TObjectID="38077_SS-196"/></metadata>
   <polyline fill="none" opacity="0" points="2869,-1624 2869,-1569 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1362f50">
    <use class="BV-10KV" transform="matrix(-0.925926 -0.000000 0.000000 -1.000000 3235.790123 -559.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_165e390">
    <use class="BV-10KV" transform="matrix(0.925926 -0.000000 0.000000 -1.000000 3180.000000 -564.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15e85e0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2964.736799 -963.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f4000">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2900.350834 -985.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16a6a80">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2975.456271 -1557.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16813c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2726.456271 -1327.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15a1c90">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 -1.000000 -0.000000 2267.975248 -1464.333333)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16227d0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1989.003992 -437.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1651ed0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2530.337325 -440.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15f1070">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2781.003992 -436.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1607530">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2241.003992 -436.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15b3590">
    <use class="BV-10KV" transform="matrix(-0.906225 -0.000000 0.000000 -0.925926 3014.666929 -548.925926)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15178d0">
    <use class="BV-10KV" transform="matrix(0.925926 -0.000000 0.000000 -1.000000 2960.000000 -627.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1519950">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -0.644444 2959.000000 -552.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1685a50">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2062.543729 -1432.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_16880a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2046.000000 -1328.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1667d80">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 2795.543729 -1522.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15eceb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2779.000000 -1418.000000)" xlink:href="#lightningRod:shape170"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130515" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2301.000000 -1299.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130515" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23947"/>
     <cge:Term_Ref ObjectID="33759"/>
    <cge:TPSR_Ref TObjectID="23947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130516" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2301.000000 -1299.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130516" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23947"/>
     <cge:Term_Ref ObjectID="33759"/>
    <cge:TPSR_Ref TObjectID="23947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130512" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2301.000000 -1299.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130512" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23947"/>
     <cge:Term_Ref ObjectID="33759"/>
    <cge:TPSR_Ref TObjectID="23947"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130542" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 -855.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130542" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23956"/>
     <cge:Term_Ref ObjectID="33777"/>
    <cge:TPSR_Ref TObjectID="23956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130538" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2588.000000 -855.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130538" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23956"/>
     <cge:Term_Ref ObjectID="33777"/>
    <cge:TPSR_Ref TObjectID="23956"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130547" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -363.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130547" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23958"/>
     <cge:Term_Ref ObjectID="33781"/>
    <cge:TPSR_Ref TObjectID="23958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130548" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -363.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130548" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23958"/>
     <cge:Term_Ref ObjectID="33781"/>
    <cge:TPSR_Ref TObjectID="23958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130544" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1962.000000 -363.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130544" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23958"/>
     <cge:Term_Ref ObjectID="33781"/>
    <cge:TPSR_Ref TObjectID="23958"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130559" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2504.000000 -362.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130559" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23964"/>
     <cge:Term_Ref ObjectID="33793"/>
    <cge:TPSR_Ref TObjectID="23964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130560" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2504.000000 -362.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130560" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23964"/>
     <cge:Term_Ref ObjectID="33793"/>
    <cge:TPSR_Ref TObjectID="23964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130556" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2504.000000 -362.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130556" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23964"/>
     <cge:Term_Ref ObjectID="33793"/>
    <cge:TPSR_Ref TObjectID="23964"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130565" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2768.000000 -382.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130565" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23967"/>
     <cge:Term_Ref ObjectID="33799"/>
    <cge:TPSR_Ref TObjectID="23967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130566" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2768.000000 -382.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130566" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23967"/>
     <cge:Term_Ref ObjectID="33799"/>
    <cge:TPSR_Ref TObjectID="23967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130562" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2768.000000 -382.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130562" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23967"/>
     <cge:Term_Ref ObjectID="33799"/>
    <cge:TPSR_Ref TObjectID="23967"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130528" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 -1087.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130528" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23951"/>
     <cge:Term_Ref ObjectID="33767"/>
    <cge:TPSR_Ref TObjectID="23951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130529" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 -1087.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130529" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23951"/>
     <cge:Term_Ref ObjectID="33767"/>
    <cge:TPSR_Ref TObjectID="23951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130525" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2609.000000 -1087.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130525" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23951"/>
     <cge:Term_Ref ObjectID="33767"/>
    <cge:TPSR_Ref TObjectID="23951"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-130509" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3094.000000 -1312.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130509" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23943"/>
     <cge:Term_Ref ObjectID="33751"/>
    <cge:TPSR_Ref TObjectID="23943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-130510" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3094.000000 -1312.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130510" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23943"/>
     <cge:Term_Ref ObjectID="33751"/>
    <cge:TPSR_Ref TObjectID="23943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-130506" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3094.000000 -1312.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130506" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23943"/>
     <cge:Term_Ref ObjectID="33751"/>
    <cge:TPSR_Ref TObjectID="23943"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="1" id="ME-270164" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -972.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270164" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23971"/>
     <cge:Term_Ref ObjectID="33810"/>
    <cge:TPSR_Ref TObjectID="23971"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="1" id="ME-270163" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2573.000000 -972.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="270163" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="23971"/>
     <cge:Term_Ref ObjectID="33810"/>
    <cge:TPSR_Ref TObjectID="23971"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/></g>
   <g href="nh_索引_接线图.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/></g>
   <g href="cx_配调_配网接线图35_南华.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1619" y="-1368"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="1619" y="-1407"/></g>
   <g href="35kV徐营变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="24" qtmmishow="hidden" width="96" x="1360" y="-1008"/></g>
   <g href="35kV徐营变35kV徐三线361间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2101" y="-1279"/></g>
   <g href="35kV徐营变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="85" x="2316" y="-981"/></g>
   <g href="35kV徐营变35kV徐营线362间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2827" y="-1287"/></g>
   <g href="35kV徐营变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2468" y="-1075"/></g>
   <g href="35kV徐营变10kV上外河线071间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="1958" y="-613"/></g>
   <g href="35kV徐营变10kV备用一线072间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="2210" y="-612"/></g>
   <g href="35kV徐营变10kV梅子树线073间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2499" y="-616"/></g>
   <g href="35kV徐营变10kV喇石线074间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="2750" y="-612"/></g>
   <g href="AVC徐营站.svg" style="fill-opacity:0"><rect height="44" qtmmishow="hidden" width="73" x="1778" y="-1395"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_16b2670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1905.000000 364.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15f3530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1894.000000 349.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15f3760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1919.000000 334.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157d8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2553.000000 1087.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157dae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2542.000000 1072.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157dcc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2567.000000 1057.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157df90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2527.000000 869.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2516.000000 854.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2541.000000 839.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157e9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3028.000000 1311.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157ebd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3017.000000 1296.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_157ee10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3042.000000 1281.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fda80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1843.000000 1250.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15fdcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1843.000000 1235.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e54e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1843.000000 1219.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e56f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1849.000000 1203.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e5930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1835.000000 1187.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e5c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 815.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e5ed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 800.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e6110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1717.000000 784.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e6350" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.000000 769.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15e6590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1709.000000 753.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eb190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2445.000000 362.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eb610" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2434.000000 347.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15eb850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2459.000000 332.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ebb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2709.000000 382.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_15ebde0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2698.000000 367.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166a010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2723.000000 352.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166a340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2243.000000 1301.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166a5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2232.000000 1286.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_166a7e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2257.000000 1271.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1632b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2531.000000 974.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1633240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2530.000000 958.000000) translate(0,12)">温度:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15ac9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2408.000000 982.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2415" cy="975" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15acfd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2191.000000 1571.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2198" cy="1564" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15ad690" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2926.000000 1648.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2933" cy="1641" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15adb70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1977.000000 395.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="1984" cy="388" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15ae050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2239.000000 388.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2246" cy="381" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15ae530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2536.000000 398.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2543" cy="391" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_15aea10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2786.000000 405.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="2793" cy="398" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2995.000000 -431.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 2995.000000 -431.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2700.000000 -1198.000000)" xlink:href="#transformer2:shape42_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2700.000000 -1198.000000)" xlink:href="#transformer2:shape42_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-NH_XY.NH_XY_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33809"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2422.000000 -917.000000)" xlink:href="#transformer2:shape13_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2422.000000 -917.000000)" xlink:href="#transformer2:shape13_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23971" ObjectName="TF-NH_XY.NH_XY_1T"/>
    <cge:TPSR_Ref TObjectID="23971"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="1460" y="-1386"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="1460" y="-1386"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="1412" y="-1403"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="1412" y="-1403"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1619" y="-1368"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1619" y="-1368"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="1619" y="-1407"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="1619" y="-1407"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="24" qtmmishow="hidden" width="96" x="1360" y="-1008"/>
    </a>
   <metadata/><rect fill="white" height="24" opacity="0" stroke="white" transform="" width="96" x="1360" y="-1008"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2101" y="-1279"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2101" y="-1279"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="85" x="2316" y="-981"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="85" x="2316" y="-981"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2827" y="-1287"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2827" y="-1287"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2468" y="-1075"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2468" y="-1075"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="1958" y="-613"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="1958" y="-613"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="2210" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="2210" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2499" y="-616"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2499" y="-616"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="2750" y="-612"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="2750" y="-612"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="44" qtmmishow="hidden" width="73" x="1778" y="-1395"/>
    </a>
   <metadata/><rect fill="white" height="44" opacity="0" stroke="white" transform="" width="73" x="1778" y="-1395"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1644030">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3204.000000 -547.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_15efab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 1.000000 2890.000000 -967.000000)" xlink:href="#voltageTransformer:shape79"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 1448.500000 -1327.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130791" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -1271.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130791" ObjectName="NH_XY:NH_XY_GG_Hz_0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130518" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -1250.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130518" ObjectName="NH_XY:NH_XY_311BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130520" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -1218.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130520" ObjectName="NH_XY:NH_XY_311BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130524" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -1202.750000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130524" ObjectName="NH_XY:NH_XY_311BK_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130521" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1902.000000 -1187.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130521" ObjectName="NH_XY:NH_XY_311BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130519" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1903.000000 -1234.250000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130519" ObjectName="NH_XY:NH_XY_311BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130531" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.500000 -815.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130531" ObjectName="NH_XY:NH_XY_001BK_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130532" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.750000 -799.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130532" ObjectName="NH_XY:NH_XY_001BK_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130533" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -784.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130533" ObjectName="NH_XY:NH_XY_001BK_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130537" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.000000 -768.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130537" ObjectName="NH_XY:NH_XY_011BK_3U0"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130534" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1776.750000 -753.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130534" ObjectName="NH_XY:NH_XY_001BK_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130528" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1475.000000 -1231.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130528" ObjectName="NH_XY:NH_XY_311BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130528" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1473.000000 -1190.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130528" ObjectName="NH_XY:NH_XY_311BK_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-130541" ratioFlag="0">
    <text fill="rgb(50,205,50)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2590.000000 -872.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="130541" ObjectName="NH_XY:NH_XY_001BK_P"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="NH_XY"/>
</svg>