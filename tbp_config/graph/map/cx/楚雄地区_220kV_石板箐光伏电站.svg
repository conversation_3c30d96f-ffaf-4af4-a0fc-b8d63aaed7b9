<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-333" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-701 -1082 3230 2136">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape16_0">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16_1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor1">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="33" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape16-UnNor2">
    <circle cx="31" cy="5" fillStyle="0" r="4" stroke-width="0.888889"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="15" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="35" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="35" y1="9" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="44" x2="35" y1="5" y2="5"/>
   </symbol>
   <symbol id="capacitor:shape40">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <text font-family="SimSun" font-size="15" graphid="g_2e87110" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
    <polyline points="17,19 17,30 " stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape37">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,63 0,73 10,73 5,63 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,28 0,18 10,18 5,28 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="86" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <rect height="14" stroke-width="1" width="27" x="18" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="5" x2="5" y1="11" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="9" x2="18" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="8" x2="8" y1="2" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="58" x2="23" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape194">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="5" x2="10" y1="8" y2="8"/>
    <polyline DF8003:Layer="PUBLIC" points="22,1 22,16 10,8 22,1 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="36" x2="22" y1="9" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape21">
    <rect height="26" stroke-width="1.99997" width="11" x="2" y="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="7" x2="7" y1="50" y2="5"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape75">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="26" y1="33" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.676705" x1="9" x2="9" y1="25" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="17" y1="44" y2="44"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.348454" x1="1" x2="18" y1="51" y2="51"/>
    <circle cx="42" cy="29" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="35" cy="33" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="42" cy="39" fillStyle="0" r="8.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="8" x2="11" y1="66" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="13" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="9" y1="60" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="15" x2="3" y1="61" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape3_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="8" y2="24"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape3-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="25"/>
    <circle cx="10" cy="12" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="7"/>
   </symbol>
   <symbol id="switch2:shape2_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="23" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="24" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape2-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="15" y2="5"/>
    <circle cx="10" cy="18" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="15" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="23" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="6" y2="15"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer:shape31_0">
    <circle cx="29" cy="42" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="102" y1="54" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="98" x2="103" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="101" y1="87" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="24" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="42" y1="54" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="24" y1="54" y2="38"/>
   </symbol>
   <symbol id="transformer:shape31_1">
    <circle cx="70" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape31-2">
    <circle cx="70" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="69" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape46_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="20" y1="61" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="15" y1="62" y2="67"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="40" x2="16" y1="56" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="42" x2="38" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="44" x2="36" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="34" x2="46" y1="31" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,62 40,62 40,31 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="47" y2="5"/>
    <circle cx="16" cy="62" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="11" y1="61" y2="56"/>
   </symbol>
   <symbol id="transformer2:shape46_1">
    <circle cx="16" cy="84" fillStyle="0" r="15" stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="16,93 10,81 22,81 16,93 16,92 16,93 "/>
   </symbol>
   <symbol id="voltageTransformer:shape146">
    <circle cx="23" cy="32" r="7.5" stroke-width="0.804311"/>
    <rect height="13" stroke-width="1" width="5" x="3" y="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="17" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="34" y2="37"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="25" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="3" y1="49" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="4" y1="51" y2="51"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="47" y2="37"/>
    <circle cx="34" cy="27" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="20" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="24" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="18" y2="21"/>
    <circle cx="33" cy="14" r="7.5" stroke-width="0.804311"/>
    <circle cx="44" cy="20" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="18" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="32" x2="29" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="34" x2="32" y1="12" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="35" x2="32" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="30" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="37" x2="35" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="47" x2="43" y1="22" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="47" x2="43" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="43" x2="43" y1="18" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="23" x2="21" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="35" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="25" x2="23" y1="29" y2="32"/>
    <circle cx="23" cy="21" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="voltageTransformer:shape147">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="35" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="40" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="33" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="33" x2="40" y1="16" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="29" x2="34" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="46" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="34" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="40" y2="39"/>
    <ellipse cx="34" cy="40" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="27" y2="27"/>
    <ellipse cx="33" cy="15" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="25" y1="3" y2="3"/>
    <ellipse cx="50" cy="27" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="13" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45294" x1="13" x2="20" y1="16" y2="15"/>
    <ellipse cx="14" cy="39" fillStyle="0" rx="12" ry="15" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.45288" x1="9" x2="14" y1="22" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="27" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.77579" x1="56" x2="56" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.39762" x1="49" x2="56" y1="30" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.36031" x1="49" x2="56" y1="25" y2="22"/>
    <ellipse cx="13" cy="16" fillStyle="0" rx="12" ry="14.5" stroke-width="0.862369"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="3" y2="3"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3366fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3367c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3368670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3369310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_336a540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_336b1e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_336bd80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_336c6a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27bbcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_27bbcd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_336f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_336f3a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3370eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_3371c60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3373860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3374450" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_3375210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3375b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3377210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3377eb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3378770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3378f30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_337a010" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_337a990" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_337b480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_337be40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_337d2a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_337de30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_337ee60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_337faa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_338e270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3381120" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3381de0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_3383300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="2146" width="3240" x="-706" y="-1087"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="1" x1="197" x2="197" y1="-625" y2="-625"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -230.190491 421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308034">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 761.299753 422.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47885" ObjectName="SW-CX_SBQ.CX_SBQ_364BK"/>
     <cge:Meas_Ref ObjectId="308034"/>
    <cge:TPSR_Ref TObjectID="47885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308029">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 622.921704 422.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47881" ObjectName="SW-CX_SBQ.CX_SBQ_363BK"/>
     <cge:Meas_Ref ObjectId="308029"/>
    <cge:TPSR_Ref TObjectID="47881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308024">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 473.807070 419.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47877" ObjectName="SW-CX_SBQ.CX_SBQ_362BK"/>
     <cge:Meas_Ref ObjectId="308024"/>
    <cge:TPSR_Ref TObjectID="47877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308019">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 330.205444 420.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47873" ObjectName="SW-CX_SBQ.CX_SBQ_361BK"/>
     <cge:Meas_Ref ObjectId="308019"/>
    <cge:TPSR_Ref TObjectID="47873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308064">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 176.867233 419.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47909" ObjectName="SW-CX_SBQ.CX_SBQ_365BK"/>
     <cge:Meas_Ref ObjectId="308064"/>
    <cge:TPSR_Ref TObjectID="47909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 30.042029 421.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308008">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 639.607375 225.187500)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47863" ObjectName="SW-CX_SBQ.CX_SBQ_311BK"/>
     <cge:Meas_Ref ObjectId="308008"/>
    <cge:TPSR_Ref TObjectID="47863"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308039">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1125.437855 419.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47889" ObjectName="SW-CX_SBQ.CX_SBQ_381BK"/>
     <cge:Meas_Ref ObjectId="308039"/>
    <cge:TPSR_Ref TObjectID="47889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308010">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1264.607375 227.187500)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47866" ObjectName="SW-CX_SBQ.CX_SBQ_312BK"/>
     <cge:Meas_Ref ObjectId="308010"/>
    <cge:TPSR_Ref TObjectID="47866"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1309.343310 418.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47893" ObjectName="SW-CX_SBQ.CX_SBQ_382BK"/>
     <cge:Meas_Ref ObjectId="308044"/>
    <cge:TPSR_Ref TObjectID="47893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308049">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1480.065491 418.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47897" ObjectName="SW-CX_SBQ.CX_SBQ_383BK"/>
     <cge:Meas_Ref ObjectId="308049"/>
    <cge:TPSR_Ref TObjectID="47897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308054">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.705491 416.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47901" ObjectName="SW-CX_SBQ.CX_SBQ_384BK"/>
     <cge:Meas_Ref ObjectId="308054"/>
    <cge:TPSR_Ref TObjectID="47901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308072">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1789.097491 417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47916" ObjectName="SW-CX_SBQ.CX_SBQ_385BK"/>
     <cge:Meas_Ref ObjectId="308072"/>
    <cge:TPSR_Ref TObjectID="47916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2147.532400 420.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.718582 420.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308059">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.742135 133.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47905" ObjectName="SW-CX_SBQ.CX_SBQ_313BK"/>
     <cge:Meas_Ref ObjectId="308059"/>
    <cge:TPSR_Ref TObjectID="47905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308000">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.742135 -451.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47855" ObjectName="SW-CX_SBQ.CX_SBQ_201BK"/>
     <cge:Meas_Ref ObjectId="308000"/>
    <cge:TPSR_Ref TObjectID="47855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1538.126582 -668.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1871.126582 -643.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1985.126582 -666.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307984">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 622.571944 -769.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47846" ObjectName="SW-CX_SBQ.CX_SBQ_271BK"/>
     <cge:Meas_Ref ObjectId="307984"/>
    <cge:TPSR_Ref TObjectID="47846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -92.190491 423.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2340.718582 426.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1630.126582 -669.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308069">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 192.500000 825.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47915" ObjectName="SW-CX_SBQ.CX_SBQ_360BK"/>
     <cge:Meas_Ref ObjectId="308069"/>
    <cge:TPSR_Ref TObjectID="47915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308077">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1804.500000 814.500000)" xlink:href="#breaker2:shape16_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47922" ObjectName="SW-CX_SBQ.CX_SBQ_380BK"/>
     <cge:Meas_Ref ObjectId="308077"/>
    <cge:TPSR_Ref TObjectID="47922"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_SBQ.CX_SBQ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="46700"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -148.000000)" xlink:href="#transformer:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="46702"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -148.000000)" xlink:href="#transformer:shape31_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="46704"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 873.000000 -148.000000)" xlink:href="#transformer:shape31-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="47925" ObjectName="TF-CX_SBQ.CX_SBQ_1T"/>
    <cge:TPSR_Ref TObjectID="47925"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_SBQ.CX_SBQ_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-252,321 853,321 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47843" ObjectName="BS-CX_SBQ.CX_SBQ_3IM"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   <polyline fill="none" opacity="0" points="-252,321 853,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SBQ.CX_SBQ_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1091,320 2529,320 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47844" ObjectName="BS-CX_SBQ.CX_SBQ_3IIM"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   <polyline fill="none" opacity="0" points="1091,320 2529,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_SBQ.CX_SBQ_2IM">
    <g class="BV-220KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="179,-611 1199,-611 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="47842" ObjectName="BS-CX_SBQ.CX_SBQ_2IM"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   <polyline fill="none" opacity="0" points="179,-611 1199,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1514,-431 1694,-431 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1514,-431 1694,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-NULL">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1861,-431 2028,-431 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="BS-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="1861,-431 2028,-431 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 173.740650 905.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1785.970909 894.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -873.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1531.000000 -873.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -865.000000)" xlink:href="#transformer2:shape46_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1978.000000 -865.000000)" xlink:href="#transformer2:shape46_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29808b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 -226.317073 612.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_26242c0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 990.742135 297.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ee3350">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 937.742135 369.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d51520">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 818.173171 557.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e2fe0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 765.173171 613.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e58eb0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 679.795122 557.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e4ae70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 626.795122 613.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2979f80">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 530.680488 554.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2667440">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 477.680488 610.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2970410">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 387.078862 555.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d46ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 334.078862 611.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b2a90">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 234.740650 554.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2987aa0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 180.740650 559.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e80150">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 86.915447 556.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3f160">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 33.915447 612.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298a350">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -173.317073 556.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e82720">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 194.740650 623.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb7590">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 110.740650 740.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e9a9f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 370.735772 260.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e3a380">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 410.735772 190.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d40530">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1182.311273 554.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e80f10">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1129.311273 610.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3384e90">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1366.216727 553.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea93d0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1313.216727 609.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2713790">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1536.938909 553.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_297a2a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1483.938909 609.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e75930">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1681.578909 551.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb2d00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1628.578909 607.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2718310">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1846.970909 552.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3c9f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1792.970909 557.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d3d4d0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1806.970909 612.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e52ff0">
    <use class="BV-35KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1722.970909 729.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2716290">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2204.405818 555.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2629db0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 2151.405818 611.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e74200">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2027.592000 555.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e90c30">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1974.592000 611.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2eb6110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1753.228364 259.000000)" xlink:href="#lightningRod:shape21"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ed1550">
    <use class="BV-35KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1794.228364 191.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ecee70">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 1050.742135 -45.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29654b0">
    <use class="BV-220KV" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1071.000000 -273.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299d2f0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1120.500000 -164.500000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_299df20">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1072.000000 -94.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29c6a00">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1542.000000 -803.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ead7a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1556.000000 -986.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f5970">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 1989.000000 -795.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3788c20">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2002.000000 -975.000000)" xlink:href="#lightningRod:shape194"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261fdb0">
    <use class="BV-220KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 661.500000 -952.500000)" xlink:href="#lightningRod:shape75"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_334c950">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 543.000000 -966.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335fc50">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -35.317073 558.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3364540">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 -88.317073 614.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a38c0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2397.592000 561.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a81b0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -0.622222 2344.592000 617.000000)" xlink:href="#lightningRod:shape37"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c7950">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1020.000000 -228.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cd2a0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 872.000000 -93.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cf260">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 276.000000 -370.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33d2f20">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 843.000000 -285.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -610.000000 -875.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307892" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -763.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307892" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47846"/>
     <cge:Term_Ref ObjectID="45828"/>
    <cge:TPSR_Ref TObjectID="47846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307893" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -763.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307893" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47846"/>
     <cge:Term_Ref ObjectID="45828"/>
    <cge:TPSR_Ref TObjectID="47846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307889" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 -763.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307889" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47846"/>
     <cge:Term_Ref ObjectID="45828"/>
    <cge:TPSR_Ref TObjectID="47846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307913" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -487.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307913" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47855"/>
     <cge:Term_Ref ObjectID="45846"/>
    <cge:TPSR_Ref TObjectID="47855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307914" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -487.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307914" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47855"/>
     <cge:Term_Ref ObjectID="45846"/>
    <cge:TPSR_Ref TObjectID="47855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307904" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1063.000000 -487.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307904" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47855"/>
     <cge:Term_Ref ObjectID="45846"/>
    <cge:TPSR_Ref TObjectID="47855"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308153" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 90.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308153" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47905"/>
     <cge:Term_Ref ObjectID="46659"/>
    <cge:TPSR_Ref TObjectID="47905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308154" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 90.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308154" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47905"/>
     <cge:Term_Ref ObjectID="46659"/>
    <cge:TPSR_Ref TObjectID="47905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308150" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1084.000000 90.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308150" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47905"/>
     <cge:Term_Ref ObjectID="46659"/>
    <cge:TPSR_Ref TObjectID="47905"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-307940" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1156.000000 -83.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307940" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47925"/>
     <cge:Term_Ref ObjectID="46699"/>
    <cge:TPSR_Ref TObjectID="47925"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308165" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 1009.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308165" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47909"/>
     <cge:Term_Ref ObjectID="46667"/>
    <cge:TPSR_Ref TObjectID="47909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308166" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 1009.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308166" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47909"/>
     <cge:Term_Ref ObjectID="46667"/>
    <cge:TPSR_Ref TObjectID="47909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308162" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 171.000000 1009.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308162" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47909"/>
     <cge:Term_Ref ObjectID="46667"/>
    <cge:TPSR_Ref TObjectID="47909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307969" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 709.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307969" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47873"/>
     <cge:Term_Ref ObjectID="46595"/>
    <cge:TPSR_Ref TObjectID="47873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307970" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 709.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307970" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47873"/>
     <cge:Term_Ref ObjectID="46595"/>
    <cge:TPSR_Ref TObjectID="47873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 351.000000 709.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47873"/>
     <cge:Term_Ref ObjectID="46595"/>
    <cge:TPSR_Ref TObjectID="47873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-307981" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 708.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307981" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47877"/>
     <cge:Term_Ref ObjectID="46603"/>
    <cge:TPSR_Ref TObjectID="47877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-307982" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 708.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307982" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47877"/>
     <cge:Term_Ref ObjectID="46603"/>
    <cge:TPSR_Ref TObjectID="47877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-307978" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 475.000000 708.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307978" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47877"/>
     <cge:Term_Ref ObjectID="46603"/>
    <cge:TPSR_Ref TObjectID="47877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308093" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 709.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308093" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47881"/>
     <cge:Term_Ref ObjectID="46611"/>
    <cge:TPSR_Ref TObjectID="47881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308094" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 709.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308094" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47881"/>
     <cge:Term_Ref ObjectID="46611"/>
    <cge:TPSR_Ref TObjectID="47881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308090" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 633.000000 709.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308090" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47881"/>
     <cge:Term_Ref ObjectID="46611"/>
    <cge:TPSR_Ref TObjectID="47881"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308105" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 710.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308105" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47885"/>
     <cge:Term_Ref ObjectID="46619"/>
    <cge:TPSR_Ref TObjectID="47885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308106" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 710.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308106" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47885"/>
     <cge:Term_Ref ObjectID="46619"/>
    <cge:TPSR_Ref TObjectID="47885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308102" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 770.000000 710.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308102" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47885"/>
     <cge:Term_Ref ObjectID="46619"/>
    <cge:TPSR_Ref TObjectID="47885"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308117" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 715.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308117" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47889"/>
     <cge:Term_Ref ObjectID="46627"/>
    <cge:TPSR_Ref TObjectID="47889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308118" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 715.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308118" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47889"/>
     <cge:Term_Ref ObjectID="46627"/>
    <cge:TPSR_Ref TObjectID="47889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308114" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1142.000000 715.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308114" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47889"/>
     <cge:Term_Ref ObjectID="46627"/>
    <cge:TPSR_Ref TObjectID="47889"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308129" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1315.000000 713.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308129" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47893"/>
     <cge:Term_Ref ObjectID="46635"/>
    <cge:TPSR_Ref TObjectID="47893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308130" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1315.000000 713.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308130" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47893"/>
     <cge:Term_Ref ObjectID="46635"/>
    <cge:TPSR_Ref TObjectID="47893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308126" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1315.000000 713.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308126" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47893"/>
     <cge:Term_Ref ObjectID="46635"/>
    <cge:TPSR_Ref TObjectID="47893"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308141" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1638.000000 711.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308141" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47901"/>
     <cge:Term_Ref ObjectID="46651"/>
    <cge:TPSR_Ref TObjectID="47901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308142" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1638.000000 711.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308142" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47901"/>
     <cge:Term_Ref ObjectID="46651"/>
    <cge:TPSR_Ref TObjectID="47901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308138" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1638.000000 711.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308138" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47901"/>
     <cge:Term_Ref ObjectID="46651"/>
    <cge:TPSR_Ref TObjectID="47901"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-308177" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 986.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308177" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47916"/>
     <cge:Term_Ref ObjectID="46681"/>
    <cge:TPSR_Ref TObjectID="47916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-308178" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 986.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308178" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47916"/>
     <cge:Term_Ref ObjectID="46681"/>
    <cge:TPSR_Ref TObjectID="47916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-308174" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 986.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="308174" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47916"/>
     <cge:Term_Ref ObjectID="46681"/>
    <cge:TPSR_Ref TObjectID="47916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 712.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47897"/>
     <cge:Term_Ref ObjectID="46643"/>
    <cge:TPSR_Ref TObjectID="47897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 712.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47897"/>
     <cge:Term_Ref ObjectID="46643"/>
    <cge:TPSR_Ref TObjectID="47897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-0" prefix="" rightAlign="0">
    <text fill="rgb(190,190,190)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1486.000000 712.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47897"/>
     <cge:Term_Ref ObjectID="46643"/>
    <cge:TPSR_Ref TObjectID="47897"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307896" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307896" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307897" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307897" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307898" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307898" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307902" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307902" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307899" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307899" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307903" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 237.000000 -720.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307903" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47842"/>
     <cge:Term_Ref ObjectID="45823"/>
    <cge:TPSR_Ref TObjectID="47842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307944" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307944" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307945" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307945" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307946" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307946" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307950" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307950" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307947" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307947" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307951" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -223.000000 191.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307951" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47843"/>
     <cge:Term_Ref ObjectID="45824"/>
    <cge:TPSR_Ref TObjectID="47843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-307952" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307952" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-307953" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307953" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-307954" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307954" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-307958" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307958" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-307955" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307955" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-307959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2390.000000 187.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="307959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="47844"/>
     <cge:Term_Ref ObjectID="45825"/>
    <cge:TPSR_Ref TObjectID="47844"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="173" x="-606" y="-934"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="173" x="-606" y="-934"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-655" y="-951"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-655" y="-951"/></g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="173" x="-606" y="-934"/></g>
   <g href="cx_索引_接线图_省地共调_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-655" y="-951"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="138" y="791"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="26" stroke="rgb(255,255,0)" stroke-width="1" width="12" x="1750" y="779"/>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2e900b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 410.735772 156.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_262bea0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1793.228364 155.000000)" xlink:href="#voltageTransformer:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_261be40">
    <use class="BV-220KV" transform="matrix(-0.979383 0.000000 -0.000000 -0.730159 398.000000 -304.000000)" xlink:href="#voltageTransformer:shape147"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -226.317073 674.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 937.742135 416.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 33.915447 674.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2151.405818 673.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1974.592000 673.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -88.317073 676.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2344.592000 679.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 333.682927 672.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 477.682927 674.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 626.682927 675.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 764.682927 675.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1128.682927 672.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.682927 671.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.682927 669.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1483.682927 671.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 636.817073 -1049.500000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_29d2e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-221,375 -221,385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-221,375 -221,385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29af6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-221,412 -221,424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-221,412 -221,424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2982330">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-221,608 -221,653 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29808b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29808b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-221,608 -221,653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d44260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,239 984,219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_26242c0@0" ObjectIDZND0="g_2ee3350@0" ObjectIDZND1="47907@x" ObjectIDZND2="47908@x" Pin0InfoVect0LinkObjId="g_2ee3350_0" Pin0InfoVect1LinkObjId="SW-308060_0" Pin0InfoVect2LinkObjId="SW-308061_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_26242c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="984,239 984,219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2751ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,219 984,219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2ee3350@0" ObjectIDND1="47907@x" ObjectIDZND0="g_26242c0@0" ObjectIDZND1="47908@x" Pin0InfoVect0LinkObjId="g_26242c0_0" Pin0InfoVect1LinkObjId="SW-308061_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ee3350_0" Pin1InfoVect1LinkObjId="SW-308060_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,219 984,219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27740c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="984,219 1019,219 1019,235 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_26242c0@0" ObjectIDND1="g_2ee3350@0" ObjectIDND2="47907@x" ObjectIDZND0="47908@1" Pin0InfoVect0LinkObjId="SW-308061_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26242c0_0" Pin1InfoVect1LinkObjId="g_2ee3350_0" Pin1InfoVect2LinkObjId="SW-308060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="984,219 1019,219 1019,235 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2661a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1019,283 1019,271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29b2db0@0" ObjectIDZND0="47908@0" Pin0InfoVect0LinkObjId="SW-308061_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b2db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1019,283 1019,271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2989f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,219 943,316 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_26242c0@0" ObjectIDND1="47908@x" ObjectIDND2="47907@x" ObjectIDZND0="g_2ee3350@1" Pin0InfoVect0LinkObjId="g_2ee3350_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_26242c0_0" Pin1InfoVect1LinkObjId="SW-308061_0" Pin1InfoVect2LinkObjId="SW-308060_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,219 943,316 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ec4d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,365 943,395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ee3350@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ee3350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,365 943,395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2983970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,376 770,386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47886@1" ObjectIDZND0="47885@1" Pin0InfoVect0LinkObjId="SW-308034_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308035_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,376 770,386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_279a6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,413 770,425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47885@0" ObjectIDZND0="47887@1" Pin0InfoVect0LinkObjId="SW-308035_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308034_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,413 770,425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29d8340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,359 770,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47886@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_2e684a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,359 770,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f9480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,499 811,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d51520@0" ObjectIDZND0="47887@x" ObjectIDZND1="g_29e2fe0@0" ObjectIDZND2="47888@x" Pin0InfoVect0LinkObjId="SW-308035_0" Pin0InfoVect1LinkObjId="g_29e2fe0_0" Pin0InfoVect2LinkObjId="SW-308036_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d51520_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="811,499 811,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e23c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,479 811,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47887@x" ObjectIDND1="g_29e2fe0@0" ObjectIDZND0="g_2d51520@0" ObjectIDZND1="47888@x" Pin0InfoVect0LinkObjId="g_2d51520_0" Pin0InfoVect1LinkObjId="SW-308036_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308035_0" Pin1InfoVect1LinkObjId="g_29e2fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="770,479 811,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d28f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="811,479 846,479 846,495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d51520@0" ObjectIDND1="47887@x" ObjectIDND2="g_29e2fe0@0" ObjectIDZND0="47888@1" Pin0InfoVect0LinkObjId="SW-308036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d51520_0" Pin1InfoVect1LinkObjId="SW-308035_0" Pin1InfoVect2LinkObjId="g_29e2fe0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="811,479 846,479 846,495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e74b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="846,543 846,531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29b3130@0" ObjectIDZND0="47888@0" Pin0InfoVect0LinkObjId="SW-308036_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b3130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="846,543 846,531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7a180">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,442 770,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47887@0" ObjectIDZND0="g_2d51520@0" ObjectIDZND1="47888@x" ObjectIDZND2="g_29e2fe0@0" Pin0InfoVect0LinkObjId="g_2d51520_0" Pin0InfoVect1LinkObjId="SW-308036_0" Pin0InfoVect2LinkObjId="g_29e2fe0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="770,442 770,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e63e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,479 770,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d51520@0" ObjectIDND1="47888@x" ObjectIDND2="47887@x" ObjectIDZND0="g_29e2fe0@1" Pin0InfoVect0LinkObjId="g_29e2fe0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d51520_0" Pin1InfoVect1LinkObjId="SW-308036_0" Pin1InfoVect2LinkObjId="SW-308035_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,479 770,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e68f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="770,609 770,654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_29e2fe0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e2fe0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="770,609 770,654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e54320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,376 632,386 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47882@1" ObjectIDZND0="47881@1" Pin0InfoVect0LinkObjId="SW-308029_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308030_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,376 632,386 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e50010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,413 632,425 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47881@0" ObjectIDZND0="47883@1" Pin0InfoVect0LinkObjId="SW-308030_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308029_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,413 632,425 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e684a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,359 632,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47882@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,359 632,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3c0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,499 673,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e58eb0@0" ObjectIDZND0="47883@x" ObjectIDZND1="g_2e4ae70@0" ObjectIDZND2="47884@x" Pin0InfoVect0LinkObjId="SW-308030_0" Pin0InfoVect1LinkObjId="g_2e4ae70_0" Pin0InfoVect2LinkObjId="SW-308031_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e58eb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="673,499 673,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b3710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,479 673,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47883@x" ObjectIDND1="g_2e4ae70@0" ObjectIDZND0="g_2e58eb0@0" ObjectIDZND1="47884@x" Pin0InfoVect0LinkObjId="g_2e58eb0_0" Pin0InfoVect1LinkObjId="SW-308031_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308030_0" Pin1InfoVect1LinkObjId="g_2e4ae70_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,479 673,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_315b170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="673,479 708,479 708,495 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e58eb0@0" ObjectIDND1="47883@x" ObjectIDND2="g_2e4ae70@0" ObjectIDZND0="47884@1" Pin0InfoVect0LinkObjId="SW-308031_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e58eb0_0" Pin1InfoVect1LinkObjId="SW-308030_0" Pin1InfoVect2LinkObjId="g_2e4ae70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="673,479 708,479 708,495 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26f3a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="708,543 708,531 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29bc570@0" ObjectIDZND0="47884@0" Pin0InfoVect0LinkObjId="SW-308031_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29bc570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="708,543 708,531 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea3d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,442 632,479 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47883@0" ObjectIDZND0="g_2e58eb0@0" ObjectIDZND1="47884@x" ObjectIDZND2="g_2e4ae70@0" Pin0InfoVect0LinkObjId="g_2e58eb0_0" Pin0InfoVect1LinkObjId="SW-308031_0" Pin0InfoVect2LinkObjId="g_2e4ae70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="632,442 632,479 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e74e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,479 632,560 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e58eb0@0" ObjectIDND1="47884@x" ObjectIDND2="47883@x" ObjectIDZND0="g_2e4ae70@1" Pin0InfoVect0LinkObjId="g_2e4ae70_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e58eb0_0" Pin1InfoVect1LinkObjId="SW-308031_0" Pin1InfoVect2LinkObjId="SW-308030_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,479 632,560 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d27820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,609 632,654 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2e4ae70@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e4ae70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,609 632,654 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e951f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,373 483,383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47878@1" ObjectIDZND0="47877@1" Pin0InfoVect0LinkObjId="SW-308024_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308025_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,373 483,383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9aff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,410 483,422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47877@0" ObjectIDZND0="47879@1" Pin0InfoVect0LinkObjId="SW-308025_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308024_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,410 483,422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d4b190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,356 483,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47878@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,356 483,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2edb800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,496 524,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2979f80@0" ObjectIDZND0="47879@x" ObjectIDZND1="g_2667440@0" ObjectIDZND2="47880@x" Pin0InfoVect0LinkObjId="SW-308025_0" Pin0InfoVect1LinkObjId="g_2667440_0" Pin0InfoVect2LinkObjId="SW-308026_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2979f80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="524,496 524,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d397a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,476 524,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47879@x" ObjectIDND1="g_2667440@0" ObjectIDZND0="g_2979f80@0" ObjectIDZND1="47880@x" Pin0InfoVect0LinkObjId="g_2979f80_0" Pin0InfoVect1LinkObjId="SW-308026_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308025_0" Pin1InfoVect1LinkObjId="g_2667440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="483,476 524,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2eb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="524,476 559,476 559,492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2979f80@0" ObjectIDND1="47879@x" ObjectIDND2="g_2667440@0" ObjectIDZND0="47880@1" Pin0InfoVect0LinkObjId="SW-308026_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2979f80_0" Pin1InfoVect1LinkObjId="SW-308025_0" Pin1InfoVect2LinkObjId="g_2667440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="524,476 559,476 559,492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_276c670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="559,540 559,528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_273d450@0" ObjectIDZND0="47880@0" Pin0InfoVect0LinkObjId="SW-308026_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_273d450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="559,540 559,528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_265fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,439 483,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47879@0" ObjectIDZND0="g_2979f80@0" ObjectIDZND1="47880@x" ObjectIDZND2="g_2667440@0" Pin0InfoVect0LinkObjId="g_2979f80_0" Pin0InfoVect1LinkObjId="SW-308026_0" Pin0InfoVect2LinkObjId="g_2667440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308025_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="483,439 483,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2665a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,476 483,557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2979f80@0" ObjectIDND1="47880@x" ObjectIDND2="47879@x" ObjectIDZND0="g_2667440@1" Pin0InfoVect0LinkObjId="g_2667440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2979f80_0" Pin1InfoVect1LinkObjId="SW-308026_0" Pin1InfoVect2LinkObjId="SW-308025_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,476 483,557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_26955a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="483,608 483,653 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2667440@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2667440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="483,608 483,653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d463e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,374 339,384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47874@1" ObjectIDZND0="47873@1" Pin0InfoVect0LinkObjId="SW-308019_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308020_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,374 339,384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e8af00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,411 339,423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47873@0" ObjectIDZND0="47875@1" Pin0InfoVect0LinkObjId="SW-308020_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308019_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,411 339,423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29715d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,357 339,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47874@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,357 339,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e511f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,497 380,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2970410@0" ObjectIDZND0="47875@x" ObjectIDZND1="g_2d46ab0@0" ObjectIDZND2="47876@x" Pin0InfoVect0LinkObjId="SW-308020_0" Pin0InfoVect1LinkObjId="g_2d46ab0_0" Pin0InfoVect2LinkObjId="SW-308021_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2970410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="380,497 380,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d2e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,477 380,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47875@x" ObjectIDND1="g_2d46ab0@0" ObjectIDZND0="g_2970410@0" ObjectIDZND1="47876@x" Pin0InfoVect0LinkObjId="g_2970410_0" Pin0InfoVect1LinkObjId="SW-308021_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308020_0" Pin1InfoVect1LinkObjId="g_2d46ab0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="339,477 380,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_265d9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="380,477 415,477 415,493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2970410@0" ObjectIDND1="47875@x" ObjectIDND2="g_2d46ab0@0" ObjectIDZND0="47876@1" Pin0InfoVect0LinkObjId="SW-308021_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2970410_0" Pin1InfoVect1LinkObjId="SW-308020_0" Pin1InfoVect2LinkObjId="g_2d46ab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="380,477 415,477 415,493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f7e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="415,541 415,529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_378dd40@0" ObjectIDZND0="47876@0" Pin0InfoVect0LinkObjId="SW-308021_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_378dd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="415,541 415,529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e95b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,440 339,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47875@0" ObjectIDZND0="g_2970410@0" ObjectIDZND1="47876@x" ObjectIDZND2="g_2d46ab0@0" Pin0InfoVect0LinkObjId="g_2970410_0" Pin0InfoVect1LinkObjId="SW-308021_0" Pin0InfoVect2LinkObjId="g_2d46ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="339,440 339,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d4e890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,477 339,558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2970410@0" ObjectIDND1="47876@x" ObjectIDND2="47875@x" ObjectIDZND0="g_2d46ab0@1" Pin0InfoVect0LinkObjId="g_2d46ab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2970410_0" Pin1InfoVect1LinkObjId="SW-308021_0" Pin1InfoVect2LinkObjId="SW-308020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,477 339,558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e56440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="339,606 339,651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2d46ab0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d46ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="339,606 339,651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d355f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,373 186,383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47910@1" ObjectIDZND0="47909@1" Pin0InfoVect0LinkObjId="SW-308064_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308065_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="186,373 186,383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d394b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,410 186,422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47909@0" ObjectIDZND0="47911@1" Pin0InfoVect0LinkObjId="SW-308065_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308064_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="186,410 186,422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b7200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,356 186,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47910@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308065_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="186,356 186,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d508a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,496 228,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_29b2a90@0" ObjectIDZND0="47911@x" ObjectIDZND1="g_2987aa0@0" ObjectIDZND2="47913@x" Pin0InfoVect0LinkObjId="SW-308065_0" Pin0InfoVect1LinkObjId="g_2987aa0_0" Pin0InfoVect2LinkObjId="SW-308067_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b2a90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="228,496 228,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e718e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,476 228,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47911@x" ObjectIDND1="g_2987aa0@0" ObjectIDZND0="g_29b2a90@0" ObjectIDZND1="47913@x" Pin0InfoVect0LinkObjId="g_29b2a90_0" Pin0InfoVect1LinkObjId="SW-308067_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308065_0" Pin1InfoVect1LinkObjId="g_2987aa0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="186,476 228,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d50b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="228,476 263,476 263,492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="47911@x" ObjectIDND1="g_2987aa0@0" ObjectIDND2="g_29b2a90@0" ObjectIDZND0="47913@1" Pin0InfoVect0LinkObjId="SW-308067_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-308065_0" Pin1InfoVect1LinkObjId="g_2987aa0_0" Pin1InfoVect2LinkObjId="g_29b2a90_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="228,476 263,476 263,492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_296efa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="263,540 263,528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29f9c70@0" ObjectIDZND0="47913@0" Pin0InfoVect0LinkObjId="SW-308067_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f9c70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="263,540 263,528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_297f810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,439 186,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47911@0" ObjectIDZND0="g_29b2a90@0" ObjectIDZND1="47913@x" ObjectIDZND2="g_2987aa0@0" Pin0InfoVect0LinkObjId="g_29b2a90_0" Pin0InfoVect1LinkObjId="SW-308067_0" Pin0InfoVect2LinkObjId="g_2987aa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308065_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="186,439 186,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3369ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,375 39,385 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,375 39,385 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_275c770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="39,412 39,424 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,412 39,424 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d48ee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="39,358 39,321 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,358 39,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e53fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="80,498 80,478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e80150@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2e3f160@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2e3f160_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="80,498 80,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2716460">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="39,478 80,478 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e3f160@0" ObjectIDZND0="g_2e80150@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2e80150_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e3f160_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="39,478 80,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e3bd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="80,478 115,478 115,494 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e3f160@0" ObjectIDND2="g_2e80150@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e3f160_0" Pin1InfoVect2LinkObjId="g_2e80150_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="80,478 115,478 115,494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e487e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="115,542 115,530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2edb560@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2edb560_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="115,542 115,530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e38b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="39,441 39,478 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2e80150@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2e3f160@0" Pin0InfoVect0LinkObjId="g_2e80150_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e3f160_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="39,441 39,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e664f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="39,478 39,559 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2e80150@0" ObjectIDND2="0@x" ObjectIDZND0="g_2e3f160@1" Pin0InfoVect0LinkObjId="g_2e3f160_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e80150_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,478 39,559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e51a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="39,608 39,653 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2e3f160@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3f160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="39,608 39,653 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d4aa40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-180,498 -180,478 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_298a350@0" ObjectIDZND0="0@x" ObjectIDZND1="g_29808b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_29808b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298a350_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-180,498 -180,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e67d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-221,478 -180,478 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_29808b0@0" ObjectIDZND0="g_298a350@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_298a350_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_29808b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-221,478 -180,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_297e9d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-180,478 -145,478 -145,494 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_29808b0@0" ObjectIDND2="g_298a350@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_29808b0_0" Pin1InfoVect2LinkObjId="g_298a350_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-180,478 -145,478 -145,494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ee78d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-145,542 -145,530 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2983f00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2983f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-145,542 -145,530 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29fa0f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-221,441 -221,478 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_298a350@0" ObjectIDZND1="0@x" ObjectIDZND2="g_29808b0@0" Pin0InfoVect0LinkObjId="g_298a350_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_29808b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-221,441 -221,478 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29e2890">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-221,478 -221,559 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_298a350@0" ObjectIDND2="0@x" ObjectIDZND0="g_29808b0@1" Pin0InfoVect0LinkObjId="g_29808b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_298a350_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-221,478 -221,559 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b0f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,506 186,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2987aa0@1" ObjectIDZND0="47911@x" ObjectIDZND1="g_29b2a90@0" ObjectIDZND2="47913@x" Pin0InfoVect0LinkObjId="SW-308065_0" Pin0InfoVect1LinkObjId="g_29b2a90_0" Pin0InfoVect2LinkObjId="SW-308067_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2987aa0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="186,506 186,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d55000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="205,715 187,715 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="47914@0" ObjectIDZND0="47912@x" ObjectIDZND1="g_2eb7590@0" ObjectIDZND2="47915@x" Pin0InfoVect0LinkObjId="SW-308066_0" Pin0InfoVect1LinkObjId="g_2eb7590_0" Pin0InfoVect2LinkObjId="SW-308069_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308068_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="205,715 187,715 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e82c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,664 187,618 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47912@1" ObjectIDZND0="g_2e82720@1" Pin0InfoVect0LinkObjId="g_2e82720_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308066_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,664 187,618 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ecdc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,715 187,700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2eb7590@0" ObjectIDND1="47915@x" ObjectIDND2="47914@x" ObjectIDZND0="47912@0" Pin0InfoVect0LinkObjId="SW-308066_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2eb7590_0" Pin1InfoVect1LinkObjId="SW-308069_0" Pin1InfoVect2LinkObjId="SW-308068_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,715 187,700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_297b8b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,917 187,900 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,917 187,900 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e65ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="144,791 144,769 187,769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2eb7590@0" ObjectIDZND1="47912@x" ObjectIDZND2="47914@x" Pin0InfoVect0LinkObjId="g_2eb7590_0" Pin0InfoVect1LinkObjId="SW-308066_0" Pin0InfoVect2LinkObjId="SW-308068_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="144,791 144,769 187,769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2962a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,784 187,769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47915@1" ObjectIDZND0="g_2eb7590@0" ObjectIDZND1="47912@x" ObjectIDZND2="47914@x" Pin0InfoVect0LinkObjId="g_2eb7590_0" Pin0InfoVect1LinkObjId="SW-308066_0" Pin0InfoVect2LinkObjId="SW-308068_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308069_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="187,784 187,769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f02b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,858 187,836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="47915@x" Pin0InfoVect0LinkObjId="SW-308069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,858 187,836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb5650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,203 378,213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47870@1" ObjectIDZND0="g_2e9a9f0@1" Pin0InfoVect0LinkObjId="g_2e9a9f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,203 378,213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea9a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,186 378,151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="47870@0" ObjectIDZND0="g_2e900b0@0" Pin0InfoVect0LinkObjId="g_2e900b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308017_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,186 378,151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e3a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,321 378,299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47843@0" ObjectIDZND0="47869@0" Pin0InfoVect0LinkObjId="SW-308017_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29d8340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,321 378,299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e55fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,180 648,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47865@1" ObjectIDZND0="47863@1" Pin0InfoVect0LinkObjId="SW-308008_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308009_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,180 648,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e56210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,217 648,229 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47863@0" ObjectIDZND0="47864@1" Pin0InfoVect0LinkObjId="SW-308009_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308008_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,217 648,229 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e72340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="648,246 648,321 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47864@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="648,246 648,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2992b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,373 1134,383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47890@1" ObjectIDZND0="47889@1" Pin0InfoVect0LinkObjId="SW-308039_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308040_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,373 1134,383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2992da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,410 1134,422 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47889@0" ObjectIDZND0="47891@1" Pin0InfoVect0LinkObjId="SW-308040_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308039_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,410 1134,422 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d402d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,356 1134,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47890@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_3384c10_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,356 1134,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ad5f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1175,496 1175,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2d40530@0" ObjectIDZND0="47891@x" ObjectIDZND1="g_2e80f10@0" ObjectIDZND2="47892@x" Pin0InfoVect0LinkObjId="SW-308040_0" Pin0InfoVect1LinkObjId="g_2e80f10_0" Pin0InfoVect2LinkObjId="SW-308041_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d40530_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1175,496 1175,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e76b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,476 1175,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47891@x" ObjectIDND1="g_2e80f10@0" ObjectIDZND0="g_2d40530@0" ObjectIDZND1="47892@x" Pin0InfoVect0LinkObjId="g_2d40530_0" Pin0InfoVect1LinkObjId="SW-308041_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308040_0" Pin1InfoVect1LinkObjId="g_2e80f10_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1134,476 1175,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e76da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1175,476 1210,476 1210,492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2d40530@0" ObjectIDND1="47891@x" ObjectIDND2="g_2e80f10@0" ObjectIDZND0="47892@1" Pin0InfoVect0LinkObjId="SW-308041_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d40530_0" Pin1InfoVect1LinkObjId="SW-308040_0" Pin1InfoVect2LinkObjId="g_2e80f10_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1175,476 1210,476 1210,492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_299fc00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1210,540 1210,528 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e63040@0" ObjectIDZND0="47892@0" Pin0InfoVect0LinkObjId="SW-308041_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e63040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1210,540 1210,528 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e80d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,439 1134,476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47891@0" ObjectIDZND0="g_2d40530@0" ObjectIDZND1="47892@x" ObjectIDZND2="g_2e80f10@0" Pin0InfoVect0LinkObjId="g_2d40530_0" Pin0InfoVect1LinkObjId="SW-308041_0" Pin0InfoVect2LinkObjId="g_2e80f10_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308040_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1134,439 1134,476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea2d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,476 1134,557 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2d40530@0" ObjectIDND1="47892@x" ObjectIDND2="47891@x" ObjectIDZND0="g_2e80f10@1" Pin0InfoVect0LinkObjId="g_2e80f10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2d40530_0" Pin1InfoVect1LinkObjId="SW-308041_0" Pin1InfoVect2LinkObjId="SW-308040_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,476 1134,557 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea2f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,606 1134,651 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2e80f10@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e80f10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1134,606 1134,651 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2990310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,182 1273,192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47868@1" ObjectIDZND0="47866@1" Pin0InfoVect0LinkObjId="SW-308010_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308011_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,182 1273,192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2990570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1273,219 1273,231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47866@0" ObjectIDZND0="47867@1" Pin0InfoVect0LinkObjId="SW-308011_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308010_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1273,219 1273,231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b16b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,372 1318,382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47894@1" ObjectIDZND0="47893@1" Pin0InfoVect0LinkObjId="SW-308044_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308045_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,372 1318,382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e5e5e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,409 1318,421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47893@0" ObjectIDZND0="47895@1" Pin0InfoVect0LinkObjId="SW-308045_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308044_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,409 1318,421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3384c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,355 1318,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47894@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,355 1318,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e816d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,495 1359,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_3384e90@0" ObjectIDZND0="47895@x" ObjectIDZND1="g_2ea93d0@0" ObjectIDZND2="47896@x" Pin0InfoVect0LinkObjId="SW-308045_0" Pin0InfoVect1LinkObjId="g_2ea93d0_0" Pin0InfoVect2LinkObjId="SW-308046_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3384e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1359,495 1359,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e81930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,475 1359,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47895@x" ObjectIDND1="g_2ea93d0@0" ObjectIDZND0="g_3384e90@0" ObjectIDZND1="47896@x" Pin0InfoVect0LinkObjId="g_3384e90_0" Pin0InfoVect1LinkObjId="SW-308046_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308045_0" Pin1InfoVect1LinkObjId="g_2ea93d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1318,475 1359,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e86530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1359,475 1394,475 1394,491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_3384e90@0" ObjectIDND1="47895@x" ObjectIDND2="g_2ea93d0@0" ObjectIDZND0="47896@1" Pin0InfoVect0LinkObjId="SW-308046_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3384e90_0" Pin1InfoVect1LinkObjId="SW-308045_0" Pin1InfoVect2LinkObjId="g_2ea93d0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1359,475 1394,475 1394,491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea8f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1394,539 1394,527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e974f0@0" ObjectIDZND0="47896@0" Pin0InfoVect0LinkObjId="SW-308046_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e974f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1394,539 1394,527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ea9170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,438 1318,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47895@0" ObjectIDZND0="g_3384e90@0" ObjectIDZND1="47896@x" ObjectIDZND2="g_2ea93d0@0" Pin0InfoVect0LinkObjId="g_3384e90_0" Pin0InfoVect1LinkObjId="SW-308046_0" Pin0InfoVect2LinkObjId="g_2ea93d0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308045_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1318,438 1318,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2887650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,475 1318,556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_3384e90@0" ObjectIDND1="47896@x" ObjectIDND2="47895@x" ObjectIDZND0="g_2ea93d0@1" Pin0InfoVect0LinkObjId="g_2ea93d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_3384e90_0" Pin1InfoVect1LinkObjId="SW-308046_0" Pin1InfoVect2LinkObjId="SW-308045_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,475 1318,556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d1d2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1318,605 1318,650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2ea93d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea93d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1318,605 1318,650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e97120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,372 1489,382 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47898@1" ObjectIDZND0="47897@1" Pin0InfoVect0LinkObjId="SW-308049_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308050_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1489,372 1489,382 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2716a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,409 1489,421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47897@0" ObjectIDZND0="47899@1" Pin0InfoVect0LinkObjId="SW-308050_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308049_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1489,409 1489,421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2713530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,355 1489,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47898@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1489,355 1489,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a0b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,495 1530,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2713790@0" ObjectIDZND0="47899@x" ObjectIDZND1="g_297a2a0@0" ObjectIDZND2="47900@x" Pin0InfoVect0LinkObjId="SW-308050_0" Pin0InfoVect1LinkObjId="g_297a2a0_0" Pin0InfoVect2LinkObjId="SW-308051_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2713790_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1530,495 1530,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a0d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,475 1530,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47899@x" ObjectIDND1="g_297a2a0@0" ObjectIDZND0="g_2713790@0" ObjectIDZND1="47900@x" Pin0InfoVect0LinkObjId="g_2713790_0" Pin0InfoVect1LinkObjId="SW-308051_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308050_0" Pin1InfoVect1LinkObjId="g_297a2a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1489,475 1530,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29a0ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1530,475 1565,475 1565,491 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2713790@0" ObjectIDND1="47899@x" ObjectIDND2="g_297a2a0@0" ObjectIDZND0="47900@1" Pin0InfoVect0LinkObjId="SW-308051_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2713790_0" Pin1InfoVect1LinkObjId="SW-308050_0" Pin1InfoVect2LinkObjId="g_297a2a0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1530,475 1565,475 1565,491 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb13e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1565,539 1565,527 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d22180@0" ObjectIDZND0="47900@0" Pin0InfoVect0LinkObjId="SW-308051_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d22180_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1565,539 1565,527 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb1640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,438 1489,475 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47899@0" ObjectIDZND0="g_2713790@0" ObjectIDZND1="47900@x" ObjectIDZND2="g_297a2a0@0" Pin0InfoVect0LinkObjId="g_2713790_0" Pin0InfoVect1LinkObjId="SW-308051_0" Pin0InfoVect2LinkObjId="g_297a2a0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1489,438 1489,475 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d54230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,475 1489,556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2713790@0" ObjectIDND1="47900@x" ObjectIDND2="47899@x" ObjectIDZND0="g_297a2a0@1" Pin0InfoVect0LinkObjId="g_297a2a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2713790_0" Pin1InfoVect1LinkObjId="SW-308051_0" Pin1InfoVect2LinkObjId="SW-308050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1489,475 1489,556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d54490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1489,605 1489,650 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_297a2a0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_297a2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1489,605 1489,650 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e7fc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,370 1634,380 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47902@1" ObjectIDZND0="47901@1" Pin0InfoVect0LinkObjId="SW-308054_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308055_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1634,370 1634,380 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e77d50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,407 1634,419 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47901@0" ObjectIDZND0="47903@1" Pin0InfoVect0LinkObjId="SW-308055_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308054_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1634,407 1634,419 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e756d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,353 1634,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47902@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1634,353 1634,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e62550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1675,493 1675,473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e75930@0" ObjectIDZND0="47903@x" ObjectIDZND1="g_2eb2d00@0" ObjectIDZND2="47904@x" Pin0InfoVect0LinkObjId="SW-308055_0" Pin0InfoVect1LinkObjId="g_2eb2d00_0" Pin0InfoVect2LinkObjId="SW-308056_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e75930_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1675,493 1675,473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e627b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,473 1675,473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47903@x" ObjectIDND1="g_2eb2d00@0" ObjectIDZND0="g_2e75930@0" ObjectIDZND1="47904@x" Pin0InfoVect0LinkObjId="g_2e75930_0" Pin0InfoVect1LinkObjId="SW-308056_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308055_0" Pin1InfoVect1LinkObjId="g_2eb2d00_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1634,473 1675,473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e62a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1675,473 1710,473 1710,489 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2e75930@0" ObjectIDND1="47903@x" ObjectIDND2="g_2eb2d00@0" ObjectIDZND0="47904@1" Pin0InfoVect0LinkObjId="SW-308056_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e75930_0" Pin1InfoVect1LinkObjId="SW-308055_0" Pin1InfoVect2LinkObjId="g_2eb2d00_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1675,473 1710,473 1710,489 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb2840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1710,537 1710,525 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d360b0@0" ObjectIDZND0="47904@0" Pin0InfoVect0LinkObjId="SW-308056_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d360b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1710,537 1710,525 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb2aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,436 1634,473 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47903@0" ObjectIDZND0="g_2e75930@0" ObjectIDZND1="47904@x" ObjectIDZND2="g_2eb2d00@0" Pin0InfoVect0LinkObjId="g_2e75930_0" Pin0InfoVect1LinkObjId="SW-308056_0" Pin0InfoVect2LinkObjId="g_2eb2d00_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308055_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1634,436 1634,473 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b0020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,473 1634,554 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e75930@0" ObjectIDND1="47904@x" ObjectIDND2="47903@x" ObjectIDZND0="g_2eb2d00@1" Pin0InfoVect0LinkObjId="g_2eb2d00_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e75930_0" Pin1InfoVect1LinkObjId="SW-308056_0" Pin1InfoVect2LinkObjId="SW-308055_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1634,473 1634,554 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29b0280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1634,603 1634,648 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2eb2d00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb2d00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1634,603 1634,648 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ca3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,371 1798,381 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47917@1" ObjectIDZND0="47916@1" Pin0InfoVect0LinkObjId="SW-308072_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308073_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1798,371 1798,381 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29ca650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,408 1798,420 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47916@0" ObjectIDZND0="47918@1" Pin0InfoVect0LinkObjId="SW-308073_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308072_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1798,408 1798,420 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27180d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,354 1798,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47917@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1798,354 1798,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2988250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,494 1840,474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2718310@0" ObjectIDZND0="47918@x" ObjectIDZND1="g_2d3c9f0@0" ObjectIDZND2="47920@x" Pin0InfoVect0LinkObjId="SW-308073_0" Pin0InfoVect1LinkObjId="g_2d3c9f0_0" Pin0InfoVect2LinkObjId="SW-308075_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2718310_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1840,494 1840,474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29884b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,474 1840,474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="47918@x" ObjectIDND1="g_2d3c9f0@0" ObjectIDZND0="g_2718310@0" ObjectIDZND1="47920@x" Pin0InfoVect0LinkObjId="g_2718310_0" Pin0InfoVect1LinkObjId="SW-308075_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308073_0" Pin1InfoVect1LinkObjId="g_2d3c9f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1798,474 1840,474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2988710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,474 1875,474 1875,490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2718310@0" ObjectIDND1="47918@x" ObjectIDND2="g_2d3c9f0@0" ObjectIDZND0="47920@1" Pin0InfoVect0LinkObjId="SW-308075_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2718310_0" Pin1InfoVect1LinkObjId="SW-308073_0" Pin1InfoVect2LinkObjId="g_2d3c9f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1840,474 1875,474 1875,490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eca3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1875,538 1875,526 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ec9920@0" ObjectIDZND0="47920@0" Pin0InfoVect0LinkObjId="SW-308075_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ec9920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1875,538 1875,526 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3c790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,437 1798,474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47918@0" ObjectIDZND0="g_2718310@0" ObjectIDZND1="47920@x" ObjectIDZND2="g_2d3c9f0@0" Pin0InfoVect0LinkObjId="g_2718310_0" Pin0InfoVect1LinkObjId="SW-308075_0" Pin0InfoVect2LinkObjId="g_2d3c9f0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308073_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1798,437 1798,474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2d3d270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,504 1798,474 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2d3c9f0@1" ObjectIDZND0="g_2718310@0" ObjectIDZND1="47920@x" ObjectIDZND2="47918@x" Pin0InfoVect0LinkObjId="g_2718310_0" Pin0InfoVect1LinkObjId="SW-308075_0" Pin0InfoVect2LinkObjId="SW-308073_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3c9f0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1798,504 1798,474 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_298de70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1817,704 1799,704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="47921@0" ObjectIDZND0="47919@x" ObjectIDZND1="g_2e52ff0@0" ObjectIDZND2="47922@x" Pin0InfoVect0LinkObjId="SW-308074_0" Pin0InfoVect1LinkObjId="g_2e52ff0_0" Pin0InfoVect2LinkObjId="SW-308077_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308076_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1817,704 1799,704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e52b40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,653 1799,607 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47919@1" ObjectIDZND0="g_2d3d4d0@1" Pin0InfoVect0LinkObjId="g_2d3d4d0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308074_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,653 1799,607 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e52dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,704 1799,689 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="47921@x" ObjectIDND1="g_2e52ff0@0" ObjectIDND2="47922@x" ObjectIDZND0="47919@0" Pin0InfoVect0LinkObjId="SW-308074_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-308076_0" Pin1InfoVect1LinkObjId="g_2e52ff0_0" Pin1InfoVect2LinkObjId="SW-308077_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,704 1799,689 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2968f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,906 1799,889 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="reactance" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,906 1799,889 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2969110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,825 1756,825 1756,805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="breaker" ObjectIDND0="0@x" ObjectIDND1="47922@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-308077_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1799,825 1756,825 1756,805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1756,779 1756,758 1799,758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDZND0="g_2e52ff0@0" ObjectIDZND1="47921@x" ObjectIDZND2="47919@x" Pin0InfoVect0LinkObjId="g_2e52ff0_0" Pin0InfoVect1LinkObjId="SW-308076_0" Pin0InfoVect2LinkObjId="SW-308074_0" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1756,779 1756,758 1799,758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,773 1799,758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="47922@1" ObjectIDZND0="g_2e52ff0@0" ObjectIDZND1="47921@x" ObjectIDZND2="47919@x" Pin0InfoVect0LinkObjId="g_2e52ff0_0" Pin0InfoVect1LinkObjId="SW-308076_0" Pin0InfoVect2LinkObjId="SW-308074_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308077_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1799,773 1799,758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,847 1799,825 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@0" ObjectIDZND0="47922@x" Pin0InfoVect0LinkObjId="SW-308077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,847 1799,825 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3386150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,825 1799,809 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="47922@0" Pin0InfoVect0LinkObjId="SW-308077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,825 1799,809 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ebac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,374 2156,384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2156,374 2156,384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ebae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,411 2156,423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2156,411 2156,423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2716030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,357 2156,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2156,357 2156,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ecbdd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2197,497 2197,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2716290@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2629db0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2629db0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2716290_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2197,497 2197,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ecc030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,477 2197,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2629db0@0" ObjectIDZND0="g_2716290@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2716290_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2629db0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2156,477 2197,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ecc290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2197,477 2232,477 2232,493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2629db0@0" ObjectIDND2="g_2716290@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2629db0_0" Pin1InfoVect2LinkObjId="g_2716290_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2197,477 2232,477 2232,493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_26298f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2232,541 2232,529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29fc2d0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29fc2d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2232,541 2232,529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2629b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,440 2156,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2716290@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2629db0@0" Pin0InfoVect0LinkObjId="g_2716290_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2629db0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2156,440 2156,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e6e980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,477 2156,558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2716290@0" ObjectIDND2="0@x" ObjectIDZND0="g_2629db0@1" Pin0InfoVect0LinkObjId="g_2629db0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2716290_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2156,477 2156,558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e6ebe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2156,607 2156,652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2629db0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2629db0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2156,607 2156,652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d3af40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,374 1980,384 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,374 1980,384 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d3b1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,411 1980,423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,411 1980,423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e73fa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,357 1980,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,357 1980,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d434a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2021,497 2021,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2e74200@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2e90c30@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_2e90c30_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e74200_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2021,497 2021,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d43700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,477 2021,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e90c30@0" ObjectIDZND0="g_2e74200@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_2e74200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e90c30_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1980,477 2021,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d43960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2021,477 2056,477 2056,493 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_2e90c30@0" ObjectIDND2="g_2e74200@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e90c30_0" Pin1InfoVect2LinkObjId="g_2e74200_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2021,477 2056,477 2056,493 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e90770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2056,541 2056,529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2ea0f00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ea0f00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2056,541 2056,529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e909d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,440 1980,477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_2e74200@0" ObjectIDZND1="0@x" ObjectIDZND2="g_2e90c30@0" Pin0InfoVect0LinkObjId="g_2e74200_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_2e90c30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1980,440 1980,477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e91650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,477 1980,558 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_2e74200@0" ObjectIDND2="0@x" ObjectIDZND0="g_2e90c30@1" Pin0InfoVect0LinkObjId="g_2e90c30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_2e74200_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,477 1980,558 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e918b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,607 1980,652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_2e90c30@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e90c30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,607 1980,652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e6d870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,202 1760,212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="47872@1" ObjectIDZND0="g_2eb6110@1" Pin0InfoVect0LinkObjId="g_2eb6110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308018_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,202 1760,212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed1090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,185 1760,150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="voltageTransformer" ObjectIDND0="47872@0" ObjectIDZND0="g_262bea0@0" Pin0InfoVect0LinkObjId="g_262bea0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308018_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,185 1760,150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ed12f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,320 1760,298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="47844@0" ObjectIDZND0="47871@0" Pin0InfoVect0LinkObjId="SW-308018_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d402d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,320 1760,298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e0120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,79 943,97 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="47906@1" ObjectIDZND0="47905@1" Pin0InfoVect0LinkObjId="SW-308059_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308060_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,79 943,97 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e0380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,124 943,142 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="47905@0" ObjectIDZND0="47907@1" Pin0InfoVect0LinkObjId="SW-308060_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308059_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,124 943,142 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e05e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,159 943,219 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="47907@0" ObjectIDZND0="g_26242c0@0" ObjectIDZND1="47908@x" ObjectIDZND2="g_2ee3350@0" Pin0InfoVect0LinkObjId="g_26242c0_0" Pin0InfoVect1LinkObjId="SW-308061_0" Pin0InfoVect2LinkObjId="g_2ee3350_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="943,159 943,219 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ece330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="144,816 144,836 187,836 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="reactance" EndDevType1="breaker" ObjectIDZND0="0@x" ObjectIDZND1="47915@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-308069_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="144,816 144,836 187,836 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ece590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,836 187,820 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDZND0="47915@0" Pin0InfoVect0LinkObjId="SW-308069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,836 187,820 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_288ac20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-533 311,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_288a190@0" ObjectIDZND0="47853@0" Pin0InfoVect0LinkObjId="SW-307998_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_288a190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="287,-533 311,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_288ae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="347,-533 365,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="47853@1" ObjectIDZND0="47842@0" ObjectIDZND1="47852@x" Pin0InfoVect0LinkObjId="g_2e5a0a0_0" Pin0InfoVect1LinkObjId="SW-307997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307998_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="347,-533 365,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_295ebb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-611 365,-533 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47842@0" ObjectIDZND0="47853@x" ObjectIDZND1="47852@x" Pin0InfoVect0LinkObjId="SW-307998_0" Pin0InfoVect1LinkObjId="SW-307997_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_288ae80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="365,-611 365,-533 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_296d6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-533 365,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="switch" ObjectIDND0="47853@x" ObjectIDND1="47842@0" ObjectIDZND0="47852@1" Pin0InfoVect0LinkObjId="SW-307997_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307998_0" Pin1InfoVect1LinkObjId="g_288ae80_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="365,-533 365,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29ba6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="287,-416 311,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29b9c10@0" ObjectIDZND0="47854@0" Pin0InfoVect0LinkObjId="SW-307999_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29b9c10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="287,-416 311,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29ba900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="347,-416 365,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="47854@1" ObjectIDZND0="47852@x" ObjectIDZND1="g_33cf260@0" ObjectIDZND2="g_261be40@0" Pin0InfoVect0LinkObjId="SW-307997_0" Pin0InfoVect1LinkObjId="g_33cf260_0" Pin0InfoVect2LinkObjId="g_261be40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307999_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="347,-416 365,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e59e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-461 365,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="47852@0" ObjectIDZND0="47854@x" ObjectIDZND1="g_33cf260@0" ObjectIDZND2="g_261be40@0" Pin0InfoVect0LinkObjId="SW-307999_0" Pin0InfoVect1LinkObjId="g_33cf260_0" Pin0InfoVect2LinkObjId="g_261be40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307997_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="365,-461 365,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e5a0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-577 943,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47856@1" ObjectIDZND0="47842@0" Pin0InfoVect0LinkObjId="g_288ae80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308001_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-577 943,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2972f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-513 890,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_29724b0@0" ObjectIDZND0="47858@0" Pin0InfoVect0LinkObjId="SW-308003_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29724b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-513 890,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29731a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-513 943,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47858@1" ObjectIDZND0="47855@x" ObjectIDZND1="47856@x" Pin0InfoVect0LinkObjId="SW-308000_0" Pin0InfoVect1LinkObjId="SW-308001_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308003_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="926,-513 943,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2973400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-486 943,-513 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47855@1" ObjectIDZND0="47858@x" ObjectIDZND1="47856@x" Pin0InfoVect0LinkObjId="SW-308003_0" Pin0InfoVect1LinkObjId="SW-308001_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308000_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,-486 943,-513 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2973660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-513 943,-541 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="47855@x" ObjectIDND1="47858@x" ObjectIDZND0="47856@0" Pin0InfoVect0LinkObjId="SW-308001_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308000_0" Pin1InfoVect1LinkObjId="SW-308003_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-513 943,-541 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e6be00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-434 889,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e6b370@0" ObjectIDZND0="47859@0" Pin0InfoVect0LinkObjId="SW-308004_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e6b370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="865,-434 889,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e6c060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="925,-434 943,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47859@1" ObjectIDZND0="47857@x" ObjectIDZND1="47855@x" Pin0InfoVect0LinkObjId="SW-308002_0" Pin0InfoVect1LinkObjId="SW-308000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308004_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="925,-434 943,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e6c2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-411 943,-434 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47857@1" ObjectIDZND0="47859@x" ObjectIDZND1="47855@x" Pin0InfoVect0LinkObjId="SW-308004_0" Pin0InfoVect1LinkObjId="SW-308000_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308002_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,-411 943,-434 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2d368c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-434 943,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47857@x" ObjectIDND1="47859@x" ObjectIDZND0="47855@0" Pin0InfoVect0LinkObjId="SW-308000_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308002_0" Pin1InfoVect1LinkObjId="SW-308004_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-434 943,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2963320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="866,-350 890,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2d38ec0@0" ObjectIDZND0="47860@0" Pin0InfoVect0LinkObjId="SW-308005_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d38ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="866,-350 890,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2963580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="926,-350 943,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="47860@1" ObjectIDZND0="47857@x" ObjectIDZND1="g_33d2f20@0" ObjectIDZND2="47925@x" Pin0InfoVect0LinkObjId="SW-308002_0" Pin0InfoVect1LinkObjId="g_33d2f20_0" Pin0InfoVect2LinkObjId="g_33c9440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308005_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="926,-350 943,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_29637e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-350 943,-375 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="47860@x" ObjectIDND1="g_33d2f20@0" ObjectIDND2="47925@x" ObjectIDZND0="47857@0" Pin0InfoVect0LinkObjId="SW-308002_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-308005_0" Pin1InfoVect1LinkObjId="g_33d2f20_0" Pin1InfoVect2LinkObjId="g_33c9440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-350 943,-375 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d24e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-713 1547,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-713 1547,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2d25060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-676 1547,-664 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-676 1547,-664 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c67a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-647 1547,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-647 1547,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29c7420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-855 1547,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_29c6a00@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29c6a00_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-855 1547,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2eadf80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-1020 1547,-1045 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_2ead7a0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ead7a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-1020 1547,-1045 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29d4fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1880,-688 1880,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1880,-688 1880,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29d5240">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1880,-651 1880,-639 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1880,-651 1880,-639 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2e7d950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1880,-622 1880,-587 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1880,-622 1880,-587 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ebe9c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-711 1994,-701 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-711 1994,-701 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2ebec20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-674 1994,-662 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-674 1994,-662 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29f5710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-645 1994,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-645 1994,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3787f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-847 1994,-870 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_29f5970@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f5970_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-847 1994,-870 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3788a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-980 1994,-964 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_3788c20@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3788c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-980 1994,-964 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3789470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1993,-1009 1993,-1031 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" ObjectIDND0="g_3788c20@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3788c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1993,-1009 1993,-1031 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9d700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1274,248 1274,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47867@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308011_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1274,248 1274,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9d8f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="649,163 649,30 1274,30 1274,165 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47865@0" ObjectIDZND0="47868@0" Pin0InfoVect0LinkObjId="SW-308011_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308009_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="649,163 649,30 1274,30 1274,165 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9db10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="186,589 186,555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2e82720@0" ObjectIDZND0="g_2987aa0@0" Pin0InfoVect0LinkObjId="g_2987aa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e82720_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="186,589 186,555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e9dd40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1798,553 1798,578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2d3c9f0@0" ObjectIDZND0="g_2d3d4d0@0" Pin0InfoVect0LinkObjId="g_2d3d4d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2d3c9f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1798,553 1798,578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8d7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-706 578,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e8cd50@0" ObjectIDZND0="47849@1" Pin0InfoVect0LinkObjId="SW-307987_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e8cd50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-706 578,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8da40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-706 632,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="47849@0" ObjectIDZND0="47846@x" ObjectIDZND1="47847@x" Pin0InfoVect0LinkObjId="SW-307984_0" Pin0InfoVect1LinkObjId="SW-307985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307987_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="614,-706 632,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8dca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-733 632,-706 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="47846@1" ObjectIDZND0="47849@x" ObjectIDZND1="47847@x" Pin0InfoVect0LinkObjId="SW-307987_0" Pin0InfoVect1LinkObjId="SW-307985_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307984_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,-733 632,-706 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8df00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-706 632,-678 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="47849@x" ObjectIDND1="47846@x" ObjectIDZND0="47847@1" Pin0InfoVect0LinkObjId="SW-307985_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307987_0" Pin1InfoVect1LinkObjId="SW-307984_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-706 632,-678 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8ebf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="553,-785 577,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e8e160@0" ObjectIDZND0="47850@1" Pin0InfoVect0LinkObjId="SW-307988_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e8e160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="553,-785 577,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8ee50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,-785 631,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47850@0" ObjectIDZND0="47848@x" ObjectIDZND1="47846@x" Pin0InfoVect0LinkObjId="SW-307986_0" Pin0InfoVect1LinkObjId="SW-307984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307988_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="613,-785 631,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8f0b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-808 632,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="47848@0" ObjectIDZND0="47850@x" ObjectIDZND1="47846@x" Pin0InfoVect0LinkObjId="SW-307988_0" Pin0InfoVect1LinkObjId="SW-307984_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307986_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,-808 632,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e8f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-785 632,-760 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="47850@x" ObjectIDND1="47848@x" ObjectIDZND0="47846@0" Pin0InfoVect0LinkObjId="SW-307984_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307988_0" Pin1InfoVect1LinkObjId="SW-307986_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-785 632,-760 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4b7e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="554,-869 578,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2e8f570@0" ObjectIDZND0="47851@1" Pin0InfoVect0LinkObjId="SW-307989_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e8f570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="554,-869 578,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4ba10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="614,-869 632,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="47851@0" ObjectIDZND0="47848@x" ObjectIDZND1="g_261fdb0@0" ObjectIDZND2="g_334c950@0" Pin0InfoVect0LinkObjId="SW-307986_0" Pin0InfoVect1LinkObjId="g_261fdb0_0" Pin0InfoVect2LinkObjId="g_334c950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307989_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="614,-869 632,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2e4bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-869 632,-844 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="47851@x" ObjectIDND1="g_261fdb0@0" ObjectIDND2="g_334c950@0" ObjectIDZND0="47848@1" Pin0InfoVect0LinkObjId="SW-307986_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-307989_0" Pin1InfoVect1LinkObjId="g_261fdb0_0" Pin1InfoVect2LinkObjId="g_334c950_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-869 632,-844 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_261e830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-642 632,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47847@0" ObjectIDZND0="47842@0" Pin0InfoVect0LinkObjId="g_288ae80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-307985_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="632,-642 632,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_261f060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="666,-943 632,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_261fdb0@0" ObjectIDZND0="47851@x" ObjectIDZND1="47848@x" ObjectIDZND2="g_334c950@0" Pin0InfoVect0LinkObjId="SW-307989_0" Pin0InfoVect1LinkObjId="SW-307986_0" Pin0InfoVect2LinkObjId="g_334c950_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_261fdb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="666,-943 632,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_261fb50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-943 632,-869 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="load" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_261fdb0@0" ObjectIDND1="g_334c950@0" ObjectIDND2="0@x" ObjectIDZND0="47851@x" ObjectIDZND1="47848@x" Pin0InfoVect0LinkObjId="SW-307989_0" Pin0InfoVect1LinkObjId="SW-307986_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_261fdb0_0" Pin1InfoVect1LinkObjId="g_334c950_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="632,-943 632,-869 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_334d5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="600,-973 632,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_334c950@0" ObjectIDZND0="g_261fdb0@0" ObjectIDZND1="47851@x" ObjectIDZND2="47848@x" Pin0InfoVect0LinkObjId="g_261fdb0_0" Pin0InfoVect1LinkObjId="SW-307989_0" Pin0InfoVect2LinkObjId="SW-307986_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_334c950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="600,-973 632,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_334e0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-1028 632,-973 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_334c950@0" ObjectIDZND1="g_261fdb0@0" ObjectIDZND2="47851@x" Pin0InfoVect0LinkObjId="g_334c950_0" Pin0InfoVect1LinkObjId="g_261fdb0_0" Pin0InfoVect2LinkObjId="SW-307989_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="632,-1028 632,-973 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_334e300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="632,-973 632,-943 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_334c950@0" ObjectIDND1="0@x" ObjectIDZND0="g_261fdb0@0" ObjectIDZND1="47851@x" ObjectIDZND2="47848@x" Pin0InfoVect0LinkObjId="g_261fdb0_0" Pin0InfoVect1LinkObjId="SW-307989_0" Pin0InfoVect2LinkObjId="SW-307986_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_334c950_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="632,-973 632,-943 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1042,-704 1066,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_3350b40@0" ObjectIDZND0="47924@1" Pin0InfoVect0LinkObjId="SW-308081_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3350b40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-704 1066,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33517d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1102,-704 1120,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47924@0" ObjectIDZND0="47923@x" Pin0InfoVect0LinkObjId="SW-308080_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1102,-704 1120,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-704 1120,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" ObjectIDND0="47924@x" ObjectIDZND0="47923@1" Pin0InfoVect0LinkObjId="SW-308080_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-704 1120,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3354d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-704 1120,-735 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" ObjectIDND0="47924@x" ObjectIDND1="47923@x" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308081_0" Pin1InfoVect1LinkObjId="SW-308080_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-704 1120,-735 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_335a9e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-83,377 -83,387 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,377 -83,387 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_335ac40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-83,414 -83,426 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,414 -83,426 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3360a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-42,500 -42,480 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_335fc50@0" ObjectIDZND0="0@x" ObjectIDZND1="g_3364540@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_3364540_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_335fc50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-42,500 -42,480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3360c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-83,480 -42,480 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_3364540@0" ObjectIDZND0="g_335fc50@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_335fc50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3364540_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="-83,480 -42,480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3360ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-42,480 -7,480 -7,496 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_3364540@0" ObjectIDND2="g_335fc50@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3364540_0" Pin1InfoVect2LinkObjId="g_335fc50_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-42,480 -7,480 -7,496 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3364080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="-7,544 -7,532 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33635f0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33635f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-7,544 -7,532 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33642e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-83,443 -83,480 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_335fc50@0" ObjectIDZND1="0@x" ObjectIDZND2="g_3364540@0" Pin0InfoVect0LinkObjId="g_335fc50_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_3364540_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="-83,443 -83,480 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3394c60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-83,480 -83,561 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_335fc50@0" ObjectIDND2="0@x" ObjectIDZND0="g_3364540@1" Pin0InfoVect0LinkObjId="g_3364540_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_335fc50_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,480 -83,561 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3394e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-83,610 -83,655 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_3364540@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3364540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,610 -83,655 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3397260">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-221,358 -221,321 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-221,358 -221,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3397a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="-83,360 -83,321 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47843@0" Pin0InfoVect0LinkObjId="g_29d8340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="-83,360 -83,321 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339e5b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,380 2350,390 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,380 2350,390 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,417 2350,429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,417 2350,429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a4670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,503 2391,483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_33a38c0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_33a81b0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_33a81b0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a38c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2391,503 2391,483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a48d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,483 2391,483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_33a81b0@0" ObjectIDZND0="g_33a38c0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_33a38c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33a81b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2350,483 2391,483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a4b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2391,483 2426,483 2426,499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_33a81b0@0" ObjectIDND2="g_33a38c0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33a81b0_0" Pin1InfoVect2LinkObjId="g_33a38c0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2391,483 2426,483 2426,499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a7cf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2426,547 2426,535 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_33a7260@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a7260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2426,547 2426,535 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a7f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,446 2350,483 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_33a38c0@0" ObjectIDZND1="0@x" ObjectIDZND2="g_33a81b0@0" Pin0InfoVect0LinkObjId="g_33a38c0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="g_33a81b0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="2350,446 2350,483 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a8bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,483 2350,564 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="g_33a38c0@0" ObjectIDND2="0@x" ObjectIDZND0="g_33a81b0@1" Pin0InfoVect0LinkObjId="g_33a81b0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33a38c0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,483 2350,564 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33a8e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,613 2350,658 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_33a81b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33a81b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,613 2350,658 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33aadc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="2350,363 2350,320 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@0" ObjectIDZND0="47844@0" Pin0InfoVect0LinkObjId="g_2d402d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2350,363 2350,320 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33ad150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-991 1547,-972 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2ead7a0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ead7a0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-991 1547,-972 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b0450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-714 1639,-704 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-714 1639,-704 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b06b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-677 1639,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-677 1639,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b5760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-648 1639,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" ObjectIDND0="0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-648 1639,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b8940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1597,-492 1597,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1597,-492 1597,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b8ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1941,-492 1941,-431 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="busSection" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1941,-492 1941,-431 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b9690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-730 1547,-743 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_29c6a00@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_29c6a00_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-730 1547,-743 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b98f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-743 1547,-806 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_29c6a00@0" Pin0InfoVect0LinkObjId="g_29c6a00_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-743 1547,-806 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b9b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1547,-743 1880,-743 1880,-705 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_29c6a00@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_29c6a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1547,-743 1880,-743 1880,-705 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33b9db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1639,-731 1639,-772 1994,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_29f5970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_29f5970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1639,-731 1639,-772 1994,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33ba8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-728 1994,-772 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="g_29f5970@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="g_29f5970_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-728 1994,-772 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33bab00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1994,-772 1994,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="g_29f5970@0" Pin0InfoVect0LinkObjId="g_29f5970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1994,-772 1994,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bd8e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="168,733 187,733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2eb7590@0" ObjectIDZND0="47912@x" ObjectIDZND1="47914@x" ObjectIDZND2="47915@x" Pin0InfoVect0LinkObjId="SW-308066_0" Pin0InfoVect1LinkObjId="SW-308068_0" Pin0InfoVect2LinkObjId="SW-308069_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb7590_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="168,733 187,733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33be3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,715 187,733 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="47912@x" ObjectIDND1="47914@x" ObjectIDZND0="g_2eb7590@0" ObjectIDZND1="47915@x" Pin0InfoVect0LinkObjId="g_2eb7590_0" Pin0InfoVect1LinkObjId="SW-308069_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308066_0" Pin1InfoVect1LinkObjId="SW-308068_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="187,715 187,733 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33be630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="187,733 187,769 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_2eb7590@0" ObjectIDND1="47912@x" ObjectIDND2="47914@x" ObjectIDZND0="47915@x" Pin0InfoVect0LinkObjId="SW-308069_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2eb7590_0" Pin1InfoVect1LinkObjId="SW-308066_0" Pin1InfoVect2LinkObjId="SW-308068_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="187,733 187,769 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c30f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1780,722 1799,722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_2e52ff0@0" ObjectIDZND0="47921@x" ObjectIDZND1="47919@x" ObjectIDZND2="47922@x" Pin0InfoVect0LinkObjId="SW-308076_0" Pin0InfoVect1LinkObjId="SW-308074_0" Pin0InfoVect2LinkObjId="SW-308077_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e52ff0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1780,722 1799,722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c3be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,704 1799,722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="47921@x" ObjectIDND1="47919@x" ObjectIDZND0="g_2e52ff0@0" ObjectIDZND1="47922@x" Pin0InfoVect0LinkObjId="g_2e52ff0_0" Pin0InfoVect1LinkObjId="SW-308077_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308076_0" Pin1InfoVect1LinkObjId="SW-308074_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1799,704 1799,722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33c3e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1799,722 1799,758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" ObjectIDND0="g_2e52ff0@0" ObjectIDND1="47921@x" ObjectIDND2="47919@x" ObjectIDZND0="47922@x" Pin0InfoVect0LinkObjId="SW-308077_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2e52ff0_0" Pin1InfoVect1LinkObjId="SW-308076_0" Pin1InfoVect2LinkObjId="SW-308074_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1799,722 1799,758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33c86f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-228 1066,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="transformer" ObjectIDND0="g_29654b0@1" ObjectIDZND0="47861@x" ObjectIDZND1="g_33c7950@0" ObjectIDZND2="47925@x" Pin0InfoVect0LinkObjId="SW-308006_0" Pin0InfoVect1LinkObjId="g_33c7950_0" Pin0InfoVect2LinkObjId="g_33c9440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29654b0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-228 1066,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33c91e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1066,-218 1113,-218 1113,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="transformer" EndDevType0="switch" ObjectIDND0="g_29654b0@0" ObjectIDND1="g_33c7950@0" ObjectIDND2="47925@x" ObjectIDZND0="47861@1" Pin0InfoVect0LinkObjId="SW-308006_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_29654b0_0" Pin1InfoVect1LinkObjId="g_33c7950_0" Pin1InfoVect2LinkObjId="g_33c9440_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1066,-218 1113,-218 1113,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33c9440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-232 1027,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_33c7950@0" ObjectIDZND0="47925@x" ObjectIDZND1="g_29654b0@0" ObjectIDZND2="47861@x" Pin0InfoVect0LinkObjId="g_33cd040_0" Pin0InfoVect1LinkObjId="g_29654b0_0" Pin0InfoVect2LinkObjId="SW-308006_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c7950_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-232 1027,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33c9f30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="942,-218 1027,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="47925@x" ObjectIDZND0="g_33c7950@0" ObjectIDZND1="g_29654b0@0" ObjectIDZND2="47861@x" Pin0InfoVect0LinkObjId="g_33c7950_0" Pin0InfoVect1LinkObjId="g_29654b0_0" Pin0InfoVect2LinkObjId="SW-308006_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="942,-218 1027,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33ca190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1027,-218 1066,-218 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_33c7950@0" ObjectIDND1="47925@x" ObjectIDZND0="g_29654b0@0" ObjectIDZND1="47861@x" Pin0InfoVect0LinkObjId="g_29654b0_0" Pin0InfoVect1LinkObjId="SW-308006_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33c7950_0" Pin1InfoVect1LinkObjId="g_33c9440_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1027,-218 1066,-218 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cae80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1136,-171 1116,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_33ca3f0@0" ObjectIDZND0="g_299d2f0@1" Pin0InfoVect0LinkObjId="g_299d2f0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33ca3f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1136,-171 1116,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cb0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-151 1065,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_299df20@0" ObjectIDZND0="g_299d2f0@0" ObjectIDZND1="47862@x" Pin0InfoVect0LinkObjId="g_299d2f0_0" Pin0InfoVect1LinkObjId="SW-308007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_299df20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-151 1065,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cbbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1082,-171 1065,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_299d2f0@0" ObjectIDZND0="g_299df20@0" ObjectIDZND1="47862@x" Pin0InfoVect0LinkObjId="g_299df20_0" Pin0InfoVect1LinkObjId="SW-308007_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_299d2f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1082,-171 1065,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cbe30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1065,-171 1037,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_299df20@0" ObjectIDND1="g_299d2f0@0" ObjectIDZND0="47862@1" Pin0InfoVect0LinkObjId="SW-308007_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_299df20_0" Pin1InfoVect1LinkObjId="g_299d2f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1065,-171 1037,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cc090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-171 1001,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="47925@x" ObjectIDZND0="47862@0" Pin0InfoVect0LinkObjId="SW-308007_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c9440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="946,-171 1001,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cc2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="993,-38 943,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="transformer" ObjectIDND0="g_2ecee70@0" ObjectIDZND0="47906@x" ObjectIDZND1="47925@x" Pin0InfoVect0LinkObjId="SW-308060_0" Pin0InfoVect1LinkObjId="g_33c9440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ecee70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="993,-38 943,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33ccde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,62 943,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="47906@0" ObjectIDZND0="g_2ecee70@0" ObjectIDZND1="47925@x" Pin0InfoVect0LinkObjId="g_2ecee70_0" Pin0InfoVect1LinkObjId="g_33c9440_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,62 943,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33cd040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-38 943,-153 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="transformer" ObjectIDND0="g_2ecee70@0" ObjectIDND1="47906@x" ObjectIDZND0="47925@1" Pin0InfoVect0LinkObjId="g_33c9440_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ecee70_0" Pin1InfoVect1LinkObjId="SW-308060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-38 943,-153 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33ce050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-150 865,-173 878,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" ObjectIDND0="g_33cd2a0@0" ObjectIDZND0="47925@0" Pin0InfoVect0LinkObjId="g_33c9440_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cd2a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="865,-150 865,-173 878,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33ce2b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-301 1088,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_29e9df0@0" ObjectIDZND0="47861@x" ObjectIDZND1="g_29654b0@0" Pin0InfoVect0LinkObjId="SW-308006_0" Pin0InfoVect1LinkObjId="g_29654b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e9df0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-301 1088,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33ceda0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1113,-273 1113,-288 1088,-288 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" EndDevType1="lightningRod" ObjectIDND0="47861@0" ObjectIDZND0="g_29e9df0@0" ObjectIDZND1="g_29654b0@0" Pin0InfoVect0LinkObjId="g_29e9df0_0" Pin0InfoVect1LinkObjId="g_29654b0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308006_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1113,-273 1113,-288 1088,-288 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33cf000">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1088,-288 1066,-288 1066,-268 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_29e9df0@0" ObjectIDND1="47861@x" ObjectIDZND0="g_29654b0@0" Pin0InfoVect0LinkObjId="g_29654b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29e9df0_0" Pin1InfoVect1LinkObjId="SW-308006_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1088,-288 1066,-288 1066,-268 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d0010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="333,-377 365,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_33cf260@0" ObjectIDZND0="47854@x" ObjectIDZND1="47852@x" ObjectIDZND2="g_261be40@0" Pin0InfoVect0LinkObjId="SW-307999_0" Pin0InfoVect1LinkObjId="SW-307997_0" Pin0InfoVect2LinkObjId="g_261be40_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cf260_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="333,-377 365,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d0b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-416 365,-377 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="voltageTransformer" ObjectIDND0="47854@x" ObjectIDND1="47852@x" ObjectIDZND0="g_33cf260@0" ObjectIDZND1="g_261be40@0" Pin0InfoVect0LinkObjId="g_33cf260_0" Pin0InfoVect1LinkObjId="g_261be40_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-307999_0" Pin1InfoVect1LinkObjId="SW-307997_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="365,-416 365,-377 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d0d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="365,-377 365,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="voltageTransformer" ObjectIDND0="g_33cf260@0" ObjectIDND1="47854@x" ObjectIDND2="47852@x" ObjectIDZND0="g_261be40@0" Pin0InfoVect0LinkObjId="g_261be40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33cf260_0" Pin1InfoVect1LinkObjId="SW-307999_0" Pin1InfoVect2LinkObjId="SW-307997_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="365,-377 365,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d0fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="418,247 418,271 378,271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="lightningRod" ObjectIDND0="g_2e3a380@0" ObjectIDZND0="47869@x" ObjectIDZND1="g_2e9a9f0@0" Pin0InfoVect0LinkObjId="SW-308017_0" Pin0InfoVect1LinkObjId="g_2e9a9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e3a380_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="418,247 418,271 378,271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1ab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,282 378,271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="47869@1" ObjectIDZND0="g_2e3a380@0" ObjectIDZND1="g_2e9a9f0@0" Pin0InfoVect0LinkObjId="g_2e3a380_0" Pin0InfoVect1LinkObjId="g_2e9a9f0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308017_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="378,282 378,271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="378,271 378,255 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2e3a380@0" ObjectIDND1="47869@x" ObjectIDZND0="g_2e9a9f0@0" Pin0InfoVect0LinkObjId="g_2e9a9f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e3a380_0" Pin1InfoVect1LinkObjId="SW-308017_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="378,271 378,255 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d1f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1801,248 1801,270 1760,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2ed1550@0" ObjectIDZND0="g_2eb6110@0" ObjectIDZND1="47871@x" Pin0InfoVect0LinkObjId="g_2eb6110_0" Pin0InfoVect1LinkObjId="SW-308018_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ed1550_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1801,248 1801,270 1760,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d2a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,254 1760,270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2eb6110@0" ObjectIDZND0="g_2ed1550@0" ObjectIDZND1="47871@x" Pin0InfoVect0LinkObjId="g_2ed1550_0" Pin0InfoVect1LinkObjId="SW-308018_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2eb6110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1760,254 1760,270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33d2cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1760,270 1760,281 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2ed1550@0" ObjectIDND1="g_2eb6110@0" ObjectIDZND0="47871@1" Pin0InfoVect0LinkObjId="SW-308018_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ed1550_0" Pin1InfoVect1LinkObjId="g_2eb6110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1760,270 1760,281 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d3cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-292 943,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="transformer" ObjectIDND0="g_33d2f20@0" ObjectIDZND0="47860@x" ObjectIDZND1="47857@x" ObjectIDZND2="47925@x" Pin0InfoVect0LinkObjId="SW-308005_0" Pin0InfoVect1LinkObjId="SW-308002_0" Pin0InfoVect2LinkObjId="g_33c9440_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33d2f20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="900,-292 943,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d47c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-350 943,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="transformer" ObjectIDND0="47860@x" ObjectIDND1="47857@x" ObjectIDZND0="g_33d2f20@0" ObjectIDZND1="47925@x" Pin0InfoVect0LinkObjId="g_33d2f20_0" Pin0InfoVect1LinkObjId="g_33c9440_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-308005_0" Pin1InfoVect1LinkObjId="SW-308002_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="943,-350 943,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_33d4a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="943,-292 943,-237 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="transformer" ObjectIDND0="g_33d2f20@0" ObjectIDND1="47860@x" ObjectIDND2="47857@x" ObjectIDZND0="47925@2" Pin0InfoVect0LinkObjId="g_33c9440_2" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33d2f20_0" Pin1InfoVect1LinkObjId="SW-308005_0" Pin1InfoVect2LinkObjId="SW-308002_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="943,-292 943,-237 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33ed6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1120,-640 1120,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="47923@0" ObjectIDZND0="47842@0" Pin0InfoVect0LinkObjId="g_288ae80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-308080_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1120,-640 1120,-611 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-307566" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -376.000000 -849.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47740" ObjectName="DYN-CX_SBQ"/>
     <cge:Meas_Ref ObjectId="307566"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378a5e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1007.000000 489.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378ab50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 474.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_378b720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1021.000000 459.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9bb80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1024.000000 -91.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9be20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1013.000000 -106.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9c060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1038.000000 -121.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9c390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 116.000000 -1009.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9c5f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 105.000000 -1024.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9c830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 130.000000 -1039.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9cb60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1082.000000 -713.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9cdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1071.000000 -728.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9d000" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 -743.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3787850" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 659.000000 764.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26196b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 648.000000 749.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2619900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 673.000000 734.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1520" x2="1675" y1="492" y2="492"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1675" x2="1675" y1="554" y2="493"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1506" x2="1520" y1="591" y2="554"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1686" x2="1676" y1="591" y2="555"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1519" x2="1674" y1="586" y2="586"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1520" x2="1520" y1="554" y2="493"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1520" x2="1675" y1="492" y2="492"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1860" x2="2015" y1="492" y2="492"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2015" x2="2015" y1="554" y2="493"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1846" x2="1860" y1="591" y2="554"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="2026" x2="2016" y1="591" y2="555"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1859" x2="2014" y1="586" y2="586"/>
    <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(60,120,255)" stroke-width="1" x1="1860" x2="1860" y1="554" y2="493"/>
   <metadata/><line fill="none" opacity="0" stroke="white" stroke-width="10" transform="" x1="1860" x2="2015" y1="492" y2="492"/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 693.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8920" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 677.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e8b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.000000 662.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 723.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 180.000000 646.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e9bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 174.000000 708.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea3f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -279.000000 -219.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea680" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -273.000000 -235.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ea8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -289.000000 -250.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eab00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -279.000000 -189.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ead40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -273.000000 -266.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eaf80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -279.000000 -204.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2336.000000 -215.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 -231.000000) translate(0,12)">U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2326.000000 -246.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33eb9b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2336.000000 -185.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ebbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2342.000000 -262.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ebe30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2336.000000 -200.000000) translate(0,12)">Ub(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ecd70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 299.000000 -707.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ecfe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 288.000000 -722.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33ed220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 313.000000 -737.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 169.740650 947.000000)" xlink:href="#capacitor:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1781.970909 936.000000)" xlink:href="#capacitor:shape40"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="CB-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="47842" cx="943" cy="-611" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47842" cx="365" cy="-611" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="770" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="632" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="483" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="339" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="186" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="39" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="378" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="648" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="-221" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47843" cx="-83" cy="321" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1134" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1318" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1489" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1634" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1798" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="2156" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1980" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1760" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="1274" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47844" cx="2350" cy="320" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47842" cx="1120" cy="-611" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="47842" cx="632" cy="-611" fill="rgb(255,255,255)" r="4" stroke="rgb(255,255,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1597" cy="-431" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="0" cx="1941" cy="-431" fill="rgb(60,120,255)" r="4" stroke="rgb(60,120,255)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -231.190491 448.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -231.190491 382.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308061">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1023.742135 276.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47908" ObjectName="SW-CX_SBQ.CX_SBQ_31367SW"/>
     <cge:Meas_Ref ObjectId="308061"/>
    <cge:TPSR_Ref TObjectID="47908"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308035">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.299753 449.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47887" ObjectName="SW-CX_SBQ.CX_SBQ_364XC1"/>
     <cge:Meas_Ref ObjectId="308035"/>
    <cge:TPSR_Ref TObjectID="47887"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308035">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 760.299753 383.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47886" ObjectName="SW-CX_SBQ.CX_SBQ_364XC"/>
     <cge:Meas_Ref ObjectId="308035"/>
    <cge:TPSR_Ref TObjectID="47886"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308036">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 851.173171 536.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47888" ObjectName="SW-CX_SBQ.CX_SBQ_36467SW"/>
     <cge:Meas_Ref ObjectId="308036"/>
    <cge:TPSR_Ref TObjectID="47888"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308030">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.921704 449.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47883" ObjectName="SW-CX_SBQ.CX_SBQ_363XC1"/>
     <cge:Meas_Ref ObjectId="308030"/>
    <cge:TPSR_Ref TObjectID="47883"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308030">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 621.921704 383.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47882" ObjectName="SW-CX_SBQ.CX_SBQ_363XC"/>
     <cge:Meas_Ref ObjectId="308030"/>
    <cge:TPSR_Ref TObjectID="47882"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308031">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 712.795122 536.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47884" ObjectName="SW-CX_SBQ.CX_SBQ_36367SW"/>
     <cge:Meas_Ref ObjectId="308031"/>
    <cge:TPSR_Ref TObjectID="47884"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.807070 446.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47879" ObjectName="SW-CX_SBQ.CX_SBQ_362XC1"/>
     <cge:Meas_Ref ObjectId="308025"/>
    <cge:TPSR_Ref TObjectID="47879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308025">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 472.807070 380.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47878" ObjectName="SW-CX_SBQ.CX_SBQ_362XC"/>
     <cge:Meas_Ref ObjectId="308025"/>
    <cge:TPSR_Ref TObjectID="47878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308026">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 563.680488 533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47880" ObjectName="SW-CX_SBQ.CX_SBQ_36267SW"/>
     <cge:Meas_Ref ObjectId="308026"/>
    <cge:TPSR_Ref TObjectID="47880"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308020">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 329.205444 447.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47875" ObjectName="SW-CX_SBQ.CX_SBQ_361XC1"/>
     <cge:Meas_Ref ObjectId="308020"/>
    <cge:TPSR_Ref TObjectID="47875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308020">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 329.205444 381.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47874" ObjectName="SW-CX_SBQ.CX_SBQ_361XC"/>
     <cge:Meas_Ref ObjectId="308020"/>
    <cge:TPSR_Ref TObjectID="47874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308021">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 420.078862 534.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47876" ObjectName="SW-CX_SBQ.CX_SBQ_36167SW"/>
     <cge:Meas_Ref ObjectId="308021"/>
    <cge:TPSR_Ref TObjectID="47876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.867233 446.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47911" ObjectName="SW-CX_SBQ.CX_SBQ_365XC1"/>
     <cge:Meas_Ref ObjectId="308065"/>
    <cge:TPSR_Ref TObjectID="47911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308065">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 175.867233 380.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47910" ObjectName="SW-CX_SBQ.CX_SBQ_365XC"/>
     <cge:Meas_Ref ObjectId="308065"/>
    <cge:TPSR_Ref TObjectID="47910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308067">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 267.740650 533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47913" ObjectName="SW-CX_SBQ.CX_SBQ_36560SW"/>
     <cge:Meas_Ref ObjectId="308067"/>
    <cge:TPSR_Ref TObjectID="47913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.042029 448.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.042029 382.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 119.915447 535.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -140.317073 535.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308066">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 191.740650 705.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47912" ObjectName="SW-CX_SBQ.CX_SBQ_3656SW"/>
     <cge:Meas_Ref ObjectId="308066"/>
    <cge:TPSR_Ref TObjectID="47912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308017">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.862355 306.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47869" ObjectName="SW-CX_SBQ.CX_SBQ_3901XC"/>
     <cge:Meas_Ref ObjectId="308017"/>
    <cge:TPSR_Ref TObjectID="47869"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308017">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 367.862355 210.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47870" ObjectName="SW-CX_SBQ.CX_SBQ_3901XC1"/>
     <cge:Meas_Ref ObjectId="308017"/>
    <cge:TPSR_Ref TObjectID="47870"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308009">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.607375 253.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47864" ObjectName="SW-CX_SBQ.CX_SBQ_311XC"/>
     <cge:Meas_Ref ObjectId="308009"/>
    <cge:TPSR_Ref TObjectID="47864"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308009">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.607375 187.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47865" ObjectName="SW-CX_SBQ.CX_SBQ_311XC1"/>
     <cge:Meas_Ref ObjectId="308009"/>
    <cge:TPSR_Ref TObjectID="47865"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308040">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1124.437855 446.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47891" ObjectName="SW-CX_SBQ.CX_SBQ_381XC1"/>
     <cge:Meas_Ref ObjectId="308040"/>
    <cge:TPSR_Ref TObjectID="47891"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308040">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1124.437855 380.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47890" ObjectName="SW-CX_SBQ.CX_SBQ_381XC"/>
     <cge:Meas_Ref ObjectId="308040"/>
    <cge:TPSR_Ref TObjectID="47890"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308041">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1215.311273 533.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47892" ObjectName="SW-CX_SBQ.CX_SBQ_38167SW"/>
     <cge:Meas_Ref ObjectId="308041"/>
    <cge:TPSR_Ref TObjectID="47892"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308011">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.607375 255.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47867" ObjectName="SW-CX_SBQ.CX_SBQ_312XC"/>
     <cge:Meas_Ref ObjectId="308011"/>
    <cge:TPSR_Ref TObjectID="47867"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308011">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1263.607375 189.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47868" ObjectName="SW-CX_SBQ.CX_SBQ_312XC1"/>
     <cge:Meas_Ref ObjectId="308011"/>
    <cge:TPSR_Ref TObjectID="47868"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.343310 445.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47895" ObjectName="SW-CX_SBQ.CX_SBQ_382XC1"/>
     <cge:Meas_Ref ObjectId="308045"/>
    <cge:TPSR_Ref TObjectID="47895"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308045">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1308.343310 379.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47894" ObjectName="SW-CX_SBQ.CX_SBQ_382XC"/>
     <cge:Meas_Ref ObjectId="308045"/>
    <cge:TPSR_Ref TObjectID="47894"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308046">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1399.216727 532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47896" ObjectName="SW-CX_SBQ.CX_SBQ_38267SW"/>
     <cge:Meas_Ref ObjectId="308046"/>
    <cge:TPSR_Ref TObjectID="47896"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.065491 445.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47899" ObjectName="SW-CX_SBQ.CX_SBQ_383XC1"/>
     <cge:Meas_Ref ObjectId="308050"/>
    <cge:TPSR_Ref TObjectID="47899"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308050">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1479.065491 379.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47898" ObjectName="SW-CX_SBQ.CX_SBQ_383XC"/>
     <cge:Meas_Ref ObjectId="308050"/>
    <cge:TPSR_Ref TObjectID="47898"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308051">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1569.938909 532.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47900" ObjectName="SW-CX_SBQ.CX_SBQ_38367SW"/>
     <cge:Meas_Ref ObjectId="308051"/>
    <cge:TPSR_Ref TObjectID="47900"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.705491 443.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47903" ObjectName="SW-CX_SBQ.CX_SBQ_384XC1"/>
     <cge:Meas_Ref ObjectId="308055"/>
    <cge:TPSR_Ref TObjectID="47903"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308055">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1623.705491 377.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47902" ObjectName="SW-CX_SBQ.CX_SBQ_384XC"/>
     <cge:Meas_Ref ObjectId="308055"/>
    <cge:TPSR_Ref TObjectID="47902"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308056">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1714.578909 530.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47904" ObjectName="SW-CX_SBQ.CX_SBQ_38467SW"/>
     <cge:Meas_Ref ObjectId="308056"/>
    <cge:TPSR_Ref TObjectID="47904"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1788.097491 444.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47918" ObjectName="SW-CX_SBQ.CX_SBQ_385XC1"/>
     <cge:Meas_Ref ObjectId="308073"/>
    <cge:TPSR_Ref TObjectID="47918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308073">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1788.097491 378.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47917" ObjectName="SW-CX_SBQ.CX_SBQ_385XC"/>
     <cge:Meas_Ref ObjectId="308073"/>
    <cge:TPSR_Ref TObjectID="47917"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308075">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1879.970909 531.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47920" ObjectName="SW-CX_SBQ.CX_SBQ_38560SW"/>
     <cge:Meas_Ref ObjectId="308075"/>
    <cge:TPSR_Ref TObjectID="47920"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308074">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1803.970909 694.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47919" ObjectName="SW-CX_SBQ.CX_SBQ_3856SW"/>
     <cge:Meas_Ref ObjectId="308074"/>
    <cge:TPSR_Ref TObjectID="47919"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.532400 447.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2146.532400 381.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2237.405818 534.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1969.718582 447.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1969.718582 381.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2060.592000 534.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.354946 305.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47871" ObjectName="SW-CX_SBQ.CX_SBQ_3902XC"/>
     <cge:Meas_Ref ObjectId="308018"/>
    <cge:TPSR_Ref TObjectID="47871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308018">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1750.354946 209.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47872" ObjectName="SW-CX_SBQ.CX_SBQ_3902XC1"/>
     <cge:Meas_Ref ObjectId="308018"/>
    <cge:TPSR_Ref TObjectID="47872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.742135 86.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47906" ObjectName="SW-CX_SBQ.CX_SBQ_313XC"/>
     <cge:Meas_Ref ObjectId="308060"/>
    <cge:TPSR_Ref TObjectID="47906"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308060">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 932.742135 166.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47907" ObjectName="SW-CX_SBQ.CX_SBQ_313XC1"/>
     <cge:Meas_Ref ObjectId="308060"/>
    <cge:TPSR_Ref TObjectID="47907"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307998">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.599278 -528.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47853" ObjectName="SW-CX_SBQ.CX_SBQ_29010SW"/>
     <cge:Meas_Ref ObjectId="307998"/>
    <cge:TPSR_Ref TObjectID="47853"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307997">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 355.599278 -456.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47852" ObjectName="SW-CX_SBQ.CX_SBQ_2901SW"/>
     <cge:Meas_Ref ObjectId="307997"/>
    <cge:TPSR_Ref TObjectID="47852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307999">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 305.599278 -411.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47854" ObjectName="SW-CX_SBQ.CX_SBQ_29017SW"/>
     <cge:Meas_Ref ObjectId="307999"/>
    <cge:TPSR_Ref TObjectID="47854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308001">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.742135 -536.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47856" ObjectName="SW-CX_SBQ.CX_SBQ_2011SW"/>
     <cge:Meas_Ref ObjectId="308001"/>
    <cge:TPSR_Ref TObjectID="47856"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308002">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 933.742135 -370.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47857" ObjectName="SW-CX_SBQ.CX_SBQ_2016SW"/>
     <cge:Meas_Ref ObjectId="308002"/>
    <cge:TPSR_Ref TObjectID="47857"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308003">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.742135 -508.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47858" ObjectName="SW-CX_SBQ.CX_SBQ_20117SW"/>
     <cge:Meas_Ref ObjectId="308003"/>
    <cge:TPSR_Ref TObjectID="47858"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308004">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 883.742135 -429.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47859" ObjectName="SW-CX_SBQ.CX_SBQ_20160SW"/>
     <cge:Meas_Ref ObjectId="308004"/>
    <cge:TPSR_Ref TObjectID="47859"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308005">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 884.742135 -345.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47860" ObjectName="SW-CX_SBQ.CX_SBQ_20167SW"/>
     <cge:Meas_Ref ObjectId="308005"/>
    <cge:TPSR_Ref TObjectID="47860"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308006">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 0.000000 0.000000 1.000000 1122.000000 -278.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47861" ObjectName="SW-CX_SBQ.CX_SBQ_2010SW"/>
     <cge:Meas_Ref ObjectId="308006"/>
    <cge:TPSR_Ref TObjectID="47861"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308007">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 996.000000 -166.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47862" ObjectName="SW-CX_SBQ.CX_SBQ_3010SW"/>
     <cge:Meas_Ref ObjectId="308007"/>
    <cge:TPSR_Ref TObjectID="47862"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.126582 -640.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1537.126582 -706.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1870.126582 -615.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1870.126582 -681.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1984.126582 -638.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1984.126582 -704.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307989">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 618.571944 -864.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47851" ObjectName="SW-CX_SBQ.CX_SBQ_27167SW"/>
     <cge:Meas_Ref ObjectId="307989"/>
    <cge:TPSR_Ref TObjectID="47851"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307988">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 617.571944 -780.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47850" ObjectName="SW-CX_SBQ.CX_SBQ_27160SW"/>
     <cge:Meas_Ref ObjectId="307988"/>
    <cge:TPSR_Ref TObjectID="47850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307987">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 618.571944 -701.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47849" ObjectName="SW-CX_SBQ.CX_SBQ_27117SW"/>
     <cge:Meas_Ref ObjectId="307987"/>
    <cge:TPSR_Ref TObjectID="47849"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308081">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 1106.571944 -699.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47924" ObjectName="SW-CX_SBQ.CX_SBQ_27217SW"/>
     <cge:Meas_Ref ObjectId="308081"/>
    <cge:TPSR_Ref TObjectID="47924"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -93.190491 450.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -93.190491 384.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 -2.317073 537.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.718582 453.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2339.718582 387.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 2430.592000 540.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.126582 -641.000000)" xlink:href="#switch2:shape3_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1629.126582 -707.000000)" xlink:href="#switch2:shape2_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308068">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 200.000000 722.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47914" ObjectName="SW-CX_SBQ.CX_SBQ_36567SW"/>
     <cge:Meas_Ref ObjectId="308068"/>
    <cge:TPSR_Ref TObjectID="47914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308076">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1812.000000 711.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47921" ObjectName="SW-CX_SBQ.CX_SBQ_38567SW"/>
     <cge:Meas_Ref ObjectId="308076"/>
    <cge:TPSR_Ref TObjectID="47921"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-308080">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1111.000000 -635.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47923" ObjectName="SW-CX_SBQ.CX_SBQ_2721SW"/>
     <cge:Meas_Ref ObjectId="308080"/>
    <cge:TPSR_Ref TObjectID="47923"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307985">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -637.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47847" ObjectName="SW-CX_SBQ.CX_SBQ_2711SW"/>
     <cge:Meas_Ref ObjectId="307985"/>
    <cge:TPSR_Ref TObjectID="47847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-307986">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 623.000000 -803.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="47848" ObjectName="SW-CX_SBQ.CX_SBQ_2716SW"/>
     <cge:Meas_Ref ObjectId="307986"/>
    <cge:TPSR_Ref TObjectID="47848"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_26f27c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -248.000000 697.000000) translate(0,17)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_29b6eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -173.000000 293.000000) translate(0,16)">35kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2667010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -344.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_275cac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -701.000000 -782.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2ecd8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -579.000000 -923.500000) translate(0,16)">石板箐光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27a1260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 913.000000 424.000000) translate(0,12)">1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_247f590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 751.000000 686.000000) translate(0,17)">石板箐Ⅳ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e9b480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 605.000000 683.000000) translate(0,17)">石板箐Ⅲ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_26443f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 446.000000 680.000000) translate(0,17)">石板箐Ⅱ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2e502d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 296.000000 680.000000) translate(0,17)">石板箐Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_297d490" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -134.000000 702.000000) translate(0,17)">电网预留柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29f0510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 101.000000 965.000000) translate(0,17)">1号无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ec4740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 279.000000 62.000000) translate(0,17)">35kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ea31d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1106.000000 686.000000) translate(0,17)">石板箐Ⅴ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d1d530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1269.000000 684.000000) translate(0,17)">石板箐Ⅵ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d546f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1452.000000 686.000000) translate(0,17)">石板箐Ⅶ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29b04e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1604.000000 681.000000) translate(0,17)">石板箐Ⅶ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3386380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1728.000000 951.000000) translate(0,17)">2号无功补偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2ec1b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1955.000000 692.000000) translate(0,17)">储能预留柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_298b8c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2115.000000 695.000000) translate(0,17)">电网预留柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2d41510" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1700.000000 67.000000) translate(0,17)">35kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ecfa30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 161.000000 -601.000000) translate(0,15)">220kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29aa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -249.000000) translate(0,12)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29aa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -249.000000) translate(0,27)">SFZB18-200000/220GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29aa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -249.000000) translate(0,42)">230±8×1.25%/37kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29aa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -249.000000) translate(0,57)">YN，yn0+d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29aa000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 -249.000000) translate(0,72)">Ud%=14</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2977d30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1555.000000 -423.000000) translate(0,12)">0.4kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29c7680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1506.000000 -697.000000) translate(0,12)">401</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ead550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1422.000000 -950.000000) translate(0,12)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2eae380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1914.000000 -414.000000) translate(0,12)">0.4kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e7dbb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1839.000000 -672.000000) translate(0,12)">403</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37881b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1953.000000 -695.000000) translate(0,12)">404</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37887e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2034.000000 -933.000000) translate(0,12)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37896a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 955.742135 -480.000000) translate(0,12)">201</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3789b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 823.742135 -229.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e9d240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1108.000000 -83.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_3786510" transform="matrix(1.000000 0.000000 -0.000000 1.000000 571.000000 -1082.000000) translate(0,16)">220kV石启线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3353fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1043.571944 -683.000000) translate(0,12)">27217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3395d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -2.000000 702.000000) translate(0,17)">储能预留柜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33ab5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2335.000000 695.000000) translate(0,17)">备用</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33b59c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1598.000000 -698.000000) translate(0,12)">402</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" graphid="g_33bad60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1568.000000 -550.000000) translate(0,21)">ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="26" graphid="g_33bbc40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1912.000000 -547.000000) translate(0,21)">ATS</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d4c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -895.000000) translate(0,12)">27167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d5660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 575.000000 -811.000000) translate(0,12)">27160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d5b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 592.000000 -754.000000) translate(0,12)">271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d5fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 576.000000 -732.000000) translate(0,12)">27117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d72e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 372.000000 -486.000000) translate(0,12)">2901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -442.000000) translate(0,12)">29017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 309.000000 -559.000000) translate(0,12)">29010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d79a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -566.000000) translate(0,12)">2011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7be0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -539.000000) translate(0,12)">20117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d7e20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 887.000000 -460.000000) translate(0,12)">20160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d8060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 950.000000 -400.000000) translate(0,12)">2016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d82a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 888.000000 -376.000000) translate(0,12)">20167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d8a70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1124.000000 -262.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d8cb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 902.000000 -259.000000) translate(0,12)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d9310" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1003.000000 -197.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d9590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 952.000000 103.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33d97d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1026.000000 245.000000) translate(0,12)">31367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33da3d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 658.000000 196.000000) translate(0,12)">311</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33da610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 780.000000 392.000000) translate(0,12)">364</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33da850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 853.000000 505.000000) translate(0,12)">36467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33daa90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 641.000000 392.000000) translate(0,12)">363</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dacd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 715.000000 505.000000) translate(0,12)">36367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33daf10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 492.000000 389.000000) translate(0,12)">362</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 566.000000 502.000000) translate(0,12)">36267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db390" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 349.000000 390.000000) translate(0,12)">361</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 422.000000 503.000000) translate(0,12)">36167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33db810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 389.000000) translate(0,12)">365</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dba50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 270.000000 502.000000) translate(0,12)">36560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33dbc90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 194.000000 674.000000) translate(0,12)">3656</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33deaa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 195.000000 799.000000) translate(0,12)">360</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e0cc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1144.000000 389.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e0f00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1217.000000 502.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1328.000000 388.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1401.000000 501.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e15c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1498.000000 388.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1572.000000 501.000000) translate(0,12)">38367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1a40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1643.000000 386.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1c80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1717.000000 499.000000) translate(0,12)">38467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e1ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1807.000000 387.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1882.000000 500.000000) translate(0,12)">38560</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e2340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1806.000000 663.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1806.000000 782.000000) translate(0,12)">380</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_33e5750" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2402.000000 296.000000) translate(0,15)">35kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5cf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1091.000000 300.000000) translate(0,12)">IIM段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e5f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1712.000000 224.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e61b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1283.000000 198.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_33e63f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 337.000000 225.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="20" graphid="g_33edd80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -582.000000 13.000000) translate(0,16)">18314451772</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30dd230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 206.000000 726.000000) translate(0,12)">36567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_30e1210" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1823.000000 716.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3149eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1127.000000 -665.000000) translate(0,12)">2721</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3465970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -667.000000) translate(0,12)">2711</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3467500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 639.000000 -833.000000) translate(0,12)">2716</text>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_29b2db0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1012.742135 279.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b3130" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 840.173171 539.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29bc570" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 701.795122 539.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_273d450" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 552.680488 536.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_378dd40" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 409.078862 537.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f9c70" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 256.740650 536.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2edb560" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 108.915447 538.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2983f00" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -151.317073 538.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e63040" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1204.311273 536.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e974f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1388.216727 535.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d22180" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1558.938909 535.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d360b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1703.578909 533.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ec9920" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 1868.970909 534.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29fc2d0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2226.405818 537.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ea0f00" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2049.592000 537.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_288a190" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 291.599278 -539.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29b9c10" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 291.599278 -422.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29724b0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 870.742135 -519.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e6b370" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 869.742135 -440.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2d38ec0" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 870.742135 -356.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e9df0" refnum="0">
    <use class="BV-0KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 1094.500000 -296.500000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8cd50" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 558.571944 -700.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8e160" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 557.571944 -779.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e8f570" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 558.571944 -863.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3350b40" refnum="0">
    <use class="BV-0KV" transform="matrix(-1.000000 0.000000 -0.000000 -1.000000 1046.571944 -698.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33635f0" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 -13.317073 540.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33a7260" refnum="0">
    <use class="BV-0KV" transform="matrix(-0.000000 1.000000 1.000000 0.000000 2419.592000 543.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33ca3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1132.000000 -165.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_SBQ"/>
</svg>