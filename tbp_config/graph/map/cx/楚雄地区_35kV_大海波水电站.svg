<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-46" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3127 -1191 1780 1226">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape8_0">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
   </symbol>
   <symbol id="breaker2:shape8_1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="99" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor1">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="24" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="15" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="91" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="82" y2="91"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="91" y2="82"/>
   </symbol>
   <symbol id="breaker2:shape8-UnNor2">
    <rect height="27" stroke-width="0.833219" width="14" x="3" y="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="16" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="7" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="10" x2="10" y1="98" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="10" x2="1" y1="99" y2="90"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.8" x1="19" x2="10" y1="90" y2="99"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.890909" x1="29" x2="29" y1="6" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.583333" x1="26" x2="26" y1="4" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.742424" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.906433" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="14" y2="14"/>
   </symbol>
   <symbol id="hydroGenerator:shape2">
    <polyline points="12,27 11,28 12,28 12,29 12,30 13,31 13,32 14,33 15,33 15,34 16,34 17,35 18,35 19,35 20,35 21,35 22,34 23,34 24,33 25,33 25,32 26,31 26,30 27,29 27,28 27,28 27,27 " stroke-width="0.06"/>
    <circle cx="27" cy="27" fillStyle="0" r="26.5" stroke-width="0.55102"/>
    <polyline arcFlag="1" points="28,27 28,26 28,25 28,24 29,23 29,22 30,21 30,21 31,20 32,20 33,19 34,19 35,19 36,18 37,19 38,19 39,19 40,20 40,20 41,21 42,21 42,22 43,23 43,24 43,25 44,26 43,27 " stroke-width="0.06"/>
   </symbol>
   <symbol id="lightningRod:shape28">
    <ellipse cx="11" cy="12" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
    <ellipse cx="11" cy="25" fillStyle="0" rx="10.5" ry="11.5" stroke-width="0.64567"/>
   </symbol>
   <symbol id="lightningRod:shape47">
    <circle cx="14" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="20" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="13" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="7" cy="10" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape129">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="77" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="23" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="67" x2="87" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="77" x2="77" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="23" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="0" x2="20" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="10" x2="10" y1="5" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.3125" x1="44" x2="44" y1="23" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="34" x2="54" y1="23" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape164">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="60" x2="60" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="57" x2="57" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="57" x2="48" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="8" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="18" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="27" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-9" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="17" x2="0" y1="34" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="-8" x2="0" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.375" x1="18" x2="18" y1="24" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.469769" x1="19" x2="27" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.324864" x1="21" x2="1" y1="26" y2="26"/>
   </symbol>
   <symbol id="switch2:shape18_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="13" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="switch2:shape18_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="5" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor1">
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="14" y2="65"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
   </symbol>
   <symbol id="switch2:shape18-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="57" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="16" y2="66"/>
    <circle cx="10" cy="11" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="13" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="19" y1="5" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="1" x2="10" y1="22" y2="13"/>
    <circle cx="10" cy="69" fillStyle="1" r="3" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="66" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="19" x2="10" y1="65" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.9" x1="10" x2="1" y1="74" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape0_0">
    <circle cx="25" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="17" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="26" x2="34" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="17" x2="34" y1="18" y2="18"/>
   </symbol>
   <symbol id="transformer2:shape0_1">
    <ellipse cx="25" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="24" y1="58" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="24" x2="32" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="16" x2="24" y1="74" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape2_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape2_1">
    <circle cx="13" cy="18" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="18" y1="12" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="9" y1="20" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="18" y1="20" y2="12"/>
   </symbol>
   <symbol id="transformer2:shape29_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="15" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape29_1">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="17" y1="36" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape30_0">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="8,21 14,10 19,21 " stroke-width="1"/>
   </symbol>
   <symbol id="transformer2:shape30_1">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="7,43 13,32 18,43 " stroke-width="1"/>
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape31_0">
    <circle cx="13" cy="36" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="60" x2="60" y1="15" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="57" x2="57" y1="9" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="64" x2="64" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="56" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape31_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="12"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="voltageTransformer:shape138">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="43" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="35" x2="41" y1="43" y2="43"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="39" y1="47" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="40" y1="45" y2="45"/>
    <ellipse cx="8" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <ellipse cx="8" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="16" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="14" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="9" y1="12" y2="10"/>
    <ellipse cx="21" cy="13" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="11" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="8" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="27" y2="24"/>
    <ellipse cx="21" cy="25" rx="8" ry="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="42" y1="21" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="42" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="21" y1="14" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="38" y1="14" y2="20"/>
    <rect height="13" stroke-width="1" width="5" x="35" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="16" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="14" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="24" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="18" x2="21" y1="29" y2="27"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="21" y1="27" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="4" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="3" x2="9" y1="10" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.747557" x1="6" x2="3" y1="14" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.750018" x1="6" x2="9" y1="14" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="18" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="16" x2="16" y1="15" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="17" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="27" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.72647" x1="25" x2="25" y1="11" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="22" x2="24" y1="13" y2="11"/>
    <circle cx="7" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <ellipse cx="15" cy="6" fillStyle="0" rx="6.5" ry="6" stroke-width="0.431185"/>
    <circle cx="24" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="15" cy="14" fillStyle="0" r="6" stroke-width="0.431185"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726441" x1="13" x2="15" y1="6" y2="4"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d735c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d746c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d75070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d75d40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d76fa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d77bc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d78620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2d790a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2737f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2737f70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d7c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d7c380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d7e280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d7e280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2d7f290" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d80f20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d81b70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d82a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d83330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d84d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d85780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d86040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d86800" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d878e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d88260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d88d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d89710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d8abf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d8b710" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2d8c760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d8d3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2d9b6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2d9c190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d8fbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2d911d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1236" width="1790" x="3122" y="-1196"/>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3128" y="-590"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3128" y="-1070"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3128" y="-1190"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-41385">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -391.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6873" ObjectName="SW-CX_DHB.CX_DHB_6011SW"/>
     <cge:Meas_Ref ObjectId="41385"/>
    <cge:TPSR_Ref TObjectID="6873"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41384">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -515.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6872" ObjectName="SW-CX_DHB.CX_DHB_6016SW"/>
     <cge:Meas_Ref ObjectId="41384"/>
    <cge:TPSR_Ref TObjectID="6872"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3968.000000 -991.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6830" ObjectName="SW-CX_DHB.CX_DHB_35367SW"/>
     <cge:Meas_Ref ObjectId="41323"/>
    <cge:TPSR_Ref TObjectID="6830"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41327">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4337.000000 -991.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6834" ObjectName="SW-CX_DHB.CX_DHB_35267SW"/>
     <cge:Meas_Ref ObjectId="41327"/>
    <cge:TPSR_Ref TObjectID="6834"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4691.000000 -991.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6838" ObjectName="SW-CX_DHB.CX_DHB_35167SW"/>
     <cge:Meas_Ref ObjectId="41331"/>
    <cge:TPSR_Ref TObjectID="6838"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41339">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4340.000000 -711.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6846" ObjectName="SW-CX_DHB.CX_DHB_39017SW"/>
     <cge:Meas_Ref ObjectId="41339"/>
    <cge:TPSR_Ref TObjectID="6846"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41337">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -392.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6844" ObjectName="SW-CX_DHB.CX_DHB_0021SW"/>
     <cge:Meas_Ref ObjectId="41337"/>
    <cge:TPSR_Ref TObjectID="6844"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -33.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11357" ObjectName="SW-CX_DHB.CX_DHB_G012SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11357"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11358" ObjectName="SW-CX_DHB.CX_DHB_F0536SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11358"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -843.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6828" ObjectName="SW-CX_DHB.CX_DHB_3531SW"/>
     <cge:Meas_Ref ObjectId="41321"/>
    <cge:TPSR_Ref TObjectID="6828"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -950.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6829" ObjectName="SW-CX_DHB.CX_DHB_3536SW"/>
     <cge:Meas_Ref ObjectId="41322"/>
    <cge:TPSR_Ref TObjectID="6829"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -845.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6832" ObjectName="SW-CX_DHB.CX_DHB_3521SW"/>
     <cge:Meas_Ref ObjectId="41325"/>
    <cge:TPSR_Ref TObjectID="6832"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -952.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6833" ObjectName="SW-CX_DHB.CX_DHB_3526SW"/>
     <cge:Meas_Ref ObjectId="41326"/>
    <cge:TPSR_Ref TObjectID="6833"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41330">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -944.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6837" ObjectName="SW-CX_DHB.CX_DHB_3516SW"/>
     <cge:Meas_Ref ObjectId="41330"/>
    <cge:TPSR_Ref TObjectID="6837"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -837.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6836" ObjectName="SW-CX_DHB.CX_DHB_3511SW"/>
     <cge:Meas_Ref ObjectId="41329"/>
    <cge:TPSR_Ref TObjectID="6836"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41333">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -753.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6840" ObjectName="SW-CX_DHB.CX_DHB_3011SW"/>
     <cge:Meas_Ref ObjectId="41333"/>
    <cge:TPSR_Ref TObjectID="6840"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41338">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -757.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6845" ObjectName="SW-CX_DHB.CX_DHB_3901SW"/>
     <cge:Meas_Ref ObjectId="41338"/>
    <cge:TPSR_Ref TObjectID="6845"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41335">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -750.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6842" ObjectName="SW-CX_DHB.CX_DHB_3021SW"/>
     <cge:Meas_Ref ObjectId="41335"/>
    <cge:TPSR_Ref TObjectID="6842"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41387">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -325.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6875" ObjectName="SW-CX_DHB.CX_DHB_6511SW"/>
     <cge:Meas_Ref ObjectId="41387"/>
    <cge:TPSR_Ref TObjectID="6875"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41390">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -311.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6878" ObjectName="SW-CX_DHB.CX_DHB_6901SW"/>
     <cge:Meas_Ref ObjectId="41390"/>
    <cge:TPSR_Ref TObjectID="6878"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41391">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4075.000000 -315.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6879" ObjectName="SW-CX_DHB.CX_DHB_6801SW"/>
     <cge:Meas_Ref ObjectId="41391"/>
    <cge:TPSR_Ref TObjectID="6879"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41389">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3712.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6877" ObjectName="SW-CX_DHB.CX_DHB_6501SW"/>
     <cge:Meas_Ref ObjectId="41389"/>
    <cge:TPSR_Ref TObjectID="6877"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41388">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3874.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6876" ObjectName="SW-CX_DHB.CX_DHB_6502SW"/>
     <cge:Meas_Ref ObjectId="41388"/>
    <cge:TPSR_Ref TObjectID="6876"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41340">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4280.000000 -391.000000)" xlink:href="#switch2:shape18_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6847" ObjectName="SW-CX_DHB.CX_DHB_0901SW"/>
     <cge:Meas_Ref ObjectId="41340"/>
    <cge:TPSR_Ref TObjectID="6847"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -144.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37448" ObjectName="SW-CX_DHB.CX_DHB_0546SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="37448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4285.000000 -139.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37449" ObjectName="SW-CX_DHB.CX_DHB_05467SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="37449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 -108.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11359" ObjectName="SW-CX_DHB.CX_DHB_F0526SW"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11359"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_DHB.CX_DHB_6M">
    <g class="BV-3KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3749,-383 4112,-383 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11312" ObjectName="BS-CX_DHB.CX_DHB_6M"/>
    <cge:TPSR_Ref TObjectID="11312"/></metadata>
   <polyline fill="none" opacity="0" points="3749,-383 4112,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DHB.CX_DHB_3M">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3753,-826 4844,-826 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="11311" ObjectName="BS-CX_DHB.CX_DHB_3M"/>
    <cge:TPSR_Ref TObjectID="11311"/></metadata>
   <polyline fill="none" opacity="0" points="3753,-826 4844,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_DHB.CX_DHB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4245,-383 4880,-383 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="6826" ObjectName="BS-CX_DHB.CX_DHB_9IM"/>
    <cge:TPSR_Ref TObjectID="6826"/></metadata>
   <polyline fill="none" opacity="0" points="4245,-383 4880,-383 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-CX_DHB.054Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 15.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="38198" ObjectName="EC-CX_DHB.054Ld"/>
    <cge:TPSR_Ref TObjectID="38198"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_DHB.052Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4488.000000 -52.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46630" ObjectName="EC-CX_DHB.052Ld"/>
    <cge:TPSR_Ref TObjectID="46630"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4639.000000 -50.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25c7630" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4029.000000 -1008.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25d1960" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4398.000000 -1008.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_256bf50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4752.000000 -1008.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_253b2b0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4401.000000 -728.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f6ca0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3978.000000 -174.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249c130" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4288.000000 -183.000000)" xlink:href="#earth:shape1"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_25d2e70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3867.000000 -965.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2532b30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4236.000000 -965.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2525e90">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3942.000000 -235.000000)" xlink:href="#lightningRod:shape47"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2540340">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3947.000000 -258.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2540dc0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3718.000000 -135.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2526c40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4492.000000 -194.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2526fe0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -194.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25275a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4299.000000 -682.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2528180">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4244.000000 -630.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25a7a80">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4239.000000 -1007.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2530350">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -509.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2544990">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3870.000000 -1007.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2546850">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4590.000000 -965.000000)" xlink:href="#lightningRod:shape28"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2546f50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4593.000000 -1007.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2516190">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4012.000000 -226.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_25170e0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4111.000000 -249.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2517960">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -254.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_23e4ea0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4076.000000 -201.000000)" xlink:href="#lightningRod:shape129"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f76f0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3984.000000 -482.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f8460">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 -484.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f91d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4840.000000 -160.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24f9f40">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4365.000000 -259.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24facb0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -81.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24fba20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4505.000000 -80.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ab8b0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3849.000000 -258.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ac620">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3787.000000 32.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ad390">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3989.000000 -1042.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ae100">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4358.000000 -1042.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24aee70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 -1042.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24b2150">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4807.000000 -110.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c09f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4240.000000 -473.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24c1e10">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4281.000000 -489.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24ca5a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4329.000000 -194.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_249ce20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4342.000000 -120.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a8170">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4528.000000 -258.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24a99d0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4643.000000 -194.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d23a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4679.000000 -257.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_24d9040">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4843.000000 -258.000000)" xlink:href="#lightningRod:shape164"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3245.000000 -1111.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41313" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -819.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41313" ObjectName="CX_DHB:CX_DHB_1T_Ua"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41314" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -804.500000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41314" ObjectName="CX_DHB:CX_DHB_1T_Ub"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41315" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -790.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41315" ObjectName="CX_DHB:CX_DHB_1T_Uc"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-41316" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3806.000000 -776.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41316" ObjectName="CX_DHB:CX_DHB_1T_Uab"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-78591" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 3262.538462 -1008.966362) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78591" ObjectName="CX_DHB:CX_DHB_sumP"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41279" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -941.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41279" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6827"/>
     <cge:Term_Ref ObjectID="9915"/>
    <cge:TPSR_Ref TObjectID="6827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41280" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -941.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41280" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6827"/>
     <cge:Term_Ref ObjectID="9915"/>
    <cge:TPSR_Ref TObjectID="6827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41278" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -941.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41278" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6827"/>
     <cge:Term_Ref ObjectID="9915"/>
    <cge:TPSR_Ref TObjectID="6827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41281" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4055.000000 -941.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41281" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6827"/>
     <cge:Term_Ref ObjectID="9915"/>
    <cge:TPSR_Ref TObjectID="6827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41283" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -943.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41283" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6831"/>
     <cge:Term_Ref ObjectID="9923"/>
    <cge:TPSR_Ref TObjectID="6831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41284" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -943.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41284" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6831"/>
     <cge:Term_Ref ObjectID="9923"/>
    <cge:TPSR_Ref TObjectID="6831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41282" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -943.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41282" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6831"/>
     <cge:Term_Ref ObjectID="9923"/>
    <cge:TPSR_Ref TObjectID="6831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41285" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4425.000000 -943.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41285" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6831"/>
     <cge:Term_Ref ObjectID="9923"/>
    <cge:TPSR_Ref TObjectID="6831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-58358" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -944.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58358" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6835"/>
     <cge:Term_Ref ObjectID="9931"/>
    <cge:TPSR_Ref TObjectID="6835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-58359" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -944.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="58359" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6835"/>
     <cge:Term_Ref ObjectID="9931"/>
    <cge:TPSR_Ref TObjectID="6835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41286" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -944.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41286" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6835"/>
     <cge:Term_Ref ObjectID="9931"/>
    <cge:TPSR_Ref TObjectID="6835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41287" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -944.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41287" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6835"/>
     <cge:Term_Ref ObjectID="9931"/>
    <cge:TPSR_Ref TObjectID="6835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41289" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -742.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41289" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6839"/>
     <cge:Term_Ref ObjectID="9939"/>
    <cge:TPSR_Ref TObjectID="6839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41290" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -742.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41290" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6839"/>
     <cge:Term_Ref ObjectID="9939"/>
    <cge:TPSR_Ref TObjectID="6839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-41288" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -742.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41288" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6839"/>
     <cge:Term_Ref ObjectID="9939"/>
    <cge:TPSR_Ref TObjectID="6839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41291" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4033.000000 -742.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41291" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6839"/>
     <cge:Term_Ref ObjectID="9939"/>
    <cge:TPSR_Ref TObjectID="6839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41293" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -750.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41293" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6841"/>
     <cge:Term_Ref ObjectID="9949"/>
    <cge:TPSR_Ref TObjectID="6841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41294" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -750.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41294" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6841"/>
     <cge:Term_Ref ObjectID="9949"/>
    <cge:TPSR_Ref TObjectID="6841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="I" PreSymbol="0" appendix="" decimal="2" id="ME-41292" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -750.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41292" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6841"/>
     <cge:Term_Ref ObjectID="9949"/>
    <cge:TPSR_Ref TObjectID="6841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41295" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4769.000000 -750.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41295" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6841"/>
     <cge:Term_Ref ObjectID="9949"/>
    <cge:TPSR_Ref TObjectID="6841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41309" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -96.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41309" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6854"/>
     <cge:Term_Ref ObjectID="9995"/>
    <cge:TPSR_Ref TObjectID="6854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41310" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -96.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41310" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6854"/>
     <cge:Term_Ref ObjectID="9995"/>
    <cge:TPSR_Ref TObjectID="6854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41308" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -96.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41308" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6854"/>
     <cge:Term_Ref ObjectID="9995"/>
    <cge:TPSR_Ref TObjectID="6854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41311" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4203.000000 -96.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41311" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6854"/>
     <cge:Term_Ref ObjectID="9995"/>
    <cge:TPSR_Ref TObjectID="6854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41297" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41297" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6848"/>
     <cge:Term_Ref ObjectID="9983"/>
    <cge:TPSR_Ref TObjectID="6848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41298" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41298" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6848"/>
     <cge:Term_Ref ObjectID="9983"/>
    <cge:TPSR_Ref TObjectID="6848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41296" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41296" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6848"/>
     <cge:Term_Ref ObjectID="9983"/>
    <cge:TPSR_Ref TObjectID="6848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41299" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4695.000000 -56.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41299" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6848"/>
     <cge:Term_Ref ObjectID="9983"/>
    <cge:TPSR_Ref TObjectID="6848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41301" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -56.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41301" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6850"/>
     <cge:Term_Ref ObjectID="9987"/>
    <cge:TPSR_Ref TObjectID="6850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41302" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -56.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41302" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6850"/>
     <cge:Term_Ref ObjectID="9987"/>
    <cge:TPSR_Ref TObjectID="6850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41300" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -56.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41300" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6850"/>
     <cge:Term_Ref ObjectID="9987"/>
    <cge:TPSR_Ref TObjectID="6850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41303" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4539.000000 -56.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41303" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6850"/>
     <cge:Term_Ref ObjectID="9987"/>
    <cge:TPSR_Ref TObjectID="6850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41305" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 -54.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41305" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6852"/>
     <cge:Term_Ref ObjectID="9991"/>
    <cge:TPSR_Ref TObjectID="6852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41306" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 -54.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41306" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6852"/>
     <cge:Term_Ref ObjectID="9991"/>
    <cge:TPSR_Ref TObjectID="6852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41304" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 -54.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41304" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6852"/>
     <cge:Term_Ref ObjectID="9991"/>
    <cge:TPSR_Ref TObjectID="6852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41307" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4857.000000 -54.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41307" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6852"/>
     <cge:Term_Ref ObjectID="9991"/>
    <cge:TPSR_Ref TObjectID="6852"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-41317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4852.000000 -442.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41317" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6826"/>
     <cge:Term_Ref ObjectID="9914"/>
    <cge:TPSR_Ref TObjectID="6826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-41318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4852.000000 -442.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41318" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6826"/>
     <cge:Term_Ref ObjectID="9914"/>
    <cge:TPSR_Ref TObjectID="6826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-41319" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4852.000000 -442.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41319" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6826"/>
     <cge:Term_Ref ObjectID="9914"/>
    <cge:TPSR_Ref TObjectID="6826"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-41379" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -341.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41379" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6874"/>
     <cge:Term_Ref ObjectID="9961"/>
    <cge:TPSR_Ref TObjectID="6874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-41380" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -341.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41380" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6874"/>
     <cge:Term_Ref ObjectID="9961"/>
    <cge:TPSR_Ref TObjectID="6874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-41378" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -341.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41378" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6874"/>
     <cge:Term_Ref ObjectID="9961"/>
    <cge:TPSR_Ref TObjectID="6874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-41381" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3897.000000 -341.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="41381" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="6874"/>
     <cge:Term_Ref ObjectID="9961"/>
    <cge:TPSR_Ref TObjectID="6874"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3257" y="-1170"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3257" y="-1170"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3208" y="-1187"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3208" y="-1187"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3419" y="-1154"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3419" y="-1154"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3419" y="-1189"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3419" y="-1189"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3257" y="-1170"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3208" y="-1187"/></g>
   <g href="cx_配调_配网接线图35kV电站.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3419" y="-1154"/></g>
   <g href="cx_索引_接线图_地调直调.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3419" y="-1189"/></g>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-41332">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -692.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6839" ObjectName="SW-CX_DHB.CX_DHB_301BK"/>
     <cge:Meas_Ref ObjectId="41332"/>
    <cge:TPSR_Ref TObjectID="6839"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41383">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3922.000000 -435.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6871" ObjectName="SW-CX_DHB.CX_DHB_601BK"/>
     <cge:Meas_Ref ObjectId="41383"/>
    <cge:TPSR_Ref TObjectID="6871"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3927.000000 -900.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6827" ObjectName="SW-CX_DHB.CX_DHB_353BK"/>
     <cge:Meas_Ref ObjectId="41320"/>
    <cge:TPSR_Ref TObjectID="6827"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -900.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6831" ObjectName="SW-CX_DHB.CX_DHB_352BK"/>
     <cge:Meas_Ref ObjectId="41324"/>
    <cge:TPSR_Ref TObjectID="6831"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4650.000000 -900.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6835" ObjectName="SW-CX_DHB.CX_DHB_351BK"/>
     <cge:Meas_Ref ObjectId="41328"/>
    <cge:TPSR_Ref TObjectID="6835"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41334">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -694.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6841" ObjectName="SW-CX_DHB.CX_DHB_302BK"/>
     <cge:Meas_Ref ObjectId="41334"/>
    <cge:TPSR_Ref TObjectID="6841"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41336">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 -437.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6843" ObjectName="SW-CX_DHB.CX_DHB_002BK"/>
     <cge:Meas_Ref ObjectId="41336"/>
    <cge:TPSR_Ref TObjectID="6843"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41386">
    <use class="BV-3KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3786.000000 -274.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6874" ObjectName="SW-CX_DHB.CX_DHB_651BK"/>
     <cge:Meas_Ref ObjectId="41386"/>
    <cge:TPSR_Ref TObjectID="6874"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4325.000000 -81.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="11356" ObjectName="SW-CX_DHB.CX_DHB_G01BK"/>
     <cge:Meas_Ref ObjectId="0"/>
    <cge:TPSR_Ref TObjectID="11356"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41347">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4324.000000 -271.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6854" ObjectName="SW-CX_DHB.CX_DHB_054BK"/>
     <cge:Meas_Ref ObjectId="41347"/>
    <cge:TPSR_Ref TObjectID="6854"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41343">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4487.000000 -271.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6850" ObjectName="SW-CX_DHB.CX_DHB_052BK"/>
     <cge:Meas_Ref ObjectId="41343"/>
    <cge:TPSR_Ref TObjectID="6850"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41341">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4638.000000 -269.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6848" ObjectName="SW-CX_DHB.CX_DHB_053BK"/>
     <cge:Meas_Ref ObjectId="41341"/>
    <cge:TPSR_Ref TObjectID="6848"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-41345">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4802.000000 -270.000000)" xlink:href="#breaker2:shape8_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="6852" ObjectName="SW-CX_DHB.CX_DHB_051BK"/>
     <cge:Meas_Ref ObjectId="41345"/>
    <cge:TPSR_Ref TObjectID="6852"/></metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="3936,-1080 3936,-1130 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="3936,-1080 3936,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="4305,-1080 4305,-1130 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4305,-1080 4305,-1130 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="" endPointId="0" endStationName="" flowDrawDirect="1" flowShape="0" id="AC-NULL" runFlow="0">
    <g class="BV-0KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" fill="none" points="4659,-1080 4659,-1130 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="0" ObjectName="AC-0"/>
    </metadata>
   <polyline fill="none" opacity="0" points="4659,-1080 4659,-1130 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="HydroGenerator_Layer">
   <g DF8003:Layer="PUBLIC" id="SM-0">
    <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3767.000000 -54.000000)" xlink:href="#hydroGenerator:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SM-0"/>
    </metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4290" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4334" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11312" cx="3931" cy="-383" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4655" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="3936" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="4305" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="4659" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="3931" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="4308" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11311" cx="4655" cy="-826" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11312" cx="3795" cy="-383" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11312" cx="3986" cy="-383" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="11312" cx="4084" cy="-383" fill="rgb(154,205,50)" r="4" stroke="rgb(154,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4496" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4647" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="6826" cx="4811" cy="-383" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_26084c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3755.000000 -36.000000) translate(0,12)">1号发电机</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c6290" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4786.000000 -42.000000) translate(0,12)">近区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_233b9c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3950.000000 -171.000000) translate(0,12)">6.3kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_22234d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4276.000000 -640.000000) translate(0,12)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c7ce0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3946.000000 -1132.000000) translate(0,12)">至妥安电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25d32a0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3915.000000 -1149.000000) translate(0,12)">安大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25c5df0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4315.000000 -1132.000000) translate(0,12)">至辛庄变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2533020" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4284.000000 -1149.000000) translate(0,12)">大辛线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2533ab0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4669.000000 -1132.000000) translate(0,12)">至马石铺变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_256c6c0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4638.000000 -1149.000000) translate(0,12)">马大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567390" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4834.000000 -97.000000) translate(0,12)">近区变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567760" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4475.000000 -42.000000) translate(0,12)">和平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567940" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4451.000000 -127.000000) translate(0,12)">1号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567ae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4624.000000 -42.000000) translate(0,12)">选治厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567e00" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4606.000000 -123.000000) translate(0,12)">1号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2567fa0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4302.000000 19.000000) translate(0,12)">者鸠线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2568140" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4282.000000 -87.000000) translate(0,12)">2号杆</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25682e0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4037.000000 -157.000000) translate(0,12)">1#站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569110" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3824.000000 -38.000000) translate(0,12)">至励磁变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3945.000000 -929.000000) translate(0,12)">353</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3957.000000 -1043.000000) translate(0,12)">35367</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2569b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -929.000000) translate(0,12)">352</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e0e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4326.000000 -1043.000000) translate(0,12)">35267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e10c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -929.000000) translate(0,12)">351</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e12a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4680.000000 -1043.000000) translate(0,12)">35167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -721.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -723.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -763.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3940.000000 -464.000000) translate(0,12)">601</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -420.000000) translate(0,12)">6011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e1fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -543.000000) translate(0,12)">6016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4664.000000 -466.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -422.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3804.000000 -303.000000) translate(0,12)">651</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2720" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4126.000000 -849.000000) translate(0,12)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24e2900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4492.000000 -404.000000) translate(0,12)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6130" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3943.000000 -980.000000) translate(0,12)">3536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6620" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3943.000000 -873.000000) translate(0,12)">3531</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3938.000000 -783.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6aa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3802.000000 -355.000000) translate(0,12)">6511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3944.000000 -350.000000) translate(0,12)">6901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a6f20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4092.000000 -347.000000) translate(0,12)">6801</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a7380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4662.000000 -780.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a7600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -867.000000) translate(0,12)">3511</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25a7840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4666.000000 -974.000000) translate(0,12)">3516</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25314d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -982.000000) translate(0,12)">3526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25319c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4312.000000 -875.000000) translate(0,12)">3521</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2531c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4315.000000 -787.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2531e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4683.000000 -644.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_25442b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -634.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,17)">危险点说明:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_250db50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3149.000000 -569.000000) translate(0,374)">联系方式:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,17)">频率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,59)">全站有功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,101)">全站无功:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_251d690" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3141.000000 -1048.000000) translate(0,143)">并网联络点的电压和交换功率:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2552e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3289.000000 -1159.500000) translate(0,16)">大海波站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25238e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3764.000000 -407.000000) translate(0,12)">6.3kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240c6f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3735.000000 -213.000000) translate(0,12)">6501</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240cbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3896.000000 -216.000000) translate(0,12)">6502</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -636.000000) translate(0,12)">S7-4000/38.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_240ce20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3799.000000 -636.000000) translate(0,27)">38.5±5%/6.3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25117d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -651.000000) translate(0,12)">S7-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25117d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4514.000000 -651.000000) translate(0,27)">38.5±5%/10.5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4283.000000 -112.000000) translate(0,12)">G01</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25122f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4281.000000 -62.000000) translate(0,12)">G012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4655.000000 -128.000000) translate(0,12)">F0536</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2512fa0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4504.000000 -132.000000) translate(0,12)">F0526</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_23e7ae0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4101.000000 -199.000000) translate(0,12)">电容器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_24b2ce0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3430.000000 -1146.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_250f7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3430.000000 -1181.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="9" graphid="g_24c17a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4314.000000 -585.000000) translate(0,8)">U</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c2950" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4302.000000 -438.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24c2f30" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4214.000000 -599.000000) translate(0,12)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_249e8e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -331.000000) translate(0,12)">054</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_249ee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4341.000000 -174.000000) translate(0,12)">0546</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_249f0b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -169.000000) translate(0,12)">05467</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24dae70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4509.000000 -331.000000) translate(0,12)">052</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24db4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -329.000000) translate(0,12)">053</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24db6e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4824.000000 -330.000000) translate(0,12)">051</text>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 123.000000 -121.500000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519400" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 699.000000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a0b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 683.666667) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251a600" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3636.000000 668.333333) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ab60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3628.000000 653.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251aed0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3998.000000 941.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251ba60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3987.000000 926.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251f360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4010.000000 912.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251fa10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4014.000000 898.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520390" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4364.000000 944.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520580" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4353.000000 929.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4376.000000 915.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25208e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4380.000000 901.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520b80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4715.000000 944.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 929.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2520f80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4727.000000 915.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4731.000000 901.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3980.000000 742.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25216a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3969.000000 727.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25218b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3992.000000 713.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521ac0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3996.000000 699.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2521dc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4714.000000 751.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522030" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4703.000000 736.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 722.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25224b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4730.000000 708.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25227e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 443.500000) translate(0,12)">Ua(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522a40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 428.166667) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522c80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4796.000000 412.833333) translate(0,12)">Uc(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2522fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4138.000000 95.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4127.000000 80.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2523460" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 66.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_25236a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4154.000000 52.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24068f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3846.000000 340.000000) translate(0,12)">P(KW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2406b60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3835.000000 325.666667) translate(0,12)">Q(KVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2406da0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3858.000000 311.333333) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2406fe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3862.000000 297.000000) translate(0,12)">Cos:</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_DHB.CX_DHB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9975"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 -582.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3907.000000 -582.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6856" ObjectName="TF-CX_DHB.CX_DHB_1T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_DHB.CX_DHB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="9979"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -592.000000)" xlink:href="#transformer2:shape0_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4631.000000 -592.000000)" xlink:href="#transformer2:shape0_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="6857" ObjectName="TF-CX_DHB.CX_DHB_2T"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -72.000000)" xlink:href="#transformer2:shape2_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3829.000000 -72.000000)" xlink:href="#transformer2:shape2_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -75.000000)" xlink:href="#transformer2:shape29_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3876.000000 -75.000000)" xlink:href="#transformer2:shape29_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -76.000000)" xlink:href="#transformer2:shape30_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3714.000000 -76.000000)" xlink:href="#transformer2:shape30_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-3KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -164.000000)" xlink:href="#transformer2:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4040.000000 -164.000000)" xlink:href="#transformer2:shape31_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -47.000000)" xlink:href="#transformer2:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4799.000000 -47.000000)" xlink:href="#transformer2:shape31_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-37315" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3432.000000 -1079.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="5897" ObjectName="DYN-CX_DHB"/>
     <cge:Meas_Ref ObjectId="37315"/>
    </metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_228fd60">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4269.000000 -545.000000)" xlink:href="#voltageTransformer:shape138"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_258e1a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4295.000000 -645.000000)" xlink:href="#voltageTransformer:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-3KV" id="g_24f0cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-490 3988,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="6871@x" ObjectIDND1="6872@x" ObjectIDZND0="g_24f76f0@0" Pin0InfoVect0LinkObjId="g_24f76f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41383_0" Pin1InfoVect1LinkObjId="SW-41384_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-490 3988,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25c5c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1017 3959,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDND1="g_2544990@0" ObjectIDND2="g_24ad390@0" ObjectIDZND0="6830@0" Pin0InfoVect0LinkObjId="SW-41323_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="g_2544990_0" Pin1InfoVect2LinkObjId="g_24ad390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1017 3959,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_24f0440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3995,-1017 4034,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6830@1" ObjectIDZND0="g_25c7630@0" Pin0InfoVect0LinkObjId="g_25c7630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41323_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3995,-1017 4034,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d20e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1050 3993,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6830@x" ObjectIDND1="6829@x" ObjectIDND2="0@1" ObjectIDZND0="g_24ad390@0" Pin0InfoVect0LinkObjId="g_24ad390_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41323_0" Pin1InfoVect1LinkObjId="SW-41322_0" Pin1InfoVect2LinkObjId="EC-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1050 3993,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1017 3936,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="powerLine" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="6830@x" ObjectIDND1="6829@x" ObjectIDZND0="0@1" ObjectIDZND1="g_2544990@0" ObjectIDZND2="g_24ad390@0" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="g_2544990_0" Pin0InfoVect2LinkObjId="g_24ad390_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41323_0" Pin1InfoVect1LinkObjId="SW-41322_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1017 3936,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d2c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1050 3936,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="6830@x" ObjectIDND1="6829@x" ObjectIDND2="g_2544990@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41323_0" Pin1InfoVect1LinkObjId="SW-41322_0" Pin1InfoVect2LinkObjId="g_2544990_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1050 3936,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d1580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1017 4328,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_25a7a80@0" ObjectIDND1="g_24ae100@0" ObjectIDND2="0@1" ObjectIDZND0="6834@0" Pin0InfoVect0LinkObjId="SW-41327_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25a7a80_0" Pin1InfoVect1LinkObjId="g_24ae100_0" Pin1InfoVect2LinkObjId="EC-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1017 4328,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25d1770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4364,-1017 4403,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6834@1" ObjectIDZND0="g_25d1960@0" Pin0InfoVect0LinkObjId="g_25d1960_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41327_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4364,-1017 4403,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256bb70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1017 4682,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2546f50@0" ObjectIDND1="0@1" ObjectIDND2="g_24aee70@0" ObjectIDZND0="6838@0" Pin0InfoVect0LinkObjId="SW-41331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2546f50_0" Pin1InfoVect1LinkObjId="EC-0_1" Pin1InfoVect2LinkObjId="g_24aee70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1017 4682,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_256bd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4718,-1017 4757,-1017 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6838@1" ObjectIDZND0="g_256bf50@0" Pin0InfoVect0LinkObjId="g_256bf50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4718,-1017 4757,-1017 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253b0c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-737 4331,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_25275a0@0" ObjectIDND1="g_2528180@0" ObjectIDND2="6845@x" ObjectIDZND0="6846@0" Pin0InfoVect0LinkObjId="SW-41339_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25275a0_0" Pin1InfoVect1LinkObjId="g_2528180_0" Pin1InfoVect2LinkObjId="SW-41338_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-737 4331,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253ba20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-737 4406,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="6846@1" ObjectIDZND0="g_253b2b0@0" Pin0InfoVect0LinkObjId="g_253b2b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41339_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-737 4406,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_253bc10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-737 4251,-737 4251,-688 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_25275a0@0" ObjectIDND1="6846@x" ObjectIDND2="6845@x" ObjectIDZND0="g_2528180@0" Pin0InfoVect0LinkObjId="g_2528180_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25275a0_0" Pin1InfoVect1LinkObjId="SW-41339_0" Pin1InfoVect2LinkObjId="SW-41338_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-737 4251,-737 4251,-688 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-383 4655,-397 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6826@0" ObjectIDZND0="6844@0" Pin0InfoVect0LinkObjId="SW-41337_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-383 4655,-397 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-433 4655,-445 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6844@1" ObjectIDZND0="6843@0" Pin0InfoVect0LinkObjId="SW-41336_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41337_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-433 4655,-445 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-492 4712,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_2530350@0" ObjectIDND1="6843@x" ObjectIDZND0="g_24f8460@0" Pin0InfoVect0LinkObjId="g_24f8460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2530350_0" Pin1InfoVect1LinkObjId="SW-41336_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-492 4712,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25c0a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-478 4655,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6843@1" ObjectIDZND0="g_24f8460@0" ObjectIDZND1="g_2530350@0" Pin0InfoVect0LinkObjId="g_24f8460_0" Pin0InfoVect1LinkObjId="g_2530350_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41336_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-478 4655,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-266 3853,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="hydroGenerator" EndDevType0="lightningRod" ObjectIDND0="6874@x" ObjectIDND1="6876@x" ObjectIDND2="0@x" ObjectIDZND0="g_24ab8b0@0" Pin0InfoVect0LinkObjId="g_24ab8b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41386_0" Pin1InfoVect1LinkObjId="SW-41388_0" Pin1InfoVect2LinkObjId="EC-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-266 3853,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258d440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-282 3795,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="6874@0" ObjectIDZND0="6876@x" ObjectIDZND1="0@x" ObjectIDZND2="6877@x" Pin0InfoVect0LinkObjId="SW-41388_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="SW-41389_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41386_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-282 3795,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258d630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-266 3795,-238 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="hydroGenerator" EndDevType2="switch" ObjectIDND0="6874@x" ObjectIDND1="g_24ab8b0@0" ObjectIDZND0="6876@x" ObjectIDZND1="0@x" ObjectIDZND2="6877@x" Pin0InfoVect0LinkObjId="SW-41388_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="SW-41389_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41386_0" Pin1InfoVect1LinkObjId="g_24ab8b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-266 3795,-238 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_258d820">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-238 3795,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="hydroGenerator" ObjectIDND0="6876@x" ObjectIDND1="6874@x" ObjectIDND2="g_24ab8b0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41388_0" Pin1InfoVect1LinkObjId="SW-41386_0" Pin1InfoVect2LinkObjId="g_24ab8b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-238 3795,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_258ddc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-77 3842,-45 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="0@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-77 3842,-45 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_258dfb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3794,-55 3794,-26 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="lightningRod" ObjectIDZND0="g_24ac620@0" Pin0InfoVect0LinkObjId="g_24ac620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3794,-55 3794,-26 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_258f700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-267 4369,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6854@x" ObjectIDND1="g_24ca5a0@0" ObjectIDZND0="g_24f9f40@0" Pin0InfoVect0LinkObjId="g_24f9f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41347_0" Pin1InfoVect1LinkObjId="g_24ca5a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-267 4369,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2541270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-282 4334,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6854@1" ObjectIDZND0="g_24f9f40@0" ObjectIDZND1="g_24ca5a0@0" Pin0InfoVect0LinkObjId="g_24f9f40_0" Pin0InfoVect1LinkObjId="g_24ca5a0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41347_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-282 4334,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fa580">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-12 4334,-38 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="38198@0" ObjectIDZND0="11357@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_DHB.054Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-12 4334,-38 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25fa7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-74 4334,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="11357@1" ObjectIDZND0="11356@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-74 4334,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2568d00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3987,-210 3987,-200 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2516190@0" ObjectIDZND0="g_24f6ca0@0" Pin0InfoVect0LinkObjId="g_24f6ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2516190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3987,-210 3987,-200 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2568ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3955,-235 3955,-210 3987,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="earth" EndDevType1="lightningRod" ObjectIDZND0="g_24f6ca0@0" ObjectIDZND1="g_2516190@0" Pin0InfoVect0LinkObjId="g_24f6ca0_0" Pin0InfoVect1LinkObjId="g_2516190_0" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3955,-235 3955,-210 3987,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24e3b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-383 3931,-396 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11312@0" ObjectIDZND0="6873@0" Pin0InfoVect0LinkObjId="SW-41385_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2516c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-383 3931,-396 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24e3d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-432 3931,-443 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6873@1" ObjectIDZND0="6871@0" Pin0InfoVect0LinkObjId="SW-41383_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41385_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-432 3931,-443 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253fb80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-470 3931,-490 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="6871@1" ObjectIDZND0="g_24f76f0@0" ObjectIDZND1="6872@x" Pin0InfoVect0LinkObjId="g_24f76f0_0" Pin0InfoVect1LinkObjId="SW-41384_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41383_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-470 3931,-490 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253fd70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-490 3931,-520 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="g_24f76f0@0" ObjectIDND1="6871@x" ObjectIDZND0="6872@0" Pin0InfoVect0LinkObjId="SW-41384_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24f76f0_0" Pin1InfoVect1LinkObjId="SW-41383_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-490 3931,-520 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_253ff60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-304 4019,-304 4019,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2540340@0" ObjectIDND1="6878@x" ObjectIDZND0="g_2516190@0" Pin0InfoVect0LinkObjId="g_2516190_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2540340_0" Pin1InfoVect1LinkObjId="SW-41390_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-304 4019,-304 4019,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2540150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4019,-231 4019,-210 3987,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="earth" ObjectIDND0="g_2516190@1" ObjectIDZND0="g_24f6ca0@0" Pin0InfoVect0LinkObjId="g_24f6ca0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2516190_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4019,-231 4019,-210 3987,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2540980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-255 3956,-263 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2525e90@0" ObjectIDZND0="g_2540340@0" Pin0InfoVect0LinkObjId="g_2540340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2525e90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-255 3956,-263 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2540ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3956,-294 3956,-304 3986,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_2540340@1" ObjectIDZND0="g_2516190@0" ObjectIDZND1="6878@x" Pin0InfoVect0LinkObjId="g_2516190_0" Pin0InfoVect1LinkObjId="SW-41390_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2540340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3956,-294 3956,-304 3986,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2526a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-123 3727,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_2540dc0@0" Pin0InfoVect0LinkObjId="g_2540dc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-123 3727,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2527380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-96 4812,-115 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="g_24b2150@0" Pin0InfoVect0LinkObjId="g_24b2150_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-96 4812,-115 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2527d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-737 4308,-718 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2528180@0" ObjectIDND1="6846@x" ObjectIDND2="6845@x" ObjectIDZND0="g_25275a0@1" Pin0InfoVect0LinkObjId="g_25275a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2528180_0" Pin1InfoVect1LinkObjId="SW-41339_0" Pin1InfoVect2LinkObjId="SW-41338_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-737 4308,-718 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2527f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-687 4308,-665 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_25275a0@0" ObjectIDZND0="g_258e1a0@0" Pin0InfoVect0LinkObjId="g_258e1a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25275a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-687 4308,-665 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259a460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-908 3936,-884 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6827@0" ObjectIDZND0="6828@1" Pin0InfoVect0LinkObjId="SW-41321_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-908 3936,-884 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-848 3936,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6828@0" ObjectIDZND0="11311@0" Pin0InfoVect0LinkObjId="g_2537ce0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41321_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-848 3936,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259a8a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1017 3936,-991 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@1" ObjectIDND1="g_2544990@0" ObjectIDND2="g_24ad390@0" ObjectIDZND0="6829@1" Pin0InfoVect0LinkObjId="SW-41322_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="g_2544990_0" Pin1InfoVect2LinkObjId="g_24ad390_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1017 3936,-991 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_259aac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-955 3936,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6829@0" ObjectIDZND0="6827@1" Pin0InfoVect0LinkObjId="SW-41320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41322_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-955 3936,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2537680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1017 4305,-993 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="g_25a7a80@0" ObjectIDND1="g_24ae100@0" ObjectIDND2="0@1" ObjectIDZND0="6833@1" Pin0InfoVect0LinkObjId="SW-41326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_25a7a80_0" Pin1InfoVect1LinkObjId="g_24ae100_0" Pin1InfoVect2LinkObjId="EC-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1017 4305,-993 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25378a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-957 4305,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6833@0" ObjectIDZND0="6831@1" Pin0InfoVect0LinkObjId="SW-41324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-957 4305,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2537ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-908 4305,-886 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6831@0" ObjectIDZND0="6832@1" Pin0InfoVect0LinkObjId="SW-41325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-908 4305,-886 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2537ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-850 4305,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6832@0" ObjectIDZND0="11311@0" Pin0InfoVect0LinkObjId="g_259a680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-850 4305,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2500160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-908 4659,-878 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6835@0" ObjectIDZND0="6836@1" Pin0InfoVect0LinkObjId="SW-41329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-908 4659,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25003c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-842 4659,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6836@0" ObjectIDZND0="11311@0" Pin0InfoVect0LinkObjId="g_259a680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-842 4659,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2500620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1017 4659,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="g_2546f50@0" ObjectIDND1="0@1" ObjectIDND2="g_24aee70@0" ObjectIDZND0="6837@1" Pin0InfoVect0LinkObjId="SW-41330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2546f50_0" Pin1InfoVect1LinkObjId="EC-0_1" Pin1InfoVect2LinkObjId="g_24aee70_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1017 4659,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2500880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-949 4659,-935 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6837@0" ObjectIDZND0="6835@1" Pin0InfoVect0LinkObjId="SW-41328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-949 4659,-935 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2500f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-826 3931,-794 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11311@0" ObjectIDZND0="6840@1" Pin0InfoVect0LinkObjId="SW-41333_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_259a680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-826 3931,-794 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2501140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-758 3931,-727 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6840@0" ObjectIDZND0="6839@1" Pin0InfoVect0LinkObjId="SW-41332_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41333_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-758 3931,-727 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25017c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-826 4308,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11311@0" ObjectIDZND0="6845@1" Pin0InfoVect0LinkObjId="SW-41338_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_259a680_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-826 4308,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2501a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4308,-762 4308,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="6845@0" ObjectIDZND0="g_25275a0@0" ObjectIDZND1="g_2528180@0" ObjectIDZND2="6846@x" Pin0InfoVect0LinkObjId="g_25275a0_0" Pin0InfoVect1LinkObjId="g_2528180_0" Pin0InfoVect2LinkObjId="SW-41339_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41338_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4308,-762 4308,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2502080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-729 4655,-755 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="6841@1" ObjectIDZND0="6842@0" Pin0InfoVect0LinkObjId="SW-41335_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41334_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-729 4655,-755 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25022c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-791 4655,-826 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6842@1" ObjectIDZND0="11311@0" Pin0InfoVect0LinkObjId="g_259a680_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41335_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-791 4655,-826 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2502940">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-383 3795,-366 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="11312@0" ObjectIDZND0="6875@1" Pin0InfoVect0LinkObjId="SW-41387_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2516c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-383 3795,-366 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2502b80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-330 3795,-309 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="6875@0" ObjectIDZND0="6874@1" Pin0InfoVect0LinkObjId="SW-41386_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41387_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-330 3795,-309 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_24eb610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-316 3986,-304 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6878@0" ObjectIDZND0="g_2516190@0" ObjectIDZND1="g_2540340@0" Pin0InfoVect0LinkObjId="g_2516190_0" Pin0InfoVect1LinkObjId="g_2540340_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41390_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-316 3986,-304 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a5a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-383 4290,-393 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="6826@0" ObjectIDZND0="6847@0" Pin0InfoVect0LinkObjId="SW-41340_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-383 4290,-393 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a5c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-383 4334,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="6826@0" ObjectIDZND0="6854@0" Pin0InfoVect0LinkObjId="SW-41347_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-383 4334,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_25a5ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4496,-383 4496,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="6826@0" ObjectIDZND0="6850@0" Pin0InfoVect0LinkObjId="SW-41343_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4496,-383 4496,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a8050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4248,-1012 4248,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25a7a80@0" ObjectIDZND0="g_2532b30@0" Pin0InfoVect0LinkObjId="g_2532b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a7a80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4248,-1012 4248,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25a82b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-700 3931,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="6839@0" ObjectIDZND0="6856@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41332_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-700 3931,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25a8510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3931,-587 3931,-556 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" ObjectIDND0="6856@0" ObjectIDZND0="6872@1" Pin0InfoVect0LinkObjId="SW-41384_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25a82b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3931,-587 3931,-556 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2530130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-677 4655,-702 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="6857@1" ObjectIDZND0="6841@0" Pin0InfoVect0LinkObjId="SW-41334_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-677 4655,-702 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2531010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-597 4655,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="6857@0" ObjectIDZND0="g_2530350@0" Pin0InfoVect0LinkObjId="g_2530350_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-597 4655,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2531270">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-514 4655,-492 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_2530350@1" ObjectIDZND0="g_24f8460@0" ObjectIDZND1="6843@x" Pin0InfoVect0LinkObjId="g_24f8460_0" Pin0InfoVect1LinkObjId="SW-41336_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2530350_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-514 4655,-492 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2544f60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-1050 3879,-1050 3879,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6830@x" ObjectIDND1="6829@x" ObjectIDND2="0@1" ObjectIDZND0="g_2544990@1" Pin0InfoVect0LinkObjId="g_2544990_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41323_0" Pin1InfoVect1LinkObjId="SW-41322_0" Pin1InfoVect2LinkObjId="EC-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3936,-1050 3879,-1050 3879,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25451c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3879,-1012 3879,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2544990@0" ObjectIDZND0="g_25d2e70@0" Pin0InfoVect0LinkObjId="g_25d2e70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2544990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3879,-1012 3879,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2545420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1048 4305,-1081 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="powerLine" ObjectIDND0="6834@x" ObjectIDND1="6833@x" ObjectIDND2="g_25a7a80@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41327_0" Pin1InfoVect1LinkObjId="SW-41326_0" Pin1InfoVect2LinkObjId="g_25a7a80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1048 4305,-1081 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2546130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1017 4305,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="6834@x" ObjectIDND1="6833@x" ObjectIDZND0="g_25a7a80@0" ObjectIDZND1="g_24ae100@0" ObjectIDZND2="0@1" Pin0InfoVect0LinkObjId="g_25a7a80_0" Pin0InfoVect1LinkObjId="g_24ae100_0" Pin0InfoVect2LinkObjId="EC-0_1" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41327_0" Pin1InfoVect1LinkObjId="SW-41326_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1017 4305,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2546390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1050 4362,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6834@x" ObjectIDND1="6833@x" ObjectIDND2="g_25a7a80@0" ObjectIDZND0="g_24ae100@0" Pin0InfoVect0LinkObjId="g_24ae100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41327_0" Pin1InfoVect1LinkObjId="SW-41326_0" Pin1InfoVect2LinkObjId="g_25a7a80_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1050 4362,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25465f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4305,-1050 4248,-1050 4248,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6834@x" ObjectIDND1="6833@x" ObjectIDND2="g_24ae100@0" ObjectIDZND0="g_25a7a80@1" Pin0InfoVect0LinkObjId="g_25a7a80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41327_0" Pin1InfoVect1LinkObjId="SW-41326_0" Pin1InfoVect2LinkObjId="g_24ae100_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4305,-1050 4248,-1050 4248,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_25477d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4602,-1012 4602,-1001 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2546f50@0" ObjectIDZND0="g_2546850@0" Pin0InfoVect0LinkObjId="g_2546850_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2546f50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4602,-1012 4602,-1001 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2547a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1050 4602,-1050 4602,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="powerLine" EndDevType0="lightningRod" ObjectIDND0="6838@x" ObjectIDND1="6837@x" ObjectIDND2="0@1" ObjectIDZND0="g_2546f50@1" Pin0InfoVect0LinkObjId="g_2546f50_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41331_0" Pin1InfoVect1LinkObjId="SW-41330_0" Pin1InfoVect2LinkObjId="EC-0_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1050 4602,-1050 4602,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250ce40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1017 4659,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="lightningRod" ObjectIDND0="6838@x" ObjectIDND1="6837@x" ObjectIDZND0="g_2546f50@0" ObjectIDZND1="0@1" ObjectIDZND2="g_24aee70@0" Pin0InfoVect0LinkObjId="g_2546f50_0" Pin0InfoVect1LinkObjId="EC-0_1" Pin0InfoVect2LinkObjId="g_24aee70_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41331_0" Pin1InfoVect1LinkObjId="SW-41330_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1017 4659,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250d0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1050 4716,-1050 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_2546f50@0" ObjectIDND1="6838@x" ObjectIDND2="6837@x" ObjectIDZND0="g_24aee70@0" Pin0InfoVect0LinkObjId="g_24aee70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2546f50_0" Pin1InfoVect1LinkObjId="SW-41331_0" Pin1InfoVect2LinkObjId="SW-41330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1050 4716,-1050 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_250d300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-1050 4659,-1080 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_2546f50@0" ObjectIDND1="6838@x" ObjectIDND2="6837@x" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="EC-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2546f50_0" Pin1InfoVect1LinkObjId="SW-41331_0" Pin1InfoVect2LinkObjId="SW-41330_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-1050 4659,-1080 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2409720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-237 3727,-237 3727,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="hydroGenerator" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="6876@x" ObjectIDND1="0@x" ObjectIDND2="6874@x" ObjectIDZND0="6877@1" Pin0InfoVect0LinkObjId="SW-41389_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-41388_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="SW-41386_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-237 3727,-237 3727,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2409980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3727,-191 3727,-171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="6877@0" ObjectIDZND0="g_2540dc0@1" Pin0InfoVect0LinkObjId="g_2540dc0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41389_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3727,-191 3727,-171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_240c490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3795,-237 3889,-237 3889,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="hydroGenerator" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="6874@x" ObjectIDND2="g_24ab8b0@0" ObjectIDZND0="6876@1" Pin0InfoVect0LinkObjId="SW-41388_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="SW-41386_0" Pin1InfoVect2LinkObjId="g_24ab8b0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3795,-237 3889,-237 3889,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_25142b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3842,-119 3842,-179 3889,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="0@0" ObjectIDZND0="6876@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-41388_0" Pin0InfoVect1LinkObjId="EC-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3842,-119 3842,-179 3889,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2514510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-179 3889,-191 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="6876@0" Pin0InfoVect0LinkObjId="SW-41388_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-179 3889,-191 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2514770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3889,-122 3889,-179 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="transformer2" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="6876@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="SW-41388_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3889,-122 3889,-179 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2516c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3986,-352 3986,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6878@1" ObjectIDZND0="11312@0" Pin0InfoVect0LinkObjId="g_2516e80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41390_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3986,-352 3986,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_2516e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-356 4084,-383 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="6879@1" ObjectIDZND0="11312@0" Pin0InfoVect0LinkObjId="g_2516c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41391_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-356 4084,-383 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_23e16c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4053,-259 4053,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_2517960@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2517960_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4053,-259 4053,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_23e7160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-320 4084,-301 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6879@0" ObjectIDZND0="g_2517960@0" ObjectIDZND1="g_25170e0@0" Pin0InfoVect0LinkObjId="g_2517960_0" Pin0InfoVect1LinkObjId="g_25170e0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41391_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-320 4084,-301 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_23e73c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-301 4053,-301 4053,-290 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_25170e0@0" ObjectIDND1="6879@x" ObjectIDZND0="g_2517960@1" Pin0InfoVect0LinkObjId="g_2517960_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_25170e0_0" Pin1InfoVect1LinkObjId="SW-41391_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-301 4053,-301 4053,-290 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_23e7620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4084,-301 4120,-301 4120,-285 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2517960@0" ObjectIDND1="6879@x" ObjectIDZND0="g_25170e0@1" Pin0InfoVect0LinkObjId="g_25170e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2517960_0" Pin1InfoVect1LinkObjId="SW-41391_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4084,-301 4120,-301 4120,-285 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-3KV" id="g_23e7880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4120,-254 4120,-234 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_25170e0@0" ObjectIDZND0="g_23e4ea0@1" Pin0InfoVect0LinkObjId="g_23e4ea0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_25170e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4120,-254 4120,-234 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e84c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4659,-89 4648,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_24facb0@0" ObjectIDZND0="0@x" ObjectIDZND1="11358@x" Pin0InfoVect0LinkObjId="EC-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24facb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4659,-89 4648,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e8e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-77 4648,-89 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@0" ObjectIDZND0="g_24facb0@0" ObjectIDZND1="11358@x" Pin0InfoVect0LinkObjId="g_24facb0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-77 4648,-89 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_23e9040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-89 4648,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" ObjectIDND0="g_24facb0@0" ObjectIDND1="0@x" ObjectIDZND0="11358@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24facb0_0" Pin1InfoVect1LinkObjId="EC-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-89 4648,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f67e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4844,-168 4812,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_24f91d0@0" ObjectIDZND0="g_2526fe0@0" Pin0InfoVect0LinkObjId="g_2526fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24f91d0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4844,-168 4812,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24f6a40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-168 4812,-151 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" ObjectIDND0="g_24f91d0@0" ObjectIDND1="g_2526fe0@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24f91d0_0" Pin1InfoVect1LinkObjId="g_2526fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-168 4812,-151 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24bf6d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-477 4247,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6847@x" ObjectIDND1="g_24c1e10@0" ObjectIDZND0="g_24c09f0@0" Pin0InfoVect0LinkObjId="g_24c09f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41340_0" Pin1InfoVect1LinkObjId="g_24c1e10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-477 4247,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c01c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-465 4290,-477 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="6847@1" ObjectIDZND0="g_24c09f0@0" ObjectIDZND1="g_24c1e10@0" Pin0InfoVect0LinkObjId="g_24c09f0_0" Pin0InfoVect1LinkObjId="g_24c1e10_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-41340_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-465 4290,-477 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c24c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-477 4290,-494 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6847@x" ObjectIDND1="g_24c09f0@0" ObjectIDZND0="g_24c1e10@1" Pin0InfoVect0LinkObjId="g_24c1e10_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41340_0" Pin1InfoVect1LinkObjId="g_24c09f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-477 4290,-494 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24c26f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4290,-525 4290,-550 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_24c1e10@0" ObjectIDZND0="g_228fd60@0" Pin0InfoVect0LinkObjId="g_228fd60_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24c1e10_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4290,-525 4290,-550 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cb2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-185 4334,-199 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="37448@1" ObjectIDZND0="g_24ca5a0@1" Pin0InfoVect0LinkObjId="g_24ca5a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-185 4334,-199 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cb550">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-252 4334,-267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_24ca5a0@0" ObjectIDZND0="g_24f9f40@0" ObjectIDZND1="6854@x" Pin0InfoVect0LinkObjId="g_24f9f40_0" Pin0InfoVect1LinkObjId="SW-41347_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24ca5a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-252 4334,-267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24cc040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-134 4334,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_249ce20@0" ObjectIDND1="11356@x" ObjectIDND2="37449@x" ObjectIDZND0="37448@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_249ce20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-134 4334,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249bc70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-134 4294,-134 4294,-144 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_249ce20@0" ObjectIDND1="11356@x" ObjectIDND2="37448@x" ObjectIDZND0="37449@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_249ce20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="SW-0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-134 4294,-134 4294,-144 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249bed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4294,-180 4294,-188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="37449@1" ObjectIDZND0="g_249c130@0" Pin0InfoVect0LinkObjId="g_249c130_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4294,-180 4294,-188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4346,-128 4335,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_249ce20@0" ObjectIDZND0="11356@x" ObjectIDZND1="37449@x" ObjectIDZND2="37448@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_249ce20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4346,-128 4335,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249e420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-116 4334,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="11356@1" ObjectIDZND0="g_249ce20@0" ObjectIDZND1="37449@x" ObjectIDZND2="37448@x" Pin0InfoVect0LinkObjId="g_249ce20_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-116 4334,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_249e680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4334,-128 4334,-134 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_249ce20@0" ObjectIDND1="11356@x" ObjectIDZND0="37449@x" ObjectIDZND1="37448@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_249ce20_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4334,-128 4334,-134 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a7f10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-266 4532,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6850@x" ObjectIDND1="g_2526c40@0" ObjectIDZND0="g_24a8170@0" Pin0InfoVect0LinkObjId="g_24a8170_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41343_0" Pin1InfoVect1LinkObjId="g_2526c40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-266 4532,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24a9770">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-266 4497,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24a8170@0" ObjectIDND1="g_2526c40@0" ObjectIDZND0="6850@1" Pin0InfoVect0LinkObjId="SW-41343_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a8170_0" Pin1InfoVect1LinkObjId="g_2526c40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-266 4497,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24aa720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4647,-383 4647,-367 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="6826@0" ObjectIDZND0="6848@0" Pin0InfoVect0LinkObjId="SW-41341_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4647,-383 4647,-367 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d2140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-265 4683,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6848@x" ObjectIDND1="g_24a99d0@0" ObjectIDZND0="g_24d23a0@0" Pin0InfoVect0LinkObjId="g_24d23a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41341_0" Pin1InfoVect1LinkObjId="g_24a99d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-265 4683,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d3110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-265 4648,-276 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24d23a0@0" ObjectIDND1="g_24a99d0@0" ObjectIDZND0="6848@1" Pin0InfoVect0LinkObjId="SW-41341_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d23a0_0" Pin1InfoVect1LinkObjId="g_24a99d0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-265 4648,-276 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d41d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4811,-383 4811,-368 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="6826@0" ObjectIDZND0="6852@0" Pin0InfoVect0LinkObjId="SW-41345_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4811,-383 4811,-368 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d8de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-266 4847,-266 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="6852@x" ObjectIDND1="g_2526fe0@0" ObjectIDZND0="g_24d9040@0" Pin0InfoVect0LinkObjId="g_24d9040_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-41345_0" Pin1InfoVect1LinkObjId="g_2526fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-266 4847,-266 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24d9db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-266 4812,-277 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_24d9040@0" ObjectIDND1="g_2526fe0@0" ObjectIDZND0="6852@1" Pin0InfoVect0LinkObjId="SW-41345_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d9040_0" Pin1InfoVect1LinkObjId="g_2526fe0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-266 4812,-277 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24db920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-266 4812,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_24d9040@0" ObjectIDND1="6852@x" ObjectIDZND0="g_2526fe0@0" Pin0InfoVect0LinkObjId="g_2526fe0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d9040_0" Pin1InfoVect1LinkObjId="SW-41345_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-266 4812,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dbb10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4812,-199 4812,-168 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_2526fe0@1" ObjectIDZND0="g_24f91d0@0" Pin0InfoVect0LinkObjId="g_24f91d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2526fe0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4812,-199 4812,-168 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dbd00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-266 4497,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_24a8170@0" ObjectIDND1="6850@x" ObjectIDZND0="g_2526c40@0" Pin0InfoVect0LinkObjId="g_2526c40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24a8170_0" Pin1InfoVect1LinkObjId="SW-41343_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-266 4497,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dc120">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-265 4648,-252 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_24d23a0@0" ObjectIDND1="6848@x" ObjectIDZND0="g_24a99d0@0" Pin0InfoVect0LinkObjId="g_24a99d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_24d23a0_0" Pin1InfoVect1LinkObjId="SW-41341_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-265 4648,-252 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_24dc350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4648,-199 4648,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_24a99d0@1" ObjectIDZND0="11358@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24a99d0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4648,-199 4648,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2649290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4509,-88 4497,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="g_24fba20@0" ObjectIDZND0="g_2526c40@0" ObjectIDZND1="46630@x" Pin0InfoVect0LinkObjId="g_2526c40_0" Pin0InfoVect1LinkObjId="EC-CX_DHB.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_24fba20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4509,-88 4497,-88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2740cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-88 4497,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="load" ObjectIDND0="g_2526c40@0" ObjectIDND1="g_24fba20@0" ObjectIDZND0="46630@0" Pin0InfoVect0LinkObjId="EC-CX_DHB.052Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2526c40_0" Pin1InfoVect1LinkObjId="g_24fba20_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-88 4497,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2740470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-199 4497,-149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_2526c40@1" ObjectIDZND0="11359@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2526c40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-199 4497,-149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_250f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4497,-113 4497,-88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="11359@0" ObjectIDZND0="g_24fba20@0" ObjectIDZND1="46630@x" Pin0InfoVect0LinkObjId="g_24fba20_0" Pin0InfoVect1LinkObjId="EC-CX_DHB.052Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4497,-113 4497,-88 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_DHB"/>
</svg>