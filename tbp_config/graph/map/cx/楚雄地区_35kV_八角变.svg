<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-108" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3117 -1199 1958 1201">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="1" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="17" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape1_0">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1_1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor1">
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
   </symbol>
   <symbol id="breaker2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="34" x2="10" y1="15" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="34" y1="16" y2="5"/>
    <rect height="13" stroke-width="0.416609" width="26" x="9" y="3"/>
   </symbol>
   <symbol id="capacitor:shape10">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="37" x2="37" y1="122" y2="130"/>
    <polyline arcFlag="1" points="37,122 35,122 33,121 32,121 30,120 29,119 27,118 26,116 25,114 25,113 24,111 24,109 24,107 25,105 25,104 26,102 27,101 29,99 30,98 32,97 33,97 35,96 37,96 39,96 41,97 42,97 44,98 45,99 47,101 48,102 49,104 49,105 50,107 50,109 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="50" x2="38" y1="109" y2="109"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.915724" x1="37" x2="37" y1="109" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="25" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.289104" x1="27" x2="47" y1="17" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="23" y2="14"/>
    <polyline arcFlag="1" points="13,23 12,23 12,23 11,23 10,23 10,24 9,24 8,25 8,25 8,26 7,27 7,27 7,28 7,29 7,30 7,30 8,31 8,32 8,32 9,33 10,33 10,34 11,34 12,34 12,34 13,34 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,34 12,34 12,34 11,34 10,34 10,35 9,35 8,36 8,36 8,37 7,38 7,38 7,39 7,40 7,41 7,41 8,42 8,43 8,43 9,44 10,44 10,45 11,45 12,45 12,45 13,45 " stroke-width="0.0428972"/>
    <polyline arcFlag="1" points="13,45 12,45 12,45 11,45 10,45 10,46 9,46 8,47 8,47 8,48 7,49 7,49 7,50 7,51 7,52 7,52 8,53 8,54 8,54 9,55 10,55 10,56 11,56 12,56 12,56 13,56 " stroke-width="0.0428972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.715488" x1="13" x2="13" y1="65" y2="56"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.393258" x1="21" x2="56" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.603704" x1="13" x2="37" y1="13" y2="13"/>
    <rect height="26" stroke-width="0.398039" width="12" x="31" y="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="20" x2="20" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.431962" x1="2" x2="2" y1="56" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.550926" x1="57" x2="57" y1="2" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.504561" x1="37" x2="37" y1="16" y2="3"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="29" x2="29" y1="7" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="4" x2="22" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="22" x2="22" y1="0" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="25" x2="25" y1="6" y2="13"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
   </symbol>
   <symbol id="lightningRod:shape55">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="55" x2="55" y1="12" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="54" x2="46" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="59" x2="59" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="62" x2="62" y1="5" y2="8"/>
    <rect height="12" stroke-width="1" width="26" x="19" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="39" y1="7" y2="7"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="6" y2="41"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="63" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="55" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="56" y2="56"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape19_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="6" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="7" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="27,39 5,17 5,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="9" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="50" y2="42"/>
    <rect height="4" stroke-width="1" width="19" x="-15" y="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-16,39 6,17 6,5 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="2" y1="41" y2="41"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="28" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="25" y2="31"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,49 16,27 28,27 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="29"/>
   </symbol>
   <symbol id="switch2:shape19-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-8" x2="-8" y1="30" y2="24"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="-6,6 16,28 28,28 " stroke-width="1"/>
    <rect height="19" stroke-width="1" width="4" x="3" y="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="-17" x2="-9" y1="27" y2="27"/>
   </symbol>
   <symbol id="transformer2:shape55_0">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="31,44 6,44 6,73 " stroke-width="1"/>
    <circle cx="31" cy="42" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="56" y2="98"/>
    <polyline DF8003:Layer="PUBLIC" points="31,87 25,74 37,74 31,87 31,86 31,87 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="74" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="76" y2="76"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="31" y1="49" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="44" y2="49"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="44" y2="39"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="44" y2="39"/>
   </symbol>
   <symbol id="transformer2:shape55_1">
    <circle cx="31" cy="20" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="20" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="36" y1="20" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="20" y2="15"/>
   </symbol>
   <symbol id="transformer2:shape95_0">
    <ellipse cx="14" cy="43" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="13" y1="38" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="17" x2="13" y1="7" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="9" y1="0" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="13" y1="31" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="37" x2="37" y1="42" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="37" y1="42" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="39" x2="35" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="46" x2="28" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="40" x2="33" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="46" y2="42"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="42" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="38" y2="42"/>
   </symbol>
   <symbol id="transformer2:shape95_1">
    <polyline DF8003:Layer="PUBLIC" points="12,70 8,61 18,61 12,70 "/>
    <circle cx="13" cy="62" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape21_0">
    <circle cx="37" cy="66" fillStyle="0" r="26.5" stroke-width="0.63865"/>
    <polyline points="64,100 1,37 " stroke-width="1.12088"/>
    <polyline points="58,100 64,100 " stroke-width="1.12088"/>
    <polyline points="64,100 64,93 " stroke-width="1.12088"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="32" y1="71" y2="62"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="46" x2="38" y1="63" y2="71"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="38" x2="38" y1="70" y2="79"/>
   </symbol>
   <symbol id="transformer2:shape21_1">
    <ellipse cx="37" cy="29" fillStyle="0" rx="26.5" ry="25.5" stroke-width="0.62032"/>
    <polyline DF8003:Layer="PUBLIC" points="38,34 31,19 46,19 38,34 38,34 38,34 "/>
   </symbol>
   <symbol id="voltageTransformer:shape15">
    <ellipse cx="23" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="20" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="13" x2="2" y1="26" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="14" y2="6"/>
    <rect height="13" stroke-width="1" width="7" x="4" y="14"/>
    <ellipse cx="34" cy="24" rx="7.5" ry="6.5" stroke-width="0.66594"/>
    <ellipse cx="23" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <ellipse cx="34" cy="35" rx="7.5" ry="7" stroke-width="0.66594"/>
    <polyline points="24,36 8,36 8,26 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="6" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="3" y1="6" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="21" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="24" x2="24" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="35" x2="35" y1="36" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="32" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="37" x2="34" y1="38" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.246311" x1="34" x2="32" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.245503" x1="35" x2="37" y1="27" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.238574" x1="37" x2="32" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.10043" x1="23" x2="23" y1="24" y2="22"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="20" x2="23" y1="26" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.179585" x1="26" x2="23" y1="26" y2="24"/>
   </symbol>
   <symbol id="voltageTransformer:shape17">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="11" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="11" y2="5"/>
    <circle cx="9" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
    <circle cx="20" cy="8" fillStyle="0" r="8.5" stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_3391640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33927b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33931a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3393e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3395070" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_3395d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33968b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_33971d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cbc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2cbc6e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3399fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_3399fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339ba40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_339c740" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_339e020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_339ec10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_339f9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a0080" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a16b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a20d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a2830" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a2ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a40d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a4a50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33a5540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a5f00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a73b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a7ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_33a8f10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33a9b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_33b8320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_33ab1d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33abe90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_33ad3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1211" width="1968" x="3112" y="-1204"/>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="1" x1="4933" x2="4933" y1="-116" y2="-101"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="3605" x2="3605" y1="-323" y2="-312"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-57435">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10727" ObjectName="SW-CX_BJ.CX_BJ_092BK"/>
     <cge:Meas_Ref ObjectId="57435"/>
    <cge:TPSR_Ref TObjectID="10727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57433">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10723" ObjectName="SW-CX_BJ.CX_BJ_091BK"/>
     <cge:Meas_Ref ObjectId="57433"/>
    <cge:TPSR_Ref TObjectID="10723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57430">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -876.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10722" ObjectName="SW-CX_BJ.CX_BJ_381BK"/>
     <cge:Meas_Ref ObjectId="57430"/>
    <cge:TPSR_Ref TObjectID="10722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57428">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.000000 -668.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10721" ObjectName="SW-CX_BJ.CX_BJ_301BK"/>
     <cge:Meas_Ref ObjectId="57428"/>
    <cge:TPSR_Ref TObjectID="10721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57429">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -491.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10703" ObjectName="SW-CX_BJ.CX_BJ_001BK"/>
     <cge:Meas_Ref ObjectId="57429"/>
    <cge:TPSR_Ref TObjectID="10703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57437">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4284.000000 -302.000000)" xlink:href="#breaker2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10730" ObjectName="SW-CX_BJ.CX_BJ_012BK"/>
     <cge:Meas_Ref ObjectId="57437"/>
    <cge:TPSR_Ref TObjectID="10730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57434">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10724" ObjectName="SW-CX_BJ.CX_BJ_093BK"/>
     <cge:Meas_Ref ObjectId="57434"/>
    <cge:TPSR_Ref TObjectID="10724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57436">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10729" ObjectName="SW-CX_BJ.CX_BJ_095BK"/>
     <cge:Meas_Ref ObjectId="57436"/>
    <cge:TPSR_Ref TObjectID="10729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57432">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10720" ObjectName="SW-CX_BJ.CX_BJ_081BK"/>
     <cge:Meas_Ref ObjectId="57432"/>
    <cge:TPSR_Ref TObjectID="10720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57431">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -286.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10719" ObjectName="SW-CX_BJ.CX_BJ_082BK"/>
     <cge:Meas_Ref ObjectId="57431"/>
    <cge:TPSR_Ref TObjectID="10719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193482">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 -877.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29416" ObjectName="SW-CX_BJ.CX_BJ_382BK"/>
     <cge:Meas_Ref ObjectId="193482"/>
    <cge:TPSR_Ref TObjectID="29416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273814">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.250000 -678.978723)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43909" ObjectName="SW-CX_BJ.CX_BJ_302BK"/>
     <cge:Meas_Ref ObjectId="273814"/>
    <cge:TPSR_Ref TObjectID="43909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273834">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -501.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43912" ObjectName="SW-CX_BJ.CX_BJ_002BK"/>
     <cge:Meas_Ref ObjectId="273834"/>
    <cge:TPSR_Ref TObjectID="43912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273918">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -283.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43914" ObjectName="SW-CX_BJ.CX_BJ_085BK"/>
     <cge:Meas_Ref ObjectId="273918"/>
    <cge:TPSR_Ref TObjectID="43914"/></metadata>
   </g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a4c240">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4416.000000 -1023.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29842f0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4031.000000 -996.000000)" xlink:href="#voltageTransformer:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29e07a0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 -997.000000)" xlink:href="#voltageTransformer:shape17"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a09a30">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3834.000000 -607.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a0c4a0">
    <use class="BV-10KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4468.000000 -604.000000)" xlink:href="#voltageTransformer:shape15"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_DuJ" endPointId="0" endStationName="CX_BJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_duba" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4151,-1072 4151,-1104 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="38062" ObjectName="AC-35kV.LN_duba"/>
    <cge:TPSR_Ref TObjectID="38062_SS-108"/></metadata>
   <polyline fill="none" opacity="0" points="4151,-1072 4151,-1104 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="NH_MJ" endPointId="0" endStationName="CX_BJ" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_bama" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4687,-1074 4687,-1102 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34579" ObjectName="AC-35kV.LN_bama"/>
    <cge:TPSR_Ref TObjectID="34579_SS-108"/></metadata>
   <polyline fill="none" opacity="0" points="4687,-1074 4687,-1102 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4832.000000 -116.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5013.000000 -115.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3838.000000 -116.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3760.000000 -116.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 -116.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BJ.093Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4729.000000 -86.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34367" ObjectName="EC-CX_BJ.093Ld"/>
    <cge:TPSR_Ref TObjectID="34367"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BJ.092Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -86.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34366" ObjectName="EC-CX_BJ.092Ld"/>
    <cge:TPSR_Ref TObjectID="34366"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BJ.091Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -88.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34365" ObjectName="EC-CX_BJ.091Ld"/>
    <cge:TPSR_Ref TObjectID="34365"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BJ.081Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4088.000000 -89.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34363" ObjectName="EC-CX_BJ.081Ld"/>
    <cge:TPSR_Ref TObjectID="34363"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-CX_BJ.082Ld">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3948.000000 -89.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="34364" ObjectName="EC-CX_BJ.082Ld"/>
    <cge:TPSR_Ref TObjectID="34364"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2b06580" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -975.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b591a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -913.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a91f80" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4223.000000 -861.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aedaa0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -820.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a49d50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -903.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2abf730" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4723.000000 -712.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac7200" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -32.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aca340" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4979.500000 -221.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a37f70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -976.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2962c20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -914.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29de610" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -862.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29f2370" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4264.000000 -722.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2973bc0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3720.500000 -218.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_2b06390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-985 4228,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10753@1" ObjectIDZND0="g_2b06580@0" Pin0InfoVect0LinkObjId="g_2b06580_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57469_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-985 4228,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af54f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-484 3779,-484 3779,-500 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="lightningRod" ObjectIDND0="10751@x" ObjectIDND1="g_2a382c0@0" ObjectIDZND0="g_2af69b0@0" Pin0InfoVect0LinkObjId="g_2af69b0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57467_0" Pin1InfoVect1LinkObjId="g_2a382c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-484 3779,-484 3779,-500 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af56e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-484 3810,-459 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="g_2af69b0@0" ObjectIDND1="g_2a382c0@0" ObjectIDZND0="10751@1" Pin0InfoVect0LinkObjId="SW-57467_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af69b0_0" Pin1InfoVect1LinkObjId="g_2a382c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-484 3810,-459 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af58d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-484 3810,-502 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2af69b0@0" ObjectIDND1="10751@x" ObjectIDZND0="g_2a382c0@1" Pin0InfoVect0LinkObjId="g_2a382c0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af69b0_0" Pin1InfoVect1LinkObjId="SW-57467_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-484 3810,-502 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2af5ac0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3811,-547 3811,-565 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2a382c0@0" ObjectIDZND0="g_2a09a30@0" Pin0InfoVect0LinkObjId="g_2a09a30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a382c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3811,-547 3811,-565 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aa6370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-786 4651,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10731@1" ObjectIDZND0="10705@0" Pin0InfoVect0LinkObjId="g_2a8cbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57447_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-786 4651,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa6560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-461 4650,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10732@1" ObjectIDZND0="10703@0" Pin0InfoVect0LinkObjId="SW-57429_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57448_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-461 4650,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a94680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-923 4228,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10754@1" ObjectIDZND0="g_2b591a0@0" Pin0InfoVect0LinkObjId="g_2b591a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57470_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-923 4228,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a8cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-822 4150,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10734@0" ObjectIDZND0="10705@0" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57450_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-822 4150,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a8cdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-985 4151,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="10753@0" ObjectIDZND0="g_2af5fa0@0" ObjectIDZND1="38062@1" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="g_2af5fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="g_2a4c240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57469_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-985 4151,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4ee00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-985 4151,-970 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_2af5fa0@0" ObjectIDND1="38062@1" ObjectIDND2="0@x" ObjectIDZND0="10733@1" Pin0InfoVect0LinkObjId="SW-57449_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2af5fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2a4c240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-985 4151,-970 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b0ea40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-923 4151,-923 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10754@0" ObjectIDZND0="10722@x" ObjectIDZND1="10733@x" Pin0InfoVect0LinkObjId="SW-57430_0" Pin0InfoVect1LinkObjId="SW-57449_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57470_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-923 4151,-923 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae4ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-934 4151,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10733@0" ObjectIDZND0="10722@x" ObjectIDZND1="10754@x" Pin0InfoVect0LinkObjId="SW-57430_0" Pin0InfoVect1LinkObjId="SW-57470_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57449_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-934 4151,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ae4ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-924 4151,-911 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="10733@x" ObjectIDND1="10754@x" ObjectIDZND0="10722@1" Pin0InfoVect0LinkObjId="SW-57430_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57449_0" Pin1InfoVect1LinkObjId="SW-57470_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-924 4151,-911 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a91d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4209,-871 4228,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10755@1" ObjectIDZND0="g_2a91f80@0" Pin0InfoVect0LinkObjId="g_2a91f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57471_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4209,-871 4228,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a928b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4173,-871 4151,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10755@0" ObjectIDZND0="10722@x" ObjectIDZND1="10734@x" Pin0InfoVect0LinkObjId="SW-57430_0" Pin0InfoVect1LinkObjId="SW-57450_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57471_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4173,-871 4151,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a93220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-884 4151,-871 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10722@0" ObjectIDZND0="10734@x" ObjectIDZND1="10755@x" Pin0InfoVect0LinkObjId="SW-57450_0" Pin0InfoVect1LinkObjId="SW-57471_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-884 4151,-871 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a93440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-871 4151,-858 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10722@x" ObjectIDND1="10755@x" ObjectIDZND0="10734@1" Pin0InfoVect0LinkObjId="SW-57450_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57430_0" Pin1InfoVect1LinkObjId="SW-57471_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-871 4151,-858 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b335a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-1042 4151,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_2af5fa0@0" ObjectIDZND0="10733@x" ObjectIDZND1="10753@x" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-57449_0" Pin0InfoVect1LinkObjId="SW-57469_0" Pin0InfoVect2LinkObjId="g_2a4c240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2af5fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-1042 4151,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b337c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4119,-1005 4151,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="10733@x" ObjectIDZND1="10753@x" ObjectIDZND2="g_2af5fa0@0" Pin0InfoVect0LinkObjId="SW-57449_0" Pin0InfoVect1LinkObjId="SW-57469_0" Pin0InfoVect2LinkObjId="g_2af5fa0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4119,-1005 4151,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b34130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-1005 4151,-985 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="powerLine" BeginDevType2="switch" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_2af5fa0@0" ObjectIDND1="38062@1" ObjectIDND2="0@x" ObjectIDZND0="10733@x" ObjectIDZND1="10753@x" Pin0InfoVect0LinkObjId="SW-57449_0" Pin0InfoVect1LinkObjId="SW-57469_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2af5fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="g_2a4c240_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-1005 4151,-985 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b34aa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-1074 4151,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="38062@1" ObjectIDZND0="g_2af5fa0@0" ObjectIDZND1="10733@x" ObjectIDZND2="10753@x" Pin0InfoVect0LinkObjId="g_2af5fa0_0" Pin0InfoVect1LinkObjId="SW-57449_0" Pin0InfoVect2LinkObjId="SW-57469_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-1074 4151,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2b34cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4151,-1005 4151,-1042 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="10733@x" ObjectIDND1="10753@x" ObjectIDND2="0@x" ObjectIDZND0="g_2af5fa0@0" ObjectIDZND1="38062@1" Pin0InfoVect0LinkObjId="g_2af5fa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57449_0" Pin1InfoVect1LinkObjId="SW-57469_0" Pin1InfoVect2LinkObjId="g_2a4c240_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4151,-1005 4151,-1042 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aed880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-830 4470,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10759@1" ObjectIDZND0="g_2aedaa0@0" Pin0InfoVect0LinkObjId="g_2aedaa0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57475_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-830 4470,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2aee3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-830 4393,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="10759@0" ObjectIDZND0="10705@0" ObjectIDZND1="10749@x" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="SW-57465_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-830 4393,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a49b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4451,-913 4470,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10758@1" ObjectIDZND0="g_2a49d50@0" Pin0InfoVect0LinkObjId="g_2a49d50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57474_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4451,-913 4470,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4a680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4415,-913 4393,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="voltageTransformer" ObjectIDND0="10758@0" ObjectIDZND0="10749@x" ObjectIDZND1="g_2a4b430@0" ObjectIDZND2="g_2a4c240@0" Pin0InfoVect0LinkObjId="SW-57465_0" Pin0InfoVect1LinkObjId="g_2a4b430_0" Pin0InfoVect2LinkObjId="g_2a4c240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57474_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4415,-913 4393,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4b210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-911 4393,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="voltageTransformer" EndDevType0="switch" ObjectIDND0="10758@x" ObjectIDND1="g_2a4b430@0" ObjectIDND2="g_2a4c240@0" ObjectIDZND0="10749@1" Pin0InfoVect0LinkObjId="SW-57465_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57474_0" Pin1InfoVect1LinkObjId="g_2a4b430_0" Pin1InfoVect2LinkObjId="g_2a4c240_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-911 4393,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a4c020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4347,-913 4391,-913 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="voltageTransformer" ObjectIDND0="g_2a4b430@0" ObjectIDZND0="10749@x" ObjectIDZND1="10758@x" ObjectIDZND2="g_2a4c240@0" Pin0InfoVect0LinkObjId="SW-57465_0" Pin0InfoVect1LinkObjId="SW-57474_0" Pin0InfoVect2LinkObjId="g_2a4c240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4b430_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4347,-913 4391,-913 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a01350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-805 4393,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10705@0" ObjectIDZND0="10749@x" ObjectIDZND1="10759@x" Pin0InfoVect0LinkObjId="SW-57465_0" Pin0InfoVect1LinkObjId="SW-57475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa6370_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-805 4393,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a01540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-858 4393,-830 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" EndDevType1="switch" ObjectIDND0="10749@0" ObjectIDZND0="10705@0" ObjectIDZND1="10759@x" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="SW-57475_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57465_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-858 4393,-830 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2abf510">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-722 4728,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10752@1" ObjectIDZND0="g_2abf730@0" Pin0InfoVect0LinkObjId="g_2abf730_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57468_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-722 4728,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac0060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4673,-722 4651,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="10752@0" ObjectIDZND0="10721@x" ObjectIDZND1="10731@x" Pin0InfoVect0LinkObjId="SW-57428_0" Pin0InfoVect1LinkObjId="SW-57447_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57468_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4673,-722 4651,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac0280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-703 4651,-722 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="10721@1" ObjectIDZND0="10731@x" ObjectIDZND1="10752@x" Pin0InfoVect0LinkObjId="SW-57447_0" Pin0InfoVect1LinkObjId="SW-57468_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57428_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-703 4651,-722 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ac04a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-722 4651,-750 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="10721@x" ObjectIDND1="10752@x" ObjectIDZND0="10731@0" Pin0InfoVect0LinkObjId="SW-57447_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-57428_0" Pin1InfoVect1LinkObjId="SW-57468_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-722 4651,-750 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac1df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-481 4413,-481 4413,-497 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ac0e80@0" ObjectIDND1="10750@x" ObjectIDZND0="g_2ac23c0@0" Pin0InfoVect0LinkObjId="g_2ac23c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac0e80_0" Pin1InfoVect1LinkObjId="SW-58407_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-481 4413,-481 4413,-497 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac1fe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-481 4444,-499 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_2ac23c0@0" ObjectIDND1="10750@x" ObjectIDZND0="g_2ac0e80@1" Pin0InfoVect0LinkObjId="g_2ac0e80_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2ac23c0_0" Pin1InfoVect1LinkObjId="SW-58407_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-481 4444,-499 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ac21d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-544 4445,-562 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_2ac0e80@0" ObjectIDZND0="g_2a0c4a0@0" Pin0InfoVect0LinkObjId="g_2a0c4a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ac0e80_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-544 4445,-562 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b25fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-222 4765,-222 4765,-206 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34367@x" ObjectIDND1="10742@x" ObjectIDZND0="g_2b266f0@0" Pin0InfoVect0LinkObjId="g_2b266f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_BJ.093Ld_0" Pin1InfoVect1LinkObjId="SW-57458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-222 4765,-222 4765,-206 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-240 4734,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="10742@0" ObjectIDZND0="g_2b266f0@0" ObjectIDZND1="34367@x" Pin0InfoVect0LinkObjId="g_2b266f0_0" Pin0InfoVect1LinkObjId="EC-CX_BJ.093Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57458_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-240 4734,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2b26490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-222 4734,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2b266f0@0" ObjectIDND1="10742@x" ObjectIDZND0="34367@0" Pin0InfoVect0LinkObjId="EC-CX_BJ.093Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2b266f0_0" Pin1InfoVect1LinkObjId="SW-57458_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-222 4734,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2adbe90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="4837,-394 4837,-137 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10704@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad9bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4837,-394 4837,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a16260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-226 4124,-226 4124,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="0@x" ObjectIDND1="34363@x" ObjectIDND2="10738@x" ObjectIDZND0="g_2a16450@0" Pin0InfoVect0LinkObjId="g_2a16450_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="EC-CX_BJ.081Ld_0" Pin1InfoVect2LinkObjId="SW-57454_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-226 4124,-226 4124,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a974a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4050,-741 4050,-716 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer2" ObjectIDND0="g_298d160@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a4c240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298d160_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4050,-741 4050,-716 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a97700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4049,-786 4049,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="busSection" ObjectIDND0="g_298d160@1" ObjectIDZND0="10705@0" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_298d160_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4049,-786 4049,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29dcb40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="3843,-394 3843,-137 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10706@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad8b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3843,-394 3843,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297d020">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="3765,-394 3765,-143 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10706@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad8b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3765,-394 3765,-143 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_297e0d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="3588,-394 3588,-140 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10706@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad8b30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3588,-394 3588,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3dcf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4132,-140 4093,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="34363@x" ObjectIDZND1="g_2a16450@0" ObjectIDZND2="10738@x" Pin0InfoVect0LinkObjId="EC-CX_BJ.081Ld_0" Pin0InfoVect1LinkObjId="g_2a16450_0" Pin0InfoVect2LinkObjId="SW-57454_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4132,-140 4093,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a3dee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4178,-141 4178,-128 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="0@1" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a4c240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4c240_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4178,-141 4178,-128 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a3f2d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4604,-107 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2aa59a0@0" ObjectIDND1="10744@x" ObjectIDZND0="34366@0" Pin0InfoVect0LinkObjId="EC-CX_BJ.092Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2aa59a0_0" Pin1InfoVect1LinkObjId="SW-57460_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4604,-107 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a40670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4635,-206 4635,-222 4604,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="switch" ObjectIDND0="g_2aa59a0@0" ObjectIDZND0="34366@x" ObjectIDZND1="10744@x" Pin0InfoVect0LinkObjId="EC-CX_BJ.092Ld_0" Pin0InfoVect1LinkObjId="SW-57460_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aa59a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4635,-206 4635,-222 4604,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a408a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4604,-240 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="switch" ObjectIDND0="34366@x" ObjectIDND1="g_2aa59a0@0" ObjectIDZND0="10744@0" Pin0InfoVect0LinkObjId="SW-57460_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_BJ.092Ld_0" Pin1InfoVect1LinkObjId="g_2aa59a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4604,-240 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a40ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-224 4474,-109 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_2af73c0@0" ObjectIDND1="10740@x" ObjectIDZND0="34365@0" Pin0InfoVect0LinkObjId="EC-CX_BJ.091Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2af73c0_0" Pin1InfoVect1LinkObjId="SW-57456_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-224 4474,-109 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad5c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-240 4474,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="load" EndDevType1="lightningRod" ObjectIDND0="10740@0" ObjectIDZND0="34365@x" ObjectIDZND1="g_2af73c0@0" Pin0InfoVect0LinkObjId="EC-CX_BJ.091Ld_0" Pin0InfoVect1LinkObjId="g_2af73c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57456_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-240 4474,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad5e60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-224 4505,-224 4505,-208 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34365@x" ObjectIDND1="10740@x" ObjectIDZND0="g_2af73c0@0" Pin0InfoVect0LinkObjId="g_2af73c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_BJ.091Ld_0" Pin1InfoVect1LinkObjId="SW-57456_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-224 4505,-224 4505,-208 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad60c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-225 3953,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="load" ObjectIDND0="g_29d9800@0" ObjectIDND1="10736@x" ObjectIDZND0="34364@0" Pin0InfoVect0LinkObjId="EC-CX_BJ.082Ld_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_29d9800_0" Pin1InfoVect1LinkObjId="SW-57452_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-225 3953,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad6bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-243 3953,-225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="10736@0" ObjectIDZND0="g_29d9800@0" ObjectIDZND1="34364@x" Pin0InfoVect0LinkObjId="g_29d9800_0" Pin0InfoVect1LinkObjId="EC-CX_BJ.082Ld_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57452_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-243 3953,-225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad6e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-225 3984,-225 3984,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="34364@x" ObjectIDND1="10736@x" ObjectIDZND0="g_29d9800@0" Pin0InfoVect0LinkObjId="g_29d9800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="EC-CX_BJ.082Ld_0" Pin1InfoVect1LinkObjId="SW-57452_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-225 3984,-225 3984,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad88d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-461 4444,-481 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="10750@1" ObjectIDZND0="g_2ac23c0@0" ObjectIDZND1="g_2ac0e80@0" Pin0InfoVect0LinkObjId="g_2ac23c0_0" Pin0InfoVect1LinkObjId="g_2ac0e80_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58407_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-461 4444,-481 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad8b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-374 3953,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10735@1" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad9250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57451_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-374 3953,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad8d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-321 3953,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10719@1" ObjectIDZND0="10735@0" Pin0InfoVect0LinkObjId="SW-57451_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57431_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-321 3953,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad8ff0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3953,-279 3953,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10736@1" ObjectIDZND0="10719@0" Pin0InfoVect0LinkObjId="SW-57431_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57452_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3953,-279 3953,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad9250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-374 4093,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10737@1" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad8b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57453_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-374 4093,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad94b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-321 4093,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10720@1" ObjectIDZND0="10737@0" Pin0InfoVect0LinkObjId="SW-57453_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57432_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-321 4093,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad9710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-275 4093,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10738@1" ObjectIDZND0="10720@0" Pin0InfoVect0LinkObjId="SW-57432_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57454_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-275 4093,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad9970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4229,-374 4229,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10748@1" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad8b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57464_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4229,-374 4229,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2ad9bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-374 4383,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10747@1" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2980760_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57463_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-374 4383,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2980500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-321 4474,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10723@1" ObjectIDZND0="10739@0" Pin0InfoVect0LinkObjId="SW-57466_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57433_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-321 4474,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2980760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-374 4474,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10739@1" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57466_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-374 4474,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29809c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4474,-276 4474,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10740@1" ObjectIDZND0="10723@0" Pin0InfoVect0LinkObjId="SW-57433_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57456_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4474,-276 4474,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2980c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-276 4604,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10744@1" ObjectIDZND0="10727@0" Pin0InfoVect0LinkObjId="SW-57435_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57460_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-276 4604,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2980e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-321 4604,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10727@1" ObjectIDZND0="10743@0" Pin0InfoVect0LinkObjId="SW-57459_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57435_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-321 4604,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29810e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-374 4604,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10743@1" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57459_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-374 4604,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-276 4734,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10742@1" ObjectIDZND0="10724@0" Pin0InfoVect0LinkObjId="SW-57434_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57458_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-276 4734,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29815a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-321 4734,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10724@1" ObjectIDZND0="10741@0" Pin0InfoVect0LinkObjId="SW-57457_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57434_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-321 4734,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4734,-374 4734,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10741@1" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57457_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4734,-374 4734,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981a60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-275 4932,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10746@1" ObjectIDZND0="10729@0" Pin0InfoVect0LinkObjId="SW-57436_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57462_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-275 4932,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-321 4932,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10729@1" ObjectIDZND0="10745@0" Pin0InfoVect0LinkObjId="SW-57461_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57436_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-321 4932,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2981f20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-374 4932,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10745@1" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57461_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-374 4932,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983710">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3810,-423 3810,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10751@0" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad8b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57467_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3810,-423 3810,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-425 4650,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10732@0" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57448_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-425 4650,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4444,-425 4444,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="10750@0" ObjectIDZND0="10704@0" Pin0InfoVect0LinkObjId="g_2ad9bd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-58407_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4444,-425 4444,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2983e30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4383,-336 4383,-312 4320,-312 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="10747@0" ObjectIDZND0="10730@0" Pin0InfoVect0LinkObjId="SW-57437_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4383,-336 4383,-312 4320,-312 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2984090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4293,-312 4229,-312 4229,-338 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="10730@1" ObjectIDZND0="10748@0" Pin0InfoVect0LinkObjId="SW-57464_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57437_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4293,-312 4229,-312 4229,-338 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29e65c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4062,-1004 4074,-1004 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_29842f0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a4c240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29842f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4062,-1004 4074,-1004 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aca0e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4933,-69 4933,-58 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="10757@0" ObjectIDZND0="g_2ac7200@0" Pin0InfoVect0LinkObjId="g_2ac7200_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4933,-69 4933,-58 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a0fd60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4940,-231 4932,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="10756@0" ObjectIDZND0="10746@x" ObjectIDZND1="g_2a08780@0" ObjectIDZND2="10769@x" Pin0InfoVect0LinkObjId="SW-57462_0" Pin0InfoVect1LinkObjId="g_2a08780_0" Pin0InfoVect2LinkObjId="CB-CX_BJ.CX_BJ_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57472_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4940,-231 4932,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a124f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="1" points="5018,-394 5018,-136 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="load" ObjectIDND0="10704@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2ad9bd0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5018,-394 5018,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a12750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4984,-231 4976,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_2aca340@0" ObjectIDZND0="10756@1" Pin0InfoVect0LinkObjId="SW-57472_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2aca340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4984,-231 4976,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a13460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-239 4932,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="capacitor" ObjectIDND0="10746@0" ObjectIDZND0="10756@x" ObjectIDZND1="g_2a08780@0" ObjectIDZND2="10769@x" Pin0InfoVect0LinkObjId="SW-57472_0" Pin0InfoVect1LinkObjId="g_2a08780_0" Pin0InfoVect2LinkObjId="CB-CX_BJ.CX_BJ_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57462_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-239 4932,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a144b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4932,-221 4932,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="10769@0" ObjectIDZND0="10746@x" ObjectIDZND1="10756@x" ObjectIDZND2="g_2a08780@0" Pin0InfoVect0LinkObjId="SW-57462_0" Pin0InfoVect1LinkObjId="SW-57472_0" Pin0InfoVect2LinkObjId="g_2a08780_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_BJ.CX_BJ_1C_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4932,-221 4932,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2afc850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4650,-526 4650,-570 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="10703@1" ObjectIDZND0="10725@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-57429_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4650,-526 4650,-570 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2afca40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4651,-647 4651,-676 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="10725@0" ObjectIDZND0="10721@0" Pin0InfoVect0LinkObjId="SW-57428_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2afc850_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4651,-647 4651,-676 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa1860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-226 4093,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="load" EndDevType0="switch" ObjectIDND0="g_2a16450@0" ObjectIDND1="0@x" ObjectIDND2="34363@x" ObjectIDZND0="10738@0" Pin0InfoVect0LinkObjId="SW-57454_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a16450_0" Pin1InfoVect1LinkObjId="g_2a4c240_0" Pin1InfoVect2LinkObjId="EC-CX_BJ.081Ld_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-226 4093,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-110 4093,-140 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="34363@0" ObjectIDZND0="0@x" ObjectIDZND1="g_2a16450@0" ObjectIDZND2="10738@x" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="g_2a16450_0" Pin0InfoVect2LinkObjId="SW-57454_0" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-CX_BJ.081Ld_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-110 4093,-140 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2aa2570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4093,-140 4093,-226 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="load" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="34363@x" ObjectIDZND0="g_2a16450@0" ObjectIDZND1="10738@x" Pin0InfoVect0LinkObjId="g_2a16450_0" Pin0InfoVect1LinkObjId="SW-57454_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="EC-CX_BJ.081Ld_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4093,-140 4093,-226 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2a37d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-986 4764,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29421@1" ObjectIDZND0="g_2a37f70@0" Pin0InfoVect0LinkObjId="g_2a37f70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193487_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-986 4764,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29629c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-924 4764,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29420@1" ObjectIDZND0="g_2962c20@0" Pin0InfoVect0LinkObjId="g_2962c20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193486_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-924 4764,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29636b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-823 4687,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="29417@0" ObjectIDZND0="10705@0" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193483_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-823 4687,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2963910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-986 4687,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="29421@0" ObjectIDZND0="0@x" ObjectIDZND1="g_295cee0@0" ObjectIDZND2="34579@1" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="g_295cee0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193487_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-986 4687,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2963b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-986 4687,-971 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="g_295cee0@0" ObjectIDND2="34579@1" ObjectIDZND0="29418@1" Pin0InfoVect0LinkObjId="SW-193484_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="g_295cee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-986 4687,-971 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2963dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-924 4687,-924 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29420@0" ObjectIDZND0="29416@x" ObjectIDZND1="29418@x" Pin0InfoVect0LinkObjId="SW-193482_0" Pin0InfoVect1LinkObjId="SW-193484_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193486_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-924 4687,-924 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2964030">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-935 4687,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29418@0" ObjectIDZND0="29416@x" ObjectIDZND1="29420@x" Pin0InfoVect0LinkObjId="SW-193482_0" Pin0InfoVect1LinkObjId="SW-193486_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193484_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-935 4687,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2964290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-925 4687,-912 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="29418@x" ObjectIDND1="29420@x" ObjectIDZND0="29416@1" Pin0InfoVect0LinkObjId="SW-193482_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193484_0" Pin1InfoVect1LinkObjId="SW-193486_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-925 4687,-912 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29de3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4745,-872 4764,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="29419@1" ObjectIDZND0="g_29de610@0" Pin0InfoVect0LinkObjId="g_29de610_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193485_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4745,-872 4764,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29df0a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4709,-872 4687,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="29419@0" ObjectIDZND0="29416@x" ObjectIDZND1="29417@x" Pin0InfoVect0LinkObjId="SW-193482_0" Pin0InfoVect1LinkObjId="SW-193483_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193485_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4709,-872 4687,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29df300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-885 4687,-872 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="29416@0" ObjectIDZND0="29417@x" ObjectIDZND1="29419@x" Pin0InfoVect0LinkObjId="SW-193483_0" Pin0InfoVect1LinkObjId="SW-193485_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-193482_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-885 4687,-872 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29df560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-872 4687,-859 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="29416@x" ObjectIDND1="29419@x" ObjectIDZND0="29417@1" Pin0InfoVect0LinkObjId="SW-193483_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-193482_0" Pin1InfoVect1LinkObjId="SW-193485_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-872 4687,-859 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29df7c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1043 4687,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_295cee0@0" ObjectIDZND0="0@x" ObjectIDZND1="29418@x" ObjectIDZND2="29421@x" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="SW-193484_0" Pin0InfoVect2LinkObjId="SW-193487_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_295cee0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1043 4687,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29dfa20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4655,-1006 4687,-1006 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="29418@x" ObjectIDZND1="29421@x" ObjectIDZND2="g_295cee0@0" Pin0InfoVect0LinkObjId="SW-193484_0" Pin0InfoVect1LinkObjId="SW-193487_0" Pin0InfoVect2LinkObjId="g_295cee0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4655,-1006 4687,-1006 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29dfc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-1006 4687,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@x" ObjectIDND1="g_295cee0@0" ObjectIDND2="34579@1" ObjectIDZND0="29418@x" ObjectIDZND1="29421@x" Pin0InfoVect0LinkObjId="SW-193484_0" Pin0InfoVect1LinkObjId="SW-193487_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="g_295cee0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-1006 4687,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29dfee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-1075 4687,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="powerLine" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="34579@1" ObjectIDZND0="g_295cee0@0" ObjectIDZND1="0@x" ObjectIDZND2="29418@x" Pin0InfoVect0LinkObjId="g_295cee0_0" Pin0InfoVect1LinkObjId="g_2a4c240_0" Pin0InfoVect2LinkObjId="SW-193484_0" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-1075 4687,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29e0140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-1006 4687,-1043 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="0@x" ObjectIDND1="29418@x" ObjectIDND2="29421@x" ObjectIDZND0="g_295cee0@0" ObjectIDZND1="34579@1" Pin0InfoVect0LinkObjId="g_295cee0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2a4c240_0" Pin1InfoVect1LinkObjId="SW-193484_0" Pin1InfoVect2LinkObjId="SW-193487_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-1006 4687,-1043 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_29e11d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4598,-1005 4610,-1005 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="voltageTransformer" EndDevType0="switch" ObjectIDND0="g_29e07a0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="g_2a4c240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29e07a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4598,-1005 4610,-1005 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_2a08370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4687,-232 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4687,-232 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2a08560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-209 4891,-229 4890,-231 4932,-231 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2a08780@0" ObjectIDZND0="10746@x" ObjectIDZND1="10756@x" ObjectIDZND2="10769@x" Pin0InfoVect0LinkObjId="SW-57462_0" Pin0InfoVect1LinkObjId="SW-57472_0" Pin0InfoVect2LinkObjId="CB-CX_BJ.CX_BJ_1C_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2a08780_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-209 4891,-229 4890,-231 4932,-231 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29fa790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4393,-911 4393,-981 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="10749@x" ObjectIDND1="10758@x" ObjectIDND2="g_2a4b430@0" ObjectIDZND0="g_2a4c240@0" Pin0InfoVect0LinkObjId="g_2a4c240_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-57465_0" Pin1InfoVect1LinkObjId="SW-57474_0" Pin1InfoVect2LinkObjId="g_2a4b430_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4393,-911 4393,-981 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29ef980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-471 4191,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43913@1" ObjectIDZND0="43912@0" Pin0InfoVect0LinkObjId="SW-273834_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273835_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-471 4191,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f2110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4250,-732 4269,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43911@1" ObjectIDZND0="g_29f2370@0" Pin0InfoVect0LinkObjId="g_29f2370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273816_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4250,-732 4269,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29f3670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-536 4191,-580 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="43912@1" ObjectIDZND0="43918@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273834_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-536 4191,-580 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29f3860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-657 4192,-687 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="breaker" ObjectIDND0="43918@0" ObjectIDZND0="43909@0" Pin0InfoVect0LinkObjId="SW-273814_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_29f3670_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-657 4192,-687 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d6ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-272 3673,-291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="43916@1" ObjectIDZND0="43914@0" Pin0InfoVect0LinkObjId="SW-273918_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273920_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-272 3673,-291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d6e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-318 3673,-335 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="43914@1" ObjectIDZND0="43915@0" Pin0InfoVect0LinkObjId="SW-273919_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273918_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-318 3673,-335 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29d7060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3681,-228 3673,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43917@0" ObjectIDZND0="43916@x" ObjectIDZND1="43929@x" ObjectIDZND2="g_2970570@0" Pin0InfoVect0LinkObjId="SW-273920_0" Pin0InfoVect1LinkObjId="CB-CX_BJ.CX_BJ_2Cb_0" Pin0InfoVect2LinkObjId="g_2970570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273921_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3681,-228 3673,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296f870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-236 3673,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="capacitor" EndDevType2="lightningRod" ObjectIDND0="43916@0" ObjectIDZND0="43917@x" ObjectIDZND1="43929@x" ObjectIDZND2="g_2970570@0" Pin0InfoVect0LinkObjId="SW-273921_0" Pin0InfoVect1LinkObjId="CB-CX_BJ.CX_BJ_2Cb_0" Pin0InfoVect2LinkObjId="g_2970570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273920_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-236 3673,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_296fad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-218 3673,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="capacitor" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="43929@0" ObjectIDZND0="43917@x" ObjectIDZND1="43916@x" ObjectIDZND2="g_2970570@0" Pin0InfoVect0LinkObjId="SW-273921_0" Pin0InfoVect1LinkObjId="SW-273920_0" Pin0InfoVect2LinkObjId="g_2970570_0" Pin0Num="1" Pin1InfoVect0LinkObjId="CB-CX_BJ.CX_BJ_2Cb_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-218 3673,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2974e50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3632,-206 3632,-226 3631,-228 3673,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="capacitor" ObjectIDND0="g_2970570@0" ObjectIDZND0="43917@x" ObjectIDZND1="43916@x" ObjectIDZND2="43929@x" Pin0InfoVect0LinkObjId="SW-273921_0" Pin0InfoVect1LinkObjId="SW-273920_0" Pin0InfoVect2LinkObjId="CB-CX_BJ.CX_BJ_2Cb_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2970570_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3632,-206 3632,-226 3631,-228 3673,-228 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29750c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3673,-371 3673,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43915@1" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad8b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273919_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3673,-371 3673,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_29758f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-435 4191,-394 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43913@0" ObjectIDZND0="10706@0" Pin0InfoVect0LinkObjId="g_2ad8b30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273835_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-435 4191,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2977010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-789 4192,-805 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="43910@1" ObjectIDZND0="10705@0" Pin0InfoVect0LinkObjId="g_2aa6370_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273815_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-789 4192,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29776f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4214,-732 4192,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="43911@0" ObjectIDZND0="43909@x" ObjectIDZND1="43910@x" Pin0InfoVect0LinkObjId="SW-273814_0" Pin0InfoVect1LinkObjId="SW-273815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273816_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4214,-732 4192,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2978140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-714 4192,-732 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="43909@1" ObjectIDZND0="43911@x" ObjectIDZND1="43910@x" Pin0InfoVect0LinkObjId="SW-273816_0" Pin0InfoVect1LinkObjId="SW-273815_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273814_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-714 4192,-732 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_29783a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4192,-732 4192,-753 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="43911@x" ObjectIDND1="43909@x" ObjectIDZND0="43910@0" Pin0InfoVect0LinkObjId="SW-273815_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-273816_0" Pin1InfoVect1LinkObjId="SW-273814_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4192,-732 4192,-753 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_2978600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3717,-228 3725,-228 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="43917@1" ObjectIDZND0="g_2973bc0@0" Pin0InfoVect0LinkObjId="g_2973bc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-273921_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3717,-228 3725,-228 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectPoint_Layer"/><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95713" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -1166.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95713" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10722"/>
     <cge:Term_Ref ObjectID="14878"/>
    <cge:TPSR_Ref TObjectID="10722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95714" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -1166.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10722"/>
     <cge:Term_Ref ObjectID="14878"/>
    <cge:TPSR_Ref TObjectID="10722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95710" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -1166.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10722"/>
     <cge:Term_Ref ObjectID="14878"/>
    <cge:TPSR_Ref TObjectID="10722"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-95751" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -501.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10704"/>
     <cge:Term_Ref ObjectID="14863"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-95752" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -501.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10704"/>
     <cge:Term_Ref ObjectID="14863"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-95753" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -501.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10704"/>
     <cge:Term_Ref ObjectID="14863"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-95757" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -501.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95757" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10704"/>
     <cge:Term_Ref ObjectID="14863"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-95754" prefix="" rightAlign="1">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5016.000000 -501.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10704"/>
     <cge:Term_Ref ObjectID="14863"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95707" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -535.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95707" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10703"/>
     <cge:Term_Ref ObjectID="13834"/>
    <cge:TPSR_Ref TObjectID="10703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95708" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -535.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95708" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10703"/>
     <cge:Term_Ref ObjectID="13834"/>
    <cge:TPSR_Ref TObjectID="10703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95704" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4764.000000 -535.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10703"/>
     <cge:Term_Ref ObjectID="13834"/>
    <cge:TPSR_Ref TObjectID="10703"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95717" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -69.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95717" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10719"/>
     <cge:Term_Ref ObjectID="14872"/>
    <cge:TPSR_Ref TObjectID="10719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95718" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -69.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95718" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10719"/>
     <cge:Term_Ref ObjectID="14872"/>
    <cge:TPSR_Ref TObjectID="10719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95716" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3959.000000 -69.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95716" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10719"/>
     <cge:Term_Ref ObjectID="14872"/>
    <cge:TPSR_Ref TObjectID="10719"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95721" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -70.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10720"/>
     <cge:Term_Ref ObjectID="14874"/>
    <cge:TPSR_Ref TObjectID="10720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95722" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -70.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10720"/>
     <cge:Term_Ref ObjectID="14874"/>
    <cge:TPSR_Ref TObjectID="10720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95720" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4098.000000 -70.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10720"/>
     <cge:Term_Ref ObjectID="14874"/>
    <cge:TPSR_Ref TObjectID="10720"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95725" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -76.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10723"/>
     <cge:Term_Ref ObjectID="14880"/>
    <cge:TPSR_Ref TObjectID="10723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95726" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -76.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95726" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10723"/>
     <cge:Term_Ref ObjectID="14880"/>
    <cge:TPSR_Ref TObjectID="10723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95724" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4476.000000 -76.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95724" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10723"/>
     <cge:Term_Ref ObjectID="14880"/>
    <cge:TPSR_Ref TObjectID="10723"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95733" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4609.000000 -74.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10727"/>
     <cge:Term_Ref ObjectID="14888"/>
    <cge:TPSR_Ref TObjectID="10727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95734" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4609.000000 -74.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10727"/>
     <cge:Term_Ref ObjectID="14888"/>
    <cge:TPSR_Ref TObjectID="10727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95732" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4609.000000 -74.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10727"/>
     <cge:Term_Ref ObjectID="14888"/>
    <cge:TPSR_Ref TObjectID="10727"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95729" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -74.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95729" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10724"/>
     <cge:Term_Ref ObjectID="14882"/>
    <cge:TPSR_Ref TObjectID="10724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95730" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -74.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95730" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10724"/>
     <cge:Term_Ref ObjectID="14882"/>
    <cge:TPSR_Ref TObjectID="10724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95728" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4747.000000 -74.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95728" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10724"/>
     <cge:Term_Ref ObjectID="14882"/>
    <cge:TPSR_Ref TObjectID="10724"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95737" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95737" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10729"/>
     <cge:Term_Ref ObjectID="14890"/>
    <cge:TPSR_Ref TObjectID="10729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95736" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10729"/>
     <cge:Term_Ref ObjectID="14890"/>
    <cge:TPSR_Ref TObjectID="10729"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95739" prefix="Ia   " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4280.000000 -276.000000) translate(0,12)">Ia    0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95739" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10730"/>
     <cge:Term_Ref ObjectID="14892"/>
    <cge:TPSR_Ref TObjectID="10730"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-95759" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -498.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95759" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10706"/>
     <cge:Term_Ref ObjectID="14865"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-95760" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -498.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95760" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10706"/>
     <cge:Term_Ref ObjectID="14865"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-95761" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -498.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10706"/>
     <cge:Term_Ref ObjectID="14865"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-95765" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -498.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10706"/>
     <cge:Term_Ref ObjectID="14865"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-95762" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3666.000000 -498.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10706"/>
     <cge:Term_Ref ObjectID="14865"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-95743" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95743" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-95744" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95744" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-95745" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95745" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-95749" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-95746" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,72)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95746" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-95750" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4960.000000 -876.000000) translate(0,87)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10705"/>
     <cge:Term_Ref ObjectID="14864"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-95768" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -623.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95768" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10725"/>
     <cge:Term_Ref ObjectID="14887"/>
    <cge:TPSR_Ref TObjectID="10725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-95767" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4790.000000 -623.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10725"/>
     <cge:Term_Ref ObjectID="14887"/>
    <cge:TPSR_Ref TObjectID="10725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-95701" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -706.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10721"/>
     <cge:Term_Ref ObjectID="14876"/>
    <cge:TPSR_Ref TObjectID="10721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-95702" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -706.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10721"/>
     <cge:Term_Ref ObjectID="14876"/>
    <cge:TPSR_Ref TObjectID="10721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-95700" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -706.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10721"/>
     <cge:Term_Ref ObjectID="14876"/>
    <cge:TPSR_Ref TObjectID="10721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-95703" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4776.000000 -706.000000) translate(0,57)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="95703" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="10721"/>
     <cge:Term_Ref ObjectID="14876"/>
    <cge:TPSR_Ref TObjectID="10721"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-193479" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -1166.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193479" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29416"/>
     <cge:Term_Ref ObjectID="41892"/>
    <cge:TPSR_Ref TObjectID="29416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-193480" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -1166.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193480" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29416"/>
     <cge:Term_Ref ObjectID="41892"/>
    <cge:TPSR_Ref TObjectID="29416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-193476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4714.000000 -1166.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="193476" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="29416"/>
     <cge:Term_Ref ObjectID="41892"/>
    <cge:TPSR_Ref TObjectID="29416"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-273973" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -57.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273973" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43914"/>
     <cge:Term_Ref ObjectID="20295"/>
    <cge:TPSR_Ref TObjectID="43914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-273974" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3654.000000 -57.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273974" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43914"/>
     <cge:Term_Ref ObjectID="20295"/>
    <cge:TPSR_Ref TObjectID="43914"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="2" id="ME-273971" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -666.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273971" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43918"/>
     <cge:Term_Ref ObjectID="20303"/>
    <cge:TPSR_Ref TObjectID="43918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-273972" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4342.000000 -666.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273972" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43918"/>
     <cge:Term_Ref ObjectID="20303"/>
    <cge:TPSR_Ref TObjectID="43918"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-273959" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -733.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273959" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43909"/>
     <cge:Term_Ref ObjectID="20285"/>
    <cge:TPSR_Ref TObjectID="43909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-273960" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -733.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273960" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43909"/>
     <cge:Term_Ref ObjectID="20285"/>
    <cge:TPSR_Ref TObjectID="43909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-273962" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4134.000000 -733.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273962" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43909"/>
     <cge:Term_Ref ObjectID="20285"/>
    <cge:TPSR_Ref TObjectID="43909"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-273965" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -549.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273965" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43912"/>
     <cge:Term_Ref ObjectID="20291"/>
    <cge:TPSR_Ref TObjectID="43912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-273966" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -549.000000) translate(0,27)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273966" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43912"/>
     <cge:Term_Ref ObjectID="20291"/>
    <cge:TPSR_Ref TObjectID="43912"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-273968" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4123.000000 -549.000000) translate(0,42)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="273968" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="43912"/>
     <cge:Term_Ref ObjectID="20291"/>
    <cge:TPSR_Ref TObjectID="43912"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="139" x="3240" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3192" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4109" y="-905"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4109" y="-905"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="3962" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="3962" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4102" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4102" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4483" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4483" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4613" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4613" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4743" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4743" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="4941" y="-315"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="4941" y="-315"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="25" x="4294" y="-336"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="25" x="4294" y="-336"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="26" x="4642" y="-907"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="26" x="4642" y="-907"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3458" y="-1150"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3458" y="-1150"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3458" y="-1185"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3458" y="-1185"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="60" x="3142" y="-773"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="60" x="3142" y="-773"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="52" x="4559" y="-622"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="52" x="4559" y="-622"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <polygon fill="rgb(255,255,255)" points="3376,-1067 3373,-1070 3373,-1017 3376,-1020 3376,-1067" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(255,255,255)" points="3376,-1067 3373,-1070 3424,-1070 3421,-1067 3376,-1067" stroke="rgb(255,255,255)"/>
     <polygon fill="rgb(127,127,127)" points="3376,-1020 3373,-1017 3424,-1017 3421,-1020 3376,-1020" stroke="rgb(127,127,127)"/>
     <polygon fill="rgb(127,127,127)" points="3421,-1067 3424,-1070 3424,-1017 3421,-1020 3421,-1067" stroke="rgb(127,127,127)"/>
     <rect fill="rgb(255,255,255)" height="47" stroke="rgb(255,255,255)" width="45" x="3376" y="-1067"/>
     <rect fill="none" height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3376" y="-1067"/>
    </a>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="53" x="4217" y="-629"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="53" x="4217" y="-629"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3681" y="-314"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3681" y="-314"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="16" qtmmishow="hidden" width="64" x="3138" y="-735"/>
    </a>
   <metadata/><rect fill="white" height="16" opacity="0" stroke="white" transform="" width="64" x="3138" y="-735"/></g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="139" x="3240" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3192" y="-1194"/></g>
   <g href="35kV八角变八角变10kV中八T线381断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4109" y="-905"/></g>
   <g href="35kV八角变八角变10kV哨房线082断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="3962" y="-315"/></g>
   <g href="35kV八角变八角变10kV八五三线081断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4102" y="-315"/></g>
   <g href="35kV八角变八角变10kV八角集镇线091断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4483" y="-315"/></g>
   <g href="35kV八角变八角变10kV洒洲线092断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4613" y="-315"/></g>
   <g href="35kV八角变10kV扎郎线093断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4743" y="-315"/></g>
   <g href="35kV八角变八角变10kV1号电容器组095断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="4941" y="-315"/></g>
   <g href="35kV八角变八角变10kV母联012断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="25" x="4294" y="-336"/></g>
   <g href="35kV八角变35kV八马线382断路器间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="26" x="4642" y="-907"/></g>
   <g href="cx_配调_配网接线图35_楚雄.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3458" y="-1150"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3458" y="-1185"/></g>
   <g href="35kV八角变GG虚设备间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="60" x="3142" y="-773"/></g>
   <g href="35kV八角变1号主变间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="52" x="4559" y="-622"/></g>
   <g href="AVC八角站.svg" style="fill-opacity:0"><rect height="47" qtmmishow="hidden" stroke="rgb(0,0,0)" width="45" x="3376" y="-1067"/></g>
   <g href="35kV八角变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="53" x="4217" y="-629"/></g>
   <g href="35kV八角变CX_BJ_085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3681" y="-314"/></g>
   <g href="35kV八角变隔刀开关远方遥控清单.svg" style="fill-opacity:0"><rect height="16" qtmmishow="hidden" width="64" x="3138" y="-735"/></g>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="120" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3119" y="-1198"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="480" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-1078"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="600" stroke="rgb(21,40,56)" stroke-width="1" width="360" x="3118" y="-598"/>
   <rect DF8003:Layer="PUBLIC" fill="none" height="23" stroke="rgb(238,238,0)" stroke-width="1" width="11" x="4387" y="-963"/>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_BJ.CX_BJ_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3869,-805 4841,-805 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10705" ObjectName="BS-CX_BJ.CX_BJ_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="10705"/></metadata>
   <polyline fill="none" opacity="0" points="3869,-805 4841,-805 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BJ.CX_BJ_9ⅠM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4345,-394 5058,-394 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10704" ObjectName="BS-CX_BJ.CX_BJ_9ⅠM"/>
    <cge:TPSR_Ref TObjectID="10704"/></metadata>
   <polyline fill="none" opacity="0" points="4345,-394 5058,-394 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_BJ.CX_BJ_9ⅡM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3580,-394 4274,-394 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="10706" ObjectName="BS-CX_BJ.CX_BJ_9ⅡM"/>
    <cge:TPSR_Ref TObjectID="10706"/></metadata>
   <polyline fill="none" opacity="0" points="3580,-394 4274,-394 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4082.000000 -721.000000)" xlink:href="#transformer2:shape55_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 4082.000000 -721.000000)" xlink:href="#transformer2:shape55_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.887500 4167.000000 -62.000000)" xlink:href="#transformer2:shape95_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(0.869565 -0.000000 0.000000 -0.887500 4167.000000 -62.000000)" xlink:href="#transformer2:shape95_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_BJ.CX_BJ_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="14886"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.892157 4617.000000 -565.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.892157 4617.000000 -565.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="10725" ObjectName="TF-CX_BJ.CX_BJ_1T"/>
    <cge:TPSR_Ref TObjectID="10725"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_BJ.CX_BJ_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="20305"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(0.909091 -0.000000 0.000000 -0.892157 4158.000000 -575.000000)" xlink:href="#transformer2:shape21_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(0.909091 -0.000000 0.000000 -0.892157 4158.000000 -575.000000)" xlink:href="#transformer2:shape21_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="43918" ObjectName="TF-CX_BJ.CX_BJ_2T"/>
    <cge:TPSR_Ref TObjectID="43918"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3228.500000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-57364" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3476.000000 -1077.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10686" ObjectName="DYN-CX_BJ"/>
     <cge:Meas_Ref ObjectId="57364"/>
    </metadata>
   </g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a42480" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4123.000000 1166.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a42630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4112.000000 1151.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a428e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4137.000000 1136.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a42a90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3900.000000 72.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a42c90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3889.000000 57.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a42e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3914.000000 42.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a431d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4421.000000 76.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a43380" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 61.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a43530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 46.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a438c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4553.000000 73.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a43a70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4542.000000 58.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a43c20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4567.000000 43.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a43fb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4694.000000 74.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a44160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4683.000000 59.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a44310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 44.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3e2b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 72.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3e420" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4030.000000 57.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a3e5d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4055.000000 42.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afacc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.500000 623.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afbb90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.500000 608.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afcd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4695.000000 520.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd0a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4720.000000 505.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd2e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4706.000000 535.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afd980" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4977.000000 42.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2afdbf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4952.000000 57.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a706b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4944.000000 459.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a70d10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 475.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a715f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 488.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a71b50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4929.000000 444.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9e280" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 506.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9e5b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3590.000000 455.000000) translate(0,12)">U0（V）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9e820" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 471.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9ea60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 484.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9eca0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3575.000000 440.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9eee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3583.000000 502.000000) translate(0,12)">Ua（kV）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9f210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 850.000000) translate(0,12)">Uc（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9f490" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 863.000000) translate(0,12)">Ub（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9f6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4869.000000 819.000000) translate(0,12)">Uab（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9f910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4877.000000 881.000000) translate(0,12)">Ua（kV）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a9fb50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 803.000000) translate(0,12)">F（Hz）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa05b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 834.000000) translate(0,12)">U0（V）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa3360" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4708.000000 692.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa3900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4733.000000 677.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa3b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 661.000000) translate(0,12)">Cos:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2aa4780" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4719.000000 707.000000) translate(0,12)">P(MW):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29882d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4657.000000 1166.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2988590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4646.000000 1151.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29887d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4671.000000 1136.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_296fe20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 43.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2970330" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3581.000000 58.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979570" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.500000 668.000000) translate(0,12)">档位（档）：</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29797d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4243.500000 653.000000) translate(0,12)">油温（℃）：</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979ea0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4067.000000 549.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297a170" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4056.000000 534.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297a3b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4081.000000 519.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <g DF8003:Layer="PUBLIC">
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297a7d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4082.000000 733.000000) translate(0,12)">P(MW):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297aa90" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4071.000000 718.000000) translate(0,12)">Q(MVar):</text>
     <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_297acd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4096.000000 703.000000) translate(0,12)">Ia(A):</text>
    </g>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_297bb10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4278.000000 628.000000) translate(0,12)">程</text>
    <circle DF8003:Layer="PUBLIC" cx="4285" cy="621" fill="none" fillStyle="0" r="10.5" stroke="rgb(255,0,0)" stroke-width="0.963841"/>
   <metadata/></g>
  </g><g id="Capacitor_Layer">
   <g DF8003:Layer="PUBLIC" id="CB-CX_BJ.CX_BJ_1C">
    <use class="BV-10KV" transform="matrix(-0.898305 -0.000000 0.000000 -0.830882 4965.964010 -112.703256)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10769" ObjectName="CB-CX_BJ.CX_BJ_1C"/>
    <cge:TPSR_Ref TObjectID="10769"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="CB-CX_BJ.CX_BJ_2Cb">
    <use class="BV-10KV" transform="matrix(-0.898305 -0.000000 0.000000 -0.830882 3706.964010 -109.703256)" xlink:href="#capacitor:shape10"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43929" ObjectName="CB-CX_BJ.CX_BJ_2Cb"/>
    <cge:TPSR_Ref TObjectID="43929"/></metadata>
   </g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4837" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="5018" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="3843" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="3765" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4393" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4049" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4150" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4604" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4474" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4650" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4651" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4444" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="4229" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4383" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4734" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10704" cx="4932" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="4093" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="3953" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4687" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="3810" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="3673" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10706" cx="4191" cy="-394" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="10705" cx="4192" cy="-805" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-57450">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -817.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10734" ObjectName="SW-CX_BJ.CX_BJ_3811SW"/>
     <cge:Meas_Ref ObjectId="57450"/>
    <cge:TPSR_Ref TObjectID="10734"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57459">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10743" ObjectName="SW-CX_BJ.CX_BJ_0921SW"/>
     <cge:Meas_Ref ObjectId="57459"/>
    <cge:TPSR_Ref TObjectID="10743"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57460">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10744" ObjectName="SW-CX_BJ.CX_BJ_0926SW"/>
     <cge:Meas_Ref ObjectId="57460"/>
    <cge:TPSR_Ref TObjectID="10744"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57466">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10739" ObjectName="SW-CX_BJ.CX_BJ_0911SW"/>
     <cge:Meas_Ref ObjectId="57466"/>
    <cge:TPSR_Ref TObjectID="10739"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57456">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4465.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10740" ObjectName="SW-CX_BJ.CX_BJ_0916SW"/>
     <cge:Meas_Ref ObjectId="57456"/>
    <cge:TPSR_Ref TObjectID="10740"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57467">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3801.000000 -418.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10751" ObjectName="SW-CX_BJ.CX_BJ_0902SW"/>
     <cge:Meas_Ref ObjectId="57467"/>
    <cge:TPSR_Ref TObjectID="10751"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57469">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 -980.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10753" ObjectName="SW-CX_BJ.CX_BJ_38167SW"/>
     <cge:Meas_Ref ObjectId="57469"/>
    <cge:TPSR_Ref TObjectID="10753"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57448">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4641.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10732" ObjectName="SW-CX_BJ.CX_BJ_0011SW"/>
     <cge:Meas_Ref ObjectId="57448"/>
    <cge:TPSR_Ref TObjectID="10732"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57447">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4642.000000 -745.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10731" ObjectName="SW-CX_BJ.CX_BJ_3011SW"/>
     <cge:Meas_Ref ObjectId="57447"/>
    <cge:TPSR_Ref TObjectID="10731"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57449">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4142.000000 -929.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10733" ObjectName="SW-CX_BJ.CX_BJ_3816SW"/>
     <cge:Meas_Ref ObjectId="57449"/>
    <cge:TPSR_Ref TObjectID="10733"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57470">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 -918.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10754" ObjectName="SW-CX_BJ.CX_BJ_38160SW"/>
     <cge:Meas_Ref ObjectId="57470"/>
    <cge:TPSR_Ref TObjectID="10754"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57471">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4168.000000 -866.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10755" ObjectName="SW-CX_BJ.CX_BJ_38117SW"/>
     <cge:Meas_Ref ObjectId="57471"/>
    <cge:TPSR_Ref TObjectID="10755"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57475">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -825.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10759" ObjectName="SW-CX_BJ.CX_BJ_39010SW"/>
     <cge:Meas_Ref ObjectId="57475"/>
    <cge:TPSR_Ref TObjectID="10759"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57465">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4384.000000 -853.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10749" ObjectName="SW-CX_BJ.CX_BJ_3901SW"/>
     <cge:Meas_Ref ObjectId="57465"/>
    <cge:TPSR_Ref TObjectID="10749"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57474">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4410.000000 -908.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10758" ObjectName="SW-CX_BJ.CX_BJ_39017SW"/>
     <cge:Meas_Ref ObjectId="57474"/>
    <cge:TPSR_Ref TObjectID="10758"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57468">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4668.000000 -717.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10752" ObjectName="SW-CX_BJ.CX_BJ_30117SW"/>
     <cge:Meas_Ref ObjectId="57468"/>
    <cge:TPSR_Ref TObjectID="10752"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-58407">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.000000 -420.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10750" ObjectName="SW-CX_BJ.CX_BJ_0901SW"/>
     <cge:Meas_Ref ObjectId="58407"/>
    <cge:TPSR_Ref TObjectID="10750"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57464">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4220.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10748" ObjectName="SW-CX_BJ.CX_BJ_0122SW"/>
     <cge:Meas_Ref ObjectId="57464"/>
    <cge:TPSR_Ref TObjectID="10748"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4374.000000 -331.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10747" ObjectName="SW-CX_BJ.CX_BJ_0121SW"/>
     <cge:Meas_Ref ObjectId="57463"/>
    <cge:TPSR_Ref TObjectID="10747"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57458">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -235.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10742" ObjectName="SW-CX_BJ.CX_BJ_0936SW"/>
     <cge:Meas_Ref ObjectId="57458"/>
    <cge:TPSR_Ref TObjectID="10742"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57457">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4725.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10741" ObjectName="SW-CX_BJ.CX_BJ_0931SW"/>
     <cge:Meas_Ref ObjectId="57457"/>
    <cge:TPSR_Ref TObjectID="10741"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57462">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10746" ObjectName="SW-CX_BJ.CX_BJ_0956SW"/>
     <cge:Meas_Ref ObjectId="57462"/>
    <cge:TPSR_Ref TObjectID="10746"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57461">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4923.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10745" ObjectName="SW-CX_BJ.CX_BJ_0951SW"/>
     <cge:Meas_Ref ObjectId="57461"/>
    <cge:TPSR_Ref TObjectID="10745"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57454">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10738" ObjectName="SW-CX_BJ.CX_BJ_0816SW"/>
     <cge:Meas_Ref ObjectId="57454"/>
    <cge:TPSR_Ref TObjectID="10738"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57453">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4084.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10737" ObjectName="SW-CX_BJ.CX_BJ_0812SW"/>
     <cge:Meas_Ref ObjectId="57453"/>
    <cge:TPSR_Ref TObjectID="10737"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57452">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -238.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10736" ObjectName="SW-CX_BJ.CX_BJ_0826SW"/>
     <cge:Meas_Ref ObjectId="57452"/>
    <cge:TPSR_Ref TObjectID="10736"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57451">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3944.000000 -333.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10735" ObjectName="SW-CX_BJ.CX_BJ_0822SW"/>
     <cge:Meas_Ref ObjectId="57451"/>
    <cge:TPSR_Ref TObjectID="10735"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4124.000000 -1010.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 -1.000000 1.000000 0.000000 4127.000000 -135.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4924.000000 -64.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10757" ObjectName="SW-CX_BJ.CX_BJ_09500SW"/>
     <cge:Meas_Ref ObjectId="57473"/>
    <cge:TPSR_Ref TObjectID="10757"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-57472">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 -226.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="10756" ObjectName="SW-CX_BJ.CX_BJ_09567SW"/>
     <cge:Meas_Ref ObjectId="57472"/>
    <cge:TPSR_Ref TObjectID="10756"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193483">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 -818.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29417" ObjectName="SW-CX_BJ.CX_BJ_3821SW"/>
     <cge:Meas_Ref ObjectId="193483"/>
    <cge:TPSR_Ref TObjectID="29417"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193487">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -981.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29421" ObjectName="SW-CX_BJ.CX_BJ_38267SW"/>
     <cge:Meas_Ref ObjectId="193487"/>
    <cge:TPSR_Ref TObjectID="29421"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193484">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4678.000000 -930.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29418" ObjectName="SW-CX_BJ.CX_BJ_3826SW"/>
     <cge:Meas_Ref ObjectId="193484"/>
    <cge:TPSR_Ref TObjectID="29418"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193486">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -919.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29420" ObjectName="SW-CX_BJ.CX_BJ_38260SW"/>
     <cge:Meas_Ref ObjectId="193486"/>
    <cge:TPSR_Ref TObjectID="29420"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-193485">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4704.000000 -867.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="29419" ObjectName="SW-CX_BJ.CX_BJ_38217SW"/>
     <cge:Meas_Ref ObjectId="193485"/>
    <cge:TPSR_Ref TObjectID="29419"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(0.000000 1.000000 -1.000000 0.000000 4660.000000 -1011.000000)" xlink:href="#switch2:shape19_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273835">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4182.000000 -430.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43913" ObjectName="SW-CX_BJ.CX_BJ_0021SW"/>
     <cge:Meas_Ref ObjectId="273835"/>
    <cge:TPSR_Ref TObjectID="43913"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273816">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4209.000000 -727.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43911" ObjectName="SW-CX_BJ.CX_BJ_30217SW"/>
     <cge:Meas_Ref ObjectId="273816"/>
    <cge:TPSR_Ref TObjectID="43911"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273815">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4183.125000 -748.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43910" ObjectName="SW-CX_BJ.CX_BJ_3021SW"/>
     <cge:Meas_Ref ObjectId="273815"/>
    <cge:TPSR_Ref TObjectID="43910"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273920">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43916" ObjectName="SW-CX_BJ.CX_BJ_0856SW"/>
     <cge:Meas_Ref ObjectId="273920"/>
    <cge:TPSR_Ref TObjectID="43916"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273919">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3664.000000 -330.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43915" ObjectName="SW-CX_BJ.CX_BJ_0852SW"/>
     <cge:Meas_Ref ObjectId="273919"/>
    <cge:TPSR_Ref TObjectID="43915"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-273921">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3676.000000 -223.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="43917" ObjectName="SW-CX_BJ.CX_BJ_08567SW"/>
     <cge:Meas_Ref ObjectId="273921"/>
    <cge:TPSR_Ref TObjectID="43917"/></metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2738570" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4181.000000 -1110.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2738570" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4181.000000 -1110.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2738570" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4181.000000 -1110.000000) translate(0,51)">杜</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2738570" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4181.000000 -1110.000000) translate(0,69)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2738570" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4181.000000 -1110.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a389b0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3709.000000 -626.000000) translate(0,15)">10kVⅡ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af5cb0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4449.000000 -204.000000) translate(0,15)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af5cb0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4449.000000 -204.000000) translate(0,33)">角</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af5cb0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4449.000000 -204.000000) translate(0,51)">集</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af5cb0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4449.000000 -204.000000) translate(0,69)">镇</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2af5cb0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4449.000000 -204.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b00040" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4578.000000 -163.000000) translate(0,15)">洒</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b00040" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4578.000000 -163.000000) translate(0,33)">洲</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2b00040" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4578.000000 -163.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27a17a0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4706.000000 -163.000000) translate(0,15)">扎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27a17a0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4706.000000 -163.000000) translate(0,33)">郎</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_27a17a0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4706.000000 -163.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a420b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4773.000000 -832.000000) translate(0,15)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2a445a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3280.500000 -1166.500000) translate(0,16)">八角变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2b0c8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3138.000000 -588.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_2a232a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3137.000000 -1026.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_29bf7c0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4308.000000 -1044.000000) translate(0,15)">35kV母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2ac18d0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4334.000000 -624.000000) translate(0,15)">10kVⅠ段母线电压互感器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a3ca90" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4947.000000 -419.000000) translate(0,15)">10kVⅠ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a3ce60" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3588.000000 -419.000000) translate(0,15)">10kVⅡ段母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23530" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4801.000000 -181.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23530" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4801.000000 -181.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23530" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4801.000000 -181.000000) translate(0,51)">1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a23530" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4801.000000 -181.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abe870" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5039.000000 -185.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abe870" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5039.000000 -185.000000) translate(0,33)">3号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abe870" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5039.000000 -185.000000) translate(0,51)">电容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2abe870" transform="matrix(1.000000 -0.000000 0.000000 1.000000 5039.000000 -185.000000) translate(0,69)">器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a15690" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4067.000000 -181.000000) translate(0,15)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a15690" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4067.000000 -181.000000) translate(0,33)">五</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a15690" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4067.000000 -181.000000) translate(0,51)">三</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a15690" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4067.000000 -181.000000) translate(0,69)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a97960" transform="matrix(1.000000 0.000000 -0.000000 1.000000 3994.000000 -618.000000) translate(0,15)">35kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a98680" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4147.000000 -49.000000) translate(0,15)">10kV2号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5c1e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3927.000000 -163.000000) translate(0,15)">哨</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5c1e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3927.000000 -163.000000) translate(0,33)">房</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a5c1e0" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3927.000000 -163.000000) translate(0,51)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297cb20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3807.000000 -181.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297cb20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3807.000000 -181.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297cb20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3807.000000 -181.000000) translate(0,51)">2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297cb20" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3807.000000 -181.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297dc30" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3729.000000 -181.000000) translate(0,15)">预</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297dc30" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3729.000000 -181.000000) translate(0,33)">留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297dc30" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3729.000000 -181.000000) translate(0,51)">3</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297dc30" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3729.000000 -181.000000) translate(0,69)">回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297ece0" transform="matrix(1.000000 0.000000 -0.000000 1.000000 4289.500000 -298.000000) translate(0,15)">分段</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297f450" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3542.000000 -182.000000) translate(0,15)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297f450" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3542.000000 -182.000000) translate(0,33)">4号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297f450" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3542.000000 -182.000000) translate(0,51)">电容</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_297f450" transform="matrix(1.000000 -0.000000 0.000000 1.000000 3542.000000 -182.000000) translate(0,69)">器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a80890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -1007.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -945.000000) translate(0,12)">38160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4188.000000 -893.000000) translate(0,12)">38117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a818c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -849.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81b00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4109.000000 -905.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81d40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -959.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a81f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4660.000000 -697.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a821c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4658.000000 -775.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a82400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4671.000000 -748.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a82640" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4400.000000 -883.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a82b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -939.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a82de0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4413.000000 -856.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4659.000000 -520.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83260" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4657.000000 -450.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a834a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3817.000000 -448.000000) translate(0,12)">0902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a836e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4451.000000 -450.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83920" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3962.000000 -315.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -268.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83da0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3960.000000 -363.000000) translate(0,12)">0822</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a83fe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4102.000000 -315.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a84220" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -264.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a84460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4100.000000 -363.000000) translate(0,12)">0812</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a846a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4294.000000 -336.000000) translate(0,12)">012</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5940" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4182.000000 -363.000000) translate(0,12)">0122</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4390.000000 -363.000000) translate(0,12)">0121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac5dc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4483.000000 -315.000000) translate(0,12)">091</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6000" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -265.000000) translate(0,12)">0916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6240" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4481.000000 -363.000000) translate(0,12)">0911</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4613.000000 -315.000000) translate(0,12)">092</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac66c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -265.000000) translate(0,12)">0926</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -363.000000) translate(0,12)">0921</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6b40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -363.000000) translate(0,12)">0931</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6d80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4741.000000 -265.000000) translate(0,12)">0936</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2ac6fc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -315.000000) translate(0,12)">093</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a136c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -91.000000) translate(0,12)">09500</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a13bb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -363.000000) translate(0,12)">0951</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a13df0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4941.000000 -315.000000) translate(0,12)">095</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a14030" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -264.000000) translate(0,12)">0956</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a14270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4947.000000 -223.000000) translate(0,12)">09567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a33210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4703.000000 -1110.000000) translate(0,15)">35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a33210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4703.000000 -1110.000000) translate(0,33)">kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a33210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4703.000000 -1110.000000) translate(0,51)">八</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a33210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4703.000000 -1110.000000) translate(0,69)">马</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_2a33210" transform="matrix(1.000000 -0.000000 0.000000 1.000000 4703.000000 -1110.000000) translate(0,87)">线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e3e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -1008.000000) translate(0,12)">38267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e4330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -946.000000) translate(0,12)">38260</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e4570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4724.000000 -894.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e47b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4638.000000 -847.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e49f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4642.000000 -907.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29e4c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4636.000000 -962.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2988a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -227.000000) translate(0,17)">3813050</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_298a840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -1142.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_298c9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3469.000000 -1177.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2a070f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3142.000000 -773.000000) translate(0,12)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,12)">1号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,27)">SZ11-2500/35GY</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,57)">2500kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,72)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f6ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4843.000000 -700.000000) translate(0,87)">Ud=7.06%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29faa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3120.000000 -187.000000) translate(0,17)">楚雄巡维中</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29faa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3120.000000 -187.000000) translate(0,38)">心变运三班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29fcf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -197.500000) translate(0,17)">18787878955</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29fcf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -197.500000) translate(0,38)">18787878953</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_29fcf60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3256.000000 -197.500000) translate(0,59)">18787878979</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -136.000000) translate(0,12)">10kV1号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2939970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4856.000000 -136.000000) translate(0,27)">电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_293be70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4559.000000 -622.000000) translate(0,12)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_293c740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3380.000000 -1053.000000) translate(0,16)">AVC</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f2e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4200.000000 -530.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29f3430" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -460.000000) translate(0,12)">0022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cf250" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4220.000000 -628.000000) translate(0,12)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cf740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4198.000000 -778.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cf980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4224.000000 -758.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29cfbc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4201.000000 -708.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_29736d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3636.000000 -89.000000) translate(0,12)">2号电容器组</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3682.000000 -312.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976810" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3680.000000 -360.000000) translate(0,12)">0852</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976a50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3625.000000 -265.000000) translate(0,12)">0856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2976c90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3690.000000 -254.000000) translate(0,12)">08567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,12)">2号主变参数：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,27)">SZ20-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,42)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,57)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,72)">Y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2979a10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4263.000000 -542.000000) translate(0,87)">Ud=7.33%</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="24" graphid="g_297c020" transform="matrix(0.666667 -0.000000 -0.000000 0.666667 3139.333333 -734.333333) translate(0,20)">隔刀远控</text>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_2a382c0">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 3805.000000 -497.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af5fa0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4124.000000 -1036.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af69b0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3772.000000 -495.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2af73c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4498.000000 -150.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2aa59a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4628.000000 -148.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a4b430">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 1.000000 0.000000 4340.500000 -908.500000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac0e80">
    <use class="BV-10KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 4439.000000 -494.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2ac23c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4406.000000 -492.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2b266f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4758.000000 -148.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a16450">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4117.000000 -152.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_29d9800">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3977.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_295cee0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 0.000000 -1.000000 4660.000000 -1037.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_298d160">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4044.000000 -736.000000)" xlink:href="#lightningRod:shape55"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2a08780">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4884.000000 -151.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2970570">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3625.000000 -148.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_BJ"/>
</svg>