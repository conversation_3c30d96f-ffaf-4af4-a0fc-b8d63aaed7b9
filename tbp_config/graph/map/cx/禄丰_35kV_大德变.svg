<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-99" aopId="0" id="thSvg" product="E8000V2" version="1.0" viewBox="3124 -1214 2198 1374">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.371212" x1="7" x2="11" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="9" x2="9" y1="27" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="0" x2="18" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.453216" x1="6" x2="13" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape76">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="59" x2="24" y1="7" y2="7"/>
    <rect height="12" stroke-width="1" width="26" x="18" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="1" x2="1" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="5" x2="5" y1="3" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="17" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="8" x2="8" y1="12" y2="1"/>
   </symbol>
   <symbol id="lightningRod:shape66">
    <rect height="31" stroke-width="0.5" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="6" y2="35"/>
   </symbol>
   <symbol id="lightningRod:shape123">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="16" x2="16" y1="2" y2="2"/>
    <ellipse cx="8" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="25" x2="20" y1="8" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="20" x2="20" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="5" y1="7" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="10" x2="8" y1="9" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="8" x2="8" y1="4" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="11" y1="18" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="16" x2="14" y1="20" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.155709" x1="14" x2="14" y1="15" y2="18"/>
    <ellipse cx="19" cy="8" fillStyle="0" rx="8.5" ry="7.5" stroke-width="0.155709"/>
    <ellipse cx="14" cy="16" fillStyle="0" rx="9" ry="7.5" stroke-width="0.155709"/>
   </symbol>
   <symbol id="lightningRod:shape50">
    <polyline DF8003:Layer="PUBLIC" points="5,39 0,50 11,50 5,39 5,40 5,39 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.222222" x1="5" x2="5" y1="29" y2="33"/>
    <polyline DF8003:Layer="PUBLIC" points="5,25 0,14 11,14 5,25 5,24 5,25 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.444444" x1="5" x2="5" y1="59" y2="51"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="7" x2="7" y1="9" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="4" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <rect height="26" stroke-width="1" width="12" x="1" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="59" y2="24"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="13" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="5" x2="9" y1="60" y2="60"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape159">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="9" x2="13" y1="17" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="13" x2="13" y1="13" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="17" x2="13" y1="17" y2="13"/>
    <ellipse cx="12" cy="13" rx="11" ry="12.5" stroke-width="1.22172"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="8" x2="12" y1="37" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.33311" x1="12" x2="12" y1="33" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.3331" x1="16" x2="12" y1="37" y2="33"/>
    <ellipse cx="12" cy="33" rx="11" ry="12" stroke-width="1.22172"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape193">
    <polyline DF8003:Layer="PUBLIC" points="1,5 10,17 19,5 1,5 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="19,39 10,27 1,39 19,39 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="10" y1="27" y2="17"/>
   </symbol>
   <symbol id="load:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.620631" x1="9" x2="9" y1="27" y2="3"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="1,13 9,1 17,13 " stroke-width="2"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="transformer2:shape11_0">
    <ellipse cx="13" cy="34" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="40" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="36" y2="40"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="32" y2="36"/>
   </symbol>
   <symbol id="transformer2:shape11_1">
    <circle cx="13" cy="16" fillStyle="0" r="13" stroke-width="0.265306"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="9" x2="13" y1="20" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="17" y1="16" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.265306" x1="13" x2="13" y1="12" y2="16"/>
   </symbol>
   <symbol id="transformer2:shape10_0">
    <ellipse cx="38" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="66" x2="71" y1="83" y2="83"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="71" x2="69" y1="83" y2="78"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.5" x1="71" x2="71" y1="80" y2="80"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.911765" x1="1" x2="69" y1="44" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="29" x2="37" y1="74" y2="66"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="45" y1="66" y2="74"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="37" x2="37" y1="58" y2="66"/>
   </symbol>
   <symbol id="transformer2:shape10_1">
    <circle cx="38" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="33" x2="33" y1="33" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="49" x2="33" y1="24" y2="33"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1a9de10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1a9e8f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9eeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1a9fcf0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa0b30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aa14c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa1d50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_1aa2530" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_12837a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_12837a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa52b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa52b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aa7190" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_1aa81a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aaaa70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aab6c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1aac5a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1aace80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aae640" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aaf340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1aafc00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ab03c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ab14a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ab1e20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ab2910" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ab32d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ab4750" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ab52f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1ab6320" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ab6f60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1ac5730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1ab8860" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1ab9e40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1abb370" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1384" width="2208" x="3119" y="-1219"/>
  </g><g id="MultiLine_Layer">
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4890,-415 4885,-405 4895,-405 4890,-415 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4890,-486 4885,-496 4895,-496 4890,-486 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3741,-487 3736,-497 3746,-497 3741,-487 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3741,-416 3736,-406 3746,-406 3741,-416 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3813,-115 3808,-125 3818,-125 3813,-115 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3813,-110 3808,-100 3818,-100 3813,-110 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4445,-109 4440,-99 4450,-99 4445,-109 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4445,-113 4440,-123 4450,-123 4445,-113 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4764,-118 4759,-128 4769,-128 4764,-118 " stroke="rgb(50,205,50)" stroke-width="1"/>
   <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4764,-114 4759,-104 4769,-104 4764,-114 " stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-78618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -937.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16538" ObjectName="SW-LF_DDB.LF_DDB_381BK"/>
     <cge:Meas_Ref ObjectId="78618"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76044">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -936.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16539" ObjectName="SW-LF_DDB.LF_DDB_382BK"/>
     <cge:Meas_Ref ObjectId="76044"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4881.515038 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16536" ObjectName="SW-LF_DDB.LF_DDB_302BK"/>
     <cge:Meas_Ref ObjectId="78616"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3732.515038 -730.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16534" ObjectName="SW-LF_DDB.LF_DDB_301BK"/>
     <cge:Meas_Ref ObjectId="78613"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78615">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3605.515038 -414.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16535" ObjectName="SW-LF_DDB.LF_DDB_001BK"/>
     <cge:Meas_Ref ObjectId="78615"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76035">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3803.515038 -417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16530" ObjectName="SW-LF_DDB.LF_DDB_081BK"/>
     <cge:Meas_Ref ObjectId="76035"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76036">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4271.515038 -417.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16531" ObjectName="SW-LF_DDB.LF_DDB_082BK"/>
     <cge:Meas_Ref ObjectId="76036"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76037">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4435.515038 -416.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16532" ObjectName="SW-LF_DDB.LF_DDB_083BK"/>
     <cge:Meas_Ref ObjectId="76037"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78607">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4594.515038 -415.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16528" ObjectName="SW-LF_DDB.LF_DDB_085BK"/>
     <cge:Meas_Ref ObjectId="78607"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76038">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4754.515038 -418.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16533" ObjectName="SW-LF_DDB.LF_DDB_086BK"/>
     <cge:Meas_Ref ObjectId="76038"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-78617">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5020.515038 -415.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16537" ObjectName="SW-LF_DDB.LF_DDB_002BK"/>
     <cge:Meas_Ref ObjectId="78617"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296560">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.015038 -404.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46046" ObjectName="SW-LF_DDB.LF_DDB_084BK"/>
     <cge:Meas_Ref ObjectId="296560"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-LF_DDB.LF_DDB_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3689,-878 5015,-878 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17360" ObjectName="BS-LF_DDB.LF_DDB_3IM"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   <polyline fill="none" opacity="0" points="3689,-878 5015,-878 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DDB.LF_DDB_9IM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3439,-529 5179,-529 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="17361" ObjectName="BS-LF_DDB.LF_DDB_9IM"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   <polyline fill="none" opacity="0" points="3439,-529 5179,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-LF_DDB.LF_DDB_9PM">
    <g class="BV-10KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3437,-148 5179,-148 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="34448" ObjectName="BS-LF_DDB.LF_DDB_9PM"/>
    <cge:TPSR_Ref TObjectID="34448"/></metadata>
   <polyline fill="none" opacity="0" points="3437,-148 5179,-148 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-LF_DDB.LD_LF_082">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -83.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37973" ObjectName="EC-LF_DDB.LD_LF_082"/>
    <cge:TPSR_Ref TObjectID="37973"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_DDB.LD_LF_083">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -60.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37974" ObjectName="EC-LF_DDB.LD_LF_083"/>
    <cge:TPSR_Ref TObjectID="37974"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_DDB.LD_LF_086">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -5.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="37975" ObjectName="EC-LF_DDB.LD_LF_086"/>
    <cge:TPSR_Ref TObjectID="37975"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_DDB.LD_LF_081">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3805.000000 -20.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="18084" ObjectName="EC-LF_DDB.LD_LF_081"/>
    <cge:TPSR_Ref TObjectID="18084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-LF_DDB.LD_LF_084">
    <use class="BKBV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -1.000000)" xlink:href="#load:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46452" ObjectName="EC-LF_DDB.LD_LF_084"/>
    <cge:TPSR_Ref TObjectID="46452"/></metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11f7100" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4928.515038 -710.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e32e0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3778.515038 -710.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1240a40" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4467.515038 -728.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_121a3f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3833.000000 -1035.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1184440" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3542.000000 -364.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1254080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-878 3771,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16564@0" Pin0InfoVect0LinkObjId="SW-76266_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-878 3771,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1311da0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-931 3771,-945 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16564@1" ObjectIDZND0="16538@0" Pin0InfoVect0LinkObjId="SW-78618_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76266_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-931 3771,-945 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12f8ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-972 3771,-987 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16538@1" ObjectIDZND0="16565@0" Pin0InfoVect0LinkObjId="SW-76267_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78618_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-972 3771,-987 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1239420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-878 4926,-894 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16568@0" Pin0InfoVect0LinkObjId="SW-76294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-878 4926,-894 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123a810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-930 4926,-944 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16568@1" ObjectIDZND0="16539@0" Pin0InfoVect0LinkObjId="SW-76044_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-930 4926,-944 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123c220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-971 4926,-986 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16539@1" ObjectIDZND0="16569@0" Pin0InfoVect0LinkObjId="SW-76295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76044_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-971 4926,-986 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11f7730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 4939,-798 4939,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="16536@x" ObjectIDND1="16560@x" ObjectIDND2="16536@x" ObjectIDZND0="16563@1" Pin0InfoVect0LinkObjId="SW-76232_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-78616_0" Pin1InfoVect1LinkObjId="SW-76229_0" Pin1InfoVect2LinkObjId="SW-78616_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 4939,-798 4939,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11f7920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4939,-749 4939,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16563@0" ObjectIDZND0="g_11f7100@0" Pin0InfoVect0LinkObjId="g_11f7100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76232_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4939,-749 4939,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11f7b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-738 4891,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16536@0" ObjectIDZND0="16579@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78616_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-738 4891,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12dfed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-878 3742,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16556@1" Pin0InfoVect0LinkObjId="SW-76190_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-878 3742,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123cfc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-798 3789,-798 3789,-785 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="16534@x" ObjectIDND1="16556@x" ObjectIDZND0="16559@1" Pin0InfoVect0LinkObjId="SW-76193_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78613_0" Pin1InfoVect1LinkObjId="SW-76190_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-798 3789,-798 3789,-785 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123d1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3789,-749 3789,-736 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16559@0" ObjectIDZND0="g_12e32e0@0" Pin0InfoVect0LinkObjId="g_12e32e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76193_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3789,-749 3789,-736 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123d3a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-738 3742,-700 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" ObjectIDND0="16534@0" ObjectIDZND0="16578@0" Pin0InfoVect0LinkObjId="g_125a530_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-738 3742,-700 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123eb00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-878 4431,-863 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16571@1" Pin0InfoVect0LinkObjId="SW-76325_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-878 4431,-863 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_123ecf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-827 4431,-816 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="16571@0" ObjectIDZND0="16572@x" ObjectIDZND1="g_12e5eb0@0" ObjectIDZND2="g_12e6ab0@0" Pin0InfoVect0LinkObjId="SW-76328_0" Pin0InfoVect1LinkObjId="g_12e5eb0_0" Pin0InfoVect2LinkObjId="g_12e6ab0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-827 4431,-816 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1220a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-816 4478,-816 4478,-803 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="16571@x" ObjectIDND1="g_12e5eb0@0" ObjectIDND2="g_12e6ab0@0" ObjectIDZND0="16572@1" Pin0InfoVect0LinkObjId="SW-76328_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="g_12e5eb0_0" Pin1InfoVect2LinkObjId="g_12e6ab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-816 4478,-816 4478,-803 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1220c10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4478,-767 4478,-754 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16572@0" ObjectIDZND0="g_1240a40@0" Pin0InfoVect0LinkObjId="g_1240a40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76328_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4478,-767 4478,-754 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1221130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1023 3771,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="powerLine" ObjectIDND0="16565@1" ObjectIDZND0="16566@x" ObjectIDZND1="g_12f90b0@0" ObjectIDZND2="18037@1" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="g_12f90b0_0" Pin0InfoVect2LinkObjId="g_1233370_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76267_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1023 3771,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1222c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1041 3787,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="powerLine" EndDevType0="switch" ObjectIDND0="16565@x" ObjectIDND1="g_12f90b0@0" ObjectIDND2="18037@1" ObjectIDZND0="16566@0" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76267_0" Pin1InfoVect1LinkObjId="g_12f90b0_0" Pin1InfoVect2LinkObjId="g_1233370_1" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1041 3787,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1222e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3823,-1041 3837,-1041 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="16566@1" ObjectIDZND0="g_121a3f0@0" Pin0InfoVect0LinkObjId="g_121a3f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76268_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3823,-1041 3837,-1041 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e5ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4941,-1100 4926,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="16570@0" ObjectIDZND0="g_123c410@0" ObjectIDZND1="16569@x" ObjectIDZND2="18085@1" Pin0InfoVect0LinkObjId="g_123c410_0" Pin0InfoVect1LinkObjId="SW-76295_0" Pin0InfoVect2LinkObjId="g_118a150_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4941,-1100 4926,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e5cc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-793 4419,-793 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="16571@x" ObjectIDND1="16572@x" ObjectIDND2="g_12e6ab0@0" ObjectIDZND0="g_12e5eb0@0" Pin0InfoVect0LinkObjId="g_12e5eb0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="SW-76328_0" Pin1InfoVect2LinkObjId="g_12e6ab0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-793 4419,-793 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e68c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-816 4431,-792 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16571@x" ObjectIDND1="16572@x" ObjectIDZND0="g_12e5eb0@0" ObjectIDZND1="g_12e6ab0@0" Pin0InfoVect0LinkObjId="g_12e5eb0_0" Pin0InfoVect1LinkObjId="g_12e6ab0_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76325_0" Pin1InfoVect1LinkObjId="SW-76328_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-816 4431,-792 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e70a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-792 4431,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_12e5eb0@0" ObjectIDND1="16571@x" ObjectIDND2="16572@x" ObjectIDZND0="g_12e6ab0@1" Pin0InfoVect0LinkObjId="g_12e6ab0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12e5eb0_0" Pin1InfoVect1LinkObjId="SW-76325_0" Pin1InfoVect2LinkObjId="SW-76328_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-792 4431,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e7290">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4431,-727 4431,-703 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12e6ab0@0" ObjectIDZND0="g_12e7480@0" Pin0InfoVect0LinkObjId="g_12e7480_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12e6ab0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4431,-727 4431,-703 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="16536@x" ObjectIDND1="16560@x" ObjectIDND2="16563@x" ObjectIDZND0="16536@x" ObjectIDZND1="16560@x" ObjectIDZND2="16563@x" Pin0InfoVect0LinkObjId="SW-78616_0" Pin0InfoVect1LinkObjId="SW-76229_0" Pin0InfoVect2LinkObjId="SW-76232_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-78616_0" Pin1InfoVect1LinkObjId="SW-76229_0" Pin1InfoVect2LinkObjId="SW-76232_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-798 4891,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="breaker" EndDevType0="breaker" ObjectIDND0="16560@x" ObjectIDND1="16563@x" ObjectIDND2="16536@x" ObjectIDZND0="16536@1" Pin0InfoVect0LinkObjId="SW-78616_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76229_0" Pin1InfoVect1LinkObjId="SW-76232_0" Pin1InfoVect2LinkObjId="SW-78616_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-798 4891,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-798 3742,-765 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="16556@x" ObjectIDND1="16559@x" ObjectIDZND0="16534@1" Pin0InfoVect0LinkObjId="SW-78613_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76190_0" Pin1InfoVect1LinkObjId="SW-76193_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-798 3742,-765 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3742,-827 3742,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="16556@0" ObjectIDZND0="16534@x" ObjectIDZND1="16559@x" Pin0InfoVect0LinkObjId="SW-78613_0" Pin0InfoVect1LinkObjId="SW-76193_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76190_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3742,-827 3742,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e8ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-878 4891,-860 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17360@0" ObjectIDZND0="16560@1" Pin0InfoVect0LinkObjId="SW-76229_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-878 4891,-860 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12e90f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4891,-824 4891,-798 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="16560@0" ObjectIDZND0="16536@x" ObjectIDZND1="16563@x" ObjectIDZND2="16536@x" Pin0InfoVect0LinkObjId="SW-78616_0" Pin0InfoVect1LinkObjId="SW-76232_0" Pin0InfoVect2LinkObjId="SW-78616_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76229_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4891,-824 4891,-798 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b3ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-529 3615,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16558@1" Pin0InfoVect0LinkObjId="SW-76192_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-529 3615,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b3f00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-475 3615,-449 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16558@0" ObjectIDZND0="16535@1" Pin0InfoVect0LinkObjId="SW-78615_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76192_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-475 3615,-449 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b6340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-529 3813,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16541@1" Pin0InfoVect0LinkObjId="SW-76091_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-529 3813,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b85c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-474 3813,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16541@0" ObjectIDZND0="16530@1" Pin0InfoVect0LinkObjId="SW-76035_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76091_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-474 3813,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b89d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-291 3813,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12b8250@1" ObjectIDZND0="16543@1" Pin0InfoVect0LinkObjId="SW-76093_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12b8250_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-291 3813,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12b8db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-236 3813,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="16543@0" ObjectIDZND0="g_12bba70@0" ObjectIDZND1="48337@x" ObjectIDZND2="16540@x" Pin0InfoVect0LinkObjId="g_12bba70_0" Pin0InfoVect1LinkObjId="SW-311531_0" Pin0InfoVect2LinkObjId="SW-76090_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76093_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-236 3813,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-224 3872,-224 3872,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12bba70@0" ObjectIDND1="48337@x" ObjectIDND2="16543@x" ObjectIDZND0="16540@1" Pin0InfoVect0LinkObjId="SW-76090_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12bba70_0" Pin1InfoVect1LinkObjId="SW-311531_0" Pin1InfoVect2LinkObjId="SW-76093_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-224 3872,-224 3872,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3872,-174 3872,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16540@0" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_1255650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76090_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3872,-174 3872,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-137 3855,-137 3855,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="16543@x" ObjectIDND1="16540@x" ObjectIDND2="48337@x" ObjectIDZND0="g_12bba70@0" Pin0InfoVect0LinkObjId="g_12bba70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76093_0" Pin1InfoVect1LinkObjId="SW-76090_0" Pin1InfoVect2LinkObjId="SW-311531_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-137 3855,-137 3855,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bb850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-224 3813,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16543@x" ObjectIDND1="16540@x" ObjectIDZND0="g_12bba70@0" ObjectIDZND1="48337@x" Pin0InfoVect0LinkObjId="g_12bba70_0" Pin0InfoVect1LinkObjId="SW-311531_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76093_0" Pin1InfoVect1LinkObjId="SW-76090_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-224 3813,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bc660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-529 3967,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16574@1" Pin0InfoVect0LinkObjId="SW-76463_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-529 3967,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12bc880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3967,-474 3967,-421 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer2" ObjectIDND0="16574@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11f7100_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76463_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3967,-474 3967,-421 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1293cb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-529 4128,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16573@1" Pin0InfoVect0LinkObjId="SW-76348_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-529 4128,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1293ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-474 4128,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="16573@0" ObjectIDZND0="g_1294ab0@0" ObjectIDZND1="g_1294310@0" Pin0InfoVect0LinkObjId="g_1294ab0_0" Pin0InfoVect1LinkObjId="g_1294310_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76348_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-474 4128,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12940f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4128,-456 4171,-456 4171,-429 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="lightningRod" ObjectIDND0="g_1294310@0" ObjectIDND1="16573@x" ObjectIDZND0="g_1294ab0@0" Pin0InfoVect0LinkObjId="g_1294ab0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1294310_0" Pin1InfoVect1LinkObjId="SW-76348_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4128,-456 4171,-456 4171,-429 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fa880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-378 4096,-403 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_12956a0@0" ObjectIDZND0="g_1294310@0" Pin0InfoVect0LinkObjId="g_1294310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12956a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-378 4096,-403 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12faaa0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-434 4096,-456 4128,-456 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="g_1294310@1" ObjectIDZND0="g_1294ab0@0" ObjectIDZND1="16573@x" Pin0InfoVect0LinkObjId="g_1294ab0_0" Pin0InfoVect1LinkObjId="SW-76348_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1294310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-434 4096,-456 4128,-456 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12fd200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-529 4281,-510 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16545@1" Pin0InfoVect0LinkObjId="SW-76111_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-529 4281,-510 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12ff110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-474 4281,-452 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16545@0" ObjectIDZND0="16531@1" Pin0InfoVect0LinkObjId="SW-76036_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76111_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-474 4281,-452 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12542b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-291 4281,-272 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_12ff330@1" ObjectIDZND0="16547@1" Pin0InfoVect0LinkObjId="SW-76113_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12ff330_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-291 4281,-272 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12544d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-236 4281,-224 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="load" EndDevType2="switch" ObjectIDND0="16547@0" ObjectIDZND0="g_1298a20@0" ObjectIDZND1="37973@x" ObjectIDZND2="16544@x" Pin0InfoVect0LinkObjId="g_1298a20_0" Pin0InfoVect1LinkObjId="EC-LF_DDB.LD_LF_082_0" Pin0InfoVect2LinkObjId="SW-76110_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76113_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-236 4281,-224 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-224 4339,-224 4339,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_1298a20@0" ObjectIDND1="37973@x" ObjectIDND2="16547@x" ObjectIDZND0="16544@1" Pin0InfoVect0LinkObjId="SW-76110_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_1298a20_0" Pin1InfoVect1LinkObjId="EC-LF_DDB.LD_LF_082_0" Pin1InfoVect2LinkObjId="SW-76113_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-224 4339,-224 4339,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255650">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4339,-174 4339,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16544@0" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_12bb410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4339,-174 4339,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12558b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-137 4322,-137 4322,-122 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="16547@x" ObjectIDND1="16544@x" ObjectIDND2="37973@x" ObjectIDZND0="g_1298a20@0" Pin0InfoVect0LinkObjId="g_1298a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76113_0" Pin1InfoVect1LinkObjId="SW-76110_0" Pin1InfoVect2LinkObjId="EC-LF_DDB.LD_LF_082_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-137 4322,-137 4322,-122 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-224 4281,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16547@x" ObjectIDND1="16544@x" ObjectIDZND0="g_1298a20@0" ObjectIDZND1="37973@x" Pin0InfoVect0LinkObjId="g_1298a20_0" Pin0InfoVect1LinkObjId="EC-LF_DDB.LD_LF_082_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76113_0" Pin1InfoVect1LinkObjId="SW-76110_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-224 4281,-137 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1255d70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-137 4281,-110 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="16547@x" ObjectIDND1="16544@x" ObjectIDND2="g_1298a20@0" ObjectIDZND0="37973@0" Pin0InfoVect0LinkObjId="EC-LF_DDB.LD_LF_082_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76113_0" Pin1InfoVect1LinkObjId="SW-76110_0" Pin1InfoVect2LinkObjId="g_1298a20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-137 4281,-110 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12564c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-529 4445,-509 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16549@1" Pin0InfoVect0LinkObjId="SW-76131_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-529 4445,-509 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12569b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-473 4445,-451 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16549@0" ObjectIDZND0="16532@1" Pin0InfoVect0LinkObjId="SW-76037_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76131_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-473 4445,-451 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1256fd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-290 4445,-271 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1256bf0@1" ObjectIDZND0="16551@1" Pin0InfoVect0LinkObjId="SW-76133_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1256bf0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-290 4445,-271 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-235 4445,-223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="16551@0" ObjectIDZND0="16548@x" ObjectIDZND1="g_12581f0@0" ObjectIDZND2="37974@x" Pin0InfoVect0LinkObjId="SW-76130_0" Pin0InfoVect1LinkObjId="g_12581f0_0" Pin0InfoVect2LinkObjId="EC-LF_DDB.LD_LF_083_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76133_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-235 4445,-223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-223 4503,-223 4503,-209 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="g_12581f0@0" ObjectIDND1="37974@x" ObjectIDND2="16551@x" ObjectIDZND0="16548@1" Pin0InfoVect0LinkObjId="SW-76130_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12581f0_0" Pin1InfoVect1LinkObjId="EC-LF_DDB.LD_LF_083_0" Pin1InfoVect2LinkObjId="SW-76133_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-223 4503,-223 4503,-209 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257ad0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4503,-173 4503,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16548@0" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_12bb410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4503,-173 4503,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257d30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-136 4486,-136 4486,-130 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="load" EndDevType0="lightningRod" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDND2="37974@x" ObjectIDZND0="g_12581f0@0" Pin0InfoVect0LinkObjId="g_12581f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="EC-LF_DDB.LD_LF_083_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-136 4486,-136 4486,-130 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1257f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-223 4445,-136 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="load" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDZND0="g_12581f0@0" ObjectIDZND1="37974@x" Pin0InfoVect0LinkObjId="g_12581f0_0" Pin0InfoVect1LinkObjId="EC-LF_DDB.LD_LF_083_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-223 4445,-136 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1296570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-529 4604,-508 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16575@1" Pin0InfoVect0LinkObjId="SW-76473_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-529 4604,-508 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1296bd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-472 4604,-450 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16575@0" ObjectIDZND0="16528@1" Pin0InfoVect0LinkObjId="SW-78607_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76473_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-472 4604,-450 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1297630">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-289 4604,-270 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1296e30@1" ObjectIDZND0="16577@1" Pin0InfoVect0LinkObjId="SW-76475_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1296e30_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-289 4604,-270 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1298320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4604,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="g_1297890@0" ObjectIDND1="16577@x" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_12bb410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1297890_0" Pin1InfoVect1LinkObjId="SW-76475_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4604,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1298560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-234 4604,-222 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="busSection" ObjectIDND0="16577@0" ObjectIDZND0="g_1297890@0" ObjectIDZND1="34448@0" Pin0InfoVect0LinkObjId="g_1297890_0" Pin0InfoVect1LinkObjId="g_12bb410_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76475_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-234 4604,-222 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12987c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-222 4662,-222 4662,-213 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="busSection" EndDevType0="lightningRod" ObjectIDND0="16577@x" ObjectIDND1="34448@0" ObjectIDZND0="g_1297890@0" Pin0InfoVect0LinkObjId="g_1297890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76475_0" Pin1InfoVect1LinkObjId="g_12bb410_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-222 4662,-222 4662,-213 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1298e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-529 4764,-511 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="17361@0" ObjectIDZND0="16553@1" Pin0InfoVect0LinkObjId="SW-76151_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_129f620_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-529 4764,-511 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129ae10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-475 4764,-453 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" ObjectIDND0="16553@0" ObjectIDZND0="16533@1" Pin0InfoVect0LinkObjId="SW-76038_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76151_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-475 4764,-453 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c7760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-226 4822,-226 4822,-212 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16555@x" ObjectIDND1="g_12c80e0@0" ObjectIDND2="48338@x" ObjectIDZND0="16552@1" Pin0InfoVect0LinkObjId="SW-76150_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76153_0" Pin1InfoVect1LinkObjId="g_12c80e0_0" Pin1InfoVect2LinkObjId="SW-311532_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-226 4822,-226 4822,-212 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c79c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4822,-176 4822,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16552@0" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_12bb410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4822,-176 4822,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c7c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-138 4805,-138 4805,-123 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="16552@x" ObjectIDND1="16555@x" ObjectIDND2="48338@x" ObjectIDZND0="g_12c80e0@0" Pin0InfoVect0LinkObjId="g_12c80e0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="SW-76153_0" Pin1InfoVect2LinkObjId="SW-311532_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-138 4805,-138 4805,-123 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_12c7e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-226 4764,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="16552@x" ObjectIDND1="16555@x" ObjectIDZND0="g_12c80e0@0" ObjectIDZND1="48338@x" Pin0InfoVect0LinkObjId="g_12c80e0_0" Pin0InfoVect1LinkObjId="SW-311532_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="SW-76153_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-226 4764,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129f3c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-450 5030,-476 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="16537@1" ObjectIDZND0="16562@0" Pin0InfoVect0LinkObjId="SW-76231_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-450 5030,-476 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129f620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-512 5030,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="16562@1" ObjectIDZND0="17361@0" Pin0InfoVect0LinkObjId="g_117cbd0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76231_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-512 5030,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129f880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-407 5073,-407 5073,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="16537@x" ObjectIDND1="16579@x" ObjectIDZND0="g_1260870@0" Pin0InfoVect0LinkObjId="g_1260870_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78617_0" Pin1InfoVect1LinkObjId="g_11f7b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-407 5073,-407 5073,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129fae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4890,-620 4890,-350 5030,-350 5030,-407 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="16579@1" ObjectIDZND0="g_1260870@0" ObjectIDZND1="16537@x" Pin0InfoVect0LinkObjId="g_1260870_0" Pin0InfoVect1LinkObjId="SW-78617_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11f7b10_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4890,-620 4890,-350 5030,-350 5030,-407 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_129fd50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5030,-407 5030,-423 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer2" EndDevType0="breaker" ObjectIDND0="g_1260870@0" ObjectIDND1="16579@x" ObjectIDZND0="16537@0" Pin0InfoVect0LinkObjId="SW-78617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1260870_0" Pin1InfoVect1LinkObjId="g_11f7b10_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5030,-407 5030,-423 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121ae00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3786,-1098 3771,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="g_12f90b0@0" ObjectIDZND0="16566@x" ObjectIDZND1="16565@x" ObjectIDZND2="18037@1" Pin0InfoVect0LinkObjId="SW-76268_0" Pin0InfoVect1LinkObjId="SW-76267_0" Pin0InfoVect2LinkObjId="g_1233370_1" Pin0Num="1" Pin1InfoVect0LinkObjId="g_12f90b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3786,-1098 3771,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_121b060">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4912,-1076 4926,-1076 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="powerLine" EndDevType2="switch" ObjectIDND0="g_123c410@0" ObjectIDZND0="16570@x" ObjectIDZND1="18085@1" ObjectIDZND2="16569@x" Pin0InfoVect0LinkObjId="SW-76296_0" Pin0InfoVect1LinkObjId="g_118a150_1" Pin0InfoVect2LinkObjId="SW-76295_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_123c410_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4912,-1076 4926,-1076 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125a530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-422 3615,-379 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="transformer2" EndDevType1="lightningRod" ObjectIDND0="16535@0" ObjectIDZND0="16578@x" ObjectIDZND1="g_125d2c0@0" Pin0InfoVect0LinkObjId="g_123d3a0_0" Pin0InfoVect1LinkObjId="g_125d2c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78615_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-422 3615,-379 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125a720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-379 3615,-350 3740,-350 3741,-353 3741,-620 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="transformer2" ObjectIDND0="16535@x" ObjectIDND1="g_125d2c0@0" ObjectIDZND0="16578@1" Pin0InfoVect0LinkObjId="g_123d3a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78615_0" Pin1InfoVect1LinkObjId="g_125d2c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-379 3615,-350 3740,-350 3741,-353 3741,-620 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_125a910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3615,-379 3568,-379 3568,-392 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="lightningRod" ObjectIDND0="16535@x" ObjectIDND1="16578@x" ObjectIDZND0="g_125d2c0@0" Pin0InfoVect0LinkObjId="g_125d2c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-78615_0" Pin1InfoVect1LinkObjId="g_123d3a0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3615,-379 3568,-379 3568,-392 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f12e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-425 3813,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="16530@0" ObjectIDZND0="g_12b8250@0" Pin0InfoVect0LinkObjId="g_12b8250_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76035_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-425 3813,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f1540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4281,-425 4281,-344 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="16531@0" ObjectIDZND0="g_12ff330@0" Pin0InfoVect0LinkObjId="g_12ff330_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76036_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4281,-425 4281,-344 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f17a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-424 4445,-343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="16532@0" ObjectIDZND0="g_1256bf0@0" Pin0InfoVect0LinkObjId="g_1256bf0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76037_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-424 4445,-343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11f1a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4604,-423 4604,-342 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="16528@0" ObjectIDZND0="g_1296e30@0" Pin0InfoVect0LinkObjId="g_1296e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-78607_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4604,-423 4604,-342 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1188640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-426 4764,-345 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" ObjectIDND0="16533@0" ObjectIDZND0="g_129b070@0" Pin0InfoVect0LinkObjId="g_129b070_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76038_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-426 4764,-345 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_11891a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5071,-1100 5046,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_122fd40@0" ObjectIDZND0="g_11888a0@1" Pin0InfoVect0LinkObjId="g_11888a0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_122fd40_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5071,-1100 5046,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1189400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5014,-1100 4977,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_11888a0@0" ObjectIDZND0="16570@1" Pin0InfoVect0LinkObjId="SW-76296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11888a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5014,-1100 4977,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1189ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1075 4926,-1100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" EndDevType0="switch" EndDevType1="powerLine" ObjectIDND0="g_123c410@0" ObjectIDND1="16569@x" ObjectIDZND0="16570@x" ObjectIDZND1="18085@1" Pin0InfoVect0LinkObjId="SW-76296_0" Pin0InfoVect1LinkObjId="g_118a150_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_123c410_0" Pin1InfoVect1LinkObjId="SW-76295_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1075 4926,-1100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1100 4926,-1155 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="16570@x" ObjectIDND1="g_123c410@0" ObjectIDND2="16569@x" ObjectIDZND0="18085@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76296_0" Pin1InfoVect1LinkObjId="g_123c410_0" Pin1InfoVect2LinkObjId="SW-76295_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1100 4926,-1155 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_118a3b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4926,-1022 4926,-1075 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="powerLine" ObjectIDND0="16569@1" ObjectIDZND0="g_123c410@0" ObjectIDZND1="16570@x" ObjectIDZND2="18085@1" Pin0InfoVect0LinkObjId="g_123c410_0" Pin0InfoVect1LinkObjId="SW-76296_0" Pin0InfoVect2LinkObjId="g_118a150_1" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4926,-1022 4926,-1075 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1233370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1098 3771,-1117 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="powerLine" ObjectIDND0="g_12f90b0@0" ObjectIDND1="16566@x" ObjectIDND2="16565@x" ObjectIDZND0="18037@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_12f90b0_0" Pin1InfoVect1LinkObjId="SW-76268_0" Pin1InfoVect2LinkObjId="SW-76267_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1098 3771,-1117 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_12335b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3771,-1041 3771,-1098 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="powerLine" ObjectIDND0="16566@x" ObjectIDND1="16565@x" ObjectIDZND0="g_12f90b0@0" ObjectIDZND1="18037@1" Pin0InfoVect0LinkObjId="g_12f90b0_0" Pin0InfoVect1LinkObjId="g_1233370_1" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-76268_0" Pin1InfoVect1LinkObjId="SW-76267_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3771,-1041 3771,-1098 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127bc80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-226 4764,-239 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="switch" ObjectIDND0="16552@x" ObjectIDND1="g_12c80e0@0" ObjectIDND2="48338@x" ObjectIDZND0="16555@0" Pin0InfoVect0LinkObjId="SW-76153_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76150_0" Pin1InfoVect1LinkObjId="g_12c80e0_0" Pin1InfoVect2LinkObjId="SW-311532_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-226 4764,-239 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127bee0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-275 4764,-292 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="16555@1" ObjectIDZND0="g_129b070@1" Pin0InfoVect0LinkObjId="g_129b070_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-76153_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-275 4764,-292 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_127e8c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4445,-136 4445,-87 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="load" ObjectIDND0="16548@x" ObjectIDND1="16551@x" ObjectIDND2="g_12581f0@0" ObjectIDZND0="37974@0" Pin0InfoVect0LinkObjId="EC-LF_DDB.LD_LF_083_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-76130_0" Pin1InfoVect1LinkObjId="SW-76133_0" Pin1InfoVect2LinkObjId="g_12581f0_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4445,-136 4445,-87 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1284760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-96 4764,-138 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="48338@1" ObjectIDZND0="g_12c80e0@0" ObjectIDZND1="16552@x" ObjectIDZND2="16555@x" Pin0InfoVect0LinkObjId="g_12c80e0_0" Pin0InfoVect1LinkObjId="SW-76150_0" Pin0InfoVect2LinkObjId="SW-76153_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311532_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-96 4764,-138 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1284950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-93 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-93 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1287160">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4764,-36 4764,-53 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="37975@0" ObjectIDZND0="48338@0" Pin0InfoVect0LinkObjId="SW-311532_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-LF_DDB.LD_LF_086_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4764,-36 4764,-53 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117cbd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-508 3487,-529 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46047@1" ObjectIDZND0="17361@0" Pin0InfoVect0LinkObjId="g_129f620_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296561_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-508 3487,-529 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117ce30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-439 3487,-472 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" ObjectIDND0="46046@1" ObjectIDZND0="46047@0" Pin0InfoVect0LinkObjId="SW-296561_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296560_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-439 3487,-472 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117d090">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-298 3487,-278 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_1213f70@1" ObjectIDZND0="46048@1" Pin0InfoVect0LinkObjId="SW-296562_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1213f70_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-298 3487,-278 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_117d2f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-174 3536,-148 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="46049@0" ObjectIDZND0="34448@0" Pin0InfoVect0LinkObjId="g_12bb410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296563_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-174 3536,-148 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11830b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-92 3487,-75 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" ObjectIDND0="g_1182690@0" ObjectIDZND0="g_1181c90@0" Pin0InfoVect0LinkObjId="g_1181c90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1182690_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-92 3487,-75 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-41 3487,-28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" ObjectIDND0="g_1181c90@1" ObjectIDZND0="46452@0" Pin0InfoVect0LinkObjId="EC-LF_DDB.LD_LF_084_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1181c90_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-41 3487,-28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3535,-210 3487,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="46049@1" ObjectIDZND0="46048@x" ObjectIDZND1="g_1182690@0" ObjectIDZND2="g_117be20@0" Pin0InfoVect0LinkObjId="SW-296562_0" Pin0InfoVect1LinkObjId="g_1182690_0" Pin0InfoVect2LinkObjId="g_117be20_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296563_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3535,-210 3487,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_11837d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-210 3487,-242 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="lightningRod" EndDevType0="switch" ObjectIDND0="46049@x" ObjectIDND1="g_1182690@0" ObjectIDND2="g_117be20@0" ObjectIDZND0="46048@0" Pin0InfoVect0LinkObjId="SW-296562_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-296563_0" Pin1InfoVect1LinkObjId="g_1182690_0" Pin1InfoVect2LinkObjId="g_117be20_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-210 3487,-242 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3527,-132 3487,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_117be20@0" ObjectIDZND0="g_1182690@0" ObjectIDZND1="46048@x" ObjectIDZND2="46049@x" Pin0InfoVect0LinkObjId="g_1182690_0" Pin0InfoVect1LinkObjId="SW-296562_0" Pin0InfoVect2LinkObjId="SW-296563_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_117be20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3527,-132 3487,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-126 3487,-132 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_1182690@1" ObjectIDZND0="g_117be20@0" ObjectIDZND1="46048@x" ObjectIDZND2="46049@x" Pin0InfoVect0LinkObjId="g_117be20_0" Pin0InfoVect1LinkObjId="SW-296562_0" Pin0InfoVect2LinkObjId="SW-296563_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1182690_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-126 3487,-132 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_1183ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-132 3487,-210 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_1182690@0" ObjectIDND1="g_117be20@0" ObjectIDZND0="46048@x" ObjectIDZND1="46049@x" Pin0InfoVect0LinkObjId="SW-296562_0" Pin0InfoVect1LinkObjId="SW-296563_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_1182690_0" Pin1InfoVect1LinkObjId="g_117be20_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-132 3487,-210 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138ba70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3546,-370 3534,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="switch" ObjectIDND0="g_1184440@0" ObjectIDZND0="46050@1" Pin0InfoVect0LinkObjId="SW-296564_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1184440_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3546,-370 3534,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138bcd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3498,-370 3487,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="46050@0" ObjectIDZND0="g_1213f70@0" ObjectIDZND1="46046@x" Pin0InfoVect0LinkObjId="g_1213f70_0" Pin0InfoVect1LinkObjId="SW-296560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-296564_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3498,-370 3487,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138c7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-351 3487,-370 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="g_1213f70@0" ObjectIDZND0="46050@x" ObjectIDZND1="46046@x" Pin0InfoVect0LinkObjId="SW-296564_0" Pin0InfoVect1LinkObjId="SW-296560_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1213f70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-351 3487,-370 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138ca00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3487,-370 3487,-412 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="46050@x" ObjectIDND1="g_1213f70@0" ObjectIDZND0="46046@0" Pin0InfoVect0LinkObjId="SW-296560_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-296564_0" Pin1InfoVect1LinkObjId="g_1213f70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3487,-370 3487,-412 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138fab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-48 3813,-60 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="switch" ObjectIDND0="18084@0" ObjectIDZND0="48337@0" Pin0InfoVect0LinkObjId="SW-311531_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="EC-LF_DDB.LD_LF_081_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-48 3813,-60 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-10KV" id="g_138fd10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3813,-90 3813,-137 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="48337@1" ObjectIDZND0="16543@x" ObjectIDZND1="16540@x" ObjectIDZND2="g_12bba70@0" Pin0InfoVect0LinkObjId="SW-76093_0" Pin0InfoVect1LinkObjId="SW-76090_0" Pin0InfoVect2LinkObjId="g_12bba70_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-311531_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="3813,-90 3813,-137 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="17360" cx="3771" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17360" cx="4926" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17360" cx="4891" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17360" cx="3742" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17360" cx="4431" cy="-878" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="3615" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="3813" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="3967" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="4128" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="4281" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="4445" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="4604" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="4764" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="5030" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="17361" cx="3487" cy="-529" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="3872" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="4339" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="4503" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="4604" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="4822" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="34448" cx="3536" cy="-148" fill="rgb(50,205,50)" r="4" stroke="rgb(50,205,50)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-52548" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3436.000000 -1091.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="9258" ObjectName="DYN-LF_DDB"/>
     <cge:Meas_Ref ObjectId="52548"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,17)">下网有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,59)">片区有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,101)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,143)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_135e0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -1025.000000) translate(0,185)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_1241010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3145.000000 -587.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_1074880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3283.000000 -1166.500000) translate(0,16)">大德变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1240e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3728.000000 -1213.000000) translate(0,15)">35kV上大线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_13073a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4870.000000 -1210.000000) translate(0,15)">35kV果大T线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11f7d00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4848.000000 -761.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="22" graphid="g_123d590" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4362.000000 -913.000000) translate(0,18)">35kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1220e00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3513.000000 -558.000000) translate(0,15)">10kV母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12917a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3912.000000 -373.000000) translate(0,15)">10kV1号站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12facc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -334.000000) translate(0,15)">10kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1255fd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4252.000000 -67.000000) translate(0,15)">库区线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12585d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4403.000000 -58.000000) translate(0,15)">卸油泵房线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1297c30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4587.000000 -134.000000) translate(0,15)">旁路</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12c8e90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4743.000000 -6.000000) translate(0,15)">和平线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_129ffb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5048.000000 -445.000000) translate(0,12)">002</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_120a270" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3334.000000 -141.000000) translate(0,15)">10kV旁路母线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121bb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -920.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121c340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3778.000000 -1012.000000) translate(0,12)">3816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121c680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3785.000000 -1067.000000) translate(0,12)">38167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_121cbe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4438.000000 -851.000000) translate(0,15)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_121d380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4485.000000 -792.000000) translate(0,15)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121d7a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -919.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121dc00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -1011.000000) translate(0,12)">3826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121de40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4942.000000 -1126.000000) translate(0,12)">3829</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4898.000000 -849.000000) translate(0,12)">3021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4946.000000 -774.000000) translate(0,12)">30217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5037.000000 -501.000000) translate(0,12)">0021</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121e9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -500.000000) translate(0,12)">0861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121ec00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -497.000000) translate(0,12)">0851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4611.000000 -259.000000) translate(0,12)">0855</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -498.000000) translate(0,12)">0831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4510.000000 -198.000000) translate(0,12)">0835</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121f820" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4829.000000 -200.000000) translate(0,12)">0865</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121fa60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -499.000000) translate(0,12)">0821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121fca0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4288.000000 -261.000000) translate(0,12)">0826</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_121fee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4346.000000 -199.000000) translate(0,12)">0825</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1220120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4135.000000 -499.000000) translate(0,12)">0901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1220360" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3974.000000 -499.000000) translate(0,12)">0031</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12205a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -499.000000) translate(0,12)">0811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12207e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3821.000000 -261.000000) translate(0,12)">0816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12596f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3879.000000 -200.000000) translate(0,12)">0815</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1259930" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3627.000000 -446.000000) translate(0,12)">001</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1259b70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3622.000000 -500.000000) translate(0,12)">0011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1241fb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3749.000000 -852.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_12424a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3796.000000 -774.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_11ec5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -236.000000) translate(0,17)">4806026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_11ec5d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -236.000000) translate(0,38)">15750350664</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_11ee600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3859.000000 -666.000000) translate(0,15)">3150kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_11eea40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5014.000000 -666.000000) translate(0,15)">5000kVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11eee70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3750.000000 -445.000000) translate(0,12)">150/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ef350" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4221.000000 -444.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11ef8d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4394.000000 -445.000000) translate(0,12)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_11efb50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4701.000000 -445.000000) translate(0,12)">400/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_118a610" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3650.000000 -757.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118ae30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3619.000000 -416.000000) translate(0,12)">300/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,255,0)" font-family="SimSun" font-size="18" graphid="g_118b070" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4838.000000 -791.000000) translate(0,15)">100/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118b2b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4975.000000 -442.000000) translate(0,12)">600/5</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_118cfe0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4929.000000 -667.000000) translate(0,15)">2号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118dc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4779.000000 -448.000000) translate(0,12)">086</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118e110" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4621.000000 -443.000000) translate(0,12)">085</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118e560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4458.000000 -445.000000) translate(0,12)">083</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118e9f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4293.000000 -445.000000) translate(0,12)">082</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_118ee40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3827.000000 -445.000000) translate(0,12)">081</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_118f290" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3168.000000 -768.000000) translate(0,15)">公用信号</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_118fee0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -1166.000000) translate(0,16)">主网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(0,0,0)" font-family="SimSun" font-size="20" graphid="g_1191080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3495.000000 -1125.000000) translate(0,16)">配网返回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1192e10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3784.000000 -965.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1193190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4939.000000 -963.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_11935e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3782.000000 -667.000000) translate(0,15)">1号主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1193a30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3701.000000 -762.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1194230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -618.000000) translate(0,15)">SZ11-3150/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1194230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -618.000000) translate(0,33)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1194230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -618.000000) translate(0,51)">YNd11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1194230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3772.000000 -618.000000) translate(0,69)">Ud%:7.11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_122f160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -616.000000) translate(0,15)">SZ11-5000/35</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_122f160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -616.000000) translate(0,33)">35±3×2.5%/10.5kV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_122f160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -616.000000) translate(0,51)">y,d11</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_122f160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -616.000000) translate(0,69)">Ud%:7.47</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1230b20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4383.000000 -673.000000) translate(0,15)">35kV母线TV</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127d890" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4452.000000 -260.000000) translate(0,12)">0836</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127dd10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4780.000000 -264.000000) translate(0,12)">0866</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127eab0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4009.000000 -685.000000) translate(0,12)">温度1℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_127faf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4637.000000 -666.000000) translate(0,12)">温度1℃):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_127fd70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4704.000000 -93.000000) translate(0,15)">F0860</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1280f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3754.000000 -89.000000) translate(0,15)">F0810</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_12813c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3790.000000 -14.000000) translate(0,15)">路溪线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12819e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -181.000000) translate(0,17)">禄丰巡维中心</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_12819e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3124.000000 -181.000000) translate(0,38)">腰站变值班：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_123e6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3275.000000 -170.500000) translate(0,17)">13508785653</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1287910" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4035.000000 -709.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_120d470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -644.000000) translate(0,12)">档位:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_120dd90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3771.000000 -639.000000) translate(0,15)">1号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_120ea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4927.000000 -636.000000) translate(0,15)">2号主变参数:</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_117d550" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3460.000000 2.000000) translate(0,15)">小厂线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e040" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3496.000000 -433.000000) translate(0,12)">084</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e2c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -497.000000) translate(0,12)">0841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e500" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3494.000000 -267.000000) translate(0,12)">0846</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_117e740" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3542.000000 -199.000000) translate(0,12)">0845</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_117e980" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3357.000000 -68.000000) translate(0,15)">1号电缆分支箱</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_138cc60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3501.000000 -392.000000) translate(0,12)">08460</text>
  </g><g id="RectangleFilled_Layer">
   <rect DF8003:Layer="PUBLIC" fill="none" height="22" stroke="rgb(0,238,0)" stroke-width="1" width="10" x="3962" y="-461"/>
  </g><g id="PowerLine_Layer">
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="CX_SY" endPointId="0" endStationName="LF_DDB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_ShangDa" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="3771,-1182 3771,-1115 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18037" ObjectName="AC-35kV.LN_ShangDa"/>
    <cge:TPSR_Ref TObjectID="18037_SS-99"/></metadata>
   <polyline fill="none" opacity="0" points="3771,-1182 3771,-1115 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" beginPointId="0" beginStationName="PAS_T1" endPointId="0" endStationName="LF_DDB" flowDrawDirect="1" flowShape="0" id="AC-35kV.LN_GuoDaTDaDe" runFlow="0">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" fill="none" points="4926,-1152 4926,-1186 " stroke-width="3"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="18085" ObjectName="AC-35kV.LN_GuoDaTDaDe"/>
    <cge:TPSR_Ref TObjectID="18085_SS-99"/></metadata>
   <polyline fill="none" opacity="0" points="4926,-1152 4926,-1186 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_12f90b0">
    <use class="BV-35KV" transform="matrix(-1.000000 -0.000000 -0.000000 1.000000 3845.000000 -1105.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_123c410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4854.000000 -1069.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e5eb0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4361.000000 -787.000000)" xlink:href="#lightningRod:shape76"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e6ab0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 -722.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12e7480">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4418.000000 -679.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12b8250">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3808.000000 -286.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12bba70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3848.000000 -64.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1294310">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -398.000000)" xlink:href="#lightningRod:shape66"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1294ab0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4164.000000 -371.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12956a0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4083.000000 -354.000000)" xlink:href="#lightningRod:shape123"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12ff330">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4276.000000 -286.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1256bf0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4440.000000 -285.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12581f0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4479.000000 -72.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1296e30">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4599.000000 -284.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1297890">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4655.000000 -155.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1298a20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4315.000000 -64.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_129b070">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4759.000000 -287.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_12c80e0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4798.000000 -65.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_125d2c0">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3561.000000 -388.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1260870">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5066.000000 -334.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_11888a0">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5051.000000 -1091.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_122fd40">
    <use class="BV-35KV" transform="matrix(0.000000 -1.000000 -1.000000 -0.000000 5116.500000 -1088.500000)" xlink:href="#lightningRod:shape159"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1213f70">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3482.500000 -293.000000)" xlink:href="#lightningRod:shape50"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_117be20">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3520.000000 -74.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1181c90">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3480.000000 -36.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1182690">
    <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3477.000000 -87.000000)" xlink:href="#lightningRod:shape193"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78775" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -1014.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78775" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78776" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -1014.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78776" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78767" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -1014.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78767" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78769" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -1014.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78769" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78777" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3856.000000 -1014.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78777" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16538"/>
     <cge:Term_Ref ObjectID="6961"/>
    <cge:TPSR_Ref TObjectID="16538"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78748" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -822.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78748" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78749" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -822.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78749" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78734" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -822.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78734" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78736" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -822.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78736" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78750" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3850.000000 -822.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78750" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16534"/>
     <cge:Term_Ref ObjectID="16116"/>
    <cge:TPSR_Ref TObjectID="16534"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78787" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1016.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78787" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78788" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1016.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78788" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78779" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1016.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78779" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78781" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1016.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78781" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78789" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4771.000000 -1016.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78789" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16539"/>
     <cge:Term_Ref ObjectID="6969"/>
    <cge:TPSR_Ref TObjectID="16539"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78751" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -329.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78751" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78752" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -329.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78752" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78740" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -329.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78740" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78742" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -329.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78742" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78753" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3597.000000 -329.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78753" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16535"/>
     <cge:Term_Ref ObjectID="16118"/>
    <cge:TPSR_Ref TObjectID="16535"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78699" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 14.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78699" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78700" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 14.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78700" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78691" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 14.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78691" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78693" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 14.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78693" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78701" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3780.000000 14.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78701" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16530"/>
     <cge:Term_Ref ObjectID="6941"/>
    <cge:TPSR_Ref TObjectID="16530"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78709" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -10.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78709" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78710" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -10.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78710" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78702" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -10.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78702" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78704" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -10.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78704" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78711" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -10.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78711" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16531"/>
     <cge:Term_Ref ObjectID="6943"/>
    <cge:TPSR_Ref TObjectID="16531"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78720" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 16.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78720" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78721" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 16.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78721" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78712" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 16.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78712" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78714" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 16.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78714" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78722" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4412.000000 16.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78722" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16532"/>
     <cge:Term_Ref ObjectID="6945"/>
    <cge:TPSR_Ref TObjectID="16532"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78688" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -10.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78688" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78689" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -10.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78689" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78680" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -10.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78680" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78682" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -10.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78682" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78690" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4583.000000 -10.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78690" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16528"/>
     <cge:Term_Ref ObjectID="6933"/>
    <cge:TPSR_Ref TObjectID="16528"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78731" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 30.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78731" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78732" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 30.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78732" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78723" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 30.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78723" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78725" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 30.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78725" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78733" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4736.000000 30.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78733" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16533"/>
     <cge:Term_Ref ObjectID="6953"/>
    <cge:TPSR_Ref TObjectID="16533"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78764" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -319.000000) translate(0,20)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78764" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78765" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -319.000000) translate(0,45)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78765" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78808" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -319.000000) translate(0,70)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78808" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78810" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -319.000000) translate(0,95)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78810" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78766" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="25" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5000.000000 -319.000000) translate(0,120)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78766" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16537"/>
     <cge:Term_Ref ObjectID="6959"/>
    <cge:TPSR_Ref TObjectID="16537"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-78761" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -821.000000) translate(0,21)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78761" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-78762" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -821.000000) translate(0,47)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78762" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-78754" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -821.000000) translate(0,73)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78754" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-78756" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -821.000000) translate(0,99)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78756" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="2" id="ME-78763" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4719.000000 -821.000000) translate(0,125)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78763" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16536"/>
     <cge:Term_Ref ObjectID="6957"/>
    <cge:TPSR_Ref TObjectID="16536"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78790" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,21)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78790" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78791" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,47)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78791" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78792" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,73)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78792" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78794" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,99)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78794" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-78795" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,125)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78795" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-78796" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3569.000000 -951.000000) translate(0,151)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78796" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17360"/>
     <cge:Term_Ref ObjectID="23899"/>
    <cge:TPSR_Ref TObjectID="17360"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-78798" prefix="Ua  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,21)">Ua   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78798" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-78799" prefix="Ub " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,47)">Ub  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78799" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-78800" prefix="Uc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,73)">Uc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78800" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-78802" prefix="Uab " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,99)">Uab  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78802" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ubc" PreSymbol="0" appendix="" decimal="2" id="ME-78803" prefix="Ubc " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,125)">Ubc  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78803" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uca" PreSymbol="0" appendix="" decimal="2" id="ME-78804" prefix="Uca " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="26" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5186.000000 -604.000000) translate(0,151)">Uca  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="78804" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="17361"/>
     <cge:Term_Ref ObjectID="23900"/>
    <cge:TPSR_Ref TObjectID="17361"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-76493" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -710.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="76493" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16578"/>
     <cge:Term_Ref ObjectID="23964"/>
    <cge:TPSR_Ref TObjectID="16578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="54" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-267431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4707.000000 -642.000000) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="267431" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="16579"/>
     <cge:Term_Ref ObjectID="23971"/>
    <cge:TPSR_Ref TObjectID="16579"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="1" id="ME-296604" prefix="P  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.030612 -0.000000 -0.000000 1.000000 3461.500000 35.000000) translate(0,12)">P   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296604" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46046"/>
     <cge:Term_Ref ObjectID="24396"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="1" id="ME-296605" prefix="Q " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.030612 -0.000000 -0.000000 1.000000 3461.500000 35.000000) translate(0,27)">Q  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296605" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46046"/>
     <cge:Term_Ref ObjectID="24396"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-296601" prefix="Ia  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.030612 -0.000000 -0.000000 1.000000 3461.500000 35.000000) translate(0,42)">Ia   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296601" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46046"/>
     <cge:Term_Ref ObjectID="24396"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ic" PreSymbol="0" appendix="" decimal="2" id="ME-296603" prefix="Ic  " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.030612 -0.000000 -0.000000 1.000000 3461.500000 35.000000) translate(0,57)">Ic   0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296603" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46046"/>
     <cge:Term_Ref ObjectID="24396"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Cos" PreSymbol="0" appendix="" decimal="1" id="ME-296606" prefix="Cos " rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.030612 -0.000000 -0.000000 1.000000 3461.500000 35.000000) translate(0,72)">Cos  0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="296606" ObjectName="NULL:NULL"/>
     <cge:PSR_Ref ObjectID="46046"/>
     <cge:Term_Ref ObjectID="24396"/>
    <cge:TPSR_Ref TObjectID="46046"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="jav" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/></g>
   <g href="jav" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/></g>
   <g href="35kV禄丰大德变2号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="77" x="4922" y="-671"/></g>
   <g href="35kV禄丰大德变10kV和平线086间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4776" y="-450"/></g>
   <g href="35kV禄丰大德变10kV旁路085间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4617" y="-446"/></g>
   <g href="35kV禄丰大德变10kV卸油泵房线083间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4455" y="-447"/></g>
   <g href="35kV禄丰大德变10kV库区线082间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4290" y="-447"/></g>
   <g href="35kV禄丰大德变10kV路溪线081间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3824" y="-447"/></g>
   <g href="35kV禄丰大德变GG虚设备间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="72" x="3165" y="-770"/></g>
   <g href="cx_索引_接线图_局属变35.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3484" y="-1174"/></g>
   <g href="cx_配调_配网接线图35_禄丰.svg" style="fill-opacity:0"><rect height="33" qtmmishow="hidden" width="101" x="3484" y="-1132"/></g>
   <g href="35kV禄丰大德变35kV上大线381间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="3781" y="-969"/></g>
   <g href="35kV禄丰大德变35kV果大T线382间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="18" qtmmishow="hidden" width="30" x="4936" y="-967"/></g>
   <g href="35kV禄丰大德变1号主变间隔间隔接线图.svg" style="fill-opacity:0"><rect height="22" qtmmishow="hidden" width="77" x="3771" y="-671"/></g>
   <g href="35kV禄丰大德变LF_DDB_084间隔间隔接线图_0.svg" style="fill-opacity:0"><rect height="15" qtmmishow="hidden" width="27" x="3496" y="-433"/></g>
  </g><g id="Line_Layer">
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,0,0)" stroke-width="1" x1="5313" x2="5322" y1="-396" y2="-396"/>
   <line DF8003:Layer="PUBLIC" arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,255,255)" stroke-width="1" x1="3528" x2="3528" y1="-464" y2="-464"/>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -374.000000)" xlink:href="#transformer2:shape11_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3953.000000 -374.000000)" xlink:href="#transformer2:shape11_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DDB.LF_DDB_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23966"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -615.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3704.000000 -615.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="16578" ObjectName="TF-LF_DDB.LF_DDB_1T"/>
    <cge:TPSR_Ref TObjectID="16578"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-LF_DDB.LF_DDB_2T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="23970"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4853.000000 -615.000000)" xlink:href="#transformer2:shape10_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-10KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4853.000000 -615.000000)" xlink:href="#transformer2:shape10_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="16579" ObjectName="TF-LF_DDB.LF_DDB_2T"/>
    <cge:TPSR_Ref TObjectID="16579"/></metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="138" x="3248" y="-1177"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="138" x="3248" y="-1177"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="3199" y="-1194"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="3199" y="-1194"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="77" x="4922" y="-671"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="77" x="4922" y="-671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4776" y="-450"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4776" y="-450"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4617" y="-446"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4617" y="-446"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4455" y="-447"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4455" y="-447"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4290" y="-447"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4290" y="-447"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3824" y="-447"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3824" y="-447"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="72" x="3165" y="-770"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="72" x="3165" y="-770"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an8.png" imageHeight="67" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3484" y="-1174"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3484" y="-1174"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/an7.png" imageHeight="65" imageWidth="256">
    <a>
     
     <rect fill="none" height="33" qtmmishow="hidden" width="101" x="3484" y="-1132"/>
    </a>
   <metadata/><rect fill="white" height="33" opacity="0" stroke="white" transform="" width="101" x="3484" y="-1132"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="3781" y="-969"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="3781" y="-969"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="18" qtmmishow="hidden" width="30" x="4936" y="-967"/>
    </a>
   <metadata/><rect fill="white" height="18" opacity="0" stroke="white" transform="" width="30" x="4936" y="-967"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="22" qtmmishow="hidden" width="77" x="3771" y="-671"/>
    </a>
   <metadata/><rect fill="white" height="22" opacity="0" stroke="white" transform="" width="77" x="3771" y="-671"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1">
    <a>
     <rect fill="none" height="15" qtmmishow="hidden" width="27" x="3496" y="-433"/>
    </a>
   <metadata/><rect fill="white" height="15" opacity="0" stroke="white" transform="" width="27" x="3496" y="-433"/></g>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-76266">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -890.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16564" ObjectName="SW-LF_DDB.LF_DDB_3811SW"/>
     <cge:Meas_Ref ObjectId="76266"/>
    <cge:TPSR_Ref TObjectID="16564"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76267">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3762.000000 -982.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16565" ObjectName="SW-LF_DDB.LF_DDB_3816SW"/>
     <cge:Meas_Ref ObjectId="76267"/>
    <cge:TPSR_Ref TObjectID="16565"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -889.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16568" ObjectName="SW-LF_DDB.LF_DDB_3821SW"/>
     <cge:Meas_Ref ObjectId="76294"/>
    <cge:TPSR_Ref TObjectID="16568"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4917.000000 -981.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16569" ObjectName="SW-LF_DDB.LF_DDB_3826SW"/>
     <cge:Meas_Ref ObjectId="76295"/>
    <cge:TPSR_Ref TObjectID="16569"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76232">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4930.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16563" ObjectName="SW-LF_DDB.LF_DDB_30217SW"/>
     <cge:Meas_Ref ObjectId="76232"/>
    <cge:TPSR_Ref TObjectID="16563"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76190">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3733.000000 -822.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16556" ObjectName="SW-LF_DDB.LF_DDB_3011SW"/>
     <cge:Meas_Ref ObjectId="76190"/>
    <cge:TPSR_Ref TObjectID="16556"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76193">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3780.000000 -744.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16559" ObjectName="SW-LF_DDB.LF_DDB_30117SW"/>
     <cge:Meas_Ref ObjectId="76193"/>
    <cge:TPSR_Ref TObjectID="16559"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4422.000000 -822.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16571" ObjectName="SW-LF_DDB.LF_DDB_3901SW"/>
     <cge:Meas_Ref ObjectId="76325"/>
    <cge:TPSR_Ref TObjectID="16571"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4469.000000 -762.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16572" ObjectName="SW-LF_DDB.LF_DDB_39017SW"/>
     <cge:Meas_Ref ObjectId="76328"/>
    <cge:TPSR_Ref TObjectID="16572"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76268">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3782.000000 -1036.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16566" ObjectName="SW-LF_DDB.LF_DDB_38167SW"/>
     <cge:Meas_Ref ObjectId="76268"/>
    <cge:TPSR_Ref TObjectID="16566"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4936.000000 -1095.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16570" ObjectName="SW-LF_DDB.LF_DDB_3829SW"/>
     <cge:Meas_Ref ObjectId="76296"/>
    <cge:TPSR_Ref TObjectID="16570"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76091">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16541" ObjectName="SW-LF_DDB.LF_DDB_0811SW"/>
     <cge:Meas_Ref ObjectId="76091"/>
    <cge:TPSR_Ref TObjectID="16541"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76093">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3804.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16543" ObjectName="SW-LF_DDB.LF_DDB_0816SW"/>
     <cge:Meas_Ref ObjectId="76093"/>
    <cge:TPSR_Ref TObjectID="16543"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76090">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3863.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16540" ObjectName="SW-LF_DDB.LF_DDB_0815SW"/>
     <cge:Meas_Ref ObjectId="76090"/>
    <cge:TPSR_Ref TObjectID="16540"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76348">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4119.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16573" ObjectName="SW-LF_DDB.LF_DDB_0901SW"/>
     <cge:Meas_Ref ObjectId="76348"/>
    <cge:TPSR_Ref TObjectID="16573"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76111">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16545" ObjectName="SW-LF_DDB.LF_DDB_0821SW"/>
     <cge:Meas_Ref ObjectId="76111"/>
    <cge:TPSR_Ref TObjectID="16545"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76113">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4272.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16547" ObjectName="SW-LF_DDB.LF_DDB_0826SW"/>
     <cge:Meas_Ref ObjectId="76113"/>
    <cge:TPSR_Ref TObjectID="16547"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76110">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4330.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16544" ObjectName="SW-LF_DDB.LF_DDB_0825SW"/>
     <cge:Meas_Ref ObjectId="76110"/>
    <cge:TPSR_Ref TObjectID="16544"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76130">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4494.000000 -168.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16548" ObjectName="SW-LF_DDB.LF_DDB_0835SW"/>
     <cge:Meas_Ref ObjectId="76130"/>
    <cge:TPSR_Ref TObjectID="16548"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76131">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -468.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16549" ObjectName="SW-LF_DDB.LF_DDB_0831SW"/>
     <cge:Meas_Ref ObjectId="76131"/>
    <cge:TPSR_Ref TObjectID="16549"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76475">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -229.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16577" ObjectName="SW-LF_DDB.LF_DDB_0855SW"/>
     <cge:Meas_Ref ObjectId="76475"/>
    <cge:TPSR_Ref TObjectID="16577"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76473">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4595.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16575" ObjectName="SW-LF_DDB.LF_DDB_0851SW"/>
     <cge:Meas_Ref ObjectId="76473"/>
    <cge:TPSR_Ref TObjectID="16575"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76150">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4813.000000 -171.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16552" ObjectName="SW-LF_DDB.LF_DDB_0865SW"/>
     <cge:Meas_Ref ObjectId="76150"/>
    <cge:TPSR_Ref TObjectID="16552"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76151">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16553" ObjectName="SW-LF_DDB.LF_DDB_0861SW"/>
     <cge:Meas_Ref ObjectId="76151"/>
    <cge:TPSR_Ref TObjectID="16553"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76231">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5021.000000 -471.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16562" ObjectName="SW-LF_DDB.LF_DDB_0021SW"/>
     <cge:Meas_Ref ObjectId="76231"/>
    <cge:TPSR_Ref TObjectID="16562"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76192">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3606.000000 -470.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16558" ObjectName="SW-LF_DDB.LF_DDB_0011SW"/>
     <cge:Meas_Ref ObjectId="76192"/>
    <cge:TPSR_Ref TObjectID="16558"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76463">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3958.000000 -469.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16574" ObjectName="SW-LF_DDB.LF_DDB_0031SW"/>
     <cge:Meas_Ref ObjectId="76463"/>
    <cge:TPSR_Ref TObjectID="16574"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76229">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4882.000000 -819.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16560" ObjectName="SW-LF_DDB.LF_DDB_3021SW"/>
     <cge:Meas_Ref ObjectId="76229"/>
    <cge:TPSR_Ref TObjectID="16560"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76133">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4436.000000 -230.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16551" ObjectName="SW-LF_DDB.LF_DDB_0833SW"/>
     <cge:Meas_Ref ObjectId="76133"/>
    <cge:TPSR_Ref TObjectID="16551"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-76153">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -234.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="16555" ObjectName="SW-LF_DDB.LF_DDB_0863SW"/>
     <cge:Meas_Ref ObjectId="76153"/>
    <cge:TPSR_Ref TObjectID="16555"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311532">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4755.000000 -52.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48338" ObjectName="SW-LF_DDB.LF_DDB_F0860SW"/>
     <cge:Meas_Ref ObjectId="311532"/>
    <cge:TPSR_Ref TObjectID="48338"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296561">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -467.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46047" ObjectName="SW-LF_DDB.LF_DDB_0841SW"/>
     <cge:Meas_Ref ObjectId="296561"/>
    <cge:TPSR_Ref TObjectID="46047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296562">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3478.000000 -237.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46048" ObjectName="SW-LF_DDB.LF_DDB_0846SW"/>
     <cge:Meas_Ref ObjectId="296562"/>
    <cge:TPSR_Ref TObjectID="46048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296563">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3526.000000 -169.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46049" ObjectName="SW-LF_DDB.LF_DDB_0845SW"/>
     <cge:Meas_Ref ObjectId="296563"/>
    <cge:TPSR_Ref TObjectID="46049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-296564">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3493.000000 -365.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="46050" ObjectName="SW-LF_DDB.LF_DDB_08460SW"/>
     <cge:Meas_Ref ObjectId="296564"/>
    <cge:TPSR_Ref TObjectID="46050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-311531">
    <use class="BV-10KV" dolleyBreakerFlag="0" transform="matrix(0.785714 -0.000000 0.000000 -0.826087 3806.000000 -56.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48337" ObjectName="SW-LF_DDB.LF_DDB_F0810SW"/>
     <cge:Meas_Ref ObjectId="311531"/>
    <cge:TPSR_Ref TObjectID="48337"/></metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 3236.000000 -1118.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80984" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -949.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80984" ObjectName="LF_DDB:LF_DDB_sumP"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-80985" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3260.000000 -902.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="80985" ObjectName="LF_DDB:LF_DDB_sumQ"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-76491" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4079.000000 -686.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="76491" ObjectName="LF_DDB:LF_DDB_1T_QT85"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-76494" ratioFlag="0">
    <text fill="rgb(0,205,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4709.000000 -666.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="76494" ObjectName="LF_DDB:LF_DDB_2T_QT108"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226247" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -1029.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226247" ObjectName="LF_DDB:LF_DDB_sumP1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointCalcAi" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-226247" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="32" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3261.000000 -985.000000) translate(0,26)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="226247" ObjectName="LF_DDB:LF_DDB_sumP1"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="LF_DDB"/>
</svg>