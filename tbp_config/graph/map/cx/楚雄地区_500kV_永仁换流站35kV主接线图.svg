<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-183" aopId="786438" id="thSvg" product="E8000V2" version="1.0" viewBox="3932 -1358 1348 879">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="currentTransformer:shape5">
    <circle cx="36" cy="32" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="21" x2="8" y1="41" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="8" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="2" x2="13" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="11" x2="4" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="9" x2="6" y1="1" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="51" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="14" x2="1" y1="31" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.260875" x1="14" x2="1" y1="24" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="7" x2="7" y1="68" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27102" x1="14" x2="1" y1="58" y2="58"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.284591" x1="14" x2="1" y1="51" y2="51"/>
    <circle cx="28" cy="41" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="33" y1="30" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="39" y1="34" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="33" y1="34" y2="30"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="25" x2="28" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="28" y1="39" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="28" x2="28" y1="41" y2="44"/>
    <circle cx="47" cy="34" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="48" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="48" y1="32" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="34" y2="37"/>
    <circle cx="47" cy="46" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="45" x2="48" y1="44" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="51" x2="48" y1="44" y2="46"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="48" x2="48" y1="46" y2="49"/>
    <circle cx="36" cy="49" r="7.5" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="33" x2="36" y1="48" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="39" x2="36" y1="48" y2="50"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="36" x2="36" y1="50" y2="53"/>
   </symbol>
   <symbol id="earth:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
   </symbol>
   <symbol id="load:shape4">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
   </symbol>
   <symbol id="reactance:shape6">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="24" y1="34" y2="3"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="24" x2="24" y1="68" y2="47"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="24" x2="11" y1="47" y2="47"/>
    <polyline arcFlag="1" points="24,34 26,34 28,35 29,35 31,36 32,37 34,38 35,40 36,42 36,43 37,45 37,47 37,49 36,51 36,52 35,54 34,55 32,57 31,58 29,59 28,59 26,60 24,60 22,60 20,59 19,59 17,58 16,57 14,55 13,54 12,52 12,51 11,49 11,47 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.505618" x1="46" x2="1" y1="3" y2="3"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="transformer2:shape17_0">
    <circle cx="13" cy="34" fillStyle="0" r="13" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape17_1">
    <ellipse cx="13" cy="17" fillStyle="0" rx="13" ry="12.5" stroke-width="0.265306"/>
   </symbol>
   <symbol id="transformer2:shape73_0">
    <ellipse cx="55" cy="60" fillStyle="0" rx="24" ry="24.5" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="2" x2="2" y1="66" y2="63"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="69" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="8" y1="72" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="55" x2="8" y1="65" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="47" x2="55" y1="57" y2="65"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="55" x2="63" y1="65" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="55" x2="55" y1="73" y2="65"/>
   </symbol>
   <symbol id="transformer2:shape73_1">
    <circle cx="55" cy="29" fillStyle="0" r="24" stroke-width="0.510204"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="47" x2="64" y1="18" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="56" x2="64" y1="34" y2="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.510204" x1="56" x2="47" y1="34" y2="18"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_134b1b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_134c050" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_134caa0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_134d870" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_134e700" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_134f300" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_134fd20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_13507e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_143eb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_143eb30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1356410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1356410" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13f62c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_13f62c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_13f72e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_14b67c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_14b7430" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_14b81e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_14b8930" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_165eeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">带电</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_165f6b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_165fda0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1660760" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1661940" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_16622c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_1662db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1663770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_1664d70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_16657c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_16669c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_1667630" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_166db70" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_166e960" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_1668fd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_166a500" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(0,0,0)" height="889" width="1358" x="3927" y="-1363"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127302">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23457" ObjectName="SW-CX_YRH.CX_YRH_312BK"/>
     <cge:Meas_Ref ObjectId="127302"/>
    <cge:TPSR_Ref TObjectID="23457"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127305">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -631.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23460" ObjectName="SW-CX_YRH.CX_YRH_313BK"/>
     <cge:Meas_Ref ObjectId="127305"/>
    <cge:TPSR_Ref TObjectID="23460"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127308">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23463" ObjectName="SW-CX_YRH.CX_YRH_314BK"/>
     <cge:Meas_Ref ObjectId="127308"/>
    <cge:TPSR_Ref TObjectID="23463"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127299">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23454" ObjectName="SW-CX_YRH.CX_YRH_317BK"/>
     <cge:Meas_Ref ObjectId="127299"/>
    <cge:TPSR_Ref TObjectID="23454"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127292">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23447" ObjectName="SW-CX_YRH.CX_YRH_301BK"/>
     <cge:Meas_Ref ObjectId="127292"/>
    <cge:TPSR_Ref TObjectID="23447"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127323">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23478" ObjectName="SW-CX_YRH.CX_YRH_322BK"/>
     <cge:Meas_Ref ObjectId="127323"/>
    <cge:TPSR_Ref TObjectID="23478"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127326">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 -631.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23481" ObjectName="SW-CX_YRH.CX_YRH_323BK"/>
     <cge:Meas_Ref ObjectId="127326"/>
    <cge:TPSR_Ref TObjectID="23481"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127329">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23484" ObjectName="SW-CX_YRH.CX_YRH_324BK"/>
     <cge:Meas_Ref ObjectId="127329"/>
    <cge:TPSR_Ref TObjectID="23484"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127319">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -632.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23474" ObjectName="SW-CX_YRH.CX_YRH_327BK"/>
     <cge:Meas_Ref ObjectId="127319"/>
    <cge:TPSR_Ref TObjectID="23474"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127312">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -835.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23467" ObjectName="SW-CX_YRH.CX_YRH_302BK"/>
     <cge:Meas_Ref ObjectId="127312"/>
    <cge:TPSR_Ref TObjectID="23467"/></metadata>
   </g>
  </g><g id="CurrentTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_11ce0c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3976.000000 -588.000000)" xlink:href="#currentTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1513ec0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4712.000000 -588.000000)" xlink:href="#currentTransformer:shape5"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4072.000000 -544.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4184.000000 -543.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4296.000000 -544.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4808.000000 -544.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4920.000000 -543.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5032.000000 -544.000000)" xlink:href="#reactance:shape6"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -543.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4420.000000 -543.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb1">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33331"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -1010.000000)" xlink:href="#transformer2:shape73_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4090.000000 -1010.000000)" xlink:href="#transformer2:shape73_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23582" ObjectName="TF-CX_YRH.CX_YRH_Zyb1"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5156.000000 -543.000000)" xlink:href="#transformer2:shape17_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5156.000000 -543.000000)" xlink:href="#transformer2:shape17_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="TF-CX_YRH.CX_YRH_Zyb2">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="33335"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -1010.000000)" xlink:href="#transformer2:shape73_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4826.000000 -1010.000000)" xlink:href="#transformer2:shape73_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="23583" ObjectName="TF-CX_YRH.CX_YRH_Zyb2"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-35KV" id="g_1412600">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-787 3984,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23298@0" ObjectIDZND0="23452@x" ObjectIDZND1="23451@x" Pin0InfoVect0LinkObjId="SW-127297_0" Pin0InfoVect1LinkObjId="SW-127296_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-787 3984,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1457fc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-759 3995,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDND1="23451@x" ObjectIDZND0="23452@0" Pin0InfoVect0LinkObjId="SW-127297_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-127296_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-759 3995,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1458380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-759 4045,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23452@1" ObjectIDZND0="g_1531120@0" Pin0InfoVect0LinkObjId="g_1531120_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127297_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-759 4045,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_150f680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-758 3984,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDND1="23452@x" ObjectIDZND0="23451@1" Pin0InfoVect0LinkObjId="SW-127296_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-127297_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-758 3984,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143f440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-701 3984,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="currentTransformer" EndDevType1="switch" ObjectIDND0="23451@0" ObjectIDZND0="g_11ce0c0@0" ObjectIDZND1="23453@x" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="SW-127298_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127296_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-701 3984,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f7620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-679 3984,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" ObjectIDND0="23451@x" ObjectIDND1="23453@x" ObjectIDZND0="g_11ce0c0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127296_0" Pin1InfoVect1LinkObjId="SW-127298_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-679 3984,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1451420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3984,-679 3995,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="currentTransformer" EndDevType0="switch" ObjectIDND0="23451@x" ObjectIDND1="g_11ce0c0@0" ObjectIDZND0="23453@0" Pin0InfoVect0LinkObjId="SW-127298_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127296_0" Pin1InfoVect1LinkObjId="g_11ce0c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="3984,-679 3995,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1451610">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4031,-679 4045,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23453@1" ObjectIDZND0="g_1451800@0" Pin0InfoVect0LinkObjId="g_1451800_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127298_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4031,-679 4045,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a13f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-787 4096,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDZND0="23458@1" Pin0InfoVect0LinkObjId="SW-127303_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-787 4096,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a2c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-695 4107,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23458@x" ObjectIDND1="23457@x" ObjectIDZND0="23459@0" Pin0InfoVect0LinkObjId="SW-127304_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127303_0" Pin1InfoVect1LinkObjId="SW-127302_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-695 4107,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14a2e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4143,-695 4157,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23459@1" ObjectIDZND0="g_143cd20@0" Pin0InfoVect0LinkObjId="g_143cd20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127304_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4143,-695 4157,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_143d920">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-722 4096,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23458@0" ObjectIDZND0="23459@x" ObjectIDZND1="23457@x" Pin0InfoVect0LinkObjId="SW-127304_0" Pin0InfoVect1LinkObjId="SW-127302_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127303_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-722 4096,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14e0260">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-696 4096,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23459@x" ObjectIDND1="23458@x" ObjectIDZND0="23457@1" Pin0InfoVect0LinkObjId="SW-127302_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127304_0" Pin1InfoVect1LinkObjId="SW-127303_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-696 4096,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14e0450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4096,-640 4096,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23457@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127302_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4096,-640 4096,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14e2220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-787 4208,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDZND0="23461@1" Pin0InfoVect0LinkObjId="SW-127306_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-787 4208,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147c640">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-694 4219,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23461@x" ObjectIDND1="23460@x" ObjectIDZND0="23462@0" Pin0InfoVect0LinkObjId="SW-127307_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127306_0" Pin1InfoVect1LinkObjId="SW-127305_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-694 4219,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147c830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4255,-694 4269,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23462@1" ObjectIDZND0="g_147ca20@0" Pin0InfoVect0LinkObjId="g_147ca20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127307_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4255,-694 4269,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147d050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-721 4208,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23461@0" ObjectIDZND0="23462@x" ObjectIDZND1="23460@x" Pin0InfoVect0LinkObjId="SW-127307_0" Pin0InfoVect1LinkObjId="SW-127305_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127306_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-721 4208,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147e530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-695 4208,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23462@x" ObjectIDND1="23461@x" ObjectIDZND0="23460@1" Pin0InfoVect0LinkObjId="SW-127305_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127307_0" Pin1InfoVect1LinkObjId="SW-127306_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-695 4208,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147e720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4208,-639 4208,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23460@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127305_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4208,-639 4208,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_149f380">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-787 4320,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDZND0="23464@1" Pin0InfoVect0LinkObjId="SW-127309_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-787 4320,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ea2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-695 4331,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23464@x" ObjectIDND1="23463@x" ObjectIDZND0="23465@0" Pin0InfoVect0LinkObjId="SW-127310_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127309_0" Pin1InfoVect1LinkObjId="SW-127308_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-695 4331,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ea4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4367,-695 4381,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23465@1" ObjectIDZND0="g_14ea6a0@0" Pin0InfoVect0LinkObjId="g_14ea6a0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127310_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4367,-695 4381,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14eae50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-722 4320,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23464@0" ObjectIDZND0="23465@x" ObjectIDZND1="23463@x" Pin0InfoVect0LinkObjId="SW-127310_0" Pin0InfoVect1LinkObjId="SW-127308_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127309_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-722 4320,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ec670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-696 4320,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23465@x" ObjectIDND1="23464@x" ObjectIDZND0="23463@1" Pin0InfoVect0LinkObjId="SW-127308_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127310_0" Pin1InfoVect1LinkObjId="SW-127309_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-696 4320,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14ec860">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4320,-640 4320,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23463@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127308_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4320,-640 4320,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1492020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-787 4433,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23298@0" ObjectIDZND0="23455@1" Pin0InfoVect0LinkObjId="SW-127300_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-787 4433,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1483110">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-696 4443,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23455@x" ObjectIDND1="23454@x" ObjectIDZND0="23456@0" Pin0InfoVect0LinkObjId="SW-127301_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127300_0" Pin1InfoVect1LinkObjId="SW-127299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-696 4443,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1483300">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4479,-696 4493,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23456@1" ObjectIDZND0="g_14834f0@0" Pin0InfoVect0LinkObjId="g_14834f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127301_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4479,-696 4493,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1483ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-722 4433,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23455@0" ObjectIDZND0="23456@x" ObjectIDZND1="23454@x" Pin0InfoVect0LinkObjId="SW-127301_0" Pin0InfoVect1LinkObjId="SW-127299_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127300_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-722 4433,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14854c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-696 4433,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23456@x" ObjectIDND1="23455@x" ObjectIDZND0="23454@1" Pin0InfoVect0LinkObjId="SW-127299_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127301_0" Pin1InfoVect1LinkObjId="SW-127300_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-696 4433,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14782e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-615 4444,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23454@x" ObjectIDND1="0@x" ObjectIDZND0="23466@0" Pin0InfoVect0LinkObjId="SW-127311_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127299_0" Pin1InfoVect1LinkObjId="g_11ce0c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-615 4444,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14784d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4480,-615 4494,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23466@1" ObjectIDZND0="g_14786f0@0" Pin0InfoVect0LinkObjId="g_14786f0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127311_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4480,-615 4494,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14796e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-640 4433,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="23454@0" ObjectIDZND0="23466@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127311_0" Pin0InfoVect1LinkObjId="g_11ce0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127299_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-640 4433,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147a150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-615 4433,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="23466@x" ObjectIDND1="23454@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127311_0" Pin1InfoVect1LinkObjId="SW-127299_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-615 4433,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_147a370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4433,-548 4433,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="load" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ce0c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4433,-548 4433,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-787 4144,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23298@0" ObjectIDZND0="23447@0" Pin0InfoVect0LinkObjId="SW-127292_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-787 4144,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14f6750">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-895 4205,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23450@1" ObjectIDZND0="g_14f6970@0" Pin0InfoVect0LinkObjId="g_14f6970_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127295_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-895 4205,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13b7100">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-983 4155,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23582@x" ObjectIDND1="23448@x" ObjectIDZND0="23449@0" Pin0InfoVect0LinkObjId="SW-127294_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_14c1bb0_0" Pin1InfoVect1LinkObjId="SW-127293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-983 4155,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13b7320">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4191,-983 4205,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23449@1" ObjectIDZND0="g_13b7540@0" Pin0InfoVect0LinkObjId="g_13b7540_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127294_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4191,-983 4205,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c1bb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-983 4144,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="23449@x" ObjectIDND1="23448@x" ObjectIDZND0="23582@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127294_0" Pin1InfoVect1LinkObjId="SW-127293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-983 4144,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_14c1dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4145,-1095 4145,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="23582@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_14c1bb0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4145,-1095 4145,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14c4500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-787 4720,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23299@0" ObjectIDZND0="23472@x" ObjectIDZND1="23471@x" Pin0InfoVect0LinkObjId="SW-127317_0" Pin0InfoVect1LinkObjId="SW-127316_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-787 4720,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147f450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-759 4731,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDND1="23471@x" ObjectIDZND0="23472@0" Pin0InfoVect0LinkObjId="SW-127317_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-127316_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-759 4731,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_147f670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4767,-759 4781,-759 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23472@1" ObjectIDZND0="g_147f890@0" Pin0InfoVect0LinkObjId="g_147f890_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127317_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4767,-759 4781,-759 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1482350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-758 4720,-737 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDND1="23472@x" ObjectIDZND0="23471@1" Pin0InfoVect0LinkObjId="SW-127316_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="SW-127317_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-758 4720,-737 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1482570">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-701 4720,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="currentTransformer" EndDevType1="switch" ObjectIDND0="23471@0" ObjectIDZND0="g_1513ec0@0" ObjectIDZND1="23473@x" Pin0InfoVect0LinkObjId="g_1513ec0_0" Pin0InfoVect1LinkObjId="SW-127318_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127316_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-701 4720,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1482790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-679 4720,-657 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="currentTransformer" ObjectIDND0="23471@x" ObjectIDND1="23473@x" ObjectIDZND0="g_1513ec0@0" Pin0InfoVect0LinkObjId="g_1513ec0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127316_0" Pin1InfoVect1LinkObjId="SW-127318_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-679 4720,-657 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1513150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4720,-679 4731,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="currentTransformer" EndDevType0="switch" ObjectIDND0="23471@x" ObjectIDND1="g_1513ec0@0" ObjectIDZND0="23473@0" Pin0InfoVect0LinkObjId="SW-127318_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127316_0" Pin1InfoVect1LinkObjId="g_1513ec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4720,-679 4731,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1513370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4767,-679 4781,-679 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23473@1" ObjectIDZND0="g_1513590@0" Pin0InfoVect0LinkObjId="g_1513590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127318_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4767,-679 4781,-679 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14934c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-787 4832,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDZND0="23479@1" Pin0InfoVect0LinkObjId="SW-127324_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-787 4832,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14955e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-695 4843,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23479@x" ObjectIDND1="23478@x" ObjectIDZND0="23480@0" Pin0InfoVect0LinkObjId="SW-127325_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127324_0" Pin1InfoVect1LinkObjId="SW-127323_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-695 4843,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1495800">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4879,-695 4893,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23480@1" ObjectIDZND0="g_1495a20@0" Pin0InfoVect0LinkObjId="g_1495a20_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127325_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4879,-695 4893,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1496350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-722 4832,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23479@0" ObjectIDZND0="23480@x" ObjectIDZND1="23478@x" Pin0InfoVect0LinkObjId="SW-127325_0" Pin0InfoVect1LinkObjId="SW-127323_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127324_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-722 4832,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ee970">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-696 4832,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23480@x" ObjectIDND1="23479@x" ObjectIDZND0="23478@1" Pin0InfoVect0LinkObjId="SW-127323_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127325_0" Pin1InfoVect1LinkObjId="SW-127324_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-696 4832,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13eeb90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4832,-640 4832,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23478@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127323_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4832,-640 4832,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f1340">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-787 4944,-757 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDZND0="23482@1" Pin0InfoVect0LinkObjId="SW-127327_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-787 4944,-757 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f19c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-694 4955,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23482@x" ObjectIDND1="23481@x" ObjectIDZND0="23483@0" Pin0InfoVect0LinkObjId="SW-127328_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127327_0" Pin1InfoVect1LinkObjId="SW-127326_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-694 4955,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f1bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4991,-694 5005,-694 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23483@1" ObjectIDZND0="g_13f1e50@0" Pin0InfoVect0LinkObjId="g_13f1e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127328_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4991,-694 5005,-694 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13f2880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-721 4944,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23482@0" ObjectIDZND0="23483@x" ObjectIDZND1="23481@x" Pin0InfoVect0LinkObjId="SW-127328_0" Pin0InfoVect1LinkObjId="SW-127326_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127327_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-721 4944,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1382480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-695 4944,-666 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23483@x" ObjectIDND1="23482@x" ObjectIDZND0="23481@1" Pin0InfoVect0LinkObjId="SW-127326_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127328_0" Pin1InfoVect1LinkObjId="SW-127327_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-695 4944,-666 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1382700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4944,-639 4944,-611 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23481@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127326_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4944,-639 4944,-611 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1383690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-787 5056,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDZND0="23485@1" Pin0InfoVect0LinkObjId="SW-127330_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-787 5056,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1383d10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-695 5067,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23485@x" ObjectIDND1="23484@x" ObjectIDZND0="23486@0" Pin0InfoVect0LinkObjId="SW-127331_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127330_0" Pin1InfoVect1LinkObjId="SW-127329_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-695 5067,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1383f70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5103,-695 5117,-695 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23486@1" ObjectIDZND0="g_13841d0@0" Pin0InfoVect0LinkObjId="g_13841d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127331_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5103,-695 5117,-695 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1384c00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-722 5056,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23485@0" ObjectIDZND0="23486@x" ObjectIDZND1="23484@x" Pin0InfoVect0LinkObjId="SW-127331_0" Pin0InfoVect1LinkObjId="SW-127329_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127330_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-722 5056,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1385220">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-696 5056,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23486@x" ObjectIDND1="23485@x" ObjectIDZND0="23484@1" Pin0InfoVect0LinkObjId="SW-127329_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127331_0" Pin1InfoVect1LinkObjId="SW-127330_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-696 5056,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1385480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5056,-640 5056,-612 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="reactance" ObjectIDND0="23484@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127329_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5056,-640 5056,-612 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1407c80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-787 5169,-758 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="switch" ObjectIDND0="23299@0" ObjectIDZND0="23475@1" Pin0InfoVect0LinkObjId="SW-127320_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-787 5169,-758 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_140a410">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-696 5179,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23475@x" ObjectIDND1="23474@x" ObjectIDZND0="23476@0" Pin0InfoVect0LinkObjId="SW-127321_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127320_0" Pin1InfoVect1LinkObjId="SW-127319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-696 5179,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_140a670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5215,-696 5229,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23476@1" ObjectIDZND0="g_13b9df0@0" Pin0InfoVect0LinkObjId="g_13b9df0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127321_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5215,-696 5229,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ba880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-722 5169,-696 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="23475@0" ObjectIDZND0="23476@x" ObjectIDZND1="23474@x" Pin0InfoVect0LinkObjId="SW-127321_0" Pin0InfoVect1LinkObjId="SW-127319_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127320_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-722 5169,-696 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13bcbb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-696 5169,-667 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="23476@x" ObjectIDND1="23475@x" ObjectIDZND0="23474@1" Pin0InfoVect0LinkObjId="SW-127319_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127321_0" Pin1InfoVect1LinkObjId="SW-127320_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-696 5169,-667 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1497a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-615 5180,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23474@x" ObjectIDND1="0@x" ObjectIDZND0="23477@0" Pin0InfoVect0LinkObjId="SW-127322_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127319_0" Pin1InfoVect1LinkObjId="g_11ce0c0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-615 5180,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1497c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5216,-615 5230,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23477@1" ObjectIDZND0="g_1497ed0@0" Pin0InfoVect0LinkObjId="g_1497ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127322_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5216,-615 5230,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1498960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-640 5169,-615 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="23474@0" ObjectIDZND0="23477@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-127322_0" Pin0InfoVect1LinkObjId="g_11ce0c0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127319_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-640 5169,-615 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14994d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-615 5169,-590 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="transformer2" ObjectIDND0="23477@x" ObjectIDND1="23474@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127322_0" Pin1InfoVect1LinkObjId="SW-127319_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-615 5169,-590 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1499730">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="5169,-548 5169,-523 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="load" ObjectIDND0="0@1" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="g_11ce0c0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_11ce0c0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="5169,-548 5169,-523 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14aee20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-787 4880,-843 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="busSection" EndDevType0="breaker" ObjectIDND0="23299@0" ObjectIDZND0="23467@0" Pin0InfoVect0LinkObjId="SW-127312_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-787 4880,-843 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b15b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-903 4891,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23467@x" ObjectIDND1="23468@x" ObjectIDZND0="23470@0" Pin0InfoVect0LinkObjId="SW-127315_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127312_0" Pin1InfoVect1LinkObjId="SW-127313_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-903 4891,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b1810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4927,-903 4941,-903 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23470@1" ObjectIDZND0="g_14b1a70@0" Pin0InfoVect0LinkObjId="g_14b1a70_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127315_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4927,-903 4941,-903 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14b2500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-870 4880,-904 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23467@1" ObjectIDZND0="23470@x" ObjectIDZND1="23468@x" Pin0InfoVect0LinkObjId="SW-127315_0" Pin0InfoVect1LinkObjId="SW-127313_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127312_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-870 4880,-904 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d2680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-904 4880,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="23470@x" ObjectIDND1="23467@x" ObjectIDZND0="23468@0" Pin0InfoVect0LinkObjId="SW-127313_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127315_0" Pin1InfoVect1LinkObjId="SW-127312_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-904 4880,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d4e10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-983 4891,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="transformer2" EndDevType0="switch" ObjectIDND0="23468@x" ObjectIDND1="23583@x" ObjectIDZND0="23469@0" Pin0InfoVect0LinkObjId="SW-127314_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127313_0" Pin1InfoVect1LinkObjId="g_1440be0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-983 4891,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5070">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4927,-983 4941,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="earth" ObjectIDND0="23469@1" ObjectIDZND0="g_14d52d0@0" Pin0InfoVect0LinkObjId="g_14d52d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127314_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4927,-983 4941,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_14d5d60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-961 4880,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="23468@1" ObjectIDZND0="23469@x" ObjectIDZND1="23583@x" Pin0InfoVect0LinkObjId="SW-127314_0" Pin0InfoVect1LinkObjId="g_1440be0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127313_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-961 4880,-983 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_1440be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4880,-983 4880,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="transformer2" ObjectIDND0="23469@x" ObjectIDND1="23468@x" ObjectIDZND0="23583@1" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127314_0" Pin1InfoVect1LinkObjId="SW-127313_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4880,-983 4880,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_1440e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4881,-1095 4881,-1150 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" ObjectIDND0="23583@0" Pin0InfoVect0LinkObjId="" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_1440be0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="0"/></metadata>
    <polyline fill="none" opacity="0" points="4881,-1095 4881,-1150 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ec310">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-870 4144,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="23447@1" ObjectIDZND0="23450@x" ObjectIDZND1="23448@x" Pin0InfoVect0LinkObjId="SW-127295_0" Pin0InfoVect1LinkObjId="SW-127293_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127292_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-870 4144,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ec500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-895 4155,-895 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23447@x" ObjectIDND1="23448@x" ObjectIDZND0="23450@0" Pin0InfoVect0LinkObjId="SW-127295_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127292_0" Pin1InfoVect1LinkObjId="SW-127293_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-895 4155,-895 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ec6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-895 4144,-925 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="23447@x" ObjectIDND1="23450@x" ObjectIDZND0="23448@0" Pin0InfoVect0LinkObjId="SW-127293_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-127292_0" Pin1InfoVect1LinkObjId="SW-127295_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-895 4144,-925 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_13ec900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4144,-961 4144,-983 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="transformer2" ObjectIDND0="23448@1" ObjectIDZND0="23449@x" ObjectIDZND1="23582@x" Pin0InfoVect0LinkObjId="SW-127294_0" Pin0InfoVect1LinkObjId="g_14c1bb0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-127293_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="4144,-961 4144,-983 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="3984" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="4096" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="4208" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="4320" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="4433" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23298" cx="4144" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="4832" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="4944" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="5056" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="5169" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="4880" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="23299" cx="4720" cy="-787" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimSun" font-size="38" graphid="g_153b8d0" transform="matrix(0.682848 -0.000000 -0.000000 0.741176 4454.555016 -1319.582353) translate(0,31)">500kV永仁换流站35kV主接线图</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_147aea0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4058.000000 -539.000000) translate(0,15)">312电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1323c00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4170.000000 -539.000000) translate(0,15)">313电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_128b9d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4284.000000 -539.000000) translate(0,15)">314电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14ed3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4387.000000 -497.000000) translate(0,15)">35kV#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14c1ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4183.000000 -1060.000000) translate(0,15)">500kV #1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14c4120" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4046.000000 -1180.000000) translate(0,15)">至永仁站500kV交流场第一串</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_147f280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3936.000000 -809.000000) translate(0,15)">35kV #1M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_149a400" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4794.000000 -539.000000) translate(0,15)">322电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_149a990" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4906.000000 -539.000000) translate(0,15)">323电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_149abd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5020.000000 -539.000000) translate(0,15)">324电抗器</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_149ae10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5123.000000 -497.000000) translate(0,15)">35kV#2站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1441060" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4919.000000 -1060.000000) translate(0,15)">500kV #2站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_1441560" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -1180.000000) translate(0,15)">至永仁站500kV #1M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="18" graphid="g_14417a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4668.000000 -809.000000) translate(0,15)">35kV #2M</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ea6c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -866.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eae50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4151.000000 -949.000000) translate(0,12)">3016</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eb1d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -1007.000000) translate(0,12)">301617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13eb730" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4153.000000 -919.000000) translate(0,12)">30167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ecb30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4889.000000 -865.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ed0e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4887.000000 -951.000000) translate(0,12)">3026</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ed540" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4888.000000 -926.000000) translate(0,12)">30267</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ed780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4885.000000 -1006.000000) translate(0,12)">302617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ed9c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 -725.000000) translate(0,12)">319</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13edd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -674.000000) translate(0,12)">31927</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ee160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3994.000000 -754.000000) translate(0,12)">31917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ee3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -661.000000) translate(0,12)">312</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_13ee5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4103.000000 -748.000000) translate(0,12)">3121</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146d880" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4105.000000 -718.000000) translate(0,12)">31217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146dac0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -660.000000) translate(0,12)">313</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146dd00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4215.000000 -746.000000) translate(0,12)">3131</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146df40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4217.000000 -717.000000) translate(0,12)">31317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146e180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4329.000000 -661.000000) translate(0,12)">314</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146e3c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4327.000000 -747.000000) translate(0,12)">3141</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146e600" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4330.000000 -718.000000) translate(0,12)">31417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146e840" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -661.000000) translate(0,12)">317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146ea80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4442.000000 -638.000000) translate(0,12)">31727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146ecc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4440.000000 -747.000000) translate(0,12)">3171</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146ef00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4441.000000 -719.000000) translate(0,12)">31717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146f140" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4727.000000 -725.000000) translate(0,12)">329</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146f380" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4728.000000 -674.000000) translate(0,12)">32927</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146f5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4729.000000 -754.000000) translate(0,12)">32917</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146f800" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4841.000000 -661.000000) translate(0,12)">322</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146fa40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4837.000000 -747.000000) translate(0,12)">3221</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146fc80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4844.000000 -690.000000) translate(0,12)">32217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_146fec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -660.000000) translate(0,12)">323</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470100" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4953.000000 -690.000000) translate(0,12)">32317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470340" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4951.000000 -746.000000) translate(0,12)">3231</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470580" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -661.000000) translate(0,12)">324</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14707c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5063.000000 -747.000000) translate(0,12)">3241</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5065.000000 -691.000000) translate(0,12)">32417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470c40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5178.000000 -661.000000) translate(0,12)">327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1470e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -611.000000) translate(0,12)">32727</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14710c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5176.000000 -747.000000) translate(0,12)">3271</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1471300" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5181.000000 -692.000000) translate(0,12)">32717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a9190" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4185.000000 -1108.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa230" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4933.000000 -1096.000000) translate(0,12)">档位：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14aa8a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3989.000000 -500.000000) translate(0,12)">Q(MVar):</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14b4480" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4732.000000 -506.000000) translate(0,12)">Q(MVar):</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-127297">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -754.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23452" ObjectName="SW-CX_YRH.CX_YRH_31917SW"/>
     <cge:Meas_Ref ObjectId="127297"/>
    <cge:TPSR_Ref TObjectID="23452"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127296">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3975.000000 -696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23451" ObjectName="SW-CX_YRH.CX_YRH_319SW"/>
     <cge:Meas_Ref ObjectId="127296"/>
    <cge:TPSR_Ref TObjectID="23451"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127298">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3990.000000 -674.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23453" ObjectName="SW-CX_YRH.CX_YRH_31927SW"/>
     <cge:Meas_Ref ObjectId="127298"/>
    <cge:TPSR_Ref TObjectID="23453"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127303">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4087.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23458" ObjectName="SW-CX_YRH.CX_YRH_3121SW"/>
     <cge:Meas_Ref ObjectId="127303"/>
    <cge:TPSR_Ref TObjectID="23458"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127304">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4102.000000 -690.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23459" ObjectName="SW-CX_YRH.CX_YRH_31217SW"/>
     <cge:Meas_Ref ObjectId="127304"/>
    <cge:TPSR_Ref TObjectID="23459"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127306">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4199.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23461" ObjectName="SW-CX_YRH.CX_YRH_3131SW"/>
     <cge:Meas_Ref ObjectId="127306"/>
    <cge:TPSR_Ref TObjectID="23461"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127307">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4214.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23462" ObjectName="SW-CX_YRH.CX_YRH_31317SW"/>
     <cge:Meas_Ref ObjectId="127307"/>
    <cge:TPSR_Ref TObjectID="23462"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127309">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4311.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23464" ObjectName="SW-CX_YRH.CX_YRH_3141SW"/>
     <cge:Meas_Ref ObjectId="127309"/>
    <cge:TPSR_Ref TObjectID="23464"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127310">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4326.000000 -690.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23465" ObjectName="SW-CX_YRH.CX_YRH_31417SW"/>
     <cge:Meas_Ref ObjectId="127310"/>
    <cge:TPSR_Ref TObjectID="23465"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127300">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4424.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23455" ObjectName="SW-CX_YRH.CX_YRH_3171SW"/>
     <cge:Meas_Ref ObjectId="127300"/>
    <cge:TPSR_Ref TObjectID="23455"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127301">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4438.000000 -691.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23456" ObjectName="SW-CX_YRH.CX_YRH_31717SW"/>
     <cge:Meas_Ref ObjectId="127301"/>
    <cge:TPSR_Ref TObjectID="23456"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127311">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4439.000000 -610.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23466" ObjectName="SW-CX_YRH.CX_YRH_31727SW"/>
     <cge:Meas_Ref ObjectId="127311"/>
    <cge:TPSR_Ref TObjectID="23466"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127295">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -890.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23450" ObjectName="SW-CX_YRH.CX_YRH_30167SW"/>
     <cge:Meas_Ref ObjectId="127295"/>
    <cge:TPSR_Ref TObjectID="23450"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127293">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4135.000000 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23448" ObjectName="SW-CX_YRH.CX_YRH_3016SW"/>
     <cge:Meas_Ref ObjectId="127293"/>
    <cge:TPSR_Ref TObjectID="23448"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127294">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4150.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23449" ObjectName="SW-CX_YRH.CX_YRH_301617SW"/>
     <cge:Meas_Ref ObjectId="127294"/>
    <cge:TPSR_Ref TObjectID="23449"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127317">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -754.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23472" ObjectName="SW-CX_YRH.CX_YRH_32917SW"/>
     <cge:Meas_Ref ObjectId="127317"/>
    <cge:TPSR_Ref TObjectID="23472"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127316">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4711.000000 -696.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23471" ObjectName="SW-CX_YRH.CX_YRH_329SW"/>
     <cge:Meas_Ref ObjectId="127316"/>
    <cge:TPSR_Ref TObjectID="23471"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127318">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4726.000000 -674.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23473" ObjectName="SW-CX_YRH.CX_YRH_32927SW"/>
     <cge:Meas_Ref ObjectId="127318"/>
    <cge:TPSR_Ref TObjectID="23473"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127324">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4823.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23479" ObjectName="SW-CX_YRH.CX_YRH_3221SW"/>
     <cge:Meas_Ref ObjectId="127324"/>
    <cge:TPSR_Ref TObjectID="23479"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127325">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4838.000000 -690.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23480" ObjectName="SW-CX_YRH.CX_YRH_32217SW"/>
     <cge:Meas_Ref ObjectId="127325"/>
    <cge:TPSR_Ref TObjectID="23480"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127327">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4935.000000 -716.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23482" ObjectName="SW-CX_YRH.CX_YRH_3231SW"/>
     <cge:Meas_Ref ObjectId="127327"/>
    <cge:TPSR_Ref TObjectID="23482"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127328">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4950.000000 -689.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23483" ObjectName="SW-CX_YRH.CX_YRH_32317SW"/>
     <cge:Meas_Ref ObjectId="127328"/>
    <cge:TPSR_Ref TObjectID="23483"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127330">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5047.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23485" ObjectName="SW-CX_YRH.CX_YRH_3241SW"/>
     <cge:Meas_Ref ObjectId="127330"/>
    <cge:TPSR_Ref TObjectID="23485"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127331">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5062.000000 -690.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23486" ObjectName="SW-CX_YRH.CX_YRH_32417SW"/>
     <cge:Meas_Ref ObjectId="127331"/>
    <cge:TPSR_Ref TObjectID="23486"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127320">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5160.000000 -717.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23475" ObjectName="SW-CX_YRH.CX_YRH_3271SW"/>
     <cge:Meas_Ref ObjectId="127320"/>
    <cge:TPSR_Ref TObjectID="23475"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127321">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5174.000000 -691.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23476" ObjectName="SW-CX_YRH.CX_YRH_32717SW"/>
     <cge:Meas_Ref ObjectId="127321"/>
    <cge:TPSR_Ref TObjectID="23476"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127322">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5175.000000 -610.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23477" ObjectName="SW-CX_YRH.CX_YRH_32727SW"/>
     <cge:Meas_Ref ObjectId="127322"/>
    <cge:TPSR_Ref TObjectID="23477"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127315">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 -898.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23470" ObjectName="SW-CX_YRH.CX_YRH_30267SW"/>
     <cge:Meas_Ref ObjectId="127315"/>
    <cge:TPSR_Ref TObjectID="23470"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127313">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4871.000000 -920.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23468" ObjectName="SW-CX_YRH.CX_YRH_3026SW"/>
     <cge:Meas_Ref ObjectId="127313"/>
    <cge:TPSR_Ref TObjectID="23468"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-127314">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4886.000000 -978.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="23469" ObjectName="SW-CX_YRH.CX_YRH_302617SW"/>
     <cge:Meas_Ref ObjectId="127314"/>
    <cge:TPSR_Ref TObjectID="23469"/></metadata>
   </g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4428.000000 -502.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5164.000000 -502.000000)" xlink:href="#load:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(2.048544 -0.000000 -0.000000 1.730747 4531.716828 -1255.851192) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127059" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -1143.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127059" ObjectName="CX_YRH:CX_YRH_Zyb1_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127060" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -1128.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127060" ObjectName="CX_YRH:CX_YRH_Zyb1_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127058" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4090.000000 -1113.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127058" ObjectName="CX_YRH:CX_YRH_Zyb1_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127072" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 3999.000000 -831.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127072" ObjectName="CX_YRH:CX_YRH_3IM_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127067" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -1129.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127067" ObjectName="CX_YRH:CX_YRH_Zyb2_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127065" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -1114.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127065" ObjectName="CX_YRH:CX_YRH_Zyb2_Ia"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127066" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4831.000000 -1144.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127066" ObjectName="CX_YRH:CX_YRH_Zyb2_P"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127063" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -865.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127063" ObjectName="CX_YRH:CX_YRH_Zyb1_Q1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127061" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -850.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127061" ObjectName="CX_YRH:CX_YRH_Zyb1_Ia1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127062" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4258.000000 -880.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127062" ObjectName="CX_YRH:CX_YRH_Zyb1_P1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127068" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -859.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127068" ObjectName="CX_YRH:CX_YRH_Zyb2_Ia1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127069" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -889.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127069" ObjectName="CX_YRH:CX_YRH_Zyb2_P1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127070" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4806.000000 -874.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127070" ObjectName="CX_YRH:CX_YRH_Zyb2_Q1"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127073" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4723.000000 -831.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127073" ObjectName="CX_YRH:CX_YRH_3IIM_U"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127064" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4241.000000 -1109.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127064" ObjectName="CX_YRH:CX_YRH_Zyb1_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127071" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4989.000000 -1097.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127071" ObjectName="CX_YRH:CX_YRH_Zyb2_Tap"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127074" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4067.000000 -500.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127074" ObjectName="CX_YRH:CX_YRH_312BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127075" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4195.000000 -500.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127075" ObjectName="CX_YRH:CX_YRH_313BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127076" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4309.000000 -500.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127076" ObjectName="CX_YRH:CX_YRH_314BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127077" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4810.000000 -506.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127077" ObjectName="CX_YRH:CX_YRH_322BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127078" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 4938.000000 -506.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127078" ObjectName="CX_YRH:CX_YRH_323BK_Q"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-127079" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 5052.000000 -506.000000) translate(0,12)">9.90</text>
    <metadata>
     <cge:Meas_Ref ObjectID="127079" ObjectName="CX_YRH:CX_YRH_324BK_Q"/>
    </metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="62" qtmmishow="hidden" width="422" x="4403" y="-1337"/></g>
   <g href="楚雄地区_500kV_永仁换流站.svg" style="fill-opacity:0"><rect height="97" qtmmishow="hidden" width="117" x="4336" y="-1358"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14724e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4047.000000 1114.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14736b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4033.000000 1144.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_1474240" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4022.000000 1129.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a3d00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3932.000000 830.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a5210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4788.000000 1115.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a5470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4774.000000 1145.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a56b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4763.000000 1130.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a6670" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4217.000000 851.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a68d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4203.000000 881.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a6b10" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4192.000000 866.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a76a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4763.000000 860.000000) translate(0,12)">Ia(A):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7900" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4749.000000 890.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a7b40" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4738.000000 875.000000) translate(0,12)">Q(MVar):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_14a8b00" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4656.000000 830.000000) translate(0,12)">Uab(kV):</text>
   <metadata/></g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_1531120" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1451800" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4041.000000 -673.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_143cd20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4153.000000 -689.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_147ca20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4265.000000 -688.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14ea6a0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4377.000000 -689.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14834f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4489.000000 -690.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14786f0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4490.000000 -609.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14f6970" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -889.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b7540" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4201.000000 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_147f890" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4777.000000 -753.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1513590" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4777.000000 -673.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1495a20" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4889.000000 -689.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13f1e50" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5001.000000 -688.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13841d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5113.000000 -689.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_13b9df0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5225.000000 -690.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_1497ed0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5226.000000 -609.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14b1a70" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 -897.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_14d52d0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4937.000000 -977.000000)" xlink:href="#earth:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_3IM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="3936,-787 4544,-787 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23298" ObjectName="BS-CX_YRH.CX_YRH_3IM"/>
    <cge:TPSR_Ref TObjectID="23298"/></metadata>
   <polyline fill="none" opacity="0" points="3936,-787 4544,-787 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YRH.CX_YRH_3IIM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="4672,-787 5280,-787 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="23299" ObjectName="BS-CX_YRH.CX_YRH_3IIM"/>
    <cge:TPSR_Ref TObjectID="23299"/></metadata>
   <polyline fill="none" opacity="0" points="4672,-787 5280,-787 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="62" qtmmishow="hidden" width="422" x="4403" y="-1337"/>
    </a>
   <metadata/><rect fill="white" height="62" opacity="0" stroke="white" transform="" width="422" x="4403" y="-1337"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="97" qtmmishow="hidden" width="117" x="4336" y="-1358"/>
    </a>
   <metadata/><rect fill="white" height="97" opacity="0" stroke="white" transform="" width="117" x="4336" y="-1358"/></g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1"/>
</svg>