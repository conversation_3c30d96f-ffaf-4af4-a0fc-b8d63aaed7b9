<?xml version="1.0" encoding="UTF-8" standalone="no"?><svg xmlns="http://www.w3.org/2000/svg" xmlns:DF8003="http://DF8003.com/SVGEX-schema#" xmlns:cge="http://iec.ch/TC57/2005/SVG-schema#" xmlns:cim="http://iec.ch/TC57/2003/CIM-schema-cim10#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:xlink="http://www.w3.org/1999/xlink" MapType="fac" StationID="SS-343" aopId="786686" id="thSvg" product="E8000V2" version="1.0" viewBox="-358 -1182 2576 1632">
 
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
 <defs>
   
   <symbol id="breaker2:shape0_0">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0_1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor1">
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
   </symbol>
   <symbol id="breaker2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="15" x2="3" y1="35" y2="10"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="14" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="3" x2="16" y1="35" y2="10"/>
    <rect height="27" stroke-width="0.416609" width="14" x="2" y="9"/>
   </symbol>
   <symbol id="breaker2:shape10_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="0" y1="13" y2="29"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
   </symbol>
   <symbol id="breaker2:shape10-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="13" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="34" y2="28"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="12" y1="28" y2="34"/>
   </symbol>
   <symbol id="dynamicPoint:shape32">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape33">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
   </symbol>
   <symbol id="dynamicPoint:shape34">
    <rect fill="none" height="26" stroke="rgb(0,255,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(0,255,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(0,255,0)" fillStyle="1" r="9" stroke="rgb(0,255,0)" stroke-width="1"/>
   </symbol>
   <symbol id="dynamicPoint:shape35">
    <rect fill="none" height="26" stroke="rgb(255,0,0)" stroke-width="2.49996" width="26" x="2" y="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="15" x2="15" y1="28" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,0,0)" stroke-width="2.43243" x1="2" x2="28" y1="15" y2="15"/>
    <circle cx="15" cy="15" fill="rgb(255,0,0)" fillStyle="1" r="9" stroke="rgb(255,0,0)" stroke-width="1"/>
   </symbol>
   <symbol id="earth:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="9" x2="9" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="2" x2="2" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="5" x2="5" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="9" x2="18" y1="6" y2="6"/>
   </symbol>
   <symbol id="earth:shape0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="12" x2="0" y1="9" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="7" x2="5" y1="2" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="9" x2="3" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.251748" x1="6" x2="6" y1="9" y2="18"/>
   </symbol>
   <symbol id="lightningRod:shape77">
    <rect height="13" stroke-width="0.424575" width="29" x="20" y="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="63" x2="63" y1="6" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="61" x2="61" y1="4" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="58" x2="58" y1="13" y2="2"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="58" x2="49" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="4" x2="43" y1="8" y2="8"/>
   </symbol>
   <symbol id="lightningRod:shape174">
    <rect height="18" stroke-width="1.1697" width="11" x="1" y="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="14" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2.1208" x1="7" x2="7" y1="39" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.447552" x1="7" x2="7" y1="7" y2="14"/>
   </symbol>
   <symbol id="lightningRod:shape139">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.27195" x1="7" x2="7" y1="6" y2="15"/>
    <rect height="25" stroke-width="1" width="12" x="1" y="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="48" y2="23"/>
   </symbol>
   <symbol id="lightningRod:shape126">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="5" x2="5" y1="46" y2="29"/>
   </symbol>
   <symbol id="lightningRod:shape4">
    <rect height="31" stroke-width="2" width="16" x="1" y="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="35" y2="35"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="9" x2="9" y1="35" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape67">
    <rect height="27" stroke-width="1" width="14" x="0" y="18"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.1875" x1="5" x2="8" y1="60" y2="60"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.344531" x1="3" x2="10" y1="57" y2="57"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.305149" x1="7" x2="7" y1="54" y2="45"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.560509" x1="12" x2="1" y1="54" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="5" y2="40"/>
   </symbol>
   <symbol id="lightningRod:shape65">
    <line arrowAngle="40" arrowLength="12" arrowPos="2" arrowType="0" stroke-width="1" x1="7" x2="7" y1="55" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="13" x2="1" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.223776" x1="8" x2="8" y1="8" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="3" x2="11" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="6" x2="9" y1="2" y2="2"/>
    <rect height="28" stroke-width="1" width="14" x="0" y="15"/>
   </symbol>
   <symbol id="lightningRod:shape188">
    <polyline DF8003:Layer="PUBLIC" points="19,18 10,0 1,19 19,18 " stroke-width="1"/>
   </symbol>
   <symbol id="lightningRod:shape157">
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,36 0,46 10,46 5,36 " stroke-width="1"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="5,24 0,14 10,14 5,24 " stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="59" y2="6"/>
   </symbol>
   <symbol id="lightningRod:shape146">
    <rect height="19" stroke-width="1" width="35" x="0" y="0"/>
    <polyline points="17,19 17,30 " stroke-width="1"/>
    <text font-family="SimSun" font-size="15" graphid="g_359ba30" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 17.000000) translate(0,12)">SVG</text>
   </symbol>
   <symbol id="load:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="15" y2="15"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="6" y2="15"/>
    <polyline DF8003:Layer="PUBLIC" points="1,15 10,15 5,25 0,15 1,15 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="15" y2="25"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="15" y2="25"/>
   </symbol>
   <symbol id="load:shape3">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="10" y1="11" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="0" x2="5" y1="11" y2="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="10" x2="5" y1="11" y2="1"/>
    <polyline DF8003:Layer="PUBLIC" points="1,11 10,11 5,1 0,11 1,11 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="5" y1="20" y2="11"/>
   </symbol>
   <symbol id="reactance:shape2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.332308" x1="13" x2="13" y1="39" y2="47"/>
    <polyline points="13,39 15,39 17,38 18,38 20,37 21,36 23,35 24,33 25,31 25,30 26,28 26,26 26,24 25,22 25,21 24,19 23,18 21,16 20,15 18,14 17,14 15,13 13,13 11,13 9,14 8,14 6,15 5,16 3,18 2,19 1,21 1,22 0,24 0,26 " stroke-width="0.0972"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.48" x1="0" x2="12" y1="26" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.44" x1="13" x2="13" y1="5" y2="26"/>
   </symbol>
   <symbol id="switch2:shape1_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="4" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="31" y1="5" y2="13"/>
   </symbol>
   <symbol id="switch2:shape1-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="41" x2="32" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="6" y2="4"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="14" x2="33" y1="5" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="5" x2="14" y1="5" y2="5"/>
   </symbol>
   <symbol id="switch2:shape7_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.651584" x1="18" x2="43" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="18" x2="9" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="2" x2="2" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="5" x2="5" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="9" x2="9" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape7_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape7-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="42" x2="42" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="51" x2="42" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="3" x2="3" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="6" x2="6" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.941176" x1="10" x2="10" y1="14" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="2" x1="43" x2="10" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape0_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="1" y1="14" y2="31"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="4" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="33"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="33" y2="14"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="1" x2="9" y1="31" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
   </symbol>
   <symbol id="switch2:shape0-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="41" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="8" x2="10" y1="32" y2="32"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="9" x2="9" y1="14" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.888889" x1="9" x2="9" y1="32" y2="14"/>
   </symbol>
   <symbol id="switch2:shape5_0">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="8" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.325792" x1="38" x2="13" y1="7" y2="16"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="38" x2="47" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="5" x2="14" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="9" y2="6"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="10" y2="5"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="14" y2="0"/>
   </symbol>
   <symbol id="switch2:shape5_1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor1">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="switch2:shape5-UnNor2">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="14" x2="14" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="54" x2="54" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="51" x2="51" y1="4" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.470588" x1="47" x2="47" y1="-1" y2="14"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="48" y1="7" y2="7"/>
   </symbol>
   <symbol id="transformer:shape31_0">
    <circle cx="29" cy="42" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="38" x2="102" y1="54" y2="86"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="98" x2="103" y1="87" y2="87"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="103" x2="101" y1="87" y2="82"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="23" x2="23" y1="79" y2="79"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="24" x2="42" y1="38" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="42" y1="54" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="33" x2="24" y1="54" y2="38"/>
   </symbol>
   <symbol id="transformer:shape31_1">
    <circle cx="70" cy="29" fillStyle="0" r="24.5" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="15" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="31" y2="24"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="24" y2="31"/>
   </symbol>
   <symbol id="transformer:shape31-2">
    <circle cx="70" cy="64" fillStyle="0" r="25" stroke-width="0.520408"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="69" y1="78" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="76" x2="69" y1="62" y2="69"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.520408" x1="69" x2="62" y1="69" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_0">
    <circle cx="30" cy="57" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="6" x2="32" y1="53" y2="26"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.25" x1="4" x2="7" y1="20" y2="20"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.39375" x1="2" x2="10" y1="23" y2="23"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.611465" x1="12" x2="0" y1="26" y2="26"/>
    <polyline arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" points="15,56 6,56 6,28 " stroke-width="1"/>
    <polyline DF8003:Layer="PUBLIC" points="32,14 26,27 39,27 32,14 32,15 32,14 "/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="32" x2="32" y1="42" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="35" y1="57" y2="53"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="26" y1="57" y2="52"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="57" y2="62"/>
   </symbol>
   <symbol id="transformer2:shape40_1">
    <circle cx="31" cy="79" fillStyle="0" r="15" stroke-width="1"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="81" y2="81"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="31" x2="31" y1="87" y2="87"/>
    <polyline DF8003:Layer="PUBLIC" points="30,87 26,78 36,78 30,87 "/>
   </symbol>
   <symbol id="voltageTransformer:shape88">
    <circle cx="7" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="22" cy="11" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="15" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="16" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
    <circle cx="7" cy="6" fillStyle="0" r="6" stroke-width="0.431185"/>
   </symbol>
   <symbol id="voltageTransformer:shape115">
    <circle cx="49" cy="19" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="6" y1="36" y2="36"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1" x1="22" x2="36" y1="44" y2="29"/>
    <rect height="6" stroke-width="1" width="14" x="22" y="33"/>
    <polyline points="36,36 50,36 50,19 " stroke-width="1"/>
    <circle cx="49" cy="8" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="50" x2="52" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.726459" x1="24" x2="24" y1="11" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752494" x1="28" x2="24" y1="13" y2="11"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.752385" x1="28" x2="24" y1="15" y2="17"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="37" x2="40" y1="8" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="16" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="37" x2="40" y1="19" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="35" x2="37" y1="22" y2="19"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="6" x2="6" y1="31" y2="41"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.07362" x1="2" x2="2" y1="34" y2="38"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="1.16541" x1="4" x2="4" y1="33" y2="39"/>
    <circle cx="37" cy="8" r="7.5" stroke-width="0.804311"/>
    <circle cx="37" cy="19" r="7.5" stroke-width="0.804311"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="5" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804274" x1="48" x2="50" y1="11" y2="8"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.804306" x1="50" x2="53" y1="8" y2="8"/>
    <circle cx="27" cy="13" r="7.5" stroke-width="0.804311"/>
   </symbol>
   <symbol id="Tag:shape0">
    <polyline fill="rgb(255,255,0)" points="85,21 85,23 83,27 80,29 77,32 73,34 68,36 62,38 56,39 50,40 43,40 36,40 30,39 24,38 18,36 13,34 9,32 6,29 3,27 1,23 1,21 1,18 3,14 6,12 9,9 13,7 18,5 24,3 30,2 36,1 43,1 50,1 56,2 62,3 68,5 73,7 77,9 80,12 83,14 85,18 85,21 " stroke="rgb(255,0,0)"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="19" x2="26" y1="13" y2="13"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ea2270" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 34.000000 30.000000) translate(0,16)">接地</text>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="10" x2="34" y1="21" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.91667" x1="22" x2="22" y1="34" y2="21"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(255,48,48)" stroke-width="2.26608" x1="16" x2="29" y1="17" y2="17"/>
   </symbol>
   <symbol id="Tag:shape1">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2857ae0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">保</text>
   </symbol>
   <symbol id="Tag:shape2">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea4210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.500000 21.500000) translate(0,12)">母线检修</text>
   </symbol>
   <symbol id="Tag:shape3">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea5040" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序不对</text>
   </symbol>
   <symbol id="Tag:shape4">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea6340" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">相序未校</text>
   </symbol>
   <symbol id="Tag:shape5">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ea6e60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">线路检修</text>
   </symbol>
   <symbol id="Tag:shape6">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ea7a20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">抽压</text>
   </symbol>
   <symbol id="Tag:shape7">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="117" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_2ea8510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 39.000000) translate(0,16)">引流已解脱</text>
   </symbol>
   <symbol id="Tag:shape8">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="56" stroke="rgb(255,0,0)" stroke-width="9.38736" width="104" x="6" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_285d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,16)">合闸压板</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="20" graphid="g_285d970" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 16.000000 54.000000) translate(0,36)">已退出</text>
   </symbol>
   <symbol id="Tag:shape9">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eabf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eabf60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 13.000000 50.000000) translate(0,35)">二种工作</text>
    <rect fill="none" height="55" stroke="rgb(255,0,0)" stroke-width="4.64286" width="98" x="3" y="3"/>
   </symbol>
   <symbol id="Tag:shape10">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="44" stroke="rgb(255,0,0)" stroke-width="7.42857" width="90" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ead9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,16)"> 线路有</text>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ead9d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 11.000000 46.000000) translate(0,35)">带电作业</text>
   </symbol>
   <symbol id="Tag:shape11">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="3" width="113" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="16" graphid="g_2eae950" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,13)">监控职责已转移</text>
   </symbol>
   <symbol id="Tag:shape12">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb0520" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">退出</text>
   </symbol>
   <symbol id="Tag:shape13">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2eb11b0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 合</text>
   </symbol>
   <symbol id="Tag:shape14">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2eb2020" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">常 分</text>
   </symbol>
   <symbol id="Tag:shape15">
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2eb2770" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 4.000000 21.000000) translate(0,12)">禁止操作</text>
    <rect fill="none" height="24" stroke="rgb(255,0,0)" stroke-width="2" width="63" x="2" y="1"/>
   </symbol>
   <symbol id="Tag:shape16">
    
   </symbol>
   <symbol id="Tag:shape17">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb3ff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">备用</text>
   </symbol>
   <symbol id="Tag:shape18">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb4c50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">重</text>
   </symbol>
   <symbol id="Tag:shape19">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb5510" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">备</text>
   </symbol>
   <symbol id="Tag:shape20">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2eb5cd0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 22.000000) translate(0,12)">保护退出</text>
   </symbol>
   <symbol id="Tag:shape21">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb6db0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">冷</text>
   </symbol>
   <symbol id="Tag:shape22">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb7730" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 3.000000 24.000000) translate(0,16)">调试</text>
   </symbol>
   <symbol id="Tag:shape23">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="26" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2eb8220" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 24.000000) translate(0,16)">热</text>
   </symbol>
   <symbol id="Tag:shape24">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2eb8be0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">断 开</text>
   </symbol>
   <symbol id="Tag:shape25">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2eba200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">拉 开</text>
   </symbol>
   <symbol id="Tag:shape26">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="123" x="4" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ebac20" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 39.000000) translate(0,20)">禁止刷新</text>
   </symbol>
   <symbol id="Tag:shape27">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="43" stroke="rgb(255,0,0)" stroke-width="7.28571" width="92" x="5" y="5"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="24" graphid="g_2ebbdc0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 22.000000 39.000000) translate(0,20)">热 备</text>
   </symbol>
   <symbol id="Tag:shape28">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ebca50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2.000000 21.000000) translate(0,12)">禁止遥测</text>
   </symbol>
   <symbol id="Tag:shape29">
    
   </symbol>
   <symbol id="Tag:shape30">
    
   </symbol>
   <symbol id="Tag:shape31">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="63" x="3" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="15" graphid="g_2ecae60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 6.000000 21.000000) translate(0,12)">全站检修</text>
   </symbol>
   <symbol id="Tag:shape32">
    <rect fill="rgb(255,255,0)" fillStyle="1" height="24" stroke="rgb(255,0,0)" stroke-width="4" width="43" x="2" y="2"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="19" graphid="g_2ec3200" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 5.000000 24.000000) translate(0,16)">注3</text>
   </symbol>
   <symbol id="Tag:shape36">
    
   </symbol>
   <symbol id="Tag:shape37">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <polyline points="76,6 1,6 " stroke-width="1"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ebdbe0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
   </symbol>
   <symbol id="Tag:shape38">
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.596178" x1="13" x2="13" y1="12" y2="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.228882" x1="20" x2="20" y1="5" y2="7"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.322998" x1="17" x2="17" y1="3" y2="9"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke-width="0.276923" x1="13" x2="4" y1="6" y2="6"/>
    <text fill="rgb(255,0,0)" font-family="SimSun" font-size="21" graphid="g_2ebef60" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 27.000000) translate(0,17)">接地</text>
    <polyline points="76,6 1,6 " stroke-width="1"/>
   </symbol>
   <symbol id="Tag:shape40">
    <rect fill="rgb(255,0,0)" fillStyle="1" height="99" stroke="rgb(255,0,0)" stroke-width="1" width="111" x="0" y="0"/>
    <line arrowAngle="40" arrowLength="12" arrowPos="0" arrowType="0" stroke="rgb(50,205,50)" stroke-width="3" x1="26" x2="73" y1="72" y2="24"/>
    <circle cx="54" cy="49" fill="none" fillStyle="0" r="39.5" stroke="rgb(50,205,50)" stroke-width="3"/>
   </symbol>
   <symbol id="Tag:shape41">
    
   </symbol>
   <style type="text/css"><![CDATA[
.BV-0KV { stroke:rgb(60,120,255);fill:none}
.BKBV-0KV { stroke:rgb(60,120,255);fill:rgb(60,120,255)}
.BV-3KV { stroke:rgb(154,205,50);fill:none}
.BKBV-3KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-6KV { stroke:rgb(139,139,0);fill:none}
.BKBV-6KV { stroke:rgb(139,139,0);fill:rgb(139,139,0)}
.BV-10KV { stroke:rgb(50,205,50);fill:none}
.BKBV-10KV { stroke:rgb(50,205,50);fill:rgb(50,205,50)}
.BV-15KV { stroke:rgb(0,255,0);fill:none}
.BKBV-15KV { stroke:rgb(0,255,0);fill:rgb(0,255,0)}
.BV-20KV { stroke:rgb(221,191,27);fill:none}
.BKBV-20KV { stroke:rgb(221,191,27);fill:rgb(221,191,27)}
.BV-35KV { stroke:rgb(255,255,0);fill:none}
.BKBV-35KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-66KV { stroke:rgb(255,255,0);fill:none}
.BKBV-66KV { stroke:rgb(255,255,0);fill:rgb(255,255,0)}
.BV-110KV { stroke:rgb(170,85,127);fill:none}
.BKBV-110KV { stroke:rgb(170,85,127);fill:rgb(170,85,127)}
.BV-220KV { stroke:rgb(255,255,255);fill:none}
.BKBV-220KV { stroke:rgb(255,255,255);fill:rgb(255,255,255)}
.BV-330KV { stroke:rgb(160,32,240);fill:none}
.BKBV-330KV { stroke:rgb(160,32,240);fill:rgb(160,32,240)}
.BV-500KV { stroke:rgb(213,0,0);fill:none}
.BKBV-500KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-750KV { stroke:rgb(213,0,0);fill:none}
.BKBV-750KV { stroke:rgb(213,0,0);fill:rgb(213,0,0)}
.BV-22KV { stroke:rgb(154,205,50);fill:none}
.BKBV-22KV { stroke:rgb(154,205,50);fill:rgb(154,205,50)}
.BV-38KV { stroke:rgb(139,76,57);fill:none}
.BKBV-38KV { stroke:rgb(139,76,57);fill:rgb(139,76,57)}
.nopower {stroke:grey;fill:none}
.choice {stroke:rgb(255,0,0);fill:none}
.bknopower {stroke:grey;fill:grey}
.bkchoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}
.busnopower {stroke:grey;fill:grey}
.buschoice {stroke:rgb(255,0,0);fill:rgb(255,0,0)}]]></style>
  </defs><g id="Head_Layer">
   <rect fill="rgb(21,40,56)" height="1642" width="2586" x="-363" y="-1187"/>
  </g><g id="Breaker_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-317571">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 901.449774 -792.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49041" ObjectName="SW-CX_YJW.CX_YJW_281BK"/>
     <cge:Meas_Ref ObjectId="317571"/>
    <cge:TPSR_Ref TObjectID="49041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317590">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.741036 -290.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49049" ObjectName="SW-CX_YJW.CX_YJW_301BK"/>
     <cge:Meas_Ref ObjectId="317590"/>
    <cge:TPSR_Ref TObjectID="49049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317593">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.741036 -291.000000)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49052" ObjectName="SW-CX_YJW.CX_YJW_302BK"/>
     <cge:Meas_Ref ObjectId="317593"/>
    <cge:TPSR_Ref TObjectID="49052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317677">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.013262 -13.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49108" ObjectName="SW-CX_YJW.CX_YJW_395BK"/>
     <cge:Meas_Ref ObjectId="317677"/>
    <cge:TPSR_Ref TObjectID="49108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317672">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 360.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49105" ObjectName="SW-CX_YJW.CX_YJW_394BK"/>
     <cge:Meas_Ref ObjectId="317672"/>
    <cge:TPSR_Ref TObjectID="49105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317667">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49102" ObjectName="SW-CX_YJW.CX_YJW_393BK"/>
     <cge:Meas_Ref ObjectId="317667"/>
    <cge:TPSR_Ref TObjectID="49102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317662">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.013262 -16.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49099" ObjectName="SW-CX_YJW.CX_YJW_392BK"/>
     <cge:Meas_Ref ObjectId="317662"/>
    <cge:TPSR_Ref TObjectID="49099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317654">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49093" ObjectName="SW-CX_YJW.CX_YJW_391BK"/>
     <cge:Meas_Ref ObjectId="317654"/>
    <cge:TPSR_Ref TObjectID="49093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317649">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49090" ObjectName="SW-CX_YJW.CX_YJW_389BK"/>
     <cge:Meas_Ref ObjectId="317649"/>
    <cge:TPSR_Ref TObjectID="49090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317644">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49087" ObjectName="SW-CX_YJW.CX_YJW_388BK"/>
     <cge:Meas_Ref ObjectId="317644"/>
    <cge:TPSR_Ref TObjectID="49087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.013262 -16.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317639">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1376.013262 -13.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49084" ObjectName="SW-CX_YJW.CX_YJW_387BK"/>
     <cge:Meas_Ref ObjectId="317639"/>
    <cge:TPSR_Ref TObjectID="49084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317634">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49081" ObjectName="SW-CX_YJW.CX_YJW_386BK"/>
     <cge:Meas_Ref ObjectId="317634"/>
    <cge:TPSR_Ref TObjectID="49081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317626">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49075" ObjectName="SW-CX_YJW.CX_YJW_385BK"/>
     <cge:Meas_Ref ObjectId="317626"/>
    <cge:TPSR_Ref TObjectID="49075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317621">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.013262 -16.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49072" ObjectName="SW-CX_YJW.CX_YJW_384BK"/>
     <cge:Meas_Ref ObjectId="317621"/>
    <cge:TPSR_Ref TObjectID="49072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317616">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1764.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49069" ObjectName="SW-CX_YJW.CX_YJW_383BK"/>
     <cge:Meas_Ref ObjectId="317616"/>
    <cge:TPSR_Ref TObjectID="49069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317611">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1874.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49066" ObjectName="SW-CX_YJW.CX_YJW_382BK"/>
     <cge:Meas_Ref ObjectId="317611"/>
    <cge:TPSR_Ref TObjectID="49066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317606">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.013262 -15.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49063" ObjectName="SW-CX_YJW.CX_YJW_381BK"/>
     <cge:Meas_Ref ObjectId="317606"/>
    <cge:TPSR_Ref TObjectID="49063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2065.013262 -14.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2162.013262 -16.623529)" xlink:href="#breaker2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317658">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 646.000000 296.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49097" ObjectName="SW-CX_YJW.CX_YJW_391QBK"/>
     <cge:Meas_Ref ObjectId="317658"/>
    <cge:TPSR_Ref TObjectID="49097"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317630">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.000000 294.000000)" xlink:href="#breaker2:shape10_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49079" ObjectName="SW-CX_YJW.CX_YJW_385QBK"/>
     <cge:Meas_Ref ObjectId="317630"/>
    <cge:TPSR_Ref TObjectID="49079"/></metadata>
   </g>
  </g><g id="Transformer_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-CX_YJW.CX_YJW_1T">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="48582"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.741036 -601.000000)" xlink:href="#transformer:shape31_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.741036 -601.000000)" xlink:href="#transformer:shape31_1"/>
    </g>
    <g id="WD-2">
     <metadata>
      <cge:PSR_Ref ObjectId="48583"/>
     </metadata>
     <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 839.741036 -601.000000)" xlink:href="#transformer:shape31-2"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="49111" ObjectName="TF-CX_YJW.CX_YJW_1T"/>
    <cge:TPSR_Ref TObjectID="49111"/></metadata>
   </g>
  </g><g id="BusSection_Layer">
   <g DF8003:Layer="PUBLIC" id="BS-CX_YJW.CX_YJW_3ⅠM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1134,-190 2218,-190 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49181" ObjectName="BS-CX_YJW.CX_YJW_3ⅠM"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   <polyline fill="none" opacity="0" points="1134,-190 2218,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g DF8003:Layer="PUBLIC" id="BS-CX_YJW.CX_YJW_3ⅡM">
    <g class="BV-35KV">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="11,-192 1088,-192 " stroke-width="6"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectID="49182" ObjectName="BS-CX_YJW.CX_YJW_3ⅡM"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   <polyline fill="none" opacity="0" points="11,-192 1088,-192 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="Load_Layer">
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 905.449774 -1128.000000)" xlink:href="#load:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 84.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 169.013262 135.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 263.013262 136.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 364.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 458.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 556.013262 133.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 875.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 965.013262 135.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1049.013262 133.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1206.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1286.013262 135.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1380.013262 136.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1481.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1673.013262 133.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1768.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1878.013262 135.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1975.013262 134.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2069.013262 135.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="EC-0">
    <use class="BKBV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2166.013262 133.000000)" xlink:href="#load:shape3"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="EC-0"/>
    </metadata>
   </g>
  </g><g id="Earth_Layer">
   <g DF8003:Layer="PUBLIC" id="g_283b860" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 779.000000 -1009.000000)" xlink:href="#earth:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2782ef0" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 837.000000 -433.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27d2e30" refnum="0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1040.500000 -560.000000)" xlink:href="#earth:shape0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="Link_Layer">
   <g class="BV-0KV" id="g_283c010">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="797,-1015 810,-1015 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="voltageTransformer" ObjectIDND0="g_283b860@0" ObjectIDZND0="g_283afc0@0" Pin0InfoVect0LinkObjId="g_283afc0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_283b860_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="797,-1015 810,-1015 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_26d55e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="814,-1014 854,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link EndDevType0="switch" ObjectIDZND0="49044@0" Pin0InfoVect0LinkObjId="SW-317574_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="0" Pin1InfoVect0LinkObjId="" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="814,-1014 854,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2773e90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1239,-228 1239,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49050@0" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_3167f80_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317591_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-228 1239,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27764f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1206,-282 1239,-282 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49051@0" ObjectIDZND0="49049@x" ObjectIDZND1="49050@x" Pin0InfoVect0LinkObjId="SW-317590_0" Pin0InfoVect1LinkObjId="SW-317591_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317592_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1206,-282 1239,-282 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2776930">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1239,-282 1239,-264 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="switch" EndDevType0="switch" ObjectIDND0="49049@x" ObjectIDND1="49051@x" ObjectIDZND0="49050@1" Pin0InfoVect0LinkObjId="SW-317591_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317590_0" Pin1InfoVect1LinkObjId="SW-317592_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-282 1239,-264 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2782cd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="900,-620 843,-586 843,-555 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="49111@x" ObjectIDZND0="49056@1" Pin0InfoVect0LinkObjId="SW-317597_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2787c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="900,-620 843,-586 843,-555 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27842b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="843,-500 843,-519 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_27837e0@0" ObjectIDZND0="49056@0" Pin0InfoVect0LinkObjId="SW-317597_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_27837e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="843,-500 843,-519 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27844d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="843,-451 843,-466 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="earth" EndDevType0="lightningRod" ObjectIDND0="g_2782ef0@0" ObjectIDZND0="g_27837e0@1" Pin0InfoVect0LinkObjId="g_27837e0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2782ef0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="843,-451 843,-466 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27d4a10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="916,-668 1012,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49111@x" ObjectIDZND0="g_2785240@0" ObjectIDZND1="49055@x" ObjectIDZND2="g_2784ac0@0" Pin0InfoVect0LinkObjId="g_2785240_0" Pin0InfoVect1LinkObjId="SW-317596_0" Pin0InfoVect2LinkObjId="g_2784ac0_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2787c20_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="916,-668 1012,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27d56c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-668 1046,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" ObjectIDND0="49111@x" ObjectIDND1="g_2784ac0@0" ObjectIDZND0="g_2785240@0" ObjectIDZND1="49055@x" Pin0InfoVect0LinkObjId="g_2785240_0" Pin0InfoVect1LinkObjId="SW-317596_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2787c20_0" Pin1InfoVect1LinkObjId="g_2784ac0_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-668 1046,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2802ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-668 1046,-652 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49111@x" ObjectIDND1="g_2784ac0@0" ObjectIDND2="49055@x" ObjectIDZND0="g_2785240@1" Pin0InfoVect0LinkObjId="g_2785240_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2787c20_0" Pin1InfoVect1LinkObjId="g_2784ac0_0" Pin1InfoVect2LinkObjId="SW-317596_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-668 1046,-652 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2804140">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-612 1046,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="g_2785240@0" ObjectIDZND0="g_2784ac0@0" ObjectIDZND1="49055@x" ObjectIDZND2="g_27d2e30@0" Pin0InfoVect0LinkObjId="g_2784ac0_0" Pin0InfoVect1LinkObjId="SW-317596_0" Pin0InfoVect2LinkObjId="g_27d2e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2785240_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-612 1046,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2787c20">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-652 1076,-668 1046,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49055@1" ObjectIDZND0="49111@x" ObjectIDZND1="g_2784ac0@0" ObjectIDZND2="g_2785240@0" Pin0InfoVect0LinkObjId="g_27d45e0_0" Pin0InfoVect1LinkObjId="g_2784ac0_0" Pin0InfoVect2LinkObjId="g_2785240_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317596_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-652 1076,-668 1046,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27d45e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-651 1012,-668 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="switch" ObjectIDND0="g_2784ac0@0" ObjectIDZND0="49111@x" ObjectIDZND1="g_2785240@0" ObjectIDZND2="49055@x" Pin0InfoVect0LinkObjId="g_2787c20_0" Pin0InfoVect1LinkObjId="g_2785240_0" Pin0InfoVect2LinkObjId="SW-317596_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2784ac0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-651 1012,-668 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2804810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1012,-609 1012,-586 1046,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="earth" ObjectIDND0="g_2784ac0@1" ObjectIDZND0="g_2785240@0" ObjectIDZND1="49055@x" ObjectIDZND2="g_27d2e30@0" Pin0InfoVect0LinkObjId="g_2785240_0" Pin0InfoVect1LinkObjId="SW-317596_0" Pin0InfoVect2LinkObjId="g_27d2e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2784ac0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1012,-609 1012,-586 1046,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_268cbc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1076,-616 1076,-586 1046,-586 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" EndDevType2="earth" ObjectIDND0="49055@0" ObjectIDZND0="g_2785240@0" ObjectIDZND1="g_2784ac0@0" ObjectIDZND2="g_27d2e30@0" Pin0InfoVect0LinkObjId="g_2785240_0" Pin0InfoVect1LinkObjId="g_2784ac0_0" Pin0InfoVect2LinkObjId="g_27d2e30_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317596_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1076,-616 1076,-586 1046,-586 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2803520">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1046,-586 1046,-578 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="earth" ObjectIDND0="g_2785240@0" ObjectIDND1="g_2784ac0@0" ObjectIDND2="49055@x" ObjectIDZND0="g_27d2e30@0" Pin0InfoVect0LinkObjId="g_27d2e30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2785240_0" Pin1InfoVect1LinkObjId="g_2784ac0_0" Pin1InfoVect2LinkObjId="SW-317596_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1046,-586 1046,-578 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_282fbe0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-690 910,-726 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="switch" ObjectIDND0="49111@2" ObjectIDZND0="49042@0" Pin0InfoVect0LinkObjId="SW-317572_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2787c20_2" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-690 910,-726 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_28e4390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-780 910,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49045@0" ObjectIDZND0="49042@x" ObjectIDZND1="49041@x" Pin0InfoVect0LinkObjId="SW-317572_0" Pin0InfoVect1LinkObjId="SW-317571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317575_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="863,-780 910,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_26f2170">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-762 910,-780 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49042@1" ObjectIDZND0="49045@x" ObjectIDZND1="49041@x" Pin0InfoVect0LinkObjId="SW-317575_0" Pin0InfoVect1LinkObjId="SW-317571_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317572_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="910,-762 910,-780 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_273fd30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-780 910,-801 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49045@x" ObjectIDND1="49042@x" ObjectIDZND0="49041@0" Pin0InfoVect0LinkObjId="SW-317571_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317575_0" Pin1InfoVect1LinkObjId="SW-317572_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-780 910,-801 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27fa660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="862,-849 910,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="breaker" EndDevType1="switch" ObjectIDND0="49046@0" ObjectIDZND0="49041@x" ObjectIDZND1="49043@x" Pin0InfoVect0LinkObjId="SW-317571_0" Pin0InfoVect1LinkObjId="SW-317573_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317576_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="862,-849 910,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27087c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-828 910,-849 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49041@1" ObjectIDZND0="49046@x" ObjectIDZND1="49043@x" Pin0InfoVect0LinkObjId="SW-317576_0" Pin0InfoVect1LinkObjId="SW-317573_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317571_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="910,-828 910,-849 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_272c210">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-849 910,-879 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49046@x" ObjectIDND1="49041@x" ObjectIDZND0="49043@0" Pin0InfoVect0LinkObjId="SW-317573_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317576_0" Pin1InfoVect1LinkObjId="SW-317571_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="910,-849 910,-879 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27fa850">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="863,-948 910,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49047@0" ObjectIDZND0="49043@x" ObjectIDZND1="49044@x" ObjectIDZND2="49048@x" Pin0InfoVect0LinkObjId="SW-317573_0" Pin0InfoVect1LinkObjId="SW-317574_0" Pin0InfoVect2LinkObjId="SW-317578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317577_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="863,-948 910,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2676250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-915 910,-948 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49043@1" ObjectIDZND0="49047@x" ObjectIDZND1="49044@x" ObjectIDZND2="49048@x" Pin0InfoVect0LinkObjId="SW-317577_0" Pin0InfoVect1LinkObjId="SW-317574_0" Pin0InfoVect2LinkObjId="SW-317578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317573_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-915 910,-948 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_26f51e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="890,-1014 910,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49044@1" ObjectIDZND0="49047@x" ObjectIDZND1="49043@x" ObjectIDZND2="49048@x" Pin0InfoVect0LinkObjId="SW-317577_0" Pin0InfoVect1LinkObjId="SW-317573_0" Pin0InfoVect2LinkObjId="SW-317578_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317574_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="890,-1014 910,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2716b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-948 910,-1014 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49047@x" ObjectIDND1="49043@x" ObjectIDZND0="49044@x" ObjectIDZND1="49048@x" ObjectIDZND2="g_287bba0@0" Pin0InfoVect0LinkObjId="SW-317574_0" Pin0InfoVect1LinkObjId="SW-317578_0" Pin0InfoVect2LinkObjId="g_287bba0_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317577_0" Pin1InfoVect1LinkObjId="SW-317573_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-948 910,-1014 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2706c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="865,-1063 910,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49048@0" ObjectIDZND0="49044@x" ObjectIDZND1="49047@x" ObjectIDZND2="49043@x" Pin0InfoVect0LinkObjId="SW-317574_0" Pin0InfoVect1LinkObjId="SW-317577_0" Pin0InfoVect2LinkObjId="SW-317573_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317578_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="865,-1063 910,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2708080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-1014 910,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="load" ObjectIDND0="49044@x" ObjectIDND1="49047@x" ObjectIDND2="49043@x" ObjectIDZND0="49048@x" ObjectIDZND1="g_287bba0@0" ObjectIDZND2="0@x" Pin0InfoVect0LinkObjId="SW-317578_0" Pin0InfoVect1LinkObjId="g_287bba0_0" Pin0InfoVect2LinkObjId="SW-0_0" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317574_0" Pin1InfoVect1LinkObjId="SW-317577_0" Pin1InfoVect2LinkObjId="SW-317573_0" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-1014 910,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_287ab70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="946,-1090 910,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_287bba0@0" ObjectIDZND0="49048@x" ObjectIDZND1="49044@x" ObjectIDZND2="49047@x" Pin0InfoVect0LinkObjId="SW-317578_0" Pin0InfoVect1LinkObjId="SW-317574_0" Pin0InfoVect2LinkObjId="SW-317577_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_287bba0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="946,-1090 910,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_2790f50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-1133 910,-1090 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="0@0" ObjectIDZND0="g_287bba0@0" ObjectIDZND1="49048@x" ObjectIDZND2="49044@x" Pin0InfoVect0LinkObjId="g_287bba0_0" Pin0InfoVect1LinkObjId="SW-317578_0" Pin0InfoVect2LinkObjId="SW-317574_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-1133 910,-1090 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-220KV" id="g_27101e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-1090 910,-1063 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="load" EndDevType0="switch" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="g_287bba0@0" ObjectIDND1="0@x" ObjectIDZND0="49048@x" ObjectIDZND1="49044@x" ObjectIDZND2="49047@x" Pin0InfoVect0LinkObjId="SW-317578_0" Pin0InfoVect1LinkObjId="SW-317574_0" Pin0InfoVect2LinkObjId="SW-317577_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_287bba0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-1090 910,-1063 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27a9530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1239,-282 1239,-298 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49051@x" ObjectIDND1="49050@x" ObjectIDZND0="49049@0" Pin0InfoVect0LinkObjId="SW-317590_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317592_0" Pin1InfoVect1LinkObjId="SW-317591_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-282 1239,-298 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e05950">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-229 612,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49053@0" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_32eba30_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317594_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-229 612,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e08c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="579,-283 612,-283 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49054@0" ObjectIDZND0="49053@x" ObjectIDZND1="49052@x" Pin0InfoVect0LinkObjId="SW-317594_0" Pin0InfoVect1LinkObjId="SW-317593_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317595_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="579,-283 612,-283 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e08ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-283 612,-265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49054@x" ObjectIDND1="49052@x" ObjectIDZND0="49053@1" Pin0InfoVect0LinkObjId="SW-317594_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317595_0" Pin1InfoVect1LinkObjId="SW-317593_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-283 612,-265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e0a960">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-283 612,-299 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="breaker" ObjectIDND0="49053@x" ObjectIDND1="49054@x" ObjectIDZND0="49052@0" Pin0InfoVect0LinkObjId="SW-317593_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317594_0" Pin1InfoVect1LinkObjId="SW-317595_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="612,-283 612,-299 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2dda540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,-357 612,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_2e09150@0" ObjectIDZND0="49052@x" ObjectIDZND1="49111@x" ObjectIDZND2="g_2776b50@0" Pin0InfoVect0LinkObjId="SW-317593_0" Pin0InfoVect1LinkObjId="g_2787c20_0" Pin0InfoVect2LinkObjId="g_2776b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2e09150_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="637,-357 612,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb9b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-326 612,-357 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="49052@1" ObjectIDZND0="g_2e09150@0" ObjectIDZND1="49111@x" ObjectIDZND2="g_2776b50@0" Pin0InfoVect0LinkObjId="g_2e09150_0" Pin0InfoVect1LinkObjId="g_2787c20_0" Pin0InfoVect2LinkObjId="g_2776b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317593_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="612,-326 612,-357 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_27c8840">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1264,-356 1239,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="transformer" EndDevType2="lightningRod" ObjectIDND0="g_2776b50@0" ObjectIDZND0="49049@x" ObjectIDZND1="49111@x" ObjectIDZND2="g_2e09150@0" Pin0InfoVect0LinkObjId="SW-317590_0" Pin0InfoVect1LinkObjId="g_2787c20_0" Pin0InfoVect2LinkObjId="g_2e09150_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2776b50_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1264,-356 1239,-356 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e68130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1239,-356 1239,-325 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="transformer" BeginDevType2="lightningRod" EndDevType0="breaker" ObjectIDND0="g_2776b50@0" ObjectIDND1="49111@x" ObjectIDND2="g_2e09150@0" ObjectIDZND0="49049@1" Pin0InfoVect0LinkObjId="SW-317590_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2776b50_0" Pin1InfoVect1LinkObjId="g_2787c20_0" Pin1InfoVect2LinkObjId="g_2e09150_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1239,-356 1239,-325 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2eb2de0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-606 910,-414 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" EndDevType0="lightningRod" EndDevType1="breaker" EndDevType2="lightningRod" ObjectIDND0="49111@1" ObjectIDZND0="g_2e09150@0" ObjectIDZND1="49052@x" ObjectIDZND2="g_2776b50@0" Pin0InfoVect0LinkObjId="g_2e09150_0" Pin0InfoVect1LinkObjId="SW-317593_0" Pin0InfoVect2LinkObjId="g_2776b50_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_2787c20_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="910,-606 910,-414 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2ddcca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1629,-216 1608,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="49058@0" ObjectIDZND0="49057@x" ObjectIDZND1="49181@0" Pin0InfoVect0LinkObjId="SW-317598_0" Pin0InfoVect1LinkObjId="g_2773e90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317599_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1629,-216 1608,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_2e0b870">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-236 1608,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="49057@0" ObjectIDZND0="49058@x" ObjectIDZND1="49181@0" Pin0InfoVect0LinkObjId="SW-317599_0" Pin0InfoVect1LinkObjId="g_2773e90_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317598_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-236 1608,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3167f80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-216 1608,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="49058@x" ObjectIDND1="49057@x" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317599_0" Pin1InfoVect1LinkObjId="SW-317598_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-216 1608,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32b1f90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-382 1608,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_30d6210@0" ObjectIDZND0="g_2687e50@0" Pin0InfoVect0LinkObjId="g_2687e50_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_30d6210_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-382 1608,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cd360">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1592,-294 1608,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49059@0" ObjectIDZND0="49057@x" ObjectIDZND1="g_276b7c0@0" ObjectIDZND2="g_30d6210@0" Pin0InfoVect0LinkObjId="SW-317598_0" Pin0InfoVect1LinkObjId="g_276b7c0_0" Pin0InfoVect2LinkObjId="g_30d6210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317600_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1592,-294 1608,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cd670">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-272 1608,-294 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49057@1" ObjectIDZND0="49059@x" ObjectIDZND1="g_276b7c0@0" ObjectIDZND2="g_30d6210@0" Pin0InfoVect0LinkObjId="SW-317600_0" Pin0InfoVect1LinkObjId="g_276b7c0_0" Pin0InfoVect2LinkObjId="g_30d6210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317598_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-272 1608,-294 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1640,-334 1608,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_276b7c0@0" ObjectIDZND0="49059@x" ObjectIDZND1="49057@x" ObjectIDZND2="g_30d6210@0" Pin0InfoVect0LinkObjId="SW-317600_0" Pin0InfoVect1LinkObjId="SW-317598_0" Pin0InfoVect2LinkObjId="g_30d6210_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_276b7c0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1640,-334 1608,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cf200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-294 1608,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49059@x" ObjectIDND1="49057@x" ObjectIDZND0="g_276b7c0@0" ObjectIDZND1="g_30d6210@0" Pin0InfoVect0LinkObjId="g_276b7c0_0" Pin0InfoVect1LinkObjId="g_30d6210_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317600_0" Pin1InfoVect1LinkObjId="SW-317598_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-294 1608,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32cf3f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1608,-334 1608,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="switch" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="g_276b7c0@0" ObjectIDND1="49059@x" ObjectIDND2="49057@x" ObjectIDZND0="g_30d6210@1" Pin0InfoVect0LinkObjId="g_30d6210_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_276b7c0_0" Pin1InfoVect1LinkObjId="SW-317600_0" Pin1InfoVect2LinkObjId="SW-317598_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1608,-334 1608,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32eb3d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="319,-216 298,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="49061@0" ObjectIDZND0="49060@x" ObjectIDZND1="49182@0" Pin0InfoVect0LinkObjId="SW-317601_0" Pin0InfoVect1LinkObjId="g_2e05950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317602_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="319,-216 298,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32eb7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-236 298,-216 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="busSection" ObjectIDND0="49060@0" ObjectIDZND0="49061@x" ObjectIDZND1="49182@0" Pin0InfoVect0LinkObjId="SW-317602_0" Pin0InfoVect1LinkObjId="g_2e05950_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317601_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="298,-236 298,-216 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32eba30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-216 298,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="busSection" ObjectIDND0="49060@x" ObjectIDND1="49061@x" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317601_0" Pin1InfoVect1LinkObjId="SW-317602_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="298,-216 298,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ec660">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-382 298,-395 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="voltageTransformer" ObjectIDND0="g_32ebc90@0" ObjectIDZND0="g_32e32d0@0" Pin0InfoVect0LinkObjId="g_32e32d0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32ebc90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="298,-382 298,-395 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32ef9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="282,-293 298,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49062@0" ObjectIDZND0="49060@x" ObjectIDZND1="g_32f0050@0" ObjectIDZND2="g_32ebc90@0" Pin0InfoVect0LinkObjId="SW-317601_0" Pin0InfoVect1LinkObjId="g_32f0050_0" Pin0InfoVect2LinkObjId="g_32ebc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317603_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="282,-293 298,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32efdf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-272 298,-293 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="lightningRod" ObjectIDND0="49060@1" ObjectIDZND0="49062@x" ObjectIDZND1="g_32f0050@0" ObjectIDZND2="g_32ebc90@0" Pin0InfoVect0LinkObjId="SW-317603_0" Pin0InfoVect1LinkObjId="g_32f0050_0" Pin0InfoVect2LinkObjId="g_32ebc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317601_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="298,-272 298,-293 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32f0e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="330,-334 298,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="g_32f0050@0" ObjectIDZND0="49060@x" ObjectIDZND1="49062@x" ObjectIDZND2="g_32ebc90@0" Pin0InfoVect0LinkObjId="SW-317601_0" Pin0InfoVect1LinkObjId="SW-317603_0" Pin0InfoVect2LinkObjId="g_32ebc90_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_32f0050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="330,-334 298,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32f1200">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-293 298,-334 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" EndDevType0="lightningRod" EndDevType1="lightningRod" ObjectIDND0="49060@x" ObjectIDND1="49062@x" ObjectIDZND0="g_32f0050@0" ObjectIDZND1="g_32ebc90@0" Pin0InfoVect0LinkObjId="g_32f0050_0" Pin0InfoVect1LinkObjId="g_32ebc90_0" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317601_0" Pin1InfoVect1LinkObjId="SW-317603_0" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="298,-293 298,-334 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_32f1460">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="298,-334 298,-350 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49060@x" ObjectIDND1="49062@x" ObjectIDND2="g_32f0050@0" ObjectIDZND0="g_32ebc90@1" Pin0InfoVect0LinkObjId="g_32ebc90_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317601_0" Pin1InfoVect1LinkObjId="SW-317603_0" Pin1InfoVect2LinkObjId="g_32f0050_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="298,-334 298,-350 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32d0740">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="77,-80 89,-80 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="77,-80 89,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33270b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="89,-80 89,-101 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="89,-80 89,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32d4c30">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="89,-51 89,-80 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="89,-51 89,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32fa940">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="46,5 89,5 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_332cce0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_332cce0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="46,5 89,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_332d1f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="89,113 89,5 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_332cce0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_332cce0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="89,113 89,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_331f450">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="89,5 89,-24 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_332cce0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_332cce0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="89,5 89,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3322be0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="162,-79 174,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="162,-79 174,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3322e40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="174,-79 174,-100 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="174,-79 174,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3336660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="174,-50 174,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="174,-50 174,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_33368c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="131,6 174,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3324ec0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3324ec0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="131,6 174,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32f9ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="174,114 174,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3324ec0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3324ec0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="174,114 174,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_32fa120">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="174,6 174,-23 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3324ec0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3324ec0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="174,6 174,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33313d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="256,-78 268,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49110@0" ObjectIDZND0="49109@x" ObjectIDZND1="49108@x" Pin0InfoVect0LinkObjId="SW-317678_0" Pin0InfoVect1LinkObjId="SW-317677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317679_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="256,-78 268,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33317a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,-78 268,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49110@x" ObjectIDND1="49108@x" ObjectIDZND0="49109@0" Pin0InfoVect0LinkObjId="SW-317678_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317679_0" Pin1InfoVect1LinkObjId="SW-317677_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="268,-78 268,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332b250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,-49 268,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49108@1" ObjectIDZND0="49109@x" ObjectIDZND1="49110@x" Pin0InfoVect0LinkObjId="SW-317678_0" Pin0InfoVect1LinkObjId="SW-317679_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317677_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="268,-49 268,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332b480">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="225,7 268,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3331a00@0" ObjectIDZND0="0@x" ObjectIDZND1="49108@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3331a00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="225,7 268,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332b880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,115 268,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3331a00@0" ObjectIDZND1="49108@x" Pin0InfoVect0LinkObjId="g_3331a00_0" Pin0InfoVect1LinkObjId="SW-317677_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="268,115 268,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_332bae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,7 268,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3331a00@0" ObjectIDZND0="49108@0" Pin0InfoVect0LinkObjId="SW-317677_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3331a00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="268,7 268,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331e6b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="357,-80 369,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49107@0" ObjectIDZND0="49106@x" ObjectIDZND1="49105@x" Pin0InfoVect0LinkObjId="SW-317673_0" Pin0InfoVect1LinkObjId="SW-317672_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317674_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="357,-80 369,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_331e910">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="369,-80 369,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49107@x" ObjectIDND1="49105@x" ObjectIDZND0="49106@0" Pin0InfoVect0LinkObjId="SW-317673_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317674_0" Pin1InfoVect1LinkObjId="SW-317672_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="369,-80 369,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3346540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="369,-51 369,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49105@1" ObjectIDZND0="49106@x" ObjectIDZND1="49107@x" Pin0InfoVect0LinkObjId="SW-317673_0" Pin0InfoVect1LinkObjId="SW-317674_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317672_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="369,-51 369,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33467a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="326,5 369,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_331eb70@0" ObjectIDZND0="0@x" ObjectIDZND1="49105@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317672_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_331eb70_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="326,5 369,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3346ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="369,113 369,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_331eb70@0" ObjectIDZND1="49105@x" Pin0InfoVect0LinkObjId="g_331eb70_0" Pin0InfoVect1LinkObjId="SW-317672_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="369,113 369,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3346e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="369,5 369,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_331eb70@0" ObjectIDZND0="49105@0" Pin0InfoVect0LinkObjId="SW-317672_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_331eb70_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="369,5 369,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33518d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="451,-80 463,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49104@0" ObjectIDZND0="49103@x" ObjectIDZND1="49102@x" Pin0InfoVect0LinkObjId="SW-317668_0" Pin0InfoVect1LinkObjId="SW-317667_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317669_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="451,-80 463,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3351b30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-80 463,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49104@x" ObjectIDND1="49102@x" ObjectIDZND0="49103@0" Pin0InfoVect0LinkObjId="SW-317668_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317669_0" Pin1InfoVect1LinkObjId="SW-317667_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-80 463,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3353250">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-51 463,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49102@1" ObjectIDZND0="49103@x" ObjectIDZND1="49104@x" Pin0InfoVect0LinkObjId="SW-317668_0" Pin0InfoVect1LinkObjId="SW-317669_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317667_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="463,-51 463,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33534b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="420,5 463,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3351d90@0" ObjectIDZND0="0@x" ObjectIDZND1="49102@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317667_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3351d90_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="420,5 463,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33538b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,113 463,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3351d90@0" ObjectIDZND1="49102@x" Pin0InfoVect0LinkObjId="g_3351d90_0" Pin0InfoVect1LinkObjId="SW-317667_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="463,113 463,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3353b10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,5 463,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3351d90@0" ObjectIDZND0="49102@0" Pin0InfoVect0LinkObjId="SW-317667_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3351d90_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,5 463,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335f6a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="549,-81 561,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49101@0" ObjectIDZND0="49100@x" ObjectIDZND1="49099@x" Pin0InfoVect0LinkObjId="SW-317663_0" Pin0InfoVect1LinkObjId="SW-317662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317664_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="549,-81 561,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_335f900">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-81 561,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49101@x" ObjectIDND1="49099@x" ObjectIDZND0="49100@0" Pin0InfoVect0LinkObjId="SW-317663_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317664_0" Pin1InfoVect1LinkObjId="SW-317662_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-81 561,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3361020">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-52 561,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49099@1" ObjectIDZND0="49100@x" ObjectIDZND1="49101@x" Pin0InfoVect0LinkObjId="SW-317663_0" Pin0InfoVect1LinkObjId="SW-317664_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317662_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="561,-52 561,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3361280">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="518,4 561,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_335fb60@0" ObjectIDZND0="0@x" ObjectIDZND1="49099@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_335fb60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="518,4 561,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3361680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,112 561,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_335fb60@0" ObjectIDZND1="49099@x" Pin0InfoVect0LinkObjId="g_335fb60_0" Pin0InfoVect1LinkObjId="SW-317662_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="561,112 561,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33618e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,4 561,-25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_335fb60@0" ObjectIDZND0="49099@0" Pin0InfoVect0LinkObjId="SW-317662_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_335fb60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,4 561,-25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336c080">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="644,-80 656,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49095@0" ObjectIDZND0="49094@x" ObjectIDZND1="49093@x" Pin0InfoVect0LinkObjId="SW-317655_0" Pin0InfoVect1LinkObjId="SW-317654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317656_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="644,-80 656,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336c2e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,-80 656,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49095@x" ObjectIDND1="49093@x" ObjectIDZND0="49094@0" Pin0InfoVect0LinkObjId="SW-317655_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317656_0" Pin1InfoVect1LinkObjId="SW-317654_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,-80 656,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336da00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,-51 656,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49093@1" ObjectIDZND0="49094@x" ObjectIDZND1="49095@x" Pin0InfoVect0LinkObjId="SW-317655_0" Pin0InfoVect1LinkObjId="SW-317656_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317654_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="656,-51 656,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336dc60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="613,5 656,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_336c540@0" ObjectIDZND0="0@x" ObjectIDZND1="49093@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_336c540_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="613,5 656,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_336e2c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,5 656,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_336c540@0" ObjectIDZND0="49093@0" Pin0InfoVect0LinkObjId="SW-317654_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_336c540_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,5 656,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3379130">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="771,-79 783,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49092@0" ObjectIDZND0="49091@x" ObjectIDZND1="49090@x" Pin0InfoVect0LinkObjId="SW-317650_0" Pin0InfoVect1LinkObjId="SW-317649_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317651_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="771,-79 783,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3379390">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-79 783,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49092@x" ObjectIDND1="49090@x" ObjectIDZND0="49091@0" Pin0InfoVect0LinkObjId="SW-317650_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317651_0" Pin1InfoVect1LinkObjId="SW-317649_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-79 783,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_337aab0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-50 783,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49090@1" ObjectIDZND0="49091@x" ObjectIDZND1="49092@x" Pin0InfoVect0LinkObjId="SW-317650_0" Pin0InfoVect1LinkObjId="SW-317651_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317649_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="783,-50 783,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_337ad10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="740,6 783,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_33795f0@0" ObjectIDZND0="0@x" ObjectIDZND1="49090@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317649_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33795f0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="740,6 783,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_337b370">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,6 783,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_33795f0@0" ObjectIDZND0="49090@0" Pin0InfoVect0LinkObjId="SW-317649_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_33795f0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,6 783,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ecc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="868,-80 880,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49089@0" ObjectIDZND0="49088@x" ObjectIDZND1="49087@x" Pin0InfoVect0LinkObjId="SW-317645_0" Pin0InfoVect1LinkObjId="SW-317644_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317646_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="868,-80 880,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36eceb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-80 880,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49089@x" ObjectIDND1="49087@x" ObjectIDZND0="49088@0" Pin0InfoVect0LinkObjId="SW-317645_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317646_0" Pin1InfoVect1LinkObjId="SW-317644_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-80 880,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ee5d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-51 880,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49087@1" ObjectIDZND0="49088@x" ObjectIDZND1="49089@x" Pin0InfoVect0LinkObjId="SW-317645_0" Pin0InfoVect1LinkObjId="SW-317646_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317644_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="880,-51 880,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36ee830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="837,5 880,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_36ed110@0" ObjectIDZND0="0@x" ObjectIDZND1="49087@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317644_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36ed110_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="837,5 880,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36eec30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,113 880,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_36ed110@0" ObjectIDZND1="49087@x" Pin0InfoVect0LinkObjId="g_36ed110_0" Pin0InfoVect1LinkObjId="SW-317644_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="880,113 880,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36eee90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,5 880,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_36ed110@0" ObjectIDZND0="49087@0" Pin0InfoVect0LinkObjId="SW-317644_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_36ed110_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,5 880,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36f9ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="958,-79 970,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="958,-79 970,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36f9e00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="970,-79 970,-100 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-79 970,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fb400">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="970,-50 970,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="970,-50 970,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fb660">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="927,6 970,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_36fa060@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36fa060_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="927,6 970,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fba60">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="970,114 970,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_36fa060@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_36fa060_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="970,114 970,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36fbcc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="970,6 970,-23 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_36fa060@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_36fa060_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,6 970,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3399c40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1042,-81 1054,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1042,-81 1054,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3399ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1054,-81 1054,-102 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-81 1054,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339b5c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1054,-52 1054,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-52 1054,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339b820">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1011,4 1054,4 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_339a100@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_339a100_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1011,4 1054,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339bc20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1054,112 1054,4 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_339a100@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_339a100_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1054,112 1054,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_339be80">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1054,4 1054,-25 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_339a100@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_339a100_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,4 1054,-25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33576f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="89,-137 89,-192 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="89,-137 89,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a0a90">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="174,-136 174,-192 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="174,-136 174,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a1810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="268,-135 268,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49109@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317678_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="268,-135 268,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a2720">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="369,-137 369,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49106@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317673_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="369,-137 369,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a3790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="463,-137 463,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49103@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317668_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="463,-137 463,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a4450">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="561,-138 561,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49100@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317663_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="561,-138 561,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a5230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,-137 656,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49094@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317655_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,-137 656,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a5ec0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,-136 783,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49091@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317650_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,-136 783,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a6e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="880,-137 880,-192 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49088@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317645_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="880,-137 880,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a7ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="970,-136 970,-192 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="970,-136 970,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33a8ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1054,-138 1054,-192 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49182@0" Pin0InfoVect0LinkObjId="g_2e05950_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1054,-138 1054,-192 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341db70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1199,-80 1211,-80 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1199,-80 1211,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341ddd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1211,-80 1211,-101 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-80 1211,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341f310">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1211,-51 1211,-80 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-51 1211,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341f570">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1168,5 1211,5 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_341e030@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_341e030_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1168,5 1211,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341f7d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1211,113 1211,5 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_341e030@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_341e030_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1211,113 1211,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_341fa30">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1211,5 1211,-24 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_341e030@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_341e030_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,5 1211,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34284d0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1279,-79 1291,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1279,-79 1291,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3428730">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1291,-79 1291,-100 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-79 1291,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3429c70">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1291,-50 1291,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-50 1291,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3429ed0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1248,6 1291,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3428990@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3428990_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1248,6 1291,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_342a130">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1291,114 1291,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3428990@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_3428990_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1291,114 1291,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_342a390">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1291,6 1291,-23 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3428990@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3428990_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,6 1291,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3432e80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1373,-78 1385,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49086@0" ObjectIDZND0="49085@x" ObjectIDZND1="49084@x" Pin0InfoVect0LinkObjId="SW-317640_0" Pin0InfoVect1LinkObjId="SW-317639_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317641_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1373,-78 1385,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34330e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-78 1385,-99 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49086@x" ObjectIDND1="49084@x" ObjectIDZND0="49085@0" Pin0InfoVect0LinkObjId="SW-317640_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317641_0" Pin1InfoVect1LinkObjId="SW-317639_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-78 1385,-99 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3434620">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-49 1385,-78 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49084@1" ObjectIDZND0="49085@x" ObjectIDZND1="49086@x" Pin0InfoVect0LinkObjId="SW-317640_0" Pin0InfoVect1LinkObjId="SW-317641_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317639_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-49 1385,-78 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3434880">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1342,7 1385,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3433340@0" ObjectIDZND0="0@x" ObjectIDZND1="49084@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317639_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3433340_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1342,7 1385,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3434ae0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,115 1385,7 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3433340@0" ObjectIDZND1="49084@x" Pin0InfoVect0LinkObjId="g_3433340_0" Pin0InfoVect1LinkObjId="SW-317639_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1385,115 1385,7 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3434d40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,7 1385,-22 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3433340@0" ObjectIDZND0="49084@0" Pin0InfoVect0LinkObjId="SW-317639_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3433340_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,7 1385,-22 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343d830">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1474,-80 1486,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49083@0" ObjectIDZND0="49082@x" ObjectIDZND1="49081@x" Pin0InfoVect0LinkObjId="SW-317635_0" Pin0InfoVect1LinkObjId="SW-317634_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317636_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1474,-80 1486,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343da90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-80 1486,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49083@x" ObjectIDND1="49081@x" ObjectIDZND0="49082@0" Pin0InfoVect0LinkObjId="SW-317635_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317636_0" Pin1InfoVect1LinkObjId="SW-317634_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-80 1486,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343efd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-51 1486,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49081@1" ObjectIDZND0="49082@x" ObjectIDZND1="49083@x" Pin0InfoVect0LinkObjId="SW-317635_0" Pin0InfoVect1LinkObjId="SW-317636_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317634_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-51 1486,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343f230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1443,5 1486,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_343dcf0@0" ObjectIDZND0="0@x" ObjectIDZND1="49081@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317634_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_343dcf0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1443,5 1486,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343f490">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,113 1486,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_343dcf0@0" ObjectIDZND1="49081@x" Pin0InfoVect0LinkObjId="g_343dcf0_0" Pin0InfoVect1LinkObjId="SW-317634_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1486,113 1486,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_343f6f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,5 1486,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_343dcf0@0" ObjectIDZND0="49081@0" Pin0InfoVect0LinkObjId="SW-317634_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_343dcf0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1486,5 1486,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34481e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1568,-80 1580,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49077@0" ObjectIDZND0="49076@x" ObjectIDZND1="49075@x" Pin0InfoVect0LinkObjId="SW-317627_0" Pin0InfoVect1LinkObjId="SW-317626_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317628_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1568,-80 1580,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3448440">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,-80 1580,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49077@x" ObjectIDND1="49075@x" ObjectIDZND0="49076@0" Pin0InfoVect0LinkObjId="SW-317627_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317628_0" Pin1InfoVect1LinkObjId="SW-317626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,-80 1580,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3449980">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,-51 1580,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49075@1" ObjectIDZND0="49076@x" ObjectIDZND1="49077@x" Pin0InfoVect0LinkObjId="SW-317627_0" Pin0InfoVect1LinkObjId="SW-317628_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317626_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1580,-51 1580,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3452b90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1666,-81 1678,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49074@0" ObjectIDZND0="49073@x" ObjectIDZND1="49072@x" Pin0InfoVect0LinkObjId="SW-317622_0" Pin0InfoVect1LinkObjId="SW-317621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317623_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1666,-81 1678,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3452df0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-81 1678,-102 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49074@x" ObjectIDND1="49072@x" ObjectIDZND0="49073@0" Pin0InfoVect0LinkObjId="SW-317622_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317623_0" Pin1InfoVect1LinkObjId="SW-317621_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-81 1678,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3454330">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-52 1678,-81 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49072@1" ObjectIDZND0="49073@x" ObjectIDZND1="49074@x" Pin0InfoVect0LinkObjId="SW-317622_0" Pin0InfoVect1LinkObjId="SW-317623_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317621_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-52 1678,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3454590">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1635,4 1678,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3453050@0" ObjectIDZND0="0@x" ObjectIDZND1="49072@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3453050_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1635,4 1678,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34547f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,112 1678,4 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3453050@0" ObjectIDZND1="49072@x" Pin0InfoVect0LinkObjId="g_3453050_0" Pin0InfoVect1LinkObjId="SW-317621_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1678,112 1678,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3454a50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,4 1678,-25 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3453050@0" ObjectIDZND0="49072@0" Pin0InfoVect0LinkObjId="SW-317621_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3453050_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,4 1678,-25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345d540">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1761,-80 1773,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49071@0" ObjectIDZND0="49070@x" ObjectIDZND1="49069@x" Pin0InfoVect0LinkObjId="SW-317617_0" Pin0InfoVect1LinkObjId="SW-317616_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317618_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1761,-80 1773,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345d7a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,-80 1773,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49071@x" ObjectIDND1="49069@x" ObjectIDZND0="49070@0" Pin0InfoVect0LinkObjId="SW-317617_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317618_0" Pin1InfoVect1LinkObjId="SW-317616_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1773,-80 1773,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345ece0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,-51 1773,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49069@1" ObjectIDZND0="49070@x" ObjectIDZND1="49071@x" Pin0InfoVect0LinkObjId="SW-317617_0" Pin0InfoVect1LinkObjId="SW-317618_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317616_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1773,-51 1773,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345ef40">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1730,5 1773,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_345da00@0" ObjectIDZND0="0@x" ObjectIDZND1="49069@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317616_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_345da00_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1730,5 1773,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345f1a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,113 1773,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_345da00@0" ObjectIDZND1="49069@x" Pin0InfoVect0LinkObjId="g_345da00_0" Pin0InfoVect1LinkObjId="SW-317616_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1773,113 1773,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_345f400">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,5 1773,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_345da00@0" ObjectIDZND0="49069@0" Pin0InfoVect0LinkObjId="SW-317616_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_345da00_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1773,5 1773,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3467ef0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1871,-79 1883,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49068@0" ObjectIDZND0="49067@x" ObjectIDZND1="49066@x" Pin0InfoVect0LinkObjId="SW-317612_0" Pin0InfoVect1LinkObjId="SW-317611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317613_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1871,-79 1883,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3468150">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,-79 1883,-100 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49068@x" ObjectIDND1="49066@x" ObjectIDZND0="49067@0" Pin0InfoVect0LinkObjId="SW-317612_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317613_0" Pin1InfoVect1LinkObjId="SW-317611_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1883,-79 1883,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469690">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,-50 1883,-79 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49066@1" ObjectIDZND0="49067@x" ObjectIDZND1="49068@x" Pin0InfoVect0LinkObjId="SW-317612_0" Pin0InfoVect1LinkObjId="SW-317613_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317611_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1883,-50 1883,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34698f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1840,6 1883,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_34683b0@0" ObjectIDZND0="0@x" ObjectIDZND1="49066@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34683b0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1840,6 1883,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469b50">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,114 1883,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_34683b0@0" ObjectIDZND1="49066@x" Pin0InfoVect0LinkObjId="g_34683b0_0" Pin0InfoVect1LinkObjId="SW-317611_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1883,114 1883,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3469db0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,6 1883,-23 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_34683b0@0" ObjectIDZND0="49066@0" Pin0InfoVect0LinkObjId="SW-317611_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_34683b0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1883,6 1883,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34728a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1968,-80 1980,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="49065@0" ObjectIDZND0="49064@x" ObjectIDZND1="49063@x" Pin0InfoVect0LinkObjId="SW-317607_0" Pin0InfoVect1LinkObjId="SW-317606_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317608_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1968,-80 1980,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3472b00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,-80 1980,-101 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="49065@x" ObjectIDND1="49063@x" ObjectIDZND0="49064@0" Pin0InfoVect0LinkObjId="SW-317607_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317608_0" Pin1InfoVect1LinkObjId="SW-317606_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,-80 1980,-101 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3474040">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,-51 1980,-80 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="49063@1" ObjectIDZND0="49064@x" ObjectIDZND1="49065@x" Pin0InfoVect0LinkObjId="SW-317607_0" Pin0InfoVect1LinkObjId="SW-317608_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317606_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1980,-51 1980,-80 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34742a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1937,5 1980,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_3472d60@0" ObjectIDZND0="0@x" ObjectIDZND1="49063@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-317606_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_3472d60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1937,5 1980,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3474500">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,113 1980,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_3472d60@0" ObjectIDZND1="49063@x" Pin0InfoVect0LinkObjId="g_3472d60_0" Pin0InfoVect1LinkObjId="SW-317606_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1980,113 1980,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3474760">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,5 1980,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_3472d60@0" ObjectIDZND0="49063@0" Pin0InfoVect0LinkObjId="SW-317606_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_3472d60_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,5 1980,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347d250">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2062,-79 2074,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2062,-79 2074,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347d4b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2074,-79 2074,-100 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2074,-79 2074,-100 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347e9f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2074,-50 2074,-79 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2074,-50 2074,-79 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347ec50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2031,6 2074,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_347d710@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_347d710_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2031,6 2074,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347eeb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2074,114 2074,6 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_347d710@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_347d710_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2074,114 2074,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_347f110">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2074,6 2074,-23 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_347d710@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_347d710_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2074,6 2074,-23 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3487ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2159,-81 2171,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2159,-81 2171,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3487f40">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2171,-81 2171,-102 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="breaker" EndDevType0="switch" ObjectIDND0="0@x" ObjectIDND1="0@x" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2171,-81 2171,-102 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_34894c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2171,-52 2171,-81 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="0@1" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2171,-52 2171,-81 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3489760">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2128,4 2171,4 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="load" EndDevType1="breaker" ObjectIDND0="g_34881e0@0" ObjectIDZND0="0@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34881e0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2128,4 2171,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3489a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2171,112 2171,4 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="0@0" ObjectIDZND0="g_34881e0@0" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="g_34881e0_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="2171,112 2171,4 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_3489ca0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2171,4 2171,-25 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="load" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_34881e0@0" ObjectIDZND0="0@0" Pin0InfoVect0LinkObjId="SW-0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_34881e0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2171,4 2171,-25 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348fc50">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1211,-137 1211,-190 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1211,-137 1211,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3490ba0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="1291,-136 1291,-190 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1291,-136 1291,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_34917e0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1385,-135 1385,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49085@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317640_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1385,-135 1385,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3491af0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1486,-137 1486,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49082@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317635_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1486,-137 1486,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3492560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,-137 1580,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49076@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317627_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,-137 1580,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348e810">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1678,-138 1678,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49073@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317622_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1678,-138 1678,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_348e470">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1773,-137 1773,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49070@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317617_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1773,-137 1773,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3495560">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1883,-136 1883,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49067@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317612_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1883,-136 1883,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3496230">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1980,-137 1980,-190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="49064@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317607_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1980,-137 1980,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3496ea0">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2074,-136 2074,-190 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2074,-136 2074,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3497a20">
     <polyline DF8003:Layer="PUBLIC" fill="none" lineStyle="2" points="2171,-138 2171,-190 " stroke-dasharray="10 5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="busSection" ObjectIDND0="0@1" ObjectIDZND0="49181@0" Pin0InfoVect0LinkObjId="g_2773e90_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="2171,-138 2171,-190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33bee10">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="637,173 655,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="49098@0" ObjectIDZND0="49096@x" ObjectIDZND1="g_33cf140@0" ObjectIDZND2="49097@x" Pin0InfoVect0LinkObjId="SW-317657_0" Pin0InfoVect1LinkObjId="g_33cf140_0" Pin0InfoVect2LinkObjId="SW-317658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317659_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="637,173 655,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385a30">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,173 655,149 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="49098@x" ObjectIDND1="g_33cf140@0" ObjectIDND2="49097@x" ObjectIDZND0="49096@0" Pin0InfoVect0LinkObjId="SW-317657_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317659_0" Pin1InfoVect1LinkObjId="g_33cf140_0" Pin1InfoVect2LinkObjId="SW-317658_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,173 655,149 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3a05dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="668,190 655,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_33cf140@0" ObjectIDZND0="49098@x" ObjectIDZND1="49096@x" ObjectIDZND2="49097@x" Pin0InfoVect0LinkObjId="SW-317659_0" Pin0InfoVect1LinkObjId="SW-317657_0" Pin0InfoVect2LinkObjId="SW-317658_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33cf140_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="668,190 655,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3385fb0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,190 655,173 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_33cf140@0" ObjectIDND1="49097@x" ObjectIDND2="g_33fb720@0" ObjectIDZND0="49098@x" ObjectIDZND1="49096@x" Pin0InfoVect0LinkObjId="SW-317659_0" Pin0InfoVect1LinkObjId="SW-317657_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_33cf140_0" Pin1InfoVect1LinkObjId="SW-317658_0" Pin1InfoVect2LinkObjId="g_33fb720_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="655,190 655,173 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f9350">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,255 655,225 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49097@1" ObjectIDZND0="g_33cf140@0" ObjectIDZND1="49098@x" ObjectIDZND2="49096@x" Pin0InfoVect0LinkObjId="g_33cf140_0" Pin0InfoVect1LinkObjId="SW-317659_0" Pin0InfoVect2LinkObjId="SW-317657_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317658_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="655,255 655,225 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f79b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,225 655,190 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="lightningRod" EndDevType1="switch" EndDevType2="switch" ObjectIDND0="49097@x" ObjectIDND1="g_33fb720@0" ObjectIDZND0="g_33cf140@0" ObjectIDZND1="49098@x" ObjectIDZND2="49096@x" Pin0InfoVect0LinkObjId="g_33cf140_0" Pin0InfoVect1LinkObjId="SW-317659_0" Pin0InfoVect2LinkObjId="SW-317657_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317658_0" Pin1InfoVect1LinkObjId="g_33fb720_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="655,225 655,190 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f9a00">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,362 655,343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="49097@x" ObjectIDZND1="g_33fb720@0" Pin0InfoVect0LinkObjId="SW-317658_0" Pin0InfoVect1LinkObjId="g_33fb720_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="655,362 655,343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33f9bf0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,343 655,291 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="reactance" EndDevType0="breaker" ObjectIDND0="g_33fb720@0" ObjectIDND1="0@x" ObjectIDZND0="49097@0" Pin0InfoVect0LinkObjId="SW-317658_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_33fb720_0" Pin1InfoVect1LinkObjId="SW-0_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,343 655,291 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_340db60">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,225 706,225 706,267 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" BeginDevType2="switch" EndDevType0="lightningRod" ObjectIDND0="49097@x" ObjectIDND1="g_33cf140@0" ObjectIDND2="49098@x" ObjectIDZND0="g_33fb720@0" Pin0InfoVect0LinkObjId="g_33fb720_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317658_0" Pin1InfoVect1LinkObjId="g_33cf140_0" Pin1InfoVect2LinkObjId="SW-317659_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,225 706,225 706,267 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33fe1c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="706,301 706,343 655,343 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="reactance" ObjectIDND0="g_33fb720@1" ObjectIDZND0="49097@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-317658_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33fb720_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="706,301 706,343 655,343 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_3410a70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,113 656,88 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="lightningRod" ObjectIDND0="49096@1" ObjectIDZND0="g_33de410@0" Pin0InfoVect0LinkObjId="g_33de410_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317657_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="656,113 656,88 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_33b58a0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="656,35 656,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_33de410@1" ObjectIDZND0="g_336c540@0" ObjectIDZND1="49093@x" Pin0InfoVect0LinkObjId="g_336c540_0" Pin0InfoVect1LinkObjId="SW-317654_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33de410_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="656,35 656,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_359dc90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="655,420 655,404 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" ObjectIDND0="g_359ac60@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_359ac60_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="655,420 655,404 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c1050">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,120 783,94 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer2" EndDevType0="lightningRod" ObjectIDND0="0@1" ObjectIDZND0="g_33c4ed0@0" Pin0InfoVect0LinkObjId="g_33c4ed0_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="783,120 783,94 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_35c1700">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="783,41 783,6 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="g_33c4ed0@1" ObjectIDZND0="g_33795f0@0" ObjectIDZND1="49090@x" Pin0InfoVect0LinkObjId="g_33795f0_0" Pin0InfoVect1LinkObjId="SW-317649_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_33c4ed0_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="783,41 783,6 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c09c0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1562,171 1580,171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" EndDevType0="switch" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="49080@0" ObjectIDZND0="49078@x" ObjectIDZND1="g_36c1020@0" ObjectIDZND2="49079@x" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="g_36c1020_0" Pin0InfoVect2LinkObjId="SW-317630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317631_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1562,171 1580,171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c0dc0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,171 1580,147 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="switch" ObjectIDND0="49080@x" ObjectIDND1="g_36c1020@0" ObjectIDND2="49079@x" ObjectIDZND0="49078@0" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317631_0" Pin1InfoVect1LinkObjId="g_36c1020_0" Pin1InfoVect2LinkObjId="SW-317630_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,171 1580,147 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c1d90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1593,188 1580,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="breaker" ObjectIDND0="g_36c1020@0" ObjectIDZND0="49078@x" ObjectIDZND1="49080@x" ObjectIDZND2="49079@x" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="SW-317631_0" Pin0InfoVect2LinkObjId="SW-317630_0" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c1020_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1593,188 1580,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c2190">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,188 1580,171 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" BeginDevType2="lightningRod" EndDevType0="switch" EndDevType1="switch" ObjectIDND0="g_36c1020@0" ObjectIDND1="49079@x" ObjectIDND2="g_36c5f40@0" ObjectIDZND0="49078@x" ObjectIDZND1="49080@x" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="SW-317631_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_36c1020_0" Pin1InfoVect1LinkObjId="SW-317630_0" Pin1InfoVect2LinkObjId="g_36c5f40_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1580,188 1580,171 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c5420">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,253 1580,223 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49079@1" ObjectIDZND0="49078@x" ObjectIDZND1="49080@x" ObjectIDZND2="g_36c1020@0" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="SW-317631_0" Pin0InfoVect2LinkObjId="g_36c1020_0" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-317630_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1580,253 1580,223 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c5680">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,223 1580,188 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="breaker" BeginDevType1="lightningRod" EndDevType0="switch" EndDevType1="switch" EndDevType2="lightningRod" ObjectIDND0="49079@x" ObjectIDND1="g_36c5f40@0" ObjectIDZND0="49078@x" ObjectIDZND1="49080@x" ObjectIDZND2="g_36c1020@0" Pin0InfoVect0LinkObjId="SW-317629_0" Pin0InfoVect1LinkObjId="SW-317631_0" Pin0InfoVect2LinkObjId="g_36c1020_0" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-317630_0" Pin1InfoVect1LinkObjId="g_36c5f40_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="1580,223 1580,188 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c5a80">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,360 1580,341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="0@0" ObjectIDZND0="49079@x" ObjectIDZND1="g_36c5f40@0" Pin0InfoVect0LinkObjId="SW-317630_0" Pin0InfoVect1LinkObjId="g_36c5f40_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1580,360 1580,341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c5ce0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,341 1580,289 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="reactance" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="0@x" ObjectIDND1="g_36c5f40@0" ObjectIDZND0="49079@0" Pin0InfoVect0LinkObjId="SW-317630_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="SW-0_0" Pin1InfoVect1LinkObjId="g_36c5f40_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,341 1580,289 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c6b70">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,223 1631,223 1631,265 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="switch" BeginDevType1="switch" BeginDevType2="lightningRod" EndDevType0="lightningRod" ObjectIDND0="49078@x" ObjectIDND1="49080@x" ObjectIDND2="g_36c1020@0" ObjectIDZND0="g_36c5f40@0" Pin0InfoVect0LinkObjId="g_36c5f40_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="SW-317629_0" Pin1InfoVect1LinkObjId="SW-317631_0" Pin1InfoVect2LinkObjId="g_36c1020_0" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,223 1631,223 1631,265 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36c6dd0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1631,299 1631,341 1580,341 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="reactance" ObjectIDND0="g_36c5f40@1" ObjectIDZND0="49079@x" ObjectIDZND1="0@x" Pin0InfoVect0LinkObjId="SW-317630_0" Pin0InfoVect1LinkObjId="SW-0_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c5f40_1" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1631,299 1631,341 1580,341 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-0KV" id="g_36c8c90">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,418 1580,402 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="reactance" ObjectIDND0="g_36c7fa0@0" ObjectIDZND0="0@1" Pin0InfoVect0LinkObjId="SW-0_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36c7fa0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,418 1580,402 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_368b790">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1537,5 1580,5 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="breaker" EndDevType1="lightningRod" ObjectIDND0="g_34486a0@0" ObjectIDZND0="49075@x" ObjectIDZND1="g_36bda30@0" Pin0InfoVect0LinkObjId="SW-317626_0" Pin0InfoVect1LinkObjId="g_36bda30_0" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_34486a0_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="1537,5 1580,5 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_36d6530">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,5 1580,-24 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="lightningRod" EndDevType0="breaker" ObjectIDND0="g_34486a0@0" ObjectIDND1="g_36bda30@0" ObjectIDZND0="49075@0" Pin0InfoVect0LinkObjId="SW-317626_0" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34486a0_0" Pin1InfoVect1LinkObjId="g_36bda30_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,5 1580,-24 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_374f890">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,5 1580,28 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="lightningRod" ObjectIDND0="g_34486a0@0" ObjectIDND1="49075@x" ObjectIDZND0="g_36bda30@1" Pin0InfoVect0LinkObjId="g_36bda30_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="2" Pin1InfoVect0LinkObjId="g_34486a0_0" Pin1InfoVect1LinkObjId="SW-317626_0" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,5 1580,28 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_374fde0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="1580,81 1580,111 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" EndDevType0="switch" ObjectIDND0="g_36bda30@0" ObjectIDZND0="49078@1" Pin0InfoVect0LinkObjId="SW-317629_1" Pin0InfoVect1LinkObjId="" Pin0InfoVect2LinkObjId="" Pin0Num="1" Pin1InfoVect0LinkObjId="g_36bda30_0" Pin1InfoVect1LinkObjId="" Pin1InfoVect2LinkObjId="" Pin1Num="1"/></metadata>
    <polyline fill="none" opacity="0" points="1580,81 1580,111 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_466e1b0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="612,-357 612,-416 910,-416 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="lightningRod" BeginDevType1="breaker" EndDevType0="transformer" EndDevType1="lightningRod" EndDevType2="breaker" ObjectIDND0="g_2e09150@0" ObjectIDND1="49052@x" ObjectIDZND0="49111@x" ObjectIDZND1="g_2776b50@0" ObjectIDZND2="49049@x" Pin0InfoVect0LinkObjId="g_2787c20_0" Pin0InfoVect1LinkObjId="g_2776b50_0" Pin0InfoVect2LinkObjId="SW-317590_0" Pin0Num="2" Pin1InfoVect0LinkObjId="g_2e09150_0" Pin1InfoVect1LinkObjId="SW-317593_0" Pin1InfoVect2LinkObjId="" Pin1Num="3"/></metadata>
    <polyline fill="none" opacity="0" points="612,-357 612,-416 910,-416 " stroke="white" stroke-width="10" transform=""/></g>
   <g class="BV-35KV" id="g_46659f0">
     <polyline DF8003:Layer="PUBLIC" fill="none" points="910,-416 1239,-416 1239,-356 " stroke-width="1"/>
     <metadata>
      
     <cge:PSR_Link BeginDevType0="transformer" BeginDevType1="lightningRod" BeginDevType2="breaker" EndDevType0="lightningRod" EndDevType1="breaker" ObjectIDND0="49111@x" ObjectIDND1="g_2e09150@0" ObjectIDND2="49052@x" ObjectIDZND0="g_2776b50@0" ObjectIDZND1="49049@x" Pin0InfoVect0LinkObjId="g_2776b50_0" Pin0InfoVect1LinkObjId="SW-317590_0" Pin0InfoVect2LinkObjId="" Pin0Num="3" Pin1InfoVect0LinkObjId="g_2787c20_0" Pin1InfoVect1LinkObjId="g_2e09150_0" Pin1InfoVect2LinkObjId="SW-317593_0" Pin1Num="2"/></metadata>
    <polyline fill="none" opacity="0" points="910,-416 1239,-416 1239,-356 " stroke="white" stroke-width="10" transform=""/></g>
  </g><g id="ConnectNode_Layer">
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="612" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="298" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="89" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="174" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="268" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="369" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="463" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="561" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="656" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="783" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="880" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="970" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49182" cx="1054" cy="-192" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1239" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1608" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1211" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1291" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1385" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1486" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1580" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1678" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1773" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1883" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="1980" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="2074" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
   <circle DF8003:Layer="PUBLIC" busDevId="49181" cx="2171" cy="-190" fill="rgb(255,255,0)" r="4" stroke="rgb(255,255,0)" stroke-width="1"/>
  </g><g id="DynamicPoint_Layer">
   <g DF8003:Layer="PUBLIC" freshType="0" id="DYN-317254" type="2">
    <use transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -53.000000 -994.000000)" xlink:href="#dynamicPoint:shape32"/>
    <metadata>
     <cge:PSR_Ref ObjectId="48931" ObjectName="DYN-CX_YJW"/>
     <cge:Meas_Ref ObjectId="317254"/>
    </metadata>
   </g>
  </g><g id="Text_Layer">
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,17)">危险点说明：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,59)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,101)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,143)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,164)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,185)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,206)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,227)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,248)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,269)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,290)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,311)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,332)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,353)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_28b8670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -330.000000 -350.000000) translate(0,374)">联系方式：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,17)">频率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,38)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,59)">全站有功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,80)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,101)">全站无功：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,122)"> </text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="Kaiti_GB2312" font-size="21" graphid="g_276c4a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -358.000000 -943.000000) translate(0,143)">并网联络点的电压和交换功率：</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(74,74,74)" font-family="SimHei" font-size="20" graphid="g_2661ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 -228.000000 -1084.500000) translate(0,16)">杨家湾光伏电站</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2856760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 855.000000 -1182.000000) translate(0,17)">220kV杨庄线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_27846f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.708738 -547.000000) translate(0,17)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27d6160" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 723.000000 -621.000000) translate(0,12)">#1主变:190MVA</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3326d20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 64.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_2dfbe00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 153.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32e2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.708738 157.000000) translate(0,17)">#2-1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32e2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.708738 157.000000) translate(0,38)">储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32e2bc0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 951.708738 157.000000) translate(0,59)">装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_330de60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1041.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_32d8530" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1188.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_30ed850" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1277.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_33ab080" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2059.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34d99b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2158.708738 157.000000) translate(0,17)">预留</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a021e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a021e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a021e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 244.708738 162.000000) translate(0,59)">Ⅷ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a09f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a09f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a09f80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 338.708738 162.000000) translate(0,59)">Ⅶ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a0d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a0d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a0d570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 424.708738 162.000000) translate(0,59)">Ⅵ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a10150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a10150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3a10150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 523.708738 162.000000) translate(0,59)">Ⅴ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34935e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34935e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34935e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1664.708738 162.000000) translate(0,59)">Ⅰ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34da450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 748.708738 230.000000) translate(0,17)">#1站用变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34fb520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.708738 366.000000) translate(0,17)">#2无功补</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34fb520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.708738 366.000000) translate(0,38)">偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_34fb520" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 539.708738 366.000000) translate(0,59)">(±33Mvar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3383960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.708738 156.000000) translate(0,17)">35kV#2</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3383960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.708738 156.000000) translate(0,38)">滤波装</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3383960" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 852.708738 156.000000) translate(0,59)">置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_369d760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.708738 152.000000) translate(0,17)">35kV#1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_369d760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.708738 152.000000) translate(0,38)">滤波装</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_369d760" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1354.708738 152.000000) translate(0,59)">置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36723c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 157.000000) translate(0,17)">#2-1</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36723c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 157.000000) translate(0,38)">储能</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36723c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 157.000000) translate(0,59)">装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36b00b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 365.000000) translate(0,17)">#1无功补</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36b00b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 365.000000) translate(0,38)">偿装置</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_36b00b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1465.708738 365.000000) translate(0,59)">(±33Mvar)</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3674f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3674f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3674f60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1754.708738 162.000000) translate(0,59)">Ⅱ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3685b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3685b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3685b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1860.708738 162.000000) translate(0,59)">Ⅲ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3730770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.708738 162.000000) translate(0,17)">杨家湾</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3730770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.708738 162.000000) translate(0,38)">集电线</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="21" graphid="g_3730770" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1957.708738 162.000000) translate(0,59)">Ⅳ回</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da9ba0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -751.000000) translate(0,12)">2811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bc4b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -806.000000) translate(0,12)">28117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2842b10" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 920.000000 -822.000000) translate(0,12)">281</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b48320" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 819.000000 -875.000000) translate(0,12)">28160</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bb6eb0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 921.000000 -904.000000) translate(0,12)">2816</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bbb830" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 820.000000 -974.000000) translate(0,12)">28167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3daa450" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 856.000000 -1040.000000) translate(0,12)">2819</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bd4660" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 822.000000 -1089.000000) translate(0,12)">28197</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3da2420" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 725.000000 -644.000000) translate(0,12)">#1主变</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3decc20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1083.000000 -641.000000) translate(0,12)">2010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3ded6a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 850.000000 -544.000000) translate(0,12)">3010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3754a90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1248.000000 -319.000000) translate(0,12)">301</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de9b30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1246.000000 -253.000000) translate(0,12)">3011</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373f780" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1163.000000 -308.000000) translate(0,12)">30117</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3740330" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 621.000000 -320.000000) translate(0,12)">302</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376a050" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 619.000000 -254.000000) translate(0,12)">3022</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3736c20" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 535.000000 -309.000000) translate(0,12)">30227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3894e70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1146.000000 -216.000000) translate(0,12)">35kVⅠ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b08f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 11.000000 -212.000000) translate(0,12)">35kVⅡ母</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d31a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 305.000000 -261.000000) translate(0,12)">3902</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36b8a00" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 238.000000 -320.000000) translate(0,12)">39027</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a62c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 321.000000 -242.000000) translate(0,12)">39020</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3743b80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1615.000000 -261.000000) translate(0,12)">3901</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36bd460" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1549.000000 -320.000000) translate(0,12)">39017</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bed5e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1632.000000 -242.000000) translate(0,12)">39010</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f7440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 277.000000 -43.000000) translate(0,12)">395</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36debf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 275.000000 -124.000000) translate(0,12)">3952</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3894570" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 213.000000 -104.000000) translate(0,12)">39527</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d28bd0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 378.000000 -45.000000) translate(0,12)">394</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d443d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 376.000000 -126.000000) translate(0,12)">3942</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45f70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 314.000000 -106.000000) translate(0,12)">39427</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370f5c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 472.000000 -45.000000) translate(0,12)">393</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_373c710" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 470.000000 -126.000000) translate(0,12)">3932</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376daf0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 407.000000 -106.000000) translate(0,12)">39327</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3727d70" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 570.000000 -46.000000) translate(0,12)">392</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3721670" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 568.000000 -127.000000) translate(0,12)">3922</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_369b280" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 505.000000 -107.000000) translate(0,12)">39227</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3bed900" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 665.000000 -45.000000) translate(0,12)">391</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376f180" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 -126.000000) translate(0,12)">3912</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d37150" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 601.000000 -106.000000) translate(0,12)">39127</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3736470" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 663.000000 123.000000) translate(0,12)">3916</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b959c0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 594.000000 147.000000) translate(0,12)">39167</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f1970" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 662.000000 265.000000) translate(0,12)">391Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39ca3a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 792.000000 -44.000000) translate(0,12)">389</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_370a9e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 790.000000 -125.000000) translate(0,12)">3892</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a6e80" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 727.000000 -105.000000) translate(0,12)">38927</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d350b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 889.000000 -45.000000) translate(0,12)">388</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3de90e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 887.000000 -126.000000) translate(0,12)">3882</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3726ec0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 824.000000 -106.000000) translate(0,12)">38827</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_34f8d60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1394.000000 -43.000000) translate(0,12)">387</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3741010" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1392.000000 -124.000000) translate(0,12)">3871</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d284e0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1330.000000 -104.000000) translate(0,12)">38717</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b92e30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1495.000000 -45.000000) translate(0,12)">386</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3b93440" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1493.000000 -126.000000) translate(0,12)">3861</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3895b60" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1431.000000 -106.000000) translate(0,12)">38617</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39d50d0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1589.000000 -45.000000) translate(0,12)">385</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37587a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 -126.000000) translate(0,12)">3851</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37470a0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1525.000000 -106.000000) translate(0,12)">38517</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3748e40" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 121.000000) translate(0,12)">3856</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3723ff0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1519.000000 145.000000) translate(0,12)">38567</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36e7f50" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1587.000000 263.000000) translate(0,12)">385Q</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d28ef0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1687.000000 -46.000000) translate(0,12)">384</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_376ad30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1685.000000 -127.000000) translate(0,12)">3841</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_38b7020" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1623.000000 -107.000000) translate(0,12)">38417</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372fc30" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1782.000000 -45.000000) translate(0,12)">383</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372f5f0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1780.000000 -126.000000) translate(0,12)">3831</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_36a8d90" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1718.000000 -106.000000) translate(0,12)">38317</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37020b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1892.000000 -44.000000) translate(0,12)">382</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3749860" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1890.000000 -125.000000) translate(0,12)">3821</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3d45680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1828.000000 -105.000000) translate(0,12)">38217</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_372a5b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1989.000000 -45.000000) translate(0,12)">381</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_37379b0" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1987.000000 -126.000000) translate(0,12)">3811</text>
   <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_39da680" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1925.000000 -106.000000) translate(0,12)">38117</text>
  </g><g id="Switch_Layer">
   <g DF8003:Layer="PUBLIC" id="SW-317572">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 905.449774 -721.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49042" ObjectName="SW-CX_YJW.CX_YJW_2811SW"/>
     <cge:Meas_Ref ObjectId="317572"/>
    <cge:TPSR_Ref TObjectID="49042"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317573">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(-0.000000 -1.000000 1.000000 -0.000000 905.449774 -874.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49043" ObjectName="SW-CX_YJW.CX_YJW_2816SW"/>
     <cge:Meas_Ref ObjectId="317573"/>
    <cge:TPSR_Ref TObjectID="49043"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317575">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.741036 -773.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49045" ObjectName="SW-CX_YJW.CX_YJW_28117SW"/>
     <cge:Meas_Ref ObjectId="317575"/>
    <cge:TPSR_Ref TObjectID="49045"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317576">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 810.741036 -842.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49046" ObjectName="SW-CX_YJW.CX_YJW_28160SW"/>
     <cge:Meas_Ref ObjectId="317576"/>
    <cge:TPSR_Ref TObjectID="49046"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317577">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 811.741036 -941.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49047" ObjectName="SW-CX_YJW.CX_YJW_28167SW"/>
     <cge:Meas_Ref ObjectId="317577"/>
    <cge:TPSR_Ref TObjectID="49047"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317578">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 813.741036 -1056.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49048" ObjectName="SW-CX_YJW.CX_YJW_28197SW"/>
     <cge:Meas_Ref ObjectId="317578"/>
    <cge:TPSR_Ref TObjectID="49048"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317574">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 848.741036 -1009.000000)" xlink:href="#switch2:shape1_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49044" ObjectName="SW-CX_YJW.CX_YJW_2819SW"/>
     <cge:Meas_Ref ObjectId="317574"/>
    <cge:TPSR_Ref TObjectID="49044"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317591">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1229.741036 -223.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49050" ObjectName="SW-CX_YJW.CX_YJW_3011SW"/>
     <cge:Meas_Ref ObjectId="317591"/>
    <cge:TPSR_Ref TObjectID="49050"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317592">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1154.741036 -275.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49051" ObjectName="SW-CX_YJW.CX_YJW_30117SW"/>
     <cge:Meas_Ref ObjectId="317592"/>
    <cge:TPSR_Ref TObjectID="49051"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317597">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 834.000000 -514.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49056" ObjectName="SW-CX_YJW.CX_YJW_3010SW"/>
     <cge:Meas_Ref ObjectId="317597"/>
    <cge:TPSR_Ref TObjectID="49056"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317596">
    <use class="BV-220KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1067.000000 -611.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49055" ObjectName="SW-CX_YJW.CX_YJW_2010SW"/>
     <cge:Meas_Ref ObjectId="317596"/>
    <cge:TPSR_Ref TObjectID="49055"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317594">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 602.741036 -224.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49053" ObjectName="SW-CX_YJW.CX_YJW_3022SW"/>
     <cge:Meas_Ref ObjectId="317594"/>
    <cge:TPSR_Ref TObjectID="49053"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317595">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 527.741036 -276.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49054" ObjectName="SW-CX_YJW.CX_YJW_30227SW"/>
     <cge:Meas_Ref ObjectId="317595"/>
    <cge:TPSR_Ref TObjectID="49054"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317598">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49057" ObjectName="SW-CX_YJW.CX_YJW_3901SW"/>
     <cge:Meas_Ref ObjectId="317598"/>
    <cge:TPSR_Ref TObjectID="49057"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317599">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 -209.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49058" ObjectName="SW-CX_YJW.CX_YJW_39010SW"/>
     <cge:Meas_Ref ObjectId="317599"/>
    <cge:TPSR_Ref TObjectID="49058"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317600">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1541.000000 -287.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49059" ObjectName="SW-CX_YJW.CX_YJW_39017SW"/>
     <cge:Meas_Ref ObjectId="317600"/>
    <cge:TPSR_Ref TObjectID="49059"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317601">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -231.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49060" ObjectName="SW-CX_YJW.CX_YJW_3902SW"/>
     <cge:Meas_Ref ObjectId="317601"/>
    <cge:TPSR_Ref TObjectID="49060"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317602">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 314.000000 -209.000000)" xlink:href="#switch2:shape5_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49061" ObjectName="SW-CX_YJW.CX_YJW_39020SW"/>
     <cge:Meas_Ref ObjectId="317602"/>
    <cge:TPSR_Ref TObjectID="49061"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317603">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 231.000000 -287.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49062" ObjectName="SW-CX_YJW.CX_YJW_39027SW"/>
     <cge:Meas_Ref ObjectId="317603"/>
    <cge:TPSR_Ref TObjectID="49062"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 80.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 26.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 165.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 111.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317678">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 259.063745 -94.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49109" ObjectName="SW-CX_YJW.CX_YJW_3952SW"/>
     <cge:Meas_Ref ObjectId="317678"/>
    <cge:TPSR_Ref TObjectID="49109"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317679">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 205.063745 -71.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49110" ObjectName="SW-CX_YJW.CX_YJW_39527SW"/>
     <cge:Meas_Ref ObjectId="317679"/>
    <cge:TPSR_Ref TObjectID="49110"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317673">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 360.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49106" ObjectName="SW-CX_YJW.CX_YJW_3942SW"/>
     <cge:Meas_Ref ObjectId="317673"/>
    <cge:TPSR_Ref TObjectID="49106"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317674">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 306.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49107" ObjectName="SW-CX_YJW.CX_YJW_39427SW"/>
     <cge:Meas_Ref ObjectId="317674"/>
    <cge:TPSR_Ref TObjectID="49107"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317668">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 454.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49103" ObjectName="SW-CX_YJW.CX_YJW_3932SW"/>
     <cge:Meas_Ref ObjectId="317668"/>
    <cge:TPSR_Ref TObjectID="49103"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317669">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 400.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49104" ObjectName="SW-CX_YJW.CX_YJW_39327SW"/>
     <cge:Meas_Ref ObjectId="317669"/>
    <cge:TPSR_Ref TObjectID="49104"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317663">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 552.063745 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49100" ObjectName="SW-CX_YJW.CX_YJW_3922SW"/>
     <cge:Meas_Ref ObjectId="317663"/>
    <cge:TPSR_Ref TObjectID="49100"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317664">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 498.063745 -74.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49101" ObjectName="SW-CX_YJW.CX_YJW_39227SW"/>
     <cge:Meas_Ref ObjectId="317664"/>
    <cge:TPSR_Ref TObjectID="49101"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317655">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49094" ObjectName="SW-CX_YJW.CX_YJW_3912SW"/>
     <cge:Meas_Ref ObjectId="317655"/>
    <cge:TPSR_Ref TObjectID="49094"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317656">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 593.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49095" ObjectName="SW-CX_YJW.CX_YJW_39127SW"/>
     <cge:Meas_Ref ObjectId="317656"/>
    <cge:TPSR_Ref TObjectID="49095"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317650">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 774.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49091" ObjectName="SW-CX_YJW.CX_YJW_3892SW"/>
     <cge:Meas_Ref ObjectId="317650"/>
    <cge:TPSR_Ref TObjectID="49091"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317651">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 720.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49092" ObjectName="SW-CX_YJW.CX_YJW_38927SW"/>
     <cge:Meas_Ref ObjectId="317651"/>
    <cge:TPSR_Ref TObjectID="49092"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317645">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 871.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49088" ObjectName="SW-CX_YJW.CX_YJW_3882SW"/>
     <cge:Meas_Ref ObjectId="317645"/>
    <cge:TPSR_Ref TObjectID="49088"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317646">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 817.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49089" ObjectName="SW-CX_YJW.CX_YJW_38827SW"/>
     <cge:Meas_Ref ObjectId="317646"/>
    <cge:TPSR_Ref TObjectID="49089"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 961.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 907.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1045.063745 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 991.063745 -74.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1202.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1148.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1282.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1228.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317640">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1376.063745 -94.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49085" ObjectName="SW-CX_YJW.CX_YJW_3871SW"/>
     <cge:Meas_Ref ObjectId="317640"/>
    <cge:TPSR_Ref TObjectID="49085"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317641">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1322.063745 -71.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49086" ObjectName="SW-CX_YJW.CX_YJW_38717SW"/>
     <cge:Meas_Ref ObjectId="317641"/>
    <cge:TPSR_Ref TObjectID="49086"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317635">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1477.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49082" ObjectName="SW-CX_YJW.CX_YJW_3861SW"/>
     <cge:Meas_Ref ObjectId="317635"/>
    <cge:TPSR_Ref TObjectID="49082"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317636">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1423.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49083" ObjectName="SW-CX_YJW.CX_YJW_38617SW"/>
     <cge:Meas_Ref ObjectId="317636"/>
    <cge:TPSR_Ref TObjectID="49083"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317627">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49076" ObjectName="SW-CX_YJW.CX_YJW_3851SW"/>
     <cge:Meas_Ref ObjectId="317627"/>
    <cge:TPSR_Ref TObjectID="49076"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317628">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1517.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49077" ObjectName="SW-CX_YJW.CX_YJW_38517SW"/>
     <cge:Meas_Ref ObjectId="317628"/>
    <cge:TPSR_Ref TObjectID="49077"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317622">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1669.063745 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49073" ObjectName="SW-CX_YJW.CX_YJW_3841SW"/>
     <cge:Meas_Ref ObjectId="317622"/>
    <cge:TPSR_Ref TObjectID="49073"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317623">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1615.063745 -74.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49074" ObjectName="SW-CX_YJW.CX_YJW_38417SW"/>
     <cge:Meas_Ref ObjectId="317623"/>
    <cge:TPSR_Ref TObjectID="49074"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317617">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1764.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49070" ObjectName="SW-CX_YJW.CX_YJW_3831SW"/>
     <cge:Meas_Ref ObjectId="317617"/>
    <cge:TPSR_Ref TObjectID="49070"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317618">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1710.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49071" ObjectName="SW-CX_YJW.CX_YJW_38317SW"/>
     <cge:Meas_Ref ObjectId="317618"/>
    <cge:TPSR_Ref TObjectID="49071"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317612">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1874.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49067" ObjectName="SW-CX_YJW.CX_YJW_3821SW"/>
     <cge:Meas_Ref ObjectId="317612"/>
    <cge:TPSR_Ref TObjectID="49067"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317613">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1820.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49068" ObjectName="SW-CX_YJW.CX_YJW_38217SW"/>
     <cge:Meas_Ref ObjectId="317613"/>
    <cge:TPSR_Ref TObjectID="49068"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317607">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1971.063745 -96.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49064" ObjectName="SW-CX_YJW.CX_YJW_3811SW"/>
     <cge:Meas_Ref ObjectId="317607"/>
    <cge:TPSR_Ref TObjectID="49064"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317608">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1917.063745 -73.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49065" ObjectName="SW-CX_YJW.CX_YJW_38117SW"/>
     <cge:Meas_Ref ObjectId="317608"/>
    <cge:TPSR_Ref TObjectID="49065"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2065.063745 -95.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2011.063745 -72.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2162.063745 -97.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-0">
    <use class="BV-0KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2108.063745 -74.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="SW-0"/>
     <cge:Meas_Ref ObjectId="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317657">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 647.000000 154.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49096" ObjectName="SW-CX_YJW.CX_YJW_3916SW"/>
     <cge:Meas_Ref ObjectId="317657"/>
    <cge:TPSR_Ref TObjectID="49096"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317659">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 586.063745 180.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49098" ObjectName="SW-CX_YJW.CX_YJW_39167SW"/>
     <cge:Meas_Ref ObjectId="317659"/>
    <cge:TPSR_Ref TObjectID="49098"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317629">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1571.000000 152.000000)" xlink:href="#switch2:shape0_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49078" ObjectName="SW-CX_YJW.CX_YJW_3856SW"/>
     <cge:Meas_Ref ObjectId="317629"/>
    <cge:TPSR_Ref TObjectID="49078"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="SW-317631">
    <use class="BV-35KV" dolleyBreakerFlag="0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1511.063745 178.000000)" xlink:href="#switch2:shape7_0"/>
    <metadata>
     <cge:PSR_Ref ObjectId="49080" ObjectName="SW-CX_YJW.CX_YJW_38567SW"/>
     <cge:Meas_Ref ObjectId="317631"/>
    <cge:TPSR_Ref TObjectID="49080"/></metadata>
   </g>
  </g><g id="Reactance_Layer">
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 642.000000 409.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="RB-0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1567.000000 407.000000)" xlink:href="#reactance:shape2"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="RB-0"/>
    </metadata>
   </g>
  </g><g id="LightningRod_Layer">
   <g DF8003:Layer="PUBLIC" id="g_287bba0">
    <use class="BV-220KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 941.449774 -1082.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2776b50">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1259.741036 -348.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_27837e0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 836.000000 -461.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2784ac0">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1005.000000 -604.000000)" xlink:href="#lightningRod:shape139"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2785240">
    <use class="BV-220KV" transform="matrix(1.000000 0.000000 0.000000 -1.000000 1041.000000 -607.000000)" xlink:href="#lightningRod:shape126"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2e09150">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 632.741036 -349.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_30d6210">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1599.000000 -345.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_276b7c0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1633.000000 -330.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32ebc90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 289.000000 -345.000000)" xlink:href="#lightningRod:shape4"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32f0050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 323.000000 -330.000000)" xlink:href="#lightningRod:shape67"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_332cce0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 39.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3325b80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 79.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3324ec0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 124.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32d7240">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 164.000000 65.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3331a00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 218.063745 61.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3332910">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 258.000000 66.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_331eb70">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 319.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3345fb0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 359.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3351d90">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 413.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3352ca0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 453.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_335fb60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 511.063745 58.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3360a70">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 551.000000 63.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_336c540">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 606.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33795f0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 733.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ed110">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 830.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36ee020">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 870.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36fa060">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 920.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36fae50">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 960.000000 65.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_339a100">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1004.063745 58.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_339b010">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1044.000000 63.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341e030">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1161.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_341ed60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1201.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3428990">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1241.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34296c0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1281.000000 65.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3433340">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1335.063745 61.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3434070">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1375.000000 66.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343dcf0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1436.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_343ea20">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1476.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34486a0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1530.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3453050">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1628.063745 58.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3453d80">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1668.000000 63.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345da00">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1723.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_345e730">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1763.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34683b0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1833.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34690e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1873.000000 65.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3472d60">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1930.063745 59.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3473a90">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1970.000000 64.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347d710">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2024.063745 60.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_347e440">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2064.000000 65.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_34881e0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2121.063745 58.000000)" xlink:href="#lightningRod:shape65"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_3488f10">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2161.000000 63.000000)" xlink:href="#lightningRod:shape188"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33de410">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 651.000000 93.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33cf140">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 663.741036 199.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33fb720">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 699.000000 306.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_359ac60">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 638.000000 450.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_33c4ed0">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 778.000000 99.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36bda30">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1575.000000 86.000000)" xlink:href="#lightningRod:shape157"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c1020">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1588.741036 197.000000)" xlink:href="#lightningRod:shape77"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c5f40">
    <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1624.000000 304.000000)" xlink:href="#lightningRod:shape174"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_36c7fa0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1563.000000 448.000000)" xlink:href="#lightningRod:shape146"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="TermMeasure_Layer">
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-318344" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,12)">318344.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318344" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-318345" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,27)">318345.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318345" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-318346" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,42)">318346.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318346" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-318351" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,57)">318351.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318351" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-318347" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,72)">318347.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318347" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-318350" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 108.000000 -309.000000) translate(0,87)">318350.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318350" ObjectName="CX_YJW.CX_YJW_3ⅡM:F"/>
     <cge:PSR_Ref ObjectID="49182"/>
     <cge:Term_Ref ObjectID="48700"/>
    <cge:TPSR_Ref TObjectID="49182"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ua" PreSymbol="0" appendix="" decimal="2" id="ME-318336" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,12)">318336.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318336" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Ub" PreSymbol="0" appendix="" decimal="2" id="ME-318337" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,27)">318337.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318337" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uc" PreSymbol="0" appendix="" decimal="2" id="ME-318338" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,42)">318338.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318338" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="3Uo" PreSymbol="0" appendix="" decimal="2" id="ME-318343" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,57)">318343.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318343" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Uab" PreSymbol="0" appendix="" decimal="2" id="ME-318339" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,72)">318339.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318339" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="48" MeasureType="Hz" PreSymbol="0" appendix="" decimal="2" id="ME-318342" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 2110.000000 -301.000000) translate(0,87)">318342.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318342" ObjectName="CX_YJW.CX_YJW_3ⅠM:F"/>
     <cge:PSR_Ref ObjectID="49181"/>
     <cge:Term_Ref ObjectID="48699"/>
    <cge:TPSR_Ref TObjectID="49181"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318303" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -854.000000) translate(0,12)">318303.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318303" ObjectName="CX_YJW.CX_YJW_281BK:F"/>
     <cge:PSR_Ref ObjectID="49041"/>
     <cge:Term_Ref ObjectID="48439"/>
    <cge:TPSR_Ref TObjectID="49041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318304" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -854.000000) translate(0,27)">318304.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318304" ObjectName="CX_YJW.CX_YJW_281BK:F"/>
     <cge:PSR_Ref ObjectID="49041"/>
     <cge:Term_Ref ObjectID="48439"/>
    <cge:TPSR_Ref TObjectID="49041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318300" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1099.000000 -854.000000) translate(0,42)">318300.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318300" ObjectName="CX_YJW.CX_YJW_281BK:F"/>
     <cge:PSR_Ref ObjectID="49041"/>
     <cge:Term_Ref ObjectID="48439"/>
    <cge:TPSR_Ref TObjectID="49041"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318475" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 234.000000 258.000000) translate(0,12)">318475.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318475" ObjectName="CX_YJW.CX_YJW_395BK:F"/>
     <cge:PSR_Ref ObjectID="49108"/>
     <cge:Term_Ref ObjectID="48575"/>
    <cge:TPSR_Ref TObjectID="49108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318476" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 234.000000 258.000000) translate(0,27)">318476.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318476" ObjectName="CX_YJW.CX_YJW_395BK:F"/>
     <cge:PSR_Ref ObjectID="49108"/>
     <cge:Term_Ref ObjectID="48575"/>
    <cge:TPSR_Ref TObjectID="49108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318472" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 234.000000 258.000000) translate(0,42)">318472.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318472" ObjectName="CX_YJW.CX_YJW_395BK:F"/>
     <cge:PSR_Ref ObjectID="49108"/>
     <cge:Term_Ref ObjectID="48575"/>
    <cge:TPSR_Ref TObjectID="49108"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318466" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 258.000000) translate(0,12)">318466.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318466" ObjectName="CX_YJW.CX_YJW_394BK:F"/>
     <cge:PSR_Ref ObjectID="49105"/>
     <cge:Term_Ref ObjectID="48569"/>
    <cge:TPSR_Ref TObjectID="49105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318467" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 258.000000) translate(0,27)">318467.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318467" ObjectName="CX_YJW.CX_YJW_394BK:F"/>
     <cge:PSR_Ref ObjectID="49105"/>
     <cge:Term_Ref ObjectID="48569"/>
    <cge:TPSR_Ref TObjectID="49105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318463" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 343.000000 258.000000) translate(0,42)">318463.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318463" ObjectName="CX_YJW.CX_YJW_394BK:F"/>
     <cge:PSR_Ref ObjectID="49105"/>
     <cge:Term_Ref ObjectID="48569"/>
    <cge:TPSR_Ref TObjectID="49105"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318457" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 258.000000) translate(0,12)">318457.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318457" ObjectName="CX_YJW.CX_YJW_393BK:F"/>
     <cge:PSR_Ref ObjectID="49102"/>
     <cge:Term_Ref ObjectID="48563"/>
    <cge:TPSR_Ref TObjectID="49102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318458" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 258.000000) translate(0,27)">318458.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318458" ObjectName="CX_YJW.CX_YJW_393BK:F"/>
     <cge:PSR_Ref ObjectID="49102"/>
     <cge:Term_Ref ObjectID="48563"/>
    <cge:TPSR_Ref TObjectID="49102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318454" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 437.000000 258.000000) translate(0,42)">318454.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318454" ObjectName="CX_YJW.CX_YJW_393BK:F"/>
     <cge:PSR_Ref ObjectID="49102"/>
     <cge:Term_Ref ObjectID="48563"/>
    <cge:TPSR_Ref TObjectID="49102"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318448" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 258.000000) translate(0,12)">318448.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318448" ObjectName="CX_YJW.CX_YJW_392BK:F"/>
     <cge:PSR_Ref ObjectID="49099"/>
     <cge:Term_Ref ObjectID="48557"/>
    <cge:TPSR_Ref TObjectID="49099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318449" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 258.000000) translate(0,27)">318449.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318449" ObjectName="CX_YJW.CX_YJW_392BK:F"/>
     <cge:PSR_Ref ObjectID="49099"/>
     <cge:Term_Ref ObjectID="48557"/>
    <cge:TPSR_Ref TObjectID="49099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318445" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 531.000000 258.000000) translate(0,42)">318445.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318445" ObjectName="CX_YJW.CX_YJW_392BK:F"/>
     <cge:PSR_Ref ObjectID="49099"/>
     <cge:Term_Ref ObjectID="48557"/>
    <cge:TPSR_Ref TObjectID="49099"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318439" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 368.000000) translate(0,12)">318439.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318439" ObjectName="CX_YJW.CX_YJW_391BK:F"/>
     <cge:PSR_Ref ObjectID="49093"/>
     <cge:Term_Ref ObjectID="48545"/>
    <cge:TPSR_Ref TObjectID="49093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318440" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 368.000000) translate(0,27)">318440.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318440" ObjectName="CX_YJW.CX_YJW_391BK:F"/>
     <cge:PSR_Ref ObjectID="49093"/>
     <cge:Term_Ref ObjectID="48545"/>
    <cge:TPSR_Ref TObjectID="49093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318436" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 694.000000 368.000000) translate(0,42)">318436.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318436" ObjectName="CX_YJW.CX_YJW_391BK:F"/>
     <cge:PSR_Ref ObjectID="49093"/>
     <cge:Term_Ref ObjectID="48545"/>
    <cge:TPSR_Ref TObjectID="49093"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318430" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 258.000000) translate(0,12)">318430.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318430" ObjectName="CX_YJW.CX_YJW_389BK:F"/>
     <cge:PSR_Ref ObjectID="49090"/>
     <cge:Term_Ref ObjectID="48537"/>
    <cge:TPSR_Ref TObjectID="49090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318431" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 258.000000) translate(0,27)">318431.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318431" ObjectName="CX_YJW.CX_YJW_389BK:F"/>
     <cge:PSR_Ref ObjectID="49090"/>
     <cge:Term_Ref ObjectID="48537"/>
    <cge:TPSR_Ref TObjectID="49090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318427" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 761.000000 258.000000) translate(0,42)">318427.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318427" ObjectName="CX_YJW.CX_YJW_389BK:F"/>
     <cge:PSR_Ref ObjectID="49090"/>
     <cge:Term_Ref ObjectID="48537"/>
    <cge:TPSR_Ref TObjectID="49090"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318421" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 258.000000) translate(0,12)">318421.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318421" ObjectName="CX_YJW.CX_YJW_388BK:F"/>
     <cge:PSR_Ref ObjectID="49087"/>
     <cge:Term_Ref ObjectID="48531"/>
    <cge:TPSR_Ref TObjectID="49087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318422" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 258.000000) translate(0,27)">318422.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318422" ObjectName="CX_YJW.CX_YJW_388BK:F"/>
     <cge:PSR_Ref ObjectID="49087"/>
     <cge:Term_Ref ObjectID="48531"/>
    <cge:TPSR_Ref TObjectID="49087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318418" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 863.000000 258.000000) translate(0,42)">318418.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318418" ObjectName="CX_YJW.CX_YJW_388BK:F"/>
     <cge:PSR_Ref ObjectID="49087"/>
     <cge:Term_Ref ObjectID="48531"/>
    <cge:TPSR_Ref TObjectID="49087"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318412" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 258.000000) translate(0,12)">318412.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318412" ObjectName="CX_YJW.CX_YJW_387BK:F"/>
     <cge:PSR_Ref ObjectID="49084"/>
     <cge:Term_Ref ObjectID="48525"/>
    <cge:TPSR_Ref TObjectID="49084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318413" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 258.000000) translate(0,27)">318413.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318413" ObjectName="CX_YJW.CX_YJW_387BK:F"/>
     <cge:PSR_Ref ObjectID="49084"/>
     <cge:Term_Ref ObjectID="48525"/>
    <cge:TPSR_Ref TObjectID="49084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318409" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1355.000000 258.000000) translate(0,42)">318409.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318409" ObjectName="CX_YJW.CX_YJW_387BK:F"/>
     <cge:PSR_Ref ObjectID="49084"/>
     <cge:Term_Ref ObjectID="48525"/>
    <cge:TPSR_Ref TObjectID="49084"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318403" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 258.000000) translate(0,12)">318403.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318403" ObjectName="CX_YJW.CX_YJW_386BK:F"/>
     <cge:PSR_Ref ObjectID="49081"/>
     <cge:Term_Ref ObjectID="48519"/>
    <cge:TPSR_Ref TObjectID="49081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318404" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 258.000000) translate(0,27)">318404.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318404" ObjectName="CX_YJW.CX_YJW_386BK:F"/>
     <cge:PSR_Ref ObjectID="49081"/>
     <cge:Term_Ref ObjectID="48519"/>
    <cge:TPSR_Ref TObjectID="49081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318400" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1464.000000 258.000000) translate(0,42)">318400.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318400" ObjectName="CX_YJW.CX_YJW_386BK:F"/>
     <cge:PSR_Ref ObjectID="49081"/>
     <cge:Term_Ref ObjectID="48519"/>
    <cge:TPSR_Ref TObjectID="49081"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318394" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 369.000000) translate(0,12)">318394.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318394" ObjectName="CX_YJW.CX_YJW_385BK:F"/>
     <cge:PSR_Ref ObjectID="49075"/>
     <cge:Term_Ref ObjectID="48507"/>
    <cge:TPSR_Ref TObjectID="49075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318395" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 369.000000) translate(0,27)">318395.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318395" ObjectName="CX_YJW.CX_YJW_385BK:F"/>
     <cge:PSR_Ref ObjectID="49075"/>
     <cge:Term_Ref ObjectID="48507"/>
    <cge:TPSR_Ref TObjectID="49075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318391" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1611.000000 369.000000) translate(0,42)">318391.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318391" ObjectName="CX_YJW.CX_YJW_385BK:F"/>
     <cge:PSR_Ref ObjectID="49075"/>
     <cge:Term_Ref ObjectID="48507"/>
    <cge:TPSR_Ref TObjectID="49075"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318385" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1672.000000 258.000000) translate(0,12)">318385.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318385" ObjectName="CX_YJW.CX_YJW_384BK:F"/>
     <cge:PSR_Ref ObjectID="49072"/>
     <cge:Term_Ref ObjectID="48501"/>
    <cge:TPSR_Ref TObjectID="49072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318386" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1672.000000 258.000000) translate(0,27)">318386.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318386" ObjectName="CX_YJW.CX_YJW_384BK:F"/>
     <cge:PSR_Ref ObjectID="49072"/>
     <cge:Term_Ref ObjectID="48501"/>
    <cge:TPSR_Ref TObjectID="49072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318382" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1672.000000 258.000000) translate(0,42)">318382.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318382" ObjectName="CX_YJW.CX_YJW_384BK:F"/>
     <cge:PSR_Ref ObjectID="49072"/>
     <cge:Term_Ref ObjectID="48501"/>
    <cge:TPSR_Ref TObjectID="49072"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318376" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1761.000000 258.000000) translate(0,12)">318376.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318376" ObjectName="CX_YJW.CX_YJW_383BK:F"/>
     <cge:PSR_Ref ObjectID="49069"/>
     <cge:Term_Ref ObjectID="48495"/>
    <cge:TPSR_Ref TObjectID="49069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318377" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1761.000000 258.000000) translate(0,27)">318377.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318377" ObjectName="CX_YJW.CX_YJW_383BK:F"/>
     <cge:PSR_Ref ObjectID="49069"/>
     <cge:Term_Ref ObjectID="48495"/>
    <cge:TPSR_Ref TObjectID="49069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318373" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1761.000000 258.000000) translate(0,42)">318373.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318373" ObjectName="CX_YJW.CX_YJW_383BK:F"/>
     <cge:PSR_Ref ObjectID="49069"/>
     <cge:Term_Ref ObjectID="48495"/>
    <cge:TPSR_Ref TObjectID="49069"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318367" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 258.000000) translate(0,12)">318367.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318367" ObjectName="CX_YJW.CX_YJW_382BK:F"/>
     <cge:PSR_Ref ObjectID="49066"/>
     <cge:Term_Ref ObjectID="48489"/>
    <cge:TPSR_Ref TObjectID="49066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318368" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 258.000000) translate(0,27)">318368.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318368" ObjectName="CX_YJW.CX_YJW_382BK:F"/>
     <cge:PSR_Ref ObjectID="49066"/>
     <cge:Term_Ref ObjectID="48489"/>
    <cge:TPSR_Ref TObjectID="49066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318364" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1861.000000 258.000000) translate(0,42)">318364.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318364" ObjectName="CX_YJW.CX_YJW_382BK:F"/>
     <cge:PSR_Ref ObjectID="49066"/>
     <cge:Term_Ref ObjectID="48489"/>
    <cge:TPSR_Ref TObjectID="49066"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318358" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1959.000000 258.000000) translate(0,12)">318358.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318358" ObjectName="CX_YJW.CX_YJW_381BK:F"/>
     <cge:PSR_Ref ObjectID="49063"/>
     <cge:Term_Ref ObjectID="48483"/>
    <cge:TPSR_Ref TObjectID="49063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318359" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1959.000000 258.000000) translate(0,27)">318359.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318359" ObjectName="CX_YJW.CX_YJW_381BK:F"/>
     <cge:PSR_Ref ObjectID="49063"/>
     <cge:Term_Ref ObjectID="48483"/>
    <cge:TPSR_Ref TObjectID="49063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318355" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1959.000000 258.000000) translate(0,42)">318355.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318355" ObjectName="CX_YJW.CX_YJW_381BK:F"/>
     <cge:PSR_Ref ObjectID="49063"/>
     <cge:Term_Ref ObjectID="48483"/>
    <cge:TPSR_Ref TObjectID="49063"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318329" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -326.000000) translate(0,12)">318329.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318329" ObjectName="CX_YJW.CX_YJW_302BK:F"/>
     <cge:PSR_Ref ObjectID="49052"/>
     <cge:Term_Ref ObjectID="48461"/>
    <cge:TPSR_Ref TObjectID="49052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318330" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -326.000000) translate(0,27)">318330.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318330" ObjectName="CX_YJW.CX_YJW_302BK:F"/>
     <cge:PSR_Ref ObjectID="49052"/>
     <cge:Term_Ref ObjectID="48461"/>
    <cge:TPSR_Ref TObjectID="49052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318326" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 752.000000 -326.000000) translate(0,42)">318326.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318326" ObjectName="CX_YJW.CX_YJW_302BK:F"/>
     <cge:PSR_Ref ObjectID="49052"/>
     <cge:Term_Ref ObjectID="48461"/>
    <cge:TPSR_Ref TObjectID="49052"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="P" PreSymbol="0" appendix="" decimal="2" id="ME-318317" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -320.000000) translate(0,12)">318317.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318317" ObjectName="CX_YJW.CX_YJW_301BK:F"/>
     <cge:PSR_Ref ObjectID="49049"/>
     <cge:Term_Ref ObjectID="48455"/>
    <cge:TPSR_Ref TObjectID="49049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Q" PreSymbol="0" appendix="" decimal="2" id="ME-318318" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -320.000000) translate(0,27)">318318.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318318" ObjectName="CX_YJW.CX_YJW_301BK:F"/>
     <cge:PSR_Ref ObjectID="49049"/>
     <cge:Term_Ref ObjectID="48455"/>
    <cge:TPSR_Ref TObjectID="49049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="42" MeasureType="Ia" PreSymbol="0" appendix="" decimal="2" id="ME-318314" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1358.000000 -320.000000) translate(0,42)">318314.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318314" ObjectName="CX_YJW.CX_YJW_301BK:F"/>
     <cge:PSR_Ref ObjectID="49049"/>
     <cge:Term_Ref ObjectID="48455"/>
    <cge:TPSR_Ref TObjectID="49049"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tap" PreSymbol="0" appendix="" decimal="0" id="ME-318332" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -600.000000) translate(0,12)">318332.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318332" ObjectName="CX_YJW.CX_YJW_1T:LF"/>
     <cge:PSR_Ref ObjectID="49111"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="49111"/></metadata>
   </g>
   <g DF8003:Layer="PUBLIC" DeviceClass="44" MeasureType="Tmp" PreSymbol="0" appendix="" decimal="2" id="ME-318333" prefix="" rightAlign="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.000000 -0.000000 -0.000000 1.000000 1135.000000 -600.000000) translate(0,27)">318333.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="318333" ObjectName="CX_YJW.CX_YJW_1T:LF"/>
     <cge:PSR_Ref ObjectID="49111"/>
     <cge:Term_Ref ObjectID="0"/>
    <cge:TPSR_Ref TObjectID="49111"/></metadata>
   </g>
  </g><g id="MotifButton_Layer">
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/></g>
   <g href="cx_索引_接线图_省调直调电厂_光伏.svg" style="fill-opacity:0"><rect height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/></g>
  </g><g id="Group_Layer">
   <g DF8003:Layer="PUBLIC" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 45.000000 -0.000000)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_251e6d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -17.000000 -258.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24a0720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -28.000000 -273.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287aeb0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 -3.000000 -288.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2519260" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2040.000000 247.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_24d6160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2057.000000 232.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_284c060" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 261.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28526c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2049.000000 277.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c43d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 292.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27c4540" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 2048.000000 307.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_28582e0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1038.000000 856.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2858590" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1027.000000 841.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_287b8c0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1052.000000 826.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2df7e80" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 20.000000 253.000000) translate(0,12)">Uab(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e00550" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 37.000000 238.000000) translate(0,12)">F(Hz):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_27ca470" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 267.000000) translate(0,12)">3U0(V):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_32b60f0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 29.000000 283.000000) translate(0,12)">Uc(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3247ee0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 298.000000) translate(0,12)">Ub(kV):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_2e00720" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 28.000000 313.000000) translate(0,12)">Ua(kV):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4282620" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 690.000000 325.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42830a0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 679.000000 310.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4283210" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 704.000000 295.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_423fff0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1298.000000 319.000000) translate(0,12)">P(MW):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_4240160" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1287.000000 304.000000) translate(0,12)">Q(MVar):</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_42402d0" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1312.000000 289.000000) translate(0,12)">Ia(A):</text>
   <metadata/></g>
   <g DF8003:Layer="PUBLIC" transform="matrix(1 0 0 -1 0 0)">
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e7fe50" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 602.000000) translate(0,12)">档位:</text>
    <text DF8003:Layer="PUBLIC" fill="rgb(255,255,255)" font-family="SimSun" font-size="15" graphid="g_3e7c310" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 1096.000000 587.000000) translate(0,12)">温度:</text>
   <metadata/></g>
  </g><g id="Transformer2_Layer">
   <g DF8003:Layer="PUBLIC" id="TF-0">
    <g id="WD-0">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 213.000000)" xlink:href="#transformer2:shape40_0"/>
    </g>
    <g id="WD-1">
     <metadata>
      <cge:PSR_Ref ObjectId="0"/>
     </metadata>
     <use class="BV-35KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 752.000000 213.000000)" xlink:href="#transformer2:shape40_1"/>
    </g>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="TF-0"/>
    </metadata>
   </g>
  </g><g id="Base_MotifButton_Layer">
   <g DF8003:Layer="PUBLIC" ImageFlag="1" UpImage="image/btn02_bg.png" imageHeight="67" imageWidth="259">
    <a>
     
     <rect fill="none" height="41" qtmmishow="hidden" width="175" x="-255" y="-1095"/>
    </a>
   <metadata/><rect fill="white" height="41" opacity="0" stroke="white" transform="" width="175" x="-255" y="-1095"/></g>
   <g DF8003:Layer="PUBLIC" ImageFlag="1" OverImage="image/20.png" UpImage="image/标题按钮左侧2.png" imageHeight="114" imageWidth="124">
    <a>
     
     <rect fill="none" height="69" qtmmishow="hidden" width="77" x="-304" y="-1112"/>
    </a>
   <metadata/><rect fill="white" height="69" opacity="0" stroke="white" transform="" width="77" x="-304" y="-1112"/></g>
  </g><g id="VoltageTransformer_Layer">
   <g DF8003:Layer="PUBLIC" id="g_283afc0">
    <use class="BV-0KV" transform="matrix(1.000000 -0.000000 0.000000 -1.000000 798.000000 -995.000000)" xlink:href="#voltageTransformer:shape88"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_2687e50">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 1616.500000 -452.500000)" xlink:href="#voltageTransformer:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
   <g DF8003:Layer="PUBLIC" id="g_32e32d0">
    <use class="BV-35KV" transform="matrix(0.000000 1.000000 -1.000000 0.000000 305.500000 -452.500000)" xlink:href="#voltageTransformer:shape115"/>
    <metadata>
     <cge:PSR_Ref ObjectId="0" ObjectName="0"/>
    </metadata>
   </g>
  </g><g id="ScadaRealValue_Layer">
   <g AccType="0" DF8003:Layer="PUBLIC" PreSymbol="0" dataTimeFlag="2" decimal="1" id="ME-0" ratioFlag="0">
    <text fill="rgb(255,255,255)" font-size="15" transform="matrix(3.000000 -0.000000 -0.000000 2.335135 -267.000000 -1036.513514) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -903.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
   <g AccType="0" DF8003:Layer="PUBLIC" PointClass="PointAiTel" PreSymbol="0" dataTimeFlag="0" decimal="2" id="ME-0" ratioFlag="0">
    <text fill="rgb(0,255,0)" font-size="15" transform="matrix(1.223776 -0.000000 -0.000000 1.395515 -234.461538 -862.966362) translate(0,12)">0.00</text>
    <metadata>
     <cge:Meas_Ref ObjectID="0" ObjectName="NULL:NULL"/>
    </metadata>
   </g>
  </g><g areaN="0" fileType="0" fixScaleFlag="1" id="whole_graph" layer0="PUBLIC:0.000000 0.000000" layer1="SCADA:0.000000 0.000000" layer2="PAS:0.000000 0.000000" layer3="OPT:0.000000 0.000000" layerN="4" moveAndZoomFlag="1" stationName="CX_YJW"/>
</svg>